{"__meta": {"id": "X3e2a7c60eb04536c9febf2d7f28e64ed", "datetime": "2025-06-27 02:26:07", "utime": **********.35146, "method": "POST", "uri": "/payment-voucher/confirm", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991166.900593, "end": **********.351478, "duration": 0.4508850574493408, "duration_str": "451ms", "measures": [{"label": "Booting", "start": 1750991166.900593, "relative_start": 0, "end": **********.223951, "relative_end": **********.223951, "duration": 0.32335805892944336, "duration_str": "323ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.223962, "relative_start": 0.32336902618408203, "end": **********.351479, "relative_end": 9.5367431640625e-07, "duration": 0.1275169849395752, "duration_str": "128ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45898840, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST payment-voucher/confirm", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@confirmVoucher", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.confirm", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=151\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:151-179</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.06217, "accumulated_duration_str": "62.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.253469, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.477}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.263362, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 2.477, "width_percent": 0.627}, {"sql": "update `voucher_payments` set `status` = 'accepted', `approved_at` = '2025-06-27 02:26:07', `voucher_payments`.`updated_at` = '2025-06-27 02:26:07' where `id` = '21'", "type": "query", "params": [], "bindings": ["accepted", "2025-06-27 02:26:07", "2025-06-27 02:26:07", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 153}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.265861, "duration": 0.05546, "duration_str": "55.46ms", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:153", "source": "app/Http/Controllers/PaymentVoucherController.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=153", "ajax": false, "filename": "PaymentVoucherController.php", "line": "153"}, "connection": "kdmkjkqknb", "start_percent": 3.104, "width_percent": 89.207}, {"sql": "select * from `voucher_payments` where `id` = '21' limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.323859, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:159", "source": "app/Http/Controllers/PaymentVoucherController.php:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=159", "ajax": false, "filename": "PaymentVoucherController.php", "line": "159"}, "connection": "kdmkjkqknb", "start_percent": 92.311, "width_percent": 0.595}, {"sql": "select * from `users` where `users`.`id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3266568, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:159", "source": "app/Http/Controllers/PaymentVoucherController.php:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=159", "ajax": false, "filename": "PaymentVoucherController.php", "line": "159"}, "connection": "kdmkjkqknb", "start_percent": 92.907, "width_percent": 0.386}, {"sql": "select * from `users` where `users`.`id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.327969, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:159", "source": "app/Http/Controllers/PaymentVoucherController.php:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=159", "ajax": false, "filename": "PaymentVoucherController.php", "line": "159"}, "connection": "kdmkjkqknb", "start_percent": 93.293, "width_percent": 0.466}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 289}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.330135, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:289", "source": "app/Services/FinancialRecordService.php:289", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=289", "ajax": false, "filename": "FinancialRecordService.php", "line": "289"}, "connection": "kdmkjkqknb", "start_percent": 93.759, "width_percent": 0.547}, {"sql": "select * from `financial_records` where `shift_id` = 47 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["47"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 295}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3319778, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:295", "source": "app/Services/FinancialRecordService.php:295", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=295", "ajax": false, "filename": "FinancialRecordService.php", "line": "295"}, "connection": "kdmkjkqknb", "start_percent": 94.306, "width_percent": 0.643}, {"sql": "select * from `financial_records` where (`id` = 47) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["47"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 306}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3336089, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:306", "source": "app/Services/FinancialRecordService.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=306", "ajax": false, "filename": "FinancialRecordService.php", "line": "306"}, "connection": "kdmkjkqknb", "start_percent": 94.949, "width_percent": 0.402}, {"sql": "update `financial_records` set `total_cash` = -1000, `financial_records`.`updated_at` = '2025-06-27 02:26:07' where `id` = 47", "type": "query", "params": [], "bindings": ["-1000", "2025-06-27 02:26:07", "47"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 306}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.334981, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:306", "source": "app/Services/FinancialRecordService.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=306", "ajax": false, "filename": "FinancialRecordService.php", "line": "306"}, "connection": "kdmkjkqknb", "start_percent": 95.351, "width_percent": 4.166}, {"sql": "update `voucher_payments` set `status` = 'accepted', `voucher_payments`.`updated_at` = '2025-06-27 02:26:07' where `id` = '21'", "type": "query", "params": [], "bindings": ["accepted", "2025-06-27 02:26:07", "21"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 170}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.340768, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:170", "source": "app/Http/Controllers/PaymentVoucherController.php:170", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=170", "ajax": false, "filename": "PaymentVoucherController.php", "line": "170"}, "connection": "kdmkjkqknb", "start_percent": 99.517, "width_percent": 0.483}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}, "App\\Models\\PaymentVoucher": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPaymentVoucher.php&line=1", "ajax": false, "filename": "PaymentVoucher.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/21\"\n]", "success": "تمت الموافقه علي سند الصرف بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/payment-voucher/confirm", "status_code": "<pre class=sf-dump id=sf-dump-1900249590 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1900249590\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2080987598 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2080987598\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1140326540 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">21</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140326540\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1615809407 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">53</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImkxZ2RPZXk2V3pZcmFVZHdraWhOWFE9PSIsInZhbHVlIjoiWHJMbVRUMU9KaFZYMkhESWo4S1NQZjdRQUFPN0hhVFNYOFA3cEsxMDBpS0pJMGtGRi9HZ01sRzkwdHNIT1BNODlibXZ5UG9qK1FNT1VReEtUb3BwMWtJUVRKU3orTVYvSkpPYXJjckkweDRCazVzSDkvUVM2QXVtTDFxem1QZUF2TU5jS1J6UXh2dklHT1U4ZmVZZ3c0bFBBMWpmeXkzZ1AwQjlLL2FMVU8wMmxGNHNHdk91Rm45QVpud3haV3U1WjJydjVNNEMxaGdNci92WDRONEljQkxzcEFEekxYakNkMGdVc2Y1RmUxdTNHVmlYSGhFZ3pyOWR6ZE14VkpjM09walBJMVpUQ1FPcVdZZTZtQ2N5cERVU3FRT29RLzBuWDFQbExYejBkZGV6ZGg1NjNib3VSc0NBQ0owZ2hUWDVVN1JqSVo4MWM1alo2aG5pdjRncjk1ZkdIUlVpc0dIcUxleWNuU1RuUEp3RWlCV1gyT0xsdHVaei95SC96OHovYU54N20yV0llbVRGanNrYTFkT29aYlNWMWhSbFlHWGtQOFZvNHNhMEVwMU15UFJvMGdQODlzdHVoV0hyWDNZbVhleGFIRTU4Q1Fxcmg1cU84NzNPbzJiMWtLVExyNFA2TUVPV2lOVUcya1BWQ3kybzgxTU51dWdwSU1lTmUvK0QiLCJtYWMiOiIyNTdjMzZkZTg1YzRmMzUzZjc0MDAxM2I3NjhiYTExN2NhYWFhMmQ3ODk0YzA5N2ZhZWViZDA5NDgxNjdjYjI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9Sc0hMMlcyMVV6a0dwWlo4RVRUSFE9PSIsInZhbHVlIjoiQkh1MS9YaDhUc3EweG9FSmhKRkN0K09Nb1R6SXZGeHc0VFgxd0tTa0wwMEJuZTNQMVpDOGVFeXd3cEFlUXZ0WGRldFFVUk9tK2x0ZVVMUnY1UWVXS0p0cEdTQXNMejFRWnVZV1FheFlnVGgvSXpSOWF2VU1yM2FoLzNIV3p1KzJmdk0wbVhOWWk1bDlnOHZhMkFVVDVReVV0N1FOck5BWm9tbFNFNUlSY2R0MGhDL2FuUFRicXpocFFUTWFORnFWWVJqNytSeUhDVHNkcC96K0tEbjcxYmdwR0haRnZpOGNKeWJHbWNLSjBVSGczcE5WYXBNYVJ3cjRmS0RGWUhxeUZOUXUxTWVzbGY3RWltTmMyMFF5bGJDVElMUnhQcEI4eS9JUDFFd2NoYnY0cm1hUXR6TWgySDRQRTJqSmFobUExcEV1YktnSWlIaEUzanJ0NE1KeW5mNi9ycEx6dGdNbkJhblVqcUE0QmRDZnF2dVVmM0xod1QrYnE4ZTZvQlBMcEhrdnpzeFdBZGxHNHBZNlFybHZ1bVBqQlkrNGx0c0xsUTBwUisxL09EYzl1SWFsK1VWdXBHN3lVb29BcVlmNTRRSDFlM1BaRmYxNENZUmdNVXB4aHVYNm1UeG1GYkxNN2RkTlBWTEFpeEFZN3lzSTVUbm4zcEx4QXgvV0kvMG0iLCJtYWMiOiIzYjE0NGE0NDIwZDBmNmU4YTMyNDc1ZWY2MmRhOWFkODRjOWM2MTIyYjYyZjYxNDUyN2NmZjFjNThlZmEyY2FhIiwidGFnIjoiIn0%3D; _clsk=1wa64lz%7C1750991165773%7C31%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615809407\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2021328633 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021328633\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1209974243 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:26:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InNzbThhVmFTajV1Rk9uR3BWdDdOMFE9PSIsInZhbHVlIjoic1Z2Sk4zMWUvOGNSZFozTFBwMFVhRmFHSjdqVzU4ZlIzNU9qK2gvOHQvUGhGOUExeVZ3c1JGVDRVL0ExYUpZTXV5L2ZZVjZQcCtUSEJNTTRnaDBFejM1bjhPS00rUG9TV1R6a3NkdXVVM1hYd0JNMGxuV2lyOUplUEVVaHErMnBubmEvSVdDVEZxVUkwalhvbEcvQU9xTUF2WnFLbnJESjJGbHJSY0dKdGo0eGZzQnRaa3dWdDNDK3ZaaVFBeCtDalFJQnQ0a0pIVGxUVEJtTFh6eFNBdmxIYm5xeFh1OExnMzErNExrOFhoaTlWSkRDRVczam9HWVFOcXF4TEtpUmFBMm5zQWJMdEpQZkRQbUQwdmdwZGxJdTRlMjJ0NnlUak5XYVEwRG0yTWVpcHFvaUVSU1NGTDlUWjd2UEQzM2tveWtnQnFQOEMrcmRzYmU1RjVyQzhwOC94ay9IZ09QRE9EK3VOaHRsN0VYNUNGTUx3YUJyTlB5bkxNSWQ4Nm5DeWpEdUlSanlWVHFZYXlxZy9SVTFRaTMySXpVaHVJYnJFQ1lHanljSFdicFBEWWNqeUQyYlhyZVpDd0o4cCtMdk1nV081aE5OWlBOeTRHN1dNajJOVWh1cmY5RzV0c1NCSlpqMzZXYjlVZDluOHZ4eTcwaXkwVXpXMmwxc1M4QVoiLCJtYWMiOiIzMjkzOWU0MjhlMDcwNjM3NTg5YTEzZTEzZWQ1YTE5ZGQ1ZDNkMzk5NmZkNTlkN2RjNjU0ODJmZjgzYzM2YmUzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlGcXlZUVhCdjVKTzFVeDVTWjNWU2c9PSIsInZhbHVlIjoiUUE1YThnS21KZWpWQmpvWTlZRXFlU0RoWnZaN3BONHZ2YUNsVFlkbDkxblNLOXNsUWswNkE0d243eFl4Z1U2YndyeWtyN3RJTTRQQVBKei9DdXhuV2QyTjl6SGU4M0xpSEZQd3BWLzkrNHY0eklLOUF2alBydkd2azNiMUYrVmR6MXV4V3hYNklYM2ZzdlNibUZJWjFrM0prckdJc2xma01pcjBTQnU5ditOWjkwc1FqN25Dc28rV292QWtQakNSb1VyUVQwb3NKS2VXem1SOTdHUlFrcXg5TGxvSEZRckh2eXVkeWVQR3o0Qmk0QW9MM1hDUkZKMWNvZ2tPakZGR3VlU1hWT1NxaGkxWmtkVG1BOW16c0JJZ08vUGtjWFdjQVJHc3RkRWp6MFFwMlowUGpEQkNMdWptUWs3bmE0T1phMFN4c0NaN0Z6VVdSbmpncS9VZEVOQVFsRGtEcU91RGlIVGQ5MnFXMXpnN0I1bGFtOFU1Q3dCTGhIS3VNTVcwTTNKWFh0dEE3YkJaZXc0RVBzUzZEY0QwU3lxbW1pd1NmMDVycVNORklVVjRvdWs3M1VybitvckZ3YkhkT2pzazZSMFpVZnk2ZSsrazRUWTV2OTBNWVdWQlNiUlZhMnZTbFhJVWR3WnVnRUJCNnFMSDlqUTJHRE1aU0RhQ255QTkiLCJtYWMiOiJkYzRhNzM1YmUzNWJiYjAyYTI3OWYyNWUwMDgwYzcxOTlhMDMyODA3NGUwMjQ1NmM2NTU0ZTZhOTdmOWVjYjdjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InNzbThhVmFTajV1Rk9uR3BWdDdOMFE9PSIsInZhbHVlIjoic1Z2Sk4zMWUvOGNSZFozTFBwMFVhRmFHSjdqVzU4ZlIzNU9qK2gvOHQvUGhGOUExeVZ3c1JGVDRVL0ExYUpZTXV5L2ZZVjZQcCtUSEJNTTRnaDBFejM1bjhPS00rUG9TV1R6a3NkdXVVM1hYd0JNMGxuV2lyOUplUEVVaHErMnBubmEvSVdDVEZxVUkwalhvbEcvQU9xTUF2WnFLbnJESjJGbHJSY0dKdGo0eGZzQnRaa3dWdDNDK3ZaaVFBeCtDalFJQnQ0a0pIVGxUVEJtTFh6eFNBdmxIYm5xeFh1OExnMzErNExrOFhoaTlWSkRDRVczam9HWVFOcXF4TEtpUmFBMm5zQWJMdEpQZkRQbUQwdmdwZGxJdTRlMjJ0NnlUak5XYVEwRG0yTWVpcHFvaUVSU1NGTDlUWjd2UEQzM2tveWtnQnFQOEMrcmRzYmU1RjVyQzhwOC94ay9IZ09QRE9EK3VOaHRsN0VYNUNGTUx3YUJyTlB5bkxNSWQ4Nm5DeWpEdUlSanlWVHFZYXlxZy9SVTFRaTMySXpVaHVJYnJFQ1lHanljSFdicFBEWWNqeUQyYlhyZVpDd0o4cCtMdk1nV081aE5OWlBOeTRHN1dNajJOVWh1cmY5RzV0c1NCSlpqMzZXYjlVZDluOHZ4eTcwaXkwVXpXMmwxc1M4QVoiLCJtYWMiOiIzMjkzOWU0MjhlMDcwNjM3NTg5YTEzZTEzZWQ1YTE5ZGQ1ZDNkMzk5NmZkNTlkN2RjNjU0ODJmZjgzYzM2YmUzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlGcXlZUVhCdjVKTzFVeDVTWjNWU2c9PSIsInZhbHVlIjoiUUE1YThnS21KZWpWQmpvWTlZRXFlU0RoWnZaN3BONHZ2YUNsVFlkbDkxblNLOXNsUWswNkE0d243eFl4Z1U2YndyeWtyN3RJTTRQQVBKei9DdXhuV2QyTjl6SGU4M0xpSEZQd3BWLzkrNHY0eklLOUF2alBydkd2azNiMUYrVmR6MXV4V3hYNklYM2ZzdlNibUZJWjFrM0prckdJc2xma01pcjBTQnU5ditOWjkwc1FqN25Dc28rV292QWtQakNSb1VyUVQwb3NKS2VXem1SOTdHUlFrcXg5TGxvSEZRckh2eXVkeWVQR3o0Qmk0QW9MM1hDUkZKMWNvZ2tPakZGR3VlU1hWT1NxaGkxWmtkVG1BOW16c0JJZ08vUGtjWFdjQVJHc3RkRWp6MFFwMlowUGpEQkNMdWptUWs3bmE0T1phMFN4c0NaN0Z6VVdSbmpncS9VZEVOQVFsRGtEcU91RGlIVGQ5MnFXMXpnN0I1bGFtOFU1Q3dCTGhIS3VNTVcwTTNKWFh0dEE3YkJaZXc0RVBzUzZEY0QwU3lxbW1pd1NmMDVycVNORklVVjRvdWs3M1VybitvckZ3YkhkT2pzazZSMFpVZnk2ZSsrazRUWTV2OTBNWVdWQlNiUlZhMnZTbFhJVWR3WnVnRUJCNnFMSDlqUTJHRE1aU0RhQ255QTkiLCJtYWMiOiJkYzRhNzM1YmUzNWJiYjAyYTI3OWYyNWUwMDgwYzcxOTlhMDMyODA3NGUwMjQ1NmM2NTU0ZTZhOTdmOWVjYjdjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1209974243\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"32 characters\">&#1578;&#1605;&#1578; &#1575;&#1604;&#1605;&#1608;&#1575;&#1601;&#1602;&#1607; &#1593;&#1604;&#1610; &#1587;&#1606;&#1583; &#1575;&#1604;&#1589;&#1585;&#1601; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}