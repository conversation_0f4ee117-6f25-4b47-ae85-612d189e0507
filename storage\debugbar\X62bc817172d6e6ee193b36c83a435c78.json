{"__meta": {"id": "X62bc817172d6e6ee193b36c83a435c78", "datetime": "2025-06-27 01:13:27", "utime": **********.600682, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.118314, "end": **********.600696, "duration": 0.48238205909729004, "duration_str": "482ms", "measures": [{"label": "Booting", "start": **********.118314, "relative_start": 0, "end": **********.517737, "relative_end": **********.517737, "duration": 0.39942288398742676, "duration_str": "399ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.517744, "relative_start": 0.3994300365447998, "end": **********.600698, "relative_end": 1.9073486328125e-06, "duration": 0.08295392990112305, "duration_str": "82.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45381824, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02745, "accumulated_duration_str": "27.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5552049, "duration": 0.02637, "duration_str": "26.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.066}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.591259, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.066, "width_percent": 1.676}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.594165, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.741, "width_percent": 2.259}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "23", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1214192245 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1214192245\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-418846798 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-418846798\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1059514608 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059514608\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-418750606 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=tu6zsp%7C2%7Cfx4%7C0%7C2004; _clsk=1ggm1jz%7C1750986797629%7C1%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjI3R0N3cmJFZ2pDa0h4VE5OWE9maXc9PSIsInZhbHVlIjoiajQ1aVJ6d2xvYWErOUZXK0NvMThMeXlzSFVvL3hoc0RnOVF4RU41TE10YmdqUm1VNWMwdFFmQWlsYWpxaHV1VGlaZXRDd2gzelNOV0V5cytxKzBGK3hIbWszTm1wVmlxOFFISzVrdzFZYkpGOVBGUmlYZTNzT2FCbk5jVjhwbmYwOW1nci9lUGdTc0E1aHRFVjlHZi9ZQ2s1SkZMT0cyMDI0Q2h2YU82Wng0Ym1KR2Q3eWsydFdNdGFPcDdZZVh6WHAvblIrYjJVZ2lpY20xbTk0QlZDbUp4WEZmR3BPeVZ5cVNiaHIyemJ2Q3V3Z2FwOFlHcHE1cFlXWnlGbnRJSTh3U3Q2VXVuYWl1RmxTdVhSSjhSV2tnb3lZVmI1eWtjVzMzbU5RUE1UUTNaZFJDV0U2OUZDS2x6eXI2SC9UUlFISENnMlBpampxRGMwL3VhcmZBYW1UWEpzZEx5R1FhK2ZkTmVRVGVEaVJlNTUySHhYMzZVYmFmVU52c3N2eFg3ZmZOaWE2UmtVbkJRK3hYVzFnbHNLU2UzZkxxV0JLNkttQmhkc3JJTGdqUXNLVGhZL3RRbVk0MTZYMk9oUkJRYU53elU4aDdCTWkwSHFuUEJZd3ZnOUl3aW1OVmZnQ0I1NURENDUyQW1TYXFOTW9QRlFtMVVlN0ZRTGxhWWMrUmQiLCJtYWMiOiI2NTk0NmU0OGRhNjE0MWIwNjc4NmU0YTQ2NjM0NTI1MzVhNDJkZTk5YTE0NDE4YjU5OGJkMjBlZTk2ZGQ3NTNlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJPbGJWOXpuVG5yRlZqa0dFMW81T1E9PSIsInZhbHVlIjoia01MYkdjOUZKdU5lbFdjZTh5NVc3ckhZMWU3cWhlVGtVWnJua28xOTNNVEpVV2VtS3dQMys0VGJ3SmErMTlCTUwzYk5CSS85dWxSZnNJd3ZkSVpvZDMxbVlqYjBmOHBNQUQ0LzZqb1UxeUVvdWt1VnpSMlVvSzVReUJ1MTRydVdFQW9TNldROTlpK1EyOG9HZjJTbHZRZU5udUVSdzF2bjMvRFFZSzBXOEZKM3YrQi8wRmJ6WTRuK0hxUTBST1dlUTlPZ0dqRk83Q00zOHBZekE1cHROalFYOExRdC9pSU1xNkZkRG5ENm9iSDdtNC9QdzIzS1VZYk55c1c0Wk92TWR5dWs1TGtTMm10aFNaUnk2OHlMajRWMkk2cW5vUERvbSs1Nk9wQng3WUNmRTh0RXUzalhmRDZoOGRmdzBGcVQrcGg3N1V4U1lBd0hXdkUvSW9sSnhCdFFBeHdMQWY1cmdtSitxZGIwelBMV1o5L1NobTdtaDZIYWxjWVlzaGlSNVNUNVhEdWxXRzlVeHI2RTdnbGR5anB4QUZCWVYzdUM2ZjQvWHBCZHJkVVNBbHl4Y1l6bmxhRHF5TnhBekdIQ1MvNVFzenBIZmZZSFRhcTA4UE9kQ09QN2Z5c1R6ZWhQYU8xRUthT2RiZGpvb1ZLNk1PdzI0MWZIU0V1NWFKTm8iLCJtYWMiOiI1NjdlZTZmNmEzNmIxY2I2Mzc5NTY2ZjI5ZmM4YzU4MDc4YmQwMzFkYjI2MjM0NGY3OWM0YTNmYzFhMGVjNDQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-418750606\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1713404736 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QVqhpWieKZCLT3nmskTMtatIDNwBdc2egPwBt6XG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1713404736\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-704006752 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:13:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjY3ZTBWd25ocngwdmJDRk1YOG5yVnc9PSIsInZhbHVlIjoiWUJYZVZoTFF4WnZnbkFYRS9TOWdwWWpOb2FCak5ENTdQNW1mbmp1bHI2ZlJGLzBrSXBuMC9tN3cwRjhnOGdqZDdRS3dUcVVHTUNLUXNNVk5KTW9JSVJFcTJ5U2ROajQzemsvUXhETTEzS0QvcDZZSm9nQnJ2MXlLbExNVjRCbGNVSHJQYjNaclhUL2RwN1ZKUGdpRmtZVFA0M2FSNmVNM1l1Ni9jUE41RktRNE9yQUJ6em10TTkrVDFBWFJrS1pBUVJFdVBaRU5NcTVmdGwyd2c4ekR5bnljTXdad3lsZHJ2VW5GT1dDcVVYc1BNMmRZS2crRUxFL3IvTXhoNWw1RDR1TXowUkdmWTlyQUlOUmJmdzdsNXF4VmNxaTN3RC9TZGEvZ2t6bTgyZit6SEtaRjlxTlRnZWt4eVJ1VjFBZTA3Z29vcU5yaDYzWWE0OFVraUNPRmxRY0NTNXI4NHNsb3NVZ3ovSStzOW1RWXNoVEVaWlRORWJqU2R2TlRZaEN3WnlMVGVwaVl6VHprY3kwb2J4RTlkOE0xSnFWTlJ0MVhTWnc1SnZOK3p5Tk8vZmdDQnJ5RHNkOU0yM0UvRkhWd0d5ekVieUJ2dFBreG5mcVFDa3ZNNTZuTzkyUTRWempsTktGWGdQMGVsRVlsM2xrbm9CQVcvbURYMXdUNWMwSTMiLCJtYWMiOiJlMGY0ZGE0OWUyOWM2MDAwM2I4YzNmZWRhMzAwNGMyZmU5NTY5ODhhMTUwMTliNjJiMjAxNmUzOTNjNTM3MjFhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikw2R1dXUy80b2NzZ0hqYnFCeWM2cGc9PSIsInZhbHVlIjoiTEZ4czZEaHVnQ25RT2xiRHUrSkhNbmF0eDUySW9BeXRyWklLS29QU1pGTHdDdTVwWk5VWDhUdmhzamIxSDdCZlBpaWd6dFB1WEdMNlRaZ0QwY2xuYVZrbDkrWW9MZ2dUd0Z1a2xBOUdtSXRKNHMwb2tYd1NJSUFEZmZxem9MdlFjMTgzZ2g1MXRZaC9ZY0dnL2o5b0Z3T1dRMjhaUXFzbUVHSC96MjVsV0UxRWNRZDFMZkJjWEE4NmVOb2ozL0swTWkrcG5BVXE1VlBLNXBGR2gxaHRzV2w4bUVpaFJuZDJGT1JGam9kM2s0d25NL2Y5L0Y5anRORldKZzNlcWlBNHhpWC9YZTZBY2tEdFZEMnZpeTdGTmF2L0NWL0VYYU9uSWl0b0ZDMTNGOXkzY0ptKy9nKzBkb0RZL1VjcE1aRHhmamxRQTg1MG14dndidHBSMmtyWlU2bEduelZDNTdtcHAxYWNaNk5UcE1vSnk0RUM5QXRKN3l3Rm5MOGUydzR2ZjNlRlN3NGlXZHQwNFNkRSs5R2daQnAyUWpXVWRyRll6MG5XUWV3M3NwODJHTHpUWFVDUzVmRGpGelhVZ2pXekxmYndDVWlNNkZOQUhENmIyYVJ1SzJSendNc1NhODNoZ3VaYzdUbXBSV1k2ank0b01EUXhhWVM4cXRMMUR6cWMiLCJtYWMiOiIwZWU0YjM3NjhjMDM4ZDRiM2IwZWE1MDNiOTViYjE5YTVmNzMyN2M2NjRkM2I3ZTkyODk4MzQ5YTI2M2VhNTlkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjY3ZTBWd25ocngwdmJDRk1YOG5yVnc9PSIsInZhbHVlIjoiWUJYZVZoTFF4WnZnbkFYRS9TOWdwWWpOb2FCak5ENTdQNW1mbmp1bHI2ZlJGLzBrSXBuMC9tN3cwRjhnOGdqZDdRS3dUcVVHTUNLUXNNVk5KTW9JSVJFcTJ5U2ROajQzemsvUXhETTEzS0QvcDZZSm9nQnJ2MXlLbExNVjRCbGNVSHJQYjNaclhUL2RwN1ZKUGdpRmtZVFA0M2FSNmVNM1l1Ni9jUE41RktRNE9yQUJ6em10TTkrVDFBWFJrS1pBUVJFdVBaRU5NcTVmdGwyd2c4ekR5bnljTXdad3lsZHJ2VW5GT1dDcVVYc1BNMmRZS2crRUxFL3IvTXhoNWw1RDR1TXowUkdmWTlyQUlOUmJmdzdsNXF4VmNxaTN3RC9TZGEvZ2t6bTgyZit6SEtaRjlxTlRnZWt4eVJ1VjFBZTA3Z29vcU5yaDYzWWE0OFVraUNPRmxRY0NTNXI4NHNsb3NVZ3ovSStzOW1RWXNoVEVaWlRORWJqU2R2TlRZaEN3WnlMVGVwaVl6VHprY3kwb2J4RTlkOE0xSnFWTlJ0MVhTWnc1SnZOK3p5Tk8vZmdDQnJ5RHNkOU0yM0UvRkhWd0d5ekVieUJ2dFBreG5mcVFDa3ZNNTZuTzkyUTRWempsTktGWGdQMGVsRVlsM2xrbm9CQVcvbURYMXdUNWMwSTMiLCJtYWMiOiJlMGY0ZGE0OWUyOWM2MDAwM2I4YzNmZWRhMzAwNGMyZmU5NTY5ODhhMTUwMTliNjJiMjAxNmUzOTNjNTM3MjFhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikw2R1dXUy80b2NzZ0hqYnFCeWM2cGc9PSIsInZhbHVlIjoiTEZ4czZEaHVnQ25RT2xiRHUrSkhNbmF0eDUySW9BeXRyWklLS29QU1pGTHdDdTVwWk5VWDhUdmhzamIxSDdCZlBpaWd6dFB1WEdMNlRaZ0QwY2xuYVZrbDkrWW9MZ2dUd0Z1a2xBOUdtSXRKNHMwb2tYd1NJSUFEZmZxem9MdlFjMTgzZ2g1MXRZaC9ZY0dnL2o5b0Z3T1dRMjhaUXFzbUVHSC96MjVsV0UxRWNRZDFMZkJjWEE4NmVOb2ozL0swTWkrcG5BVXE1VlBLNXBGR2gxaHRzV2w4bUVpaFJuZDJGT1JGam9kM2s0d25NL2Y5L0Y5anRORldKZzNlcWlBNHhpWC9YZTZBY2tEdFZEMnZpeTdGTmF2L0NWL0VYYU9uSWl0b0ZDMTNGOXkzY0ptKy9nKzBkb0RZL1VjcE1aRHhmamxRQTg1MG14dndidHBSMmtyWlU2bEduelZDNTdtcHAxYWNaNk5UcE1vSnk0RUM5QXRKN3l3Rm5MOGUydzR2ZjNlRlN3NGlXZHQwNFNkRSs5R2daQnAyUWpXVWRyRll6MG5XUWV3M3NwODJHTHpUWFVDUzVmRGpGelhVZ2pXekxmYndDVWlNNkZOQUhENmIyYVJ1SzJSendNc1NhODNoZ3VaYzdUbXBSV1k2ank0b01EUXhhWVM4cXRMMUR6cWMiLCJtYWMiOiIwZWU0YjM3NjhjMDM4ZDRiM2IwZWE1MDNiOTViYjE5YTVmNzMyN2M2NjRkM2I3ZTkyODk4MzQ5YTI2M2VhNTlkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-704006752\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1386355991 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1386355991\", {\"maxDepth\":0})</script>\n"}}