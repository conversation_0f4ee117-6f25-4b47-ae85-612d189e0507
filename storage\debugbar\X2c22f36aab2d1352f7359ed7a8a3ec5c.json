{"__meta": {"id": "X2c22f36aab2d1352f7359ed7a8a3ec5c", "datetime": "2025-06-27 02:28:42", "utime": **********.32942, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991321.922775, "end": **********.329434, "duration": 0.4066588878631592, "duration_str": "407ms", "measures": [{"label": "Booting", "start": 1750991321.922775, "relative_start": 0, "end": **********.258535, "relative_end": **********.258535, "duration": 0.33575987815856934, "duration_str": "336ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.258544, "relative_start": 0.3357689380645752, "end": **********.329436, "relative_end": 2.1457672119140625e-06, "duration": 0.0708920955657959, "duration_str": "70.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48198040, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00703, "accumulated_duration_str": "7.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.288081, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.617}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.29714, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.617, "width_percent": 4.979}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.309981, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 27.596, "width_percent": 8.962}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.311834, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 36.558, "width_percent": 6.686}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.3161278, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 43.243, "width_percent": 38.549}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.321168, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 81.792, "width_percent": 18.208}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1051945820 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1051945820\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.31522, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-582212434 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-582212434\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1794803276 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1794803276\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-820678698 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-820678698\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-52160451 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii93bzhBNWpwZHR4b0t1cWk0cFRTZ2c9PSIsInZhbHVlIjoibFIxcWROQ2NrUUNHaXRkQlEwcFRjRmlGSzNubVM2bEZJem1tckxudUhIQjM3UGx5WTJIV2lielRSQ0VrbUpwWDROWTU4QmV0cDZtQlQ0ckpNSXhyanZVM3NEY2o0bTljL1JmM21RRUlTUjYvREVSMlhYUDQyZWhmZHpSdzY0L3p0QU1sRjhJRlZaZ0Nsc3dJR3cveEpnZVN3aHEreXZXaUxEeXdzZ2Z3ZjRMbkVNMm9IT2pYRzkycjhqQjdqSUNjaDNxSFBnK0FyQUJIT1NMNEFqUnREVisvQ2dELytpTUJTU3pmSEwrU2t6M0EwcTgvaCt5TGR2Mis4NkhrNFRrakVGMm41dGVPckFzQTJmYzJDeWRvOURJRWJxaWt2TzhMRlJCVWpHbnhWczAveGpvcjBRdDdHMnVFZ0ZjYnhkZTZxT1ZiYUtFL3FUSjdiS0IwZXQvL3A4WWNMT2JqRmxsdjlIZ1FMeDhMYUVVWXcvVkFwK05FODByR09mUkxQZUVQY0c1aXU5dU8zS2ZTVURxSWF0QVpVUU94dFk5ajVTcFRyT1JBdE8zSWkzalBMUHNzV1h4OXFUMDNCR2N0RHhseVYwa0MxM1QvTkNtcFRvUlBZTEw1bzc0N0p4MS9kQW1vWXMyaUI5T0REQnFBZlJvdGlRZll4dGd2ZzZsc20yVFEiLCJtYWMiOiIyYjNiMmU4ZGY2ZWE2Y2I4OTZkYTljMDRiMjdiYzA1NTdjOWM3MDM2MjI3Y2I5N2ZkNDZmOGJmZGI5YjkwZDRjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJDMFhiWmN3QkJYNnRQMGtWUFV2Q3c9PSIsInZhbHVlIjoiK1ZmT1hOdWxsZ3pxRU5aRWZRNktUbndWSWd5OEluOHptck5UeWhrbHVwZlRQVG5UMVExaitnSi90YzhQaU1jVHRGUnlXYlM4RURRT0xEQ25rUStrRmpPMFVMaEdNUURicDdZL2xPRjdZMi9ZdmpTMkoya3JYM2hLcVcrOXorcjh0aG52TEYvRUdTK2kwOUE0eG12dWxWaGFNSHlESXFka2JaTmJubnJ1QjBLREFKY1pCMmhZWndPdWs1aDB6WVRTZlEraHYxRE5BMmpmdEpzQllLOFJZdGQvdnoyczI4cG9KM1d5VkZNK1k2WWhZOENDaXhjelZtUlFjMzZjamZNRTB4VmhmbEdCekNJWTNZckYzeEdBSm9USTN2YWpNOTZjby96amYrT21XajR0WHdvNFBJRi9PSWtTQm5YWE9haTNUSUF6UUJsbFZna1JxRXRrd0Y4c0RGcHNDOWlhM2tPdXVnWTlRT2Y4K0RCbi9SV1NnMlkwcy9OZ0VSSEJYUzVhRlFRM01ORHBOWEdZeGhtRWVrdkxBejlldWxpS1NmMFQwajM2dXFsNiswL0JnZmI4cnBoZFNRc2JZYkNBZkxIckZoSjc2YnArZ2xNMGwxNDdiYldEOGlpK3BacHQyZ2tEWjFjdG9vVWNIcGRqdzdKM0R3bEZYTU1jYzhocTRnWkEiLCJtYWMiOiIxMjJhYzM5MWMxMjMzOWVhMzU4YjQ0ZTFmYTRlMWJhMzZiYzRkMzc2ZjNiZmY2NjE1NTcwN2FlNGQxZTM3NGMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-52160451\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2026210821 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2026210821\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1728559480 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:28:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilc0RE1Od1hRcUJVc3p0Y085VTlmOXc9PSIsInZhbHVlIjoic1N3Y2pkUmlQZ21JdmtmK2xXS25QQVVHMk9Zd0ZTQ2ljOVJDMWNzZ2ZHVmpNUVk1WVVKamFXR296c2dveHFYeUNLN0d0d3BEZ3dDOFhNSFBWL1pZWkUySjNjTGxleFRCeEZEZzhzM3BWWFFwZ3JVUWRHRmgvc3ZBTGZaRHZSTGdqbnlWbVlWaDZuVlRITWd5cGZMd2V1OElReXR5Vm1ybnJMNXVOWTJNV3BwZ2hhWER3RXBwbUJ0RXVoWnhpNlBKVTYvakZnN1R5NTZxVlpuZ2VCWUdrQ0FqN1hKR2hsZ1FpMHZtU3FCS1ZqaHNjVTlsRThtK1RrZ0p6aHJVRWQ1MGx3UWdIRDQyYXhFMFF3M0NTL3ZCNmVQNG45bFU3VkpMVmlUeDUyWHVOallsMWliTndTMHl4aXBDSFhud0RSRzFpSmxCb3BWNWxMdUlyRk1EOFd2VkxvUVphRWsrYXhFeTRYYzlCR3JjVzUxbW4wTU00Z2QzcFA0Z1NOZXIwcVk3d3IwaE8yT0Urb3k4bTQrdTU2c0pUd1prMFA2VUdZM091Qk1rZ0tBajNZUW9rV3FNb1dudWJ5MU1zUlhLSUJDMmpZdjhuYVBtaXB0a2JvRTZ5UzlpTjR2Q3B5aFBQbGdNOU1sR1dVWkhRUDl1aWtXcWc3Tzk2MVVTcmp6ZE05bXoiLCJtYWMiOiJhOTUwZjQ3NjA0MjYwNGExZDBlZGE3ZjQxMjAzNjNkM2E5NzkzMDljZWFhYzQxYWU2N2NkNTA3ZWZmZDhiYTRlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:28:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVER0kzNFhvVUJoS292aVYvM01ic2c9PSIsInZhbHVlIjoiOHV3STM5ZDJjVXZSQnlFWWNvZFZCZFhzZlYyTkppYXlqQnlWVkxXTXgyUmZVOUFZRDlxcDk0dm1JQlBsQ3Y4cWU5WUpXWlhaeFRzMDBLcVlhUmRpd1hWYkNXYTd4TlB3NEUwemV2S3U4ZHkzQ29sQVArMXdmVkE0dzlYODF6NHJwV0JqajZPYlFXK0hiR1RCRkhmcVI4TnRTMldCbDJJQndSZDl3UUE0UERBN3lmOU9PU0xQZlYxeERLQkoxclkycmhzN1FjVDRNdjRrN29TcWFJMU5zazdEZTJnK2tlWUpQMGp3Y2ozWWdtZVIvVHJBTXgzY2NCWXdzKzJJcmE4RHNaWGZuWG00VVd2bm5hbFJMRGZxajNRa0dKd3g3L1pCbklGamxVN1JkUU81WU5FZDJqcDcxSDhmeUI4bmt4T1l2NlkvM1VvZmtDc3Y1VFEwcy92UDhERTR1dGRTWGZSU1pOSktzRnp3Rm5qcytnRmhiVGVUZlFtN1Z4Vmk2eDBOcnBGTEJZanBMKytQOHhRT1o0OXgzUDRsRDhJcStqa1pyc1RuZzJwUmh6c0Y5TWsvbGI4VDV4NmYvSVAzWUJQNDBydEoyMmtLZE5jUFJ1enZERUlaQkpGRkdVdEU4UGtsamZKc2JlTzEzN2hrRWZtUURBOWJncS9JSWVjVSt6cUEiLCJtYWMiOiI4NDU0Mjg0YjFiOTkwMzg0MTVjNGU2YWEyMDIyNDEyYjliMmUxZWVkMTE4MzNiMmM5YjY1MTc5YTUzZmFmODY3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:28:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilc0RE1Od1hRcUJVc3p0Y085VTlmOXc9PSIsInZhbHVlIjoic1N3Y2pkUmlQZ21JdmtmK2xXS25QQVVHMk9Zd0ZTQ2ljOVJDMWNzZ2ZHVmpNUVk1WVVKamFXR296c2dveHFYeUNLN0d0d3BEZ3dDOFhNSFBWL1pZWkUySjNjTGxleFRCeEZEZzhzM3BWWFFwZ3JVUWRHRmgvc3ZBTGZaRHZSTGdqbnlWbVlWaDZuVlRITWd5cGZMd2V1OElReXR5Vm1ybnJMNXVOWTJNV3BwZ2hhWER3RXBwbUJ0RXVoWnhpNlBKVTYvakZnN1R5NTZxVlpuZ2VCWUdrQ0FqN1hKR2hsZ1FpMHZtU3FCS1ZqaHNjVTlsRThtK1RrZ0p6aHJVRWQ1MGx3UWdIRDQyYXhFMFF3M0NTL3ZCNmVQNG45bFU3VkpMVmlUeDUyWHVOallsMWliTndTMHl4aXBDSFhud0RSRzFpSmxCb3BWNWxMdUlyRk1EOFd2VkxvUVphRWsrYXhFeTRYYzlCR3JjVzUxbW4wTU00Z2QzcFA0Z1NOZXIwcVk3d3IwaE8yT0Urb3k4bTQrdTU2c0pUd1prMFA2VUdZM091Qk1rZ0tBajNZUW9rV3FNb1dudWJ5MU1zUlhLSUJDMmpZdjhuYVBtaXB0a2JvRTZ5UzlpTjR2Q3B5aFBQbGdNOU1sR1dVWkhRUDl1aWtXcWc3Tzk2MVVTcmp6ZE05bXoiLCJtYWMiOiJhOTUwZjQ3NjA0MjYwNGExZDBlZGE3ZjQxMjAzNjNkM2E5NzkzMDljZWFhYzQxYWU2N2NkNTA3ZWZmZDhiYTRlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:28:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVER0kzNFhvVUJoS292aVYvM01ic2c9PSIsInZhbHVlIjoiOHV3STM5ZDJjVXZSQnlFWWNvZFZCZFhzZlYyTkppYXlqQnlWVkxXTXgyUmZVOUFZRDlxcDk0dm1JQlBsQ3Y4cWU5WUpXWlhaeFRzMDBLcVlhUmRpd1hWYkNXYTd4TlB3NEUwemV2S3U4ZHkzQ29sQVArMXdmVkE0dzlYODF6NHJwV0JqajZPYlFXK0hiR1RCRkhmcVI4TnRTMldCbDJJQndSZDl3UUE0UERBN3lmOU9PU0xQZlYxeERLQkoxclkycmhzN1FjVDRNdjRrN29TcWFJMU5zazdEZTJnK2tlWUpQMGp3Y2ozWWdtZVIvVHJBTXgzY2NCWXdzKzJJcmE4RHNaWGZuWG00VVd2bm5hbFJMRGZxajNRa0dKd3g3L1pCbklGamxVN1JkUU81WU5FZDJqcDcxSDhmeUI4bmt4T1l2NlkvM1VvZmtDc3Y1VFEwcy92UDhERTR1dGRTWGZSU1pOSktzRnp3Rm5qcytnRmhiVGVUZlFtN1Z4Vmk2eDBOcnBGTEJZanBMKytQOHhRT1o0OXgzUDRsRDhJcStqa1pyc1RuZzJwUmh6c0Y5TWsvbGI4VDV4NmYvSVAzWUJQNDBydEoyMmtLZE5jUFJ1enZERUlaQkpGRkdVdEU4UGtsamZKc2JlTzEzN2hrRWZtUURBOWJncS9JSWVjVSt6cUEiLCJtYWMiOiI4NDU0Mjg0YjFiOTkwMzg0MTVjNGU2YWEyMDIyNDEyYjliMmUxZWVkMTE4MzNiMmM5YjY1MTc5YTUzZmFmODY3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:28:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1728559480\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1478558906 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478558906\", {\"maxDepth\":0})</script>\n"}}