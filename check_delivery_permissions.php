<?php

// <PERSON><PERSON><PERSON> to check delivery permissions and roles
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$kernel->bootstrap();

use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;

echo "=== Delivery Permissions Check ===\n\n";

// 1. Check if delivery permissions exist
echo "1. Checking delivery permissions:\n";
$deliveryPermissions = [
    'manage delevery',
    'show delevery', 
    'create delevery',
    'edit delevery',
    'delete delevery'
];

foreach ($deliveryPermissions as $permission) {
    $exists = Permission::where('name', $permission)->exists();
    echo "   - $permission: " . ($exists ? "✓ EXISTS" : "✗ MISSING") . "\n";
}

echo "\n";

// 2. Check if Delivery role exists
echo "2. Checking Delivery role:\n";
$deliveryRole = Role::where('name', 'Delivery')->first();
if ($deliveryRole) {
    echo "   ✓ Delivery role exists\n";
    echo "   Permissions assigned to Delivery role:\n";
    $rolePermissions = $deliveryRole->permissions()->pluck('name')->toArray();
    foreach ($deliveryPermissions as $permission) {
        $hasPermission = in_array($permission, $rolePermissions);
        echo "     - $permission: " . ($hasPermission ? "✓" : "✗") . "\n";
    }
} else {
    echo "   ✗ Delivery role does not exist\n";
}

echo "\n";

// 3. Check users with delivery permissions
echo "3. Users with delivery permissions:\n";
$usersWithDeliveryPermissions = User::whereHas('permissions', function($query) {
    $query->whereIn('name', ['manage delevery', 'show delevery']);
})->orWhereHas('roles.permissions', function($query) {
    $query->whereIn('name', ['manage delevery', 'show delevery']);
})->get();

if ($usersWithDeliveryPermissions->count() > 0) {
    foreach ($usersWithDeliveryPermissions as $user) {
        echo "   - {$user->name} (ID: {$user->id}) - Type: {$user->type}\n";
        echo "     Roles: " . $user->roles->pluck('name')->implode(', ') . "\n";
        echo "     Can manage delivery: " . ($user->can('manage delevery') ? "✓" : "✗") . "\n";
    }
} else {
    echo "   ✗ No users found with delivery permissions\n";
}

echo "\n";

// 4. Check company role permissions
echo "4. Checking company role permissions:\n";
$companyRole = Role::where('name', 'company')->first();
if ($companyRole) {
    echo "   ✓ Company role exists\n";
    $companyPermissions = $companyRole->permissions()->pluck('name')->toArray();
    foreach ($deliveryPermissions as $permission) {
        $hasPermission = in_array($permission, $companyPermissions);
        echo "     - $permission: " . ($hasPermission ? "✓" : "✗") . "\n";
    }
} else {
    echo "   ✗ Company role does not exist\n";
}

echo "\n=== End Check ===\n";
