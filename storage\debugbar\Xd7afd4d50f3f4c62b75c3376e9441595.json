{"__meta": {"id": "Xd7afd4d50f3f4c62b75c3376e9441595", "datetime": "2025-06-27 02:34:21", "utime": 1750991661.007791, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.587275, "end": 1750991661.007804, "duration": 0.4205288887023926, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.587275, "relative_start": 0, "end": **********.931864, "relative_end": **********.931864, "duration": 0.3445889949798584, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.931875, "relative_start": 0.34459996223449707, "end": 1750991661.007805, "relative_end": 1.1920928955078125e-06, "duration": 0.07593011856079102, "duration_str": "75.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45735016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02724, "accumulated_duration_str": "27.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.957551, "duration": 0.02632, "duration_str": "26.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.623}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.992583, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.623, "width_percent": 1.836}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.998595, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.458, "width_percent": 1.542}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-378450839 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-378450839\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1605200522 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1605200522\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-387880532 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387880532\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1835625055 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991646682%7C41%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJ5UzNvY0hERkd6dEt1a3paWjJQcVE9PSIsInZhbHVlIjoiVURkSmZDL0did1BkSkltdGZjNzRxTWs1dmNPZzNkRkhsdVJUaEVuN3RHQlMwdEdaMU5zNmV4Z1JvMmNMR2d1NzN0bnMyMjNwK21yMDZTK3dOY240bXpuaDdKZDVrUVVFRityTlpHaTFFRW1lRjhGOTRmMWxsNUVFRENtc0xmUGtsZjAySnN4THAvMGR4cjRodzJnTkEvOG9sUG1qdDUvbmdqU3R5eTMwUGFoZmpaQU9FR0Q3bHMzYVBIckc3WGpGazNCa281a3YwYkhBeHpnYzh0d1hZcTBJNUcraTIxK1NtUTJMSGZTSlZFYytsRUpLV1dtMjNmcDBjMG84czVrRGNmNUFGdC80OFQwVVBGUjJGV0Q4TVVqb25QKzljNTI4RXBMQjEyRit4U2UrWHJScXBkWGVpd2kwRE82clg0L3lqdjh6Qm12WVBhZGhyV01KQWlicWdzWGxnUmsySVFxTmJiMzBGZVBnOTJqV2JiTjl3UWRkR1ZVNHAvUVkybjdESU1qalByenVyN2Y4eHNJYzhRelBhRjJKaGNtbURsQng4WERDRHBEeWlOTkczVUtGNEIvMTVFbHBNMkhBM3dwZFFvQ0tCZXRBVHZLNHN0TmdzZzRmN1FmR0dyMGEzOGRTMys1ZmJyUjltZnNiYitkT1NUQzRXRGhoVmFMRFhrNTIiLCJtYWMiOiI3OWIwZjBiMTUzOTllNTJmN2U4OTllMjgzNThhMGUyMjFkZTJiMzE5YzY0Njk0YzNhYzkwYTMwNzc2MTkxNGZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjEvb1ViallGL1d3R2VrVytOUTNGQ2c9PSIsInZhbHVlIjoiY1haOFVMdVN5UHRGVGZlTTI5SGJROWJMYXJVOElpNEpCUkt5aGx0VGd6WHBoOURmSWZoMnhKcXFHY21TZkNSZ2JSM3hvSHdxd0NGNzVBZnQ1Q3NsV3VhRFljQk9CbldGeFdzYlVPUlh5NE1maEdQWmx4d3NDY3R1N0M3QjFqQ2JlQXVzZXFuVnpCMkNTSS9JZGtkKzN2RzhxSXBkQWtWd3ZPSHlkMEpNTytwN3lZZGdBMHVjQ2FZVEJQbEhNODY3MlRmdHhLUkl6TFlObGxHbUJEaXpORVp5dG1tVHdEQnpUSEc0M3J5NHdOelArSUlmT3lRZUphWUlWeE9BSXo1Unk2SEFxQndkbDV1dlR0SFVYdVo1TkZGdE1QZzBYRFFzeEF2YzIrT2xnK2F1cTFuVVVGaVN6YnBlOC9XZHZISC9hYWhYa3lhcnNIVmVISlZEeUUrZFdqVk5YcGRTK3FGRlFka1A2dEwrYWlUMlo1UmhoNno2TDNWRDlzelYrTFN0WVNqc3BIMkJwNjR4RGJpTnRIZDUyYzlWNG1rdlRzNVNRQ2ViV3ZwcTNUN0F3Y2NPZWR4U3Y0T2VUWklyMlJIaVhtdnF4c1F6V2YyaVg4dm1GWUgxazJ5L3hXMDZOa2NqZkVUcFFnbndtMG45SUxCL2VxN2grR29wV2pMY2w0WFEiLCJtYWMiOiIzNTUyYjMyYmE4NWZkOTY5NDViNjhlYWFhZGE5YmY2MjE2ZTg4OWQxNmMzYTMyNjJjYzE5ZWZlMzRjYjA5NWYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1835625055\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1425964829 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425964829\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1931232136 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilc3c3hUM2JmZGo0ZTJaeU9LUjB4TFE9PSIsInZhbHVlIjoiR3VZdzNXNGNtc3htQjMvS2RWdytqRmVuRzdBaDBOaGh4L29xN1RJbzNMTU9MbUVNTDNVaVIyMFdDMnFMVDJDVUx6QWlJUGx1VG85MkdJNjZSZ2dpOVJZd2RWV3FRbW55d1Q5QVZyalUrV25jL3djQ2N6c3ZEYmdVdElCWGpYekR4QjV5Q0JlZkJqR2pSallKbnhpK0pNMjZ3ZUVtaElVQjZxdHF1alZWS0Z5eG5hYURzMWs2bjJiWmdqT3h4aGUzWEYrYzNEK2hxNGZjWFBxc0dWallkVVpCSTZYQnhLamFwWXlXMmxqOWJvNDZ5d0lYbUpYMjV1WHJsWHl0QUhVd2cxS0hiNkQyc2Y0QWVRdjZzUm9TNkV4OHh1QXVyUDRRTFNsb29YaEI2QlZISVhZdGFGY2NtalpiNlR2NDhiRlRtb084cVQ4UDhaMURHNWlGeWZYdDFTNXNvNSs5amlnNlRIa2V2Y1REbGhTS1FndmFRY3hVRldxOHVwZmt6anJzM2RGNk5Ga0J6U2N6ZksyU1V4L0hPakIzNWxBNVR1N2x3YmdYNmZnbnNRdHh1allGS2JZOENoMFM1MjRaVUU3MWdXNlJaTDRacEh0RFlaY2hCMXUreDk5K0MxbGk3WEdYSERLTEMzMXZUY2JZY1NPREFmMTlxa3hrRmhOZmQ4MVkiLCJtYWMiOiI3ODJjMTRhMjQ4NDc2NzZlYjg4MjU1MWFmYjk4OWYxODljOGY3YzViMmFmOGFmMTNmY2YwYTk0OTJkM2YwYTcxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InM4dmVlRGNkK0svMWF4cmdHcEIrbkE9PSIsInZhbHVlIjoiekhvU25lc0Rza1lTbW5mRzNFd2hzdS9IWmhaR21zLzhZSmxLc3V2WnFXR2FkemowQU1KVHFOTUVEb0sxL2FLaDJxWVlDL3BQenEyb29NTW1FUkZ2YlhmMlN2a09ubzZxbTBJWlhxcVhlSTdyYkp2blVVbDlSeWpwUnpOaVVHcHRQYncwSVRMYUtac3hrRjdkMWVLM094bVg1WGlQWGxVcnVGYVYzdmJIZVdGWmhTTGdRTnRlaUpOMzFGNVNYSHJJd0VIUm1Yb2VadE1JdlZwVjdEOU81emR6Y2hxYk80NVJWV2pCZ3IvTGd5NTBUYURBc2lMVjBtcUZuc09OWmpMZkVGL2wrdE1qYWRuZFpMVXdRdjFSaE1PdzN4S2pNbk9ibnlxSXFlMGpmL0tEaVM5Q3lhd1B5MlEydDRySXVCYy9IdVpSOER6a2hrWXhuY0kvZlpJbnR0WmEyaEVlNll3RnRkSnNZc1RFY0d5V01OV3BFTEdrWnJEaE9rbTJ6MjU5VDNWekJlY29aU0UwUzBoc01ZWW5KWXNEc1JxbWtJYVhERm1WVElhbWJYMlQ2RDdVb3lOMGI5dlgrSjNGWEtFNjJkejViMjJlZkdwUmpwTG1UV0cwRjJmdzFMb0pMYStYWXBpTTZHR1VHY2pwUXpVRWxta28yNHBBZy9RSnlrc0kiLCJtYWMiOiJkM2VkNDIxNmRlOTRiNzJjMTdhNDRkYTdjYTk2NDIxY2EwNjRiNjU2ZWNhYjMxNjE5Y2U4MDhmZWZjZmRiMDY0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilc3c3hUM2JmZGo0ZTJaeU9LUjB4TFE9PSIsInZhbHVlIjoiR3VZdzNXNGNtc3htQjMvS2RWdytqRmVuRzdBaDBOaGh4L29xN1RJbzNMTU9MbUVNTDNVaVIyMFdDMnFMVDJDVUx6QWlJUGx1VG85MkdJNjZSZ2dpOVJZd2RWV3FRbW55d1Q5QVZyalUrV25jL3djQ2N6c3ZEYmdVdElCWGpYekR4QjV5Q0JlZkJqR2pSallKbnhpK0pNMjZ3ZUVtaElVQjZxdHF1alZWS0Z5eG5hYURzMWs2bjJiWmdqT3h4aGUzWEYrYzNEK2hxNGZjWFBxc0dWallkVVpCSTZYQnhLamFwWXlXMmxqOWJvNDZ5d0lYbUpYMjV1WHJsWHl0QUhVd2cxS0hiNkQyc2Y0QWVRdjZzUm9TNkV4OHh1QXVyUDRRTFNsb29YaEI2QlZISVhZdGFGY2NtalpiNlR2NDhiRlRtb084cVQ4UDhaMURHNWlGeWZYdDFTNXNvNSs5amlnNlRIa2V2Y1REbGhTS1FndmFRY3hVRldxOHVwZmt6anJzM2RGNk5Ga0J6U2N6ZksyU1V4L0hPakIzNWxBNVR1N2x3YmdYNmZnbnNRdHh1allGS2JZOENoMFM1MjRaVUU3MWdXNlJaTDRacEh0RFlaY2hCMXUreDk5K0MxbGk3WEdYSERLTEMzMXZUY2JZY1NPREFmMTlxa3hrRmhOZmQ4MVkiLCJtYWMiOiI3ODJjMTRhMjQ4NDc2NzZlYjg4MjU1MWFmYjk4OWYxODljOGY3YzViMmFmOGFmMTNmY2YwYTk0OTJkM2YwYTcxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InM4dmVlRGNkK0svMWF4cmdHcEIrbkE9PSIsInZhbHVlIjoiekhvU25lc0Rza1lTbW5mRzNFd2hzdS9IWmhaR21zLzhZSmxLc3V2WnFXR2FkemowQU1KVHFOTUVEb0sxL2FLaDJxWVlDL3BQenEyb29NTW1FUkZ2YlhmMlN2a09ubzZxbTBJWlhxcVhlSTdyYkp2blVVbDlSeWpwUnpOaVVHcHRQYncwSVRMYUtac3hrRjdkMWVLM094bVg1WGlQWGxVcnVGYVYzdmJIZVdGWmhTTGdRTnRlaUpOMzFGNVNYSHJJd0VIUm1Yb2VadE1JdlZwVjdEOU81emR6Y2hxYk80NVJWV2pCZ3IvTGd5NTBUYURBc2lMVjBtcUZuc09OWmpMZkVGL2wrdE1qYWRuZFpMVXdRdjFSaE1PdzN4S2pNbk9ibnlxSXFlMGpmL0tEaVM5Q3lhd1B5MlEydDRySXVCYy9IdVpSOER6a2hrWXhuY0kvZlpJbnR0WmEyaEVlNll3RnRkSnNZc1RFY0d5V01OV3BFTEdrWnJEaE9rbTJ6MjU5VDNWekJlY29aU0UwUzBoc01ZWW5KWXNEc1JxbWtJYVhERm1WVElhbWJYMlQ2RDdVb3lOMGI5dlgrSjNGWEtFNjJkejViMjJlZkdwUmpwTG1UV0cwRjJmdzFMb0pMYStYWXBpTTZHR1VHY2pwUXpVRWxta28yNHBBZy9RSnlrc0kiLCJtYWMiOiJkM2VkNDIxNmRlOTRiNzJjMTdhNDRkYTdjYTk2NDIxY2EwNjRiNjU2ZWNhYjMxNjE5Y2U4MDhmZWZjZmRiMDY0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1931232136\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1012222354 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1012222354\", {\"maxDepth\":0})</script>\n"}}