{"__meta": {"id": "Xbf498baf7247a743034746d2f95741a4", "datetime": "2025-06-27 02:25:38", "utime": **********.595324, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.204008, "end": **********.595338, "duration": 0.3913300037384033, "duration_str": "391ms", "measures": [{"label": "Booting", "start": **********.204008, "relative_start": 0, "end": **********.526834, "relative_end": **********.526834, "duration": 0.32282590866088867, "duration_str": "323ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.526843, "relative_start": 0.32283496856689453, "end": **********.595339, "relative_end": 9.5367431640625e-07, "duration": 0.0684959888458252, "duration_str": "68.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50947520, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01097, "accumulated_duration_str": "10.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5537412, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 13.674}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.563437, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 13.674, "width_percent": 3.555}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('2000', 0, 22, 8, '2025-06-27 02:25:38', '2025-06-27 02:25:38')", "type": "query", "params": [], "bindings": ["2000", "0", "22", "8", "2025-06-27 02:25:38", "2025-06-27 02:25:38"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.574259, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 17.229, "width_percent": 36.372}, {"sql": "select * from `financial_records` where (`shift_id` = 47) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["47"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5798032, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 53.601, "width_percent": 4.011}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (47, '2000', 22, '2025-06-27 02:25:38', '2025-06-27 02:25:38')", "type": "query", "params": [], "bindings": ["47", "2000", "22", "2025-06-27 02:25:38", "2025-06-27 02:25:38"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.581396, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 57.612, "width_percent": 20.146}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-27 02:25:38' where `id` = 22", "type": "query", "params": [], "bindings": ["0", "2025-06-27 02:25:38", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.584727, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 77.758, "width_percent": 22.242}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "success": "تم تحديث الرصيد المفتوح بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-1686443799 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1686443799\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1113869353 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1113869353\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1699589604 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2000</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1699589604\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-744162485 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">68</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6Ilc4ajJKV2h3N3BSL1pSSnVweG5HaVE9PSIsInZhbHVlIjoic0wxalRKbC9qM3BRbDlTeFhDQTNZNXlrZEVZK1E3eTk1NHVSaktteTM2VGdkUDg4NGx2N3c3ZEpOeUhkVzRXbE83cUp0VGgyK2VLY1NHdWxmaVVQd3NoejJuSFNMQnRFaExNSVNYbEJmYnkrcnJ3b3dTRGc2VmMxNEtqK1MrcC9IUEZjOVFpV3pXSURIdzhNOWR1d2pGYW5hWURLSHhWUERUOEtWeENsVXZOcnNoamxUVExlVmRpekZPby9EVmRyc3E3cVN4QjlSU2hqT1NLSnAyS0xUSW9VVnFaQkVkQ0s0aXM1TlVOajZXVEI5R0ZSdmRTSm11RjI0UlBySy9uZWJXR0dHeWQ2ckRKdkF2cE81N2Zwa1ZjdU1LdGpmblZjc3QvM0hiRGNKV01VRVExYzdhdFg4dWU3SHBSL2tXL01nWEQxSTB2L3NuTDNCcmhmbm9NVWRtbnoyM3B4ZnRZd3QreU9yMUJJU2ExWW9hYmpnTkR4cU02NTVMKzhJd2FGcDRZWTIrRERwMEtiR2JkSXpFWC9KRFAzazNGQ29mV0NLVmFrbEdZckxvRDk4bmZqQUZ2bVRTMmkyK2g1VjlKM2NqVktqaEdzL0NSalBVVFRhUHVmZkk1aDhtcFV1emszMW1wUndKU2w3U3RNeWRLMkx5RjVJTVA1c3BpOGRsK3IiLCJtYWMiOiIzMDE3OWMxODBkY2Y5NGZiOTQ1OGE5OGJjYWY0MDFiNzg5ZDUwYTVmMmJlOGIwMGM1ZThiYjUyNWI1ZmQ4OWY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InR1NkVpUVNNTWFNOEE1L2IwNmNSWlE9PSIsInZhbHVlIjoiTWNVSnYvS2JGamtmMlBxdVpxRVVRMGFDNU1WR3RnRHNESXlUMEJzL1FkT2hhbzNjc3NEUEM2NlBmOGxIaUt5Z3lxZHJ0Y2J6UmhkWXNHQlArNzJiYkRQL3RaYkdjZ0xLcUlHZkU2SGxrM1BOdSt3WFUzRFpPWURkbHZlelFWVmpFdEVCbW1HbTczVEhhN3EvM3Y1QXdiQnVwS2p2bkNnUTZVRm41ZkV0OHliWTJoaDBRRDkrK2h2QkN5eVQ3TllYeUdMdFhkNkk1VXZFdXgxR3hIV0pSS3VqM1ZiWXNkUWlQa2g4Zkt6bE91SE9iMGVvUTdGK0lXM24wclFCSFpRVm5sRlZ1MmE5WmlXQTB1SUI2M2FUWGxQK2QvdXMvNk4yelJ2NVN6cU9Oc1pxa1ozSzcxVDhKTy9DWWxWQVRPa2tOS3FCc1JJK1B0Nkltb3hnNXV5ZkI1TkU3VXMxakhURTdLVzdMa2ZqeXZ4SUhnTXdVTkFBb1F0R1oyMVE4MW9LWFhpZEZMdWR6K3VHN281QklFR2xVOWh5cUlrbDRkNnVXckQxQXE2eE9hUkZYTURiRkpOdlZ0ZHdQbjJrNVJrdW9raVFIRlNsWGlZLzBEWGd6Sm14MVZlNkRZeXBkZkRadEVnK20xYk9CR0t5dEtVRG1VeThvaUZORFQwNHp0RnIiLCJtYWMiOiI2ZmRlODU4MTkwODlkNjFkOTQ4NzkxNjRlOTViZmY4MDdjMDgyYzVlYjYwMWQwZDMxZjhlNzY5OGE3YjVlNDEwIiwidGFnIjoiIn0%3D; _clsk=1wa64lz%7C1750991135471%7C25%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-744162485\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1080313608 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1080313608\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1636732890 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNCdWdNbSsxZ3NVR2twaTZ5T3hXWHc9PSIsInZhbHVlIjoic3dhR2hlVUJuS0t4Mk9mYndzaWhHWmNRaWtxdmVPUE9sTzNMVmlxVmVYa1U2VG5DUXRtcjFiWnFjZFh4Zi9oOThKejlCYXRrS2h2aGJNYTRDUUtjSDVxNFVNeEhFWWRMS3p3b0kwQkd1K3ZrTVJLZjYrQXVHaHN2cjFKYnVZNG9SZExMc3hxc1RZMDZ0TlA4aVRRTjA0czJMUy9mUlpDbEEvd1h2UmN4NVh3Q21SaWRRK29hUWxDcURaTUVvOHRMU21Pc1V3VUc5OUVwTlFreWdZQXQ4UjN3VmVzeTBBSlFQTi9McVJXMWphZ1R1NEVkdHFZNjAxVytiTSt2eklUS3VBNGZyWkJrbkxtZmUveStxNG1hNi9McnozZkNSNFBnaUhRRCtOOWFWeThadUM0aW55emp2OEQzRDg4c25rN21BQ1huRUFFRDRIaWVNNndPemhrcGxZaVJ5TFloUTJSUHphczlla1JVM0tDaHY0V1RSc0xXRHUvdFhOTmNxZUxSNnZKakN4ZW81TjVCRTdOUXhrOXZFVklZSVBVdUhFR0JIeEdIbEFPQ0svQm1uYTI5Ni96NDE1d1Fod3g0aUpaQXNJNlczTG8yZGxXaWIrV2cwelhXMDhGODdXKzZwdGNBSlRDbzNrU1dSdGthVE9nTENoZmp5REs2ZHRhSmkyV3giLCJtYWMiOiIyOGI3ZTVlYmYyNjk3ODRlNjAxMWFkYzhkM2U3MWE4NzY5NzRmZmNiMDBkZTA5MmIxMzEwZWU2ODM4NGI2MjM1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRNWG11b3Y4MkpEOVUzbk5QVzNJbVE9PSIsInZhbHVlIjoiWkUrK09qZzc5R1hScjBrd2JRUlIwQzY4TXlUQ2ROUzVJSmhRMDVBQmI3MTYxTWFDdUsybnJOam13ZjBIL3Z5b1dSZlZ2TWVPeitOMEcvNFZRc01qa1FBZ2hDeEhyOHZaME5uVVYvaVBXUzdLZmJUNEVBMVlPQkFLRktOYk9RWnJncHcxSlJ0eXp2SG94QXZUMkRWRno4cTZ1NkJiTWRXemVnNzY4b0hPbWZSRGovcE5xQkplRldQUXJwR3Z1WnRjMHBVMDdNamxSeTZlT0JrQUdRbXRneEx3R00zb09nZlFTRWhuWmUvM0dFRHcrNWZoQXgrWWxodHMraUt6Q201Rjl0TytXYVhnVkFYNjhxcklzd1RuYk56OEVEd21YdURtamlHUHF1SGlac3UwUm5pbUpuYzk3bVNjQnR0VzZGdTFmeDZlVlZteUFpcWVkeG8rbzF5UWgrSWZ2TFBXSVRFSXZnWUJMa0pZbnVSTGtoR1Exa3I5OWgwRjA4SGJUNnVXZ2diQlRUdkdCaVh3dS9aZmhZT2FTMExpanZHdjkyOG8yblA0Nno2SWVXTTgvYVQxMHFwVTdRNXRlN25PMXRENnFwaW4xL2w0cDh6QVgzQXZSWVNPY1g2WHBSd1FFdy9qR1I1Sm9vdUxHaERHLzAyZ3l1cDZ3WFl4ZVpVQlVJeW4iLCJtYWMiOiJkYjk3M2FiNDlhNTZmYzk2ZDA2MzBkOTRiYjg5ZDAxMGZhY2FkZDU5NDY1NjEzOWUxMmIwOTY2OTBlZWY0N2UwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNCdWdNbSsxZ3NVR2twaTZ5T3hXWHc9PSIsInZhbHVlIjoic3dhR2hlVUJuS0t4Mk9mYndzaWhHWmNRaWtxdmVPUE9sTzNMVmlxVmVYa1U2VG5DUXRtcjFiWnFjZFh4Zi9oOThKejlCYXRrS2h2aGJNYTRDUUtjSDVxNFVNeEhFWWRMS3p3b0kwQkd1K3ZrTVJLZjYrQXVHaHN2cjFKYnVZNG9SZExMc3hxc1RZMDZ0TlA4aVRRTjA0czJMUy9mUlpDbEEvd1h2UmN4NVh3Q21SaWRRK29hUWxDcURaTUVvOHRMU21Pc1V3VUc5OUVwTlFreWdZQXQ4UjN3VmVzeTBBSlFQTi9McVJXMWphZ1R1NEVkdHFZNjAxVytiTSt2eklUS3VBNGZyWkJrbkxtZmUveStxNG1hNi9McnozZkNSNFBnaUhRRCtOOWFWeThadUM0aW55emp2OEQzRDg4c25rN21BQ1huRUFFRDRIaWVNNndPemhrcGxZaVJ5TFloUTJSUHphczlla1JVM0tDaHY0V1RSc0xXRHUvdFhOTmNxZUxSNnZKakN4ZW81TjVCRTdOUXhrOXZFVklZSVBVdUhFR0JIeEdIbEFPQ0svQm1uYTI5Ni96NDE1d1Fod3g0aUpaQXNJNlczTG8yZGxXaWIrV2cwelhXMDhGODdXKzZwdGNBSlRDbzNrU1dSdGthVE9nTENoZmp5REs2ZHRhSmkyV3giLCJtYWMiOiIyOGI3ZTVlYmYyNjk3ODRlNjAxMWFkYzhkM2U3MWE4NzY5NzRmZmNiMDBkZTA5MmIxMzEwZWU2ODM4NGI2MjM1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRNWG11b3Y4MkpEOVUzbk5QVzNJbVE9PSIsInZhbHVlIjoiWkUrK09qZzc5R1hScjBrd2JRUlIwQzY4TXlUQ2ROUzVJSmhRMDVBQmI3MTYxTWFDdUsybnJOam13ZjBIL3Z5b1dSZlZ2TWVPeitOMEcvNFZRc01qa1FBZ2hDeEhyOHZaME5uVVYvaVBXUzdLZmJUNEVBMVlPQkFLRktOYk9RWnJncHcxSlJ0eXp2SG94QXZUMkRWRno4cTZ1NkJiTWRXemVnNzY4b0hPbWZSRGovcE5xQkplRldQUXJwR3Z1WnRjMHBVMDdNamxSeTZlT0JrQUdRbXRneEx3R00zb09nZlFTRWhuWmUvM0dFRHcrNWZoQXgrWWxodHMraUt6Q201Rjl0TytXYVhnVkFYNjhxcklzd1RuYk56OEVEd21YdURtamlHUHF1SGlac3UwUm5pbUpuYzk3bVNjQnR0VzZGdTFmeDZlVlZteUFpcWVkeG8rbzF5UWgrSWZ2TFBXSVRFSXZnWUJMa0pZbnVSTGtoR1Exa3I5OWgwRjA4SGJUNnVXZ2diQlRUdkdCaVh3dS9aZmhZT2FTMExpanZHdjkyOG8yblA0Nno2SWVXTTgvYVQxMHFwVTdRNXRlN25PMXRENnFwaW4xL2w0cDh6QVgzQXZSWVNPY1g2WHBSd1FFdy9qR1I1Sm9vdUxHaERHLzAyZ3l1cDZ3WFl4ZVpVQlVJeW4iLCJtYWMiOiJkYjk3M2FiNDlhNTZmYzk2ZDA2MzBkOTRiYjg5ZDAxMGZhY2FkZDU5NDY1NjEzOWUxMmIwOTY2OTBlZWY0N2UwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1636732890\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1691050667 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1575;&#1604;&#1585;&#1589;&#1610;&#1583; &#1575;&#1604;&#1605;&#1601;&#1578;&#1608;&#1581; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691050667\", {\"maxDepth\":0})</script>\n"}}