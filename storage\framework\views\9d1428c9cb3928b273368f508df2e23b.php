<?php
    $is_sale_session_new = Auth::user()['is_sale_session_new'] && Auth::user()->can('manage pos');
?>

<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Payment voucher')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('pos.financial.record')); ?>"><?php echo e(__('Financial record')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Payment voucher')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('action-btn'); ?>
    <?php if(Auth::user()['id'] === $payment->creator->id && $payment->status == "accepted"): ?>
        <div class="float-end">
            <form action="<?php echo e(route('payment.voucher.export')); ?>" method="POST" accept-charset="UTF-8">
                <?php echo csrf_field(); ?>
                <input type="hidden" name="voucher_id" value="<?php echo e($payment?->id); ?>">
                <input type="submit" value="<?php echo e(__('Print Voucher')); ?>" class="btn btn-sm btn-primary">
            </form>
        </div>
    <?php endif; ?>
    <?php if(Auth::user()['id'] === $payment->creator->id && $payment->status == "pending"): ?>
    <div class="float-end">
        <a href="#" data-url="<?php echo e(route('payment.voucher.edit', $payment->id)); ?>" data-title="<?php echo e(__('Edit voucher')); ?>"
            data-ajax-popup="true" data-size="md" data-bs-toggle="tooltip" title="<?php echo e(__('Edit voucher')); ?>"
            class="btn btn-sm btn-warning me-2">
            <?php echo e(__('Edit voucher')); ?>

        </a>
    </div>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row timeline-wrapper">
                        <div class="col-md-6 col-lg-4 col-xl-4 create_invoice">
                            <div class="timeline-icons"><span class="timeline-dots"></span>
                                <i class="ti ti-plus text-primary"></i>
                            </div>
                            <h6 class="text-primary my-3"><?php echo e(__('Issue')); ?></h6>
                            <p class="text-muted text-sm mb-3">
                                <?php echo e($payment->creator?->name); ?>

                            </p>

                        </div>
                        <div class="col-md-6 col-lg-4 col-xl-4 send_invoice">
                            <div class="timeline-icons"><span class="timeline-dots"></span>
                                <i class="ti ti-mail text-warning"></i>
                            </div>
                            <h6 class="text-warning my-3"><?php echo e(__('Issue Date')); ?></h6>
                            <p class="text-muted text-sm mb-3">
                                <?php echo e(__('Issue Date')); ?> : <?php echo e($payment->date); ?>

                                <i class="ti ti-calendar mr-2"></i>
                            </p>
                        </div>
                        <div class="col-md-6 col-lg-4 col-xl-4 create_invoice">
                            <div class="timeline-icons"><span class="timeline-dots"></span>
                                <i class="ti ti-report-money text-info"></i>
                            </div>
                            <h6 class="text-info my-3"><?php echo e(__('Voucher Status')); ?></h6>
                            <p class="text-muted text-sm mb-3">
                                <?php echo e(__('Voucher Status')); ?> : <spna class="badge <?php echo e($payment->status == "pending"?"bg-warning":"bg-success"); ?> p-2 px-3 rounded"><?php echo e(($payment->status == "pending")? __('Waiting'): __('Accepted')); ?></spna>
                            </p>
                        </div>
                    </div>
                    <div class="row justify-content-center">
                        <?php if($payment->status=="pending" && $payment->pay_to_user_id == Auth::user()->id): ?>
                            <a href="#" class="btn btn-sm btn-warning w-25" data-bs-toggle="tooltip"
                            data-url="<?php echo e(route('payment.voucher.confirm.show', $payment->id)); ?>" data-ajax-popup="true"
                            data-size="md" data-bs-toggle="tooltip" title=""><i
                                    class="ti ti-send mr-2"></i><?php echo e(__('Confirm')); ?></a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="invoice">
                        <div class="invoice-print">
                            <div class="row invoice-title mt-2">
                                <div class="col-xs-12 col-sm-12 col-nd-6 col-lg-6 col-12">
                                    <h4><?php echo e(__('Payment voucher')); ?></h4>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-nd-6 col-lg-6 col-12 text-end">
                                    <h4 class="invoice-number"><?php echo e($payment->custome_id); ?></h4>
                                </div>
                                <div class="col-12">
                                    <hr>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col text-end">
                                    <div class="d-flex align-items-center justify-content-end">
                                        <div class="me-4">
                                            <small>
                                                <strong><?php echo e(__('Issue Date')); ?> :</strong><br>
                                                <?php echo e($payment->date); ?><br><br>
                                            </small>
                                        </div>

                                    </div>
                                </div>
                            </div>


                            <div class="row">
                                <div class="col">
                                    <small class="font-style">
                                        <strong><?php echo e(__('Payment Amount')); ?> :</strong><br>
                                        <?php echo e($payment->payment_amount); ?>

                                    </small>
                                </div>

                                <div class="col">
                                    <small>
                                        <strong><?php echo e(__('Payed to')); ?></strong><br>
                                        <?php echo e($payment->payTo?->name); ?>

                                    </small>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <small>
                                        <strong><?php echo e(__('Voucher Status')); ?></strong><br>
                                        <span class="badge <?php echo e($payment->status == "pending"?"bg-warning":"bg-success"); ?> p-2 px-3 rounded"><?php echo e(($payment->status == "pending")? __('Waiting'): __('Accepted')); ?></span>
                                    </small>
                                </div>

                                <div class="col">
                                    <small>
                                        <strong><?php echo e(__('Payment Method')); ?></strong><br>
                                        <?php echo e(ucwords(str_replace('_', ' ', $payment->payment_method))); ?>

                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col">
                            <small class="font-style">
                                <strong><?php echo e(__('Purpose')); ?> :</strong><br>
                                <?php echo e($payment->purpose); ?>

                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\erpq24\resources\views/voucher/payment/show.blade.php ENDPATH**/ ?>