{"__meta": {"id": "Xca19f13d5f21ee9186c7df8844a4a9c8", "datetime": "2025-06-27 02:15:03", "utime": **********.971003, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.539285, "end": **********.971019, "duration": 0.4317340850830078, "duration_str": "432ms", "measures": [{"label": "Booting", "start": **********.539285, "relative_start": 0, "end": **********.921799, "relative_end": **********.921799, "duration": 0.38251399993896484, "duration_str": "383ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.921808, "relative_start": 0.3825230598449707, "end": **********.971021, "relative_end": 1.9073486328125e-06, "duration": 0.04921293258666992, "duration_str": "49.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45394432, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00256, "accumulated_duration_str": "2.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.953392, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.828}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9631321, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.828, "width_percent": 17.969}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.965538, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 91.797, "width_percent": 8.203}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990356916%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRVdG9iWmtXOXRjMEVRZGJZenVJdXc9PSIsInZhbHVlIjoiL2NWR3VRZ3ZmLzRkVkZxVmxMbkZtN3RZVktSc0JOYk5YT2hGaEtOMWZySlc5MGpWc0RpaStSamVWSkt2SU5QSDVsbHE1S2JiS0NzTWN6MzBjZkU4bjR5NmcydGRZREEwa0NKNXA0cGF5T1JISkdHY0d0Q3JFT1cwWmRxWVZUL0NqcWVabVFQY1pQS2Z4TlJrc0w3RUpiYTQwYnp2TUYzb3FiNlZXVG5xYUpEVXpQK2JpT0xvRkdqeFpTN0NvNzA3SkxkWlh4TGhTNjNQVThSWGMvai9GT2pVdHVYNVE0SWRDTVAwMzlwY0RwTklzNEJtM3lHOGdsMnJkYUl3cHpGTlFrUkZ2LzgwU3VISkNBcTVsRUU3bU1kRVJIaC9FMHNqbTNoTTBWanZzUkljaTFGU2xuRXhvNnZ4MHpuOHpsSGRVZkR1TVRhRjZ0NGxjc1NvdG52bGIwZGhmMGxVMnhyL2lEZ0NZejFZemFyNUtENmNNSUxLYTdjS3prNjQ1UDBDVm41NXk2Q3RkalF4OWZ1b1BhTjhJVkFLMVNnTG9TOVpua1o4YUIwTkJFS043aVNuWFkvSENzeDVtcnpDVGRKY0ppOTVKNTYwOGk4ZlUwMmJ3SzJQWkxCMCtkbUQ0elhZTSs5cXUxMzdLbXBWQzhHc1lWYkNLcXFpbDhGTlJ6YzUiLCJtYWMiOiIwNDc1NWFjNjgxZDUzM2QwNzYzY2Q5OTBkMzkzZmIxYjU1NjEwNTljMWZkODVhYzdjOWM1NzE4MmJmNGM5MzY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpaZTFiS2ZHYVhRTGRzTXg0TTJlZFE9PSIsInZhbHVlIjoieUFLdVBISGJYN2hKa0dTZG5QRUFrVXlEdVBhdmRTOVV2a1hHWFJ4YVRXQXI2SUNsZWl5OHBiVmtma3E5aGI2R2YzRER3TWJvVFBuT21TazJyRDhRWE5kOTB6Z0FvUlFkellRQkRVd3FOUjRkVHN1cjN1VndXUGp6MFp2bUIwZEhOTkJHTjRLdWxTSVJ5WVRoVmlvS0IvV0NQanptSGN4N0xMcjltRU5EY0I3TjdjWGh1SmUrRkNNZW1GQTRYYW9aeUNMTHFZamFNcWVmRmcyZUZYaFVZemV0RGdXZy84S0dYdllib3Flc0NyRjRwdS9ERW9LdEF6SXovNWdEZDE3ZGtEQ3ZIaGdmWS9GZDVmMzhSS0V4Q2w5YmhQeHdQZ1hyM0NnYnNmSzl5aHRmcjFSVlRBdTZRenNrcTlvODlPUDlYc082QmZGZTd3OEFMNnRwL3lJTVJlVjZkRVpibHQyR1RZME9UZGlIV3kwR3pmRVVPWXNzT0l3S3kyVGt2bHQ0S0REbTJGeFVIMWdzRUpWOHRMREtsaWwvTjVTWDNTcDdXd0w0UGRDQWx4bjM2dTRlNVVsemFmbG9CdzM4Z29BZlhCMGlVZ25SNTZRb3diTEpqcitkeWExZGhOWGNvOGRoN01oWjJIQjY1OUVjS212TnVkbU9tLytML05yc3pzeE8iLCJtYWMiOiJjMDdjYWJiZDQyNWVkZGY2NjA0NzE2NTc5ZGNjOGU0OTdhMzRmYzU3MTNjODljODlkYjkzNGJmN2VhNjUxMTk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1769453345 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769453345\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1753238893 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1vRWY0L210cTlic2c4QWJLdlhiSFE9PSIsInZhbHVlIjoiRVFWamU3VXhXUnkvZ1lTeDFJTW9vU0duaHBNeTJ2ejFMMmVKbEJyNmthN1F5Sm9JK085bHlWOVVrL2MvOFB0ZE1QQmV6RS9ORXkyMjNuNVE5TEtYaUNpRkdwbUI3bmM3UUFRQ255YUxya3VNazFNbWNQdHBnUk5XUVIrT29ZYkN1VmRsRGR5SDdSRnpCYXN6R3lqRGdwYzRKTEVYVUUxRUorZlNtZitORndxMU9IckV1OEZROWpJd2hES2pvOVFWVURueS8vYUN4NnYvL3VQYS9uOC9sbUVHYjN3NWQ3Yk1ydHh1MWdLWWw2VHJpL3dsUDVZdmNPVk5BcDQ2aXdwTDNyaGViYW5wOTYveWJLUTVURHFVNFMxOWxGQ28wRE10c3BFa2phMURSdGZ0UlkwN2krUXRsZGozZ2tMaHNQbCtVa1ZuNnVTT1p0cU82bTI4ZGVIM3dneWpHbWQ0SlRFRHZ2WkV6d2lVcFNVNi9pMWJ1SldIRWYzNS96cTVqK2p2ejQyNXJ1RjFJWVcyZ1U5L1g1UENyYkNsM01QV3lsTnVydWdwR0h3U3FnSTBUNE5mbHVXMzltcm9Kd2xIZTlnVEdEQTROVE9PS1JpYWZVWSt4QWVGaUdGNmRQazE2dGZIbVgvY3JmUCtTOTh1LzFGNnJhTXhVQ3lNZkl6MUpyYnYiLCJtYWMiOiIzZGZlMTIyNzNjODQwNDhlMTQ2MjQ3ZTUyM2JiZjM0YWRjMjUxN2E1M2ZiNDg1NDRiOTQ0YmM0MDQzNmJlZDZlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxmbXlXbmpkNldrcmhPTFBhZGNHUHc9PSIsInZhbHVlIjoiTGVrS2t5RXFtREFpbEd6dkdWczQvT3BZSlZIUTNVSjFveVVxQ2V1ZXpmZWVJRUFyanAzUkEyYWlSRmZkeWpiTElibzJwTnNvdGVCejFiREFPa1ZqeGNxMW5YVEc4djByUG1mQ25mVXU1d2szZHc5OHBZYnBWbUJlRlA0c3k2UEZaZkhHK2VkZGZEQkNlZmxYTUpDajZMZloxMTZHYVV0bWVqVDJEZlcrKzY4aWJ5Nmhqc1AxNy9sd1k5RlQ4eFVCelZsN2IxYWgzRjVFR1lYK1gxVnFLMTFVazdLRmRiREdPbkY0TXJtNCs1aVUxWFMycnBwTm5hVG43b3N4dGlKMUtDWWJGK2FhRlY2OFNYRUlWUTVaYmdTT0NtSkE4MzBXQWNvR3kxcXROZWloMUFQeVN3NzdWRndiMzd2dE1rdjEwNTZ5SW5lcXVtaGU0S21rRjhpRTJnZzZiM2FGdDAzVis0OTNFdUhLbndZbG1pNzUyVGlCdmlPVmJjbXlvcG5teCtmZGdrT3lxY3JSYzd1QW5oOGxReWNBUlRKRzZsNEdrdVBLcjVSb0d6NTh3aW9FamRXeGk3ak8yaEJVKzRYNHhaYkdsTW80M1hnSzFqOGltVWNGdWlRMXltbktHZGpMay9oMFFrTGlqd2MrNUl5VFk3M0ttSzlmM21ST1Y2VW0iLCJtYWMiOiIxYTBkMDU4MzgyYTU5ZjRjOWYzNmM0ZTA5YzFjMWQwOWQ2MmQ1NjU5MWEwYTk1MDlkZjIxYWRlM2QzYzlhNDE2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1vRWY0L210cTlic2c4QWJLdlhiSFE9PSIsInZhbHVlIjoiRVFWamU3VXhXUnkvZ1lTeDFJTW9vU0duaHBNeTJ2ejFMMmVKbEJyNmthN1F5Sm9JK085bHlWOVVrL2MvOFB0ZE1QQmV6RS9ORXkyMjNuNVE5TEtYaUNpRkdwbUI3bmM3UUFRQ255YUxya3VNazFNbWNQdHBnUk5XUVIrT29ZYkN1VmRsRGR5SDdSRnpCYXN6R3lqRGdwYzRKTEVYVUUxRUorZlNtZitORndxMU9IckV1OEZROWpJd2hES2pvOVFWVURueS8vYUN4NnYvL3VQYS9uOC9sbUVHYjN3NWQ3Yk1ydHh1MWdLWWw2VHJpL3dsUDVZdmNPVk5BcDQ2aXdwTDNyaGViYW5wOTYveWJLUTVURHFVNFMxOWxGQ28wRE10c3BFa2phMURSdGZ0UlkwN2krUXRsZGozZ2tMaHNQbCtVa1ZuNnVTT1p0cU82bTI4ZGVIM3dneWpHbWQ0SlRFRHZ2WkV6d2lVcFNVNi9pMWJ1SldIRWYzNS96cTVqK2p2ejQyNXJ1RjFJWVcyZ1U5L1g1UENyYkNsM01QV3lsTnVydWdwR0h3U3FnSTBUNE5mbHVXMzltcm9Kd2xIZTlnVEdEQTROVE9PS1JpYWZVWSt4QWVGaUdGNmRQazE2dGZIbVgvY3JmUCtTOTh1LzFGNnJhTXhVQ3lNZkl6MUpyYnYiLCJtYWMiOiIzZGZlMTIyNzNjODQwNDhlMTQ2MjQ3ZTUyM2JiZjM0YWRjMjUxN2E1M2ZiNDg1NDRiOTQ0YmM0MDQzNmJlZDZlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxmbXlXbmpkNldrcmhPTFBhZGNHUHc9PSIsInZhbHVlIjoiTGVrS2t5RXFtREFpbEd6dkdWczQvT3BZSlZIUTNVSjFveVVxQ2V1ZXpmZWVJRUFyanAzUkEyYWlSRmZkeWpiTElibzJwTnNvdGVCejFiREFPa1ZqeGNxMW5YVEc4djByUG1mQ25mVXU1d2szZHc5OHBZYnBWbUJlRlA0c3k2UEZaZkhHK2VkZGZEQkNlZmxYTUpDajZMZloxMTZHYVV0bWVqVDJEZlcrKzY4aWJ5Nmhqc1AxNy9sd1k5RlQ4eFVCelZsN2IxYWgzRjVFR1lYK1gxVnFLMTFVazdLRmRiREdPbkY0TXJtNCs1aVUxWFMycnBwTm5hVG43b3N4dGlKMUtDWWJGK2FhRlY2OFNYRUlWUTVaYmdTT0NtSkE4MzBXQWNvR3kxcXROZWloMUFQeVN3NzdWRndiMzd2dE1rdjEwNTZ5SW5lcXVtaGU0S21rRjhpRTJnZzZiM2FGdDAzVis0OTNFdUhLbndZbG1pNzUyVGlCdmlPVmJjbXlvcG5teCtmZGdrT3lxY3JSYzd1QW5oOGxReWNBUlRKRzZsNEdrdVBLcjVSb0d6NTh3aW9FamRXeGk3ak8yaEJVKzRYNHhaYkdsTW80M1hnSzFqOGltVWNGdWlRMXltbktHZGpMay9oMFFrTGlqd2MrNUl5VFk3M0ttSzlmM21ST1Y2VW0iLCJtYWMiOiIxYTBkMDU4MzgyYTU5ZjRjOWYzNmM0ZTA5YzFjMWQwOWQ2MmQ1NjU5MWEwYTk1MDlkZjIxYWRlM2QzYzlhNDE2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1753238893\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}