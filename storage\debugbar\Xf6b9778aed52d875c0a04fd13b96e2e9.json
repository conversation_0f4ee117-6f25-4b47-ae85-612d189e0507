{"__meta": {"id": "Xf6b9778aed52d875c0a04fd13b96e2e9", "datetime": "2025-06-27 02:34:36", "utime": **********.60324, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.17155, "end": **********.603254, "duration": 0.431704044342041, "duration_str": "432ms", "measures": [{"label": "Booting", "start": **********.17155, "relative_start": 0, "end": **********.529157, "relative_end": **********.529157, "duration": 0.3576068878173828, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.529165, "relative_start": 0.35761499404907227, "end": **********.603256, "relative_end": 1.9073486328125e-06, "duration": 0.07409095764160156, "duration_str": "74.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020640000000000002, "accumulated_duration_str": "20.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.559741, "duration": 0.01973, "duration_str": "19.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.591}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.588105, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.591, "width_percent": 2.422}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.593987, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.014, "width_percent": 1.986}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-878425027 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-878425027\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1338102370 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1338102370\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1579512157 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1579512157\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1169902570 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991674435%7C45%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNmeGRuZnp2c2g2VDhKWW4vQlJTdGc9PSIsInZhbHVlIjoiMXYwckt2UzZIaTR2M29UYlhhKytxUFZqdnZ5SGJNRXRlVm5HK0NUamR2UE1OY3RscytZb21ydTFQUWtNUFdJK1U4VmlQSkw3ZmZES2Zid1BSejFBaThFdlBEZDdFTEcrL0d1YXkrVkc3eFRtalltUlltNGh3VDFDN1pzdVEybjJqZkxQb3NiVWhoOE1zTktsZ08xd1FoUXp5dkVNeC91empEeWVPK0FqYUExZHF0aVo0UE5Hbmg1Z2U0NnhtWFZQUTZBUnIzMDVWcWsrZUYzQUJ5dDdMZ1lGOWRwWkEvS3hXOTZZR3FCTHBCQ3FVNjBKYlJHNyt1TkdpdXNHdDFvdjNOaHVsL0lISEN1QmJLZHhCRHg2RWh1T1BXc2lOdTFCMHl5c3lJbXhRUmpDamlIRlphTXlsZEtWalQzNVRWT2ZBYjVnbVJNVDdlMDZWdERrUlJMYkZucGJCWjRMcHVyK2Z3UTA3c295YTFSRCt3blpPa2NlWFJSaHhVanpDMjA4M2t2WGh5RVh3OVZoTmxPRWFqeGg3eTZhbVEzUHBGSkdSbDdKMnRxcHh4a3l2eGZLT0IwQUEzZWhLTEdaYVIyNmt1TnJwNHM5V0RzZXBTa0lkWmRkWStrMktMYTV6eDJoVmhaVjcyZExDMmx5SWpENk9QSE9HZ1NDaVNKNjVOZ0UiLCJtYWMiOiI3MDY5ZTdiODE0NDE4NjZmZjVhMTEwNTg1ZDI1NDk1NjNkNDg5ZDFlNzcwNjRhNmQzNGY1YzBmNmU4NmE5Y2QxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllIbUxwb3AvVlVPaWcrM0RRRWlqOXc9PSIsInZhbHVlIjoidmRFcWZDKzY2cHgrVkZhNUxSUllMSWtmb0trRUYvWjF5dTl5bHM3ajBUdGJHbWJRRGg4bDdVVXE4OVF0Q09GanBFQ3U1QWV3Vy9lY0FVQmZBL0JSRHoyZ0tnU29lV3l4QjAzelJBQk13L1BaaFpXSFY0aUZDOEVOZjgzMmMzTVBqRjFWSkFWa0J6MVpGN3c4cHNGTmIzMmlPbVBwMk94d2JLdHhTb3hGSEIxY1RnSkxXODFpaFhvbGFPNXkvdENlODN4MjgrMlpFL2R2U1BlVTR3K0JJM0JWZU90aFN3b2t3QnlGRGdCQVdVYlY4RVNjQ3FBL3g5QTNpMy9pQkR4eUFYa3plZ1dlTFEzYXdFUElxbGZmWlVUVk1CdlVvRlRNdld6bnpGOWNMM054OFRTcWcwb0NGanVQK3VKcENpbUxkdEhWMW43aXorNmtIa2QrZVBkTHI3UWRIQ2ttajRMZzloYzZ2b0pBTExjbU9TVXRKSDREVzBpQWVLWEdxWHY3UzcxNGkvd0pvSTY1ZGpyVG83L25FOFdQSjlHVUx6VTZPT3ZDZzF2TlpvUy9NdXl4ZVhwaFBIWEpaQmxUMkg3cDE0bWs5anZqMmJ4SmJyYmIyNmg1WFlUditSUFNDQ1hHRVlPSTZjWkJ4TU5rMlpwTFVEL1hUakVETGYxZzZveFAiLCJtYWMiOiI4NmNjNjA2ZmEzZWVhY2NhMjgyMzcyZTVlYTkyZWQ0OTgyMjI4YjE4NmU3MzVmZjRkMTEwNjAwOGJjYzhhM2RiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1169902570\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1739895068 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739895068\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-819697799 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpYNzBwZFAxVUFIU2hqWU91d1g3WFE9PSIsInZhbHVlIjoiS0dQRW9tZTdvWEpCQzlvaXlpOUV0RG5LUjRBZHBKYnZLOGQvM3hPVUF2TVRVRHRFdkc0RktkaW5VRTZZQUxQekliMzd6SFhaczVUVnRVVFRlcWlQekNFTDRtaXh0UTdYbSsySlZEMHFBUVArOVJQWm1VanhYM2hZNExGeVp3amhIQWZOeHU2RVkrcnU3cnlETVc3ZzhUelJFL3RCcXU4QjZtaHFpck5nVGFIRVI4MXpXbzY4Uys0aU5LZ2wvMEdwbUVYT0Z4WTFRRU9PMXlsZmZsVTNuMjZFNmlMWkRuckg4T0FvVWM4QjdySmcrSE5UUHJqWnBJWW41ZHM1b0pEb0dYVXNxTTl4SERNNmVGbzBDQnJraUVKRVZFZG00a3pUMEJEYUpzdXpDNk5HY2lYRFdmN3c0QkcwLyt2aXFPYlJueURQVkkxamdmaGU5TGI4OTdKcVlsSmtiYitmS2JSdVNpYnZ2NjkyUXJJdElxOHRxbkFWMWdxTFg3Y1BMWmhNUFRnaXFXZHVTYiswcjhrLzZXaDBnVTk0RnZjOVJoeXZCZEJjZlJkcVdrdm15bUZXSlpTcnZWcWxJYUwvNTdOa0ZuK2s2NnFJalAwNHQ0THMvQ1YrWTNpbjhDZWZ5VS96YUQvRlF4a00yd1VmWWhiOUJjQkRvLzNCQlg2akpKZUciLCJtYWMiOiIyOTk4MDgxYzIzZGNjZTVlYmM1ODFkNDA0YTMxN2JkZjFlMGY0MTYyOGJkNDI5ODcxNzIwY2FkZDA4ZGJiOGFlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ill3VlVkTUZFKzBIeU9vbDh6QlVjVFE9PSIsInZhbHVlIjoiYWNrb2d4a1lWaFFGR21LV1N5ZUZpZTZpVHloSThDTjFzaGsvNEd6Uk95QmJaNTRYc3B0ZGttcG4vVzVGWXNkUlVEK2xoMXdBVDJwdENKQ3FBQ1IzaVhDV2tabjNpOGtzbjF1SDVsVDYrVGRhRTVMMFJkVHpCS2lRV3hZdmpsTHErUWN0Y0daeUR1MWVTaEFBeTZIUm5BWWg3QkR2NXQxVjVuS0VucUlkSzR3TXcxY1dEMi93UGh6MXdqOHBqSXVaSlZmZkVzTktxQkhRL0dpY090TVpxUHJhRVRMTGtYRVNhOGkrMGJhRkVmZG5kWWxpcEpIa3ZoN2grazl6YkdjcGhkSStXdXRBT2N4YVBKcGRFYnJNcWQvZkJNWDFrQzlOWWxqa3AvN0hkOEc4RG91bXVLUkFIbW56bFRTUkJlZnlnOVFCTmNSaDBpdVlRU2Y0N2hhWUt4QlVzTDNYL3hxWlA3MWhadFRpQXg2QmNta3YvbVZTL0F2bVo2S3FLeWdIWTEvZkFXdGRzVnIvSSs0eWpIaDRtRTl5Ly9PRzE0NlRISDU5U3QxSVQxVy93MjBIWFJIZWYrRnhyeEJzWTI2YmFQYVkyMGVjU0pHbjU4QmhmNFRPT2FnNFZDYnJsZ2JxblE1cnBJcGhJcFE2ZzU3QmllMjRIOUU4VWVJUHZkTjQiLCJtYWMiOiIyYTNiOTI1NmYwZmI5ZDUwMGE0YzMxOTRhNzc4NmYwMjkzMjhhODU1MjI3MzE1OTQzMThhNGViNjA5NzMyYTdmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpYNzBwZFAxVUFIU2hqWU91d1g3WFE9PSIsInZhbHVlIjoiS0dQRW9tZTdvWEpCQzlvaXlpOUV0RG5LUjRBZHBKYnZLOGQvM3hPVUF2TVRVRHRFdkc0RktkaW5VRTZZQUxQekliMzd6SFhaczVUVnRVVFRlcWlQekNFTDRtaXh0UTdYbSsySlZEMHFBUVArOVJQWm1VanhYM2hZNExGeVp3amhIQWZOeHU2RVkrcnU3cnlETVc3ZzhUelJFL3RCcXU4QjZtaHFpck5nVGFIRVI4MXpXbzY4Uys0aU5LZ2wvMEdwbUVYT0Z4WTFRRU9PMXlsZmZsVTNuMjZFNmlMWkRuckg4T0FvVWM4QjdySmcrSE5UUHJqWnBJWW41ZHM1b0pEb0dYVXNxTTl4SERNNmVGbzBDQnJraUVKRVZFZG00a3pUMEJEYUpzdXpDNk5HY2lYRFdmN3c0QkcwLyt2aXFPYlJueURQVkkxamdmaGU5TGI4OTdKcVlsSmtiYitmS2JSdVNpYnZ2NjkyUXJJdElxOHRxbkFWMWdxTFg3Y1BMWmhNUFRnaXFXZHVTYiswcjhrLzZXaDBnVTk0RnZjOVJoeXZCZEJjZlJkcVdrdm15bUZXSlpTcnZWcWxJYUwvNTdOa0ZuK2s2NnFJalAwNHQ0THMvQ1YrWTNpbjhDZWZ5VS96YUQvRlF4a00yd1VmWWhiOUJjQkRvLzNCQlg2akpKZUciLCJtYWMiOiIyOTk4MDgxYzIzZGNjZTVlYmM1ODFkNDA0YTMxN2JkZjFlMGY0MTYyOGJkNDI5ODcxNzIwY2FkZDA4ZGJiOGFlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ill3VlVkTUZFKzBIeU9vbDh6QlVjVFE9PSIsInZhbHVlIjoiYWNrb2d4a1lWaFFGR21LV1N5ZUZpZTZpVHloSThDTjFzaGsvNEd6Uk95QmJaNTRYc3B0ZGttcG4vVzVGWXNkUlVEK2xoMXdBVDJwdENKQ3FBQ1IzaVhDV2tabjNpOGtzbjF1SDVsVDYrVGRhRTVMMFJkVHpCS2lRV3hZdmpsTHErUWN0Y0daeUR1MWVTaEFBeTZIUm5BWWg3QkR2NXQxVjVuS0VucUlkSzR3TXcxY1dEMi93UGh6MXdqOHBqSXVaSlZmZkVzTktxQkhRL0dpY090TVpxUHJhRVRMTGtYRVNhOGkrMGJhRkVmZG5kWWxpcEpIa3ZoN2grazl6YkdjcGhkSStXdXRBT2N4YVBKcGRFYnJNcWQvZkJNWDFrQzlOWWxqa3AvN0hkOEc4RG91bXVLUkFIbW56bFRTUkJlZnlnOVFCTmNSaDBpdVlRU2Y0N2hhWUt4QlVzTDNYL3hxWlA3MWhadFRpQXg2QmNta3YvbVZTL0F2bVo2S3FLeWdIWTEvZkFXdGRzVnIvSSs0eWpIaDRtRTl5Ly9PRzE0NlRISDU5U3QxSVQxVy93MjBIWFJIZWYrRnhyeEJzWTI2YmFQYVkyMGVjU0pHbjU4QmhmNFRPT2FnNFZDYnJsZ2JxblE1cnBJcGhJcFE2ZzU3QmllMjRIOUU4VWVJUHZkTjQiLCJtYWMiOiIyYTNiOTI1NmYwZmI5ZDUwMGE0YzMxOTRhNzc4NmYwMjkzMjhhODU1MjI3MzE1OTQzMThhNGViNjA5NzMyYTdmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819697799\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2088918315 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088918315\", {\"maxDepth\":0})</script>\n"}}