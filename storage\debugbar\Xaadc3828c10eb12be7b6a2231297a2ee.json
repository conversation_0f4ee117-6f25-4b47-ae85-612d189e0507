{"__meta": {"id": "Xaadc3828c10eb12be7b6a2231297a2ee", "datetime": "2025-06-27 01:05:07", "utime": **********.651216, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.238653, "end": **********.651244, "duration": 0.41259098052978516, "duration_str": "413ms", "measures": [{"label": "Booting", "start": **********.238653, "relative_start": 0, "end": **********.590198, "relative_end": **********.590198, "duration": 0.3515450954437256, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.590207, "relative_start": 0.35155415534973145, "end": **********.651246, "relative_end": 2.1457672119140625e-06, "duration": 0.061038970947265625, "duration_str": "61.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041864, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015189999999999999, "accumulated_duration_str": "15.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.615115, "duration": 0.014119999999999999, "duration_str": "14.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.956}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.637719, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.956, "width_percent": 3.423}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6434639, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.379, "width_percent": 3.621}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/ledger-report/229?account=229\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1170520369 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/ledger-report/229?account=229</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986299810%7C79%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlY2czczc1NjQWVCSkpzeEJXQi8zb3c9PSIsInZhbHVlIjoicnc5WGczUEdCZXJucHlvOTRIVU1SUHNUeDhzVHFKVmFLR3dGVnMxdHU2cVEyYzBMME8rZnBmSi8xcmJockNYeCtRMTNaQ0dyaitmbDJVN0ZkcFFsVFhKdGMxKy9YY1hGRTU4OXE1YjdibE1oM1FueFdLMXNvT2xDanFqUnlOL01MTk1ITHdnL2tUaVB5QzJNQStFMzRDZ0d4TWZIVGdBLzFmd0RURmYxcDhjOWczMitudWNhUS9raFpNYjNBNER0SStpY3RMbVl3SUNjankvMjBjcGJjcnlDTU96MWNjTWE2MWlJQUhZcGxtbWlxckJsaG9ObTJnMnJWdVdlS3V3YjlmbldHQTVGUWt5aHNSdXFuaW5Yb09wNXRiaWhGazBFRHZRRW9MWmhBVTQwYm1DNEtpOFpHZGNoaWlKdmV5Q2t3S2haa2laR3hXSDIvY0dHcU96S2FjTXZxQU8waEEzYzRFUktmVnZKamhaTWdPVWw1OTUzKy9pV0pNSTB3dW5wYXBRclEyeWV6V3laaXZ2MmhXNWc5UnQ3RlRwWjJWalp0cVJvUW9lbGYwRHBadEE3NnZ1OEtLTURNb3prTFYzQ0dXS29VQnRlODdRZzZzWjNDUWI3a3htTGREU0Z2ZUI1Sk1TcmJlVW5vUUp5amZzMnlFL2NFU2E1bzZmY082YU4iLCJtYWMiOiIwNzJhMTdjZjAxYjFlZjczN2RlMzI1MGZhMDM1ZjlmYmEzOGY4ZjI1MjM2NzIzNGY5Mzg3OTQzN2E3MTBlOGU5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBidGZLZHpOTUlLUWNWekg2NmJpS0E9PSIsInZhbHVlIjoiK3hGN0krRFk3REhVZjZxaTNuVldxSFl6MlZMVnluK2xSWFZMdGFvV0FtdVBhT2FaQit0bHpoS3NlMjZHRTNMemF1ck5YRHRGc3VPT0VCUEJaQmFnOUl0Y0VheFVGVzFYZERKRWcvVlJDVjJXWHJHeUY3NjkycXlSYnRhNmZkZGlpelMyVmcxUUJqZ2t2UnZBU1l1TFNHRE1LTEVRVUR4WE9ZeDVhSEdJc1VPMWpkMEZjOVJQakxTSWpJQ25hU1dsUnN3Z2dybVFpcWpMOXVXd1JGbWRSbkl0M3NwMlNGOUhiVVlVSG9TOUNlblAzSkwyc05mcTZTNkM5ME5CVXRrNDZnb2RSL2JNVE9DNjJUeW1YNms1N3FtekhidnNKdU9JeUxlOW01bEhjUEo0KzdMYTBqVXVyYTNaa2E1M0tMRHYwSHNJVXZiWnFnWFBSQ2VRWGhITmhydkJVU3hRODBHdDVNdmtRcSs4eVdMRVRmcG1qeU1IZzJhTW9jR2huTWhLUGRmYmg3c2JzYTQvSDhLQUx4Z2ZVM0ZMbXlJV2RDUjlPallDSXdyUWVKQ1dOL25yVXc0Yk5tOXQ0RUI4MTBEN0M3aGI5QWdJL1FNZWl5dzJYUlhURXJlUUNmYTg3cWQvTXFadEcvMHBwTGVURFYvL25TV0Zhd2V6aUxUYlRVMXgiLCJtYWMiOiJjYTM3NTA1YjgxZmViYTUzZmJjNmEwOWNkNTFkZTQwY2FjNTQ1NTM3ZDdjZmNmOTA4MDYyODg3M2M5MWZlY2M4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1170520369\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-170936779 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:05:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhoZGhvQnM0SG1EMnNkQnNNNytMcmc9PSIsInZhbHVlIjoiR1RSQ0F1SzdlU2NOazZReVFjdDl1ZFhoMU1YMkwrcGFhWGY2eElrNktxNktIcEFMb2o5QjluTUk5U3JiOGFDMU4rLzBsc1NXb0F2OG1FbkU0TG4wR2c4QUFaVjZMdXVqMG1DenRzdGtqOGdBbncxMUJNK0h6YnFHTDJqVUR0cmV0eEdSMXBqMDB6a2xEYnQ2NmMrNEhvRW5iMmpvVjUwblNuQzZkT2Y3VHhoUHZ3dXVSZkY4Z3RaR3dBNUNKZmVzbVJRT2tUUW05YitPVEFOUlJJZFpnT2E0eHJTcU1GSm5EZU94cG9iNnJPUlpGQ1FPTmQrditHZWl5L1l5QWVtby9uOFJuM1pIemZldEJ6SnhmYmF1UDFjR3REejlzbWVxTTBWYnlZZHRESC9NdkF3ZkJtcVBMUkdVZzBtRmNMbEhsRFU5V2NEY1ViaHYySlgxNEh6eXczbnBObFNVNFV4VHpkTUpwNXBTUEU2RCtZZWJkRndqUGZsQ0pFMS91TGc4bEh4Qk9zREZ2cTc1Z28yWG1vWG56VHUrUllxajVLTndwWWhyUG5CSGJJaVAyR3gvWlNNS0pzNzg5UEpRQURCdXBFdnpRUVpQQXJrcERPWUJPVGs4WWxlUGdOSTlDYTlKVHNuVWRFcGpYNGdYZHhDVXB5cUQyenlGL2hvUmlKMVIiLCJtYWMiOiI3MzM2ZDlhZDRhZjhmYTM4ZTgyNGJhZTc1YzhlZmMxZjQ1Y2VlMjM2OTIyYjUwZjA5MDRlZWU0MmNlZGJhOWE5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:05:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InA2dkxrUUZVSjZvWkxuRnVFcFlleWc9PSIsInZhbHVlIjoibVN1ZFBGU3Vnb3J3eDZqRUJsaXkzTnNpc0c3SGppS0FkOHpXZHBFTmN4NFZKWDZNZlEvM2J0MmlFQzlFa0s4ZEpPakJqZHFzeHBpZUczdzB6MFpVOW1QR2hYYlhBaEpWYksvV3ZRZm8ydzhSVkJFVUd5MEgwU01uZG43bzFoZ1B4NW94bW1UUXRGSHVHMk9zRnV4dXh5NUNDeGxOR0N1Y0E1YlJpNzFZVmM3dnJhQnlNSm5DK25oaHNnWTFZdXNRSmN4bjljbzZCZTNxeU9mSkp2K3RDUkJDc1lZenM5aFluVnFuRjZOUjMxL0lLR2xCVEJ0akMyVUxzT1VXaEtTZVZOaGoxdjF5Y0xhR0xlSlZBbDd3am1yUGQyL0k3YXFtcVh2SlJBOFNreGVjZjgyUE5nVEJFcHlJdEQ5SXA0UEo3OGdFdi9HSWhFQVkyVWM5SDBDNEFJMHBXZFl0UVdrNzFYYzBWaU1OYnhSMWJGS3N4RWo1eVpFZXFFN3prcElBOGNxcmQyd1gxUTlLeWFXcTJWZ1loSm5vWlhUeFUrNEpkS2kzUTdqVnB5aGw2eXErVVBEejZhQTYrSnkwNk9jL3psbHU2TEtkZHZPK2pKY3FQNnhDUmhKdlJjRitPWCtsWDVNSWFQU2tIYm82SWVrWEc1MHp3SHE5ZEtlcDErSFkiLCJtYWMiOiI3ZjNiYWRlNDBiNWM4OTcwZGE3NTZhMDhkZTFkZjdjZThmNGM5Mjk3MDJlOGQ0YzI5YTRkOWFjNTdjNWVmZTU2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:05:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhoZGhvQnM0SG1EMnNkQnNNNytMcmc9PSIsInZhbHVlIjoiR1RSQ0F1SzdlU2NOazZReVFjdDl1ZFhoMU1YMkwrcGFhWGY2eElrNktxNktIcEFMb2o5QjluTUk5U3JiOGFDMU4rLzBsc1NXb0F2OG1FbkU0TG4wR2c4QUFaVjZMdXVqMG1DenRzdGtqOGdBbncxMUJNK0h6YnFHTDJqVUR0cmV0eEdSMXBqMDB6a2xEYnQ2NmMrNEhvRW5iMmpvVjUwblNuQzZkT2Y3VHhoUHZ3dXVSZkY4Z3RaR3dBNUNKZmVzbVJRT2tUUW05YitPVEFOUlJJZFpnT2E0eHJTcU1GSm5EZU94cG9iNnJPUlpGQ1FPTmQrditHZWl5L1l5QWVtby9uOFJuM1pIemZldEJ6SnhmYmF1UDFjR3REejlzbWVxTTBWYnlZZHRESC9NdkF3ZkJtcVBMUkdVZzBtRmNMbEhsRFU5V2NEY1ViaHYySlgxNEh6eXczbnBObFNVNFV4VHpkTUpwNXBTUEU2RCtZZWJkRndqUGZsQ0pFMS91TGc4bEh4Qk9zREZ2cTc1Z28yWG1vWG56VHUrUllxajVLTndwWWhyUG5CSGJJaVAyR3gvWlNNS0pzNzg5UEpRQURCdXBFdnpRUVpQQXJrcERPWUJPVGs4WWxlUGdOSTlDYTlKVHNuVWRFcGpYNGdYZHhDVXB5cUQyenlGL2hvUmlKMVIiLCJtYWMiOiI3MzM2ZDlhZDRhZjhmYTM4ZTgyNGJhZTc1YzhlZmMxZjQ1Y2VlMjM2OTIyYjUwZjA5MDRlZWU0MmNlZGJhOWE5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:05:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InA2dkxrUUZVSjZvWkxuRnVFcFlleWc9PSIsInZhbHVlIjoibVN1ZFBGU3Vnb3J3eDZqRUJsaXkzTnNpc0c3SGppS0FkOHpXZHBFTmN4NFZKWDZNZlEvM2J0MmlFQzlFa0s4ZEpPakJqZHFzeHBpZUczdzB6MFpVOW1QR2hYYlhBaEpWYksvV3ZRZm8ydzhSVkJFVUd5MEgwU01uZG43bzFoZ1B4NW94bW1UUXRGSHVHMk9zRnV4dXh5NUNDeGxOR0N1Y0E1YlJpNzFZVmM3dnJhQnlNSm5DK25oaHNnWTFZdXNRSmN4bjljbzZCZTNxeU9mSkp2K3RDUkJDc1lZenM5aFluVnFuRjZOUjMxL0lLR2xCVEJ0akMyVUxzT1VXaEtTZVZOaGoxdjF5Y0xhR0xlSlZBbDd3am1yUGQyL0k3YXFtcVh2SlJBOFNreGVjZjgyUE5nVEJFcHlJdEQ5SXA0UEo3OGdFdi9HSWhFQVkyVWM5SDBDNEFJMHBXZFl0UVdrNzFYYzBWaU1OYnhSMWJGS3N4RWo1eVpFZXFFN3prcElBOGNxcmQyd1gxUTlLeWFXcTJWZ1loSm5vWlhUeFUrNEpkS2kzUTdqVnB5aGw2eXErVVBEejZhQTYrSnkwNk9jL3psbHU2TEtkZHZPK2pKY3FQNnhDUmhKdlJjRitPWCtsWDVNSWFQU2tIYm82SWVrWEc1MHp3SHE5ZEtlcDErSFkiLCJtYWMiOiI3ZjNiYWRlNDBiNWM4OTcwZGE3NTZhMDhkZTFkZjdjZThmNGM5Mjk3MDJlOGQ0YzI5YTRkOWFjNTdjNWVmZTU2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:05:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-170936779\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/ledger-report/229?account=229</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}