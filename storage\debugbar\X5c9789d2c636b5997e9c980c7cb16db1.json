{"__meta": {"id": "X5c9789d2c636b5997e9c980c7cb16db1", "datetime": "2025-06-27 00:15:08", "utime": **********.562659, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.1188, "end": **********.562677, "duration": 0.4438769817352295, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.1188, "relative_start": 0, "end": **********.511395, "relative_end": **********.511395, "duration": 0.3925950527191162, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.511405, "relative_start": 0.3926050662994385, "end": **********.562679, "relative_end": 2.1457672119140625e-06, "duration": 0.05127406120300293, "duration_str": "51.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028616, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0034400000000000003, "accumulated_duration_str": "3.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.538602, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.244}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.548887, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.244, "width_percent": 13.953}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5548139, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.198, "width_percent": 21.802}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1228560696 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1228560696\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-524231021 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-524231021\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-294844090 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294844090\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1380235981 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983302495%7C48%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVRY0pwVmwrMWtjNEtPMHM3YlJWY3c9PSIsInZhbHVlIjoiUmh6N0l2R2VDOEUyVVI1ODhDWTVSOTZqT05QcndPcnlUNEJyT1lVMktZVk81cmJ6QkNaTHdiNFZLZWE1eXRFdjVYTVRBbHk1ZmVXNmNwbjRQcEpuTFEwNlNBMktRakJvV3djeDc1UHNnbUExTHQ4ODArVEtVa1cyS3RCQVQ1RXA5MEk1cmtHajVrdGxtbVNUcHhCV21oNVBXbE5CYTdEQlZ6UDE3RkRoUzVxajdtb1RqTytGVkNmdER6b01vTjRVTy91MUp2UEp1MmdPb3kyTzkxME1xSmlWR0pnUlVZKzBIbTdGN3FickxNZDNFYUZ4ajF3SldHR0VveDBFMmRpWW9rcjBIaS9WVUo3VWRsZXV6L0RhZ3UyeVd3L05FSUh6N2VLaUZmSWl3L2hFaTFJNmo2aFhXa1Boa0dNSktsRE1Cc3hzaUYremlKZkNjb0xHdGVTVWc3ZC9NSUNabGdRdlFBcjVXVlNadnpXYy9nakF2RVZWQjBrZ3JlV2VTYnd0QmtkTTdWQXhTckZvejdoVnE5QVNqQWpSZEZxMFFPeWFQZm40enpSOXIvVkdqaWExdWprRkR3NXZ6NVJaTnhFbm5nbFNUY0cxdDZEMzVOTGI0RzFUU0dRQlRvWitsK3llUmFNMHNoMkVNa3dUT3RlTnB5bTU5aVJ0dVRYU29NOS8iLCJtYWMiOiJmOTQ4ZGJkNzQwOTcwNzU0MTg1ZGI2MmYyMmE1YjE4MWY1OGU2ODU1MmMwYmExY2JjOWNmNzRhOWI5YzBmZDgyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii8zZk5NSmFaZE9tOXBqYmJ5bjIxK0E9PSIsInZhbHVlIjoieUVhbjJQL1NhT3BZOGhKdWw5YTZRcmxKNVRTY2xBK0lLQ21DcnpuNVdhRkdTbnQ5dlo3Z0U1S2EzR0R4M0daTUdUYVVqNFU0ZHpEbkpUQ29GN0lsM2tlY0xHVndVY3pJWFZSR09VYXU1YkFzVGozQnJjdnkzR2xHOWkrVEpEWDljU1FpSkpHWGR4NkNnbkFYaWlRVzZra1dBb1pRWWxUVGpJdkVOd21YeXR0bjYvV2ZybzZhem1raUZIT0cxQXdZSXZrTWoxemZHNEM2Ti9oaEtpYmR4OGFJOTgyQ3UyRXJzVS8wcXFVclNZSm9yUmFQU284VUJKaU1QNkh6WnI0UTFBZVhNTjZIcG1FYVJMVndFczZldzJEVVJjdWV5dUp4VXRxem1vY1ZHZExLc0l5QmZUaGpBSWEzRENtcWM5VFJPd2YwWlVldmVvMmlodUpRTnFYQ3FTNWI3YjFmQUI3Z2hXL29iYTVrSURxbFNSTDhrVVZwUUkvWkhaaytQazNZYUlWa1d3WTBZcGpTOGJVMFgrZDZ0dWNxeVR2eUZubmZBeTNsV05xS2ZkQjFjY3RzM0RxRkQ1Q2h6QytBZWVHb0lITC9YTG1NbUlvQ2R1ais3bEt1QWs2ZUM0dUVYL3dWVi9xSEZ3ZnNDNWlRN0pyVjhKcW1ZdXRoQ3c2NklBQlQiLCJtYWMiOiI5NWI1ZDM1ODljYTk4NmEzNDJjNWU5MGVhNjQyZDlmZjcwMmIxNWRhMDg4ZmE3OTc5MjRjOThjYjZlMTA5NmE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380235981\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-883621376 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-883621376\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2105034765 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:15:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklQNkpLdlFkRHBsWFhNQldtcWo3THc9PSIsInZhbHVlIjoiNGFveHFqamQ3SGdiZUV5ZWpQSU1SZExMNmFreWVPOXRhRUkwckVOQ29sMXplZHcwV1FiOTJDK3Q1M09WYjM3RFExSDhHbWh5SUtuNDV1VEthM0ZsN2Q3SUNXeng1eHVWd0VjNHVlcjdNUDlod25RYnZaOTVEVGFBaHFlRkNWbFo5U1BEd29wNStBcjJKN1pURVI2bnpJTXF0V0RTakNES2pKTTBmZjJ5V3drY3B3U3lFcHN0b01HUnNwY0E3ZUJBOU1LZWdhMVA2UTYrTEpYaXIzK0NwRzZYeTNuektXNklCQk95WHNkMlhHUGNCWVZHNTRSZVA2VjY0MktLMHRIZDBkallqb0JkME5oVTYzSVpnb0JpUWNpNSswZFMwT1JEWlFnT2ZNL2hJZU1xNXJDOGJjRTIreTZ3Zk54ZjZiRTYzcHBnRlNOZFJZMHVmRjU2bG1zMnJRSTBJUWpkMW1YVVNnOE13aUlKTTBWOGhjUjBiNEl6QjRYWXdrK3hnRUUwL2tuUnJiTEtzczEvbWJtMmFMcmtMOW9Vb2ZYOVpiN3Z0K000ZEd1MGQwZGdXa2FwRmZML2tUaXlIbnVrOTF5d09NYXpGd1owRG55V05qNkY4QWoxUlRxYnF0Y2hRUFdmbWd1SzNiL0M5SU5MMFYrcklXVHBtb0w1OEhLVVVkbGQiLCJtYWMiOiJmZjZiN2RjY2E0MmE2YzlhNTUwNzQzODgyZjFkNWE1MmJkNTBjNzIyY2E5MzhlYmI2YzBiMzEyMzVkYWMwZTFkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:15:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImowRWt0T0pNZmdia2M0R0M0bTVkOWc9PSIsInZhbHVlIjoiUzc4OVJvTTVxTTdpeCtKRVpqNkROYlhjbXFSdENiSy9lT1RGcmZkMFZXcUhwVXlqdVljV2VIL1EvNTJ1Q0hjSit3b2J5YW1mSklwOFlPMzd0ODV4Z3NKVUZ2SUFlZExNTVRVMEFkRnVOWHJhdDlhaXZENTJLV2VsRDJ1eDR3RVhnZm5pNWkyclpDaGNKNXlLODJ5QU1aeWNHemRhSGc3N1ZMeDJQTGlHd3crTjh6ODVLb1p2anh5QThwZklTMXB0NHR5NW1icEEreHhrSW4ySitiSW9idTB5Y3dJSllRZlNrV0RSUDlZVm5uOEQ5dXpWS1hNOWdjWHlCbnJUMzMvdjByS3VMcHQ4YWt4Y1NtYlBtQWh3ODRUd05aSHpYMEluTXhqMWpxc1NkUmRRVWpaZjdwSDBNVnI0eVJkQXl2aTRROHc3RXkyVUJSalU1K2tuS1FuQTYvZTVMZE01VmdWbTZTM3NwUStqWVVUQWREVWRNR01MMnhtZU41dDNjL2s3UkNLSmp6dEI0aFF2NlpWZ3ZpcnBpU3RGNk1wZkovcnV0Zk14RldyTmRRaHFsT203cFhoRzJMVFQ0RzBSanYyTm5NaEVNVS9BYnJ5dUtZMWFjdk5IcjRZWFBRcCtDTlQ5bk1aV3pyQTJhc1NKVzdiQngxRE44Ry9qRUx4cHVwTm4iLCJtYWMiOiJmYjA3ZTkwODk4OWQ2MTZiMzBkNzczNzBjMzRkMDQ3ODZiODY1NzBhMmRkNzQxMmZlZjg0MDc0YWU4NTY5NTkwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:15:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklQNkpLdlFkRHBsWFhNQldtcWo3THc9PSIsInZhbHVlIjoiNGFveHFqamQ3SGdiZUV5ZWpQSU1SZExMNmFreWVPOXRhRUkwckVOQ29sMXplZHcwV1FiOTJDK3Q1M09WYjM3RFExSDhHbWh5SUtuNDV1VEthM0ZsN2Q3SUNXeng1eHVWd0VjNHVlcjdNUDlod25RYnZaOTVEVGFBaHFlRkNWbFo5U1BEd29wNStBcjJKN1pURVI2bnpJTXF0V0RTakNES2pKTTBmZjJ5V3drY3B3U3lFcHN0b01HUnNwY0E3ZUJBOU1LZWdhMVA2UTYrTEpYaXIzK0NwRzZYeTNuektXNklCQk95WHNkMlhHUGNCWVZHNTRSZVA2VjY0MktLMHRIZDBkallqb0JkME5oVTYzSVpnb0JpUWNpNSswZFMwT1JEWlFnT2ZNL2hJZU1xNXJDOGJjRTIreTZ3Zk54ZjZiRTYzcHBnRlNOZFJZMHVmRjU2bG1zMnJRSTBJUWpkMW1YVVNnOE13aUlKTTBWOGhjUjBiNEl6QjRYWXdrK3hnRUUwL2tuUnJiTEtzczEvbWJtMmFMcmtMOW9Vb2ZYOVpiN3Z0K000ZEd1MGQwZGdXa2FwRmZML2tUaXlIbnVrOTF5d09NYXpGd1owRG55V05qNkY4QWoxUlRxYnF0Y2hRUFdmbWd1SzNiL0M5SU5MMFYrcklXVHBtb0w1OEhLVVVkbGQiLCJtYWMiOiJmZjZiN2RjY2E0MmE2YzlhNTUwNzQzODgyZjFkNWE1MmJkNTBjNzIyY2E5MzhlYmI2YzBiMzEyMzVkYWMwZTFkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:15:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImowRWt0T0pNZmdia2M0R0M0bTVkOWc9PSIsInZhbHVlIjoiUzc4OVJvTTVxTTdpeCtKRVpqNkROYlhjbXFSdENiSy9lT1RGcmZkMFZXcUhwVXlqdVljV2VIL1EvNTJ1Q0hjSit3b2J5YW1mSklwOFlPMzd0ODV4Z3NKVUZ2SUFlZExNTVRVMEFkRnVOWHJhdDlhaXZENTJLV2VsRDJ1eDR3RVhnZm5pNWkyclpDaGNKNXlLODJ5QU1aeWNHemRhSGc3N1ZMeDJQTGlHd3crTjh6ODVLb1p2anh5QThwZklTMXB0NHR5NW1icEEreHhrSW4ySitiSW9idTB5Y3dJSllRZlNrV0RSUDlZVm5uOEQ5dXpWS1hNOWdjWHlCbnJUMzMvdjByS3VMcHQ4YWt4Y1NtYlBtQWh3ODRUd05aSHpYMEluTXhqMWpxc1NkUmRRVWpaZjdwSDBNVnI0eVJkQXl2aTRROHc3RXkyVUJSalU1K2tuS1FuQTYvZTVMZE01VmdWbTZTM3NwUStqWVVUQWREVWRNR01MMnhtZU41dDNjL2s3UkNLSmp6dEI0aFF2NlpWZ3ZpcnBpU3RGNk1wZkovcnV0Zk14RldyTmRRaHFsT203cFhoRzJMVFQ0RzBSanYyTm5NaEVNVS9BYnJ5dUtZMWFjdk5IcjRZWFBRcCtDTlQ5bk1aV3pyQTJhc1NKVzdiQngxRE44Ry9qRUx4cHVwTm4iLCJtYWMiOiJmYjA3ZTkwODk4OWQ2MTZiMzBkNzczNzBjMzRkMDQ3ODZiODY1NzBhMmRkNzQxMmZlZjg0MDc0YWU4NTY5NTkwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:15:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105034765\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-974767618 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-974767618\", {\"maxDepth\":0})</script>\n"}}