{"__meta": {"id": "X7268bc1a46a8310f432680a47b64b6cd", "datetime": "2025-06-27 02:27:38", "utime": **********.42105, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.015197, "end": **********.421064, "duration": 0.4058668613433838, "duration_str": "406ms", "measures": [{"label": "Booting", "start": **********.015197, "relative_start": 0, "end": **********.34807, "relative_end": **********.34807, "duration": 0.3328728675842285, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.34808, "relative_start": 0.3328828811645508, "end": **********.421066, "relative_end": 2.1457672119140625e-06, "duration": 0.07298612594604492, "duration_str": "72.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50171880, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.00824, "accumulated_duration_str": "8.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.379716, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 20.995}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.391607, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 20.995, "width_percent": 6.796}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-27 02:27:38', `closed_by` = 22, `shifts`.`updated_at` = '2025-06-27 02:27:38' where `id` = '47' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-27 02:27:38", "22", "2025-06-27 02:27:38", "47"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.404151, "duration": 0.00335, "duration_str": "3.35ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "kdmkjkqknb", "start_percent": 27.791, "width_percent": 40.655}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-27 02:27:38' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-27 02:27:38", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.409233, "duration": 0.0026, "duration_str": "2.6ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 68.447, "width_percent": 31.553}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "success": "تم إغلاق الشيفت بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-1889659062 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1889659062\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1391639746 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1391639746\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1016689436 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1016689436\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-208195861 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991245628%7C35%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhWbnc4R1ZqTXJoYzJxVjMrNzZ5TEE9PSIsInZhbHVlIjoiK092bkQ5ZTUyUitTVkxEcm1sM1ZCKzZJdkMrMGxpK0lDYjB2Q1hKK2FxL042VkdWWUJ6UGhZN04zU281eFZwbmVRYStsTGdRbHR5ZFNLQzRhaDZGdENyYm5vVys1K0ZlRFRGTEFqam5uR0RXejd0ZmlTYmtZRGZpL1BTMExYTkdyOHF1RWNuS21qK2lXQTN6Ulp3bnJuMHFoWXZhcGtHdnFEc2t4YTBWWnNpY3ZZRTFpRnN1MGsrdktvUTFDYlkwUEw0VUl5MmNKUDR4UmFKMzFjZEVodTQzVkZDV2VPNVJxOStrM3lnRVJsbFdTa3VVRDNrN3IzMWVqeUp4Z1N6S2NOS1BsY2NYZXNDd1pCR1VVSmtqYVZkSkpwK1g0NkZKUVJMWmpzRTR2NUlJdWVhczFjNW1IVnk1WGk2cjNTQ2xwZ1UwRldKeVF5R3U3RkNkK0k2djM4L0I4SWZ6YStWeG12bElhSmE1eWhzSkpyMnFhUTFWeEhEaThpdWNmRHNLV1ViU1htUkQ3ZWpDelY4YVVEQlBaTGowemxrdWw0dXpKVTdsZWRHMm9nOUZyL25BT2V2V1d6RjkvcWIxS0hoTHV2VFNFUFhWMlpkWHFTU2Nrc3N4TlcvK1duL21uMkFUdjY4SE1ma2Q1VnFyL0lsR1l0ZjdCdVkyN0pOaVJjWk4iLCJtYWMiOiJlZDFhMjRkZjA1YTE3N2RiY2FlMzk4MmNjYmE4YzgyZDhkYTNiN2FlN2IyMTNkYmE0MzUwYmQwM2ExODg1YzBlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im14SE5JS2xpRWZJNy9oYTAwRzFKYmc9PSIsInZhbHVlIjoiTmRyREt5K01aL0RlMGlDN0hPMVJXRnA2S0UvWWdwMEdmMjFUZWFlTEFEZmFJWlFPVy9ldlVOOXMyT2Rib0NHQjdXNVdWbkNSRng2UFNjMkl2alBmTEdOUnA5dU9MaURMeFNoOHp3c2dQK1I3dXN1Z2dSNklNdVhNNVhUZGZLL2dVLytiTDFqb2dONEN6MHl2MUE3U2daR0lMeDl2VHJ2MHFZZkRxLzJoQ3IyK0hRYVV4RFNBNitCUnM1TzBiVmZmbWNFaTlXUUNrNTRYTzd0cTliMGNqbkg2S0pKb1Yzc0hWUk9ZU1RrTUE5OUlvRktTc1Vua2VRTzRzcG1SWXZlQWVwbDg3czF2MTliVGFNTXlFQ1Z6MStPM2IxNWxJK2p5MmVlV0wyOGdIcG5ObVpBWGs3MHZRR2dZQTNSRjVOa1Z6VWlWS1ArQ2NaK3k0bXZCWWRaL3k4cEtXbXk1MHlYVFBiQ2J2ZTVvNWNQa1AvNEpaTm43ZEwyUTdIeUV0TnZ1VENaZDBYNTRqVmRRWlhIQUZKWjV4dWYyNTRndEZVczNJYmR6MFl0YmRteFJwcHBTUHNQT055UVY1RjRpL0RhZVJySzEwVWhpV0x0aXVGR2lhUFBtcFEwcjVvUXR4c1lmbzRPUmh3czVZUFFnc2NyQjJEZXczMVg5OUU1TjZ3TWwiLCJtYWMiOiJiMWI5YzI2ZmI0NDEwZDg1Nzc3YTEzZmU2Yzc0NWNlZjYwZTcwMTI3ZThiYzQwNjI5MWM3ZGNkMmU0YjNiZjBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-208195861\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1238192777 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238192777\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-281096040 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik9BSUtHTm5WbXF3MWIvU1B1WGpvNnc9PSIsInZhbHVlIjoiTXFPV1pvUWJyU210ZnBtSUVHM1dJaGJzNHJxeTBLY2xibTZocEVmc3NIWGFtTEQ1VGRPc0QraU9WZngvbnorQW5uSVFkK1cycXVkUTIycElLYUsxOGp3YWp3dnpJSXBuMTh1QzkyWEs2T0FCdVpOU28rWDJ2UExqMGQvZkI3Nkk3OVZENU9RQndjdHZ0Y2NZaHJSUFNhSGFMcmgzNGxmYktUWDlSWW93azNsbk1YRXFrV2xnTWxGMHZrRmZocm9EOWU1Y2k1Rmh0QmlYRXV0UFBRVy8zcUY4SFR5UEREV3RUOE05VzNDbzZNL0oyZHllb1JaK2QzVENSQ2oweUxkT3pHWTR3aVUrOHpFR1M0eUJNTkRTb0NvUmVJQ2FtOG9KWFA0ZzZqTzJVSXRnMnVPbndyOFFSLzVDUFNqQlAzTlIzb3p1ajh3QTc5Q1o3dWJPOFZraVEzY2dqT1pNRTRSUEF1Rjk5TjFUc1JqWVR2ZExBVlh0WnluQVV4NGo2aSs2V2FScTcxVk5GV3BCZmo3bDFuZ25FTUY4bSt0T1lRWnNhSjA0dC9UWGNqUWc1TGhSazhtSVhMTmFVZHloT3UvT3paRkVIVnExby9ELzZGdjBOTUNUNnRuVUZGbnlZK0dQdUttenN2K3hqT3pML016TVpKaHJuRXZPaFIvQytUNkIiLCJtYWMiOiI0MjNjMzMyYTNjOGVmZDhiNzVkY2U3M2QyMDVlMzAzZThiZjcxNDY1YTI4ZTQyOTM4NTQ3MDY2ZjU5NmFlODgyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVvb2x3bk81U2dCT0hUQXMyRlR5cWc9PSIsInZhbHVlIjoiQWllZFYybVllM25lTVNJV0QwOUZScXp0Q2w1OTNxL21kQnNWc013WGtaNWJBM2Qzc00wWnM2VzNGUzZRbWNHaHVkYWMyVTYzSjJYVitSUXU0d1E3NmdVQkRXN1V0SlhhZVZ6T2x3LzlaTEc3REFJdGIxaktFc0M4bUltVEdDelYwQlNzUXU1aisvMFpKZHJnblo4bVF6Wld1OERKd2Rzb1hob2MrRHZYd3FwczhXZ3IvRjFiWGI0TDM1T0ZkUTJQVThKTmlXQnFzbGk3Mmo0VnlvR3BYZHZZWW1qOHozQlRPODhEZHBTVUo2QkZHajBHWE16Q2ovUHFvZWJ4UmtRV1QycGROaUZXVURQd2d3K3pZWkc1RlZHS0Q0c2RnRC92NjhVV0ZmS2IyZUJ3cDJqbkVrTUtsdDVkM2M0RU8xNG9wcmZPblNkM2ZKNUptWmpTN3RhUnF5RXh6U3JxaGh6TDUvY3dpQnFkaWZGdHF1b2NwVlhDRUJUaktjbTRQNWIrYThGZnlLRnFIdDVENEN2dHVTU1dPdEkzM2pWZW1xR3VENU9EMFlpV2E2UzFmQktqVHMyRGVPRUs1Y2NFYjRJWlBFamJ4ZEhUK1VYY01xUmY2cDR4TDh3dzN3b21LWEFlSTFVSFE3b25oUXJEVjhpdTBVV1JHZ1ZLOHhRMlZGa1giLCJtYWMiOiI2MDdlYjk5OWRjNzdmYTQzNWU1MGQ2OTk3MjAxZjA2ZmI0ZGJjZDI3NDMwMGM2ZTYyMGE3OWM2MWM5MTgwNGFjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik9BSUtHTm5WbXF3MWIvU1B1WGpvNnc9PSIsInZhbHVlIjoiTXFPV1pvUWJyU210ZnBtSUVHM1dJaGJzNHJxeTBLY2xibTZocEVmc3NIWGFtTEQ1VGRPc0QraU9WZngvbnorQW5uSVFkK1cycXVkUTIycElLYUsxOGp3YWp3dnpJSXBuMTh1QzkyWEs2T0FCdVpOU28rWDJ2UExqMGQvZkI3Nkk3OVZENU9RQndjdHZ0Y2NZaHJSUFNhSGFMcmgzNGxmYktUWDlSWW93azNsbk1YRXFrV2xnTWxGMHZrRmZocm9EOWU1Y2k1Rmh0QmlYRXV0UFBRVy8zcUY4SFR5UEREV3RUOE05VzNDbzZNL0oyZHllb1JaK2QzVENSQ2oweUxkT3pHWTR3aVUrOHpFR1M0eUJNTkRTb0NvUmVJQ2FtOG9KWFA0ZzZqTzJVSXRnMnVPbndyOFFSLzVDUFNqQlAzTlIzb3p1ajh3QTc5Q1o3dWJPOFZraVEzY2dqT1pNRTRSUEF1Rjk5TjFUc1JqWVR2ZExBVlh0WnluQVV4NGo2aSs2V2FScTcxVk5GV3BCZmo3bDFuZ25FTUY4bSt0T1lRWnNhSjA0dC9UWGNqUWc1TGhSazhtSVhMTmFVZHloT3UvT3paRkVIVnExby9ELzZGdjBOTUNUNnRuVUZGbnlZK0dQdUttenN2K3hqT3pML016TVpKaHJuRXZPaFIvQytUNkIiLCJtYWMiOiI0MjNjMzMyYTNjOGVmZDhiNzVkY2U3M2QyMDVlMzAzZThiZjcxNDY1YTI4ZTQyOTM4NTQ3MDY2ZjU5NmFlODgyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVvb2x3bk81U2dCT0hUQXMyRlR5cWc9PSIsInZhbHVlIjoiQWllZFYybVllM25lTVNJV0QwOUZScXp0Q2w1OTNxL21kQnNWc013WGtaNWJBM2Qzc00wWnM2VzNGUzZRbWNHaHVkYWMyVTYzSjJYVitSUXU0d1E3NmdVQkRXN1V0SlhhZVZ6T2x3LzlaTEc3REFJdGIxaktFc0M4bUltVEdDelYwQlNzUXU1aisvMFpKZHJnblo4bVF6Wld1OERKd2Rzb1hob2MrRHZYd3FwczhXZ3IvRjFiWGI0TDM1T0ZkUTJQVThKTmlXQnFzbGk3Mmo0VnlvR3BYZHZZWW1qOHozQlRPODhEZHBTVUo2QkZHajBHWE16Q2ovUHFvZWJ4UmtRV1QycGROaUZXVURQd2d3K3pZWkc1RlZHS0Q0c2RnRC92NjhVV0ZmS2IyZUJ3cDJqbkVrTUtsdDVkM2M0RU8xNG9wcmZPblNkM2ZKNUptWmpTN3RhUnF5RXh6U3JxaGh6TDUvY3dpQnFkaWZGdHF1b2NwVlhDRUJUaktjbTRQNWIrYThGZnlLRnFIdDVENEN2dHVTU1dPdEkzM2pWZW1xR3VENU9EMFlpV2E2UzFmQktqVHMyRGVPRUs1Y2NFYjRJWlBFamJ4ZEhUK1VYY01xUmY2cDR4TDh3dzN3b21LWEFlSTFVSFE3b25oUXJEVjhpdTBVV1JHZ1ZLOHhRMlZGa1giLCJtYWMiOiI2MDdlYjk5OWRjNzdmYTQzNWU1MGQ2OTk3MjAxZjA2ZmI0ZGJjZDI3NDMwMGM2ZTYyMGE3OWM2MWM5MTgwNGFjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-281096040\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-892783721 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1578;&#1605; &#1573;&#1594;&#1604;&#1575;&#1602; &#1575;&#1604;&#1588;&#1610;&#1601;&#1578; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-892783721\", {\"maxDepth\":0})</script>\n"}}