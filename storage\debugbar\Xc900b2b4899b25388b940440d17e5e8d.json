{"__meta": {"id": "Xc900b2b4899b25388b940440d17e5e8d", "datetime": "2025-06-27 02:26:05", "utime": **********.574367, "method": "GET", "uri": "/payment-voucher/21/confirm", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.173325, "end": **********.57438, "duration": 0.40105485916137695, "duration_str": "401ms", "measures": [{"label": "Booting", "start": **********.173325, "relative_start": 0, "end": **********.509489, "relative_end": **********.509489, "duration": 0.3361639976501465, "duration_str": "336ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.509499, "relative_start": 0.33617401123046875, "end": **********.574382, "relative_end": 2.1457672119140625e-06, "duration": 0.06488299369812012, "duration_str": "64.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46710608, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x voucher.payment.popup", "param_count": null, "params": [], "start": **********.570001, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/voucher/payment/popup.blade.phpvoucher.payment.popup", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvoucher%2Fpayment%2Fpopup.blade.php&line=1", "ajax": false, "filename": "popup.blade.php", "line": "?"}, "render_count": 1, "name_original": "voucher.payment.popup"}]}, "route": {"uri": "GET payment-voucher/{id}/confirm", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@showConfirmVoucher", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.confirm.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=146\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:146-149</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01368, "accumulated_duration_str": "13.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.541099, "duration": 0.01329, "duration_str": "13.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.149}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5623128, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.149, "width_percent": 2.851}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/21\"\n]"}, "request": {"path_info": "/payment-voucher/21/confirm", "status_code": "<pre class=sf-dump id=sf-dump-146394319 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-146394319\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1557106785 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1557106785\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1696398141 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1696398141\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-587311649 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991161643%7C30%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVFZzVIUW91VnRFZWp1RzNMSHM1dVE9PSIsInZhbHVlIjoiOFRJdzZGQWFhTTdhaDMwaTQ3U1BCZlRuWnRtYjlpcG5aczhwMVFsbUJhV0lYMnpLZXNWV09hTldXRHFnMllpY2ptSXlWTlYrL2N4d2E2cW9VdE5JcE82elc0d2pDWTdOTngzRXN6VmF6YVU3b1FGbEhVSTlZWTl0b0RPZG5WVS9icDQ2RWxscSt5UkdYeG1kL1JQUmJBZDBDYjZQUno2bEwyVTNxSWlkVEQ1ZWRJdHBuVXZMZE9IWklra1Vhd3ZaaDNacWUyeEZ5YmwzeWd3Wi93dTZqdUxwZ3lLWDhtcWVIVng0MGdSMDgwWVV4ekt5d1c1SEcvaGVEcVVoY3NHMkpWZnlkWlFoUUI3T3FsUHc1d3FOR0pXR3BLREd2a3FjMklEcTJlRk1Qb3pMREg0WnVYekk4WlpRdW1wKzB4SzlCRDcrdVA0VFVERUNpTGZxa0FsZ1FNbUVYMTNISnlOY09yZzBmRTUzWE9iYnhHOHY5TFk4M3dubTkyYXNuL0JBVzM2anZXNHVmVExEOElzOXRxaVFrcStpaEluV2FmeEltWmhjY1BFQmJBbVYzMHZDck0vSWlNeXBoTG5MM2hhRnRLbitJaGdmTXpiQ1E1UGFlTmNFUERHWkJJa0ZZVU5MVmVkTnJ1ZTl6TU92M3JWcFBjZFgwY1ZuS3RRRGwvQVUiLCJtYWMiOiJiN2Y3NGM1NDI5YmM2NWMwNGQ2MzRjNWIyMjM1Y2NlN2ZmNDE3MDYxMmY1MzA2YWY2MzI5NjQ4OTRjMGJhYWIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkJlUkQ0YUc5a0duOFB6RkI0R04vMFE9PSIsInZhbHVlIjoiTiswSUhkRjlscnZBam8rS0RUQ3BFSHA3YU9pc2I1VHVva3pMelN3UFd4UTVXbWp2bzlyV0g0bndtTEJGL3E3c2twcXFrSS9hdW12VHZHS2tGUng4RVZqVUdreklaM1dtVzlvMlB1UHg2YlU2eDNrbEJZcitzSmQ3WmRrbkwzQTZQTWE3cTZQZ0VWbDNTYS9MWmNFNm8zTXJnb1R5eVVPUGNGWDBzbDNyU0E1NlNBeXVYQ3J5L0oyK1MvMlFjRjBkVVlzdVRoN0F2QUZZTnNZWlZvY3lud3dxZE1sUmhHeWJqUi9tbWZkK2tpMlg4Slo1Qk9MRk5ZSzEyNHYvSGtZV3RUd2RPandkT1YxTXpzaDJWVFAyeG1sM2NEcFdWR2c5UStrZExVNnUxR1FicVk1N1JvMHZNeFlKUW05T25La2FxTHQ1b0tJQ1pXREdadnE0ai9EQWw1UkZYdE9MR2pMdHFNYnBXQ3l4bTVnVi8ydEtma1NjNmMwRkpETk1TeCtjZDFLSytDc29GOFNLWTFULzJibFRualpXM3d6by93ZU5VV1RzS1RPaWpRL2VFNTN3U1I1dzJZTVB3R3JLbkJUMWd4d3U0bmtSSHhjdXF4TUh4N0dJaUlIbFNOOFZYRUJBTXJJWCt2WGgvU29LZmpjUFJreloxOG45U0Ztb2ZXL3UiLCJtYWMiOiIwZjk3OWM3ZTQ2NDEwNGQ4MjEwZTU1MWU3YTJhNDJlNzE2NGI1YjFmMzljODFjMGRiYTc4YTY3MDQzNjlhN2U4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-587311649\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1746473419 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1746473419\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1891034395 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:26:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImkxZ2RPZXk2V3pZcmFVZHdraWhOWFE9PSIsInZhbHVlIjoiWHJMbVRUMU9KaFZYMkhESWo4S1NQZjdRQUFPN0hhVFNYOFA3cEsxMDBpS0pJMGtGRi9HZ01sRzkwdHNIT1BNODlibXZ5UG9qK1FNT1VReEtUb3BwMWtJUVRKU3orTVYvSkpPYXJjckkweDRCazVzSDkvUVM2QXVtTDFxem1QZUF2TU5jS1J6UXh2dklHT1U4ZmVZZ3c0bFBBMWpmeXkzZ1AwQjlLL2FMVU8wMmxGNHNHdk91Rm45QVpud3haV3U1WjJydjVNNEMxaGdNci92WDRONEljQkxzcEFEekxYakNkMGdVc2Y1RmUxdTNHVmlYSGhFZ3pyOWR6ZE14VkpjM09walBJMVpUQ1FPcVdZZTZtQ2N5cERVU3FRT29RLzBuWDFQbExYejBkZGV6ZGg1NjNib3VSc0NBQ0owZ2hUWDVVN1JqSVo4MWM1alo2aG5pdjRncjk1ZkdIUlVpc0dIcUxleWNuU1RuUEp3RWlCV1gyT0xsdHVaei95SC96OHovYU54N20yV0llbVRGanNrYTFkT29aYlNWMWhSbFlHWGtQOFZvNHNhMEVwMU15UFJvMGdQODlzdHVoV0hyWDNZbVhleGFIRTU4Q1Fxcmg1cU84NzNPbzJiMWtLVExyNFA2TUVPV2lOVUcya1BWQ3kybzgxTU51dWdwSU1lTmUvK0QiLCJtYWMiOiIyNTdjMzZkZTg1YzRmMzUzZjc0MDAxM2I3NjhiYTExN2NhYWFhMmQ3ODk0YzA5N2ZhZWViZDA5NDgxNjdjYjI2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9Sc0hMMlcyMVV6a0dwWlo4RVRUSFE9PSIsInZhbHVlIjoiQkh1MS9YaDhUc3EweG9FSmhKRkN0K09Nb1R6SXZGeHc0VFgxd0tTa0wwMEJuZTNQMVpDOGVFeXd3cEFlUXZ0WGRldFFVUk9tK2x0ZVVMUnY1UWVXS0p0cEdTQXNMejFRWnVZV1FheFlnVGgvSXpSOWF2VU1yM2FoLzNIV3p1KzJmdk0wbVhOWWk1bDlnOHZhMkFVVDVReVV0N1FOck5BWm9tbFNFNUlSY2R0MGhDL2FuUFRicXpocFFUTWFORnFWWVJqNytSeUhDVHNkcC96K0tEbjcxYmdwR0haRnZpOGNKeWJHbWNLSjBVSGczcE5WYXBNYVJ3cjRmS0RGWUhxeUZOUXUxTWVzbGY3RWltTmMyMFF5bGJDVElMUnhQcEI4eS9JUDFFd2NoYnY0cm1hUXR6TWgySDRQRTJqSmFobUExcEV1YktnSWlIaEUzanJ0NE1KeW5mNi9ycEx6dGdNbkJhblVqcUE0QmRDZnF2dVVmM0xod1QrYnE4ZTZvQlBMcEhrdnpzeFdBZGxHNHBZNlFybHZ1bVBqQlkrNGx0c0xsUTBwUisxL09EYzl1SWFsK1VWdXBHN3lVb29BcVlmNTRRSDFlM1BaRmYxNENZUmdNVXB4aHVYNm1UeG1GYkxNN2RkTlBWTEFpeEFZN3lzSTVUbm4zcEx4QXgvV0kvMG0iLCJtYWMiOiIzYjE0NGE0NDIwZDBmNmU4YTMyNDc1ZWY2MmRhOWFkODRjOWM2MTIyYjYyZjYxNDUyN2NmZjFjNThlZmEyY2FhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImkxZ2RPZXk2V3pZcmFVZHdraWhOWFE9PSIsInZhbHVlIjoiWHJMbVRUMU9KaFZYMkhESWo4S1NQZjdRQUFPN0hhVFNYOFA3cEsxMDBpS0pJMGtGRi9HZ01sRzkwdHNIT1BNODlibXZ5UG9qK1FNT1VReEtUb3BwMWtJUVRKU3orTVYvSkpPYXJjckkweDRCazVzSDkvUVM2QXVtTDFxem1QZUF2TU5jS1J6UXh2dklHT1U4ZmVZZ3c0bFBBMWpmeXkzZ1AwQjlLL2FMVU8wMmxGNHNHdk91Rm45QVpud3haV3U1WjJydjVNNEMxaGdNci92WDRONEljQkxzcEFEekxYakNkMGdVc2Y1RmUxdTNHVmlYSGhFZ3pyOWR6ZE14VkpjM09walBJMVpUQ1FPcVdZZTZtQ2N5cERVU3FRT29RLzBuWDFQbExYejBkZGV6ZGg1NjNib3VSc0NBQ0owZ2hUWDVVN1JqSVo4MWM1alo2aG5pdjRncjk1ZkdIUlVpc0dIcUxleWNuU1RuUEp3RWlCV1gyT0xsdHVaei95SC96OHovYU54N20yV0llbVRGanNrYTFkT29aYlNWMWhSbFlHWGtQOFZvNHNhMEVwMU15UFJvMGdQODlzdHVoV0hyWDNZbVhleGFIRTU4Q1Fxcmg1cU84NzNPbzJiMWtLVExyNFA2TUVPV2lOVUcya1BWQ3kybzgxTU51dWdwSU1lTmUvK0QiLCJtYWMiOiIyNTdjMzZkZTg1YzRmMzUzZjc0MDAxM2I3NjhiYTExN2NhYWFhMmQ3ODk0YzA5N2ZhZWViZDA5NDgxNjdjYjI2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9Sc0hMMlcyMVV6a0dwWlo4RVRUSFE9PSIsInZhbHVlIjoiQkh1MS9YaDhUc3EweG9FSmhKRkN0K09Nb1R6SXZGeHc0VFgxd0tTa0wwMEJuZTNQMVpDOGVFeXd3cEFlUXZ0WGRldFFVUk9tK2x0ZVVMUnY1UWVXS0p0cEdTQXNMejFRWnVZV1FheFlnVGgvSXpSOWF2VU1yM2FoLzNIV3p1KzJmdk0wbVhOWWk1bDlnOHZhMkFVVDVReVV0N1FOck5BWm9tbFNFNUlSY2R0MGhDL2FuUFRicXpocFFUTWFORnFWWVJqNytSeUhDVHNkcC96K0tEbjcxYmdwR0haRnZpOGNKeWJHbWNLSjBVSGczcE5WYXBNYVJ3cjRmS0RGWUhxeUZOUXUxTWVzbGY3RWltTmMyMFF5bGJDVElMUnhQcEI4eS9JUDFFd2NoYnY0cm1hUXR6TWgySDRQRTJqSmFobUExcEV1YktnSWlIaEUzanJ0NE1KeW5mNi9ycEx6dGdNbkJhblVqcUE0QmRDZnF2dVVmM0xod1QrYnE4ZTZvQlBMcEhrdnpzeFdBZGxHNHBZNlFybHZ1bVBqQlkrNGx0c0xsUTBwUisxL09EYzl1SWFsK1VWdXBHN3lVb29BcVlmNTRRSDFlM1BaRmYxNENZUmdNVXB4aHVYNm1UeG1GYkxNN2RkTlBWTEFpeEFZN3lzSTVUbm4zcEx4QXgvV0kvMG0iLCJtYWMiOiIzYjE0NGE0NDIwZDBmNmU4YTMyNDc1ZWY2MmRhOWFkODRjOWM2MTIyYjYyZjYxNDUyN2NmZjFjNThlZmEyY2FhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891034395\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-747053789 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747053789\", {\"maxDepth\":0})</script>\n"}}