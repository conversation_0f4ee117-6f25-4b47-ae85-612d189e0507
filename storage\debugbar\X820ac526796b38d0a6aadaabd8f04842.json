{"__meta": {"id": "X820ac526796b38d0a6aadaabd8f04842", "datetime": "2025-06-27 02:13:28", "utime": **********.912592, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.477877, "end": **********.912605, "duration": 0.43472814559936523, "duration_str": "435ms", "measures": [{"label": "Booting", "start": **********.477877, "relative_start": 0, "end": **********.855711, "relative_end": **********.855711, "duration": 0.3778340816497803, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.85572, "relative_start": 0.37784314155578613, "end": **********.912606, "relative_end": 9.5367431640625e-07, "duration": 0.05688595771789551, "duration_str": "56.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45274504, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00213, "accumulated_duration_str": "2.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8904471, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.812}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9043128, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.812, "width_percent": 20.188}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1600885546 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1600885546\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-335509893 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-335509893\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-730384385 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-730384385\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2106126414 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990356916%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlIxTTVDQ25BY3BLRGsrZmtiVDBUOUE9PSIsInZhbHVlIjoiSVpldzd2TWhyL0k2dmtjSXVuQ0pBaHh3OUc5UVp6UUJtRzNaczZCOCtSSWxEL0lrQWNhSFJIUzNmQk1iTDdkZ1dYd0pYU2xNZmgxYlhSa0gxcFJxSEtRSU9yUEV1MkxIS2lqYk9DVktKT0JVcVh2Sllzcjhlc3ZRbXlMc2FrZ2swMXJJdTVZNG1hRVNJZUJwNXp1RyttY3pJSnVxV0JEcU83dUJtYTJ0OHRjdDBpU2dDaDFieSsrd3dpSGRKQUVGR0x0cTZUbzZSNUM3S1BrQjZMcGdBU2pzbkI0V3B4TENjbEFpNi8wS0lkT2I0aHYxN3JSUWxWM1MzR0tSSGtmME9wVnVFY1BlbTREOHZxTUpJcDZtOFE4T010bVhjbmdVY0RZWjVPaFZBeW41YTNlT25INXFqTlV6elkvTWd5UElldmsrLy9Dbm4vSEFqVjRmc3c3M1UxY2xEUFkyNlRIQ1JqWkRWZDdONzVnMFVnRnNIUlRSUUdXWmFpSXZvY1ZFaTYyZHQ0aDY2bGZKOGlxWEl4bzUrRCt0TndxUzhobXNJZmc4TEMyTGliTVNyb3lpNDA0eTdxZkJjWVRKc28rZExYYnRrcmxJLzg3bzJ3QkZlY09OVHArU3FLeDNPRWQ3Mktzb3IydGU1TzRQRldYckRwK0ozU0hpa1F5TWExRXIiLCJtYWMiOiI1YTNhZjRkYmU0OWM4ZWE4YzdiYWMwNzhkZmJkNzZmYjBlZmVkNGM0OTlmODgwZTg0OGQwMjdiMDFkODgzYzhjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFMQ29YU0pocEQ2LzI1TzJOUFUxeWc9PSIsInZhbHVlIjoidDNCYU8xMFlGaEtJdmRUK3VqSmE5RXkyN3UyVk1kWE5KQXhMUEUyZWh0TTFPQ3ZpaXd4TlBkVkFpZ1NhdFQ5dWowUExEd1hYckNFK3NXbXZBZUwrNlJxa2RlSGFiT0hWZTV5UnRlVWFXZ3ArRHlEeklabkJhbnVjSUhWbkVEaGRJRytWNGNZQmRsVUgzVzk4ZGczM0QwTm1yQnpVWjlzRHl2Vm9hM1pRYlNQOStrYlM2VzFXbWNEWm03a25hbmhXVzZjQWsxY2FENkFEa0Y3ZUpmVmxPUkp1S3RmMXduK0NHVHRuQkUxYS9uK2FPUnd3UGV0Q3hteFNNVmZFSFdlR08rK3NVd2F0MXg2U1NxUFNZY0ppMGs3WGNrNG5MMmg1dkh6MytVWmxlMi9RVUZNMXdIVHcvaDB1RUJtcTN2MTd0eHYvbTlBZzFZYzcxVlppeWhGdXk5dEpOTWNrMzlLZUNhTW02WG5LV1poL3pwTkJrejJ1NGVuTC80alQ3WDJvdVlOZnZtSWlnaW5aV08rc0x6ck1GbzF6S1pzeSs1NzV5U2VvSkpMOTlGOCt2bjZodWFpOUtuNnJBbEx3TDdtSnd5eGR4dEwyLzZDVW9tQlVDNEZSbmV0OXEzanlHSDdRbkR3bFBZek8wQ0hUYk1iU0lvRlFEM3h6bXNmQ1JIUVQiLCJtYWMiOiJkNzA0OTE2ZGNhNmZkZmIzMmNlODBhZDIyOWNiNjViMzJlNDQ3N2FjZjE5NjVhODlmM2U3YzY1YjIwMWJkMzliIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2106126414\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1277613241 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1277613241\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-330221258 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:13:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1XZE1ZdkREYU5TOTVJOHZBWnJmbEE9PSIsInZhbHVlIjoiQUs5VHBKSStSL1d1SzBmMC9sN0VYYkQ1dW5QYXlEdzhZeG5TTThPMlpITTV0Y1NTd1BISHZtL3AzRjhBdUU0M2pMbERYVENNcGovTzRYWGpYL1R3aWxzbHNWQ1BhZVhLd0JPRnhJK2toR2xwN202YTZvc2k1allIRnJNd1FHaG9MU3pUVld0RVMrb05rQ2daRXZvSmxXdXBLRVFLV0JJdTgza3F2ckFTa0RVMldkeXBleC9jdWJvTTVWK3JpbWZORU1TT1g1L3k1NEx5WHBYbjdsdktDR21QRVlKZHNzRGxmb2lOdWpETWR4cFA5WWo4N2xyVWxDbm5oV0t3RlUrTzdMc1BiT1F1MC83TG5BL1R3SFVpYzhabmFzSUhCUU1ROGZNeGMyeElPZ0Y5MWNjYkFhN09sWkM0QWVHY1dkSnRTaHM5T2NQbUo0QWVaK09SMGttT05kNWRHWFVvM0ZuNENZTFozNDIrZEtLL0crOTNLb0taSXJnWE1GWWw5MnVyaFFYVmMzdkZLdWNUSyt2UW5KT0Z4ckVJdEJMcGJudDFjNzlFeFVxUXBOMVB4bFJwQlQ0eTNDamIxTmN0S0t2Q3J0aWgyZ3J2V05PWlo4ZFpyTmNUVlBuNlUwcDVqRjFqT2xVSEdaY0JMeW10Y0tEeVJQN04vbTgrOVQ4S3ZPRGQiLCJtYWMiOiJjMjZkOWQzZjczMGFjYWZkNDJmZTI4ODlhY2FkYTc5Y2MyN2MyYjFmM2Y5MGJhNjBmMmI2YmE1NWYyYWM1YzA1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:13:28 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdQMldkVnNkMnF3Q1lJVW5vcjlleUE9PSIsInZhbHVlIjoiOW1YbStSdUREYXdPY3pBaGlSS05nLyswT2ZlaWU3WFlQNWdFY0xKMEJYdUxwRTR5aDVoVUg0MEVVTU5kY1BIREthM0RmOFdsaFBMT0oycXArYnFnUGNCcjBxd2FLMnE0RUJKWllXS3VNN3V6OW9VRitYbGtvVFdiR1psSW1oMTVMMThmUUZVODZrL2s0NFJod0JXckJ1MHZjZ1J4WnVnODFCNDBBeUZNMTFzdTZhUUhxb2FSczAvd2RVSzJZZFpRT1dxS3VCZmMveE1lbGV3ejlyNjY3bTdkbEtPMGRVeWhmZTV4VG9UQTVPeU1Cc3lpN3QyZGp5T25UVkFQUnlicURNY2d3dkxjN1paejR4UmhjU21tNFk3bU91TXoyb2JNQStHTXd6cGJLU1E3czY4QmR1WEJtZDE5QUNTV2NtVElnc0V0R3VLRXpodmFFZFp6SFFNem1hQTY5U3dzSXQ1aUIyNUFkenUzS1FwTTRhUlNFa0ZmOEtpcDFBcXp3dGxySW5jS09WdXp3OGJsUHNlcmxYSUpYc1dSdlFMUjliRFhUNElBZTBhY0RCSXhueHdxbzJ1WU16Z25BamR5SWNNempabDBWdFQ0V3hGc3hXeUxpOXNGbEVmQUlJUVBkblExRzB0U1Y2WXRiamw2Y3d0ZE5pYUlGM0E1TnI3TUJVakgiLCJtYWMiOiJkODYwYmEyYWNjYWU1Yjg0NjBjM2JkYTg2NWYzOTc1ZmQyMGI1ZTg4MTJmOTY1NzJiZTVjOTE4NzE3OWE5Nzg3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:13:28 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1XZE1ZdkREYU5TOTVJOHZBWnJmbEE9PSIsInZhbHVlIjoiQUs5VHBKSStSL1d1SzBmMC9sN0VYYkQ1dW5QYXlEdzhZeG5TTThPMlpITTV0Y1NTd1BISHZtL3AzRjhBdUU0M2pMbERYVENNcGovTzRYWGpYL1R3aWxzbHNWQ1BhZVhLd0JPRnhJK2toR2xwN202YTZvc2k1allIRnJNd1FHaG9MU3pUVld0RVMrb05rQ2daRXZvSmxXdXBLRVFLV0JJdTgza3F2ckFTa0RVMldkeXBleC9jdWJvTTVWK3JpbWZORU1TT1g1L3k1NEx5WHBYbjdsdktDR21QRVlKZHNzRGxmb2lOdWpETWR4cFA5WWo4N2xyVWxDbm5oV0t3RlUrTzdMc1BiT1F1MC83TG5BL1R3SFVpYzhabmFzSUhCUU1ROGZNeGMyeElPZ0Y5MWNjYkFhN09sWkM0QWVHY1dkSnRTaHM5T2NQbUo0QWVaK09SMGttT05kNWRHWFVvM0ZuNENZTFozNDIrZEtLL0crOTNLb0taSXJnWE1GWWw5MnVyaFFYVmMzdkZLdWNUSyt2UW5KT0Z4ckVJdEJMcGJudDFjNzlFeFVxUXBOMVB4bFJwQlQ0eTNDamIxTmN0S0t2Q3J0aWgyZ3J2V05PWlo4ZFpyTmNUVlBuNlUwcDVqRjFqT2xVSEdaY0JMeW10Y0tEeVJQN04vbTgrOVQ4S3ZPRGQiLCJtYWMiOiJjMjZkOWQzZjczMGFjYWZkNDJmZTI4ODlhY2FkYTc5Y2MyN2MyYjFmM2Y5MGJhNjBmMmI2YmE1NWYyYWM1YzA1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:13:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdQMldkVnNkMnF3Q1lJVW5vcjlleUE9PSIsInZhbHVlIjoiOW1YbStSdUREYXdPY3pBaGlSS05nLyswT2ZlaWU3WFlQNWdFY0xKMEJYdUxwRTR5aDVoVUg0MEVVTU5kY1BIREthM0RmOFdsaFBMT0oycXArYnFnUGNCcjBxd2FLMnE0RUJKWllXS3VNN3V6OW9VRitYbGtvVFdiR1psSW1oMTVMMThmUUZVODZrL2s0NFJod0JXckJ1MHZjZ1J4WnVnODFCNDBBeUZNMTFzdTZhUUhxb2FSczAvd2RVSzJZZFpRT1dxS3VCZmMveE1lbGV3ejlyNjY3bTdkbEtPMGRVeWhmZTV4VG9UQTVPeU1Cc3lpN3QyZGp5T25UVkFQUnlicURNY2d3dkxjN1paejR4UmhjU21tNFk3bU91TXoyb2JNQStHTXd6cGJLU1E3czY4QmR1WEJtZDE5QUNTV2NtVElnc0V0R3VLRXpodmFFZFp6SFFNem1hQTY5U3dzSXQ1aUIyNUFkenUzS1FwTTRhUlNFa0ZmOEtpcDFBcXp3dGxySW5jS09WdXp3OGJsUHNlcmxYSUpYc1dSdlFMUjliRFhUNElBZTBhY0RCSXhueHdxbzJ1WU16Z25BamR5SWNNempabDBWdFQ0V3hGc3hXeUxpOXNGbEVmQUlJUVBkblExRzB0U1Y2WXRiamw2Y3d0ZE5pYUlGM0E1TnI3TUJVakgiLCJtYWMiOiJkODYwYmEyYWNjYWU1Yjg0NjBjM2JkYTg2NWYzOTc1ZmQyMGI1ZTg4MTJmOTY1NzJiZTVjOTE4NzE3OWE5Nzg3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:13:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-330221258\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-286639523 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-286639523\", {\"maxDepth\":0})</script>\n"}}