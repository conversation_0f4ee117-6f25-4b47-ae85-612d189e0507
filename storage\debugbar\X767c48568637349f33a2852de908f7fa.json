{"__meta": {"id": "X767c48568637349f33a2852de908f7fa", "datetime": "2025-06-27 01:05:17", "utime": **********.515818, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.051988, "end": **********.515834, "duration": 0.46384620666503906, "duration_str": "464ms", "measures": [{"label": "Booting", "start": **********.051988, "relative_start": 0, "end": **********.449859, "relative_end": **********.449859, "duration": 0.3978710174560547, "duration_str": "398ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.449868, "relative_start": 0.39788007736206055, "end": **********.515836, "relative_end": 1.9073486328125e-06, "duration": 0.06596803665161133, "duration_str": "65.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041816, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015090000000000001, "accumulated_duration_str": "15.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.479505, "duration": 0.01417, "duration_str": "14.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.903}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.5022051, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.903, "width_percent": 3.115}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5078292, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.018, "width_percent": 2.982}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2012415738 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986307573%7C80%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IllidFEyVGVQNitCNURiRUtnZGd6ZVE9PSIsInZhbHVlIjoiUmExSjI1eENBdHYwRzcvZnBUR0FZblpqMGRoNnBSTytBK3ZpemczOEFKWmk2VCtFanRPWW1PNHduRnoyK2JkckVVTE4zTDhiSjR2aWx5aTljZ3BVUHY5SFBEMVpsL2ZRV2I4dmVYK3Z0NUczTUpadkdqeGEyc2luak9jT3BaV0g4U0g2OTVIK041OGlqdDBKVE1KTkpIZ0tjUENndjdqdnZIRTBtYXMwSmVCR3d0aUxNcTBGdXYzdlkvT2JoODFXZVVLZktQZFVBRjRkRUNPdjRMaVltZU8zdTlSVERIQTRSYXVxdE5YL0h4VkhKbG9icHpZclZzQk1WVnFBclNIcnRUS0dmT1EwUGF6VFY4Y0U2VW1PWTY0MzhYYU5VYStFNjBkemdIVEg5RUtXMENnYUY4cnlCMTFkQXZESk8wZFhuc3hIallRL3NoWVVJMGV3ZmNBbUwyaHBlYlpoMk5ka1N4K29mTXRIOXRzNGZLV0t1a0FiYW4wVFNORDc0VWhMMXN1UWlzVDUwUGFLRTdsbmZQMmQwQUFnTTZqa05oWTZqMGVzQVdhbGFsTXVMQXEvcmczTk0rN2g3endrNlp1eUVjZFhubjZxeXI3RXlJM3VkOThOZTFxRE9SWjdCblRWTnRqZjdqc3NENHVJUkJmZU8vYTMwZWxmejVZdWgvNDMiLCJtYWMiOiI0MGI0MGJhY2U5YmU4NTM0NTUxNDc5ZGVjOTFiMWExYTkxOTdjMzQ1MDY1ODcyY2RmOTJlN2M4ZGNmZmYyODRmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InM3ZE9jSW9tRkYxNkhEeWVKM280T2c9PSIsInZhbHVlIjoibUt0eEw5SlRlNllWbzVsd0hhNWVhbGE4OXhiNkZXSlBzN01rV0gybzR1V1EzdnF1MzhnZXRKTGtUTFBUZmZETEgvMXFIMnBUeVdrREVTcEpzMHphVzVPcGJtN0gxTDJBa09YekVPZy9mQUxZSStCck9VWk1NZkZ6b0pBR2xWQ2U5SHFURlBkQWc0Ymt1eWg1bXhsc28wVktMWnJrOWh0TTMvWHBUUWgzS0tJZFRtbmxUY2Z5dVNHclRDQXQzRTF1ajBlL1I4ZnBrQkZNZHM4WFZhSU9TNkdEdkFDRyswbnV1cGFBcU5tb0tRSDdSQmlPaDA2UW5mU0w2cmVhT2JwMmpETktBa0NaTFMyMm83bzNVbk1ZYjF0TkgrV0lkUDJWWjRTUXVXOG42bHNweFVYQWExNXlsUE13VXQxOUo2TUtZZ2JuSmZ1MkhHVEZoRGlDSTJtVGZHb0NIWFRUeGo4NW9rcHNJV1hkdWF0alF2YUZpU1lOcFdJdGZFRGhRNEpEcWNGcVZwK291YlVaV0JSZzFESlpuMFRMdlBhK1AzMHlPeDFLdDFZMzNEcUhsT281a0VCaEF4blhicmZxOGQzbnFNUVVianJGL0dCbkgvRXk0ZnVYaE5hbnFQSTl3eGZ4SkluUTVyQzlyQ0Z6RFhRNWdzN3Riak5wb3V6MzhzTXEiLCJtYWMiOiJhOGIyODYxNTY1ZTk1OTlhYWM1MDQ3ZGNmOGFjMmUwYzI5YzBlMDBlZWM5ZGVkOGYyM2FkODlmZmU1MjQ5M2E2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2012415738\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2099334757 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2099334757\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1143263969 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:05:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkRrKzRqaStQZ1N5azFFdk0wRFM0Umc9PSIsInZhbHVlIjoidGZ3TlJhalpkUzBjbzBxdncrdWRMY2phYUpkN0RhUVFQRFpObWRDWGJYbituYklzZklxSXFKMFJwMnYvNDAzQnplb1FZV0MzMVRiV1JSaUNPeTA0MUkyZTV0alB0SXZpQ3ZxMmtSU2ZQRW83SEUzaTB5NHY3eXY3VElRUVVzeDY0ZUQveUJkWElOaFRpemRnVWkzTm9KUVVTeEJpd2hORWZKU3hRY3kzbXZIMGx0TzVNdHF1NDdSVjUxMmdyNEFlTWRUU3hWL1F4QmVka0RGbmwxdlNjaCs3RWY0ZEJqeUtOVktDRDdQRkhoMFB3b1VSeWdreWUyUzhwQis1NzZFNzRaMFMwUTlyblhvRGh2NDVVUDcvcVNRRGNhL1FxdVFoaGZ5amJwTVZEcFBwcmdLMFdyK2prQVFhNVdNbG1WSXFxbHg4eVpHUTczRUhBRVdzcjJleDJzd09TTnZBU1hhY1Jlc0ZQT09nbi9HVEFqaHpMSmt3TlQwbmhPWnNoNXBrdkQ4Tlh3Uytwdk9TQlhTbkJWUnprNkVLMEJPR3lMclluMU4ydHpJK1Nhdkc3Z3QrVlUrSlhaZTd0YTlzWkdDN1N5dFd1V0VFU1JXUUhxeTNOaitOKzhMVWhjUHhzUzk5d055eWhWVWxJY28vY05hN2d4cStTNWZOSmgwQnhIZmEiLCJtYWMiOiJkY2U1NzVkNjFjOWFmZDRkZDY5OTQyZTRiNTBmNDg4MTA1ZWFkMzE5YjcyNmFkNzU4ZGVlYThhNjFmNTQ2OGM0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:05:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBLSEw2aVdnOERubWFNZU0wM3FlaVE9PSIsInZhbHVlIjoiOHM1S2N3a1B1UG5PZUl3bUoyL1R0VzJFYXRQVklsNUxGVXRxUzlqd2tQcnNwT3VkV2tQME9hUFVLNm1ZUjBTbWw0Z3JNSitSdU5yWkxlTjF3cVpkeHd2bk5GRzJBVEppMDZaUkhYQ0tHNTZJc1MvZkxsR2JhWVI0aWU3OU9DajdkVkN0UmNPZGdWdllLUEJseERSNXFxZXFHckR5QnVzV2ZYUGNGSll2UGpRRWFaSWlsa1hSZVY3cHMrRGlSbHE0aldTWmNPWWphZ0UvM1BBL1FES1ZBRFQ5MG02RmI5M0w2RTZURElHUjkvbUtnUk8xYWxNWHIxUFpwY2hqcll1bnd2RjVhaG41eW1tVmhkSUF4ZXNoUXdVcHF3M2JzUkxieHJtT1JaSXJLRzE4ZDNyRWo1ZXU1TWxPLzNKeWRGb2pIeUJ1RG5CL2o5RDU0Q3VlU2xCZFlKZVY5b3ZCNkZTdVBCTmx3ZVZyL2VBWUZuWjN3dFZ6b2tDdHROeW4vbGxPVUdidUkweHVZaUJOeFp4NnVrYW5ES0hsYkRrV3Q5djNDUnAwbklRZEJUSWJwMGhjcloxN21Yc1ZPZlRYeEUxUzJLbHl0OVpPZE1ZeGNTeG95Z2lmWkZpUFg5UGtnQXlDbEFyV3JpUkJwV3lJTDNMeFArbllTMlV3K1E5Nzc2L3giLCJtYWMiOiJhNTE2ZmM0NTY2ODUzMzc2Njc1YTQxYmNmNGQ0MWU2MmQ3OTA0YzgwOGEzMmJiNDBiNWYyOGRiNDFkZjFmOGRmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:05:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkRrKzRqaStQZ1N5azFFdk0wRFM0Umc9PSIsInZhbHVlIjoidGZ3TlJhalpkUzBjbzBxdncrdWRMY2phYUpkN0RhUVFQRFpObWRDWGJYbituYklzZklxSXFKMFJwMnYvNDAzQnplb1FZV0MzMVRiV1JSaUNPeTA0MUkyZTV0alB0SXZpQ3ZxMmtSU2ZQRW83SEUzaTB5NHY3eXY3VElRUVVzeDY0ZUQveUJkWElOaFRpemRnVWkzTm9KUVVTeEJpd2hORWZKU3hRY3kzbXZIMGx0TzVNdHF1NDdSVjUxMmdyNEFlTWRUU3hWL1F4QmVka0RGbmwxdlNjaCs3RWY0ZEJqeUtOVktDRDdQRkhoMFB3b1VSeWdreWUyUzhwQis1NzZFNzRaMFMwUTlyblhvRGh2NDVVUDcvcVNRRGNhL1FxdVFoaGZ5amJwTVZEcFBwcmdLMFdyK2prQVFhNVdNbG1WSXFxbHg4eVpHUTczRUhBRVdzcjJleDJzd09TTnZBU1hhY1Jlc0ZQT09nbi9HVEFqaHpMSmt3TlQwbmhPWnNoNXBrdkQ4Tlh3Uytwdk9TQlhTbkJWUnprNkVLMEJPR3lMclluMU4ydHpJK1Nhdkc3Z3QrVlUrSlhaZTd0YTlzWkdDN1N5dFd1V0VFU1JXUUhxeTNOaitOKzhMVWhjUHhzUzk5d055eWhWVWxJY28vY05hN2d4cStTNWZOSmgwQnhIZmEiLCJtYWMiOiJkY2U1NzVkNjFjOWFmZDRkZDY5OTQyZTRiNTBmNDg4MTA1ZWFkMzE5YjcyNmFkNzU4ZGVlYThhNjFmNTQ2OGM0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:05:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBLSEw2aVdnOERubWFNZU0wM3FlaVE9PSIsInZhbHVlIjoiOHM1S2N3a1B1UG5PZUl3bUoyL1R0VzJFYXRQVklsNUxGVXRxUzlqd2tQcnNwT3VkV2tQME9hUFVLNm1ZUjBTbWw0Z3JNSitSdU5yWkxlTjF3cVpkeHd2bk5GRzJBVEppMDZaUkhYQ0tHNTZJc1MvZkxsR2JhWVI0aWU3OU9DajdkVkN0UmNPZGdWdllLUEJseERSNXFxZXFHckR5QnVzV2ZYUGNGSll2UGpRRWFaSWlsa1hSZVY3cHMrRGlSbHE0aldTWmNPWWphZ0UvM1BBL1FES1ZBRFQ5MG02RmI5M0w2RTZURElHUjkvbUtnUk8xYWxNWHIxUFpwY2hqcll1bnd2RjVhaG41eW1tVmhkSUF4ZXNoUXdVcHF3M2JzUkxieHJtT1JaSXJLRzE4ZDNyRWo1ZXU1TWxPLzNKeWRGb2pIeUJ1RG5CL2o5RDU0Q3VlU2xCZFlKZVY5b3ZCNkZTdVBCTmx3ZVZyL2VBWUZuWjN3dFZ6b2tDdHROeW4vbGxPVUdidUkweHVZaUJOeFp4NnVrYW5ES0hsYkRrV3Q5djNDUnAwbklRZEJUSWJwMGhjcloxN21Yc1ZPZlRYeEUxUzJLbHl0OVpPZE1ZeGNTeG95Z2lmWkZpUFg5UGtnQXlDbEFyV3JpUkJwV3lJTDNMeFArbllTMlV3K1E5Nzc2L3giLCJtYWMiOiJhNTE2ZmM0NTY2ODUzMzc2Njc1YTQxYmNmNGQ0MWU2MmQ3OTA0YzgwOGEzMmJiNDBiNWYyOGRiNDFkZjFmOGRmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:05:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143263969\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}