{"__meta": {"id": "X746a963f2288f6db60ee594bfcc7a7a6", "datetime": "2025-06-27 02:23:32", "utime": **********.957704, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.542904, "end": **********.957729, "duration": 0.4148252010345459, "duration_str": "415ms", "measures": [{"label": "Booting", "start": **********.542904, "relative_start": 0, "end": **********.902775, "relative_end": **********.902775, "duration": 0.35987114906311035, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.902784, "relative_start": 0.3598802089691162, "end": **********.957731, "relative_end": 1.9073486328125e-06, "duration": 0.0549468994140625, "duration_str": "54.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45734760, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0031400000000000004, "accumulated_duration_str": "3.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9303691, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.554}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.941524, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.554, "width_percent": 17.197}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.947918, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.752, "width_percent": 23.248}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1880539834 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1880539834\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2019243102 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2019243102\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-865400478 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-865400478\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-628946245 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991011173%7C16%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhvQy9vRFhsTm5sMjR1cmVJdlEyb3c9PSIsInZhbHVlIjoiSTM5ZTl3VXpWazlqOXRuY2dveW82eW16S2E2aGxmSWNKOFAxbWVVTjVZNnVtVG1GS0tZdS9XU2I4cS9QaDIyS0NSN2NaTnlnTC9vV1N6TUJQQzVVTERkd3NEU0krZUZwZlZ4dldtcGRVVnJSVVhSbUdkMWNJRkJ4d1ExMnhKS3YySU5jbEpsc1BieWVBZUkzV0J4a1ZjYzE2RnhESGNXNlllb0tQVVJ3ejk0c3ZGcngxdUFuaDQwbXphNG05eGhIUmwwNUNvbHZvM3dsVkpldDhHaUgwdjBkN2pNK3NvOXhZaFFvMVJubkpJbzdVTUMrRXJDbjFxRTlFVmlDMCtBcnZrTGp5bUttUHUyVkRYeFhWMVc0UGNFVE5XWmh5TVhQcXdpWUlrQVNKUFRiNyt4d1IveTM0ZGpGYW1vRWFkNmgyenlmbWRaS0hFcGJBR2l1NlNyZnc1UTd3eHhNRzN3VXdiM2Z2MEhINjduZllqYUhkUUQ1T3UvVE1EYjBnNGs5RXkxZzhHTVlDeks2WGVrdEFUcmJ5RGlXNWpiTFpTUGRBdkZsVjJBQkpSMTFENnNsOGFRanRnYlI4WDhmb2ZJUTZVODl0SWpVN3BWRS9ZTXpDK2JyMjhYd2ZUd1EwOHk0ZjcwN0g5TkZoaGRKWCtVT0FPKyt5TFJUNlJoYytHdU4iLCJtYWMiOiJkNDIxZTc4YTY0OTg0Yzc5MzAzNGY4NjMxZWJkNThjODY2YmU1Zjg0OGI0MjgwOWQ1MGI5NGM5NGUxNDk4YTdmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ink3cVp5S1gwMkxUNEdFb3Nrd0IxOEE9PSIsInZhbHVlIjoidkZNMCtRbUd3NmZIY3ZDRkpGYk9kcUVxdnpnRWtUc0s0cXhYL0NjWlVJMGNHVW1tSStmdm55N01PVGJYQjNhaGxQVXplUzRUVTJhVjR6VzNHNlVOTVJidXpLYmVVWXZNVzB5SWJqazdIZHlHYmFyaXVZQ044R3FiNGpqTmxZYkppK1YrMlpwbHVCS05YSkRNbHhSbzR1MUI2YjZNc2wwaXJLay9HQXpJTHVXTXpTRm9kNThNWVBNYmF4YWcydm5jNW9xZ0pqdG0xSklDVnVTZjBEclgxUVZFRHlkUUNtQ21GWGNudU9IQzdNclZGNE4zZVdFZjhXdE1IN0oxRUN3cEhZMnNkOEFIcmlCVWM1ZGhjMlZnVFpEbnpwM0ZsVmQvRTg2WUdiWlpNK0lXeHJOckxWaTBtNHJoY2ZiVzA4VjZramdNUGRLdnJiMWI1SlZITkpBaHNQSlZwd3prakhQY3llSjRONk5RZVQ5QWJ2Z0pOenFBODRiZW9DQ1ZKTVppQmpzK1hSaDVpcVdTS1hNMUd4alRHeHJzN2gzUUZzVjJYMXBPOGdzNmN6dHVtSkFnUXBBQi93NndwbmNlTk9OajlxZVo5emZwQzBsY3AydUp1Tmh5Z3ZWclIzQTA3eXJacGMwR1NLVnJMYnFYdDhxaVdjWm16cmMvQ1JrUU4yS28iLCJtYWMiOiI1MzU5NTc1YzU2OWYwNTQ5ZjQ3ZjAwY2U3NTBjMWE2MmVjZWFmMDE1Zjg0OGM1OGQwOGEyMzRiNTdhYzU0NjY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-628946245\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1386938750 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1386938750\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-223073890 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlWdkdGRHpXbGw0cmhscUd1SWlPNkE9PSIsInZhbHVlIjoibHVxR2htRkdiT0c2UHZ4a3dPN1I3WnJuZ0NBdFI0clNWYmkwUUk0Sk9mVEcvUkhNR0hWQTd0cmNRanVsbWhWQUNvMWlJaXlDUEhIUzNaOTRiZm1PaHFPbU04b0UvTVhLQjhOdFRkQ21WVzd6R0Fra05nbDNCenRrQlZTcHp3bVhOY2lZYStkeFB6UnpHcFdNbXRVcVRETkVacUs5SzVBTUllL3pHZkYxNmRlV2FXQy9CL2FVWW8vcEpVdXhoR24vaWlHa0owT2RPelpGMXMvTnRjZk9RU0wxY0VqckN2RXVkWlR0Y2JkdDlBTnhwT1FKWS9HdEdVM3E1Q1RubEdUZXp2b3FEeU9XV3Bydk0xd3RyTkM2SUVxOXhla25pQmxWWHBOR25MekhDTmhkSWxyRlk3ZkFORmlHNlVPVElhMHV6QXJJTzVRSTc5cnV5Zkx6alhzZWN1SlJTTEJhS1MyeUF0U0VwZGFzZjN0WTlPZng0eExOQ3lDTmpXRXE0dG5HdHJlQlBwS01RdENHaVlEV2I0N3d0TGlFM3dnWW9ibDFxR2VxbW4xWDEzSFZzS1U0QXVVWWpYbFJBQWtZM2NIK294ODArbVY0aGU2Q2ppMS9IeWt1c2dKNGdlV2hPTk10RXZBMk1NTzZyZ2hFR080SVZtbHN3YzM5QXlUVlE2VUoiLCJtYWMiOiJmM2JmZTJiYjU1NDE3NjAzMzczYjhlZjc2YzQ4ZWZkZDUxMjcwMjJjMjlmNjM3YTk0ODIxM2NmMjFhMDliNmI1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRvMDh6N1hIbjVGMGpqVDhOb1Npd1E9PSIsInZhbHVlIjoicTVCUzhDdiswTTQ1bEdZSmhUYjhwd1AvaDBmWXNHek4ra0NkTU1GQjF5dWhCdmtqcDR6dmZ1YWRvZDNORWFwNzBKaDBXRFlvY3N4YmN3dlU1b1B6bHdqdmlkSklyMmw0dkFTRFlHVG1oU3JiQkpOS29IYUpQKytPa1dvS0FpWkpKYUR6bVV2UnZjS3JqSTdISzA3TVNtcURSZ1liYTkxdldGcWR0YmhqS05nYmhBZVBMdXJnT1pSMmxudUtxVlltSnROUHJDemIzWkRhTTNpSjJhMTZTTENXa0IveWc0WFE5dklCMmN6SUNkMHNHYytJKzA4ZmRUdVo3V3RKQ2hrQjQ5ZklWWG41N0VxVEtLamZpUjVlUnQ5SUdrbVdxenByc2dVNjFZT0c5SDdRNlpRM2tMREwzVVNwZTNBZ2M4R1B1bk9VRGkvUWU3bUkyc2p2M3kwbUEzMEt5ajNnR3NHQVFUNWlOVXMvdHdadEY0Z2R0My9sNlgyeHU0bGxycEdiOVFEcjBMQ0Z0aXlCalR1RmFwcm5YWVZ1cEJTWWZGQlk3ZnVxSzBRekhqSDQ0aEMyOFp3a2x2QXgzZjdyN3VUVllEZ2twWHRrUVNudXR5cXplUU1IMzJEYW5HdmxZR2FYN2hXZmh2STZwY3hmUkFkd0g3NkovWW8vU2k2dVVGaWEiLCJtYWMiOiIyY2FiMGIwOTUwOTY5OWZkNDRmY2RkOWRhOGVhNmZkM2ExZTE5YmNiYzFkMTY2MTBkYWM5N2NlM2NhYjIyODVjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlWdkdGRHpXbGw0cmhscUd1SWlPNkE9PSIsInZhbHVlIjoibHVxR2htRkdiT0c2UHZ4a3dPN1I3WnJuZ0NBdFI0clNWYmkwUUk0Sk9mVEcvUkhNR0hWQTd0cmNRanVsbWhWQUNvMWlJaXlDUEhIUzNaOTRiZm1PaHFPbU04b0UvTVhLQjhOdFRkQ21WVzd6R0Fra05nbDNCenRrQlZTcHp3bVhOY2lZYStkeFB6UnpHcFdNbXRVcVRETkVacUs5SzVBTUllL3pHZkYxNmRlV2FXQy9CL2FVWW8vcEpVdXhoR24vaWlHa0owT2RPelpGMXMvTnRjZk9RU0wxY0VqckN2RXVkWlR0Y2JkdDlBTnhwT1FKWS9HdEdVM3E1Q1RubEdUZXp2b3FEeU9XV3Bydk0xd3RyTkM2SUVxOXhla25pQmxWWHBOR25MekhDTmhkSWxyRlk3ZkFORmlHNlVPVElhMHV6QXJJTzVRSTc5cnV5Zkx6alhzZWN1SlJTTEJhS1MyeUF0U0VwZGFzZjN0WTlPZng0eExOQ3lDTmpXRXE0dG5HdHJlQlBwS01RdENHaVlEV2I0N3d0TGlFM3dnWW9ibDFxR2VxbW4xWDEzSFZzS1U0QXVVWWpYbFJBQWtZM2NIK294ODArbVY0aGU2Q2ppMS9IeWt1c2dKNGdlV2hPTk10RXZBMk1NTzZyZ2hFR080SVZtbHN3YzM5QXlUVlE2VUoiLCJtYWMiOiJmM2JmZTJiYjU1NDE3NjAzMzczYjhlZjc2YzQ4ZWZkZDUxMjcwMjJjMjlmNjM3YTk0ODIxM2NmMjFhMDliNmI1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRvMDh6N1hIbjVGMGpqVDhOb1Npd1E9PSIsInZhbHVlIjoicTVCUzhDdiswTTQ1bEdZSmhUYjhwd1AvaDBmWXNHek4ra0NkTU1GQjF5dWhCdmtqcDR6dmZ1YWRvZDNORWFwNzBKaDBXRFlvY3N4YmN3dlU1b1B6bHdqdmlkSklyMmw0dkFTRFlHVG1oU3JiQkpOS29IYUpQKytPa1dvS0FpWkpKYUR6bVV2UnZjS3JqSTdISzA3TVNtcURSZ1liYTkxdldGcWR0YmhqS05nYmhBZVBMdXJnT1pSMmxudUtxVlltSnROUHJDemIzWkRhTTNpSjJhMTZTTENXa0IveWc0WFE5dklCMmN6SUNkMHNHYytJKzA4ZmRUdVo3V3RKQ2hrQjQ5ZklWWG41N0VxVEtLamZpUjVlUnQ5SUdrbVdxenByc2dVNjFZT0c5SDdRNlpRM2tMREwzVVNwZTNBZ2M4R1B1bk9VRGkvUWU3bUkyc2p2M3kwbUEzMEt5ajNnR3NHQVFUNWlOVXMvdHdadEY0Z2R0My9sNlgyeHU0bGxycEdiOVFEcjBMQ0Z0aXlCalR1RmFwcm5YWVZ1cEJTWWZGQlk3ZnVxSzBRekhqSDQ0aEMyOFp3a2x2QXgzZjdyN3VUVllEZ2twWHRrUVNudXR5cXplUU1IMzJEYW5HdmxZR2FYN2hXZmh2STZwY3hmUkFkd0g3NkovWW8vU2k2dVVGaWEiLCJtYWMiOiIyY2FiMGIwOTUwOTY5OWZkNDRmY2RkOWRhOGVhNmZkM2ExZTE5YmNiYzFkMTY2MTBkYWM5N2NlM2NhYjIyODVjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-223073890\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1969248239 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969248239\", {\"maxDepth\":0})</script>\n"}}