{"__meta": {"id": "X859e2ca9285a432de352fe7398661f3a", "datetime": "2025-06-27 00:43:13", "utime": **********.313362, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750984992.831454, "end": **********.313379, "duration": 0.48192501068115234, "duration_str": "482ms", "measures": [{"label": "Booting", "start": 1750984992.831454, "relative_start": 0, "end": **********.215814, "relative_end": **********.215814, "duration": 0.38436007499694824, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.215824, "relative_start": 0.3843698501586914, "end": **********.313381, "relative_end": 1.9073486328125e-06, "duration": 0.09755706787109375, "duration_str": "97.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018179999999999998, "accumulated_duration_str": "18.18ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.271964, "duration": 0.01697, "duration_str": "16.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.344}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.298912, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.344, "width_percent": 3.795}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.305484, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.14, "width_percent": 2.86}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InhuVzRZdXgvaTIzSXU5NzJBckw2MkE9PSIsInZhbHVlIjoiOCtWV2hJVEg1ZWJ5eXRIOEVmK0Nudz09IiwibWFjIjoiN2Q5ZmM4OTJhY2EyMDRjNTA0NDE5M2MwZTQwMThjODkwMjRlNWVjOTI4OGQwMjFiMGNkZTU4YTFmNDRhOTNjYiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1337090116 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1337090116\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-314939265 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-314939265\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1300203938 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300203938\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-405344091 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6InhuVzRZdXgvaTIzSXU5NzJBckw2MkE9PSIsInZhbHVlIjoiOCtWV2hJVEg1ZWJ5eXRIOEVmK0Nudz09IiwibWFjIjoiN2Q5ZmM4OTJhY2EyMDRjNTA0NDE5M2MwZTQwMThjODkwMjRlNWVjOTI4OGQwMjFiMGNkZTU4YTFmNDRhOTNjYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750984990287%7C64%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImU4WTZXUHh5UWFFS2pzaEQ5eVowNFE9PSIsInZhbHVlIjoiTTdyMGlJS0tQZFI1SDVOMzJHNjhrZER1ZTB5cHZOd21YMCs0MFIyanZRcGgwUTQvOEV3SldSWTlBRGc5YXU1MnNGd3dQTGdvYXlMNWdMaG1VMlpPZWVxK1gxNXRzYnkzd0NUU0VocnUvaWFRV0g2dGl4amdGdmo1VmNqZWpZcFVySTVjUWdySG1vaExYQmtxcEIwemZCZGVRV2xlNFlWZ0g1SnI3VUNucWRrcjd0U1N5T3laQ1JnVmM1aG1HUFBaTkEreExUV3htSWJ0YTd2ZURRZEJsSzYzMGtpMnFtMVl0T200VEhjaTRxV1ZuNk85VU96ZVBWdHQrZjB5VlBwd0NGSGlWSm1UWFBEMjU1NFlyYVNpY2dDVVllL1NkaDQzNmdHNTd4Ukl1RElHMitYY0w4OXU4N0tBU1o0amJjcm4wTlV2eEYzV3lXZ2tVckIvRkQ5M2g1elRIRmtBT3JWTW15MzFrU1ZQa1RTUVZUYXlKT2JCcXIrKzVaRjBscDhXZy9qUDZDUC9tTFp2UTlaUHE3aEFKRVIrVXdZK0wzcXJmeXpqeTZjQ1hCTTJTc0F6NmVzR3l3WUk4TnNKbERpMVNid0dPSXRpWTQ0bnBlZVVSSFR3MG5kSmVFK3Rzd3hycWdvN2labXlFM0tEcGJpNGc3S0V3NzJoNXpNQlVRcGQiLCJtYWMiOiIxNzYwNzU4Yjg3ODJjYTlhMGE0MjdhZmYyNzQ2N2VmOWE1YzY0OGU5ZTFmM2JkNmZhYTJiZWY5YzQyMDIyODI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1GRXFHZTc1eHcwcDhQeDhqZlh3dWc9PSIsInZhbHVlIjoiTGF2b3ErOC80ZGVYTk9VSEpYYldOeEgxanJxSFkrS01mWnJydzgyMUs1dkdlRnFhQTZCNVVPaGZZNkQ3U0tvWmlsTkl2VEM4SkdQMWc2Z2VPcjBFTnBUZ0tyUit4MjNGVndQTG5ySnhVV1ZvalE5Nm5QWG1uT29IcUUxdWtreVJvZTJaazI1bER4MFJPckY2K3RiUUtlZTF1RE1QaUk0cGNnRzQ0dk9BQ2I5bm5PL1g3YWN2cUxjMWNqckxaUkVGb2kwNXY0dGZVck1lblZZQzZ5Y1I0Y0dTdDhnczJMcS9hSFhmZXhDVUpkMDZnYTRDUytwemRuOEtFWkRObFRJQnhaMXpSeHVEeUZpaWhGdnhNMGZKTkQrdXZiQUNKMFdleHRidytEeGFXTU5yMGVPZW9KaXpUQXBneEU1dHlzbEIwQURRaEdVTzhFc1FCR2JadGh5QU01ZWRyZ2k4SmpDSVh1Z0hVUXFkZFgraUtmRjhKYUEzcFFiM0dxMllxT3ZTZ2VqdytjMllEYzlBbFZiakRTZ1FNK2JVTHhSRC9ERzVRbHhiR29FcFgxK3hTMG52NTdUS1JidW1kMXkvVnZVSVlZSWo1YUVpY1g3RkVlOGxTckhRdGxQWklLL0pJS3ZQekdxYnNrYnEwYm9HbGVrenNETDByVy81ekozeGZXL2YiLCJtYWMiOiI3NmY2YjU5ZmJiZThkZTRlMDc5ZDE3ODY5NjM4ZjFiNmU3MWM1NDhhNDhjMmVlZjA3OGU2MDhlMmI3ZjBjMzk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405344091\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1740781112 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1740781112\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1926409789 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:43:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBlNElOWUJ5aHVvUWNZYWdpckJZWVE9PSIsInZhbHVlIjoiWEpxWnJJTnJreXNBTnJIWWN4cXJod3ZzNVo3QnYwMHlxT2x3Y24rRzJ1TWVtTUtGaXpLU05iRHFnemJwaUJoV2I2OFFZSGVEejhadEV2TTkwclhrY2w1bkxVc2taU1BSK3NwQndvMFhDMnk0bGlLeXFtV1RsUys2Znh6ZWhRb2xkbXlTaW9OVkxvKy9iR3o2SzVrYzVJQytYaDdTeGs1ZFUyeTdJSkNtcy9iUXZBYnRkU2RjdWtLNEc5SlJPUTRGK013ZTBXSGdKcVVmR1g3Y3BlSTExMm1lUkJyY3MwcndsbkhnMHQzQ2ZERHNub0JyeDREYU5RamRuYTRwc3JUTDlya2Q4WXlHNm0rZjhUWHpBbGF1aWZsMWhsT3J6VmZzMVRJajhvR0UvREdvUlhVdXJLQ2VOdVJDSldTOC9CeWY1MTVtam1GMTMyMTRFSFd3OFptTXVVYnhPek1nbmhiSk55UWFCbWFPVTQ1VVViVFRYbGRjS0tNcFVqbWd4cW9teDlKa2J3cTkrZE44TUhwQlFqMFdKNlhPMzhIc2J6Z1RCbTFYbDF1ZnFudGNpcnhOK2dCQkxxVjFlV0JRR2ZodUtFNFVURTVHcnRIdmF0V1JRdlVVblFjMUFmU2YyVjY3Mjh2TURlakV1T1lMMmNRSXBmS3FVMUZoMUFlSzdRMUoiLCJtYWMiOiI3ZTA1ZDkyM2M1N2EzYTlkNTgwNDJkMjI3Y2YwMmU1ZWEzMjA1OWI4ZDE2ZDFkNzBiMmMwYzU0MDcyNjZmNTUxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:43:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1BSENRMU5MVWl5dU9rWWhUUkhuU2c9PSIsInZhbHVlIjoiUEpQdEJocUpNbS9YNkR2djJMYkpUWXd4bUJUV1A2bHJlWmtyZXlYYlIzUjQ3SmIrZm5lSlN6OXdvdU0zS1ZRdkN6K013Y3dycUh4V3FGSmQzb2IzMmpWTUQ5a1pPQ1pvR1BWT09ONyt5UGJIUUpHNG5WcWNjdytHdk9KaUxXWjNiN0xVaEVFR0F1R3R1QWsxTDZ4Q3AyWEI0dG5LUlRaZm15TVlrcVZSd0M2bmpOeXArWEdGYmJyOG9jY0hpMlVvV2locU5Nd0FOUWc1UUl2bXl5VHlqSzdacGYvdWJLUWFLSXJLZXN1NFdqalNIeGdKTlAxd1ZQM3NiUVg5YUhDUE9sdkRSSU9XYTBMaGtwM2RMdFFpZHVUZ1NobjQ4WjdTU0xIQ0NBQzNON0lMNHNqTlMzeUR2c2VaMytoN2ZZOW1vT0gyTXU0dC9yTi9VRFl0Rm1SK2UzUFNMYUphdURmOGczNXhrcktUMVFzUDJUbFcvV3VIMnY4YUZwSnpEa25BeEEzbk5aNFN0TU1maUdQS2VtWUtYUWFHOEc4TjJLN2VPd1prNWpTZzdsNUJEaU9JcmVzakp5Y2M0SnBsMnNCWi9IZDQ3ajVSQlFrNk1HYWM2OU5sVlRPRm50NTVLN2hxelp5SzBTcUluLzlqZ2F1N2ppN0VRVThZZnRUOVM5R3UiLCJtYWMiOiJlOWNjMDMzMzk1OGUxY2YzMGY2OGYxNTVhNmNlMzQ0NDUyMTE1MjEwODAxMzYyOGZmZDIxZmM3MGU0YzQwMDU4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:43:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBlNElOWUJ5aHVvUWNZYWdpckJZWVE9PSIsInZhbHVlIjoiWEpxWnJJTnJreXNBTnJIWWN4cXJod3ZzNVo3QnYwMHlxT2x3Y24rRzJ1TWVtTUtGaXpLU05iRHFnemJwaUJoV2I2OFFZSGVEejhadEV2TTkwclhrY2w1bkxVc2taU1BSK3NwQndvMFhDMnk0bGlLeXFtV1RsUys2Znh6ZWhRb2xkbXlTaW9OVkxvKy9iR3o2SzVrYzVJQytYaDdTeGs1ZFUyeTdJSkNtcy9iUXZBYnRkU2RjdWtLNEc5SlJPUTRGK013ZTBXSGdKcVVmR1g3Y3BlSTExMm1lUkJyY3MwcndsbkhnMHQzQ2ZERHNub0JyeDREYU5RamRuYTRwc3JUTDlya2Q4WXlHNm0rZjhUWHpBbGF1aWZsMWhsT3J6VmZzMVRJajhvR0UvREdvUlhVdXJLQ2VOdVJDSldTOC9CeWY1MTVtam1GMTMyMTRFSFd3OFptTXVVYnhPek1nbmhiSk55UWFCbWFPVTQ1VVViVFRYbGRjS0tNcFVqbWd4cW9teDlKa2J3cTkrZE44TUhwQlFqMFdKNlhPMzhIc2J6Z1RCbTFYbDF1ZnFudGNpcnhOK2dCQkxxVjFlV0JRR2ZodUtFNFVURTVHcnRIdmF0V1JRdlVVblFjMUFmU2YyVjY3Mjh2TURlakV1T1lMMmNRSXBmS3FVMUZoMUFlSzdRMUoiLCJtYWMiOiI3ZTA1ZDkyM2M1N2EzYTlkNTgwNDJkMjI3Y2YwMmU1ZWEzMjA1OWI4ZDE2ZDFkNzBiMmMwYzU0MDcyNjZmNTUxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:43:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1BSENRMU5MVWl5dU9rWWhUUkhuU2c9PSIsInZhbHVlIjoiUEpQdEJocUpNbS9YNkR2djJMYkpUWXd4bUJUV1A2bHJlWmtyZXlYYlIzUjQ3SmIrZm5lSlN6OXdvdU0zS1ZRdkN6K013Y3dycUh4V3FGSmQzb2IzMmpWTUQ5a1pPQ1pvR1BWT09ONyt5UGJIUUpHNG5WcWNjdytHdk9KaUxXWjNiN0xVaEVFR0F1R3R1QWsxTDZ4Q3AyWEI0dG5LUlRaZm15TVlrcVZSd0M2bmpOeXArWEdGYmJyOG9jY0hpMlVvV2locU5Nd0FOUWc1UUl2bXl5VHlqSzdacGYvdWJLUWFLSXJLZXN1NFdqalNIeGdKTlAxd1ZQM3NiUVg5YUhDUE9sdkRSSU9XYTBMaGtwM2RMdFFpZHVUZ1NobjQ4WjdTU0xIQ0NBQzNON0lMNHNqTlMzeUR2c2VaMytoN2ZZOW1vT0gyTXU0dC9yTi9VRFl0Rm1SK2UzUFNMYUphdURmOGczNXhrcktUMVFzUDJUbFcvV3VIMnY4YUZwSnpEa25BeEEzbk5aNFN0TU1maUdQS2VtWUtYUWFHOEc4TjJLN2VPd1prNWpTZzdsNUJEaU9JcmVzakp5Y2M0SnBsMnNCWi9IZDQ3ajVSQlFrNk1HYWM2OU5sVlRPRm50NTVLN2hxelp5SzBTcUluLzlqZ2F1N2ppN0VRVThZZnRUOVM5R3UiLCJtYWMiOiJlOWNjMDMzMzk1OGUxY2YzMGY2OGYxNTVhNmNlMzQ0NDUyMTE1MjEwODAxMzYyOGZmZDIxZmM3MGU0YzQwMDU4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:43:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926409789\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2089968741 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6InhuVzRZdXgvaTIzSXU5NzJBckw2MkE9PSIsInZhbHVlIjoiOCtWV2hJVEg1ZWJ5eXRIOEVmK0Nudz09IiwibWFjIjoiN2Q5ZmM4OTJhY2EyMDRjNTA0NDE5M2MwZTQwMThjODkwMjRlNWVjOTI4OGQwMjFiMGNkZTU4YTFmNDRhOTNjYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2089968741\", {\"maxDepth\":0})</script>\n"}}