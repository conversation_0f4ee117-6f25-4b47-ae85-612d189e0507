{"__meta": {"id": "X430fe8e34edeac0623277463bdc05345", "datetime": "2025-06-27 01:15:05", "utime": **********.784782, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.363376, "end": **********.7848, "duration": 0.42142415046691895, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.363376, "relative_start": 0, "end": **********.730762, "relative_end": **********.730762, "duration": 0.3673861026763916, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.730772, "relative_start": 0.36739611625671387, "end": **********.784802, "relative_end": 1.9073486328125e-06, "duration": 0.05402994155883789, "duration_str": "54.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042184, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0031099999999999995, "accumulated_duration_str": "3.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7581859, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.63}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.768791, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.63, "width_percent": 19.293}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.775351, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.923, "width_percent": 16.077}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1464789872 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1464789872\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-664609224 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-664609224\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-39780188 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-39780188\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-358133574 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/vender/eyJpdiI6IlVvSkhPR0UzbVFOQlZjM3ZnU2lYNFE9PSIsInZhbHVlIjoiMnhjV1h6Q01KR2VNWFdTZmtuVzIyQT09IiwibWFjIjoiMjM2N2RiZDkwNTA1Y2FhMzkwOWZjM2QxZGRjZDdkOGU5ZmJiYjU5MGUxMTBjMjY2ODI3YzU1Mzk3OWZkMjljYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986903260%7C90%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjF3VnBrUUd0SHVYMkxDSEJ2OGhMV1E9PSIsInZhbHVlIjoiWDZnSHR6Skx0TGVDcmZCSUhaU2FYRjNUMEtZclQvWUV1Smd3S2JCeURnSjJPNW1YT283REZmdXJoSGRudGhtVWtwOHpiYTIxVmFGT2ZHaCtSWGNPeVlSa2tJVWZMWXRsejQ4c2x6aDJvYlBjeEs5K0srN2FXd2lyZFJMOXhVMk0yOHZUZXFHcFZ3Sm5VVFpLV3VDMjVNZ0t4N1Zab1FoTGhRdG9UaGwxTXNFM1lSZUFEVTBnb3FoSlVaUWxKdVhiMWVsa3NUR05PcSt6eERSSFd2aVZUS3IzcCtDblR0WVRBdzVDM29xdWVUaDlWMlB6V2NXSFNpN2lZZFNQMWRJQXAremczYzU2RkVHamZ6blBFL1NRQ0VPUUxFbEYxUHJxRTcvT3p2R1A3YWZmU0UycDFjMksyNFRJNDEvdHhSdFV2NGY5SkRZbVNycG95UnBlMnJPdVpvbG1uUkhsL3UrNUpUVDRrbnhub2ZhRDNwbm41d091TlJzSWVuQXJXOERMRFgwQzdOS3VEL0hUdEpnd0JJeEVIcWhHZDVPVElSRGZ2bnhOUmFLb0VrNTRXMzFLeTlnWjdPVldRbGJPeEpaOUdLSWFnNlhNaGQ1VncwUXBwejdSYTFiZ25saUwzYmJtR2tqemkzQmFPMnFVZGxVcDFIQ1JTYWNtd3FvZ0x3UVciLCJtYWMiOiI3NzAxMTE3NjU5MzVkOWFkMDg5Y2ExZjU5YmM1MThkZjgxNTVmMWFkMTlmNjQ1YjVlOTgxOTljNDZiZDY1MGJkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJTT2taaGZOZ29Oa1YrdmFDNUg0dVE9PSIsInZhbHVlIjoieHhIL3ZobkNGUE90dHlDdTVUcWw4U2VLZWlDWndUTE5QaWFvUTgrdlhRdGtXZ0pCQjJVUEtLcXBYQTJqb01yWjF1dWZEU3ZiMlowS0h1WmpUOGtJRVc0SkI1a3FTbUVxOVVMUGd5UjJwQ0k3MzEydmJWYW85Y3pTT1BCU3UzQWd0aVlBU0toMVlZcEpkeDNsdHVpUGJlVmp6SUxmai9VRWNtdVhydmIrQTF5ZVR0c01jNm5mUzJHU3V1NVJETk5TNldod2RMSEFwaGhxVUM1TGFBQjlkMkJ6M0wrcjNuMnEzd0Qycm1qSTE1aU9FbzhqNGFJd3dzZGtZUGJ4Ulc3ZUw5S3UxaGdWS1MyVFp3bXB6VHVKVFRxSnVkWDI2SlZOdVhtZGxaOEtWUEo5REJwN2VzMTl3bUZRNFJ4cnFXdlJVS1JPUlVsZ1htUFpDcHdZMDZkd1BFZ3NZOHE4STNQMGJMRlNkdWx1WXQ4eFFrazJyK2dmeU5sdGNkTkFMYnA2UVhPbmdwaXdlUzhXeDhSMW9vYit3RUcwQkVrMm5vV2lCRGdhV3FYM0x2blI5d0RwYklDTTg0cjN2MHlsd2lCb2ptTGhJTEUyNE4zaEhYVmVPejBPbzZPdTdaRU1GeEtQUVQrMTNvUFZmOENQb1VtUkZGd0N4WjduSTZOYTV5QXYiLCJtYWMiOiI0ODBmODM4M2I4Y2YzNzBhZTA2ZGFmYmU5YWRjNjU5MzI1YWI1MThmMGVhNDU2OGQxYmVmYTM0YjQzM2NkNTM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-358133574\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-89272056 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-89272056\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-811065752 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:15:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5pVUtYK2hxOEpoOTJveTlsTE92d2c9PSIsInZhbHVlIjoiTXo3STNBYzhNZmE5bUxHMEdncnJhYXF1cldKellGdzV6M2VLTzdGL2pJUkNGWXNSWjRXazNDKzQwQ3hERkNnVVFtMHppS1kwTjdCRmI2aE5oZFJCT0pBVFdJd2RyTnhWS1JZUytFbThPZUpWSG93WDZXMnNYSzVzU1F4Mk0wYkRTd3k4V3NpMWM1eUVVTjZqMlFvKzFWTGo0VUpUcDVDem1wb2IyU29QZ3prbWx0eTZJMHNJelZBUGlENVFmUmxMZzJVaFo4bXFRSTBSZVo1QkZwV3ZTZTBXNm96aVVLbWxBMmx3Z1FjcjgxSi9nMG9Vcll2K2RLdmQ3M016dWJqZlJKYlAzVDJCTFFDeWJYS1h6RVUrOFhOVk5kMFhMT2p4c1ZsTkJpeXZva3QxQzJCNmo5QzkramxuaXVTZ1RtYnBET0hBSzNqMEd3Nm5Bb0xHK1lkemZxVjJkc2xyaXgrSGZNS3dqTjN5TlVCdVVody9HcWRBOEFNL3kzdFJaUUVPUHN2OEJxNFZQdTBZK0xkWU91RzcrTm00MzIxS2k3RFRNYzFuWU52RGlqYnVaSlF2c292L0Z5OGJSRW1zRDVzb1FadlNzSXROTnlvRjgySExaSmpjcnRnVXJPaHhYVWt0Zy94bjlYbW1YenU4TmdzME5oKzlvQUdqbEU4UWpNMkciLCJtYWMiOiI0NTQ3MTBjMDlkNzVlYTliZWI5MDA3OTY4ZGVhYmYwOThmYzgwMTA5NzY4NjhjMzMzOTUwNDQ4MGUyNWYwYzA3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlF0SWFMb1hQU21PSnZ2ZU00ZDRNWlE9PSIsInZhbHVlIjoiVGpiTWw5OC9oamRpTUhFV2Y3NndFbFR0cjU0bHQ1TzN4NHJidDNaTStOWXlvUkpsUnd3bWYzNExhSDN6WklJdkFZUXhRQW1mdlhXWlR1d2dFL1Jkb1pGRWwzcE1keHl0RU5jUGNqWlRidlJoQjFjTHB2MXcxbUMyQ3Z5TmxFUlZpZ0xHWUc1QkU2eEdSaHBBa3VTRkdWVWpnaHZCdllOcmZieXhuVFNYM3RnUjA3eXhBaTdoOFVTTkV5eE05REtmSEkvNWNYNzhIV3A4MXlxdVB1ckNaVkZabk5WRC8wQlBwK1NoaXNDVHZDYmVJdTcxSVhIT21lSDlUcWlPMFRwUzRuUlNTUWlPN05GZDdyWC90NkhMYkx1VHlhOGZ3ZEdkdWlHblIwSDV5a25RRzc0NWUxNFJhTEtIZ0VUMVVkNUpPRGZIeUdTQmNtWTRxeG5tbHo1dzdUMm5SYWp0eCtOY0YxTC9XcnovTmVKbit2MTUveGZOOUJVWkdEeldZSW5WQms5L3IzQnlsRUFVWG4wSnVobEROY0pMWXJueCtUUkZxUVE3b0R3Tnl5VXMxRUdGSXZVaWtncTQ3eVVPZTg0QWgvczBNRnYvMHc2Qm9OaDlRT0xDcUhTWGd4ZDRVQVVOaUZ0UXBQSG0zSXArQ3U2Q01vMWtuVXRMcHZvVVZrbnoiLCJtYWMiOiI4MDk2MmZhMTJlZDIxZWUyYTY0M2QxOWFlZWZmOWQyYTNlODhiNGNlMDQzZTdiOTdmMDk0OTY5YWFlOGRmN2FmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5pVUtYK2hxOEpoOTJveTlsTE92d2c9PSIsInZhbHVlIjoiTXo3STNBYzhNZmE5bUxHMEdncnJhYXF1cldKellGdzV6M2VLTzdGL2pJUkNGWXNSWjRXazNDKzQwQ3hERkNnVVFtMHppS1kwTjdCRmI2aE5oZFJCT0pBVFdJd2RyTnhWS1JZUytFbThPZUpWSG93WDZXMnNYSzVzU1F4Mk0wYkRTd3k4V3NpMWM1eUVVTjZqMlFvKzFWTGo0VUpUcDVDem1wb2IyU29QZ3prbWx0eTZJMHNJelZBUGlENVFmUmxMZzJVaFo4bXFRSTBSZVo1QkZwV3ZTZTBXNm96aVVLbWxBMmx3Z1FjcjgxSi9nMG9Vcll2K2RLdmQ3M016dWJqZlJKYlAzVDJCTFFDeWJYS1h6RVUrOFhOVk5kMFhMT2p4c1ZsTkJpeXZva3QxQzJCNmo5QzkramxuaXVTZ1RtYnBET0hBSzNqMEd3Nm5Bb0xHK1lkemZxVjJkc2xyaXgrSGZNS3dqTjN5TlVCdVVody9HcWRBOEFNL3kzdFJaUUVPUHN2OEJxNFZQdTBZK0xkWU91RzcrTm00MzIxS2k3RFRNYzFuWU52RGlqYnVaSlF2c292L0Z5OGJSRW1zRDVzb1FadlNzSXROTnlvRjgySExaSmpjcnRnVXJPaHhYVWt0Zy94bjlYbW1YenU4TmdzME5oKzlvQUdqbEU4UWpNMkciLCJtYWMiOiI0NTQ3MTBjMDlkNzVlYTliZWI5MDA3OTY4ZGVhYmYwOThmYzgwMTA5NzY4NjhjMzMzOTUwNDQ4MGUyNWYwYzA3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlF0SWFMb1hQU21PSnZ2ZU00ZDRNWlE9PSIsInZhbHVlIjoiVGpiTWw5OC9oamRpTUhFV2Y3NndFbFR0cjU0bHQ1TzN4NHJidDNaTStOWXlvUkpsUnd3bWYzNExhSDN6WklJdkFZUXhRQW1mdlhXWlR1d2dFL1Jkb1pGRWwzcE1keHl0RU5jUGNqWlRidlJoQjFjTHB2MXcxbUMyQ3Z5TmxFUlZpZ0xHWUc1QkU2eEdSaHBBa3VTRkdWVWpnaHZCdllOcmZieXhuVFNYM3RnUjA3eXhBaTdoOFVTTkV5eE05REtmSEkvNWNYNzhIV3A4MXlxdVB1ckNaVkZabk5WRC8wQlBwK1NoaXNDVHZDYmVJdTcxSVhIT21lSDlUcWlPMFRwUzRuUlNTUWlPN05GZDdyWC90NkhMYkx1VHlhOGZ3ZEdkdWlHblIwSDV5a25RRzc0NWUxNFJhTEtIZ0VUMVVkNUpPRGZIeUdTQmNtWTRxeG5tbHo1dzdUMm5SYWp0eCtOY0YxTC9XcnovTmVKbit2MTUveGZOOUJVWkdEeldZSW5WQms5L3IzQnlsRUFVWG4wSnVobEROY0pMWXJueCtUUkZxUVE3b0R3Tnl5VXMxRUdGSXZVaWtncTQ3eVVPZTg0QWgvczBNRnYvMHc2Qm9OaDlRT0xDcUhTWGd4ZDRVQVVOaUZ0UXBQSG0zSXArQ3U2Q01vMWtuVXRMcHZvVVZrbnoiLCJtYWMiOiI4MDk2MmZhMTJlZDIxZWUyYTY0M2QxOWFlZWZmOWQyYTNlODhiNGNlMDQzZTdiOTdmMDk0OTY5YWFlOGRmN2FmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-811065752\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-490895589 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-490895589\", {\"maxDepth\":0})</script>\n"}}