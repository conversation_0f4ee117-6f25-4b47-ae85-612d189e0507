{"__meta": {"id": "Xfe6486f3c3ffc332825f563a29c2f82d", "datetime": "2025-06-27 01:13:15", "utime": **********.96809, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.545908, "end": **********.968103, "duration": 0.4221949577331543, "duration_str": "422ms", "measures": [{"label": "Booting", "start": **********.545908, "relative_start": 0, "end": **********.876674, "relative_end": **********.876674, "duration": 0.330765962600708, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.876683, "relative_start": 0.33077502250671387, "end": **********.968105, "relative_end": 2.1457672119140625e-06, "duration": 0.09142208099365234, "duration_str": "91.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45938800, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.939321, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.944779, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.960719, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.963092, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.03063, "accumulated_duration_str": "30.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.900811, "duration": 0.024300000000000002, "duration_str": "24.3ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.334}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.927269, "duration": 0.0033599999999999997, "duration_str": "3.36ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 79.334, "width_percent": 10.97}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.932311, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 90.304, "width_percent": 0.555}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.939928, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 90.859, "width_percent": 1.567}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9454248, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 92.426, "width_percent": 1.959}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4090}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.953264, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4090", "source": "app/Models/Utility.php:4090", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4090", "ajax": false, "filename": "Utility.php", "line": "4090"}, "connection": "kdmkjkqknb", "start_percent": 94.385, "width_percent": 1.665}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4091}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.955653, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4091", "source": "app/Models/Utility.php:4091", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4091", "ajax": false, "filename": "Utility.php", "line": "4091"}, "connection": "kdmkjkqknb", "start_percent": 96.05, "width_percent": 1.11}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9578838, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 97.16, "width_percent": 1.339}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq24\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.961548, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 98.498, "width_percent": 1.502}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c0QLm2IJSY55XLSie7FZIMItvVnGUAnfB3XPkDy7", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1129409240 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1129409240\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1056418674 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1056418674\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1540953068 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1540953068\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1301590926 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6IkNRVTRqaWhjakhGbnROZzFmYzExaXc9PSIsInZhbHVlIjoia1RvMU42ditCT0tiRXlHVDNaaVFubkNDT09mMHlRc0hDRGxwZFR4QUx2Z2JkcHBOTnY4d0tpc2d3OW50Q0NpYjdDdnZNTHEyT0pINnB0MjRhb1RSYXh0cUdOeStvNE0zSWhXVCtEYVhuYVIzeVh4UW4vZHhPSk5EQURyVVRCSE5xUlZMa3hJYkFub1k2RHNDbmE3VVdOUVBNbGdlR2VQTlg5YmdnZ2JXazZ6TzV6QStNZjhyelFyN2xqckhLcFgvcnBXd2xCMWNGWTZrZHdkbGg0aFdzQkg5WmJwRzg5bktVZjMvc0lvUTJTWGo3Qit4Mm1ZaG1oZVFSVWZRMnA5SWl3OGw2dU11WGE5bHk2NFQ4YTJwLzlCYllaTExuMlZmZXpMY2dDeWhMeWJ6STF4aEtiY0N6YzByekgwUnc0MGRlNTFzUjRXb0wwVXJTZlQ2RUNhRG5YZTQyT0dMaHEvb2hkR2pLWmZPUU1VdE02ejMwZm9pNmtacTdWUUp1R3F1RUFFQ3hUZG5LaWhyUmxlQTNZc1FSZ1RhVTZoOFVSanFvY0FFV2FLeXZaV0kzZDBVSllWUE9tL0g4TnpVM1ozalBmbFZreXh4SFJ6aDVwVFV5M2IzdmtjRTRZdFB4NkRsNFJucVBLbzV1T2NoUTgrUXF5dG54bXduZkF5MjZmSEciLCJtYWMiOiJjZjdlYWI1ZWIxZTQ0MzliZDJmZjY2ODE4NmJiZTVlN2JmY2IzYTc4MjhhOWViZGQxMGIyMWNmZWE4MDE4ZjNmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik84NWFJTDJFOEZHbUNlb1VlOWhoNVE9PSIsInZhbHVlIjoiM0s3R0JUdS9vVytCK3ZlSkpZVHIxbE9QVnFyU3JKSjhWdVAwQWNvbjUrTXNzTndQNWx4NGZESEtpNTAwcjFoc0hJdkdsUHV5ZVhJWkVUZjAwZm0vd0d6bFQxelkxZlQyTmJjQ1UrVVpZemY0L2lBY2JSN2dNVWd4cWFpUzFYWE5hOE1PSGtmakFIUkZCN1lJUDdRNTBXVTBIVmtxTGY3Z0RYNndHbXdkazRWaEJFVXc2Q0wvTXF0clV4YWQ2SEJ1RDEySHRIbEw5YUo4Wnc0eE0rOHJuajY3RjR0YzIxNERKenYzREprM2xSaW5vN05tNU5QMkQrZjJRYWxuYm9yTnhwaVMycldPQTJRQmRNWTkrRHQvVHBOWGYxaGhsZ3BJZURJVXk0YmFLc1dLK0w3QWVabnlSR2haV0JuWjNKdDVZRk42ZXVTUnlkQTJHY2JwUVErWldZVVJuR2pSZ00vbjRDeG1SSTRCK24rL082WWU5OWp1Q2VtaXY0dTF1UGJsTnQ0b25FWU5DMHBxYXdGWEQxT0pOaCtKSGZpV3ZTd3NvTHEzWEFSN1AyTVdyVDl1eHRlTC9nOElZd0dGaGVQQ2hYMVgwcFVzNEJDMUloeWZNemFTc0FSZ1NlaDVGSk4yQUlTR3oxbFN3V3QwcUJOOEJPYjUyNElobGM5OUFQM28iLCJtYWMiOiIxZTg1NTMzMzg5ZGY3YmNlYzYwZTIwZWJmMGZjOGU5ZTVhODNjOGQ4ZTUwNGZjZWNhNzU0MmZlOTM1Y2MwZTI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301590926\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1660384301 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c0QLm2IJSY55XLSie7FZIMItvVnGUAnfB3XPkDy7</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">czOqTklJP2d1yoJQUeh78ZgoOyZMhC716aFTwd5f</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1660384301\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-345463940 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:13:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhRd1ZuTm1BOGRaMzJzMDlJMFhXR1E9PSIsInZhbHVlIjoiVFVPWmVLekYxQno0K2FmNXBVOFNmNWczZ2hjcjU5NDQvUnRjWXE2TFFHbGlITnJhZkpIUzZPdUJoRmhoUW5rRnZ0bURtRmZWbVgzMDRhS1VleHhUUm5OSElBeXhZamlQdFJGV0dabTVCeGJoM3lYQW5QYy9oYmhiU2JzeW96Q0hxR1JKRXo5OU5YYzYzZTZvTENrM2RXb3dORU5kbFl1R0ZUS09Dd0Uya0MwSW5WTzRQckMyTWg0SndJMWI0VHhIS3JETUUwRWZwQmJnd2p5MkxQT2lrQ0w3aFIxanZjQ2wrekVzdmtDeFZrbHpMaTgzMWppbjZwY1d3Wjg0ME1RL1MwamwzaWdMUkk4eVE3cUhjYU9YVlRMWVR2cXFuR3hkOEZSZytsbnR0RWNmOWt2STNlM1BYaFFvMHJtN2JIS3dMTy9hUjluNmx1Yk1mTUVKZm82bTEvMTFtaEVrbEJsUHNRWlg4eldkcnVjaVpIODVid0tVMkhYTWhKRlpVVElvdGxjMVk3ZmxjOExjQkRpc3cvMUFTMTBhZUI1UDdEV1kvdk5kRE96MHFGdVRwVWk0eUE1SlRNb25xamRzbGNNbUozNVJrQUFwU1Q5c2ZHU2JDbmovZ1BxY0UvZ0RrbTUrdjhZbUJZQldVVzdiQUhhVG10aEZ2czQyVFY0emorQkYiLCJtYWMiOiIzMDMyYWJlNjFjYWJjODcwMzQ5Njc5NzIxYTY5NmQ4MjMwYTNlYmIwNjYxZGNkODdjYjY2ZWY4ZGQ4ZTIwZjdiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpQdzdKVTBzSUVrZ2cxV2xIQVVWNFE9PSIsInZhbHVlIjoiN0ltUFVrNlRGbDZocXU3R3I5Q0FPSVlqL0xLZ0dUaHFjbW5hV1UzY3FSSzdWby9wcXQxUEZ5cXNUOXVhOU4rSXhQUzdHeVlpc1QxdUpRcVBDMG1CQ0h5L0N3Z3dVb3FSM3hheXFKcDk1TjJjL0R3cXplRm9aaXRMS2wxYnBhQzFIZlJPaFZFOUhhMXlvVi9wMzMva2g4MU8wTURlMDlkWWhCb0FRQm9qdjBqd2N6WDlLa05UWW1NN3JxVnBrWlN5WkJnVFY5dWhWU3FyTjM4VzJ5djNybDU2OGh1cVRrNmNLRCtocW5Dd2ZCeHZyc1FieXp3aU1FbU05b3NUNTJBZ296S2dnY2JTUXYwcnhpcjdsVTlHQzNUd1lLSjdzMkhPT25sOE04eS9ycTJsMzQycUZ6bHh2Q3hicTR6L1krVHNvWVBoamtzSklkM1l6cWkxM2JEK1g5Rm8xY011aVVEMTRZbG9sVEpteHUwSUFUMTdkU0xUZHd3VVNQWFptTmVYSVBIcWNYOTJrVWo4RHhGS2F1NjE1YzFvMDFKckxVN2xZTmJRN2NWMHd4S0h6dStGTE45VnBaSFMxR3NBaEFzbmo4RXoyeUZ6Y0x4ejJFSWovbzYydE5URkx2RHY1WGM0bGt5SGZ4Z2pxeU9iK0dCWmZZUEVuajMvdDRhNHVTQlUiLCJtYWMiOiJjNzRhZDhjMjRiMjA3MDBjZTk2Yzg2MDE2Y2RlNmQzYjVjYTk4MDVkYmJjZjYyMjZlMzQ4ODBmYjkzZjc4OTQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhRd1ZuTm1BOGRaMzJzMDlJMFhXR1E9PSIsInZhbHVlIjoiVFVPWmVLekYxQno0K2FmNXBVOFNmNWczZ2hjcjU5NDQvUnRjWXE2TFFHbGlITnJhZkpIUzZPdUJoRmhoUW5rRnZ0bURtRmZWbVgzMDRhS1VleHhUUm5OSElBeXhZamlQdFJGV0dabTVCeGJoM3lYQW5QYy9oYmhiU2JzeW96Q0hxR1JKRXo5OU5YYzYzZTZvTENrM2RXb3dORU5kbFl1R0ZUS09Dd0Uya0MwSW5WTzRQckMyTWg0SndJMWI0VHhIS3JETUUwRWZwQmJnd2p5MkxQT2lrQ0w3aFIxanZjQ2wrekVzdmtDeFZrbHpMaTgzMWppbjZwY1d3Wjg0ME1RL1MwamwzaWdMUkk4eVE3cUhjYU9YVlRMWVR2cXFuR3hkOEZSZytsbnR0RWNmOWt2STNlM1BYaFFvMHJtN2JIS3dMTy9hUjluNmx1Yk1mTUVKZm82bTEvMTFtaEVrbEJsUHNRWlg4eldkcnVjaVpIODVid0tVMkhYTWhKRlpVVElvdGxjMVk3ZmxjOExjQkRpc3cvMUFTMTBhZUI1UDdEV1kvdk5kRE96MHFGdVRwVWk0eUE1SlRNb25xamRzbGNNbUozNVJrQUFwU1Q5c2ZHU2JDbmovZ1BxY0UvZ0RrbTUrdjhZbUJZQldVVzdiQUhhVG10aEZ2czQyVFY0emorQkYiLCJtYWMiOiIzMDMyYWJlNjFjYWJjODcwMzQ5Njc5NzIxYTY5NmQ4MjMwYTNlYmIwNjYxZGNkODdjYjY2ZWY4ZGQ4ZTIwZjdiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpQdzdKVTBzSUVrZ2cxV2xIQVVWNFE9PSIsInZhbHVlIjoiN0ltUFVrNlRGbDZocXU3R3I5Q0FPSVlqL0xLZ0dUaHFjbW5hV1UzY3FSSzdWby9wcXQxUEZ5cXNUOXVhOU4rSXhQUzdHeVlpc1QxdUpRcVBDMG1CQ0h5L0N3Z3dVb3FSM3hheXFKcDk1TjJjL0R3cXplRm9aaXRMS2wxYnBhQzFIZlJPaFZFOUhhMXlvVi9wMzMva2g4MU8wTURlMDlkWWhCb0FRQm9qdjBqd2N6WDlLa05UWW1NN3JxVnBrWlN5WkJnVFY5dWhWU3FyTjM4VzJ5djNybDU2OGh1cVRrNmNLRCtocW5Dd2ZCeHZyc1FieXp3aU1FbU05b3NUNTJBZ296S2dnY2JTUXYwcnhpcjdsVTlHQzNUd1lLSjdzMkhPT25sOE04eS9ycTJsMzQycUZ6bHh2Q3hicTR6L1krVHNvWVBoamtzSklkM1l6cWkxM2JEK1g5Rm8xY011aVVEMTRZbG9sVEpteHUwSUFUMTdkU0xUZHd3VVNQWFptTmVYSVBIcWNYOTJrVWo4RHhGS2F1NjE1YzFvMDFKckxVN2xZTmJRN2NWMHd4S0h6dStGTE45VnBaSFMxR3NBaEFzbmo4RXoyeUZ6Y0x4ejJFSWovbzYydE5URkx2RHY1WGM0bGt5SGZ4Z2pxeU9iK0dCWmZZUEVuajMvdDRhNHVTQlUiLCJtYWMiOiJjNzRhZDhjMjRiMjA3MDBjZTk2Yzg2MDE2Y2RlNmQzYjVjYTk4MDVkYmJjZjYyMjZlMzQ4ODBmYjkzZjc4OTQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-345463940\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1575521459 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c0QLm2IJSY55XLSie7FZIMItvVnGUAnfB3XPkDy7</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1575521459\", {\"maxDepth\":0})</script>\n"}}