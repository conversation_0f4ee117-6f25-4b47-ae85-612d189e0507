{"__meta": {"id": "X52821587cd62d1d6958fee29245867f4", "datetime": "2025-06-27 02:26:01", "utime": **********.619357, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.210087, "end": **********.619375, "duration": 0.4092879295349121, "duration_str": "409ms", "measures": [{"label": "Booting", "start": **********.210087, "relative_start": 0, "end": **********.557685, "relative_end": **********.557685, "duration": 0.3475978374481201, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.557693, "relative_start": 0.34760594367980957, "end": **********.619377, "relative_end": 1.9073486328125e-06, "duration": 0.06168389320373535, "duration_str": "61.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45735016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027800000000000004, "accumulated_duration_str": "2.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.588734, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.705}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.598205, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.705, "width_percent": 12.23}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.603585, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.935, "width_percent": 19.065}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/21\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-19240371 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-19240371\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1964467894 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1964467894\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2088051199 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088051199\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1163976114 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991158215%7C29%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im43MEpSM2E0K1ZRK2JjZGlFdG50cUE9PSIsInZhbHVlIjoiVGNxcEFtZENHLzUzdmY2YUJ2TXRRbkdhZXIrTzRTa2ZGRVVHR0F5ZW0wS2VOVDZ1eWFycWtHOUJoVDdVd2ZYME5kR2craXhidEp0YmdjT3FmTjczbG1ickFPRGFqeVZJNXNLeFhGTlhTa1JYN0NtRUM3cDNjelg5dGptdms3RmRRKzR2NGMydzM0YlFyTEFFUTBUdWZtYlJ4RlRuSVgxcllncEIxVXhEWklqK3RTNjFoMDhoQjNYNnRCT0xpeWFXeHlXZTlOSC9IaE5LOC8xenFLQlNxTTFxbWszdHVBaUQxVnlGSU1sUFJ4cWhsbTVlWG9IVGpSRmNDMXZSdUNlSlFEb3BySEs4d3l0U2dnT25NTXJEQmM3LzNyTzdPbFJ2VlZJMlJMZ1N3YlF1TGp0cVN0RGFDZTlKU2R6Rk1TbSszOHpXY0gyZFJVZWFlb0JiMXYwdHVIN0wvVG4xNTVCdXM1em9SRFN1Rzh5UWM5SVF4MG1sTzVwSkFhQUJMY3FCWHJXbWJob20vT1RrQU04Nzl3Y1MwZitVQ25RMktnWG1CbXhsVDBkbnBBK3NCYUYzNXVKYUlSSDNYS1FxdFFJcEJBN3lZK0tDR3k0d1A0Q2pWMTJwS1hxVzBzU0ZWMGVNeDNHN1k1NGJmOEllbjlQcUdWZUtpYUhLV2IrOVJHcXIiLCJtYWMiOiIxNGFhMGQ3NTk2MDA4OTIyNDc1N2JmMzQzYWM3MThiNjNjNjFlMWIwYWNiMmZhOWNiMDM1YmM1NDNmMjE1Y2JiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imdhdzl6MDRhMFpnN2pkemZkVmhGdkE9PSIsInZhbHVlIjoiUWtzUU9ZMWU4WUlrV2xoUiszRSs2UXg3U1VPbFNNQzBXa2EwdzM2djd0NXFxOEhXQ0dJLzJSd0Z6T3VGWDZVVTZBVCtxamhHSVpkdFI1Z3NVNU1NN3d0NThYQVZWSjJic21RR1diOEdzZnAxbWRkcDB3YlNxL3NNVkdWNDlJZUx2K2JQRDF2ZWZ4Tm9HRXhVUzIybWVmUFRKNVhVSEd3WGxPL0FCbTRXQzk1d05VZStTM0FaN0JDVVVlekJLOWtGeERXa0cxSy9obElBRVYvYUIwN0NBSldrbW13eW9GVzZSYTRkcjFhL0Mxbm9FZXdaOXJYSTUxZC8vWUxoQjgyVFY0aGtOS25sTFhldHVEYm0wclJlWEV0WElZamdkNE9Oa1lpK1M1eit1L0dROCs0empjclRhRy93M0dLK1dkNTNqWnJ5eEUwcUw1ZTlOcWZBWUd3ZWtYYjB6Kyt4dDFxclJCWW0zcXM4aU9WNWVnTFRsOHVRQTZVaUVXcGttbkNEcUMzUUZwV0hybXhqNDB3RndLRXpua0VUWjlZQ1ZCWkw0ZVVMd0hGczBrTUorcVJnR1NSZm9xYXJoU2F4OTV5aGlWMW81enNKbkRLTy90RmFITWQ4RG5TbE4zTm9xakxjaXJsTUNVam9VZ2NETk1VUmtEZ1pxcTVVbDd1NHIxWXIiLCJtYWMiOiI1MzcwZTU0ZjAyOTk5OWVmNmYxYjQ0ZDllYjQ5OGFjNzExNTU1YWI0YTdjNzk4MzdlNGJmMWM4M2JhZjk0MzUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163976114\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2069552485 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2069552485\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:26:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJBUGJ0WHh1K21xM0xMLzFQdGJRNXc9PSIsInZhbHVlIjoiNGhXdmtiRnhlT3FpOUkrQUtGL213L2dTOEN6ZkxxQytBeWhsVmlYT0FxZUcxRkxCQjFMMGRsbnpSMEtDMUNSSjJ3eHZ4RHpiMTdwbmRMOG1FbFd6RnFUQlB6TEJwSm84VzJqb2QrdmpMVXp5c0M3UzJYc1hWZlNvRFYzRk5LSEg0TnhldGRqTHRYT0NScG15Q1VRQXBNM3lQU2EwN3NQK2N6MGkyUitrYkxZenV4c0p0Ri9IaVlMTWdXdFpYL25keWN5NHBNdWRJRVl4SURmTVlScXlMcERxZVJ2bEs4N1d0dHlEWXc2ZkpnVTY2UFMraHR4a0Y2endXMkxkbzhObU5MVndsYmIxY2FoZWlCRkVWeDBRc3RZMVR5cHcwY3lNUDB5Nmc1dTdhVjNFT083UEJwS3R2UGRkLzBCaW5OZEU3NXp1YVhoaEpOK1NXdlFsRlk1TGluUkU1SUZWQ0crVVJmdTYzcnk2V1ozT3NsaHlQd3IydjBsWVlGc2g1NUdFRytXcjV0cy9UdkgxQWc2cnVJRHM4Z0dZZnZvb0xkcUVnZXNiMVpJWTVkOVlwM05weEdJblBlbWFoOE1CNXZTM0VoTzJ4dFVxY2NsVFA3K0ZUeEc2dHlLVWhHcEhiZE1VNTVjTXpEMUpqL2E5US9DRWVGczEvUS8xMVE4Szlpb2wiLCJtYWMiOiJjMjQzMTZmMGYxNDkzZDhiMjdhNTQ5MDczZTNjMjJlMjA3ZWRhZTdmOWU1MGI0MmU2Njk0MjYyNzY2NDQ2OGIyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZDVUREdTJyOVpvUnZKK1pUN1RiWWc9PSIsInZhbHVlIjoiVFZrRS92STAyeWZvMVhJQnN4clZBbmF5Zk9leG12T01sWi9rVzd6VjhXemtPWjc3Z2ZuNU9FSlVlWjNaVXh6aE16cGxJVUN1ZVB5eHRxOCtVdkpjNkRycCtQL29mOHkwSHlWZkFZMnh4cFFNbGNsdEpyUUNidDRKaXR4U0Njc1FnL0FiSmVWcjdXSWdENERNMFdyaE5pVkVzaWl2SDhDOFhKWGxUNUMrRDJlWjJSWXlwaHdzek0rdk04TzVGVFg0NkpvN1VrQmQyczRZaU1tLytHOG1Ba2tKV0Yrb0hyZ1JoOTdnbW5YY1cxOHBmRXY0UmFXOEpEbEgrYXFVOUZlcDdJQUZiMGxiSDhEejc1VXp4d2ZpNmFxb0FNZ3B5SnN2VWhCTTg5d3FmUGpWbi9WTG8rUXdCV3FtdnlTeEtsV2dmTTVuVmJlWmpIOWNCMys0YUtxWlNuQ0RSUXBUdi9zVzBiRFQ1REwyRHl5U2NuTnJoS0lrMzlGUFdsS3BMWDdpT1NGcjNNRE5ZTUZFeWx2OTVjWXR6ZWJ3U01LV2tzOXpjY2pFbnluM1VQM2dYQjFyZ05UREdUNE1FeUh2eXkvUHNvNlFqNHU4WUhjb0ZpMkVQd3JRQTZxN1pJVHRzaktFeFFTYkJTcG5YZzBtb1NtUzhMTTZ1SXY4bHF4SmdPZkwiLCJtYWMiOiJhOGFmYjlkYjYzOGZlMjk4MGExODliMDIyZjFjOTFkNjkxMTAxODc3Njg0MzU2NmJkYzZkMzE5OGYxNGNhZTY1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJBUGJ0WHh1K21xM0xMLzFQdGJRNXc9PSIsInZhbHVlIjoiNGhXdmtiRnhlT3FpOUkrQUtGL213L2dTOEN6ZkxxQytBeWhsVmlYT0FxZUcxRkxCQjFMMGRsbnpSMEtDMUNSSjJ3eHZ4RHpiMTdwbmRMOG1FbFd6RnFUQlB6TEJwSm84VzJqb2QrdmpMVXp5c0M3UzJYc1hWZlNvRFYzRk5LSEg0TnhldGRqTHRYT0NScG15Q1VRQXBNM3lQU2EwN3NQK2N6MGkyUitrYkxZenV4c0p0Ri9IaVlMTWdXdFpYL25keWN5NHBNdWRJRVl4SURmTVlScXlMcERxZVJ2bEs4N1d0dHlEWXc2ZkpnVTY2UFMraHR4a0Y2endXMkxkbzhObU5MVndsYmIxY2FoZWlCRkVWeDBRc3RZMVR5cHcwY3lNUDB5Nmc1dTdhVjNFT083UEJwS3R2UGRkLzBCaW5OZEU3NXp1YVhoaEpOK1NXdlFsRlk1TGluUkU1SUZWQ0crVVJmdTYzcnk2V1ozT3NsaHlQd3IydjBsWVlGc2g1NUdFRytXcjV0cy9UdkgxQWc2cnVJRHM4Z0dZZnZvb0xkcUVnZXNiMVpJWTVkOVlwM05weEdJblBlbWFoOE1CNXZTM0VoTzJ4dFVxY2NsVFA3K0ZUeEc2dHlLVWhHcEhiZE1VNTVjTXpEMUpqL2E5US9DRWVGczEvUS8xMVE4Szlpb2wiLCJtYWMiOiJjMjQzMTZmMGYxNDkzZDhiMjdhNTQ5MDczZTNjMjJlMjA3ZWRhZTdmOWU1MGI0MmU2Njk0MjYyNzY2NDQ2OGIyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZDVUREdTJyOVpvUnZKK1pUN1RiWWc9PSIsInZhbHVlIjoiVFZrRS92STAyeWZvMVhJQnN4clZBbmF5Zk9leG12T01sWi9rVzd6VjhXemtPWjc3Z2ZuNU9FSlVlWjNaVXh6aE16cGxJVUN1ZVB5eHRxOCtVdkpjNkRycCtQL29mOHkwSHlWZkFZMnh4cFFNbGNsdEpyUUNidDRKaXR4U0Njc1FnL0FiSmVWcjdXSWdENERNMFdyaE5pVkVzaWl2SDhDOFhKWGxUNUMrRDJlWjJSWXlwaHdzek0rdk04TzVGVFg0NkpvN1VrQmQyczRZaU1tLytHOG1Ba2tKV0Yrb0hyZ1JoOTdnbW5YY1cxOHBmRXY0UmFXOEpEbEgrYXFVOUZlcDdJQUZiMGxiSDhEejc1VXp4d2ZpNmFxb0FNZ3B5SnN2VWhCTTg5d3FmUGpWbi9WTG8rUXdCV3FtdnlTeEtsV2dmTTVuVmJlWmpIOWNCMys0YUtxWlNuQ0RSUXBUdi9zVzBiRFQ1REwyRHl5U2NuTnJoS0lrMzlGUFdsS3BMWDdpT1NGcjNNRE5ZTUZFeWx2OTVjWXR6ZWJ3U01LV2tzOXpjY2pFbnluM1VQM2dYQjFyZ05UREdUNE1FeUh2eXkvUHNvNlFqNHU4WUhjb0ZpMkVQd3JRQTZxN1pJVHRzaktFeFFTYkJTcG5YZzBtb1NtUzhMTTZ1SXY4bHF4SmdPZkwiLCJtYWMiOiJhOGFmYjlkYjYzOGZlMjk4MGExODliMDIyZjFjOTFkNjkxMTAxODc3Njg0MzU2NmJkYzZkMzE5OGYxNGNhZTY1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}