{"__meta": {"id": "X87fe6bedadf941911d9969a02f1659fa", "datetime": "2025-06-27 02:25:17", "utime": **********.237325, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991116.816994, "end": **********.237338, "duration": 0.42034411430358887, "duration_str": "420ms", "measures": [{"label": "Booting", "start": 1750991116.816994, "relative_start": 0, "end": **********.143941, "relative_end": **********.143941, "duration": 0.3269469738006592, "duration_str": "327ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.14395, "relative_start": 0.32695603370666504, "end": **********.23734, "relative_end": 1.9073486328125e-06, "duration": 0.09338998794555664, "duration_str": "93.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48198040, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02563, "accumulated_duration_str": "25.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.174401, "duration": 0.02011, "duration_str": "20.11ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.463}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.203571, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.463, "width_percent": 1.873}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2184181, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 80.336, "width_percent": 3.121}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.220452, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.457, "width_percent": 1.912}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.225155, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 85.369, "width_percent": 9.559}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2295918, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 94.928, "width_percent": 5.072}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1267526873 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1267526873\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.223891, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-667699740 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-667699740\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1559343551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1559343551\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-36164769 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-36164769\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1143427476 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991028607%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjQrQnRBTjNxUDVTd1BjK0hpMU1rT3c9PSIsInZhbHVlIjoiZ1B1cldEREFUSkFpbVR3cWcxL1JtS25IV2RsVFJhZS85Tkx2ZlJMaDFvMFNvekU5NHNOc0x2ei9RL08rN2RoTFpvYTlsR21kaFBMTnhkK3JpM2xweDBVbWJRQ2VDUXhzU1BQSzl6STR6TWtJSUpPTml5TzRXZFI4ZkJDZTBxeDVyN1l1ajY2dWdHMmQzdlVmMm40Y1NRK1pyaTVyYkltTWJvMDV6Y0h4aGM5WnM1bERhZlMycnNRUjU5M0JqdEdBc1FLREhXMVB1eVhvaUd6ZGVCSVlyR1V5aU9QejdVQjVIY3hha3k1alBoU0I4VldXalphOXdMcWM0UWg5MGI2YWNHSDB0ajZ2QkREaG95eWFadFpsWGxEN1RTUmZRb3JoeS9OUFUzU29FYUZ5ZXVwMlRrYVQwQURQRDVyUFVncU9WL0RmRE4vTWpYMVc2RXk2U1Q1ZlUxQWU2VmFid2hQNllMM1RVbFJMUE1FdHByTCt0R0gzVWdwckQ1WCs0ZWFLWFpka2dRSjR4N3VsSVgyWG9ZQmxFSWxDTlFBMC9rN1N0dlJMcS82YWNrUkhxcHI5Rys4R2F4KzI0Y1M4a3RKWjYwVTBnODFCRGx2NnE4aUdnL2tOamtkRXJKSnRUclcrZExYU29oeXFUOEhybUJUY0ZQU1I1Q25rZEV5R1BRZ2oiLCJtYWMiOiIxMTI3NTE1NjI3MGQ1Yjc0NGQ5YjQxZGM1ZDU2MzdjYjQ0NDI2OWQ3MTBkZGYwNmMwNDIyNmJjZWJlOGQ5ZjdiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZxVEI1cWI0Y1J4TCtBZGcybHBpVHc9PSIsInZhbHVlIjoibkxzeHFWY2tUbDlOSm9hMnc5MW0yRmlabUlxL3M3Y3ByT2hvdTFBVDVjOTJTZk5wa25mM1pqeEJTeTJ0YUZVM1BRL1NSb0xQTkcyUktvSFM3ZVlvYXpVT3gzU0x0eHQ1NkIzMVFDZnRTdGhCdkE2QTdpcTV5dmF1WXdhSjZHZEVpZWlOOWdvaUVTMWsxTlJ1dkp2NE83NTFha3U1UzNiR2hPc25zSk5XZWJ4SDlCdkFRWHBZZXpxWXplSUxPYnlZekh4YjRrZlJNeWR0UzdtaU5hbVVDUk5ncGQrU3h0YjRoRC9jZ2dKRm9TMHdqR2VMbGJLUXZmVENjYXY0Q1RWYXVXNi9oVXMxNXB4WENEVEtGbUIvOFZrbjNZOXMyNjFWQ09lcWYvQ01tWUJ3QzRCKzMvelFIVXJobWVLR2hTSmxoR09Sa0sxODY2eEQyWUF2L2NQM2ZLdkowYldFYVpHdjR4QUJCVHpqbTByclRHa2hOckZPejc5Mm56N3pJbkZ6K1MvUnVLSzNiU0x2M2FkdUxGWWRwdXRUVWJUN0VHZ3RZdXpWNE5DeExvbmJQKzR3cUlqTDhqV1NUMGNpR3dleGlSamlpeFdEQzFIczdQZWhISUdWcjRDYnRoWkk0Uit3ZGlRMEFPVXdUbEN5Tm5FVWlvK1haR3dhd2FsQTNTTEciLCJtYWMiOiIxNDkxZmRhN2YxZGViNDIxZWUyNDljMTMzNDc5ZTE2NTNmZWU1NzlhYzllYzg1N2FlZTUxYWVlMjE3ZDEzN2E2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143427476\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-736593066 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-736593066\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1857815111 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:17 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdQUktPQ2dxMWdKdEZPVmh6blZ0UVE9PSIsInZhbHVlIjoiUStxT2tXYzhWZ3c4cGsyRkpLZ200eVY5a3ZpV2Q5UjNlQTlmaFR1b2ZYbkUxNlJ2czJuV2lmeHF6eGpCNmdVRmtnMmQ4c2tqczNzcEYvdXBLSnVRTi9OaFNrbDZ2cEZxbkhHN3FoOWZtc05nOGZ5ZzBFdjR0SGtiVmZiRnR1Z2E5N2FKWHFuTW5tNnBnUWRmQXdiQ3BoTWYxUGQyUjRJeElXQmFsZVZxZW1jN3V1cU50NFU2cENkY0pJazhoYTlCd09GUGJHWVJ1c2hiWFdLbFdJWUlXUU5zendIaFJ4U2NCZFMyR3BvQXpYaFZjVEt1Z2VpNzdjSXhXNWE3M1luNiszNkZ4alBMS21qczdhOEYwdGdrNU5abnRVQ01seUlVL2xWa05qL3B6bHVyNUkyalI3cFZnYkFycmdxRXdlbit5VVdxTldTY0x1SmJVQVNud0ljZ0NyUkthM1RBZ0pQTk5PTUU0YzA2bms1d1NWZWQ0WjZQUlc1aFM2Vnl6ZjQwbnhtV3hpWkUvcDFxQkkrUzluZWpxYTQyNnhYL0FGVDkvK3ptelA0SENuU052UXlNdkt2K1hxdDdhb1RzNVIrazA3Q21KME9jSGJGTjJTczI1dnQ5bVMyTVZzMWJQdlRrQlhIYmNkY2VLdGxkaWVaVE9JSSt0Q0RzNjhQQjBZQWMiLCJtYWMiOiIyNDExNTk1ZTVhMDNmZGQ1MmMxZGZjYTM4NWZlNzUzYzQ2MTg2OTE2ZjQ0MzI3Y2VhY2RjNTY5MmYwOTUzNTU2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:17 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImU0ZDdBTXlYQ1ZrOXZnbW9PUU5CZXc9PSIsInZhbHVlIjoickIrMkNvbzBPb3U4c2VXKzA2YjlXbGlqc0VrNURLaU1UMDNkZ25Cemo2TWEvZ09pZThXM09QY1RkTGEwNTR0N2p1K2xuSkdpdHBaSUM1RWZWZ2thWTlsSjBPbUQ5Yk1JcEFZZy95T3ZPNU1rNzBrdWZOazkweFU4Z0xiWm9tZDlLVHE1YjkydjU3eTV6WEcwby9obVliYjh4aUd0L2dIZlRKU1lhdzgxbVp5WVhQdndRSWJQeGc0RWY2dWsvMmNBcGhsdmhRZmdtOHFwYjlVWjVxS0huVTBMeGZzQWJqNDFFUXRtRjNnWE9RdFdVc00vY2ltL213MTR1SThET3RraVlFZUd3Y0JMWnUzL0k1cUtpL0Zna3BqUTNLN0JYd2dmUThCK0xzT2lxZ2FCNzAxSU9KYmVmellTckprcHhyUWY3ZmlraWhlTTFLV0E1UktxZlhnYVlZdGhhSHZBUGlLQTdnZk43WVY5VTViRzJQeEY3bXRBdFFVbHpSSGloMndkRmE0cS9RdzZXQ244NGVkRFhGeXUyczQ1VmU4YlpaY211d2JJQ1kxbUxJZnIwOVZEc3NxU2RCVDdubHoxbDZHV0N3R2V3Q1hOc0NNMmMzUG5ZU0F6QW4zT3Y3K0h5TUpYS04xeTJ5U3hUcUlkKzRKWEx3amsxU2dFNTI4SU1yQ3YiLCJtYWMiOiIyMTM5ODg3NzY3ZTkzZDk2YWJhODVjYzFjYjhkZGRiYTE0NmQ4MTBmY2NkODhjOGRkNmU2NDI0ZGZmZTIzNDIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:17 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdQUktPQ2dxMWdKdEZPVmh6blZ0UVE9PSIsInZhbHVlIjoiUStxT2tXYzhWZ3c4cGsyRkpLZ200eVY5a3ZpV2Q5UjNlQTlmaFR1b2ZYbkUxNlJ2czJuV2lmeHF6eGpCNmdVRmtnMmQ4c2tqczNzcEYvdXBLSnVRTi9OaFNrbDZ2cEZxbkhHN3FoOWZtc05nOGZ5ZzBFdjR0SGtiVmZiRnR1Z2E5N2FKWHFuTW5tNnBnUWRmQXdiQ3BoTWYxUGQyUjRJeElXQmFsZVZxZW1jN3V1cU50NFU2cENkY0pJazhoYTlCd09GUGJHWVJ1c2hiWFdLbFdJWUlXUU5zendIaFJ4U2NCZFMyR3BvQXpYaFZjVEt1Z2VpNzdjSXhXNWE3M1luNiszNkZ4alBMS21qczdhOEYwdGdrNU5abnRVQ01seUlVL2xWa05qL3B6bHVyNUkyalI3cFZnYkFycmdxRXdlbit5VVdxTldTY0x1SmJVQVNud0ljZ0NyUkthM1RBZ0pQTk5PTUU0YzA2bms1d1NWZWQ0WjZQUlc1aFM2Vnl6ZjQwbnhtV3hpWkUvcDFxQkkrUzluZWpxYTQyNnhYL0FGVDkvK3ptelA0SENuU052UXlNdkt2K1hxdDdhb1RzNVIrazA3Q21KME9jSGJGTjJTczI1dnQ5bVMyTVZzMWJQdlRrQlhIYmNkY2VLdGxkaWVaVE9JSSt0Q0RzNjhQQjBZQWMiLCJtYWMiOiIyNDExNTk1ZTVhMDNmZGQ1MmMxZGZjYTM4NWZlNzUzYzQ2MTg2OTE2ZjQ0MzI3Y2VhY2RjNTY5MmYwOTUzNTU2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:17 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImU0ZDdBTXlYQ1ZrOXZnbW9PUU5CZXc9PSIsInZhbHVlIjoickIrMkNvbzBPb3U4c2VXKzA2YjlXbGlqc0VrNURLaU1UMDNkZ25Cemo2TWEvZ09pZThXM09QY1RkTGEwNTR0N2p1K2xuSkdpdHBaSUM1RWZWZ2thWTlsSjBPbUQ5Yk1JcEFZZy95T3ZPNU1rNzBrdWZOazkweFU4Z0xiWm9tZDlLVHE1YjkydjU3eTV6WEcwby9obVliYjh4aUd0L2dIZlRKU1lhdzgxbVp5WVhQdndRSWJQeGc0RWY2dWsvMmNBcGhsdmhRZmdtOHFwYjlVWjVxS0huVTBMeGZzQWJqNDFFUXRtRjNnWE9RdFdVc00vY2ltL213MTR1SThET3RraVlFZUd3Y0JMWnUzL0k1cUtpL0Zna3BqUTNLN0JYd2dmUThCK0xzT2lxZ2FCNzAxSU9KYmVmellTckprcHhyUWY3ZmlraWhlTTFLV0E1UktxZlhnYVlZdGhhSHZBUGlLQTdnZk43WVY5VTViRzJQeEY3bXRBdFFVbHpSSGloMndkRmE0cS9RdzZXQ244NGVkRFhGeXUyczQ1VmU4YlpaY211d2JJQ1kxbUxJZnIwOVZEc3NxU2RCVDdubHoxbDZHV0N3R2V3Q1hOc0NNMmMzUG5ZU0F6QW4zT3Y3K0h5TUpYS04xeTJ5U3hUcUlkKzRKWEx3amsxU2dFNTI4SU1yQ3YiLCJtYWMiOiIyMTM5ODg3NzY3ZTkzZDk2YWJhODVjYzFjYjhkZGRiYTE0NmQ4MTBmY2NkODhjOGRkNmU2NDI0ZGZmZTIzNDIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:17 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1857815111\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-880558127 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-880558127\", {\"maxDepth\":0})</script>\n"}}