{"__meta": {"id": "Xb41cc45eb4fe7d0b04a940a510908f77", "datetime": "2025-06-27 02:12:42", "utime": **********.980565, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.487947, "end": **********.980581, "duration": 0.4926340579986572, "duration_str": "493ms", "measures": [{"label": "Booting", "start": **********.487947, "relative_start": 0, "end": **********.8325, "relative_end": **********.8325, "duration": 0.34455299377441406, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.832509, "relative_start": 0.3445620536804199, "end": **********.980582, "relative_end": 9.5367431640625e-07, "duration": 0.1480729579925537, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48197696, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.08098000000000001, "accumulated_duration_str": "80.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.863847, "duration": 0.01349, "duration_str": "13.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 16.658}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8853471, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 16.658, "width_percent": 0.63}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.89909, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 17.288, "width_percent": 0.901}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.901006, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 18.19, "width_percent": 0.494}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.905103, "duration": 0.*****************, "duration_str": "64.37ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 18.684, "width_percent": 79.489}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.9720478, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 98.172, "width_percent": 1.828}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1368065184 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1368065184\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.904146, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-261186735 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-261186735\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1863804635 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1863804635\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-254996020 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-254996020\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-969719153 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990356916%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1BNzdpODlXTW1CVkJzNkdHYWFmSnc9PSIsInZhbHVlIjoiL1FqYkZTaU9aSThJTWhzaWFIVzlHOHNSWmRLdXZVb3IzWHlSZWl6bzlteXVFL2R2bjdad1E2TjlpVVMrVW9mTW5ma1B1aUpCUERtZXl1ZCtmSy9WNytLTzFoYWdicjBPRkxOY3kyZCtSckRncjY2SHliV0piWlVXc2trUkUwRUhvcFJHanNIN3BMdkhKdzArL0tQNDh4VnV5NVhKYnorU29vTDlOVEFDaFBZZ1NIS2w1aFp6NlBRQ0lFdjF3dnR6djNORm5SYWFFUzhGZVRNZHl1NmYxa1NmTnJieGpwT0NteHAvM1lGb005SlB2ci9yY3p0Tlk3UWh1K3lsR3JNTHpLN0tjMWQ3K1dpM3JZOE91RWt6ejJyK21MaEEyaUZZU284UlN1SWRQeWlXYXhocXhuRGw1VUozQ01sckNkNTgyQTV0d3B3Zy82VDNxZHp6b0RnZkNoNWxNdjkwN3BacVU3ZU1mUXR4Z2d2SUQ1ZmpjMWx2NWFZb3BlUHgrbHQ2UTJ3cUZvbDlGNDNEQVV6d3F1WmVkWEpwYnlSYnI0UmNHeFJvTXV2cEFEbkdFdlJWMjN3bXFSekp0UmMwN2VhSVNDeGY4VWZVenZHOS9iai9CSTBwR0hBdnE0OTZVSytLNUpjSXNEa0pYZGdUN0lZMk9ZOStJd0Vvb3RxeU12VnQiLCJtYWMiOiJjMzcyODE4MzdlZjM5ZTQ0ZDI2ODI3ZDZmYmVjMDFkOWI0YTc1YmE4Y2E2YmU4YjExM2QxOTEzMWU4YWYzZThmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNBL1hPYVNRQ05GWUdVZUE3WnpFRlE9PSIsInZhbHVlIjoiWVd5OFBGQzJxd1hOMURiQ0pGenkvZnBudk5jQVNEa3B6NmxJdCtGd3JQQWFFY1Z1dWp0V3ZZczJ2cjJFaGFYVGxWblZaa0FYOE5UUUR2NzVnZklXOEZoQ3lVVGROSEF2elVFYXNHZXN5YWxrUzVjZkNVQjNUcFozZ2plU0RKSVRpbGkxOVR0OGYrQy9IWFQ1MEMvMUFLN0V6QVdsNFN3cUtBVFRLQ3g1NUFBdDNSNTdxVGlFVHU0ckhqekw0NkUvL05pdHFyMEdoWUpWT3VwaENxQ1k3Q21JMHBLM1IrU1p3T1FvSnBHSVR0Q2NuV2ZjNmp3RnlHSkkvQmNqWDlHclQxQzBBUzh5K0x2bkQ3djlTRzBTc0xObVdORjZzZ0tycHdQaXZ3a3F5WFVYbDhLOVNoeWVEYmVRR2dxMWI1UmxYaGFURmw2UlYxTHdXRkFydmVENXpWM1ZFc0ZNZ2tlZ0NVTjR6RGpNZkNhRkQ5Wll0ZEdOb2RGY0RNQ01kNlp2eFNkcU52L1J2MlIzYVFYR3htN2RybExnWlV0VmVIM0FDVi9YNFl0V2FjeGlXbVhjRExNTnplYUxmWE1GYnVEODEwYTZqZG1RWmZSVzNkRDA2RUhSWHZEdmFoWXBsUlphWnBoUzlXaExjaHhEMU82eUY4Y0prdzJZM1d3c2NLWnkiLCJtYWMiOiJmYjRjZDY0N2EwYWQ1MDFiZjM4MzIxOGZmOTFhMDg5M2M2MWQyZGU5NGFhZDAyMjMxYjZjYjU0YTkzYzRlMTNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-969719153\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1240650692 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240650692\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-277368142 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpuMUExNzNiMGRQM2dCUTB4SVBQeUE9PSIsInZhbHVlIjoiTWNuL1gzL1Q3M05PU1NwZk84aXA1ZE1zbUF5dG5zTi9ESGR5QkFxUlRQdDQ5T3kzSnY0NVBiUmpyZmNUQVJZQm50N0FqMHNOMU51SWlXZFhrMXMzTlJzU1JvdUdnTkNYanpoU3RCZUsvYVVCYVJIRXhuYkNINHNoTm5YRHQveEhjOHZkeHlVbnRSNlhpbkRSQjFqdVR6RzJoYmduNmxpc0ZsVmtFS3VKOWFPYzRkdXFBZVQ4L1MwM1lCb1hqRzNyUzFabnJjVEFyZVRJZGFRdWRqeXNkY0ZiZDZhSnozUE5qa0NySTk0UlZxSk5WTG85TytKamkwbG9vUURNWjkxQTg4TTBiU1lTM01EOHVKV0dNWXBGMnVObDNiQTE1VjdZZ3FuaTdOekV4NjR2TGRneFRLRzJGejd3M0lSa2FjbmROVGRWSU5Zejlnbkt1dHNTeTltOVlVZXljK0xMMSt4cHhEaW5JQTFNZEJ2NHJDR1VtTzFNMStFSGhoNXorZURzcStUSyttU3pRWXY5Mi9EZFBRVEh0cks5NG1YdDFLSFFrS2s3OGJXQXhtQVFxK2VudTg3N3lYU0RUMGk1SUpPbkErNDl6N0NpNzJZeWRnYWRVdVVwTEZBSE9ZME1KZE1IeHlWdk1adVN6bjNmd2thZnBWMlRVNnlaUGFiOTJaRkIiLCJtYWMiOiI3ZTE2MDY2MDE0MzI2MTBiYzUzMThhYWZkM2E4OTRkYmQ5YTBhYTlmNTBhNGYyZWVhZDUwOWEwYzcwMjViYzNkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdDVXV4TEpEdURjdmcyTUtqWXBwWWc9PSIsInZhbHVlIjoiKytXMUlqVGM5ekNaU1lpMy9Bd0dzb0o2RTBZTzNmYVNvT1BOaXZwSy9rTWZSakM5d09HWmk2TUtpejR2bjZPUThBcXQzNHN6YVQwbWxqMVFKQnRxWjViNHRsTHZoRXVvRFFWenJ2NndzRDRCVzRGZ1liaWpzZ0hmbnBhQjQ1NnROdDZjM0VtM1JYWENsRFRvQmYyWDN4QUdIc0tEWXBET0VGSlNmQlBCUTEwK3FUQlhVQ05wMzdFZW41K3N3WGVnWGdEVmZnalFhRkpjSjN0blppeHRYT0ZkdUFKaGRUcWduZk9qL3ZEY0VlbmFKVDNLVGRPTEZ0ZWZLRGxQRUczdDlPcld2Z0dzM1RzYzExenRCRUVDbHdpc0QwZWdlTUJoT095bVpUeFQxY2xxOTE3ZkhsMTVzSUVRc1M4dytiNWJCOHB5YnlLWXhtZ20weGJ0Yy9yUDl2TlZpMlptMWpncmZQS2IyY2ZNazlEajRJUVQ0RExKZEhNbXRUSDJMWnVBQ0FZSnNjbVVYWldrczhsdWlEUVhWM0xDVExPVTBocGFQaE9rWmZ5TllMcXZsTnVvMGlXYkVCMlNVb2J3ci9TYXFsangzYUZhOHo4bHA2N2s4N2JjZW9oSS9qakwwWkx6V0FZczhublhpM1VUSDBBaDRPejIwc1V6bDlGczB0MHYiLCJtYWMiOiIxMDUwYWMzZjM4ODc3YThlMmVlMzg3YzFiYjE0NmFkYjUyYmIzMjdmNTEzMDIyMmM1ZGY4OGYyMTI0ZTJiZDBmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpuMUExNzNiMGRQM2dCUTB4SVBQeUE9PSIsInZhbHVlIjoiTWNuL1gzL1Q3M05PU1NwZk84aXA1ZE1zbUF5dG5zTi9ESGR5QkFxUlRQdDQ5T3kzSnY0NVBiUmpyZmNUQVJZQm50N0FqMHNOMU51SWlXZFhrMXMzTlJzU1JvdUdnTkNYanpoU3RCZUsvYVVCYVJIRXhuYkNINHNoTm5YRHQveEhjOHZkeHlVbnRSNlhpbkRSQjFqdVR6RzJoYmduNmxpc0ZsVmtFS3VKOWFPYzRkdXFBZVQ4L1MwM1lCb1hqRzNyUzFabnJjVEFyZVRJZGFRdWRqeXNkY0ZiZDZhSnozUE5qa0NySTk0UlZxSk5WTG85TytKamkwbG9vUURNWjkxQTg4TTBiU1lTM01EOHVKV0dNWXBGMnVObDNiQTE1VjdZZ3FuaTdOekV4NjR2TGRneFRLRzJGejd3M0lSa2FjbmROVGRWSU5Zejlnbkt1dHNTeTltOVlVZXljK0xMMSt4cHhEaW5JQTFNZEJ2NHJDR1VtTzFNMStFSGhoNXorZURzcStUSyttU3pRWXY5Mi9EZFBRVEh0cks5NG1YdDFLSFFrS2s3OGJXQXhtQVFxK2VudTg3N3lYU0RUMGk1SUpPbkErNDl6N0NpNzJZeWRnYWRVdVVwTEZBSE9ZME1KZE1IeHlWdk1adVN6bjNmd2thZnBWMlRVNnlaUGFiOTJaRkIiLCJtYWMiOiI3ZTE2MDY2MDE0MzI2MTBiYzUzMThhYWZkM2E4OTRkYmQ5YTBhYTlmNTBhNGYyZWVhZDUwOWEwYzcwMjViYzNkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdDVXV4TEpEdURjdmcyTUtqWXBwWWc9PSIsInZhbHVlIjoiKytXMUlqVGM5ekNaU1lpMy9Bd0dzb0o2RTBZTzNmYVNvT1BOaXZwSy9rTWZSakM5d09HWmk2TUtpejR2bjZPUThBcXQzNHN6YVQwbWxqMVFKQnRxWjViNHRsTHZoRXVvRFFWenJ2NndzRDRCVzRGZ1liaWpzZ0hmbnBhQjQ1NnROdDZjM0VtM1JYWENsRFRvQmYyWDN4QUdIc0tEWXBET0VGSlNmQlBCUTEwK3FUQlhVQ05wMzdFZW41K3N3WGVnWGdEVmZnalFhRkpjSjN0blppeHRYT0ZkdUFKaGRUcWduZk9qL3ZEY0VlbmFKVDNLVGRPTEZ0ZWZLRGxQRUczdDlPcld2Z0dzM1RzYzExenRCRUVDbHdpc0QwZWdlTUJoT095bVpUeFQxY2xxOTE3ZkhsMTVzSUVRc1M4dytiNWJCOHB5YnlLWXhtZ20weGJ0Yy9yUDl2TlZpMlptMWpncmZQS2IyY2ZNazlEajRJUVQ0RExKZEhNbXRUSDJMWnVBQ0FZSnNjbVVYWldrczhsdWlEUVhWM0xDVExPVTBocGFQaE9rWmZ5TllMcXZsTnVvMGlXYkVCMlNVb2J3ci9TYXFsangzYUZhOHo4bHA2N2s4N2JjZW9oSS9qakwwWkx6V0FZczhublhpM1VUSDBBaDRPejIwc1V6bDlGczB0MHYiLCJtYWMiOiIxMDUwYWMzZjM4ODc3YThlMmVlMzg3YzFiYjE0NmFkYjUyYmIzMjdmNTEzMDIyMmM1ZGY4OGYyMTI0ZTJiZDBmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-277368142\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-271945549 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271945549\", {\"maxDepth\":0})</script>\n"}}