{"__meta": {"id": "Xd07ac463b7f8c87b521d716946cbbba4", "datetime": "2025-06-27 02:26:08", "utime": **********.338514, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991167.917108, "end": **********.33853, "duration": 0.42142200469970703, "duration_str": "421ms", "measures": [{"label": "Booting", "start": 1750991167.917108, "relative_start": 0, "end": **********.262839, "relative_end": **********.262839, "duration": 0.3457310199737549, "duration_str": "346ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.262847, "relative_start": 0.34573888778686523, "end": **********.338532, "relative_end": 1.9073486328125e-06, "duration": 0.07568502426147461, "duration_str": "75.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45737176, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.026959999999999998, "accumulated_duration_str": "26.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.289401, "duration": 0.02596, "duration_str": "25.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.291}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3240302, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.291, "width_percent": 2.3}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3299959, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.591, "width_percent": 1.409}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/21\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1379268821 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1379268821\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1287389710 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1287389710\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1249478913 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1249478913\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1155295821 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991165773%7C31%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRvTDlrRVo3djl3M3dLa3BmMzFTd2c9PSIsInZhbHVlIjoiQ1RWc3ozNS83U3k0ZHBFSnhoY0EreXUrejY4ZlJuakczWjg4aU1GK1ByUDNkS2h4WHRoeDVQMzFiZm5NTlR5OHVhTXFSUUUxY1BkanVGV3RqZExFRXZ3NUYxQ1hyZ01HTWV0dkZjR2JBVXlvcnNVRUR1dU1zVVZsTitqYnFHeks5U1dVUEI5VUxuZjRoNUFKa0VaTW1tckIyVjA1T2pUTEhCTDlxY2NSN2YzZnNlTnJVVzVPYm0vRGFhcGVzNFhxMHZMRHcwUXBNcFFXWjJjUlVNWDFGZ05zOGkxU1JwUDM3RGlmVlNHcjVaak82SVprZzBUVDBsRU5QYlNVWDN0Z3hiOWtQeVNYdml2WmFvZUFMWVhRZnowVXBWNDcySzhQdW9qN1QxWk0ycFB3VlJBTlF5VmlCZnRTNFhQM1l4RXlQSDNrS1ExdWoxUThVb0tIdHNLcWdvYW0xK2RHbklxYytQakwxRlU5RVVJSGJ1U2pDc0ZpdDIxenZrZ3Y4RkFobmZvTTQrTWhKclk3UXhucjRGeGpEMjRVQUFxRldpKzFoVlRKSTVPekIyT1NSOXpUeEFoMUpmT3RiSWNOaW44ckxYaDduUy9vRVNhV3NCem5SRFd3ZE1jZUdDd3B5RjJNcld3VVBiYVhnTmhyelpEbHNhZW0wUHlVdzJwb1ZmWWUiLCJtYWMiOiJjODJhMTMwNDlhNTA2NjhhYjEyMGEwY2JiYTIwNmJjNjBiNWVlMDM4OTVkYzkzNTU4MzliZjU5YmZjOTZlY2IzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1GUEp3cExLVENZS2ozK3ppV2RsTFE9PSIsInZhbHVlIjoic2ZBcFhSakc5emFiYWJ3RE9xSllDeUpLaDZVSkVvdHB0OHR5SS9wNENpK3UvbXovOW91K3NWVTkrNkl6VTFxdHVzWHZuS1YzQmljV2pITnNNTTNlZkJTUGc4dDJBdUQrRms0WkRmSENlWmIvK0VLTlNsNWlCNDdDSWppb3lvMXdJdHJMK1diQm9RQ282dUM4SzlzRHFKQkd3ZU5SOVRoelltUzNPL2RCODU1YW1ONU5VWW9TZmo2VENuQWVYRTBpcEdodFZyYkxsTjZYKzFqYmo1YTNCeFllRzNOVEQ2REZYWWRZamZxS0VoYzZPM1I1YUcxMUhtWkcrSmJJWHJZajF6dVlOQ2RaZ3dQbEgzaFBvUFZZOHoxbHViaTdMalRqbjVpaEcrbjcyNjd4dFhFMjhMdG0rWTVKdjc2OUJJUlRDNUlkVFpPbnFHV0lvRW1GcHp2Q09vNEthVFRSanVyYWt2UWZUY3BIbEtXdUNNTkFwanBGUFV5VFpyaDdsM1dDTkZKbkVjUDVpaW1YMG9mNlZwZWg3dDV0N1pEMHgvZmJ1cmd6QmtVbFZpY0pPcytyQXRmMkVxVkRvT2p3ajc0Q3lTeGpiaW5zTW1Yd3AzMlZuSDMyUUJMOXhpTmZ2MUZObHV4ZjNFSmRiTi9ac1lwZC9yU00rTGxLMUs4R2ZqYXEiLCJtYWMiOiI1Y2MwM2I4MDcyOWYzNTBkZWJhNmFmMTg0ZjU5ZGFkNGE5MjcxNWZhNGZmNmVlMjc3OTg4ZGFiYWY0ZTQ0ZWFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155295821\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1142503757 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1142503757\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-382003003 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:26:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZZS0tOajcweFZSc0hTWTFXWFZhS1E9PSIsInZhbHVlIjoiak5ZMTByQ0JwV1N2TVdYRmxpVTNPS3hrclNnaGtSS2h3bExUd0F5ZHRlb2lPTGhtT3Z6MWo0K1M2NVVaUWVYSXltWjNsOFI3SDIyQnN0aDJhS2NTS3Q5REhHSmlIYnA2UE9ZelhGQUh2UXRvNzhEbUZQZ3c3dTlRdUZ3c1h2Wk9qRHdFeEdUWmJzZ1BHL2h0WGlPTDN2eDJnNVFENlNiSUJGbVpUbC9GMjVwbWdwWFVxT20vM1dvQy9rcEY4ZUJQRWQyajY1ak4vVkkwWW8xSVN5SEZWaFdQWWFBYm5jeDRGRGN3MlBKdHNXc1pCOWg4dDhWUFcxYklIL0tzWkNBb2QvSDNiVnJuL2FaTEI5U3IvUjYzdEJtQkkrbDhnazl3bGM5MHVCNE5ESHBGekJ2R2NhQVNseGtpOUZlWnRUVFA4Q1E4NXlSZHVXalFpRHhHREdNWmRwak93R2NiZXVRS0JKWmxzQ2lwT210NlZ1TmtRY09VTmdpaHl5QXNNTWJSTU9jL0VIeXVITTI1ZGpLRHdocTZOVVp5VEFDMVpHOHc2QmxXdVlWRlZ4S09UTm5DOXhBcFcwRlJheFVSRWNBR1kvcFdsRktzck5MLzZVK0ZMeHZOR3RLVW8xRGRlR0FSSk9TWFRLRmRuRUFMZXljMEJFTWFTYis5U21kRkxNT3QiLCJtYWMiOiJkMTU2YjEyZmU5ZjhkMWI3OTEwZjQ5ZjFlYTdmYTRkZDg5MDU5MDdhMzg2ZWU3ODk3MTM5MzIxNzUzZTUyOWQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik92NnpBM0czbXNvYkFwV3QrTjFwZUE9PSIsInZhbHVlIjoiUUNNelkyaVM5TWpucGdrNzZsK0MxeWZIOEZXdG9DNDJkbWhCSTdYc1Uya3p2dS81VERqekNQdnBtbDNwTlpLSE9tWXJiQnBhNVpoNXpEYWRJYmRZUk5XNjRlVGdxWkZ2VnQxZVgvcGlsN2FMNW9wV3d3RkFlWTVncFdKMkVoc1hsNEIyY1kxSU5vcC9VMlBIb1RUWUJCbyszaTRBU0JjSzByZzJZWHlNdjRjaU5LUFhyRHFKVGYvVEZHYnBFWVA1c0pBSEZEdU5xZURvaWMwdnQ0RFFlSDBWeGlkQ0NFMzhSS0NIZXBMNDZnMDBWOWQwU1dqeDR2UlQzRmgvb1N6eVdHaHNrUGNpMW1EU09PVVJZOWdpRVpTNW9sSDRzSWV5WHRRMVJoSlVDT1VxWEZKWEhQZGlwWkdXa1U5ZGN4eU8xSGZtdzhSY0Z2dzRpUEVLcWV5eDcwMnFHdnNxRGswZlJRUHA5Y3BsMXczZTFVekNLeGdWWUpxWHQzSFMxS0Frb2JlczF3OTJ5OHhrd2svL05EWVhRRExkV1JtY3NveGI0bHV4VnVVQ3lib3BaSmQyeWdySmFOcHVOQys0N2ZtdFlQRWxJU2c2ZGtLQkJERUdOYUhKVlNoeS9aMDArcC9KeWRkMHNna05tL2hJNjZSajJVVGpNUVlML0dHLzlBb24iLCJtYWMiOiI3MDEzMjBmNDExOWRlOWRlMjFlYjk0ZGRhZjM2NzNlNjkzZDE2MTM3ZTBhYmNkNmI2OTA0NDU4Njk5YzZiZjY0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZZS0tOajcweFZSc0hTWTFXWFZhS1E9PSIsInZhbHVlIjoiak5ZMTByQ0JwV1N2TVdYRmxpVTNPS3hrclNnaGtSS2h3bExUd0F5ZHRlb2lPTGhtT3Z6MWo0K1M2NVVaUWVYSXltWjNsOFI3SDIyQnN0aDJhS2NTS3Q5REhHSmlIYnA2UE9ZelhGQUh2UXRvNzhEbUZQZ3c3dTlRdUZ3c1h2Wk9qRHdFeEdUWmJzZ1BHL2h0WGlPTDN2eDJnNVFENlNiSUJGbVpUbC9GMjVwbWdwWFVxT20vM1dvQy9rcEY4ZUJQRWQyajY1ak4vVkkwWW8xSVN5SEZWaFdQWWFBYm5jeDRGRGN3MlBKdHNXc1pCOWg4dDhWUFcxYklIL0tzWkNBb2QvSDNiVnJuL2FaTEI5U3IvUjYzdEJtQkkrbDhnazl3bGM5MHVCNE5ESHBGekJ2R2NhQVNseGtpOUZlWnRUVFA4Q1E4NXlSZHVXalFpRHhHREdNWmRwak93R2NiZXVRS0JKWmxzQ2lwT210NlZ1TmtRY09VTmdpaHl5QXNNTWJSTU9jL0VIeXVITTI1ZGpLRHdocTZOVVp5VEFDMVpHOHc2QmxXdVlWRlZ4S09UTm5DOXhBcFcwRlJheFVSRWNBR1kvcFdsRktzck5MLzZVK0ZMeHZOR3RLVW8xRGRlR0FSSk9TWFRLRmRuRUFMZXljMEJFTWFTYis5U21kRkxNT3QiLCJtYWMiOiJkMTU2YjEyZmU5ZjhkMWI3OTEwZjQ5ZjFlYTdmYTRkZDg5MDU5MDdhMzg2ZWU3ODk3MTM5MzIxNzUzZTUyOWQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik92NnpBM0czbXNvYkFwV3QrTjFwZUE9PSIsInZhbHVlIjoiUUNNelkyaVM5TWpucGdrNzZsK0MxeWZIOEZXdG9DNDJkbWhCSTdYc1Uya3p2dS81VERqekNQdnBtbDNwTlpLSE9tWXJiQnBhNVpoNXpEYWRJYmRZUk5XNjRlVGdxWkZ2VnQxZVgvcGlsN2FMNW9wV3d3RkFlWTVncFdKMkVoc1hsNEIyY1kxSU5vcC9VMlBIb1RUWUJCbyszaTRBU0JjSzByZzJZWHlNdjRjaU5LUFhyRHFKVGYvVEZHYnBFWVA1c0pBSEZEdU5xZURvaWMwdnQ0RFFlSDBWeGlkQ0NFMzhSS0NIZXBMNDZnMDBWOWQwU1dqeDR2UlQzRmgvb1N6eVdHaHNrUGNpMW1EU09PVVJZOWdpRVpTNW9sSDRzSWV5WHRRMVJoSlVDT1VxWEZKWEhQZGlwWkdXa1U5ZGN4eU8xSGZtdzhSY0Z2dzRpUEVLcWV5eDcwMnFHdnNxRGswZlJRUHA5Y3BsMXczZTFVekNLeGdWWUpxWHQzSFMxS0Frb2JlczF3OTJ5OHhrd2svL05EWVhRRExkV1JtY3NveGI0bHV4VnVVQ3lib3BaSmQyeWdySmFOcHVOQys0N2ZtdFlQRWxJU2c2ZGtLQkJERUdOYUhKVlNoeS9aMDArcC9KeWRkMHNna05tL2hJNjZSajJVVGpNUVlML0dHLzlBb24iLCJtYWMiOiI3MDEzMjBmNDExOWRlOWRlMjFlYjk0ZGRhZjM2NzNlNjkzZDE2MTM3ZTBhYmNkNmI2OTA0NDU4Njk5YzZiZjY0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-382003003\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1997098960 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1997098960\", {\"maxDepth\":0})</script>\n"}}