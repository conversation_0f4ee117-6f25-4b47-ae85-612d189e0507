{"__meta": {"id": "X0173f1b456bad5e1f71c544d78f92872", "datetime": "2025-06-27 02:25:39", "utime": **********.611121, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.180411, "end": **********.61114, "duration": 0.4307289123535156, "duration_str": "431ms", "measures": [{"label": "Booting", "start": **********.180411, "relative_start": 0, "end": **********.546999, "relative_end": **********.546999, "duration": 0.36658787727355957, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.547009, "relative_start": 0.36659789085388184, "end": **********.611144, "relative_end": 4.0531158447265625e-06, "duration": 0.06413507461547852, "duration_str": "64.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45734992, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013230000000000002, "accumulated_duration_str": "13.23ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5735202, "duration": 0.012230000000000001, "duration_str": "12.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.441}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.59498, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.441, "width_percent": 3.704}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6010501, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.145, "width_percent": 3.855}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-123511408 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-123511408\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-916095659 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-916095659\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-226002155 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-226002155\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1811086488 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991135471%7C25%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldwVisvVEZRTU1BQ0tXREVGWmNLOWc9PSIsInZhbHVlIjoiZkVxelJEaUN6T3dOM1NyUWc0YXdHUmFUdzF4NWdTNmRvck5HK29wQk52Z1grT3BXcmpaZU5ETTBDWXl4dXFOS3NhWmFtWlI5clU5ZVhMTXNqbFNQMXBPMkF3U1NyRTZHcmVlVTR5NCtYUlBjRTdpZFZISjhUalhBWktsNHIyWGN6SVArWkJOOHZnNXlsbHQ0elpDeHpNeENBTG1ZMUFWSlQ0VUpsRitnc005RWxwVVlvSEF4MDg4Y2VNTWxWMzFqZWx5WEhFVXF3eDUvV2RlNFBURmQ4dE95R2pVcjNPQnhkdnEvdEpHbHkxWjkxVE9SYlg3UmV3cGdUTkJsbzlGRWRQZHhwbzNRakE0aVhwcnVxajhpZnMvQ2V1MUllOGdiTk5xeWpjQkxQQnRzRW9hRlFlY3pNaVpYM2QzbDFFc0s1eFJzaXhQRkZPdHBmbUZRazdRQnYwd1hXVGJMS0JUWVpXMHlUMlBReUMzYWRpd1c0eW9tU1VlZ1VVZTBBOGxaZ2pTVU1LNDEzUE9CQUZVWTkxY2JxNjlNWlhBMDJiK0kwK3BxZlk5Q2NTN2VlQ0hucU15TVNwTDRRNzhTc2QzL2ptckpqUVIyQUxJNjEzMzY0UDNFelpXZnZsZ2NxanJPZStWUGNWM3JpbUgrc21KTnUyVWdwbUVWSEFoS1FyUU0iLCJtYWMiOiJjMWYyYTRhOThkMzcyNzkzMjE5ZWQ1ZGJlODhjZTkwZGQ2NmYxNDUwYzQ4MTU3YmJhYjJmNTIyYWNjMmEwODk0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFsQTdKdmw1UUIzL1J1YmowbzQvNFE9PSIsInZhbHVlIjoiUHVwcjIxQ3VWRTRQMlhhZDd3SXhvMTg5MTlSSFhydk9icEljM2E1MSszQ1VCaDdzRFFrdUtreUU5VFBzMkJCNnZhK2xUVjFodmo0WjhWbUxZWU9TYkJ5WWxIdDRGVmErbER5WStPLzQ4bXhwRFZpK2swaUkxVUFRSlA5Qm9xRzg0YmlYdTA0RmEzQUtyZXR1WGpTL1dEQ0lzMzlmcnlwSExHRnlQdmFXQjJvQWRYSStCczYzYUZSNjEyYzFSVWxNdVVHbkM4T2o4cFpWT0x1RE1TcVExTDJvWUxuVVEyWllKeUtpMU5EYVEySXFHdVA5alZteWM2bzQ3YUR5NVVSdVk0SEJ0SzBTR0xDeDJ3c1FXcE54NnRXY3VmVTlXZmZjck9PWS91ZktHVlc0WVVKc2J2c1dYcDZ4TjVwcUN6U3N5QVNqcStheDdQaE5yQTQyYkgxemk0RTRsVDRqdFdqTkJzWE9ITkYzQ081OWljeGFHOWFJQ1ZDQjJyZ01uMkJQZHU4YUtGSlY1SmlRMDl3bHgvenN1azVrT2huRnNlZWFKeDdIWHNpWWFiVWxJREt0TDRnWjVuU0FicWF4RGJmS0FSemIySmhUY0hOWmRsa0NDVTA1bmpEUy9zb2xIeWIrTGhLRFBzV0xVcllEdWhnbjQ5amVVTjFPZmhDQmxGd3YiLCJtYWMiOiI1YjI3YzMyZDFmOWRhMzE3OTI2NWFjNWVlNzZkNTc2NGE2NzA1YjBiZWI4ZjgwYTI3NzhmNmM5MWJlNDc4ZjMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1811086488\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-588818160 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-588818160\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1778149851 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlF2NWY1aDkzZk5CUFBKaWtmVHlBN2c9PSIsInZhbHVlIjoiS28zUmJVL0kzTUJzU2N1ZENIQnNHSUpuL01KL3hHUWh2KzlEa1N4ejZCbURuaWNVbEUySUxXU29YS3NyTFRrVVJURElzNkEvRTltZWQxUE5tTlF0T01CRk01SExQNW4vQ0VYQUNsT0tWdGJ6VHFNZDZ0czYyaGdTM294azlsYXZvcHI3SWVhODFlYWUrYzVadGlJOWlpZ3ZrYkJ3VnNJSVdpcXNmTWNxMVl3am9yU1RNMlR3dFhoODFMZkVxM1BxQjF0U3VINW1rVElma2o3WHY2ZUZxd3pDcEpNZFF1S0FQTUhBVXhjak85OEFBMG5TYlkzVGNxby9tWXl5Mm1mK1FCbnIxcC9DWVNMZDdpSGpQengwTHZERmN0dHpnTmloMC9yZmtXSjlhSjVHdlJiSmN5SU9TK2ZYZHlYUHBiNmRQMmF3ZlBwRytKQ3Z2ZldVZTlGZzl0bTlzNHRDbEQyMUlaQ1RDWFlabCt3SGc1WHlFZDE5MzYwY3o4cUx0UTlHbzhaYWhzeEdPNm9tRUNoamtXaFljTzhENURvWUpwS24rdURNQkluYVBwVkZIMlJzRDcyZk5tQVRYck9oQ0xQWFBMeXdOeVllb3NZTW05UlRtZEVFNHBDVDhTSDFueU9xMThHRUtuR0ZKOUxjUk1MWmRYWjdxZzYxWllEb2FwZjMiLCJtYWMiOiI4NTVkMzk4NDdhYTY0ZTcyNTk1NWE0NjQ3YWI0YjY4ZmFkYmI1OTJlNzliODUwMzgwZTE0N2ViNGRhZDYzMGJiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InQwOUJNZ3VNbjVoZjJZRWdIWVVYc3c9PSIsInZhbHVlIjoiNlk1Q05JSkRZbnNHa3ZlQURJZHViRzErTkIyZWVGYzJBR1daM2hyc2l5Z1NUUUZ1NTN5NW1nRkZGUEFhNk5Yc3J1ZGYyMUtTa2ZvaUxIZnJVT2JoQ2VHMUJGNU03YzVjcG1pMkdIVEl1M2VsL3dUaEdFRHZvYkg4Yzk1SkttWVdWNWJhRU9lbVFFYmlaV0pnTlNQK0crdWx3MUpyNEVNNGRocXZXNDFmMVhqZXA4eHB1VlRDbDg0aWdqUGZjdlB3UFpCRy8xVlFEZzF1QnVxNTk4dDF1WkFjU05lekphQ2N5Nk9HaXQwVGZDaGtZYnlJbmhIYnJUTHlVMjhCOTdRRytBVWVWa2NtSXBjMzQ5bUtnTnQwOVA2QkdzWkZ1T3dSdDNObGdGT0tQL2o0Uk9Tb0NnTWdnZHdUQWQ4UEZvU2V3UWtGVDUxQzQ5c2dIdGNVU08zbEg5WWluRzg5WjRtWjROVkNzdlJicTJJUTNCeVNyRnM5cGhDaTJUdDV6bStkNEJvcE1RbXdqMW1xMEFzck1zY2N0MXoxakpvUHJUQkRPZDJ2bVJ1ZzJZV1A1RmwzZUpDZURiYk9FUU5uS29tbUM0UDN4cGJJUXhYTTZUL3lLZ0QxaVg1Tk9zTXBrYUJXMDhtTnRWS0dBNWdSakxyYWhnK29tSkZsN3NENTBkYlgiLCJtYWMiOiI4MjhjYzY3NzFkMWYzODBmMWFkOWM0NjQ2NDRjNDY3NmJjZmFiMWFhZjA0M2VjNGMzNmUzZjlmNzRiNjlhOTBiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlF2NWY1aDkzZk5CUFBKaWtmVHlBN2c9PSIsInZhbHVlIjoiS28zUmJVL0kzTUJzU2N1ZENIQnNHSUpuL01KL3hHUWh2KzlEa1N4ejZCbURuaWNVbEUySUxXU29YS3NyTFRrVVJURElzNkEvRTltZWQxUE5tTlF0T01CRk01SExQNW4vQ0VYQUNsT0tWdGJ6VHFNZDZ0czYyaGdTM294azlsYXZvcHI3SWVhODFlYWUrYzVadGlJOWlpZ3ZrYkJ3VnNJSVdpcXNmTWNxMVl3am9yU1RNMlR3dFhoODFMZkVxM1BxQjF0U3VINW1rVElma2o3WHY2ZUZxd3pDcEpNZFF1S0FQTUhBVXhjak85OEFBMG5TYlkzVGNxby9tWXl5Mm1mK1FCbnIxcC9DWVNMZDdpSGpQengwTHZERmN0dHpnTmloMC9yZmtXSjlhSjVHdlJiSmN5SU9TK2ZYZHlYUHBiNmRQMmF3ZlBwRytKQ3Z2ZldVZTlGZzl0bTlzNHRDbEQyMUlaQ1RDWFlabCt3SGc1WHlFZDE5MzYwY3o4cUx0UTlHbzhaYWhzeEdPNm9tRUNoamtXaFljTzhENURvWUpwS24rdURNQkluYVBwVkZIMlJzRDcyZk5tQVRYck9oQ0xQWFBMeXdOeVllb3NZTW05UlRtZEVFNHBDVDhTSDFueU9xMThHRUtuR0ZKOUxjUk1MWmRYWjdxZzYxWllEb2FwZjMiLCJtYWMiOiI4NTVkMzk4NDdhYTY0ZTcyNTk1NWE0NjQ3YWI0YjY4ZmFkYmI1OTJlNzliODUwMzgwZTE0N2ViNGRhZDYzMGJiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InQwOUJNZ3VNbjVoZjJZRWdIWVVYc3c9PSIsInZhbHVlIjoiNlk1Q05JSkRZbnNHa3ZlQURJZHViRzErTkIyZWVGYzJBR1daM2hyc2l5Z1NUUUZ1NTN5NW1nRkZGUEFhNk5Yc3J1ZGYyMUtTa2ZvaUxIZnJVT2JoQ2VHMUJGNU03YzVjcG1pMkdIVEl1M2VsL3dUaEdFRHZvYkg4Yzk1SkttWVdWNWJhRU9lbVFFYmlaV0pnTlNQK0crdWx3MUpyNEVNNGRocXZXNDFmMVhqZXA4eHB1VlRDbDg0aWdqUGZjdlB3UFpCRy8xVlFEZzF1QnVxNTk4dDF1WkFjU05lekphQ2N5Nk9HaXQwVGZDaGtZYnlJbmhIYnJUTHlVMjhCOTdRRytBVWVWa2NtSXBjMzQ5bUtnTnQwOVA2QkdzWkZ1T3dSdDNObGdGT0tQL2o0Uk9Tb0NnTWdnZHdUQWQ4UEZvU2V3UWtGVDUxQzQ5c2dIdGNVU08zbEg5WWluRzg5WjRtWjROVkNzdlJicTJJUTNCeVNyRnM5cGhDaTJUdDV6bStkNEJvcE1RbXdqMW1xMEFzck1zY2N0MXoxakpvUHJUQkRPZDJ2bVJ1ZzJZV1A1RmwzZUpDZURiYk9FUU5uS29tbUM0UDN4cGJJUXhYTTZUL3lLZ0QxaVg1Tk9zTXBrYUJXMDhtTnRWS0dBNWdSakxyYWhnK29tSkZsN3NENTBkYlgiLCJtYWMiOiI4MjhjYzY3NzFkMWYzODBmMWFkOWM0NjQ2NDRjNDY3NmJjZmFiMWFhZjA0M2VjNGMzNmUzZjlmNzRiNjlhOTBiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1778149851\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1697757666 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1697757666\", {\"maxDepth\":0})</script>\n"}}