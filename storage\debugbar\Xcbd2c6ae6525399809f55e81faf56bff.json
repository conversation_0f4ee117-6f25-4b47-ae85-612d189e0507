{"__meta": {"id": "Xcbd2c6ae6525399809f55e81faf56bff", "datetime": "2025-06-27 02:25:39", "utime": **********.662184, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.180411, "end": **********.6622, "duration": 0.48178887367248535, "duration_str": "482ms", "measures": [{"label": "Booting", "start": **********.180411, "relative_start": 0, "end": **********.592263, "relative_end": **********.592263, "duration": 0.4118518829345703, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.592272, "relative_start": 0.41186094284057617, "end": **********.662201, "relative_end": 9.5367431640625e-07, "duration": 0.06992888450622559, "duration_str": "69.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722944, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0035399999999999997, "accumulated_duration_str": "3.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.625058, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.017}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.640514, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.017, "width_percent": 16.949}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6475658, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 77.966, "width_percent": 22.034}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1521767228 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1521767228\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1091291866 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1091291866\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-655467617 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-655467617\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-730373445 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991135471%7C25%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldwVisvVEZRTU1BQ0tXREVGWmNLOWc9PSIsInZhbHVlIjoiZkVxelJEaUN6T3dOM1NyUWc0YXdHUmFUdzF4NWdTNmRvck5HK29wQk52Z1grT3BXcmpaZU5ETTBDWXl4dXFOS3NhWmFtWlI5clU5ZVhMTXNqbFNQMXBPMkF3U1NyRTZHcmVlVTR5NCtYUlBjRTdpZFZISjhUalhBWktsNHIyWGN6SVArWkJOOHZnNXlsbHQ0elpDeHpNeENBTG1ZMUFWSlQ0VUpsRitnc005RWxwVVlvSEF4MDg4Y2VNTWxWMzFqZWx5WEhFVXF3eDUvV2RlNFBURmQ4dE95R2pVcjNPQnhkdnEvdEpHbHkxWjkxVE9SYlg3UmV3cGdUTkJsbzlGRWRQZHhwbzNRakE0aVhwcnVxajhpZnMvQ2V1MUllOGdiTk5xeWpjQkxQQnRzRW9hRlFlY3pNaVpYM2QzbDFFc0s1eFJzaXhQRkZPdHBmbUZRazdRQnYwd1hXVGJMS0JUWVpXMHlUMlBReUMzYWRpd1c0eW9tU1VlZ1VVZTBBOGxaZ2pTVU1LNDEzUE9CQUZVWTkxY2JxNjlNWlhBMDJiK0kwK3BxZlk5Q2NTN2VlQ0hucU15TVNwTDRRNzhTc2QzL2ptckpqUVIyQUxJNjEzMzY0UDNFelpXZnZsZ2NxanJPZStWUGNWM3JpbUgrc21KTnUyVWdwbUVWSEFoS1FyUU0iLCJtYWMiOiJjMWYyYTRhOThkMzcyNzkzMjE5ZWQ1ZGJlODhjZTkwZGQ2NmYxNDUwYzQ4MTU3YmJhYjJmNTIyYWNjMmEwODk0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFsQTdKdmw1UUIzL1J1YmowbzQvNFE9PSIsInZhbHVlIjoiUHVwcjIxQ3VWRTRQMlhhZDd3SXhvMTg5MTlSSFhydk9icEljM2E1MSszQ1VCaDdzRFFrdUtreUU5VFBzMkJCNnZhK2xUVjFodmo0WjhWbUxZWU9TYkJ5WWxIdDRGVmErbER5WStPLzQ4bXhwRFZpK2swaUkxVUFRSlA5Qm9xRzg0YmlYdTA0RmEzQUtyZXR1WGpTL1dEQ0lzMzlmcnlwSExHRnlQdmFXQjJvQWRYSStCczYzYUZSNjEyYzFSVWxNdVVHbkM4T2o4cFpWT0x1RE1TcVExTDJvWUxuVVEyWllKeUtpMU5EYVEySXFHdVA5alZteWM2bzQ3YUR5NVVSdVk0SEJ0SzBTR0xDeDJ3c1FXcE54NnRXY3VmVTlXZmZjck9PWS91ZktHVlc0WVVKc2J2c1dYcDZ4TjVwcUN6U3N5QVNqcStheDdQaE5yQTQyYkgxemk0RTRsVDRqdFdqTkJzWE9ITkYzQ081OWljeGFHOWFJQ1ZDQjJyZ01uMkJQZHU4YUtGSlY1SmlRMDl3bHgvenN1azVrT2huRnNlZWFKeDdIWHNpWWFiVWxJREt0TDRnWjVuU0FicWF4RGJmS0FSemIySmhUY0hOWmRsa0NDVTA1bmpEUy9zb2xIeWIrTGhLRFBzV0xVcllEdWhnbjQ5amVVTjFPZmhDQmxGd3YiLCJtYWMiOiI1YjI3YzMyZDFmOWRhMzE3OTI2NWFjNWVlNzZkNTc2NGE2NzA1YjBiZWI4ZjgwYTI3NzhmNmM5MWJlNDc4ZjMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-730373445\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1453741243 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1453741243\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-813628500 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhlNk5RaWRnenV4dlFobTMya1Y3U3c9PSIsInZhbHVlIjoiblplM3BiR2J4WmxrZUJsTks0d3lWanhlV2grTXlDUFYvRFM1M3pXbUxLVkhFWnNDckRkSEN0UGViYlFMRkJXcTFqMWFVL0p5Z3psbnd4ZG8zNTBYTzl0SUljeDNnWFcrYk1QWmx2YWJScmdJTWxtNmQvcTBmUUI4TG50K2NUSVoreG5EcXdWdVRSZlZuQlk0bGNKRkIxM1J4WDhDRzRBQjJ1bThIYVQ1VzVzeThSd0JBYi9nQ0ViYXpPMDhXR0ZCRFBZOXlDM0U3K0JGTmk3RnQvMkVrTDh3S28xT3Bhb05xTGZJczFMNFdmcFVyMXd5dVVBSnJOaGlGeUo1MjJiQUNlL0t5b09peWNEUlR0aVdLdTJXOUhZZFQ1NjFuOElEMDhHZTI0VXYxWlVFMkpXaFFxYWdSUU1vVmVZZjR5WktYZ1ZIS1lDMEpRNU5YY1BaOE02UXdabExtUk83cnIwNTRCdlB6ZURSSkpEVytzQlpQNDFTSDdlMHJwTmt6Qm9pbU9HVmszQjJkREU5OVNyc1ZtK2MzL0RxUEpQaXJ5UW54SUNsWFFzVWpqbGdVeVc4OEFYSHFudWlMb2NpUWFrWnRvbE15UDNNeHp1VXJYMW9xVTdISGhYMCs4OGk4MFFvOTlPcU5EM01INWJsYy9WdWhUU2VtYjdxZk9BejNQQWUiLCJtYWMiOiJhZmVkMWY2MjdhNDg0MTNkNWNjNTY1NjI4NmYyOGFlY2ViMTIzYTVhY2QzYmZkMDJlOTlkOWM1MmExY2I1ZDA5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJxQzVPODU1Z2hqdVFKSTgzY1NSS3c9PSIsInZhbHVlIjoia3FvbGE0TnJtZnhVOHZMVTh3VU5sV3Faam80ZktLY1IzejJIYzRYcE9NbS9jd3VzMzVyRmFjdFFnTVJuN0hDMFNEb0tCTmg1MkN5Rko2Z09aSTkyclVkUFdUMjFpUzRiT2F2SlBSN0ZvOVR2VS91NXVLWFQzcTByZEVobFQ4Sld6QjJocVpXU1R4UVZYcnd0aG11K0pZTkxabkxEajV2UXFRQTVPbktDb1hiVyswWTJza0I1TDlGZ09iYThhRGw4TW4zb25LNWE3YWw4R1hObHBZL0JJZXIzWUlLcjFmOS9aZm83UjhhU2VKcGFnN0psalkwMkJydzlrT2xrYUExczVXL0ZoSlh1WEN1MkdvZllJRkZYZ3RCa1I0TXVOZ2c0QTdVOVMyQXVUQ1pob2VobWt2di83V25VQnBhZVovUWJTWVBqMTdTYmV0REVVdDZVc3dOUXdra1ZSdUtvOXRoOGJNc003bGV3WlFNZGJXUFd4T2FvQm5iOTZwd0k3Si82SEpaRmxlc3pXSkJ3YWxybVkxQkdmVkl4clBNb2hHa0VvQmxvZGRwMUNya2ovdkN0SGkyRmRjcEY3Y1drbVc0eU12RjJzbG5HRE5wU3RTOElKbkd0TFdnVStuK1RqbUs1V3R4NGVoQ0xWRG8vYU02WVhKRVZEYXllRm9wYVdoZ3giLCJtYWMiOiJmODk4ZjkzMzlmMDUzZmUxMDUwNmIxMmNjZDY3MzFkOGJkNjgyYWZhMmI0Y2Y4Y2JlMDU3ZTY4OGM1YzhlZjI5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhlNk5RaWRnenV4dlFobTMya1Y3U3c9PSIsInZhbHVlIjoiblplM3BiR2J4WmxrZUJsTks0d3lWanhlV2grTXlDUFYvRFM1M3pXbUxLVkhFWnNDckRkSEN0UGViYlFMRkJXcTFqMWFVL0p5Z3psbnd4ZG8zNTBYTzl0SUljeDNnWFcrYk1QWmx2YWJScmdJTWxtNmQvcTBmUUI4TG50K2NUSVoreG5EcXdWdVRSZlZuQlk0bGNKRkIxM1J4WDhDRzRBQjJ1bThIYVQ1VzVzeThSd0JBYi9nQ0ViYXpPMDhXR0ZCRFBZOXlDM0U3K0JGTmk3RnQvMkVrTDh3S28xT3Bhb05xTGZJczFMNFdmcFVyMXd5dVVBSnJOaGlGeUo1MjJiQUNlL0t5b09peWNEUlR0aVdLdTJXOUhZZFQ1NjFuOElEMDhHZTI0VXYxWlVFMkpXaFFxYWdSUU1vVmVZZjR5WktYZ1ZIS1lDMEpRNU5YY1BaOE02UXdabExtUk83cnIwNTRCdlB6ZURSSkpEVytzQlpQNDFTSDdlMHJwTmt6Qm9pbU9HVmszQjJkREU5OVNyc1ZtK2MzL0RxUEpQaXJ5UW54SUNsWFFzVWpqbGdVeVc4OEFYSHFudWlMb2NpUWFrWnRvbE15UDNNeHp1VXJYMW9xVTdISGhYMCs4OGk4MFFvOTlPcU5EM01INWJsYy9WdWhUU2VtYjdxZk9BejNQQWUiLCJtYWMiOiJhZmVkMWY2MjdhNDg0MTNkNWNjNTY1NjI4NmYyOGFlY2ViMTIzYTVhY2QzYmZkMDJlOTlkOWM1MmExY2I1ZDA5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJxQzVPODU1Z2hqdVFKSTgzY1NSS3c9PSIsInZhbHVlIjoia3FvbGE0TnJtZnhVOHZMVTh3VU5sV3Faam80ZktLY1IzejJIYzRYcE9NbS9jd3VzMzVyRmFjdFFnTVJuN0hDMFNEb0tCTmg1MkN5Rko2Z09aSTkyclVkUFdUMjFpUzRiT2F2SlBSN0ZvOVR2VS91NXVLWFQzcTByZEVobFQ4Sld6QjJocVpXU1R4UVZYcnd0aG11K0pZTkxabkxEajV2UXFRQTVPbktDb1hiVyswWTJza0I1TDlGZ09iYThhRGw4TW4zb25LNWE3YWw4R1hObHBZL0JJZXIzWUlLcjFmOS9aZm83UjhhU2VKcGFnN0psalkwMkJydzlrT2xrYUExczVXL0ZoSlh1WEN1MkdvZllJRkZYZ3RCa1I0TXVOZ2c0QTdVOVMyQXVUQ1pob2VobWt2di83V25VQnBhZVovUWJTWVBqMTdTYmV0REVVdDZVc3dOUXdra1ZSdUtvOXRoOGJNc003bGV3WlFNZGJXUFd4T2FvQm5iOTZwd0k3Si82SEpaRmxlc3pXSkJ3YWxybVkxQkdmVkl4clBNb2hHa0VvQmxvZGRwMUNya2ovdkN0SGkyRmRjcEY3Y1drbVc0eU12RjJzbG5HRE5wU3RTOElKbkd0TFdnVStuK1RqbUs1V3R4NGVoQ0xWRG8vYU02WVhKRVZEYXllRm9wYVdoZ3giLCJtYWMiOiJmODk4ZjkzMzlmMDUzZmUxMDUwNmIxMmNjZDY3MzFkOGJkNjgyYWZhMmI0Y2Y4Y2JlMDU3ZTY4OGM1YzhlZjI5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-813628500\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1674049078 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1674049078\", {\"maxDepth\":0})</script>\n"}}