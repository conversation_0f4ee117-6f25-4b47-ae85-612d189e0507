{"__meta": {"id": "Xa1d6b9f70a91a0a18d3d484fdf3feebd", "datetime": "2025-06-27 00:23:00", "utime": **********.986585, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.524911, "end": **********.986601, "duration": 0.46169018745422363, "duration_str": "462ms", "measures": [{"label": "Booting", "start": **********.524911, "relative_start": 0, "end": **********.91335, "relative_end": **********.91335, "duration": 0.3884391784667969, "duration_str": "388ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.913359, "relative_start": 0.38844799995422363, "end": **********.986603, "relative_end": 1.9073486328125e-06, "duration": 0.07324409484863281, "duration_str": "73.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043952, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02195, "accumulated_duration_str": "21.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9441369, "duration": 0.02133, "duration_str": "21.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.175}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.973256, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.175, "width_percent": 1.367}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.978805, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.542, "width_percent": 1.458}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1065168993 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1065168993\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-154319428 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-154319428\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1537192699 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537192699\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-795301908 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983778279%7C52%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1Cc1pRTUhBY1dHRzd6TGN0M05VV2c9PSIsInZhbHVlIjoiY2x1T3UwbENrUVhQL1FMRmhYYnhNNkwwWkpWOFZKZnREZUZrb2lKNmtQdXRsdkgwa25qT3NSSFR1UkJnREp3aGJjazFvK2pFeFF6NC83LzNSbEY0N1JKeE95SE1aR0s3TTZwQU1iUGd1dFk0eWM4K2hEZk5pZXBBTTBkZmZyaHNHV2xoeEFGQU82OCt6UmRJMi9uVkJKYWZxNWlwQmU2YUhrM0EyTFJybFl2d3BUSW5DU3M3YUkzb2lOb1YvcXVta2dIek9jaU85U1JhbWZhcnNwcU85cmppR0xPWjFwcXpqL2RtbnhTdTVFd0RUSFhwa3RuK1FQUkw2QWdwZHBnYnZlQmpIbklxVjJXblJUankrZ0FwYnh0YW92aTAxTnUvSWxMakdVZ2Zua3lQUnZ5R3pLNDBxVmROUnhxSEcrczZJcEkxV1BwL25OMXF2ZmkwTzNrdnFYaEwyM0N6cUhCV0VQczZINzNRZ3lxbTllMUM5cDlDdEVSWTJxMkpXTjgzZDZRRGhFWlpSRDVoUVJQbVd0em5Gck9KZHZ6YnNMTER4SWhoNHV1QURPTHVER0xLYUUybWV5WmtrK25sV1M2Mzl0MHYvMmhOVWY5SVUvbVc3aXFqdmRMTUxDVi9WT1NuM2FKaWtGYlRsRWRjTXJkRUJkakNKZk9QZ3h6dG5HMTkiLCJtYWMiOiJjNmE1NmNmN2EzZDhmYThmMDI2ZmYzZDExZjg0MmE1MDBmYzZmNGUzNDYxNjljMTc0OWM5ZmZhNTdhNWQwYzE5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZRT0NDcTdaTkxJa0x0VGtQYmd0dEE9PSIsInZhbHVlIjoiL2s4aWpLbGVxZTkrWVQwME1lejZFQ2pPSU1sZnJvNEVwWktKS0hsQkRXTytHZWtqWVh6aXdTcGxYZy9IMjBNNkF6ZU1rRkd1UlNqd0o1MEF3RmYydndNVFFFdllncFQ2SXVuUGl1Njl1R1dqdHlLb3Y3U1pqQXRsbWhoQTg1RTk5UDRuK2VNeFZTOHNwbU1teUk3M0JCS2w1blR5ME5Xa2lVNGVFVGE0VWcwK0lvVXpJRTdrdldmd3pQNWp5UVBBRE8yR2RDblpQN0tYVCttNnZPQlByalR0NDR5WGtoakgrbFNvaFk2U25hdUpuL3BVcEpMdWxUTjd4MnQwemVjYjQvRkMzcDNxNFhlbmpJZDV5MDQwSVR0SysyMFkvV0hjTW5PZXJFSGxtVkhlZ1AvdEw2VjZDN2JlL05laE9vbURNRGl2alB4dkdpcWw3NTB4NlpiVDJTeGtNYkw1RkpONVV3Z2RLVVpTbHpZNmVlWmcwNWZvNCsvUXdlL1pzVklmeFZybW4ydS9TTithd2tpWGVVNkZIM2x0NzcxSUNUMmpmNDZBRGRDOHdjbTV6MXhremdTazZxY1JpcEx2MU9QeWpjRmdGQy9VbmZrK29iOFFCQW4yRHBlclZUd0sxZVpFVUEvN1hWN3NGUmJvZEtiSTkvVElsd2oyUGhvTGVWeVIiLCJtYWMiOiJmYTg1MWExNzllNTE4NmM4OGVkNmM3YWZjMTQxOWZiZDQ0ZWQzYTJlYTM3ZDhiNWE0ZTgyZWU5MzdlYzdiOTNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795301908\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-446407102 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:23:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJxQUUzNUo1bFhRaGtZUHVuVnNHNUE9PSIsInZhbHVlIjoidUJJVzBVcGx3S21JRXVlaURFdktWODF6L0t6T1lzV3JUUnhleVdYdXgxdGtWWVJNbzVuSk4rMW5tODBBSFovUG1oWC9ycVp3MmduTDBzOVNwYWNrb0dJREQ2VG5tK0dxUXhvREhMdlVaUmZ3dFBWK2JUNWFLbGFoaGkyQ2dEQ3B3YnR5VndVOVJLMWJNYk4vTy9sb0d2dXh3Vi9ockIzYzFiMzF6elJ1dy9DemRGeGZZSjl3ZGJkdGFtMWhhei9kSW5URUk1SzNibEpLSWliQnhaQmhNR1FkLzcwOWxWZS9zNTFkelk1Rm45RmozTG52QTJKWnRidG1JU2wxWjVZaVFZTmlwQ0tKVENRMHpLVTBXRm9ySTdNbk1wM0RqdXJYZ1U2VjIrQUlvcjZQaDQxU3hzeFVNQ25mSVlaSFloS2svUDBSbjU2Smd6T285K3BZdE94SkJBQmVIcVVMV2tRRWcvRURGdTdFcG5BZ251bUJDRVpIS1kxMy9aUnhIWGF0Zit2ZUkrazRlMmlqVThmK2hEOG1TNVF3c05vckVjUk45RWpwZE1id3dXeFV2cGhVT2tqZEx0S1BXK0JraTVORk1sNkZ4cmZleWRxYThYK2RnRnM1UTVEd0dvM252RzJtb2UwWXU3R0ZTWnZjZWRjZkxib3NSYzlybjgrWERaNk4iLCJtYWMiOiI0NDA2M2EzNDIxOTVlZDc1YzlmYzYzYTgwODZkYzZhOWZlMTM4OWZmZDRhNDYwNWQxODdiMjZmYTA0YmM4ZGE4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:23:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjdGb2ZzVlczeC9oelRob2Jxei9INkE9PSIsInZhbHVlIjoiWTlhUU9rUUllTmlOTnREN01BTmRjSWpqSmdJeXNCdW03MWZKOVVBU1I0NHFJQXp1NHl6cFlGQTZTOEtGLzl0ZzFJU0dsSmVXSnR2bExYeWVOaUMxU1NBL2lLM2daK0hZVFRFaXM5Y0JFN1Ardi9ib2t4YjJQZDU5bXRKRnNoZW5nNlBJOHduZTNwbnlyNmpWeDU5Y3FUN2greHNzNlRTMVIvbDlhUWs0T01EdHp6MHpLK1ZBK3dKQWNtYnNGSmpMZjlhOHpnb1V4SmxzOUN0SWtEK1hjMVdsRTg4cHZ0MHhrOUtkclFITW8wenVNWXBNeVN6TmlmaWlwcWs5cTZnV2lXanZmay8vVTVadEkzdC90c2YrcE8zalVLRThTNU5qK0JQbWNHdEtka3Nid2twVVMvL3lUc1F5SU1yeFNjYTFlYm1tVm9MY3gwZEhDUzdsV2dFVnBoRHZrellRY2kzbVhrN0JoK1dWY3Zwalp6aVYzZVdRQzZBb2FhdXhaTXRUdEdBYXJkaFFmcWdlTTN4ekl4M3VEbExUeFhYbUJKa3MzMDBsOHlYRXdlZlBqQjFuazF2VzlFQUZoNjhpcXJpWjNicEpZa2ZpYUdSV0tUZmo3SWlaeE9Fb3BxQS9Yc1lvTU9GK2x6ejB4bU1CbWtRU3BVVkhPTEQ4cC9uZzVGenMiLCJtYWMiOiJkYjk2MDhhZWM3YzcxNTQ3OWIwMTRiMTVjMmY1MDRkY2I2YTgyNTYzMDJmNTNiMGU4N2ZlZWUwN2E1ZGMzMTM4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:23:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJxQUUzNUo1bFhRaGtZUHVuVnNHNUE9PSIsInZhbHVlIjoidUJJVzBVcGx3S21JRXVlaURFdktWODF6L0t6T1lzV3JUUnhleVdYdXgxdGtWWVJNbzVuSk4rMW5tODBBSFovUG1oWC9ycVp3MmduTDBzOVNwYWNrb0dJREQ2VG5tK0dxUXhvREhMdlVaUmZ3dFBWK2JUNWFLbGFoaGkyQ2dEQ3B3YnR5VndVOVJLMWJNYk4vTy9sb0d2dXh3Vi9ockIzYzFiMzF6elJ1dy9DemRGeGZZSjl3ZGJkdGFtMWhhei9kSW5URUk1SzNibEpLSWliQnhaQmhNR1FkLzcwOWxWZS9zNTFkelk1Rm45RmozTG52QTJKWnRidG1JU2wxWjVZaVFZTmlwQ0tKVENRMHpLVTBXRm9ySTdNbk1wM0RqdXJYZ1U2VjIrQUlvcjZQaDQxU3hzeFVNQ25mSVlaSFloS2svUDBSbjU2Smd6T285K3BZdE94SkJBQmVIcVVMV2tRRWcvRURGdTdFcG5BZ251bUJDRVpIS1kxMy9aUnhIWGF0Zit2ZUkrazRlMmlqVThmK2hEOG1TNVF3c05vckVjUk45RWpwZE1id3dXeFV2cGhVT2tqZEx0S1BXK0JraTVORk1sNkZ4cmZleWRxYThYK2RnRnM1UTVEd0dvM252RzJtb2UwWXU3R0ZTWnZjZWRjZkxib3NSYzlybjgrWERaNk4iLCJtYWMiOiI0NDA2M2EzNDIxOTVlZDc1YzlmYzYzYTgwODZkYzZhOWZlMTM4OWZmZDRhNDYwNWQxODdiMjZmYTA0YmM4ZGE4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:23:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjdGb2ZzVlczeC9oelRob2Jxei9INkE9PSIsInZhbHVlIjoiWTlhUU9rUUllTmlOTnREN01BTmRjSWpqSmdJeXNCdW03MWZKOVVBU1I0NHFJQXp1NHl6cFlGQTZTOEtGLzl0ZzFJU0dsSmVXSnR2bExYeWVOaUMxU1NBL2lLM2daK0hZVFRFaXM5Y0JFN1Ardi9ib2t4YjJQZDU5bXRKRnNoZW5nNlBJOHduZTNwbnlyNmpWeDU5Y3FUN2greHNzNlRTMVIvbDlhUWs0T01EdHp6MHpLK1ZBK3dKQWNtYnNGSmpMZjlhOHpnb1V4SmxzOUN0SWtEK1hjMVdsRTg4cHZ0MHhrOUtkclFITW8wenVNWXBNeVN6TmlmaWlwcWs5cTZnV2lXanZmay8vVTVadEkzdC90c2YrcE8zalVLRThTNU5qK0JQbWNHdEtka3Nid2twVVMvL3lUc1F5SU1yeFNjYTFlYm1tVm9MY3gwZEhDUzdsV2dFVnBoRHZrellRY2kzbVhrN0JoK1dWY3Zwalp6aVYzZVdRQzZBb2FhdXhaTXRUdEdBYXJkaFFmcWdlTTN4ekl4M3VEbExUeFhYbUJKa3MzMDBsOHlYRXdlZlBqQjFuazF2VzlFQUZoNjhpcXJpWjNicEpZa2ZpYUdSV0tUZmo3SWlaeE9Fb3BxQS9Yc1lvTU9GK2x6ejB4bU1CbWtRU3BVVkhPTEQ4cC9uZzVGenMiLCJtYWMiOiJkYjk2MDhhZWM3YzcxNTQ3OWIwMTRiMTVjMmY1MDRkY2I2YTgyNTYzMDJmNTNiMGU4N2ZlZWUwN2E1ZGMzMTM4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:23:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-446407102\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1484954070 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1484954070\", {\"maxDepth\":0})</script>\n"}}