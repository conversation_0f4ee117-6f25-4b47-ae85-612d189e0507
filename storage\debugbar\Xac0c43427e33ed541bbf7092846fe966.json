{"__meta": {"id": "Xac0c43427e33ed541bbf7092846fe966", "datetime": "2025-06-27 01:15:25", "utime": **********.933696, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.525404, "end": **********.93371, "duration": 0.4083061218261719, "duration_str": "408ms", "measures": [{"label": "Booting", "start": **********.525404, "relative_start": 0, "end": **********.882414, "relative_end": **********.882414, "duration": 0.3570101261138916, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.882423, "relative_start": 0.35701894760131836, "end": **********.933712, "relative_end": 1.9073486328125e-06, "duration": 0.05128908157348633, "duration_str": "51.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043928, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00279, "accumulated_duration_str": "2.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.909724, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.007}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9200828, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.007, "width_percent": 21.864}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.926753, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.871, "width_percent": 16.129}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1471462023 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1471462023\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1929789501 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1929789501\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-932474680 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-932474680\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1319332792 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986905698%7C91%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjU3SGQ1RkJqa3lMQzAreUUySzdWRHc9PSIsInZhbHVlIjoid2ViaFVzQkFSc2daQVNZa2xBVytmcnBLY2dKRkVZYnRybytDQUd4YWZZODROemNHYXFPTm1IVms2NFJnN2g4RlREeWIxb3RzZS9PRmRkeG5zb1JPUUZtRUtGYjJJNDdUNXE4RTFMaDJXRkQ4WDhzbHJvUUljaFFqNGNWOHJOMDMwS2ZPdWRwQVFTaUZUQjU2QlRvOGJYUWFmcGRoM1d4a0tCTm5TT3FpT285WXo2enF1N3NZQ3pTT2NwVEF2eVI0MHc0TmpFeTFQYmx2RnVoMk5Ja3QrSjlWS1dEUXNkUDEyWTR4cWRoQ0xPSm92bXp2cWdxUmN5RkMvRlZPbkszUk5QR0FKR01yRGw3NXVXK3dPVGxCa2ZoOGw5bEpxMU5iTjkrcHBKcTZtWlFDbXdNMGVTQkU5YkpOeG80NmZvQTdmNXQ1OWpSYkU2TXZCQmgrbjJkaHZ2ZW8vSWQ0YW8wUWtsQXQyVm9sbTVOWUhuQmg2ZGlZcEpPSWNhSEZhajhLZUF1NHJkUFNIRG9nZ0ttcXlSMUVIOEc5Vmdzd3M3NytJZG1CQWdiZWttMjdLMzI0azZoZ29nWGtlYkgwb1RlUmJMeVV1WnB2Qk10RC92dzV6MWttVUFncXJJT3hEU0U0ZjdwNHBDZVdoRVJFWSs2WE5FVTBqM0swRmNXS0ZJVEYiLCJtYWMiOiIyYWZmOGM3ZGI3ZGU3ODk4NTFiMTM5ODJhZGI1MzdiZjg1NzI4NzJiYzMzODE1ZDBhOTk4NDYyZDMyMjJmNjRmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlRpdVM5MnNleVczMFUxeGF5SnZMbUE9PSIsInZhbHVlIjoiOVBGMHJUWGlvZy8yL05OZjgvNC9OeFN4cWRjWmUrdE9YMHBJdERpRzNSZEtrdzBKU3ZhM3Z0VDRPM1ptRER6bGFtZlBwbVM5ME5VaWdGQ1FSa1I0VEFYZ1hBRUZvS3RkMWpRQ25yL1MwVmRFclM1bm9RZXh4Z2N6SGpmT29hbmFaY1dnb1luMjZHZXFLWFlaRmg4b0JSVmxZYWRGR1hsS0VHR0VqYWJlZHhGWnhUWUtlcXc5VjN2dXZNUWJCb3pobk9Nc1pDSkxaOXZkMWR4SUJxR0xkZ0lRWHZrbE5MQzFSZ1ZQc1k4SWdjcUJWNHFOTjk0bDZOaHc4U01MRGsvNmNXUU9yQmdWeHE2WEdldUNsb29qeEtzemNPcElpQXVraTUvK3BKK1ZkUkNOMk9hSUI5NldpbjNvZ0ZIbGZsd2l0K21HQ1F2SXh2WW1HNzV1ckQ2R0NLaGk5UVQySlF3UHprM3FhbU50Q1piS0hZVFRkY0VVaEM0K3Uxb0ZuRExWcGlZT2pEM0NmUjBOZHVRUDR0RnVXRktYYnZNalpTK0pZRTJZSTJyZFlSdEIyU1MxTm5uOUNUdmdFVk13Y0FjRTR5bHNyYnJBVVk4UGhERVl0b2w0eFUwVEJNbmQ0dW4vemUwQ0QxbTY1elFucFRCQ2tTUnlJeTRPajgvZWI1VkciLCJtYWMiOiI0YWViNGNjMTU4YWU4NzhlNjE5NGI1ZGIwMDBlNGM4MTRkN2Y5ZGYwZDE4MmY1ZjNlYWEyZDhlMWI3ZThmNmM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1319332792\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1564389832 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1564389832\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1754231325 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:15:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5ucGtkNGhPbW9oYVdmeXllaHpUZVE9PSIsInZhbHVlIjoiSVpCZW1xQ0FUU2FPK1p5M0VEWVNNMXF2NlFvc0JBS29RNit2UFZSQ2wwTW91MjNlWHp6M2FmNVNBYnNZeG4vZUMzVlF4akxDWm95dVU0d3VWZjNnSzlTRGpnZ0NEUURJeFRpelh2RzUxWjRzbGFYQnRlV0ZyTG91OEtvam1DMUNJTkJuNEhpWnRkSzgxUlhmMjRkM3JjZGxENXRKb0QzaE9aQjdZaHRhME5MaENhZkpRZjR1NjV0TFFma244MWRpYmhIWDk0cEkzR3p2OWRZTWt2b0R2dWZQcm96QzlLVUk4WDRnWVhmTklKKytwRllpNGJpQUJmNEhsSnMwM0VIME55VEpNSE5IL0Vrd05PeDJ5ODRvc1RtUk9vaVFEVVRmL0tJV0pKTTl5dFBWY3hTSWl4aUxqdkZmU3FLdVJVb0xwSm1DNmx1dHhaRzZ0Nlc4NGFnUVNuT1B4SmNjVmhEY0RNNC80QUFRU1MrZDR0dURYMWwrOFpuSFFoRmpOVGZuSTl5dlJrbk1VNHdVNE1jaWFxYXRzeUlrYWdzUTF6cldlQW40QjRURDlqOGVHWHAwRFA3ZDl2Q2R1NmFXNStXa2NlRlEzcTlnbkl1SVUxQlNyc2FTaDlPdG0zTnVna29KampHcndJWFIwdkQxNUIvR2NwWThjS21NYnIrcmNINGMiLCJtYWMiOiJiNzc5Yjk1YTM5M2JlMWM2YTU4ZWU1MmFmMjJmMWZmOWEzNjBmMjY4YzE2ZDM3ZDQ1Y2U1ZTRjN2E4MmMyMDVhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpmaWFMQUFJbnhUOXViSFFaWXNYbUE9PSIsInZhbHVlIjoiVnhHRFQ3TVlzd3h3Ly92eXRXODdXYkNtRnJyYWhmSmFiMzVsczhpRnZ0SFBrVGdyRHg0UU80bHdnY0NTejNRNUY1K0RHNUhsZTUwemVzWVgrVW43aGhkSllrdU5YU3RUWXBNRjlwUG5McGRCVkVkRXI3UEpMcHphQnBKdzlHRGN6N0NqQUNEcmd4OGVzajNlSlNVNEtZQ2t1WGhFVEFDbGRvSmRYYUpUVTdMdnBnSWdaWW1GQUtLOEViRkJtb3h5TFdkS1ljcldWb3AwRUE2R00zVHVwNlloMVNaZW1hTkpCVThPT0Y1YzVYbWFFbVZkMlNqR2JyUTVDSHc1Y3lJTFVUa2tKVEtyL0tyTHFWQWFQTkIzSkllcllVczBJNFFYcjFXRmZBSGdVdWkrYTdlOFp4NzZSVk5OVEdyaEY0MW5FTzlmVjExS1ZlSE5KWnFxUVhYN09BQkx1emRvWUdOR215a0Yzb3B5MnNmQWhVR3VTenZOSkE2M0l2a3Q3L3gzZFpoSWl6TVVpc3U5ODBVTWtaV25sUUtkN2tzVGt5dFJleGZ6OGRtWUNsOW00ZGJkbHN2Y0RLYzBkaERWSHpkN0d6TU5lY1pGUzkrS0JVTkVac2c3cVc1endXUDNYTDJ1ZjY2dzFyMGtTdlB6Zlkwa0pKMGdYdW5RUTdjMW5mWUIiLCJtYWMiOiJjNjI2MDJmMWRjZmY1ODU5Y2Q4NzJhZTQxOGIwNDM5NjYwZWI0NjU1NjA0YjY3NWUxNGNjNjUxMzViOTI0ZDZkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5ucGtkNGhPbW9oYVdmeXllaHpUZVE9PSIsInZhbHVlIjoiSVpCZW1xQ0FUU2FPK1p5M0VEWVNNMXF2NlFvc0JBS29RNit2UFZSQ2wwTW91MjNlWHp6M2FmNVNBYnNZeG4vZUMzVlF4akxDWm95dVU0d3VWZjNnSzlTRGpnZ0NEUURJeFRpelh2RzUxWjRzbGFYQnRlV0ZyTG91OEtvam1DMUNJTkJuNEhpWnRkSzgxUlhmMjRkM3JjZGxENXRKb0QzaE9aQjdZaHRhME5MaENhZkpRZjR1NjV0TFFma244MWRpYmhIWDk0cEkzR3p2OWRZTWt2b0R2dWZQcm96QzlLVUk4WDRnWVhmTklKKytwRllpNGJpQUJmNEhsSnMwM0VIME55VEpNSE5IL0Vrd05PeDJ5ODRvc1RtUk9vaVFEVVRmL0tJV0pKTTl5dFBWY3hTSWl4aUxqdkZmU3FLdVJVb0xwSm1DNmx1dHhaRzZ0Nlc4NGFnUVNuT1B4SmNjVmhEY0RNNC80QUFRU1MrZDR0dURYMWwrOFpuSFFoRmpOVGZuSTl5dlJrbk1VNHdVNE1jaWFxYXRzeUlrYWdzUTF6cldlQW40QjRURDlqOGVHWHAwRFA3ZDl2Q2R1NmFXNStXa2NlRlEzcTlnbkl1SVUxQlNyc2FTaDlPdG0zTnVna29KampHcndJWFIwdkQxNUIvR2NwWThjS21NYnIrcmNINGMiLCJtYWMiOiJiNzc5Yjk1YTM5M2JlMWM2YTU4ZWU1MmFmMjJmMWZmOWEzNjBmMjY4YzE2ZDM3ZDQ1Y2U1ZTRjN2E4MmMyMDVhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpmaWFMQUFJbnhUOXViSFFaWXNYbUE9PSIsInZhbHVlIjoiVnhHRFQ3TVlzd3h3Ly92eXRXODdXYkNtRnJyYWhmSmFiMzVsczhpRnZ0SFBrVGdyRHg0UU80bHdnY0NTejNRNUY1K0RHNUhsZTUwemVzWVgrVW43aGhkSllrdU5YU3RUWXBNRjlwUG5McGRCVkVkRXI3UEpMcHphQnBKdzlHRGN6N0NqQUNEcmd4OGVzajNlSlNVNEtZQ2t1WGhFVEFDbGRvSmRYYUpUVTdMdnBnSWdaWW1GQUtLOEViRkJtb3h5TFdkS1ljcldWb3AwRUE2R00zVHVwNlloMVNaZW1hTkpCVThPT0Y1YzVYbWFFbVZkMlNqR2JyUTVDSHc1Y3lJTFVUa2tKVEtyL0tyTHFWQWFQTkIzSkllcllVczBJNFFYcjFXRmZBSGdVdWkrYTdlOFp4NzZSVk5OVEdyaEY0MW5FTzlmVjExS1ZlSE5KWnFxUVhYN09BQkx1emRvWUdOR215a0Yzb3B5MnNmQWhVR3VTenZOSkE2M0l2a3Q3L3gzZFpoSWl6TVVpc3U5ODBVTWtaV25sUUtkN2tzVGt5dFJleGZ6OGRtWUNsOW00ZGJkbHN2Y0RLYzBkaERWSHpkN0d6TU5lY1pGUzkrS0JVTkVac2c3cVc1endXUDNYTDJ1ZjY2dzFyMGtTdlB6Zlkwa0pKMGdYdW5RUTdjMW5mWUIiLCJtYWMiOiJjNjI2MDJmMWRjZmY1ODU5Y2Q4NzJhZTQxOGIwNDM5NjYwZWI0NjU1NjA0YjY3NWUxNGNjNjUxMzViOTI0ZDZkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754231325\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1650549269 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650549269\", {\"maxDepth\":0})</script>\n"}}