{"__meta": {"id": "X53c4aa3c018f0215f416a9d1eddb9c75", "datetime": "2025-06-27 02:25:23", "utime": **********.53972, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.12903, "end": **********.539734, "duration": 0.4107038974761963, "duration_str": "411ms", "measures": [{"label": "Booting", "start": **********.12903, "relative_start": 0, "end": **********.491131, "relative_end": **********.491131, "duration": 0.36210107803344727, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.491142, "relative_start": 0.36211204528808594, "end": **********.539736, "relative_end": 2.1457672119140625e-06, "duration": 0.048593997955322266, "duration_str": "48.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45409488, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00276, "accumulated_duration_str": "2.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.520819, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.014}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.531822, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.014, "width_percent": 17.754}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5342782, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 88.768, "width_percent": 11.232}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991028607%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZSblBlbTFGekxYVjZpTjdJQ1U2anc9PSIsInZhbHVlIjoialdRSWpybjRkUEhOcmxLczUyQTJwKy9rOU1KNUt3SmlEMnZRK0RYOXFNeXpLOGZFSUM0bU9ZN0lJbmtSY3NCOVJkNmxoYktJemF2Nkx1QmNHYmV6bGNINUF2SkFSL3VvMzRVdU15NHB2NldjYzkzdkdDejhDMTRqRkU1Y0lOOUJWK3VRNlVSSTkvQzlBc0Z1NDBERzJZYksvS3JEd0pmR2lKZm5BcWl6U245QXQvWmN4N1JoQ3FTMG9VTXhsdk1iQWZkR1VLaTIwNjlJaEpGZ21FK29wV29JVHlXQmR6V2J6M0VhaDBuUllmMlcyajV3ZnRManVTUkpqUVYzUStOb3VLN1lTY1FEbGVwK2MxOXRNQ245VzErQ3c0RmF6amNtS1BTZnhodjMrenJqOG1Nbld1YmMzRFEyNWk0OUNYeXJPSjJFNGJYb0VPNGZJSlJqb2JSY2JSTXBiRHE3Q0hpTmtYZ3QwK2tLdjkzOHVXT1hDSWZUN2F5d2FieXNJR1ppUXFQUHV5TnFSaE5uRkY5dURzZk5MSFVrcUdmekRjQjRZdG1zTkMxWGRCak9RQXZKVFJJa1c5TGFsOFRHVkZHQUk1aUY1MFZOZThERHJqbFBjTkZVbjg1QnBjSUh5dkdyMmNHWkVSRkNoeWJ3cjZiNUFSd2NZZXpoZWR5NzNQT0oiLCJtYWMiOiI2Y2ZhZWZiMDc4NmY0YTU1OTMyODRhYTVlYjM5ZjM4OTc0NTg5MmMwMDE4MzNlOGE4NjA2NDlhYzE3YWI2NTVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFHOElRMUhVWU5oNmE1SDhKckVZNWc9PSIsInZhbHVlIjoiUEFCMEtibWZsc3VJcU54SGN3QzROV3p6NFFGSmh4UG9aWTc3Q1RlY3hOQjFYcEdSQWE5WWw2Z1U1N0kvSVlFdDYzUFNFTFBtVHU3TVBXVTgreUNldDNnbjRkN3dxcll0RHVnV0dBUEdLNmN0ZUh2YVlyNlNMMU9ZWGhKWjFlTGxQY2hTaEQxUmI4azA0Z0hoeEFmeWVUcGNVMXlteDZNMEtpcEFoMGhPZ0JvU29uTnV5SjdqOVQrM1pDb0k5VG8wV0VSbFZwTkxoWXQzeVd2S1RhWVJQc3NNR2tHK2hCd3F3VGt0dHp1cTkvbkdOaTB5Ym5qbTFSUnNOZmJNc3RFYmJOV0w1eno3OFdlV0tUWlNMcCtuQXZaNnNuZTkza0dWUzZkL2F2T0JYTVhHUkRHNlgrTlZEQmFZUjhUOG1rL3I1cUtzbThRV3RNZzlod2VsZUlJVW0wWjU5WElmRWJyRU5UL0h1ZDRvMGlJcVZ2aGRwbnNxc21Ic0h1Nk56K0ZjNWlvRXNjUXFkWmNJUU84VEJLSDRyOTVTaFYvekxIQk9DUndjckVLUWh3Uy9pRmpXQ0RJU1RoeWYyMXkwdXhLaWlDTGI2bExtdExIWEplMnI1Vm5rd2xEZTQ5Z2pSZThYTi9DakZjdHFiOTVRaElVVFNNNThzcWoyb1g0cWpwVm4iLCJtYWMiOiJlZjM3ZGI1MDEwOGExNjBlMmViMjFiNTY3YWM1YjRkMjVhZjIxZmQxYjgxYWMxOGE1NTFjMmNkZGMzOGEzM2VhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-619622894 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-619622894\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-760692227 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBIdXhpa210aGQrekZIRHMyV3JnWWc9PSIsInZhbHVlIjoibGRXWENrSzc1dnNBd0V0d2ZhVUs5WHdVZDdxSHlxcURXUXZnUkw0YkJRcU5iVnZaTVI3RDhjUzJkeEhranZmZzJjalYyRHlmYlpTbThaejhvVm5qNUtsVE9rdGUyVG1pSVUxNFl6ak5QcHg5SmMwWFhzb0N5SzhIOW9lNDRXZGVFeVRaWjNDUEpZdzBJcGRwSEd1bDE3VjdmNzdOcHJTOHNHVHZFNHAreG9qQUFCeGNHV3hDTmRkazhLbXZRTzZpRUd2MGUwcGNNUEc3UnBWcnZ1MTVOQ1JmRzJDd1J6MnQzOTgxMm1ZTjBPd3dHaFYvK2d4TGxjNkdMamtwNThMOXdqL3ErTWx1eFhwaEZiVmxHeHRLR2tyemE4aVdHcW0vbmdORm4zR3U2M2dka1BDSTlGWVdPRkxDd0JIamNCQzZJNWU4VVVsT2VwZktzNEFWbnZyMVgxdVp6ZUw1aGZNNmM3YURaQVh6UzhQWUNZK1ZwZmxMQjhPcjVVWFZMdVU4Yklyd3NjcGYxU1ZmeXgwdThxRHl1aXMyVkY4dy9SSUVNL1BxeXNaN3B5OUUzWHplUXVNZEc5T2V6cmgxZDhpVGhrc2FtR1R6R0JjTThRN05CWFBhcFhMdDJiRzREaHJZN2NDM1ZaUEQ5bnNzOHJQRkNYakdvT2todUg0RjBBbHQiLCJtYWMiOiJmYmM3ZDllMWY3MDc4MzVlYTE3MjY2MzgyZGJlMzkxYWY5ZmRiZTU0YmE0NzJhN2U4ZDczYjg2YmZjNmIwZDhmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFnUXJwK2hGK05wRlJxVEpKMk90c1E9PSIsInZhbHVlIjoicmNDN3hTdWN5RHRhbVArRlZHRU0zSkpKTUVIQ3M3c0RaakM2b2c5bThkTk1keFRJQllEbEhDNEtlaDdlenVjL29IY1NWQVVKb2xxTVhqbk1XWFprVURLQnZoYXU5a21kcnRDNmNzT3JyYnJqSlJuT0FxUDZRWkZsQk5tYTgwMEVjRGkzZ3I3YlozbUY4RTRtSWl4TUI3Qkh5VVNpeEZqN21HeHNjVjRiYU4ySEJOOTF2ZDlhTHJCcnczKzNhWUxhNCtnWXBsS0lkdENyNWZlWVdlOHJ1MmV4SnhOQks0TjNYS0QvdmN2cWJRd1F5dTB4Mk9JaElpM00zVkc0Y1R5U24zOUVOWEo0WWdIT1hhMjNkVk1VcjBJdEZHckcwVmNZSXVyNXhUQTd5UVh5QVlaeVV6dDNGT2Y0SDVDNWpXc0ZDL2FPeEVoWmhwQlp0TkNTS0x5LzJLWXBZTFZBQnRtMG4vK1lOMmZsUWxPYzN0dTE0akVWa0R4OFVFU3dHOFI3QVN3M2VzNlFGb2MwTkx6RW9ocWI3TXJqWVhqM2xxcTZFRnkwazhyM09QaS9DbFg1RklhTDZ6NEJJUk56cWtUUGxzL0wrZXNHSWJvbFBiKzd2bEZKMjY5N09NOFk1TnlHREtqcFB3R1dmREFJQTJlOGpEVkR0Z0xaRnRwOTNTQXQiLCJtYWMiOiIzODNhYWQxMTI3YTBjMzhmYTJiZmI5YzQyNTVkODQwMDYzNzdmMjJmNGY2Zjg0MTYzZjlmODIzMjNlYTFlMTZiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBIdXhpa210aGQrekZIRHMyV3JnWWc9PSIsInZhbHVlIjoibGRXWENrSzc1dnNBd0V0d2ZhVUs5WHdVZDdxSHlxcURXUXZnUkw0YkJRcU5iVnZaTVI3RDhjUzJkeEhranZmZzJjalYyRHlmYlpTbThaejhvVm5qNUtsVE9rdGUyVG1pSVUxNFl6ak5QcHg5SmMwWFhzb0N5SzhIOW9lNDRXZGVFeVRaWjNDUEpZdzBJcGRwSEd1bDE3VjdmNzdOcHJTOHNHVHZFNHAreG9qQUFCeGNHV3hDTmRkazhLbXZRTzZpRUd2MGUwcGNNUEc3UnBWcnZ1MTVOQ1JmRzJDd1J6MnQzOTgxMm1ZTjBPd3dHaFYvK2d4TGxjNkdMamtwNThMOXdqL3ErTWx1eFhwaEZiVmxHeHRLR2tyemE4aVdHcW0vbmdORm4zR3U2M2dka1BDSTlGWVdPRkxDd0JIamNCQzZJNWU4VVVsT2VwZktzNEFWbnZyMVgxdVp6ZUw1aGZNNmM3YURaQVh6UzhQWUNZK1ZwZmxMQjhPcjVVWFZMdVU4Yklyd3NjcGYxU1ZmeXgwdThxRHl1aXMyVkY4dy9SSUVNL1BxeXNaN3B5OUUzWHplUXVNZEc5T2V6cmgxZDhpVGhrc2FtR1R6R0JjTThRN05CWFBhcFhMdDJiRzREaHJZN2NDM1ZaUEQ5bnNzOHJQRkNYakdvT2todUg0RjBBbHQiLCJtYWMiOiJmYmM3ZDllMWY3MDc4MzVlYTE3MjY2MzgyZGJlMzkxYWY5ZmRiZTU0YmE0NzJhN2U4ZDczYjg2YmZjNmIwZDhmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFnUXJwK2hGK05wRlJxVEpKMk90c1E9PSIsInZhbHVlIjoicmNDN3hTdWN5RHRhbVArRlZHRU0zSkpKTUVIQ3M3c0RaakM2b2c5bThkTk1keFRJQllEbEhDNEtlaDdlenVjL29IY1NWQVVKb2xxTVhqbk1XWFprVURLQnZoYXU5a21kcnRDNmNzT3JyYnJqSlJuT0FxUDZRWkZsQk5tYTgwMEVjRGkzZ3I3YlozbUY4RTRtSWl4TUI3Qkh5VVNpeEZqN21HeHNjVjRiYU4ySEJOOTF2ZDlhTHJCcnczKzNhWUxhNCtnWXBsS0lkdENyNWZlWVdlOHJ1MmV4SnhOQks0TjNYS0QvdmN2cWJRd1F5dTB4Mk9JaElpM00zVkc0Y1R5U24zOUVOWEo0WWdIT1hhMjNkVk1VcjBJdEZHckcwVmNZSXVyNXhUQTd5UVh5QVlaeVV6dDNGT2Y0SDVDNWpXc0ZDL2FPeEVoWmhwQlp0TkNTS0x5LzJLWXBZTFZBQnRtMG4vK1lOMmZsUWxPYzN0dTE0akVWa0R4OFVFU3dHOFI3QVN3M2VzNlFGb2MwTkx6RW9ocWI3TXJqWVhqM2xxcTZFRnkwazhyM09QaS9DbFg1RklhTDZ6NEJJUk56cWtUUGxzL0wrZXNHSWJvbFBiKzd2bEZKMjY5N09NOFk1TnlHREtqcFB3R1dmREFJQTJlOGpEVkR0Z0xaRnRwOTNTQXQiLCJtYWMiOiIzODNhYWQxMTI3YTBjMzhmYTJiZmI5YzQyNTVkODQwMDYzNzdmMjJmNGY2Zjg0MTYzZjlmODIzMjNlYTFlMTZiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-760692227\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}