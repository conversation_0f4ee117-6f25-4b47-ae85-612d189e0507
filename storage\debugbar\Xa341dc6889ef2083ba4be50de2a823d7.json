{"__meta": {"id": "Xa341dc6889ef2083ba4be50de2a823d7", "datetime": "2025-06-27 00:18:31", "utime": **********.055655, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750983510.597702, "end": **********.055672, "duration": 0.45796990394592285, "duration_str": "458ms", "measures": [{"label": "Booting", "start": 1750983510.597702, "relative_start": 0, "end": 1750983510.999292, "relative_end": 1750983510.999292, "duration": 0.40158987045288086, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750983510.999311, "relative_start": 0.401608943939209, "end": **********.055673, "relative_end": 9.5367431640625e-07, "duration": 0.05636191368103027, "duration_str": "56.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041792, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00271, "accumulated_duration_str": "2.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.028276, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.886}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0397, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.886, "width_percent": 18.819}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0456629, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.705, "width_percent": 20.295}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IlRjTW5xSFI4U3lTMGNZTEV1TTUxTXc9PSIsInZhbHVlIjoieGhlY1BZcTBHOFVESmtlMjJQbGpvdz09IiwibWFjIjoiNTlkZTQ0ZTllYmZiNjYxNTE3ZjY4ODdiMDE5N2NlZDg5Zjk1NmI0MWJiNzg2YWUxMjlkYTAxMDU3NjUyZDI2NiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1979620363 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1979620363\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-6648260 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-6648260\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1578593423 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578593423\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-82331595 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IlRjTW5xSFI4U3lTMGNZTEV1TTUxTXc9PSIsInZhbHVlIjoieGhlY1BZcTBHOFVESmtlMjJQbGpvdz09IiwibWFjIjoiNTlkZTQ0ZTllYmZiNjYxNTE3ZjY4ODdiMDE5N2NlZDg5Zjk1NmI0MWJiNzg2YWUxMjlkYTAxMDU3NjUyZDI2NiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983312249%7C50%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhlRzdSTXpERGFTdXJVQ1FmZU5IcWc9PSIsInZhbHVlIjoiREY4MEdyc1lqRldXOXE1SnN4QXEweU0ya1JKeWw4YkJTelJZSjV5bmpuYmhIQVJRZ0hwcHRiOXV0enEwMGpUTGNISzBlNXpYdGhHKzVUVUM2eXlXZ3doeG9wY2drTXJTQ0hiTHAwYUloZ1VUV0JYcHBtVXZKNmhZaGVodDNjZVZzWXRIWHZEYXNhWURCaU8yOWhYcFZhM1VRSVpzRmR1dHJ6TzAybkx1OWNsL1BmNTdJcDBkemY4SkRnNHBLZU9qaEVZeXhURjhGcU8rQ1JTdEZ2ejM5RkU3M3hOYXZsbFhzZldZL01KaHB4OEwzNE9RTnFWRzJDMXlQcDRJbWRNMUZTWlpIempBdHY4MC9TQzgvK2d0ZVJzS3RSdnF6VW9FNldaS0poSnM2M2lWUnFRR3FKUjUwZDVXRGdZcDNFSUhpNURRSXBlTDNwWjg5U3VoNkl4dS9SUnQ5d3NkTG9DTFl5Wld0R1Y0YlQ1T2dOcHJUczR5QTdpMDBvdTNlRHZPMGxvMGdmRFhiR0I1VXhPWEI0Rll3VjVobCtqTktQSzZNNFBXREhJeE1wZ3NUWlFlU01sQU1JT3UxZkhYQ2Rsc1krRVRBbzlwaVA1dFpXckpMc1c2aElTdzVnY21zNUhzc0pKd2pXejlLVUpxSkd3UGN3MDZxcGEvdWJNRTUraEUiLCJtYWMiOiI1YmQ2ZjE3MTI0YjJmZTJhN2YyM2E5N2QwYjFjMTVjZDU2NzFlNGVjNDU5MmYzODY0ZjU4ZWZhZDEzNzJkNjgyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImpnVDVpUHBpSlIzYkI0STBqS1plbXc9PSIsInZhbHVlIjoiL2NBdFdmT090MUg0UUNxSHphTFhBUnBrK0QvRkFTQWI4NytZRFhEWnArWFdnS2cxUFRhSXlmcUNqZTNFNTNacGZ6b2UxdUxua0xBakFTL1NpVUg2T2FOTDBDUWkvUGF1cXBwOHF1aW9rem4zQXZvdU5mNnJmb1J3N2crTDJQS0ZKeU9XVGt3Y1JuT1M5Wk1zYitBNnpTdnFSTC9yc1NUckRKNGhXOUtKQlVQRDZSYXV0ZDdtNkcyM24vR1dQSU5Uc0c5RWxFazMxZ2hIUDdqL0pKcHVHajN3YUpiTEE5RGJPNC9qbEsvNllpNnBsRWE3d2EvdFJNVFh0VGFNUGRJdW16eng0NjNXUHE5VTdVaUQvSFNTdFBObEE5RThSb25kM3JDVVpCbWtjS3JPN1JWZWVzOFpQK01BTWNIUU9RdTd2TUZ0cUNYMjlDUHk4NG14N1NEWGFYRWVsM1gyUWV1MklCVXA5VmJWbUYwZXRBS1RSNThWTXNwV0d3VkUvZ0hUZkI1SHlGb2JRUXdVZjhITzV6RE9VbE9lWk5TK1V4L0JGYURuWFdNZHhPSDcyZUtSd0ZET3M0cDNHT1cxWkptQS9qUVpuZSt6M1phVjZoRkNISUdta0JKUW1aditOOWFBaWJBZWVKNWpDOUpTMkdvNDBONEZmNmFLVW00NWFvWDQiLCJtYWMiOiI0MTZmMzMxZGY3NjU2MzdiNDEzZmQ0YmMxYWZkOGQ4NGIzMjU2N2NlMjFmYjcwNWY3YTY0MGE0NjZkOGYwNmNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-82331595\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-592597083 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-592597083\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1918646846 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:18:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitTVnFtM0xremFUZXhMaVlsMDJuUEE9PSIsInZhbHVlIjoiYUwzWjhmZGdkOS9hcWVZd2ZqbG1PbE9WMlFmVW8wa2trOXgwOWNQZytUVWl3VVJCMjZ2SXNSMHdCYWVMVUF6UnY2SFI1b2daUVN6djE5WDV6djRKS2JWWWFQSWkvazJyVnlsQWhHRE9LMEFRc3MvdjNTWm5WdjB0RGgrb2JIWWNmYVR3MHBKaTcyWitER2FlenpsSWt4SnppMndRUkVtUVRjMTl1bkdQYnA2S0lPRXZyVFJpTzhoSnJnWUFYVWg3ZTJUMklpczV5ZFk2TkFKSWgwaUdJQVM0SXY0OW9JTnZwMS93Q2l2WW95WFc5dmtIRURnTGNuQ3FUVTFCelBZdnBkYXdzQVErcm9wbDhkcjBrb1VVQStaWDRpa3UzSEhRbFhiQ1o2MkN1MFZMZHNtYkYvakVRTURONkdkWVFESmdScUxWV2o5bXROR2hsbVE0U3FtMzFvbEJQM3FhVXlrOXhLSXFPOGtDUHgyRFFNQzBpalVMVitRcU4xRFg2NWpkaVFsdTJOaHZrNWZDbmoyVHdCejZPU2g1a21xVWMydmFRQzNvTHZpUHBzSEdldFc4Y0hwZjduZkZaM1pIL1cwMzVYbnJUUTJLcTg4UVoyZ0NVMm1kV054VUhVRXcrVXpMdkx0RGFHTFRHUXB1SVZKYjlhTEpEN3JSbnFWOGpteFgiLCJtYWMiOiI1NzQzY2FlMDY0YzA3ZDQ0M2NiNzY2ZWM5OWJlOTljMjg3NGE0MGY3ZjkwM2RiYWRjYzdiNjVhYTlmYjY2ODUyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:18:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjV0eHJJZHF6K21yRUVjZktrVitpTFE9PSIsInZhbHVlIjoienJNUEVRbmU3ZW9FYVZlOHNZb0UwMVBHbEYzRFQva2cwMG1yYXJzV1EzNTZLT3l1RFd4MnUvdHBoVHpOd0VjRHJHQjJrSHBHbCtBTHlPNjA4US9zRDhiM2s2VFRrdVpNV1pJdXNmRWpidzB5dG9kYlArZVVxVEp5cnUzbnZIRVVOVkYweS9HUGJuR0tmRUFwRTQ3OUVyZFQ4ZFNMUFJDVFdrajV3a3NnTUV1TFgvNXBveW9MQTdWQ2xsT3pkOEFsWlBwVFhNZW5qM2h2UHB3a3BHTWZDeXFQN0FFeXAyMHYzL20zT3ZrWTFGNWNzclhXU09ja1F6QktUMEMwQXVTZlErNmpHR0E3WStlU2Yzam9IU1ZJWmJnVHJKTjFhTm12dEtveHYrWXhQYklyZkR5eTlERUphUVNnMXdobDdVRzhlVUx1cnl4WHFPUHgrNzNrbzRPeWVjek1zaFBRaFFSVXdPTWIydnhVOTBITFVKczZScitPUjFJT1dreTJtRGN6aGZlR1NMMzZjaEZsOFB5SG15NXZNbGI0NWV4NnRVWkpQQjJlaHluRlV6ZnVHMnZQZEdJaklWMnpXTGJHUis4WkJYZTE2S0ZBMlVUeEVYOEtjcGFDNEdsRDliZGxLMmFNZUtBYWVNL0xIaW40dk14ZERaOHR5RnBDaFJ0Q1RraXAiLCJtYWMiOiIwODg5ZjFjZWNmY2IzYTFmODA2MDNjNDM3Yjc5MWFmZGVhNzYxMGVlMTNmYzU4MjEyZjU2NDQxMTU4ODU0MTljIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:18:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitTVnFtM0xremFUZXhMaVlsMDJuUEE9PSIsInZhbHVlIjoiYUwzWjhmZGdkOS9hcWVZd2ZqbG1PbE9WMlFmVW8wa2trOXgwOWNQZytUVWl3VVJCMjZ2SXNSMHdCYWVMVUF6UnY2SFI1b2daUVN6djE5WDV6djRKS2JWWWFQSWkvazJyVnlsQWhHRE9LMEFRc3MvdjNTWm5WdjB0RGgrb2JIWWNmYVR3MHBKaTcyWitER2FlenpsSWt4SnppMndRUkVtUVRjMTl1bkdQYnA2S0lPRXZyVFJpTzhoSnJnWUFYVWg3ZTJUMklpczV5ZFk2TkFKSWgwaUdJQVM0SXY0OW9JTnZwMS93Q2l2WW95WFc5dmtIRURnTGNuQ3FUVTFCelBZdnBkYXdzQVErcm9wbDhkcjBrb1VVQStaWDRpa3UzSEhRbFhiQ1o2MkN1MFZMZHNtYkYvakVRTURONkdkWVFESmdScUxWV2o5bXROR2hsbVE0U3FtMzFvbEJQM3FhVXlrOXhLSXFPOGtDUHgyRFFNQzBpalVMVitRcU4xRFg2NWpkaVFsdTJOaHZrNWZDbmoyVHdCejZPU2g1a21xVWMydmFRQzNvTHZpUHBzSEdldFc4Y0hwZjduZkZaM1pIL1cwMzVYbnJUUTJLcTg4UVoyZ0NVMm1kV054VUhVRXcrVXpMdkx0RGFHTFRHUXB1SVZKYjlhTEpEN3JSbnFWOGpteFgiLCJtYWMiOiI1NzQzY2FlMDY0YzA3ZDQ0M2NiNzY2ZWM5OWJlOTljMjg3NGE0MGY3ZjkwM2RiYWRjYzdiNjVhYTlmYjY2ODUyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:18:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjV0eHJJZHF6K21yRUVjZktrVitpTFE9PSIsInZhbHVlIjoienJNUEVRbmU3ZW9FYVZlOHNZb0UwMVBHbEYzRFQva2cwMG1yYXJzV1EzNTZLT3l1RFd4MnUvdHBoVHpOd0VjRHJHQjJrSHBHbCtBTHlPNjA4US9zRDhiM2s2VFRrdVpNV1pJdXNmRWpidzB5dG9kYlArZVVxVEp5cnUzbnZIRVVOVkYweS9HUGJuR0tmRUFwRTQ3OUVyZFQ4ZFNMUFJDVFdrajV3a3NnTUV1TFgvNXBveW9MQTdWQ2xsT3pkOEFsWlBwVFhNZW5qM2h2UHB3a3BHTWZDeXFQN0FFeXAyMHYzL20zT3ZrWTFGNWNzclhXU09ja1F6QktUMEMwQXVTZlErNmpHR0E3WStlU2Yzam9IU1ZJWmJnVHJKTjFhTm12dEtveHYrWXhQYklyZkR5eTlERUphUVNnMXdobDdVRzhlVUx1cnl4WHFPUHgrNzNrbzRPeWVjek1zaFBRaFFSVXdPTWIydnhVOTBITFVKczZScitPUjFJT1dreTJtRGN6aGZlR1NMMzZjaEZsOFB5SG15NXZNbGI0NWV4NnRVWkpQQjJlaHluRlV6ZnVHMnZQZEdJaklWMnpXTGJHUis4WkJYZTE2S0ZBMlVUeEVYOEtjcGFDNEdsRDliZGxLMmFNZUtBYWVNL0xIaW40dk14ZERaOHR5RnBDaFJ0Q1RraXAiLCJtYWMiOiIwODg5ZjFjZWNmY2IzYTFmODA2MDNjNDM3Yjc5MWFmZGVhNzYxMGVlMTNmYzU4MjEyZjU2NDQxMTU4ODU0MTljIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:18:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918646846\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IlRjTW5xSFI4U3lTMGNZTEV1TTUxTXc9PSIsInZhbHVlIjoieGhlY1BZcTBHOFVESmtlMjJQbGpvdz09IiwibWFjIjoiNTlkZTQ0ZTllYmZiNjYxNTE3ZjY4ODdiMDE5N2NlZDg5Zjk1NmI0MWJiNzg2YWUxMjlkYTAxMDU3NjUyZDI2NiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}