{"__meta": {"id": "X963750e6d4e4229dd7a57a31b80ae4cd", "datetime": "2025-06-27 01:04:59", "utime": **********.922993, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.460403, "end": **********.923007, "duration": 0.4626040458679199, "duration_str": "463ms", "measures": [{"label": "Booting", "start": **********.460403, "relative_start": 0, "end": **********.850074, "relative_end": **********.850074, "duration": 0.38967108726501465, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.850083, "relative_start": 0.3896801471710205, "end": **********.923009, "relative_end": 1.9073486328125e-06, "duration": 0.07292580604553223, "duration_str": "72.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45027104, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020599999999999997, "accumulated_duration_str": "20.6ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.87949, "duration": 0.019719999999999998, "duration_str": "19.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.728}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.908308, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.728, "width_percent": 1.65}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.914391, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.379, "width_percent": 2.621}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/chart-of-account\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-967947002 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986296729%7C78%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5JTTlNMWZtdzhBdTB5MGMxWXF6UkE9PSIsInZhbHVlIjoiYUVUTkF2NS81RnNITHgvSUxiOUhSUlduZ3dNS0gwaEEraDhWby9EcmNCUkVxUS91RHRaOEQ1QWZ0ZGY0bEluWC92ME4vc3IrbzhZc2JzdzNieE9pWEpMa1gvblNrZ1IyUTZtRzBzd1QyQ1RzZ3Zta2ZEQnVJZ2hTYWxXRVNJWkNjdHJrN3N1dmxuVTR1eFFWcHdGR1FmUm1BdENpRGY0VnpNZzV1ZUIwTlBEZFc4Znl1WTdTblkrdjkxd1R4aEF6UzF1MFVXQ25NaUhLc1J1aUtSeFFyQ05aNlN0QStVazlnTEVtdzFUdUt5NU9xbVMyNk1XcTFqb0loYUYrTkFGVFBaVWQ4YTJndkRjZG00MktCRjRVeldyWGNTSjk2bzRBNEgzdTNGQ0RpVnQ1VzlPNEJxUnNXdjB6L3M0MHg4NjRNOVFhNmVmZk9PcUlJT3FLb2REczZON3J0Y0ZTMzBHTGEweGJXa2cxOU1SaXIxeHNENnZwQkVJQXdsUDcyWFhlc0JCaXB0cG5tZXdZK0pYZHJBcFF0QjJUU1plclNzZnFxd3BXUlVhRXI5VmtHMmIveUlDMkdCVG15ZmxnMjA3WUtTb1lxRmdlYTIza1RvSnpvYWxYVzFPcTNKTU9SVlRmWVRYVnVHN0d4MnRRSGY0WW1NQlhYd3FHVy9kT0JnL20iLCJtYWMiOiJhYWZjM2QwZDUwYzRhZTMzNWMxZmE2NDg3YmM5YTRhMWM1YzRiNmUwM2Y3NDQyNWNhZTBmMGQyMjU5YjA3Y2MzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlHcHpJUXE4WWxyYmQ0TmJUeC80Q0E9PSIsInZhbHVlIjoid2ZqU3NzTkRycU5qUlg4WHlZcjFsd2xLeHd2Z1hMM1NvM29Mc255aEEveHkzcUpLTU5SUFZNbnozbm1rMTN5bC9oUWFHdUJHeEhwU1c5dEhUcDRGSmNabFBCd0pKdi9wOFZxdkhJZW1pY0hiRzNpaHkxSW1QV2UrbzMrZEV2S216dTQ1QWorVXRVTzlWUXYwdWRsRTJmekhoNno0WHVhVnk0ZnBkV0ZLUEtNblJ0YmVocVFBVkxDd0hnSXlMcGlna2xFbm1aUk51Z2djYU1OZlkxRG51eHR6OE9YT3QyWjNLTWdCalZIYllWVkU1V3RlT1lSeTJyWDFLT1UwQm1STHlPUEtHdGhVZi9pc2VtNTUzTWUxMXVETDZWZTNISHkxSXV5MkpPZzUxV2w2RjRtQ09sZWJYRzNmbFhrTHVMNjJFQWhaQkNuNFVMUXV2MVhKbTF0WDlwQW8xVXlremt3bHE3SUU3SWZ0NEpaZ1JvL2FOQUwyNEhBM1h1MzRycUwyaDRyd2ZGMlZaUXV4YzFkRG1LNDdmWWFvQjJlWUpMYVZxRlJBR29oaWRlWTNmQWxURHl0bnhwWGZkRlRGVnBtRFlwdm15enBTWmZkb3NqdGFENmZMTlR2Z1JuSFA5cnlqdlVQR3FoVGlvaWtVUklLQ3hpKzZVK3pDWHEwc1J1ZmUiLCJtYWMiOiJmNzY5NjM0ZGMyM2E1ZDI0NzFkZmI0MTE1NDY2NzE4OWU5Nzk5ZDc4MWE5NjNjM2U1YmFhYzJmY2IyNGFmMTQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967947002\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1889301548 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889301548\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-622103724 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:04:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVQUjF5RTdpM1Y5ZVl1SkplTjdhbWc9PSIsInZhbHVlIjoiTDZGeGtHS3VSQWkydjRKbmRuaXhlNkVsUkZqR2cyUnBLNGVYbXB5TDhWRTZvUmt4Q1JQdnpSQXRSWjJMNlFaOENINUpNOHBEOWdycUlnbUpzQWVHU1lnc2NrTXhycC9vWnNjZy80VC84RWZXMmpOWGhGMVUxZ0p3cjU0cTdjYVIxMUhuOEt2d29CNjlFZ3dWNnh1Tm1lVVRXR09mUmthYzZaTm9pRkVqM3NTUVUxamxRZHB5NnJqY3FPd3h4aXRUcUhReklkRnIrSUQzbzBQU0JTRlhINnZyeEFRenhnVElUL0NKVjhNNFNLd1h6LzNlSW5jK0xvZnRJNS9yVThMOTRxbHU0SG96bCtoYzdvN2tuRCtIaERjZUhCT05LVG52ZzdQOEI4WHRzZUdGWlorMGpIczY0S2ZvamUyc21mWWt1cDZpMldPNmRsczhPVExlRW8yc2NHQlYzWGZCZldXWUlUejFSVXB6b3V4bGVVdkU3eE4xVzl2VmNZdjN5amlqZld3cnI3UnJYVHpWaWNEZ3ZWMm53dVZqbVVkVmtjR2tCS2UzSnh3SVlkdDQzaWhjS2lhMFBuVXZRU1ZmMHlKVUV2blZZbHZTa3F1RmZwUm1lSDMyN3FzenBGRTdremdKa2UrRGFkRExPN1MwREVBVkpKaUljbzVPRlhOcVU0SVEiLCJtYWMiOiIwZTk2ODE5YWIyZTRjNmE2MGZiMThkNzg2YTJhOGVmZGUxZDE5ZWEzNTMwYTQyOWU0NmFkNzk3YmNiOWQ2OTE1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:04:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJ0WUpaMWVWNnhwTWZla3RTM2tkSmc9PSIsInZhbHVlIjoiZVhIbjBuS0hMRW8yR3VVU2wxYUUzMVZoUHRzRUhFSHB2cGNBYTVRazQ1ZmhaTnBnTEh3L3NFNzc0eDIvMm42TUg0a2dXVmxUUkVFRXdEK29lRnFkdlBWOTl2bGpkMWx6REhCdFUvRnBKTmI5NHg4WmVxVEtKYXV3bXB0aTNJV3B5SHdOb3hqbEgvb1hibzhreWNrdkJsejAwdldVb2hsVnZCZ2k2OWxRVXlPSzRrUmswTDJkWnNlWDBNUFl1VnFXTXV3RnlYSzJuVGpkQy9tNHk0czZzRy9Zc1dJMW1EM29rdGhVcjJZQzZRdlUxOXRzRnFheFZqdWlZYkp1QjNZOGRkYmhnRkdCUm5vTmR3SUlRZzU5OGZIQXNWTHIyQWJ6R3JkMFdWYzA2ZE1lOTdXbFFRMVdIZXZKRzJsVUMwTmFKTk51U0NNdUFuWTZnbmFpVWtHeFVyV1l2Q28zeHpHZVlnZ3lTMCsybWJyR2F5MGVUTGgyY3RtbWU0TGJaZFhWZE9MOEJWOFdmM2R5N0wvV1FsMTgrdWhvU1g4MkVpejl5OFhhSkFWdW5aSkVJdHkvWjUwS0kzNDVSOU9KNkREc2ZiZW5zOU9xVFdrYUlqRnJYWFFUaitQUDJSdUQrOUFEL0x0ZytzWUt2QTdKRVUzcHF4KzNKSkZDUXh3R1JkdS8iLCJtYWMiOiIxNjRlYWI0NGMwMjQ1ODcxMTcyOTUwZjgwNmQ2YTYwM2Q3MTBiNmNiNTdhMGE5NmY4MGJhODVkYWYwMzIzNDEyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:04:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVQUjF5RTdpM1Y5ZVl1SkplTjdhbWc9PSIsInZhbHVlIjoiTDZGeGtHS3VSQWkydjRKbmRuaXhlNkVsUkZqR2cyUnBLNGVYbXB5TDhWRTZvUmt4Q1JQdnpSQXRSWjJMNlFaOENINUpNOHBEOWdycUlnbUpzQWVHU1lnc2NrTXhycC9vWnNjZy80VC84RWZXMmpOWGhGMVUxZ0p3cjU0cTdjYVIxMUhuOEt2d29CNjlFZ3dWNnh1Tm1lVVRXR09mUmthYzZaTm9pRkVqM3NTUVUxamxRZHB5NnJqY3FPd3h4aXRUcUhReklkRnIrSUQzbzBQU0JTRlhINnZyeEFRenhnVElUL0NKVjhNNFNLd1h6LzNlSW5jK0xvZnRJNS9yVThMOTRxbHU0SG96bCtoYzdvN2tuRCtIaERjZUhCT05LVG52ZzdQOEI4WHRzZUdGWlorMGpIczY0S2ZvamUyc21mWWt1cDZpMldPNmRsczhPVExlRW8yc2NHQlYzWGZCZldXWUlUejFSVXB6b3V4bGVVdkU3eE4xVzl2VmNZdjN5amlqZld3cnI3UnJYVHpWaWNEZ3ZWMm53dVZqbVVkVmtjR2tCS2UzSnh3SVlkdDQzaWhjS2lhMFBuVXZRU1ZmMHlKVUV2blZZbHZTa3F1RmZwUm1lSDMyN3FzenBGRTdremdKa2UrRGFkRExPN1MwREVBVkpKaUljbzVPRlhOcVU0SVEiLCJtYWMiOiIwZTk2ODE5YWIyZTRjNmE2MGZiMThkNzg2YTJhOGVmZGUxZDE5ZWEzNTMwYTQyOWU0NmFkNzk3YmNiOWQ2OTE1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:04:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJ0WUpaMWVWNnhwTWZla3RTM2tkSmc9PSIsInZhbHVlIjoiZVhIbjBuS0hMRW8yR3VVU2wxYUUzMVZoUHRzRUhFSHB2cGNBYTVRazQ1ZmhaTnBnTEh3L3NFNzc0eDIvMm42TUg0a2dXVmxUUkVFRXdEK29lRnFkdlBWOTl2bGpkMWx6REhCdFUvRnBKTmI5NHg4WmVxVEtKYXV3bXB0aTNJV3B5SHdOb3hqbEgvb1hibzhreWNrdkJsejAwdldVb2hsVnZCZ2k2OWxRVXlPSzRrUmswTDJkWnNlWDBNUFl1VnFXTXV3RnlYSzJuVGpkQy9tNHk0czZzRy9Zc1dJMW1EM29rdGhVcjJZQzZRdlUxOXRzRnFheFZqdWlZYkp1QjNZOGRkYmhnRkdCUm5vTmR3SUlRZzU5OGZIQXNWTHIyQWJ6R3JkMFdWYzA2ZE1lOTdXbFFRMVdIZXZKRzJsVUMwTmFKTk51U0NNdUFuWTZnbmFpVWtHeFVyV1l2Q28zeHpHZVlnZ3lTMCsybWJyR2F5MGVUTGgyY3RtbWU0TGJaZFhWZE9MOEJWOFdmM2R5N0wvV1FsMTgrdWhvU1g4MkVpejl5OFhhSkFWdW5aSkVJdHkvWjUwS0kzNDVSOU9KNkREc2ZiZW5zOU9xVFdrYUlqRnJYWFFUaitQUDJSdUQrOUFEL0x0ZytzWUt2QTdKRVUzcHF4KzNKSkZDUXh3R1JkdS8iLCJtYWMiOiIxNjRlYWI0NGMwMjQ1ODcxMTcyOTUwZjgwNmQ2YTYwM2Q3MTBiNmNiNTdhMGE5NmY4MGJhODVkYWYwMzIzNDEyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:04:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-622103724\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}