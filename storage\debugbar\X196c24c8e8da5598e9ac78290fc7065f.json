{"__meta": {"id": "X196c24c8e8da5598e9ac78290fc7065f", "datetime": "2025-06-27 01:15:25", "utime": **********.948664, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.524403, "end": **********.948679, "duration": 0.42427587509155273, "duration_str": "424ms", "measures": [{"label": "Booting", "start": **********.524403, "relative_start": 0, "end": **********.893903, "relative_end": **********.893903, "duration": 0.36949992179870605, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.893912, "relative_start": 0.3695089817047119, "end": **********.948681, "relative_end": 2.1457672119140625e-06, "duration": 0.054769039154052734, "duration_str": "54.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45027056, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026100000000000003, "accumulated_duration_str": "2.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.924931, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.153}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9353101, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.153, "width_percent": 19.54}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.941416, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.693, "width_percent": 20.307}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1095412736 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1095412736\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-657525939 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-657525939\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1813076842 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1813076842\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986905698%7C91%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjU3SGQ1RkJqa3lMQzAreUUySzdWRHc9PSIsInZhbHVlIjoid2ViaFVzQkFSc2daQVNZa2xBVytmcnBLY2dKRkVZYnRybytDQUd4YWZZODROemNHYXFPTm1IVms2NFJnN2g4RlREeWIxb3RzZS9PRmRkeG5zb1JPUUZtRUtGYjJJNDdUNXE4RTFMaDJXRkQ4WDhzbHJvUUljaFFqNGNWOHJOMDMwS2ZPdWRwQVFTaUZUQjU2QlRvOGJYUWFmcGRoM1d4a0tCTm5TT3FpT285WXo2enF1N3NZQ3pTT2NwVEF2eVI0MHc0TmpFeTFQYmx2RnVoMk5Ja3QrSjlWS1dEUXNkUDEyWTR4cWRoQ0xPSm92bXp2cWdxUmN5RkMvRlZPbkszUk5QR0FKR01yRGw3NXVXK3dPVGxCa2ZoOGw5bEpxMU5iTjkrcHBKcTZtWlFDbXdNMGVTQkU5YkpOeG80NmZvQTdmNXQ1OWpSYkU2TXZCQmgrbjJkaHZ2ZW8vSWQ0YW8wUWtsQXQyVm9sbTVOWUhuQmg2ZGlZcEpPSWNhSEZhajhLZUF1NHJkUFNIRG9nZ0ttcXlSMUVIOEc5Vmdzd3M3NytJZG1CQWdiZWttMjdLMzI0azZoZ29nWGtlYkgwb1RlUmJMeVV1WnB2Qk10RC92dzV6MWttVUFncXJJT3hEU0U0ZjdwNHBDZVdoRVJFWSs2WE5FVTBqM0swRmNXS0ZJVEYiLCJtYWMiOiIyYWZmOGM3ZGI3ZGU3ODk4NTFiMTM5ODJhZGI1MzdiZjg1NzI4NzJiYzMzODE1ZDBhOTk4NDYyZDMyMjJmNjRmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlRpdVM5MnNleVczMFUxeGF5SnZMbUE9PSIsInZhbHVlIjoiOVBGMHJUWGlvZy8yL05OZjgvNC9OeFN4cWRjWmUrdE9YMHBJdERpRzNSZEtrdzBKU3ZhM3Z0VDRPM1ptRER6bGFtZlBwbVM5ME5VaWdGQ1FSa1I0VEFYZ1hBRUZvS3RkMWpRQ25yL1MwVmRFclM1bm9RZXh4Z2N6SGpmT29hbmFaY1dnb1luMjZHZXFLWFlaRmg4b0JSVmxZYWRGR1hsS0VHR0VqYWJlZHhGWnhUWUtlcXc5VjN2dXZNUWJCb3pobk9Nc1pDSkxaOXZkMWR4SUJxR0xkZ0lRWHZrbE5MQzFSZ1ZQc1k4SWdjcUJWNHFOTjk0bDZOaHc4U01MRGsvNmNXUU9yQmdWeHE2WEdldUNsb29qeEtzemNPcElpQXVraTUvK3BKK1ZkUkNOMk9hSUI5NldpbjNvZ0ZIbGZsd2l0K21HQ1F2SXh2WW1HNzV1ckQ2R0NLaGk5UVQySlF3UHprM3FhbU50Q1piS0hZVFRkY0VVaEM0K3Uxb0ZuRExWcGlZT2pEM0NmUjBOZHVRUDR0RnVXRktYYnZNalpTK0pZRTJZSTJyZFlSdEIyU1MxTm5uOUNUdmdFVk13Y0FjRTR5bHNyYnJBVVk4UGhERVl0b2w0eFUwVEJNbmQ0dW4vemUwQ0QxbTY1elFucFRCQ2tTUnlJeTRPajgvZWI1VkciLCJtYWMiOiI0YWViNGNjMTU4YWU4NzhlNjE5NGI1ZGIwMDBlNGM4MTRkN2Y5ZGYwZDE4MmY1ZjNlYWEyZDhlMWI3ZThmNmM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-443342779 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:15:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFxKzVKWnUvem1HRmlQa0V6ZDUyUXc9PSIsInZhbHVlIjoiMGlNOUwyaVFmL3ZPcGxnT3Q3a05OSjBNT3Q4SnZwd0lDMHlwVjVQSFlWbUZwckFkNGxaOStXSjNkY21yRHgzcnhhdUlWTUROby9iYjdsTmtOZW5kK29MN0p4dS9xTWtpa1FDcnU1eThJTlA5ajdLKzlQelZmbWgwWi9yaW9iWTNrRVRSMkRWeDFiOGlpNzZmU3N0S2FDSE96MVVqZkloMkEvRGVxS3hINWd1SFpjQjVGOW1PYU5DNHFZcVJPVnlmOWxVaVpqVFpDajhva003ODg0bUtTUER0RktHNDZySEFVYWEzWnV6YXdwYlpYVDZVZWg4UTlVeGFza0gwdFhrSDh2amljc0w1bm93VTRNSkJSMTV2WEQreWN0ZDJ1QVJ0Z3pQMmM2bkRTZFRCZU1xSGJjLzhaTVRpeDlrTkhKSVBtRGsxTkIrMTFGbVQxY2MveDZObjU0b2RzdjZ4NkdlbkxqM3dmSDZhZ3NzY3pOYVJrWi96ak9CQzVRL0QvVjFrdHhEcW1aVmpjdFJpeTVCbmdiaWVxS3gxSTFGZFJrVU9MSTBhV2llRzFaemh1Q0NqczR2ci8wTHNuWloveU8xRVhCMWtEQUloK0o2OWNQd2N6RUxtN1BReHdjdEtLRTQzUitRK2xSVXA0Q01ob0UzZzkvZ0hwcUdONFFYRlZlakEiLCJtYWMiOiI1N2Y5NzQ5ZWNmMDM5NWEyYWExZTJkMDJjNzAxZTE5NzZmOTRiZTI0YzY2OTQ2YmZmMTBmNjMwMTc2YWYzZjljIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjBjaFEwTkZ0MTlVL0VkZjArdFU0OWc9PSIsInZhbHVlIjoieFVWb1pkaVJBT21EZVY1SE5qUUM4RG5nbGlCaHB0Zis3b2grMktaSVg2T1BzV09FRnBETFAxVGtDM1Z3bUljTDRpMXRqTU1PSmJYalQzdFRVaVE0WStNTVUxUGVXL2JRN0tJSkd0VUNsVkk4Z1RvdHE0SEhLQ2ZlU25Td2RFQXppcWl6Skl2VG9nU0V0eWoyamVldEpDWUN3OEx5UktGS3gxdC83RDNRdEd3Wld0ZndYQ2ZpYkt3aU1wUk1tdUg0NVVvd1UyTW1oNVVlUzc0R2VDTlhiUDZiMkZrWHpYMCtJZTlMWVAweXVqRjVxSVVWVHBuZEJ4WHhBQXo0Q1Vsa1hSb05CQVlJbTVLa2VRVnVmb1hrb2swU3R2U0pqa21yVi9HUUk2M3h1MG1mRW1UVk5tY2tjNGVoRlkzc1lyWlNhYk4zNTRNU0V6ZlltYnd0TWk5bkhNcVdFclRYam9IUG1RczlYdjc3a2d4Ri9kT2cvM3FXQk15Zzg5clFEd2NpbUhQdUtFa0llc1ptci9VZ2Fla05WUVdhdC9JWDA3U2tLZ3k3TVMwd2V3R3I0UGtzaEtHMVBMdWtGTldYc25VYStzWG02QktqRjBISTdDTFkwR2dDRENoMDBJbUtlYU41ZDdNMTFVdFJGSnV5OWtrZys2NHprby9qQ0tSK3ZaNnoiLCJtYWMiOiIzZTQzNTZmYjkxOTEyNjQ5Y2I1NWVjMzM4NmNiY2VjZDU1MDZkOTcxZmNjN2JmNDI5ZWI5YzAwNWJkZjhhYzgwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFxKzVKWnUvem1HRmlQa0V6ZDUyUXc9PSIsInZhbHVlIjoiMGlNOUwyaVFmL3ZPcGxnT3Q3a05OSjBNT3Q4SnZwd0lDMHlwVjVQSFlWbUZwckFkNGxaOStXSjNkY21yRHgzcnhhdUlWTUROby9iYjdsTmtOZW5kK29MN0p4dS9xTWtpa1FDcnU1eThJTlA5ajdLKzlQelZmbWgwWi9yaW9iWTNrRVRSMkRWeDFiOGlpNzZmU3N0S2FDSE96MVVqZkloMkEvRGVxS3hINWd1SFpjQjVGOW1PYU5DNHFZcVJPVnlmOWxVaVpqVFpDajhva003ODg0bUtTUER0RktHNDZySEFVYWEzWnV6YXdwYlpYVDZVZWg4UTlVeGFza0gwdFhrSDh2amljc0w1bm93VTRNSkJSMTV2WEQreWN0ZDJ1QVJ0Z3pQMmM2bkRTZFRCZU1xSGJjLzhaTVRpeDlrTkhKSVBtRGsxTkIrMTFGbVQxY2MveDZObjU0b2RzdjZ4NkdlbkxqM3dmSDZhZ3NzY3pOYVJrWi96ak9CQzVRL0QvVjFrdHhEcW1aVmpjdFJpeTVCbmdiaWVxS3gxSTFGZFJrVU9MSTBhV2llRzFaemh1Q0NqczR2ci8wTHNuWloveU8xRVhCMWtEQUloK0o2OWNQd2N6RUxtN1BReHdjdEtLRTQzUitRK2xSVXA0Q01ob0UzZzkvZ0hwcUdONFFYRlZlakEiLCJtYWMiOiI1N2Y5NzQ5ZWNmMDM5NWEyYWExZTJkMDJjNzAxZTE5NzZmOTRiZTI0YzY2OTQ2YmZmMTBmNjMwMTc2YWYzZjljIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjBjaFEwTkZ0MTlVL0VkZjArdFU0OWc9PSIsInZhbHVlIjoieFVWb1pkaVJBT21EZVY1SE5qUUM4RG5nbGlCaHB0Zis3b2grMktaSVg2T1BzV09FRnBETFAxVGtDM1Z3bUljTDRpMXRqTU1PSmJYalQzdFRVaVE0WStNTVUxUGVXL2JRN0tJSkd0VUNsVkk4Z1RvdHE0SEhLQ2ZlU25Td2RFQXppcWl6Skl2VG9nU0V0eWoyamVldEpDWUN3OEx5UktGS3gxdC83RDNRdEd3Wld0ZndYQ2ZpYkt3aU1wUk1tdUg0NVVvd1UyTW1oNVVlUzc0R2VDTlhiUDZiMkZrWHpYMCtJZTlMWVAweXVqRjVxSVVWVHBuZEJ4WHhBQXo0Q1Vsa1hSb05CQVlJbTVLa2VRVnVmb1hrb2swU3R2U0pqa21yVi9HUUk2M3h1MG1mRW1UVk5tY2tjNGVoRlkzc1lyWlNhYk4zNTRNU0V6ZlltYnd0TWk5bkhNcVdFclRYam9IUG1RczlYdjc3a2d4Ri9kT2cvM3FXQk15Zzg5clFEd2NpbUhQdUtFa0llc1ptci9VZ2Fla05WUVdhdC9JWDA3U2tLZ3k3TVMwd2V3R3I0UGtzaEtHMVBMdWtGTldYc25VYStzWG02QktqRjBISTdDTFkwR2dDRENoMDBJbUtlYU41ZDdNMTFVdFJGSnV5OWtrZys2NHprby9qQ0tSK3ZaNnoiLCJtYWMiOiIzZTQzNTZmYjkxOTEyNjQ5Y2I1NWVjMzM4NmNiY2VjZDU1MDZkOTcxZmNjN2JmNDI5ZWI5YzAwNWJkZjhhYzgwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443342779\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2131796175 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2131796175\", {\"maxDepth\":0})</script>\n"}}