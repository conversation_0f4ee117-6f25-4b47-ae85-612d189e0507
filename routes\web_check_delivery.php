<?php

use Illuminate\Support\Facades\Route;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;

Route::get('/check-delivery-permissions', function () {
    $output = [];
    
    // 1. Check delivery permissions
    $output[] = "=== Delivery Permissions Check ===";
    $output[] = "";
    
    $deliveryPermissions = [
        'manage delevery',
        'show delevery', 
        'create delevery',
        'edit delevery',
        'delete delevery'
    ];
    
    $output[] = "1. Checking delivery permissions:";
    foreach ($deliveryPermissions as $permission) {
        $exists = Permission::where('name', $permission)->exists();
        $output[] = "   - $permission: " . ($exists ? "✓ EXISTS" : "✗ MISSING");
    }
    
    $output[] = "";
    
    // 2. Check Delivery role
    $output[] = "2. Checking Delivery role:";
    $deliveryRole = Role::where('name', 'Delivery')->first();
    if ($deliveryRole) {
        $output[] = "   ✓ Delivery role exists";
        $rolePermissions = $deliveryRole->permissions()->pluck('name')->toArray();
        $output[] = "   Permissions assigned to Delivery role:";
        foreach ($deliveryPermissions as $permission) {
            $hasPermission = in_array($permission, $rolePermissions);
            $output[] = "     - $permission: " . ($hasPermission ? "✓" : "✗");
        }
    } else {
        $output[] = "   ✗ Delivery role does not exist";
    }
    
    $output[] = "";
    
    // 3. Check current user
    $output[] = "3. Current user check:";
    $user = auth()->user();
    if ($user) {
        $output[] = "   User: {$user->name} (ID: {$user->id})";
        $output[] = "   Type: {$user->type}";
        $output[] = "   Roles: " . $user->roles->pluck('name')->implode(', ');
        $output[] = "   Can manage delivery: " . ($user->can('manage delevery') ? "✓ YES" : "✗ NO");
        $output[] = "   Has Delivery role: " . ($user->hasRole('Delivery') ? "✓ YES" : "✗ NO");
    } else {
        $output[] = "   ✗ No authenticated user";
    }
    
    $output[] = "";
    
    // 4. Check company role
    $output[] = "4. Checking company role:";
    $companyRole = Role::where('name', 'company')->first();
    if ($companyRole) {
        $output[] = "   ✓ Company role exists";
        $companyPermissions = $companyRole->permissions()->pluck('name')->toArray();
        foreach ($deliveryPermissions as $permission) {
            $hasPermission = in_array($permission, $companyPermissions);
            $output[] = "     - $permission: " . ($hasPermission ? "✓" : "✗");
        }
    } else {
        $output[] = "   ✗ Company role does not exist";
    }
    
    $output[] = "";
    $output[] = "=== End Check ===";
    
    return response('<pre>' . implode("\n", $output) . '</pre>');
})->middleware('auth')->name('check.delivery.permissions');
