{"__meta": {"id": "Xf43e233a59617d7a1d9bd9a9237f879b", "datetime": "2025-06-27 02:27:39", "utime": **********.545554, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.082357, "end": **********.545568, "duration": 0.4632110595703125, "duration_str": "463ms", "measures": [{"label": "Booting", "start": **********.082357, "relative_start": 0, "end": **********.479745, "relative_end": **********.479745, "duration": 0.3973879814147949, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.479754, "relative_start": 0.3973970413208008, "end": **********.54557, "relative_end": 1.9073486328125e-06, "duration": 0.06581592559814453, "duration_str": "65.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45734992, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01417, "accumulated_duration_str": "14.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.507781, "duration": 0.0132, "duration_str": "13.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.155}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.529773, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.155, "width_percent": 4.517}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.536429, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.671, "width_percent": 2.329}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-335390086 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-335390086\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1512696270 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1512696270\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-489312358 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-489312358\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-307267611 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991245628%7C35%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtIaVdNaXlKMDBoSTNLMW16NFBiV1E9PSIsInZhbHVlIjoiVGwxWFE0anpnN1c4bVBOa1UzOVY5c3JndFJkWkxmdmRLOC9qRHl2V2FHZGYyc3BlWEYxOUpUSXRhZUZMSzRFa09KS3lXc1JRYXZrTWprR2R3WWlaWlVhdGxFOTZWNHNHSytXZnpaVXhaN2psTFJsUFJxWW1CMmlvOHBLaE1JdStoREVSanlpS3VMWjkzUURjUG9waEs1YzZwRmUwUlVMUjVtVGhPSEQybVBuUUZRdHo4TmNiWWFlTVRNN1VkUG9SaU5JTG90VStXOGxsZlB4dmhOa1YxWWpmTHRaM21wNVMxYzZ0QkMrRGVQYmMrbWw4TVk0SGxDZC9kUml1a0pybWhNV3NzU1NnRUxUMWV5YTRxQ0lLaUw5amgzTlo4dE8zK1I4QUEvb2VmbG1UK1pwSERsYjMzR21IL0NhdjZjanR2U0hhZjJvY3ZBMVhHRXoyakZKcjdOM084RFF2MzdwRnMxN1Z3ODc0MU0rbVNnRUtWblpySE5JL3dvUWFmUHlrWGNBV2x3ak05eGg2YXYrSHhoQ2dGR1BkaWtMODR2MjdZMU16bllScWkxSytIaUs2dTRFWjhoSjFycGdJYVorQkJhWnZRWThRbVVYalhyUjhSUHBESFRRb29GeFZPUk5ibllXYnQraHNnNmR4Ykl4R0pwZVR4NFo5ZHl2d3J5eHAiLCJtYWMiOiJiNDkzNzk4N2FjYTBkNDcyOTYxOWFlZjRiNGRlOGI0YjBmYjFlOGRmZTJmMmZkMmY4NDYyNTQ0YmNhZDZhZjI5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhFK056VGVIVDVuT1A4U1liZHJIZnc9PSIsInZhbHVlIjoibkp2dkNRbkpCcjRwMlNUcjhIV2JnU3RuY3VtMWlYV1p4RTdlZ0E4SVZCM2Yxc3A3U2t4dlpLUVFwSXFIMUFmVzJXNDNzdmMwZXNCZmhWT05XaGRYd29nb2ViWGFBaDZrcEg0cHB2bFk0VEw3VUgzRytpSGdCcGRiY0VEMis3ZnZ0NHUrRFdlbmZ5MnBGZGsySW53cmtKbDgrSUhkbTZXMU83VzJNa1FLVkxTbS9Db3FrQlVlZFVlYXltVGtFZ2lmQUt5SFI0YlJhd25qR1A3UGpZWmtnWUppT0pvRm9vMkE5Y2szVXd4NWkzRW9sYjlacS9RbmwzMkp1NWVPTWgwYk5wSmpsc01Pai9LSHd2UmMwL2VENGhOQVM1VDdITFduSklkU3NwM1ZzTSt2Mk54U3NEVGM4QXE3NlpZdGNCUC8rdzJOZ0UvSS9PZzI5Tzd5YWl3U1kzeGk0dVg4cG5pTC81eXgySERkekMzclhXT2ZsSGlaeXNJT1VNN2thYVBDU3c3amoxdUFnOGV4VE41d2RSdkNUMmdibk5ZR0w1cStkWHRMVVJDdDJydzVobDVLb0tTTDd2cmd2YU9Vem4rYVNWcEFFOVIrNlprQ241c0NRRDZGRzRmNjltWExNdk02TG1idG9RU2tGU2FQaWtvOVBIUUxBRkNkRmU5am8wYUIiLCJtYWMiOiJmZGU5YzRiOTM5NTJmY2ZhNWQ1NGFmMDJiMzc5M2U2ZmJkYjIzYWQ5ZmNkMjE3OTcyZDc0ZDgzNjQ3YzRiZGRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-307267611\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-241939470 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-241939470\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-434167339 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktFZ2VUVFVibEgzM2dHSzhoQUxCWWc9PSIsInZhbHVlIjoiZnBqeS9kN3BtbVBuRlYxaCtmcVNyMW1VS20zemt3L0hZY2duWXp0SjNBS0JZYXRyakRmRWdyL1F0RmRpSVo4ejlvbUcvS0JoTEZwNGpHQWl0STUrS0c0eldmL2dHQVFQbnNTNTUvT0RWZVROZjVRbHdQckVLbStVWlVvdG5rMGxxNXJVNUh6ZUU3Mmd0WVRqSkNnSE85OE05cU5MWHFrZEg5dEwrYk9GZThLM3lBd0s2czF4SEFFa2phT2dzK0NqRFdxUmFtZng5OWlEYUVNZEkxRHhadE81VGRlQTM4WUpFa1B6M2Q3MHVqWm1lYU9LNDlHTTJnS0dlVEljam9FNTkyRExTN29VR2NTaXNVMkh4Y0xrUlRyeXc2dlptVTgzTVFHNTA1UzE4K0hhMENNRm0wTUZCeW9uVmNEMmJSVkRucUxRTkJkYmNIUzUrb1lLY1E2T2gxMTYvNjVGOUF5NExCVXJSaGltc3FRbG15YWZJUEthVlNMM1BuL2szbmI0cXZLNjUzNDVlZDByaVlmWUdFSFZJa1pUdmp2VHFITGVybkYyUFFUdHozSVM4VzlqN3NZT3lkcnlaTFg4bUd0dHFQc2FwRzRkcGdTQkw5c29ubTBVNVdONVJyN2VvWFNlcEVnOXFXZ081OGpuSEZuVDR3dDVveTIwU3RZenZEV0MiLCJtYWMiOiJkZjgyMmM4NDA3ZDNiZTRhYjY1MTA1NzA0MWZmMTFjODcwNGQ2ZTg5ZGRkZjUwZDgzYzAyNTkzZDZiNmUwYjQ0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkFWZXdhdmlnalNDL3VZS0VBTUZDdEE9PSIsInZhbHVlIjoiLzgvRWFBY0g4M3NGakJ4dVNTUzFlakpyNXVyYktqMWpVOTVJcVp6SXJiTS94VHB2UUV1cmNSVkJpMGxtRWtHdzZiM3ZFZXBXMHd1aUNjTE9LZktXS3hsQkhYN3JVNml1aGxLTFBteWs2N3N1cmJzMlowQ2FJTGN5elFNSjErczNIZTQ3NzFNaGlnaWFGOHJoVmdRdFJFdnZPSldKUmUzaU5WWStOTWt4RzFHamo3UWJIZzRrK1pwMjVKa0o2Z0haL3dNby9pd1laWGNYcWNabkdqUXE3amZlTXplNk0vZlNtL1RPWE9tMmlNcmpTYUVkL1FXN3NsdmdrUU1NcnVzSmkzQTBHOTZhN1VTc0RVVUsvWXJ2ZFRkT1pwRTF6czFOOGdFd0JheHhlK01kRjJYMkZYSTB2Z2NnM2thR3g0RHFRTzkrVHVVTkhram5QS2lJV3hlVmpPSXZyajEyQ1ZIWnpBbUh5VjdGbnUvMDMzL0pQaE5EKzN0eEh3eGMyQUVEcVhUKzQyQ0VJNWc0WVYwUWdmdmZFdFFvM1E3ZXVxNWI4eCtmaHczOEh4RU1FZ0xMYjNMSW5VdEFFeXFraEttem5FenQycWYyYURJSVZNRVR0UHgvbWhLVm9jcnZkd21tMjB6MDgxNFhoTzU0OGsrNURINEw4K1o3bTEzYkJYWWUiLCJtYWMiOiIyMTc2MmViNTE1YTVjM2ZlNGRkYjI5NjZkZGQyM2VmZmMwNWJiY2RmY2ZkYmYxZmZmODc3ZDgxMjg2MDM1ZDRkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktFZ2VUVFVibEgzM2dHSzhoQUxCWWc9PSIsInZhbHVlIjoiZnBqeS9kN3BtbVBuRlYxaCtmcVNyMW1VS20zemt3L0hZY2duWXp0SjNBS0JZYXRyakRmRWdyL1F0RmRpSVo4ejlvbUcvS0JoTEZwNGpHQWl0STUrS0c0eldmL2dHQVFQbnNTNTUvT0RWZVROZjVRbHdQckVLbStVWlVvdG5rMGxxNXJVNUh6ZUU3Mmd0WVRqSkNnSE85OE05cU5MWHFrZEg5dEwrYk9GZThLM3lBd0s2czF4SEFFa2phT2dzK0NqRFdxUmFtZng5OWlEYUVNZEkxRHhadE81VGRlQTM4WUpFa1B6M2Q3MHVqWm1lYU9LNDlHTTJnS0dlVEljam9FNTkyRExTN29VR2NTaXNVMkh4Y0xrUlRyeXc2dlptVTgzTVFHNTA1UzE4K0hhMENNRm0wTUZCeW9uVmNEMmJSVkRucUxRTkJkYmNIUzUrb1lLY1E2T2gxMTYvNjVGOUF5NExCVXJSaGltc3FRbG15YWZJUEthVlNMM1BuL2szbmI0cXZLNjUzNDVlZDByaVlmWUdFSFZJa1pUdmp2VHFITGVybkYyUFFUdHozSVM4VzlqN3NZT3lkcnlaTFg4bUd0dHFQc2FwRzRkcGdTQkw5c29ubTBVNVdONVJyN2VvWFNlcEVnOXFXZ081OGpuSEZuVDR3dDVveTIwU3RZenZEV0MiLCJtYWMiOiJkZjgyMmM4NDA3ZDNiZTRhYjY1MTA1NzA0MWZmMTFjODcwNGQ2ZTg5ZGRkZjUwZDgzYzAyNTkzZDZiNmUwYjQ0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkFWZXdhdmlnalNDL3VZS0VBTUZDdEE9PSIsInZhbHVlIjoiLzgvRWFBY0g4M3NGakJ4dVNTUzFlakpyNXVyYktqMWpVOTVJcVp6SXJiTS94VHB2UUV1cmNSVkJpMGxtRWtHdzZiM3ZFZXBXMHd1aUNjTE9LZktXS3hsQkhYN3JVNml1aGxLTFBteWs2N3N1cmJzMlowQ2FJTGN5elFNSjErczNIZTQ3NzFNaGlnaWFGOHJoVmdRdFJFdnZPSldKUmUzaU5WWStOTWt4RzFHamo3UWJIZzRrK1pwMjVKa0o2Z0haL3dNby9pd1laWGNYcWNabkdqUXE3amZlTXplNk0vZlNtL1RPWE9tMmlNcmpTYUVkL1FXN3NsdmdrUU1NcnVzSmkzQTBHOTZhN1VTc0RVVUsvWXJ2ZFRkT1pwRTF6czFOOGdFd0JheHhlK01kRjJYMkZYSTB2Z2NnM2thR3g0RHFRTzkrVHVVTkhram5QS2lJV3hlVmpPSXZyajEyQ1ZIWnpBbUh5VjdGbnUvMDMzL0pQaE5EKzN0eEh3eGMyQUVEcVhUKzQyQ0VJNWc0WVYwUWdmdmZFdFFvM1E3ZXVxNWI4eCtmaHczOEh4RU1FZ0xMYjNMSW5VdEFFeXFraEttem5FenQycWYyYURJSVZNRVR0UHgvbWhLVm9jcnZkd21tMjB6MDgxNFhoTzU0OGsrNURINEw4K1o3bTEzYkJYWWUiLCJtYWMiOiIyMTc2MmViNTE1YTVjM2ZlNGRkYjI5NjZkZGQyM2VmZmMwNWJiY2RmY2ZkYmYxZmZmODc3ZDgxMjg2MDM1ZDRkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-434167339\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1399478984 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1399478984\", {\"maxDepth\":0})</script>\n"}}