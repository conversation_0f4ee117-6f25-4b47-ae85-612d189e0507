{"__meta": {"id": "Xe47d14db8b8ab3689b2f0583dd0287fa", "datetime": "2025-06-27 02:27:47", "utime": **********.371704, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991266.905211, "end": **********.371717, "duration": 0.4665060043334961, "duration_str": "467ms", "measures": [{"label": "Booting", "start": 1750991266.905211, "relative_start": 0, "end": **********.22697, "relative_end": **********.22697, "duration": 0.3217589855194092, "duration_str": "322ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.226978, "relative_start": 0.32176709175109863, "end": **********.371719, "relative_end": 1.9073486328125e-06, "duration": 0.14474081993103027, "duration_str": "145ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50947520, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.08637, "accumulated_duration_str": "86.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.255485, "duration": 0.0268, "duration_str": "26.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 31.029}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.290427, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 31.029, "width_percent": 0.417}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('2500', 0, 22, 8, '2025-06-27 02:27:47', '2025-06-27 02:27:47')", "type": "query", "params": [], "bindings": ["2500", "0", "22", "8", "2025-06-27 02:27:47", "2025-06-27 02:27:47"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.300381, "duration": 0.05368, "duration_str": "53.68ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 31.446, "width_percent": 62.151}, {"sql": "select * from `financial_records` where (`shift_id` = 48) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["48"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.356374, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 93.597, "width_percent": 0.521}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (48, '2500', 22, '2025-06-27 02:27:47', '2025-06-27 02:27:47')", "type": "query", "params": [], "bindings": ["48", "2500", "22", "2025-06-27 02:27:47", "2025-06-27 02:27:47"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.358026, "duration": 0.00259, "duration_str": "2.59ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 94.118, "width_percent": 2.999}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-27 02:27:47' where `id` = 22", "type": "query", "params": [], "bindings": ["0", "2025-06-27 02:27:47", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.36159, "duration": 0.00249, "duration_str": "2.49ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 97.117, "width_percent": 2.883}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "success": "تم تحديث الرصيد المفتوح بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-1461488818 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1461488818\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1051165338 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1051165338\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-211431689 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2500</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-211431689\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1518183684 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">68</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IktRZXdqQ0dHdytCRnpoYVNQMndyRnc9PSIsInZhbHVlIjoiVmlTa0lzcUY4ekpQd2xHUkFSeEV0NEptTUlwSFk2RHZtNTVVQXdpcjh2Z2tsTDF3eE1CVkp6RzQ0bzVxRmJ1NEFIcGVuajFRc0ZWNXJEYzVBWVExR0M3YlFNU01TUjRzTUs1dWIybEJ1Qm1kNk1Rd2lDaU1rMnhocWJmZFp6Uk5NN3h5K1BYK2xPWXdpd0xXKzkyS0VOTW1mYU8zd2dlMWxWRmpERmV4YU9QSXIvOUxKOGxzdXdUcW11K2RtRkJyYmQzamlUV2JKNkc3RnNtVG5rRUp2dFQ4d0cwSkJmMlM3ZkhHSDI2MU5QK1ZKRXVUQ3pCdWhCL2JDNDN6b1lZRUd0OWNDUkNLQkRVVnlIcnVjaFpOcDEzbEVLU245VjFXMUFGT3lhTWQ5Wk1WY09UUmdkZ0NYZTdrSjIxclF6aitoNkcrcDRXZFZkWDFnRVozNEhWbEVRTUhBQ0E5ajNVWm9aODJBVXJnWlNTaXZRODZLWS9YbTZIRjV3RzgyN0ZibS83TEpIQnFQMlRtTWpuc3MyMTdPMEtTOEljQXdWeEpjemZlWWd5dkZ6aVFGT0NJd0gvY29QeXFNSlJZT1hxT0ZQZHMrSUhWUkFXbGtraWJQUm51UHNiQ3YwbFlwU3R4MGdpWGRqNy9sRlN5VmJCS1NnK2RaY2doM3NXQlpWbVEiLCJtYWMiOiI5NTZiZjhmZmQyOTM0YTcxOTUyMzUyMzI1NjNlYWVmODg2Y2Q2Zjg2ZmI5OTY2N2Y4ZmZjZWFlYWQxNDJjMGJjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImV4b0N0YmFFR3VZRGt3UEhtZmphaUE9PSIsInZhbHVlIjoiTzdPc0kxVEUzUVNOVUpSVTB0ZmNZMWpUZU44Q29yWnFyMWNkOXBkeE1RejVHTmxQUWk4YXJFR1VNMG1HZEdDZDJBYmNYMjM4cDJDTC9ETzZuNFVpMGN3U2dadXA0b3JuYmN1a3k5NXpXcWdrOE4xZFA0Qk95TGE5L3hsL0pMYnZDYlVTdG4vb1FqSXY0K1hnMzBsRlhPaFEvNVJJWmt5L29PZTVQRUU5THdDamVBdGJ1dkFtR2F4SHNIakxEbVZSWVdNamlTdzFkaEtKcDdkLzBoeU05REsvMTFwSjhqV2RjcnVINnl4QVlaV2xuZGlOaEpPekdxcmxTbXB4MjB0dXBxUmlzMTBmd0pwSW1VUUpPbDVFeEhhT21RMmdWaEpRMXI5ZHIzVnJNMGtVVVpBZXQvNjlSem8vSFFsQ2d0cUlLVmNhazVzdnprUm4zRkNSYVhyaUh1T1I1VkYwdENBdVovKysyczZtMVVYSDZDL1l4anBlc2h3TzFzTHBsVlVrTlpJLzQrR2g1djZTMnFtQnpDSDI2bFF2STl4NVVZZjNYS1dOekZVUGk1QzJkOW9vWGIwOVBSZUVYckF1ejcwd2NrRmhTaHdyQzI1ejZCbWVhMHFoWkhaMWU1Z2VkMDlnNWdHWmV5K0pRbTFKblAvYWs1bldCdjc0OEhrRGV5ZDAiLCJtYWMiOiIxMjAwMmU3OTM3NTc0Njc0ODkwMGZhMjU5MmM0ZTBiZmEyZTkxZjI1N2Q3MTM0NTE4ZjJhNTNhNWFmMzA0ODM1IiwidGFnIjoiIn0%3D; _clsk=1wa64lz%7C1750991263527%7C37%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518183684\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1252005501 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1252005501\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-919877097 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imp3eU1ZVDVkREVnRkxiNXg3S3Fud3c9PSIsInZhbHVlIjoiOURwMzcrMm83MitXYXIrSy96OWNLWlF6YXNsYmxFYU1YbVdsSnI4WUl5ZXVLa3QzT3pMTFdWOGNYVU1SaUxjcGRvSTUzUTBraVpnTkFtaXc0TzVjN0JXZzBOYVJJWVQ3Sk9SNldYYVN3K2VjWjBwYlM1R1BvTWkvSmlNK0hxZy85S0RmK2xkaDBoelJ5TjNvT2VBazdycVBpWlRzRTlLZnVPbXhRQ0FmNldMaUVPTzVBeUJweWZqYVlyc1NKYTVGMk5sVUNUWHQxcVMyNVVoQ2hwM1o2SG1QRVRQR0ZpbzZqYWdML0Z3NmNzZXRwQ2haY3NoUEdHVjU2VE9FbU9lYjVQQ2NOMGlmN0YxOHgyNW1RUGM0M3VVaWx1UmZtOVRwV0RYTUZRY3NFaVl2N1AxbFA4RzltVThMeEJWM0tqNFpNcVFka3N2dmppTkpNVmJwVjkxTlYrNFFvb1pPT2FzbnNvL2JIeS8wM1NHK0U5NkkxUER5M1NpSDJZek03MG04RWJKak54U29yMjJuTytZWWhUZXliQmpueDl4TFV6QmhQblc3cmJ0WmV5bFRadFNPRERoUEFIdU5wQ0kwSzNPVXNTNkpZaEdlR2c4K09neHgvSFNZVnlJaWlpNkIxejlrZDB2U2RWOGJXcG9MQTlQRDUrSXo3bnRpV2RPeTRhSEoiLCJtYWMiOiIzODZkOTg4MGFkMTdiZWFmOTEwNzc2ZjhmMGZlNzk0YWM0MTllY2E4YjdiYmU4YjhmYzJkMmRiYjE5NTgxYjFmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpiNDF4M1BlZm1mU0ZaYVZRYXB1bWc9PSIsInZhbHVlIjoibEJLSEVNcVRIcGlqUlhXZW40N0hFNEhqYkREazVQWkJQczh5V2pvelFjTEZ6aUdoOVY2TklhMTY2QXg2MElkSXFNZnROUk9oL1lPVWtYVUJuYkNUdnpST1JmQmpvUXdSbmU4eXNIMXlCRzExU2lYd2dZZmlzSFVxejNndWY3SzlDYjVndGliT0dMcEtnblVuaTYzWVV1cUpuaFcyWG1xTDE1RVhiZ0JZNWRzS1daajlCZFMrY1M3NUhaMU15MjVCTXh3Z0FQaFBDMUNtWG5CS3FVM1dpL25yb0pPODJMcWE2NlhVa2V1NUdCZitINlJOS2NJa1hJd1FEMDBQSUgzaUx5WVQxR2dGT0ZyRWtMVXhQRnZhUFFlZHJlMThVcld3d1Y1Z3dHYXo4UUF5MERWbVZkYkFGTTNRYUVPZE1RSTk5Q1ZBVzY3SlhaUGp5R1RmUDBpUmNHR21ON1BNZWR1cnZreFhCam95dzdFMUdrdStKVWwvOU9ob0dVZWE5d2puYm91eVdsRmlsVnllLzQyWGdQOG4wTFAzelNUMHMrMENZcmxkRVJFSnZkNi9FMFc3bUFnYStzS1QyUlFNa1ZaMVVHRG5mdzVJTW5kNGZLOWZxT1FIRzdSRnNXZVViUnhNcDFxL3Nhcitqb0xrUlRaejU0cHNlN21TZ0hnbUY3MEUiLCJtYWMiOiJhNTI3OTQyNGU0NGQzYTQ5NDgwMzVmYjVkODZjOTZmNjVlYzFlZjZiYjMzNWY2NGIxM2RiODA4NWY2MWIyZjZmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imp3eU1ZVDVkREVnRkxiNXg3S3Fud3c9PSIsInZhbHVlIjoiOURwMzcrMm83MitXYXIrSy96OWNLWlF6YXNsYmxFYU1YbVdsSnI4WUl5ZXVLa3QzT3pMTFdWOGNYVU1SaUxjcGRvSTUzUTBraVpnTkFtaXc0TzVjN0JXZzBOYVJJWVQ3Sk9SNldYYVN3K2VjWjBwYlM1R1BvTWkvSmlNK0hxZy85S0RmK2xkaDBoelJ5TjNvT2VBazdycVBpWlRzRTlLZnVPbXhRQ0FmNldMaUVPTzVBeUJweWZqYVlyc1NKYTVGMk5sVUNUWHQxcVMyNVVoQ2hwM1o2SG1QRVRQR0ZpbzZqYWdML0Z3NmNzZXRwQ2haY3NoUEdHVjU2VE9FbU9lYjVQQ2NOMGlmN0YxOHgyNW1RUGM0M3VVaWx1UmZtOVRwV0RYTUZRY3NFaVl2N1AxbFA4RzltVThMeEJWM0tqNFpNcVFka3N2dmppTkpNVmJwVjkxTlYrNFFvb1pPT2FzbnNvL2JIeS8wM1NHK0U5NkkxUER5M1NpSDJZek03MG04RWJKak54U29yMjJuTytZWWhUZXliQmpueDl4TFV6QmhQblc3cmJ0WmV5bFRadFNPRERoUEFIdU5wQ0kwSzNPVXNTNkpZaEdlR2c4K09neHgvSFNZVnlJaWlpNkIxejlrZDB2U2RWOGJXcG9MQTlQRDUrSXo3bnRpV2RPeTRhSEoiLCJtYWMiOiIzODZkOTg4MGFkMTdiZWFmOTEwNzc2ZjhmMGZlNzk0YWM0MTllY2E4YjdiYmU4YjhmYzJkMmRiYjE5NTgxYjFmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpiNDF4M1BlZm1mU0ZaYVZRYXB1bWc9PSIsInZhbHVlIjoibEJLSEVNcVRIcGlqUlhXZW40N0hFNEhqYkREazVQWkJQczh5V2pvelFjTEZ6aUdoOVY2TklhMTY2QXg2MElkSXFNZnROUk9oL1lPVWtYVUJuYkNUdnpST1JmQmpvUXdSbmU4eXNIMXlCRzExU2lYd2dZZmlzSFVxejNndWY3SzlDYjVndGliT0dMcEtnblVuaTYzWVV1cUpuaFcyWG1xTDE1RVhiZ0JZNWRzS1daajlCZFMrY1M3NUhaMU15MjVCTXh3Z0FQaFBDMUNtWG5CS3FVM1dpL25yb0pPODJMcWE2NlhVa2V1NUdCZitINlJOS2NJa1hJd1FEMDBQSUgzaUx5WVQxR2dGT0ZyRWtMVXhQRnZhUFFlZHJlMThVcld3d1Y1Z3dHYXo4UUF5MERWbVZkYkFGTTNRYUVPZE1RSTk5Q1ZBVzY3SlhaUGp5R1RmUDBpUmNHR21ON1BNZWR1cnZreFhCam95dzdFMUdrdStKVWwvOU9ob0dVZWE5d2puYm91eVdsRmlsVnllLzQyWGdQOG4wTFAzelNUMHMrMENZcmxkRVJFSnZkNi9FMFc3bUFnYStzS1QyUlFNa1ZaMVVHRG5mdzVJTW5kNGZLOWZxT1FIRzdSRnNXZVViUnhNcDFxL3Nhcitqb0xrUlRaejU0cHNlN21TZ0hnbUY3MEUiLCJtYWMiOiJhNTI3OTQyNGU0NGQzYTQ5NDgwMzVmYjVkODZjOTZmNjVlYzFlZjZiYjMzNWY2NGIxM2RiODA4NWY2MWIyZjZmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-919877097\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1693088193 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1575;&#1604;&#1585;&#1589;&#1610;&#1583; &#1575;&#1604;&#1605;&#1601;&#1578;&#1608;&#1581; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693088193\", {\"maxDepth\":0})</script>\n"}}