{"__meta": {"id": "X1aba789a909859a19f53866219e8fa54", "datetime": "2025-06-27 02:12:22", "utime": **********.755392, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.314633, "end": **********.755409, "duration": 0.44077610969543457, "duration_str": "441ms", "measures": [{"label": "Booting", "start": **********.314633, "relative_start": 0, "end": **********.672649, "relative_end": **********.672649, "duration": 0.3580160140991211, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.672659, "relative_start": 0.35802602767944336, "end": **********.755411, "relative_end": 1.9073486328125e-06, "duration": 0.08275198936462402, "duration_str": "82.75ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45734672, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02716, "accumulated_duration_str": "27.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.703968, "duration": 0.02578, "duration_str": "25.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.919}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.739611, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.919, "width_percent": 2.172}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7461808, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.091, "width_percent": 2.909}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1009025413 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1009025413\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1969160983 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1969160983\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-717018352 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-717018352\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1992895945 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990338154%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZKOC9pSXZqV3VrZlVSMnJaeTlpdGc9PSIsInZhbHVlIjoiZ3FQNWQzN3lqdWFUWkxWMHV3L1BkbG1QNTIvczZIbmhZRmUyUmFENTZTOGdFTm5YcEE1dkJRMmQwbTNtcEpFQkN1TUdDMmR1dlRJK3k4dnN4RHg3ZVZEdmpGSmNSbXFuWWFkZUhvN01Bb2pNWGNhSDdSZ0ZQWmlzeWxLazh2SGNLcHFYajNTdU5PekRRcDY5TjB4SEhSc0x5SEQvVDJsK2FveWlHVFRtMjkvUHovWVEyVUZoMXRxNm5RS0JDMTJBOThrOFFVbjd3R2tabXBZYWRCUWlRSXZRRmtZTTNSL3NFZjl3MEhDTUhTNlJXUjZyZlBxZW80MDRwbndBWmltUlV6dEhwUlhwRjRhQTFEaFA2ZXczdG9idnVoV0I1OHlkc2RUczIvWFo5U05WTi8xNDMrS2RRZXFjNmltQ05Ydm5TQkNjUmVUKy9ZZTJoR2JTOU1JaWJoZi8xaysvQXpEM1krb0lwclJsTXY3cHJJbFZuc0k1b2VZYVIrTGJ1WENBUzJ5ZGpWdVI4bVhDMDdPRU1yMi9UOGorQXgvS3FxL1Y2cGdCc3JETk9hVDNCc1QxUTZTdUtnbE5NZkxnSmpmVUVuS1hsbENlUHNGOXIyczZLWnRtc2NqL2poU0VpUHJReGNEeG40NThsdHJmZnUyUWJuemNaU1VlRU8xUFd3akkiLCJtYWMiOiJkN2M3YmE4MmUyZjg3MjA0MjU4M2EyZDM4MDkyMjVkYWI5ODBlNTZlNmM1YjFmYzI5MTQ1NzM5YzcwYjQ4ODYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldtYzFMUkZNMUFsb2FiSFVuWTFxK3c9PSIsInZhbHVlIjoibWUwUldDYVF4ZktEUTgwSnFTSFU2aWh5M1kyUHFmQ2VlbmtFZGtWQjRGZVorRVQ4cVBoRVNsQjlGSjY0bWt5RlBWa1I2WndZa2wrOFl4OTdtOVdZT2tiTjJrWjBtNWF2ZVpNa2lPUzJoSUNYSWNGdkVlVkxqVjF0MUhqUWpsb1UzN0tzZVZ0cjNXcEV6TlBmY2RFZkFwVXdYbWN6Nk1ONGNiTm5ENU9jak5aYmI3MnZVa1ZjNnlVUzlvV0huUzYwMWR0RWZaVWpKV0x6L0JNU082RGxwakUyZzRjMFdOVGkvbmFWYUZSVHU3dkZwUTFrSndHQkc4QVFPcERGMXI0YXBTem5sVnR1U0pIeDRSWW5laWJsNi9wUnF0Tk8wNDFRczA2OWxva1VsM3NWTWdUbHAyNE1RYWs4QW44SXZ1dlN5RWxydzRVNXkvQXlidjQ5d1F0cnhGcFg1dGRGOGhtUFNlYTN1M3JQa2ZDVnhXL0tEOHlxWnI1OFUyOVB2NUZwdDgySDljeGYrWWFvaWhGVnEyT1VOMlJPUEoyT3VaZU9kNmdlRGVSS0FkQ2xhOTkzODUwODltYiswUFNMQ2llZy9GYkxhQVI1dWNXY0NvK3Q2Nk02VXpDT1JkZzdXS3hUamVCdUd3TEVoRWJFb1pNemdpOE02eE1DU2dXNTF6bm4iLCJtYWMiOiIxYTFhMjNiMGE2ZmE5ODMzNjkyODZjNjljZjI1N2QyZmYwOWEwZGM3YmZiNzkwNmFkZTQ5NWI0MmNlNTEzMzA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1992895945\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1130546589 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130546589\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2043627455 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpiNy85UVpYT3VNVFMwNGd0QzdlZnc9PSIsInZhbHVlIjoibUJ3VzBTUjdrVmM3czRLbEc4VGhNa2VlTVVPNjFDUVBKY2paeSs0R1RLRFdINWFRUlhLQUUxOXMweEdCNjhoeGJOYlk2Z3BXVm43c1dMTXJYelR6b1JJYytrbmxWVDlFUTlPdU84aEVwdkovTnJKaWxPYWwycjdjT3pTaHlsSngxV2NFSGNrekFJeXRXSm12SFM3WGl5VThNdVEwLzZVVTZqNFFGN09lNm9IbFduQVpUWGxPbkxIekkwSEdXVXlyTjBWZ3Jsa3lpVUZxZFFMNjV5QVNnSlJ3a3ZUdzFPQ3h0aTFjWi92T1YvUFVOaUxoakg2dUxETTNlNEJMTlNGT3lmNzBUTitYT3RiRERPN1BPZ01FdFZNTk41Wm5HVFdJT3JWMlBPTm1QRFNJMzVTTlUxMHRkYTRBOWcwc3VTZWhQdmRwWngyT1hFM092ekUwaVc5LzZac3MvWldvM0JPcW8zaFdSNDlyaGlldU9BNHRIWVBKNGZJcVVlU0tJVDZGL1BhU05DdkF2ell3QmM4alptS2NuU3VhV3VkQ0g3amVEM1d2blJUNXdDd0dyM1Zma25WQS83NGwvTW9OVjQ1TVg1UHkySlRGZk55dXhhUVR5QXZrNW5UR0FtTjE4S0xCbFliaVNUUkx4YTUrN0RSb2NCUHl5RmwrYkQzVkdKUzAiLCJtYWMiOiI5NjE5ZDkxN2QyNjNmYzhkZjM3NGNjNGNmNzM3MDgyMjM1YzJkMDBmZDMzNjk3NzI4ODExMmE4ODZlMDAwYmRmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZ5S0ZzUWFUNTIzK3liN3J3V0QyNlE9PSIsInZhbHVlIjoiWTdHNGxKbGdyNFgyVStGUThaeTNNVjFUMnVaYTBxTlVsSDlMWUswbmh6VUF3UG5aVmtjS0FGMVVEVzBzcFpQNTArdXVWakJ4MVA1WWxIV3d1TkZzUXI2Rkk5b2pYSGh0cWNvWGFnYjRURlNTNnJyNVRqL3l6UFdjcEFEcm43bktTV3BpdTgrd0s4MkxJSjQzeDRpa3FlSzRVTThQYVVpcjJMQTA2ZHBHbW82Z2Y0TkR0RjVWNmp3V2laTlUyb2hzZTQ2Wm9vamJkZEZEVkNNRXF4Vm9oL1NSSTNCeFZERnNXTzM5ZHVIRjhPdUlUcmt4QUxNY1hXOW9ZS2Z0eEkyN1R2Szg3WFBpbXRuWmx2K3JvMmtWRDQvZmpkN3YyNzVMMzE3RVpoakVIRmdYZTlGUlNYZUJ0a012cXdrY2UrejZRZ3g0bjc5bStYdVVkR09iajBGSDVkM0pNL1l3OGZLWFRwVWF0MUd2c1htdFNNUVFkTi9rL0FxYXU1cWtZV25rTmZldjBVTVJKWkRnc0dEWGVYL2RTQkRNKzM0TkMrM1QxOGVpc1BlLzRTTUxKcmIyQlEzRkFmMEF3bkt1ZnJXcDZhbUFCblZmT2w5T3crZVVkbmJhQmNOcU9TbnQyU25vQ0FiM2JUV3NlQVFTOU9ha3IrZzNBUFZNT2F6ZUpiVy8iLCJtYWMiOiJjMmNhNWExYmJhZGExMGViM2NjMWUzYTZlNjI1YzZlMTE3Mzk0ZmQ5ODZkMjg2NGIxYTA1OWM5MmJiNDViMjdmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpiNy85UVpYT3VNVFMwNGd0QzdlZnc9PSIsInZhbHVlIjoibUJ3VzBTUjdrVmM3czRLbEc4VGhNa2VlTVVPNjFDUVBKY2paeSs0R1RLRFdINWFRUlhLQUUxOXMweEdCNjhoeGJOYlk2Z3BXVm43c1dMTXJYelR6b1JJYytrbmxWVDlFUTlPdU84aEVwdkovTnJKaWxPYWwycjdjT3pTaHlsSngxV2NFSGNrekFJeXRXSm12SFM3WGl5VThNdVEwLzZVVTZqNFFGN09lNm9IbFduQVpUWGxPbkxIekkwSEdXVXlyTjBWZ3Jsa3lpVUZxZFFMNjV5QVNnSlJ3a3ZUdzFPQ3h0aTFjWi92T1YvUFVOaUxoakg2dUxETTNlNEJMTlNGT3lmNzBUTitYT3RiRERPN1BPZ01FdFZNTk41Wm5HVFdJT3JWMlBPTm1QRFNJMzVTTlUxMHRkYTRBOWcwc3VTZWhQdmRwWngyT1hFM092ekUwaVc5LzZac3MvWldvM0JPcW8zaFdSNDlyaGlldU9BNHRIWVBKNGZJcVVlU0tJVDZGL1BhU05DdkF2ell3QmM4alptS2NuU3VhV3VkQ0g3amVEM1d2blJUNXdDd0dyM1Zma25WQS83NGwvTW9OVjQ1TVg1UHkySlRGZk55dXhhUVR5QXZrNW5UR0FtTjE4S0xCbFliaVNUUkx4YTUrN0RSb2NCUHl5RmwrYkQzVkdKUzAiLCJtYWMiOiI5NjE5ZDkxN2QyNjNmYzhkZjM3NGNjNGNmNzM3MDgyMjM1YzJkMDBmZDMzNjk3NzI4ODExMmE4ODZlMDAwYmRmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZ5S0ZzUWFUNTIzK3liN3J3V0QyNlE9PSIsInZhbHVlIjoiWTdHNGxKbGdyNFgyVStGUThaeTNNVjFUMnVaYTBxTlVsSDlMWUswbmh6VUF3UG5aVmtjS0FGMVVEVzBzcFpQNTArdXVWakJ4MVA1WWxIV3d1TkZzUXI2Rkk5b2pYSGh0cWNvWGFnYjRURlNTNnJyNVRqL3l6UFdjcEFEcm43bktTV3BpdTgrd0s4MkxJSjQzeDRpa3FlSzRVTThQYVVpcjJMQTA2ZHBHbW82Z2Y0TkR0RjVWNmp3V2laTlUyb2hzZTQ2Wm9vamJkZEZEVkNNRXF4Vm9oL1NSSTNCeFZERnNXTzM5ZHVIRjhPdUlUcmt4QUxNY1hXOW9ZS2Z0eEkyN1R2Szg3WFBpbXRuWmx2K3JvMmtWRDQvZmpkN3YyNzVMMzE3RVpoakVIRmdYZTlGUlNYZUJ0a012cXdrY2UrejZRZ3g0bjc5bStYdVVkR09iajBGSDVkM0pNL1l3OGZLWFRwVWF0MUd2c1htdFNNUVFkTi9rL0FxYXU1cWtZV25rTmZldjBVTVJKWkRnc0dEWGVYL2RTQkRNKzM0TkMrM1QxOGVpc1BlLzRTTUxKcmIyQlEzRkFmMEF3bkt1ZnJXcDZhbUFCblZmT2w5T3crZVVkbmJhQmNOcU9TbnQyU25vQ0FiM2JUV3NlQVFTOU9ha3IrZzNBUFZNT2F6ZUpiVy8iLCJtYWMiOiJjMmNhNWExYmJhZGExMGViM2NjMWUzYTZlNjI1YzZlMTE3Mzk0ZmQ5ODZkMjg2NGIxYTA1OWM5MmJiNDViMjdmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043627455\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1438684464 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1438684464\", {\"maxDepth\":0})</script>\n"}}