{"__meta": {"id": "X13645a96219e7f69b47aa524c88fd471", "datetime": "2025-06-27 02:27:48", "utime": **********.386093, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991267.94825, "end": **********.38611, "duration": 0.43786001205444336, "duration_str": "438ms", "measures": [{"label": "Booting", "start": 1750991267.94825, "relative_start": 0, "end": **********.332312, "relative_end": **********.332312, "duration": 0.3840620517730713, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.33232, "relative_start": 0.38406991958618164, "end": **********.386111, "relative_end": 9.5367431640625e-07, "duration": 0.053791046142578125, "duration_str": "53.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45720280, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025, "accumulated_duration_str": "2.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.358155, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 56.4}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.369494, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 56.4, "width_percent": 20}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.375824, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 76.4, "width_percent": 23.6}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-240250147 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-240250147\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-695132662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-695132662\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1237752319 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237752319\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1534990860 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991263527%7C37%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFtclcrRUZZMk9nWlZ1UGFjb0pnd2c9PSIsInZhbHVlIjoieW4xZ1F0cGJnOE1SUldxTlhPT3FGZEQ1YkZrYmU2cjNSYVNjYUMxY2Eyc0U3T0xFeDdzT08yaHVwSTc3aytzZGIzN3Qxd1dIci9yaGFWSXduYjdMaHR3RFU0QWZVNUhHZnpJME4xSTA3a1Bra1ZncFJLSlRtZngybmorMFdXbFFUVG5zUUxJcTYzYmNFUU9YRDNqS3NOQWVrQ1dBRlozcm9ZcTh4Wnh6ODQwSy9GMkNJbHNWaWhPV1JVRGgxR1BUNlc4ZExIQ0JNMm5iSkRIaDZ2dDJ1bHc5aXZYQjBTT2lzd2xBT205VEkrUkFzTGo0S3E4NTQ4YUlEMlhNd1ZIWHlJdGg3NkNNeVJtWGJWMUt4bXRmQ1VHclFMWk53WkZoTFdLSFRuckFmMG9Xc3UxZ0RLWHZMSFZzR2IzdlJmbFZLMERGWE04WnQyenNZTVB3R0YyMjdKck0vb0NUZWZnQTZadm9CTzVVL0dQSlk5bnArczVVT3g1T1FZZFE1NFVkYkZ5cVgzTEd6S2ZleEZBNFI5OFNiWk00eGpnUHo2TDFCZm1kTTg4d2UxQXM1a3RiUHBXRWE5L3MyUWI1NVRKOGFud1czOXc3RDY4RTgrQ1BhK2ZvclI4Uk5aOU8xRFdnRGZoZmU0MVduWDVGL1U1emZFL0NYeDUxaHN0Wk5xVEkiLCJtYWMiOiI2MDhjMDU1YzZlNWU1MzVkY2E1OTZkZjIzNDU5YjI4ZjI4OTMwZmQ3MGJkNDhjZjU1NDhjNDRmOTJiNWZjOTI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjB2eXFRUkdZVXgva09ySGdnbnl0MkE9PSIsInZhbHVlIjoiUFBXbHlIMnlreTZJc3gzckY4b0ljQ081UmlsZDg0anlvVHI1MDFNYTh4Sjg1TmxCZEZkN241UUk5Q3lnZEtaWE4xMWNhQnpPMmRoRURvWVYyeFU2bnErN0t0ejBuZUFqdHV4OFh5SXRWOHoyNkovSGNjVWVQYktEdkdodUJFSG1vTDREdkdyRmNqNEJVN1JPcWVMTlBseWhudmc2N2NnZkFTQXVUc0hsdmFOZWdMNll0V2hyUjNrM3dibUtJWThtUEh0N2JtemFWSjdlaVNmbDQ2NE0wWWlULzJKTytPRW84cHIvSUlQYjBEYllGRnRmRnlURHNLQm1YeUFtVis4MGM4R3hveFEzZ3JhanVNNDdBUUhadkRRa1BjUXh6aW1EMzltUW9nUlN0R3hVdTlLek4wYkd4bmNTL3hkOEc3ZDR3ZEZ4eTlMcWcweHE2eVcyYmgrU0NmTG5YY0d4MVlxUjJvbFZVRjJpTFNCbVUzOVJyT2FHMFdJTVp3NjdwU3JZNjZsQWpLUmtFQmVVZU14OThXWkpEd1NqMnN6b1RPZFhad2wrcHkxbEFtZ2NxK2RONUNDSEVmZktQMVB4eW1xVXVCa1ltZnBnZGVpWllCSTg1dzVJeU9BN244MGdJSXZLOXk1c3JpR2FtMFhyOWRPZ0J6eEhuWkRaTVZWQU1kNG4iLCJtYWMiOiI0ZWQxNjg1NzRlN2U5OGY0MjY5YzQzYjcxZTU5ZTBlYmM3ZDkzNmJhMDQ2NGJjOTg1OGNkNDkxNTBlYjc1OGZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1534990860\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1437088057 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZiSDNEM3R2OFhTWWJsQnlXSEJLeEE9PSIsInZhbHVlIjoiUXRGWlYyVGVTSnJCZzY4aFROaytkM0VuN2p5YkdCWWVSYUNLREo0ZHE1dXc4YVlhaURkV3E1V1UzZjJldEpsYnRsbmFadlNncVArODQxelc5Skp4d3haU1BMYmJXaHBiSW02dElnRDVNZTA3Y0xmczU0NldvWXVzTDc1Rllsd1hFelNtWU4ya2ZsVmdDSFQyQkJSV3EwQ200S3FuVHlpUzBXNXo1SWYzWXpaWjVDb2NQSXlHdnE5OUFHb2xSckNWQ2lmTVZpN1pERFBobVFhSHFzUTJhWEpYME9uaXdJZmx6clhIbTNHSUt3SkNhcUF1UzIyZTQxZExxd21ZcHNKaHJ3NG5LVW9ES1hJR1Q4VDcrQ3JqbjZKS2IvSlBQdTR3L1g2dTBlVldNVkxZK1FCTFVLZ3hST0prUzRUYmt1VXpDSWlUQzQvNHlLVFBQYzBodG82Y2ZabjVYbWVNbjIvQkZ6YlFFYWtSM2taWngvdjdEdzNBbFRJUFByQ21Hb2h3UFF3ZDJvWEVyRExMVENyOGxNUXNGTFFVMVR3OFJ1QnlFQ3d5cGFUZzZsbTRWbng1dklyMGRkYU55Um83QWVkVEhaZHRES2FQZ0F1Qnl0L21JYzAwOWhTVjd4MmZCNW1XU1BUYXlRdFM4QllaYjdTVXZmWnJ3QTRrQUJPTUd5Y1MiLCJtYWMiOiI1ZTdlN2U3MjBmNzExNGI5OGNkNDhiNTUyODlkODgzNjE1M2IyZDQyNGVkZjQ2ZDlmNTRmNTZlNGRjNTQ1YTIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlMrbmtnSWVHa3ZnYTNIUlBEUzRVRWc9PSIsInZhbHVlIjoiWS9UMVNhOEtYb0cxTFNVbDJ0NXhJdEZodE9BZU1DbHh0QVpteVdtQUttdDVHMnhBMDQ3ZGppRXhoZkp3T25sNU9ZMm9rb1FweThCbjZoSkY2NVBqekNpNFBBNFNMOHczbFgrRUVNNklpZWpDYlVaL0J4OEEvT01FTGRLUXk4NUtjcHFkbk5hMWllazh5ZnVnQjZ2OHZjN1c3czM0MFJuamdsRHRsTUNGU2xRaDVvU29UemxOTzRsZ0pLS3R6WDdINmxNT0JSbkwwQTZmODFOMVJveGNRL0FySEFDQ0liRjZ4QSs1cDU3RmxpdkdndUEweDkvWXZKWUoxbVh4QW91Rm9rajlkNndSaGN3RFBtSDVobzlzQWh0K1I4TGRSWXhueFplSmVVRW5LNlhITTN1ZWFTK000UEt2akVNaERZYWh3dFNFdmNPazFHVDE3eitiL2xhQTVTVnZFRjFjZm1RbFFPWlNmRm1oeWtJWHdUOTBYN29XMkxkQVZrdDRYajF6SE5GQk93OEIvdlB4OVRaeVRVWjViNW5FVU01LzZPRHVxMEhFTjY1dEdnWlM3dWRSbWlCVFpQS1ZCVTZhSHRTNTk5cXJHbEZIMEYxMVVxeVo1WDg1clZyVUwyUUhDcVhhTHN3Q3d4cG5TbUkzdmdESkQxMm1YZFVXRzc5U1JvQk0iLCJtYWMiOiJiOGNmYTc1ZGQxNjViZjUzNmE3MTFmMzdhMTIwODc4YzcwYTE5OTc1NWRmNmZjOTc5MjI4MmZjNGZiM2E2NGIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZiSDNEM3R2OFhTWWJsQnlXSEJLeEE9PSIsInZhbHVlIjoiUXRGWlYyVGVTSnJCZzY4aFROaytkM0VuN2p5YkdCWWVSYUNLREo0ZHE1dXc4YVlhaURkV3E1V1UzZjJldEpsYnRsbmFadlNncVArODQxelc5Skp4d3haU1BMYmJXaHBiSW02dElnRDVNZTA3Y0xmczU0NldvWXVzTDc1Rllsd1hFelNtWU4ya2ZsVmdDSFQyQkJSV3EwQ200S3FuVHlpUzBXNXo1SWYzWXpaWjVDb2NQSXlHdnE5OUFHb2xSckNWQ2lmTVZpN1pERFBobVFhSHFzUTJhWEpYME9uaXdJZmx6clhIbTNHSUt3SkNhcUF1UzIyZTQxZExxd21ZcHNKaHJ3NG5LVW9ES1hJR1Q4VDcrQ3JqbjZKS2IvSlBQdTR3L1g2dTBlVldNVkxZK1FCTFVLZ3hST0prUzRUYmt1VXpDSWlUQzQvNHlLVFBQYzBodG82Y2ZabjVYbWVNbjIvQkZ6YlFFYWtSM2taWngvdjdEdzNBbFRJUFByQ21Hb2h3UFF3ZDJvWEVyRExMVENyOGxNUXNGTFFVMVR3OFJ1QnlFQ3d5cGFUZzZsbTRWbng1dklyMGRkYU55Um83QWVkVEhaZHRES2FQZ0F1Qnl0L21JYzAwOWhTVjd4MmZCNW1XU1BUYXlRdFM4QllaYjdTVXZmWnJ3QTRrQUJPTUd5Y1MiLCJtYWMiOiI1ZTdlN2U3MjBmNzExNGI5OGNkNDhiNTUyODlkODgzNjE1M2IyZDQyNGVkZjQ2ZDlmNTRmNTZlNGRjNTQ1YTIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlMrbmtnSWVHa3ZnYTNIUlBEUzRVRWc9PSIsInZhbHVlIjoiWS9UMVNhOEtYb0cxTFNVbDJ0NXhJdEZodE9BZU1DbHh0QVpteVdtQUttdDVHMnhBMDQ3ZGppRXhoZkp3T25sNU9ZMm9rb1FweThCbjZoSkY2NVBqekNpNFBBNFNMOHczbFgrRUVNNklpZWpDYlVaL0J4OEEvT01FTGRLUXk4NUtjcHFkbk5hMWllazh5ZnVnQjZ2OHZjN1c3czM0MFJuamdsRHRsTUNGU2xRaDVvU29UemxOTzRsZ0pLS3R6WDdINmxNT0JSbkwwQTZmODFOMVJveGNRL0FySEFDQ0liRjZ4QSs1cDU3RmxpdkdndUEweDkvWXZKWUoxbVh4QW91Rm9rajlkNndSaGN3RFBtSDVobzlzQWh0K1I4TGRSWXhueFplSmVVRW5LNlhITTN1ZWFTK000UEt2akVNaERZYWh3dFNFdmNPazFHVDE3eitiL2xhQTVTVnZFRjFjZm1RbFFPWlNmRm1oeWtJWHdUOTBYN29XMkxkQVZrdDRYajF6SE5GQk93OEIvdlB4OVRaeVRVWjViNW5FVU01LzZPRHVxMEhFTjY1dEdnWlM3dWRSbWlCVFpQS1ZCVTZhSHRTNTk5cXJHbEZIMEYxMVVxeVo1WDg1clZyVUwyUUhDcVhhTHN3Q3d4cG5TbUkzdmdESkQxMm1YZFVXRzc5U1JvQk0iLCJtYWMiOiJiOGNmYTc1ZGQxNjViZjUzNmE3MTFmMzdhMTIwODc4YzcwYTE5OTc1NWRmNmZjOTc5MjI4MmZjNGZiM2E2NGIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1437088057\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-785469193 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-785469193\", {\"maxDepth\":0})</script>\n"}}