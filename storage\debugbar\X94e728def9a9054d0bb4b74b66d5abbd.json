{"__meta": {"id": "X94e728def9a9054d0bb4b74b66d5abbd", "datetime": "2025-06-27 00:14:46", "utime": **********.126791, "method": "GET", "uri": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750983285.687955, "end": **********.126806, "duration": 0.43885111808776855, "duration_str": "439ms", "measures": [{"label": "Booting", "start": 1750983285.687955, "relative_start": 0, "end": **********.050521, "relative_end": **********.050521, "duration": 0.3625659942626953, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.050531, "relative_start": 0.3625760078430176, "end": **********.126807, "relative_end": 9.5367431640625e-07, "duration": 0.07627606391906738, "duration_str": "76.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47521888, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.show", "controller": "App\\Http\\Controllers\\BillController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=333\" onclick=\"\">app/Http/Controllers/BillController.php:333-393</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0035299999999999997, "accumulated_duration_str": "3.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0845368, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 52.125}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.094682, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 52.125, "width_percent": 12.181}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.101665, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 64.306, "width_percent": 13.314}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.115294, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 77.62, "width_percent": 13.598}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.1170201, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 91.218, "width_percent": 8.782}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => show bill, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-418402020 data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-418402020\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.120231, "xdebug_link": null}]}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-353409732 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-353409732\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-537905128 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-537905128\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-394888385 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-394888385\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1560510060 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983282490%7C45%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImNzTkNLYm0wcUY5dEMxbzZuRXBIUUE9PSIsInZhbHVlIjoiU2VpYURNeEhROHZnY0pKMkovalN2S0xmNFAvbHllSHY2QTBmZ3U3NUhQVXpWNE1JengzdEZ5aVlMWHhQYm9qaFduQ2FsNS9Oajg5eU1BaTBiV1cwWHZiRHhVNHhORGFheFM1NUNiVms0OEV4cjd2eHlhVnpPRTQzUjZtSXY1aDI0S1FZN3lIR0htZklKd2lNdmdRQ1lqZFRrczdpNGlBb1JaRzh2RU12WGJiRDJlenN6S1h0Sk5zZW1NMWd2WXZabDBlaS9paWxveVpTQktZTlQrekk0bksxSEtPd2VuM0wwc2xiWi91VjMxaGhKTDZuZ044ampjdHZnV2RxR1RmTkU3WXZTTTYweUJta0lPYm0wd0pJWUk2WE0zeWE5cTg3RHVHdTVEUnJJN3RIb0VXemUxVzlTSzNoV3oydWU5VFE0Yk8xTWFxZllsalBuQWkwWnlvdHhTVDYvWTlEQ1p0WkxrcnNWNVlKNHlBZTAyZmRZdWh1cXRQM3c5L1QwUlg0UnUwa0Vsa20xM2pYWW5vREpFUlBvYUJ4cGZxZFdMNUdaM3N1alhuczhPK1orc0pvbmd4dmh0cURlYml0VWZCRm9aZmJ6a0VjK1BWR2VHMVRXempBYUNXaFZEU0R1Z1dPbW1ObUd4MTZGMVpBdU9WR3hMdlQ4ak5tQ2U2S2N5ZEIiLCJtYWMiOiIwYWNjODNkNzQ0MTdmMzg2MjU5OTVkYmVhMmFiMzY5OWNlYzdjZTdlOTI4YzQwN2JkMmUwMDMwNjZiY2I1Yzg2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImN5bHFTelBPdUx5RmFITEVxTjlTTXc9PSIsInZhbHVlIjoiQ2VrSkJ4MlAzUUduc0NmZGJKZFkzc05CcFV3b3NZUDJmUDRGUEtTVzZsaXRmR1l5MUJPa2IwTHptZnpOTFlFL2FRV2Mzd08rRkJWcWNqTVVLZEdrQkNsMlNkUkZQY08ybldJajBlMFVkbzVWM002M1grQTRtMHh0SEdXbnRqUVdTcmJ3VU0wcU1teFh1ZnNScGZtS3hnRlRWa0hFa256amNUeEc5OTFwUVlQc0NtcDZqRzM1WkU5RWxLNWtJVjlXMUJBcVlkSmM3bVZrS0JyZ3BncHcxK0xBU3g5cklXRENzU3hzN1JyaTVNSUlhcWhIYVpDejczaWs0S2tGek9ZeXNDNks4UEQxUy9wcnhkQlRBdE5ieFFFeUxqaWw5U2c4cjJ0MXUxb3daa0ZaaHN5UWdJdHROeW9rWGoyNGhvcFdydk4vRXU1MmtwWXJNbTg3d2FCdkgzMncrd2E0eHZ4QmdOU0dNTEhUd2VERFNVeGp6MEdtZ0NxTkhtcGVoK3JieU43UXU2MlM0TGJaMjJQeXYyYUFmd1VueGZhS2lYK01BWTNvMWthc3lER0RPUGJwbVdmM0gwZVQ1bmNhNnNEL1FSdHRMWml2elA0ZEFFYlh1WnFORDlnY0JWMkpaSVJDM3gzSGgwR25iVS9ibVNCYjRrSVZ6R2xXVSt4Q3BNc3AiLCJtYWMiOiI5YjEwYTU3MjU3Mzk4NTdkOGFlODhjNTM2NTcyNmIzMTI5NGRjYmQ2M2U4N2M1ZTNkMDk1NTYxNjY1ZTc5OTlkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1560510060\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1726511282 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726511282\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1272213908 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlQvNE52eitSb2trcXp3ZDh5N1gyWnc9PSIsInZhbHVlIjoiQ1JVdjBsVUl6NkcvTVpFektiU3o1ZHNQOU9PN3k1N015OUxJZXQvc1lzWHJmcTlQV0dsbC91Y3gwOHNBeG9zUWgxS0R5WkVXSGx5dmk4SXRaWE05RXllNHl3VG5vd29hVnpDSDNQYytiSUpWemJ6N3FlNWUxTXFWa05DY0VmS2tpQ0lpQ1ppQnJkNGpHT2lhZ01UZW54ZzdmeStzWVk0c3d0UnhUNzJNazlrUWlvOGpadzVUeS9QOVZrcThLMFh0QlRQV2p0dHNaZEI2R3ZPTHJ3N0EzWmJLenkzMFRkekxwRHlFaGo3cG5aTzdnNWpBLzhvRXNWbGVpM2QvTEpEbkxmdk92WHNoV1J6dkJwa01UUnNybHUvQ1IrSHBYRUJlKzduUUM4YStVb041bG5WMkFIQ2V0QUQrdFNNQjJXVjBldkJaa3V0cDBtQW1yTmVva0xSV0hnejA1aXNJek5MVmNYcDVPY3c0WDJnYjlKbGRFSFR5QmtuYkxNcUFsQmg2WGtNNVNTTmMxMmV5Y0RqcUJIWHNJTlhYM05NMjFDTmJ0L0JxdVoyWklLY0g4RDlWNWVmZkhEYkk4VEdsSlYvRTNQeUU3bG4zNHZ3ZTlZUTFKcmYwV202c0tseU5RRE93WFlxSkQ0K2tERDBTb3EzS21SblA2cUpUeXJTUEJZd2siLCJtYWMiOiJjYTY4NTdiNmMxYjJkNjUwOThkNmQyZjc4ODg4ZDlmNGM5NjZiNTAzN2U3ZDk1NDU2NWY0N2M3YWQ5NzIzMjk4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZJTTQxbnZGSm41cjR0SXNoRi9xZUE9PSIsInZhbHVlIjoiTm05TTlKMHZyZk4xOXFuMENFckxzT2N0c1lUT3JyK0hIU2wvVjNOb0c2TVlYMmtHQVFWa01pNzhwOTMwZFVvd0sxWFlKSFpGdTNSVFFrbmN0ZURYQ00zZ2RrRHlVMWFPd1lhdldtYStRMmd4V1lWenlhSGtwUndWbTFXY0VjZ2RlNHlSaWkrQXlNYXd0azJyeDVEdC8ydG1kZ1RZQVA1bWQ0VVpOTW8rYVFoQUhsM1ZOckVseTFHMko3bVNTYzd2MUt0N29DRmpXY1VpYXlvbFZMWGFyVGpUU1dUK292b3dQdVdFK21KMFpnQzhPWGJlZnhnOEtqSnhCbUZUSy9WLzN0eGtqWWh5b3ZxU3owdHQ2ZERObDZFZjd6Z3puWGhsRTAwcXVCV3lhZXM1LzFmU2hNQ2VwVkhVT1BEcXlwc242eUVRUFV6M3p2aDdzcVVuQUdBUVFhUnBxbXZ5dHhqOUt3b0xIQ0FuNEZOaTlYa1Rwc0U0V2ZjSmFTMXEveEhNa25rUnp6QmRPbTdxZFVrR0lBaFFVdGdJRU1RNUJvTUl4VnhpYk42Kzh2akJnZ2N0aWNRWjc5Ull5ajE0dXFmeS94eTEyZW4vNklWYnBnZG9ScGErbFBKdFpXRUNRM0FLZlhwVExpeG5rcnpWUEpOTGttbk1SQUs2c1ZrNU9Jc0YiLCJtYWMiOiIwNGVjZjg5ZDViNDE3NjkwMTI3YWFkZWQxZDc5MGJkOTQ0NjczMzliMzRlMWRkYjQ4ZTU0MDA4ZGEyNjllNDk0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlQvNE52eitSb2trcXp3ZDh5N1gyWnc9PSIsInZhbHVlIjoiQ1JVdjBsVUl6NkcvTVpFektiU3o1ZHNQOU9PN3k1N015OUxJZXQvc1lzWHJmcTlQV0dsbC91Y3gwOHNBeG9zUWgxS0R5WkVXSGx5dmk4SXRaWE05RXllNHl3VG5vd29hVnpDSDNQYytiSUpWemJ6N3FlNWUxTXFWa05DY0VmS2tpQ0lpQ1ppQnJkNGpHT2lhZ01UZW54ZzdmeStzWVk0c3d0UnhUNzJNazlrUWlvOGpadzVUeS9QOVZrcThLMFh0QlRQV2p0dHNaZEI2R3ZPTHJ3N0EzWmJLenkzMFRkekxwRHlFaGo3cG5aTzdnNWpBLzhvRXNWbGVpM2QvTEpEbkxmdk92WHNoV1J6dkJwa01UUnNybHUvQ1IrSHBYRUJlKzduUUM4YStVb041bG5WMkFIQ2V0QUQrdFNNQjJXVjBldkJaa3V0cDBtQW1yTmVva0xSV0hnejA1aXNJek5MVmNYcDVPY3c0WDJnYjlKbGRFSFR5QmtuYkxNcUFsQmg2WGtNNVNTTmMxMmV5Y0RqcUJIWHNJTlhYM05NMjFDTmJ0L0JxdVoyWklLY0g4RDlWNWVmZkhEYkk4VEdsSlYvRTNQeUU3bG4zNHZ3ZTlZUTFKcmYwV202c0tseU5RRE93WFlxSkQ0K2tERDBTb3EzS21SblA2cUpUeXJTUEJZd2siLCJtYWMiOiJjYTY4NTdiNmMxYjJkNjUwOThkNmQyZjc4ODg4ZDlmNGM5NjZiNTAzN2U3ZDk1NDU2NWY0N2M3YWQ5NzIzMjk4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZJTTQxbnZGSm41cjR0SXNoRi9xZUE9PSIsInZhbHVlIjoiTm05TTlKMHZyZk4xOXFuMENFckxzT2N0c1lUT3JyK0hIU2wvVjNOb0c2TVlYMmtHQVFWa01pNzhwOTMwZFVvd0sxWFlKSFpGdTNSVFFrbmN0ZURYQ00zZ2RrRHlVMWFPd1lhdldtYStRMmd4V1lWenlhSGtwUndWbTFXY0VjZ2RlNHlSaWkrQXlNYXd0azJyeDVEdC8ydG1kZ1RZQVA1bWQ0VVpOTW8rYVFoQUhsM1ZOckVseTFHMko3bVNTYzd2MUt0N29DRmpXY1VpYXlvbFZMWGFyVGpUU1dUK292b3dQdVdFK21KMFpnQzhPWGJlZnhnOEtqSnhCbUZUSy9WLzN0eGtqWWh5b3ZxU3owdHQ2ZERObDZFZjd6Z3puWGhsRTAwcXVCV3lhZXM1LzFmU2hNQ2VwVkhVT1BEcXlwc242eUVRUFV6M3p2aDdzcVVuQUdBUVFhUnBxbXZ5dHhqOUt3b0xIQ0FuNEZOaTlYa1Rwc0U0V2ZjSmFTMXEveEhNa25rUnp6QmRPbTdxZFVrR0lBaFFVdGdJRU1RNUJvTUl4VnhpYk42Kzh2akJnZ2N0aWNRWjc5Ull5ajE0dXFmeS94eTEyZW4vNklWYnBnZG9ScGErbFBKdFpXRUNRM0FLZlhwVExpeG5rcnpWUEpOTGttbk1SQUs2c1ZrNU9Jc0YiLCJtYWMiOiIwNGVjZjg5ZDViNDE3NjkwMTI3YWFkZWQxZDc5MGJkOTQ0NjczMzliMzRlMWRkYjQ4ZTU0MDA4ZGEyNjllNDk0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272213908\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1590059647 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1590059647\", {\"maxDepth\":0})</script>\n"}}