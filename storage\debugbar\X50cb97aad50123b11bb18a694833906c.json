{"__meta": {"id": "X50cb97aad50123b11bb18a694833906c", "datetime": "2025-06-27 01:03:54", "utime": **********.96718, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.519017, "end": **********.967194, "duration": 0.4481770992279053, "duration_str": "448ms", "measures": [{"label": "Booting", "start": **********.519017, "relative_start": 0, "end": **********.916108, "relative_end": **********.916108, "duration": 0.3970909118652344, "duration_str": "397ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.916117, "relative_start": 0.39709997177124023, "end": **********.967196, "relative_end": 1.9073486328125e-06, "duration": 0.05107903480529785, "duration_str": "51.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45027680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00253, "accumulated_duration_str": "2.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.942475, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.079}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.95328, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.079, "width_percent": 21.739}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.960221, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.818, "width_percent": 18.182}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-232872197 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-232872197\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-779094678 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-779094678\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1555121435 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1555121435\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986202112%7C74%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InF5Y1dRVHFLaEdpQVIxc254VjM3WVE9PSIsInZhbHVlIjoiRXpTQ25mUjBtR1k4WlYzcG9DOWVrNGFIWDdhUHEvUUltNVcyVm1HYTZMejRkdUJNMDFlVi9kMmdjbGFvSGprbkhGSVlPRGR1QkhUTFMveUZ0QSszSUI0NkM5ZTR1VlpHYjk4TXRzdTE5UGtQZnIydStTaDlWOVBjTUhEL0FsQmR2OVEyZDZHSDU2YTNWb0U4dW84akJFa09BRFdXZkEraG1seENRb3lhYlZ0TTNUbkVZYnNHR2FHZ0Q0NU9FZWhDbFFhMzczZGRRcjJsUHNNUmdNRVU5cjU1SlVHa3ZPa2pKcXp5QjFCWVNwNFRNQ2FxemxyV0xLdjBRREdxMmVQdGVXa3paWW5ZV2MvV1ZTY1lxVENua2laOWFiN3MyWlZaUWs0YUNmcHRhUU0zcDViMVhPemRlRzRZL2NocHBxdndBemZrRzlwWGF2dFR3K3pxTXN5VHAxTTBESHkxblNuZnVBb0Zaa0pxcVU5dGhBSkdVdVBrekNER0FpSVRHOG9qRS81L1JtK0VHODhZOENZL0kwZ0l5RFVSSkwwREI1VzNEQUFmbW9uUzltTVdjN0hnNjNRZEQxUERRKzcydHNrS2VVT3ZCMlhEc0FZUi9oL2wrSkFsenhpSTdqMzE4azIxR09Vc0NGQjJDbW00MXBzVEFBMFVsWldyQldnUU9rMy8iLCJtYWMiOiIyNjdjMGI5MjE3MjU5ZGIwZmUwYjE1ZGNlNmNkMDg0MWFkMjI5N2Q3MDVkZWEwYjgyMDgwMjcxNWEwNTUwYjRjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllwdmkvYTdjM2RSR1dmOHV2NWptOHc9PSIsInZhbHVlIjoiKzZQeXRBVzRqdWZnOGtuZGdMbG5YN1NZUjF1OTkySmUwV1NpSHVPNWFkZGlEYjB3b1FRbUxFWDNjNjlkNWNHZCtrNDFWdE1qQjJUUjZiSVRFalNhSnB4K0t4SldEK2tLb1U5a1dCMnNoT3hjenFLb3R2TlJ1OFpmd1FpVjl3ZExiN0VPeHZzR2h3WmFwSzZQbWpLSVhwak1Ed0VHbDNRNnRMSlh5U2RCdlUyekhXOHc3dTZncXd5TUc0NjB1NFdPdnNyQ0Z6bVdJUExkMnVWbTVxeUd3NTQyNTdSVXEwNmdGOTZ4MlVsMG1tQTBWUnM5M09LS1dNcEZJQVplTDAxenJRTTRtaEVvWGx1SnV6RWt1blFQajRUVUlOREJ2emZKOWl5c0t4ZHVKQUdKaE96cmxRRDNtei90K3ZUeERZT2M0V3cvYi9mR0hmRi9NTkRaUUtHWU1OTUJGaDQyRkhXWWtVa09IZUlFK1dzNCszZlJ3THpnZHZjMFJQMjRmMjFvZ2hESWhJbnBYeXVUa1Z1MUJRbzA2c2o2Z0tVYVE4dHU0aG9sZUs2c2RHWk0vczJjejROSFdhL3Q0akRCUXplcHpaeFowL3pWamxDRW94bGlIVDNhc1VSUzRTRXFVbG93Z24rd3RiZWtwNkdIUUNDcDNUVjJFelNHOVlUcUpTaVIiLCJtYWMiOiJlYmY4NGNiODZlZTU3ZjRlMmYyMmZhMGZiYzM0ODI4ZWVlODU3MzQ1YjI2NTEyYWNjZWU4MTJjMjcwYTJhNzhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-444624466 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:03:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5GUXJUUXFWSDZDRUNJeHFuc3ZZT1E9PSIsInZhbHVlIjoiMHV6Wm5IdVB4TjdlSVl3cGltcjdjR0p3WHkzbzhOc2JYQXg1MVBTRVc2Wm1zRFFsUjhGbkNMZ3Fnck02cXJCeFRKdnhXci9GbE5HSjkxSmEwNFZzUi90MlNUeHI2NExVczV5aWpSclZSdmRnTHBLUFZrR0o2NWF0LzFGaGUwQjhva1JNczZZc0dSMHFzVHQxTXl4NlpJRGtiUjNoTTZYc0UzeGE0S1duZGt0WHY1S2NqOHgyZ29QQVVrM2padnJZREluaEduK3c1bTUwYWdLanhBaDNFWGhTYjlsMlJUWXFWSE9DMGtycUdzUUpMdVMyc1RkNlRJN2E0T2F1NCtEVmFMdFpKUC81VlJ5OEhzRVlHUkprTjNLSUxEaWE4d2RiTUNBVDJ4OTMwdiswcmNuRWR4N1BFOXVRanhtU0F1SmYxNHUyRjVuVkR5QmJya2FhU0tmdGFZTjdrb2xka3BsUEJ2UGU5YTNHV2lzMUhkUE5WdHBzQmRZSDc0R3lsdEFIZXNObndXVWVLRENVSjdKb1RXZXJlMXFRdkw4b1NSS3JKdXhLQ1RNdy9yL1lKdm5TWHlSMjk5eDdjcy94dm0wS3VwZ1VHZHNHdkJKekFNM2hMRXhucG0rbE5MSk16bklZditLWUkzbEg1TnhlTFlHZlpHcHBxWVFNb0R2NnpJVlMiLCJtYWMiOiJmNmUwMDgxOTQ1YzcwYjVmZTkxNjcyNGM5NjFmODE2OGRmZGYxZTBlNWNmOTI3NGJjODViYjc2ZDY1MTlkZTdjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:03:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilh1M3RvZWJlWDNmd3cwQVNFem5IRGc9PSIsInZhbHVlIjoiUkV5TkxKMVJuMWgzVnVUdlpjV213dlA0L25QVXJMZ2M0UWJwU0srTTl1Q3FNbUsyZGVQV2Q2amdqZGtoNVFDQ05QT2RYR3orZVBqQ3BvWG56eHhjWktCVDF0TzFaUGp0R2MzTEJka0xZYVVjblFsVDhObko2eUppS2JXZHdZZStnVFFUVlBoSUc4MzVSdzdpUG8wQ1dVd0o3NWY5ZVlmem5UMkd1R0t5UlZ4SmlFbG0xdVpXOVQ0bU1sVEgzR01lTUtMVUlBeUtrRXlIRW5MMWM2Zk40ZE8zQU9MOUFIcUY0NFdqalh4YmJ2cDVwQmFhT2JCQng1WVhlQXBzVHd6WjFlU3lsQzNFdnk4M2d1TFpxRGNCbzJjK3pFRSs3bCtpRGZOZTltOHJmN2pFenNwWGd3anZQLzNDWE9ZdndrRmxtdUNQOHFucWF0N052bnVRZmdha3Z5OU1JblZvaVpZalMvVmUrR3psNDZnaTU1ZkE0bXJrNEhhb1pkKzdFYmNEVHR0MlBaMVE1ditXZDdLZGtudlFOMjNSWHRWSDJsclVrNXA0TjROVHcxSU5Fc3l6UFQ4ZmZIL1B3NGpLa1gyS2hkSXVVSUhNRFBvU1hDWmRLZnFaQWQ0aDFLK0ZERW9qeGFmN3ZZM0tQd1lQNkJIUmQwSWxvdG4yVkl5MVlveGoiLCJtYWMiOiJmNmY3Mjc4ZGNmNjZjMzM3YTY5YjYwYjQ5YzJlZDVmOTQxMzMwNzkyZThkOGI2MTBjYmI2OWFjODU2YjNmNGQyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:03:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5GUXJUUXFWSDZDRUNJeHFuc3ZZT1E9PSIsInZhbHVlIjoiMHV6Wm5IdVB4TjdlSVl3cGltcjdjR0p3WHkzbzhOc2JYQXg1MVBTRVc2Wm1zRFFsUjhGbkNMZ3Fnck02cXJCeFRKdnhXci9GbE5HSjkxSmEwNFZzUi90MlNUeHI2NExVczV5aWpSclZSdmRnTHBLUFZrR0o2NWF0LzFGaGUwQjhva1JNczZZc0dSMHFzVHQxTXl4NlpJRGtiUjNoTTZYc0UzeGE0S1duZGt0WHY1S2NqOHgyZ29QQVVrM2padnJZREluaEduK3c1bTUwYWdLanhBaDNFWGhTYjlsMlJUWXFWSE9DMGtycUdzUUpMdVMyc1RkNlRJN2E0T2F1NCtEVmFMdFpKUC81VlJ5OEhzRVlHUkprTjNLSUxEaWE4d2RiTUNBVDJ4OTMwdiswcmNuRWR4N1BFOXVRanhtU0F1SmYxNHUyRjVuVkR5QmJya2FhU0tmdGFZTjdrb2xka3BsUEJ2UGU5YTNHV2lzMUhkUE5WdHBzQmRZSDc0R3lsdEFIZXNObndXVWVLRENVSjdKb1RXZXJlMXFRdkw4b1NSS3JKdXhLQ1RNdy9yL1lKdm5TWHlSMjk5eDdjcy94dm0wS3VwZ1VHZHNHdkJKekFNM2hMRXhucG0rbE5MSk16bklZditLWUkzbEg1TnhlTFlHZlpHcHBxWVFNb0R2NnpJVlMiLCJtYWMiOiJmNmUwMDgxOTQ1YzcwYjVmZTkxNjcyNGM5NjFmODE2OGRmZGYxZTBlNWNmOTI3NGJjODViYjc2ZDY1MTlkZTdjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:03:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilh1M3RvZWJlWDNmd3cwQVNFem5IRGc9PSIsInZhbHVlIjoiUkV5TkxKMVJuMWgzVnVUdlpjV213dlA0L25QVXJMZ2M0UWJwU0srTTl1Q3FNbUsyZGVQV2Q2amdqZGtoNVFDQ05QT2RYR3orZVBqQ3BvWG56eHhjWktCVDF0TzFaUGp0R2MzTEJka0xZYVVjblFsVDhObko2eUppS2JXZHdZZStnVFFUVlBoSUc4MzVSdzdpUG8wQ1dVd0o3NWY5ZVlmem5UMkd1R0t5UlZ4SmlFbG0xdVpXOVQ0bU1sVEgzR01lTUtMVUlBeUtrRXlIRW5MMWM2Zk40ZE8zQU9MOUFIcUY0NFdqalh4YmJ2cDVwQmFhT2JCQng1WVhlQXBzVHd6WjFlU3lsQzNFdnk4M2d1TFpxRGNCbzJjK3pFRSs3bCtpRGZOZTltOHJmN2pFenNwWGd3anZQLzNDWE9ZdndrRmxtdUNQOHFucWF0N052bnVRZmdha3Z5OU1JblZvaVpZalMvVmUrR3psNDZnaTU1ZkE0bXJrNEhhb1pkKzdFYmNEVHR0MlBaMVE1ditXZDdLZGtudlFOMjNSWHRWSDJsclVrNXA0TjROVHcxSU5Fc3l6UFQ4ZmZIL1B3NGpLa1gyS2hkSXVVSUhNRFBvU1hDWmRLZnFaQWQ0aDFLK0ZERW9qeGFmN3ZZM0tQd1lQNkJIUmQwSWxvdG4yVkl5MVlveGoiLCJtYWMiOiJmNmY3Mjc4ZGNmNjZjMzM3YTY5YjYwYjQ5YzJlZDVmOTQxMzMwNzkyZThkOGI2MTBjYmI2OWFjODU2YjNmNGQyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:03:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-444624466\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1913930198 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1913930198\", {\"maxDepth\":0})</script>\n"}}