{"__meta": {"id": "X6484df806cdbec21490bd1403e708c86", "datetime": "2025-06-27 02:27:52", "utime": **********.512089, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.078517, "end": **********.512104, "duration": 0.43358707427978516, "duration_str": "434ms", "measures": [{"label": "Booting", "start": **********.078517, "relative_start": 0, "end": **********.459102, "relative_end": **********.459102, "duration": 0.3805849552154541, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.459111, "relative_start": 0.38059401512145996, "end": **********.512105, "relative_end": 9.5367431640625e-07, "duration": 0.0529940128326416, "duration_str": "52.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45287808, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00211, "accumulated_duration_str": "2.11ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.494977, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.199}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5051332, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.199, "width_percent": 21.801}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1717811179 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1717811179\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-414764874 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-414764874\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-867968853 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867968853\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1082905471 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklZT2Q0Z1dUcTNaQnlvUEpCNWhVL2c9PSIsInZhbHVlIjoiVnp5NzJaMnQzejJBbzRiZzR2MGlYUHdhdjk5emQ3Z1dta0g2SzQ4WVNuNUhPb0FqR0NmQiszQWdsNHQ0Uk1OQmhZRDY0bXhSeXMrQnhFbUo4a093eEVqdFM3Rm1MTC9uK0h1N2FxU2wrSjZ4cDRiQWd5ay9hNUliR0hUbVhRS2UxdHVkSWJyVFkxL2ZoSVdMY1NUR01uRi9lUVlYdFN0Z3EyTGlKaFowRWtjUjFISTFVdmpidE5qdEthTWEva1hEeTQ5WUdUWXJOZm9HWS9ZeXkzN0srdUgxVkNCUTZiWW5rTEd6VlVIMFcxYnhHNkoyckFid0tRamdYbEtzSW5BalNDNk9LQWlWSzJYSmJraFZJL1RlRUN0Tk5lb2FDa0ZhN3dQeGdUbDJwK2xkb2dBeXJ3N2d3d1ZRdzhlcWh0dnhiOWN2anNPQWdkbGlnM2xJTTRSbHJqSCtwOHFEVWJ2WjFoc1hycDhrTTZWbmFpUUc0aWIrMVJwWFBpNG9PYkNsMmk1K3FSRmY3Ymk0eFVBQ1MvMDRZZHVtRzIwVHhXU213c0MraTlqQWwwQjhib2JSTlpUQm4vcmVLV2NBMk9IaU5yWnRaUHM4Y245N1IwdWExdDVyRzRzbnZ4N2tZL1lvMFRuaFZHN1VzR0grekZXS0E4RDVrREdkV0F6MVJFdWEiLCJtYWMiOiJiY2FkYzExYzgzNzRmMzA3MzYwMTNhZWIxYzIwMGJiNjdjODYxNmUwNTU2OTkyZjM4MTdmMzViZGFhMDlhOGQ4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZISjZYd24zYkdabzJXUkM1MUdvbkE9PSIsInZhbHVlIjoiNnBvbS8raTJzbkxycjhIejlEUjdKZ0JuclJRV2RlMjBqTk5zRFhsclJxaVVDZ0NBQmg5a0c3dEZaYmV1NEhhNmlOQWlqTFc2VCtkMGM3MXJYeFRJQVhVWkFTUStpTTQwbFFpMWw2U0cycVJvcEVRd0t2VTh5N252S21yVTRRdDJMQjRtQWVJQjB3cFpYYkwrRE00V3BCR1QvVHJZalVnRDNXN1ZFclJveVlSQ0JjNHZKNXFFeWFFMFJ1YmRhbkFISUNyRVlsVzRnN2lWbnZVdTJkQUZIdEM4V0hUdU8xYWl2Q0dva1VuVTJFMUJzQy9OeGY5SFRVaGJESWVZZGcwYWZXN2lxc2pZemoyY0VTd1lzSHBiTkhQT2RLZXlTR05CMVY1aXZWRUM0YUhkb2ZFMXZ3aFRmWExveXhzeVFJZUNOUS9OaFR2RFZOdVAvOTVVUGRrQzVydS9tY0pEZHltRHFLSTVEOGdiQUJRRVh6R0RsUVdsaHp2RTdOOURBbHlMdUMzWFhZTWp6L1NGRVdBL2ZNV0JtNVVITDBFZXdkb2gzMTBPOVFQMUtINE5reXBKMGVhQWJMMWl0RVZBMENzd0tBUzhEK1J2Rk0wMTFhOWV3NGtUN2JQK0RvREMweHFuZ3JNbElZcXdaazRSUmllanZsWXAvOFNzOU5zK1RrUFAiLCJtYWMiOiJjZTVlNmI1ZjAwOTEyNTIyZjU1YjBiNjE3ODlhZjg1NzJlM2U5YTIyYWQyN2M0Njg5Njk0NGI1NmRhYzIyYmZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1082905471\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1369152660 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1369152660\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik51cU45TUUrQ2x5K1p4ZDlhZ212aVE9PSIsInZhbHVlIjoicTR2cXVkNzFoVGtsOUJ5MXREQjB5TEhKLytrbGlJRUVDUVNJMVpJc1VTbUdLZGNtVWxNTlBLN3FKOTkreHJUMnFpUnI3VkF0cEk0bGMvb3dmM0JlY2t3QTZwSUdaZDVtVjFxWHNzaW4yQ3Z1R3NqamRvOGN4SzllWXgxZWhPaXhVY2lxTVZrOE1GQytzQUtlbXFhZzNVaFFvd0QwV0wybjMxR3F0S0g5Nm5yQzJlS1I5MCtnVGlFZmlCblhmUXlkK0lEanc1MFJHT2htWGVRM3IrbFBTc3JsTUcvTzdhY0x6ZWVTWktwd1JPMkFFTktVUlFKMkhtMHVha2NTQ0h4VzNNdzNvcEVvM1NnSExKZmtxcUdFSk1TSGhBY3NQOUcxRElRK0RjSjNZWkJmdWtITktneitndzR5Zm5sbU5LZCtiSHNRaGFOZHJHemhsVjZUQll6T21mZytWMDd2anUyM0RITjRwTm1hQWhsNEdEOHhLQktQM3lxelFxdDlRUTlPTThOUDF6NWNXM2R4aHlhaHNFS3F1bGhmN0ZxSGZlN1oyYU9NdkdRWjNwbzgvSlB0RkZhZkM4Qkc3L2JNNlhJbHhVWndDZFhMUWp6RnRsZE5BenIrL3MzSHFHVE45L2grUWNFZk9YYU5sYjVGU3l2NVlpQkgrbnRoWkhUam9EVWsiLCJtYWMiOiIwNzliZTlmNTRlZjllZjg4YzJmNGZlN2FlMGI2ODUzOGJkMGI1NmY2N2YyYzgxNmZjMThjN2Y4YzRiMGFlZmUxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikg3d2x1ekpMY2JlQUlMaGpkejgrZnc9PSIsInZhbHVlIjoiRENmWTR2T0JRNXFITmdld0ZWUmlhZkpJcG5QaThYT3JNN3NJbFh5bXAzS3hLK08xeHdkaU5Felk5YWwwVEwwaUUzdVE4aE5HZVBORkVsYUxoVlk5dTB1NmRrMTE1Y1FFNHJodDdhM0VIV3hxd0prZUZ6OFpXbzNYZ1djZGR3RkMvUVd5YXRFTFg5RUc3cDNxZXNyakRGNUE5d2lOOXJ5NGFxUTVucTBXVGhNRUl6NUtaSWRjdUkxQXpER3dsNGVWN052dXJmNW9XQzJ4NDJlbFdyVDhONXR4UHEwUDk5NGE1TU5oMEtlZFdYNitQZzg4OHZOdEFWRmt1d1BSeGdhMDdRZUIrNXU2T1FBbGpkQWZuL2Z1RkFjVTJza25BNktKbEV1S0xUU2l2UGlxd3lGcjJSQU1RelI4T2FrbVhFQXN3cVlWSkFNdUs4M1pBZTJEMEJZVUU3RjBsRFBhVWg5cXdNTHg0N2NoK2hWanlST2FuT2QxUnBFNUZDbEJ1QTB2bFIxN3ZSYmRJaWRYTXE1a3pOS0hQY29KUXNMcW1zZHdWY0JzQUE2dDBtdTFuYlcyNUdFakVJV2pFKzBUWE9qUUhLMSsxc0I4NGp5Syt0cEVYdnpKVXdTUjdYcHdvQjZHcXBicGtRbUpVRkwwUmFPOTJKZldYWWQ5Lzl4N01aVnYiLCJtYWMiOiI3NjYzMWI3NWE1OTk0OTM5N2QzNmYxMDJjMWUwZTAzNmQ0ZTk5MDllMmY0NDE2YWFmMDQwZTFhYzVhZWYwNjhmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik51cU45TUUrQ2x5K1p4ZDlhZ212aVE9PSIsInZhbHVlIjoicTR2cXVkNzFoVGtsOUJ5MXREQjB5TEhKLytrbGlJRUVDUVNJMVpJc1VTbUdLZGNtVWxNTlBLN3FKOTkreHJUMnFpUnI3VkF0cEk0bGMvb3dmM0JlY2t3QTZwSUdaZDVtVjFxWHNzaW4yQ3Z1R3NqamRvOGN4SzllWXgxZWhPaXhVY2lxTVZrOE1GQytzQUtlbXFhZzNVaFFvd0QwV0wybjMxR3F0S0g5Nm5yQzJlS1I5MCtnVGlFZmlCblhmUXlkK0lEanc1MFJHT2htWGVRM3IrbFBTc3JsTUcvTzdhY0x6ZWVTWktwd1JPMkFFTktVUlFKMkhtMHVha2NTQ0h4VzNNdzNvcEVvM1NnSExKZmtxcUdFSk1TSGhBY3NQOUcxRElRK0RjSjNZWkJmdWtITktneitndzR5Zm5sbU5LZCtiSHNRaGFOZHJHemhsVjZUQll6T21mZytWMDd2anUyM0RITjRwTm1hQWhsNEdEOHhLQktQM3lxelFxdDlRUTlPTThOUDF6NWNXM2R4aHlhaHNFS3F1bGhmN0ZxSGZlN1oyYU9NdkdRWjNwbzgvSlB0RkZhZkM4Qkc3L2JNNlhJbHhVWndDZFhMUWp6RnRsZE5BenIrL3MzSHFHVE45L2grUWNFZk9YYU5sYjVGU3l2NVlpQkgrbnRoWkhUam9EVWsiLCJtYWMiOiIwNzliZTlmNTRlZjllZjg4YzJmNGZlN2FlMGI2ODUzOGJkMGI1NmY2N2YyYzgxNmZjMThjN2Y4YzRiMGFlZmUxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikg3d2x1ekpMY2JlQUlMaGpkejgrZnc9PSIsInZhbHVlIjoiRENmWTR2T0JRNXFITmdld0ZWUmlhZkpJcG5QaThYT3JNN3NJbFh5bXAzS3hLK08xeHdkaU5Felk5YWwwVEwwaUUzdVE4aE5HZVBORkVsYUxoVlk5dTB1NmRrMTE1Y1FFNHJodDdhM0VIV3hxd0prZUZ6OFpXbzNYZ1djZGR3RkMvUVd5YXRFTFg5RUc3cDNxZXNyakRGNUE5d2lOOXJ5NGFxUTVucTBXVGhNRUl6NUtaSWRjdUkxQXpER3dsNGVWN052dXJmNW9XQzJ4NDJlbFdyVDhONXR4UHEwUDk5NGE1TU5oMEtlZFdYNitQZzg4OHZOdEFWRmt1d1BSeGdhMDdRZUIrNXU2T1FBbGpkQWZuL2Z1RkFjVTJza25BNktKbEV1S0xUU2l2UGlxd3lGcjJSQU1RelI4T2FrbVhFQXN3cVlWSkFNdUs4M1pBZTJEMEJZVUU3RjBsRFBhVWg5cXdNTHg0N2NoK2hWanlST2FuT2QxUnBFNUZDbEJ1QTB2bFIxN3ZSYmRJaWRYTXE1a3pOS0hQY29KUXNMcW1zZHdWY0JzQUE2dDBtdTFuYlcyNUdFakVJV2pFKzBUWE9qUUhLMSsxc0I4NGp5Syt0cEVYdnpKVXdTUjdYcHdvQjZHcXBicGtRbUpVRkwwUmFPOTJKZldYWWQ5Lzl4N01aVnYiLCJtYWMiOiI3NjYzMWI3NWE1OTk0OTM5N2QzNmYxMDJjMWUwZTAzNmQ0ZTk5MDllMmY0NDE2YWFmMDQwZTFhYzVhZWYwNjhmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}