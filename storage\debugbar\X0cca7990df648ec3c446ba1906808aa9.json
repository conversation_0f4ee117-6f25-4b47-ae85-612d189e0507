{"__meta": {"id": "X0cca7990df648ec3c446ba1906808aa9", "datetime": "2025-06-27 02:15:04", "utime": **********.044039, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990503.541286, "end": **********.044055, "duration": 0.5027689933776855, "duration_str": "503ms", "measures": [{"label": "Booting", "start": 1750990503.541286, "relative_start": 0, "end": 1750990503.978471, "relative_end": 1750990503.978471, "duration": 0.43718504905700684, "duration_str": "437ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750990503.97848, "relative_start": 0.4371941089630127, "end": **********.044057, "relative_end": 1.9073486328125e-06, "duration": 0.06557679176330566, "duration_str": "65.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722624, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00302, "accumulated_duration_str": "3.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0152292, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.887}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.028479, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.887, "width_percent": 15.894}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.034549, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.781, "width_percent": 17.219}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1018038416 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990356916%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRVdG9iWmtXOXRjMEVRZGJZenVJdXc9PSIsInZhbHVlIjoiL2NWR3VRZ3ZmLzRkVkZxVmxMbkZtN3RZVktSc0JOYk5YT2hGaEtOMWZySlc5MGpWc0RpaStSamVWSkt2SU5QSDVsbHE1S2JiS0NzTWN6MzBjZkU4bjR5NmcydGRZREEwa0NKNXA0cGF5T1JISkdHY0d0Q3JFT1cwWmRxWVZUL0NqcWVabVFQY1pQS2Z4TlJrc0w3RUpiYTQwYnp2TUYzb3FiNlZXVG5xYUpEVXpQK2JpT0xvRkdqeFpTN0NvNzA3SkxkWlh4TGhTNjNQVThSWGMvai9GT2pVdHVYNVE0SWRDTVAwMzlwY0RwTklzNEJtM3lHOGdsMnJkYUl3cHpGTlFrUkZ2LzgwU3VISkNBcTVsRUU3bU1kRVJIaC9FMHNqbTNoTTBWanZzUkljaTFGU2xuRXhvNnZ4MHpuOHpsSGRVZkR1TVRhRjZ0NGxjc1NvdG52bGIwZGhmMGxVMnhyL2lEZ0NZejFZemFyNUtENmNNSUxLYTdjS3prNjQ1UDBDVm41NXk2Q3RkalF4OWZ1b1BhTjhJVkFLMVNnTG9TOVpua1o4YUIwTkJFS043aVNuWFkvSENzeDVtcnpDVGRKY0ppOTVKNTYwOGk4ZlUwMmJ3SzJQWkxCMCtkbUQ0elhZTSs5cXUxMzdLbXBWQzhHc1lWYkNLcXFpbDhGTlJ6YzUiLCJtYWMiOiIwNDc1NWFjNjgxZDUzM2QwNzYzY2Q5OTBkMzkzZmIxYjU1NjEwNTljMWZkODVhYzdjOWM1NzE4MmJmNGM5MzY3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkpaZTFiS2ZHYVhRTGRzTXg0TTJlZFE9PSIsInZhbHVlIjoieUFLdVBISGJYN2hKa0dTZG5QRUFrVXlEdVBhdmRTOVV2a1hHWFJ4YVRXQXI2SUNsZWl5OHBiVmtma3E5aGI2R2YzRER3TWJvVFBuT21TazJyRDhRWE5kOTB6Z0FvUlFkellRQkRVd3FOUjRkVHN1cjN1VndXUGp6MFp2bUIwZEhOTkJHTjRLdWxTSVJ5WVRoVmlvS0IvV0NQanptSGN4N0xMcjltRU5EY0I3TjdjWGh1SmUrRkNNZW1GQTRYYW9aeUNMTHFZamFNcWVmRmcyZUZYaFVZemV0RGdXZy84S0dYdllib3Flc0NyRjRwdS9ERW9LdEF6SXovNWdEZDE3ZGtEQ3ZIaGdmWS9GZDVmMzhSS0V4Q2w5YmhQeHdQZ1hyM0NnYnNmSzl5aHRmcjFSVlRBdTZRenNrcTlvODlPUDlYc082QmZGZTd3OEFMNnRwL3lJTVJlVjZkRVpibHQyR1RZME9UZGlIV3kwR3pmRVVPWXNzT0l3S3kyVGt2bHQ0S0REbTJGeFVIMWdzRUpWOHRMREtsaWwvTjVTWDNTcDdXd0w0UGRDQWx4bjM2dTRlNVVsemFmbG9CdzM4Z29BZlhCMGlVZ25SNTZRb3diTEpqcitkeWExZGhOWGNvOGRoN01oWjJIQjY1OUVjS212TnVkbU9tLytML05yc3pzeE8iLCJtYWMiOiJjMDdjYWJiZDQyNWVkZGY2NjA0NzE2NTc5ZGNjOGU0OTdhMzRmYzU3MTNjODljODlkYjkzNGJmN2VhNjUxMTk1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1018038416\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1565716781 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1565716781\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1966990922 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1DZC91RFFHSCsyUWs3Rk9WZnJmTHc9PSIsInZhbHVlIjoiQXFBQnE1ckwxTVVEakpkK2FNYUVzZTN1OUtWbUdkZGZHWkdpeDd2ZWhxd1g1RFJibEtUbVFxZ2duZlZjLzBCdHAzUjdrNzdoSmQ5V0d6OWxMVGpmWFN6Y1IwQmYrWTJEeExXWC9KZUV4MkUzdW10VHhxQmM3M2dlWGtOeVJSbGk3QW5MbGRjc012V3dyM1dOREVpcXkwOTBxR0hXYWRFOTF3Z3YrM29FZnAvQllTWDA4aktKZW1NYmtXdHp0bjZSWUtkb01oVnJZMFJkMXdwSnp4MXJxZ1BTaW9QOE54Y3JmM2cyUDBEY215cGoxNWFNa3loQkFaUHZKNnZSSVp6SG5JOStUS0tGaUtFY1ZncHdRRW4rUnNMSWw3N0x5V0FHeHJhS2FDQ2l3aEJnNnk4aE5qckxpUkNGODV1RG1DeDYzY0RuS0t1UWpvdGVodTF4OHNVQVFuUGpEclhEU0J5bmF1cmNwcml0QW52eXhNWFE5VmV3aXZRNXJrOGpvOHF5TnlyL0tsQTlhMzJIaHNYOEw3VzY5T0s3K1EwaEMvU2lqUFpWbENrVUQxZjZsYzJQOTQ4ZytkVGoza1d1WTFjMFdiZERORS9XVE1BRVlZUDJqSTkveW9KbWRzb0x5c0wxSUFHc3pSdGQvZDVqL3M3cGhMQkFKMWxiNG1TVkc4T08iLCJtYWMiOiI3NjJkN2QxZmI4YWMzODQ4ZWUzNmRiNzYyODg5NDAzZGRkMGU5ZGUzOGUzMTBhZTdiNDI3OTU0Zjk0ODQ4M2VhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFwUlg3VXRCeVlCV2w5Rjd0UXdvNnc9PSIsInZhbHVlIjoienBTNm5HanhGYXppYVJiQktpK0VaZmxqemxKK1Jxa2plbm42ZkloWlEzcTNySGZoVW9KSytydjFJTVZyckdITXZ3RlpqUnZibWJ3TitlbFJUOWZLOU05Y3FzSm5uL2VqWDI3ZTJaQlR1YlQwVldYT3pic3ZDVTRNMm9UamNhbVJoTW40ak5VR2ZFcmQ0bzVjNzFuSXVJL3JaTmxvRkFLZ1h5VkpKSkZNOU9kOUYrVWlCK2tVVGdxZFV6eTdjOXp5QVRTckpOTXE4aC9vWHVWNkhEOTRnaklWWG1KYXhLcmZVVFBoTEc4ZVdmTE1GRjFqQkNWWFZqaDQ0Mm5hTGoyeGhqaHdPQUt5YXhqQzdjbFl0bUtFM3IvK3cwRHQ2TS85QU9wMHo1MVZqMWhrZlVVUGxPTXVadExsaXhNNFZLUGtOVlQ0TUhmS01JeVYyQ0VRMUdYdkliVEZOL2VYTkJTSlpFMTd6QlJ4SFNMeTBJd29pRmE1YW5ZcFRhbms0dnF0RXBVRTIxMUV4YXNOUE45ZG5SbmU2bFVMQ093T1FhRW4vaFdTM1dET1RjcWxUS1FVTEpKMTJrTTY5ZGVreVVSUUttaGJKTWV5NGlPMkZWYXE1cWxmdkZOL3RoekZFOG5nclFaMi9tK1VBYTV0dkhUVXNmL0Z4anlhRTJ2RTlpTmgiLCJtYWMiOiIxYTNlZTNkYzUwOGFiOTFlMzZhOWUwYWI1MTMwYWQ3MWVhNDM5NjllZmM4NzgzNGY4MzBkNjcxZTdlOGM5ZWIwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1DZC91RFFHSCsyUWs3Rk9WZnJmTHc9PSIsInZhbHVlIjoiQXFBQnE1ckwxTVVEakpkK2FNYUVzZTN1OUtWbUdkZGZHWkdpeDd2ZWhxd1g1RFJibEtUbVFxZ2duZlZjLzBCdHAzUjdrNzdoSmQ5V0d6OWxMVGpmWFN6Y1IwQmYrWTJEeExXWC9KZUV4MkUzdW10VHhxQmM3M2dlWGtOeVJSbGk3QW5MbGRjc012V3dyM1dOREVpcXkwOTBxR0hXYWRFOTF3Z3YrM29FZnAvQllTWDA4aktKZW1NYmtXdHp0bjZSWUtkb01oVnJZMFJkMXdwSnp4MXJxZ1BTaW9QOE54Y3JmM2cyUDBEY215cGoxNWFNa3loQkFaUHZKNnZSSVp6SG5JOStUS0tGaUtFY1ZncHdRRW4rUnNMSWw3N0x5V0FHeHJhS2FDQ2l3aEJnNnk4aE5qckxpUkNGODV1RG1DeDYzY0RuS0t1UWpvdGVodTF4OHNVQVFuUGpEclhEU0J5bmF1cmNwcml0QW52eXhNWFE5VmV3aXZRNXJrOGpvOHF5TnlyL0tsQTlhMzJIaHNYOEw3VzY5T0s3K1EwaEMvU2lqUFpWbENrVUQxZjZsYzJQOTQ4ZytkVGoza1d1WTFjMFdiZERORS9XVE1BRVlZUDJqSTkveW9KbWRzb0x5c0wxSUFHc3pSdGQvZDVqL3M3cGhMQkFKMWxiNG1TVkc4T08iLCJtYWMiOiI3NjJkN2QxZmI4YWMzODQ4ZWUzNmRiNzYyODg5NDAzZGRkMGU5ZGUzOGUzMTBhZTdiNDI3OTU0Zjk0ODQ4M2VhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFwUlg3VXRCeVlCV2w5Rjd0UXdvNnc9PSIsInZhbHVlIjoienBTNm5HanhGYXppYVJiQktpK0VaZmxqemxKK1Jxa2plbm42ZkloWlEzcTNySGZoVW9KSytydjFJTVZyckdITXZ3RlpqUnZibWJ3TitlbFJUOWZLOU05Y3FzSm5uL2VqWDI3ZTJaQlR1YlQwVldYT3pic3ZDVTRNMm9UamNhbVJoTW40ak5VR2ZFcmQ0bzVjNzFuSXVJL3JaTmxvRkFLZ1h5VkpKSkZNOU9kOUYrVWlCK2tVVGdxZFV6eTdjOXp5QVRTckpOTXE4aC9vWHVWNkhEOTRnaklWWG1KYXhLcmZVVFBoTEc4ZVdmTE1GRjFqQkNWWFZqaDQ0Mm5hTGoyeGhqaHdPQUt5YXhqQzdjbFl0bUtFM3IvK3cwRHQ2TS85QU9wMHo1MVZqMWhrZlVVUGxPTXVadExsaXhNNFZLUGtOVlQ0TUhmS01JeVYyQ0VRMUdYdkliVEZOL2VYTkJTSlpFMTd6QlJ4SFNMeTBJd29pRmE1YW5ZcFRhbms0dnF0RXBVRTIxMUV4YXNOUE45ZG5SbmU2bFVMQ093T1FhRW4vaFdTM1dET1RjcWxUS1FVTEpKMTJrTTY5ZGVreVVSUUttaGJKTWV5NGlPMkZWYXE1cWxmdkZOL3RoekZFOG5nclFaMi9tK1VBYTV0dkhUVXNmL0Z4anlhRTJ2RTlpTmgiLCJtYWMiOiIxYTNlZTNkYzUwOGFiOTFlMzZhOWUwYWI1MTMwYWQ3MWVhNDM5NjllZmM4NzgzNGY4MzBkNjcxZTdlOGM5ZWIwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1966990922\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}