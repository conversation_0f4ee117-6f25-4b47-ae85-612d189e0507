{"__meta": {"id": "X3901b4366743cc912a71862c456a4923", "datetime": "2025-06-27 00:25:52", "utime": 1750983952.03421, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.570222, "end": 1750983952.034224, "duration": 0.4640021324157715, "duration_str": "464ms", "measures": [{"label": "Booting", "start": **********.570222, "relative_start": 0, "end": **********.961723, "relative_end": **********.961723, "duration": 0.39150118827819824, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.961733, "relative_start": 0.3915112018585205, "end": 1750983952.034233, "relative_end": 9.059906005859375e-06, "duration": 0.07249999046325684, "duration_str": "72.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041768, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023829999999999997, "accumulated_duration_str": "23.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9878712, "duration": 0.02259, "duration_str": "22.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.796}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750983952.020991, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.796, "width_percent": 2.644}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750983952.027324, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.44, "width_percent": 2.56}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1659610465 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1659610465\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-575780731 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-575780731\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-271038761 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-271038761\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1202637758 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983946543%7C55%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IitHV2p3M204MzFsZnBJdjZVc0UzK1E9PSIsInZhbHVlIjoiaFc5b2h4T3NSSkRMU2E4K3VOYkRPWHU0UWQ3Y3pkSFlYZEt2eFlXNUxkS09kSmJPckR6SmR3aFRpckxLWWovN0JxcVhSWTFpWlBFbU1sR1NkUTlQNVFKOXFhZjBxRWtiTWxpWnBSVnExZjhMd0Q2eTl0d0ZZL3E1MnZBMWxydmtRMW9HRkhuWFpLUTNtZ0JxdWh4YTdZa0hadTE2UHUxOGErNHJaTnpub1o2K0JOT3F0U3FRb0d0dHdOUjhEY2VsdmVrSS9nOFlaamtZaUt2SzAva1hYN3Q1RExMUU1iN2pQYjNCY2VDQm9rQjZqNlRackNzOUF5NXlGTXk5cDVLZmRvNS9Zd1BZQktTWCsxMFAyVkdNNGozQTc2dlFvaml3YWRwNFlYdE5icWZXTDFvUHNvWEpmUWpFdFRLMjA0V3g0Y0htVEllZG0zcEFlOEdqeXJlWS91VUFhbkoyM3JKRzV4R0xnVG9WcnZtOEtsZkFFSVZFVzNkNU5QTGdxa09SVWsxNCtDaExRTldCUU5VbFZ5VTFWaXFEY0ZRK25ZQnhDb3hXdjlaaVhPd1lBc0FZQlNpZjVvcGZMSkNSVWc4MkpQa25ZRXBoY1dxWjFVbWNCc2tXaDhTcEFGV0NIT3pLTTgvZEdGZU11T1V3OVBnM3hlTEo3QzRKTFhLYjN5ZS8iLCJtYWMiOiJiODQzNGE1MTQ1Y2RiOWIyODA5NTdhMTM1MDQ5YTBiZmQwODU2YzI0Mjk1MTE3MTM5ZmRkNjdiZjNkNWRjMDViIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllSQndid3JoamRHNHhrRlg3ZHU3bkE9PSIsInZhbHVlIjoiWWVXVDJUVHNHc3JLVmxXZ2dXL01EVnZqdEgwaUpkNkFDTlhzOHZscXlvRVZTTHdIYytCeitUVnZBQWlPRHVDRENZZVk1Ui9nNXdkRlIxUkQ4SzNjRlhsY0tqS08xTndsMDVNcHV0UW9adTRsR3FEZDFacXgzRlRSSTFYMmFJZ3B4NDNFVDIwcWpjMDE1MFhxam9kZlphTk40U3ZVSW9kNlA4dWFUM3V2UFh6SW5yM0lhNlVCUzVKRUpILzF3TmhkcGMvRjJnaGVVYWw5b0VvL1Z6RTY4ZkIyUXROM0ZZcHdrMnZWOG0wdSs3U3BMVjFJVlNNL2ZNMktrcDFDQm5rOTc1NGZJakJ5YXY0SVIrUjFTbkRvNkJTQ2tlUG5BTmpUY0xmT1Vta3E1WnRyRnBkdis5UWZjNmh4L2dUWlVlV1Q2MW00Ym91b1hzcExiTHhjRm9JREk1QWJwdkIvS0FlYkhORVR5MHd2NmVCQ0VnaSsyYXRzZVY4N3BnRlVyemJLa05hNDlWR0hQT1FpdUZXUlJVSWxtZ0o0ZjNwZTE0aGk0UDljQjNRWFJadUlTanVRZDlBUm1yV1JYQndRQWRwWmpqUkx3a3F4M2E4S2kwRlZsUDdyM2tLYVA0eGFEaHZSVDlHbmZxd3pjUmVqMlE2aFByYU5FRmhMRmhaeU5qdnUiLCJtYWMiOiJlODMxNmY5NDk2MGY1ZmJjN2ViNDhjZjVkNTA5NjUzMjRjMGQzZTFkZjM5YWJjYjcyNjkwMWI4MDYxZjYxZjQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1202637758\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-935379282 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:25:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IloxSUFPTEdrSUszbHdOUkdsUmFQYVE9PSIsInZhbHVlIjoiaTZCQmNRRzJ5UjZPNkZTNVlmaHROZUdaUWJxSTFsRlFaZjZqckVpYU5rVWpSWmhObnFtQ3l4eDlwRnNRYzBsSXBPSXEvTUo5VmpudFdUUm5GRkNTSElUTHZDZ3IyZEdBNU03Qm9aZUdjZzNEWUdPc0VGNDViT3pmQkduMmxYMkxmaE1qUjhIczJ4djcyVS9XUHY5SmdhbGhORWdhRDhWTWZlVGp3OUREY0xHZURVeGJaa2F6UmtsQXRhMWJ1aFVVQVlwcDErNENqeCtzd0JMeC9FRk9SZm5oNFVpUjY2OTUyRGFWaHI3UW0rN0RPQ2tMaGk2d1VselVWOUFVWGxhNWRrWmhva0FhTXlPZFFUTkNla0wrb2gzSFJiQmhmYVBJUjVNQVlDdFR6bytMdzFRRURyVlhyNEFnK2ttUCtLdysvelR3cmwvQndkdUgvUTlWV2JBaUg4T1QrR0J0VzFVbzZ3WUh2cWNGMXgxNFVUeDBuWTlQbHQzTTBERzhVN3d6cWpKT2dUZkNTOXV2L2E2TFhhTEx5eGhDYmhBbm5Ya1E5a0JNOE9uSHpubEtiTGxRbzhkSVN5a2dPVUZtVzdZTEFYQndSNE1yUE9QSnR1RkIyQ1RTcTg3RmR6dXBoZWp4cVhneStyYnFycXNDNjNxSnN4Y0NlUjltRDVCaitGZVkiLCJtYWMiOiIxZjZlZGRhM2E0NzVmMWQ2YzE3M2NiNDAzNzFlYzU1MzMzY2VhMjQ4ZjNmZTczNmY3NTVjY2EwN2JjMjg3YjJlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:25:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFUZTA2aFhlWnZmakNUcS81MXFKb3c9PSIsInZhbHVlIjoiREFHNWZsMWVWZm9mMmpNWVVJck9wOUE3YXZkbm1MaUYvbGlJeHZ1NlprOE1wZTV4K2hjVjAzMGdGV1JacS9VUkl2bkdUbWtkNERXd0VTL3l6b0U5YmV6Ym01MFJ0dGJubTFPemsvVmQ2U0diTU12TEZHTmFZc0dKamsrTm16WEo0djYzQnE3QzFPN0YreE9IanRrU2NTRVRoRWdROUMwcGswNkpmbXRHU1ovUnJaR013UmpERDRBRndlL1hxZGJ5K3RQWE9WL0cxZUZOcWd1eThOVDNGNitYK2o4bzFWd1RmcHZNd3VaQVlyeGZOOGF5V3BUVWt6VWFIVi9LWHFyYytpM3psZmtITUtwajRQS1hhWjlKNGVuTkQyUXE3WnVzOC9pMlhybDBOSmdsY2c2V3BWakRzVy80KzV0WElhUkRadjVOWEVCZ0o1eEJ2ek9IemI5aFMyT25QSmlJR0tib1YxMThGVkNBVFNuUWtXdExWNXFBbmhxQTRxWWdlUG5XOThzYmE0T2hJSnRXWDU2bmxmajh6dk55cTRheE5qTTEzcUlUazc1MnhzZ2E2SHB2QWJ6b3F5OEM3Vjd6UnJQU21kOENaOEZLcEltVklFa2NKcmlnK1luSGpwOTNsdkEzSzZzVHFTMFhUb0dmaWRtTnFJV3FNVWVJMkRRNi9DbXkiLCJtYWMiOiI3YTA0MDBlMGY2MzA0ZjhkNWYzOWNlNTc5MDVlODNlYzYzOTI0YTlhYjBkYTZiMzkwMjRjZDBhNzQ1NjExYTA1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:25:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IloxSUFPTEdrSUszbHdOUkdsUmFQYVE9PSIsInZhbHVlIjoiaTZCQmNRRzJ5UjZPNkZTNVlmaHROZUdaUWJxSTFsRlFaZjZqckVpYU5rVWpSWmhObnFtQ3l4eDlwRnNRYzBsSXBPSXEvTUo5VmpudFdUUm5GRkNTSElUTHZDZ3IyZEdBNU03Qm9aZUdjZzNEWUdPc0VGNDViT3pmQkduMmxYMkxmaE1qUjhIczJ4djcyVS9XUHY5SmdhbGhORWdhRDhWTWZlVGp3OUREY0xHZURVeGJaa2F6UmtsQXRhMWJ1aFVVQVlwcDErNENqeCtzd0JMeC9FRk9SZm5oNFVpUjY2OTUyRGFWaHI3UW0rN0RPQ2tMaGk2d1VselVWOUFVWGxhNWRrWmhva0FhTXlPZFFUTkNla0wrb2gzSFJiQmhmYVBJUjVNQVlDdFR6bytMdzFRRURyVlhyNEFnK2ttUCtLdysvelR3cmwvQndkdUgvUTlWV2JBaUg4T1QrR0J0VzFVbzZ3WUh2cWNGMXgxNFVUeDBuWTlQbHQzTTBERzhVN3d6cWpKT2dUZkNTOXV2L2E2TFhhTEx5eGhDYmhBbm5Ya1E5a0JNOE9uSHpubEtiTGxRbzhkSVN5a2dPVUZtVzdZTEFYQndSNE1yUE9QSnR1RkIyQ1RTcTg3RmR6dXBoZWp4cVhneStyYnFycXNDNjNxSnN4Y0NlUjltRDVCaitGZVkiLCJtYWMiOiIxZjZlZGRhM2E0NzVmMWQ2YzE3M2NiNDAzNzFlYzU1MzMzY2VhMjQ4ZjNmZTczNmY3NTVjY2EwN2JjMjg3YjJlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:25:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFUZTA2aFhlWnZmakNUcS81MXFKb3c9PSIsInZhbHVlIjoiREFHNWZsMWVWZm9mMmpNWVVJck9wOUE3YXZkbm1MaUYvbGlJeHZ1NlprOE1wZTV4K2hjVjAzMGdGV1JacS9VUkl2bkdUbWtkNERXd0VTL3l6b0U5YmV6Ym01MFJ0dGJubTFPemsvVmQ2U0diTU12TEZHTmFZc0dKamsrTm16WEo0djYzQnE3QzFPN0YreE9IanRrU2NTRVRoRWdROUMwcGswNkpmbXRHU1ovUnJaR013UmpERDRBRndlL1hxZGJ5K3RQWE9WL0cxZUZOcWd1eThOVDNGNitYK2o4bzFWd1RmcHZNd3VaQVlyeGZOOGF5V3BUVWt6VWFIVi9LWHFyYytpM3psZmtITUtwajRQS1hhWjlKNGVuTkQyUXE3WnVzOC9pMlhybDBOSmdsY2c2V3BWakRzVy80KzV0WElhUkRadjVOWEVCZ0o1eEJ2ek9IemI5aFMyT25QSmlJR0tib1YxMThGVkNBVFNuUWtXdExWNXFBbmhxQTRxWWdlUG5XOThzYmE0T2hJSnRXWDU2bmxmajh6dk55cTRheE5qTTEzcUlUazc1MnhzZ2E2SHB2QWJ6b3F5OEM3Vjd6UnJQU21kOENaOEZLcEltVklFa2NKcmlnK1luSGpwOTNsdkEzSzZzVHFTMFhUb0dmaWRtTnFJV3FNVWVJMkRRNi9DbXkiLCJtYWMiOiI3YTA0MDBlMGY2MzA0ZjhkNWYzOWNlNTc5MDVlODNlYzYzOTI0YTlhYjBkYTZiMzkwMjRjZDBhNzQ1NjExYTA1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:25:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-935379282\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-25689797 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25689797\", {\"maxDepth\":0})</script>\n"}}