{"__meta": {"id": "X86bd8343174a52d4be3df276c2ccf3fc", "datetime": "2025-06-27 01:05:25", "utime": **********.287507, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750986324.741466, "end": **********.287522, "duration": 0.5460560321807861, "duration_str": "546ms", "measures": [{"label": "Booting", "start": 1750986324.741466, "relative_start": 0, "end": **********.228128, "relative_end": **********.228128, "duration": 0.4866619110107422, "duration_str": "487ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.228137, "relative_start": 0.48667097091674805, "end": **********.287523, "relative_end": 9.5367431640625e-07, "duration": 0.05938601493835449, "duration_str": "59.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029264, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032, "accumulated_duration_str": "3.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.262972, "duration": 0.00222, "duration_str": "2.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.375}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.274338, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.375, "width_percent": 15}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.28034, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.375, "width_percent": 15.625}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/chart-of-account\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-853522445 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986322642%7C82%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRqRzZvV0JsRnVFZTZGRmsxNlBTZkE9PSIsInZhbHVlIjoiNlphQThlaUZIYXNCQUprcG5KNjZiUWNINWdua1FPZlN4dHB4eXN1T2xCZC9zUjNHZlltUFJ6MXpvbXRUY2ZPUzhMWVcxb0tuZ3RZRWd5bGtmWjZFazVNM2FqbWl1YjEwRldnQ2Y5WjFRNHV5Q2gvS1ZnKzBjWkFGVHc3d3hWOVE3c0ZyeVNkUEROaWsvSnBnTmZROFdEcUp5cWJBZWFiYmZxeW83NEdmd3JWdjZSZkZ2cVRMc1FPSFg2aVFxemZZOXoxUHdMVldNK2dUL0hqRk8xUTh5alJYaDE5TjQrK2c5WkIvdXpTQjdJMG9OYzhmWko2V24wSFhMclhKYVR3Z0pBazlsdjEzdWFlVzlIU2pBLytSWGNwY2tqdnJxK1BEQVBwdHdYZmhDWXNQMFhwenB4ZFBiNExoQXdJM2pCZnRzWHNqaVR3V0pVT2JtVFdpWW5IVTVReENibWRsT25uWFhMT2h3V0JNUytBbEVlSjRKMExwL3ZGVnRQUlMxUXhyOExCQTRTVW40VHhSd0pxMllydk9Mci9UdDZHd2ZoQldjNTl1bDFPSzFkWVo2Vk0rVzZMYTRxWTZBUFh6OXdna0dreE9PYVhLK1lXNHZSem5vRTZ2L2F4NU9ieTVIK0RVR2djWGZzd2Q4UytEb09GcGI1VDczWWRjQmZKOU0rQlIiLCJtYWMiOiI0ZWEwMmRlZWVjNTMyOGRlODI1MjNhOGM2OGI4MGFiMmVkODIzOGI3NGY1YjUwZDM4MzlmN2ExYTRjMWFlZWY2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZWRFYwb1FKK0pmU1UrOGhiQzh1T2c9PSIsInZhbHVlIjoidVlYSjEyTi8zNFBNZzJ3ZjNlU29rbVhlOU41Z3FzMzM3SVFTcXhtVDR2SEw1YlNoWnQraWtLb0JFSDZEb0lRQXpyeHQ0cnN4WGIzMm1Xd09ZQ1paNm85Q0NpL1VuTHNDUVhrdWFjdkRxUHhZNUg3NG5rd3M0dkR1VjVabFNDY3pFSkZDbERZWGF6SnlwcklURnVRN0FrdzQ3b2ozTldpMkNhWlRiZjJ0QXIxM1k4U25ybXpnd2lBVHBwTWRYLzVLRGNkUUM2cjliQ1laYWZoZnU5VVlab2V1TERsZXU3ejl3aVI0S21qUGN4UDA4elE5aG41UElldEhpY2lVZUxnWVNWaENHandBam1CVHBoZTllSnVvaDhlSFpQNkE3cVIyOWNTRm92Y2Y3alJURVBJTW50MVpJanozbXU1U3R0OWRuNEdSdC8yVk1BLzMzZFRRU0FTbWFhMjkvUjVUd25GRkMrOWhWcWc2T0dYdEhWUDNMQ0NGazNlL25QWUsyRncvNFB4R0taYTBTeXIxK3JIQi9MVTJQUlZWTXhkMUtXbWNvUEt0N2dsUllSLzZVc2cvdUtJd3lrdVlWODdPZ2t2bTR0VEpvajIrWlFQcGxzaTdNU2IrRFNzM2JwUFFHLzk5aEltSGR0b2ZKazFPN3R6Ymc1VUNmQlhrL1I5czBlTUoiLCJtYWMiOiI1YmZkNGUwNzZkZDNmMmU1MWEwMjg3NDYzOWI3MzhjMzliZjVkM2Y2NGZiZDlkYzEzNDczNzYyOTQxOTUwZjA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853522445\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-444881955 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-444881955\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1166464400 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:05:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpOWkZMRUJEeDhHWmM5cVRlWkFTUVE9PSIsInZhbHVlIjoiOVAxVldYWENrWDdvYlIrMTd0SWpVaWpXU1NsMG0wdHMvM0RaZHFFOTVxQ2VnKzRyOVZoUk1LeUJab1lQT0hJd2V5dHFNZFkrZWQ1N3k1U3N0Rkt0d2FzcERSWUlUVkNSN1c1Rmsyd05acEZMbUptUnVLWVNTMGI4SlRjckVyWGZzbENNdGtMK25aa0Q2M3ZyRWVrZ3JhRVNMTTkrNzkyY0orZ1ZtNURxWnF0aFRCTm16aTNXTDhrQjI5SWl6RVBJMmZXM3JsbnV6bXdVK1ZrMThVeVB1YTA2djY5NUdMcldBY1J3blFTbVMwcjlReXpCbDRzdlZ2cE1lMERaVTBPd3pJVndCQUtValNLTzErNTMzeXN6NWYxVlAzbndxRjZSa2RZWVl6Zyt4ViszNXNZSENXZ21pSHdLcWIyNnZnaVJQNFpreC9PSHhQNGd3dGk1QWtubnlhY3NIUW9saUFEQWhjV3N4djIzSnlFNDJreGxVQm4xVGxGUGIzNys0d2Z6ODZab1NRSTR4U3ltSjRxamVVeXNudnRRZS9rYThZSnhFVy9rVTJDQjFDMndxdVplZEJBelRDRDJDNHVuNlBMZFI5aHR2SlRuL2hjOEFDNWlVdXlsUzNlZnlaKzd4RTErbFNVc0E3MjhBRTJZYm56YXVRV2F4Q1dlYkd3MHZpNnMiLCJtYWMiOiI4N2E4NjdhYmM5NGIyYjMyZTEwZjU2ZjY0YzdjOWIyYWVjOTY4ZDY2MjFhNjczNzJhZGRhMDZiZTM5YzkwZmQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:05:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IllnRGtDRElSTm9Lak80bFRYVTFxRHc9PSIsInZhbHVlIjoidEpBWlVkOVFIYUg0dzNuNzJJWWJwbzFFZy9tMC9RVCtRczJSRTZwSFIxWDVvbklmazMvZDVXMFd6RHZyWWdySGdYZzZyTDRCT3NyVktBejltR0NPY2FUanN0MkRMNEU1YWJiSEh2TDhlWFZaZ1RlU3hlaTJ0RXJBR3lWd2xRcUhET0FzZDhvczhLQkpuSDhPYTVra0pzaS8zTkFVbjRmRlQyRXlITWJPc3VIb2g5RVZYVVhoNERPVnlVY0VuaE5NcmhuQWRsNXlLdEZUVjJyeHBETnFjZGZqQm9kdmtoNSs1dmJMNHNiN1BkSWJITzVZdHZrbk5teXVnb0VsdGI1SGp2aHVqTjA5di8xR0xmUDl0YkFsODJQbWdSbWpVbm1vQzArSDNTTDJxUHR6TzBWbUV0ejh6RFh0dkVWbUhUWXFMamllY0FuanZuVlV3R0gvMXdXQ0owcnd1em0rT0IyVmVVVG8rQlVkNVptbmZMT05CejBlNmc1QXZLbFNzS3FqV2RqOG1RZlRxVklqWksxbDVQcDN3OG9ZanR5eHdzdmJYYkdxVERHWEtRZDJ3cU9oVEFSYmRidGgwbG9ZS2ZleVVJTWJhUnBTUkpabHJ3RWV6c3ZCS3hGWk04dHpxczYvaEZiUmJqQm56QUlJL2l1TFNqOWZoZmZleTR1YStHeUoiLCJtYWMiOiIzNWUxM2I2MDE2MDMxNzI3ZDRjM2FhMTBmY2U2OGYyNjQyZTQ0ZGFmYjM1NDg1YmM4NGMzMTAxYWI3NzA0NDQwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:05:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpOWkZMRUJEeDhHWmM5cVRlWkFTUVE9PSIsInZhbHVlIjoiOVAxVldYWENrWDdvYlIrMTd0SWpVaWpXU1NsMG0wdHMvM0RaZHFFOTVxQ2VnKzRyOVZoUk1LeUJab1lQT0hJd2V5dHFNZFkrZWQ1N3k1U3N0Rkt0d2FzcERSWUlUVkNSN1c1Rmsyd05acEZMbUptUnVLWVNTMGI4SlRjckVyWGZzbENNdGtMK25aa0Q2M3ZyRWVrZ3JhRVNMTTkrNzkyY0orZ1ZtNURxWnF0aFRCTm16aTNXTDhrQjI5SWl6RVBJMmZXM3JsbnV6bXdVK1ZrMThVeVB1YTA2djY5NUdMcldBY1J3blFTbVMwcjlReXpCbDRzdlZ2cE1lMERaVTBPd3pJVndCQUtValNLTzErNTMzeXN6NWYxVlAzbndxRjZSa2RZWVl6Zyt4ViszNXNZSENXZ21pSHdLcWIyNnZnaVJQNFpreC9PSHhQNGd3dGk1QWtubnlhY3NIUW9saUFEQWhjV3N4djIzSnlFNDJreGxVQm4xVGxGUGIzNys0d2Z6ODZab1NRSTR4U3ltSjRxamVVeXNudnRRZS9rYThZSnhFVy9rVTJDQjFDMndxdVplZEJBelRDRDJDNHVuNlBMZFI5aHR2SlRuL2hjOEFDNWlVdXlsUzNlZnlaKzd4RTErbFNVc0E3MjhBRTJZYm56YXVRV2F4Q1dlYkd3MHZpNnMiLCJtYWMiOiI4N2E4NjdhYmM5NGIyYjMyZTEwZjU2ZjY0YzdjOWIyYWVjOTY4ZDY2MjFhNjczNzJhZGRhMDZiZTM5YzkwZmQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:05:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IllnRGtDRElSTm9Lak80bFRYVTFxRHc9PSIsInZhbHVlIjoidEpBWlVkOVFIYUg0dzNuNzJJWWJwbzFFZy9tMC9RVCtRczJSRTZwSFIxWDVvbklmazMvZDVXMFd6RHZyWWdySGdYZzZyTDRCT3NyVktBejltR0NPY2FUanN0MkRMNEU1YWJiSEh2TDhlWFZaZ1RlU3hlaTJ0RXJBR3lWd2xRcUhET0FzZDhvczhLQkpuSDhPYTVra0pzaS8zTkFVbjRmRlQyRXlITWJPc3VIb2g5RVZYVVhoNERPVnlVY0VuaE5NcmhuQWRsNXlLdEZUVjJyeHBETnFjZGZqQm9kdmtoNSs1dmJMNHNiN1BkSWJITzVZdHZrbk5teXVnb0VsdGI1SGp2aHVqTjA5di8xR0xmUDl0YkFsODJQbWdSbWpVbm1vQzArSDNTTDJxUHR6TzBWbUV0ejh6RFh0dkVWbUhUWXFMamllY0FuanZuVlV3R0gvMXdXQ0owcnd1em0rT0IyVmVVVG8rQlVkNVptbmZMT05CejBlNmc1QXZLbFNzS3FqV2RqOG1RZlRxVklqWksxbDVQcDN3OG9ZanR5eHdzdmJYYkdxVERHWEtRZDJ3cU9oVEFSYmRidGgwbG9ZS2ZleVVJTWJhUnBTUkpabHJ3RWV6c3ZCS3hGWk04dHpxczYvaEZiUmJqQm56QUlJL2l1TFNqOWZoZmZleTR1YStHeUoiLCJtYWMiOiIzNWUxM2I2MDE2MDMxNzI3ZDRjM2FhMTBmY2U2OGYyNjQyZTQ0ZGFmYjM1NDg1YmM4NGMzMTAxYWI3NzA0NDQwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:05:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1166464400\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}