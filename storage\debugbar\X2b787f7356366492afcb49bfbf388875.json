{"__meta": {"id": "X2b787f7356366492afcb49bfbf388875", "datetime": "2025-06-27 02:28:44", "utime": **********.619701, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.173929, "end": **********.619718, "duration": 0.445789098739624, "duration_str": "446ms", "measures": [{"label": "Booting", "start": **********.173929, "relative_start": 0, "end": **********.566271, "relative_end": **********.566271, "duration": 0.39234209060668945, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.566285, "relative_start": 0.39235591888427734, "end": **********.61972, "relative_end": 1.9073486328125e-06, "duration": 0.05343508720397949, "duration_str": "53.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45273096, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0020700000000000002, "accumulated_duration_str": "2.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.599459, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 78.261}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6111372, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 78.261, "width_percent": 21.739}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-687265389 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-687265389\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1523453834 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1523453834\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2047916549 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047916549\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-621973197 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilc0RE1Od1hRcUJVc3p0Y085VTlmOXc9PSIsInZhbHVlIjoic1N3Y2pkUmlQZ21JdmtmK2xXS25QQVVHMk9Zd0ZTQ2ljOVJDMWNzZ2ZHVmpNUVk1WVVKamFXR296c2dveHFYeUNLN0d0d3BEZ3dDOFhNSFBWL1pZWkUySjNjTGxleFRCeEZEZzhzM3BWWFFwZ3JVUWRHRmgvc3ZBTGZaRHZSTGdqbnlWbVlWaDZuVlRITWd5cGZMd2V1OElReXR5Vm1ybnJMNXVOWTJNV3BwZ2hhWER3RXBwbUJ0RXVoWnhpNlBKVTYvakZnN1R5NTZxVlpuZ2VCWUdrQ0FqN1hKR2hsZ1FpMHZtU3FCS1ZqaHNjVTlsRThtK1RrZ0p6aHJVRWQ1MGx3UWdIRDQyYXhFMFF3M0NTL3ZCNmVQNG45bFU3VkpMVmlUeDUyWHVOallsMWliTndTMHl4aXBDSFhud0RSRzFpSmxCb3BWNWxMdUlyRk1EOFd2VkxvUVphRWsrYXhFeTRYYzlCR3JjVzUxbW4wTU00Z2QzcFA0Z1NOZXIwcVk3d3IwaE8yT0Urb3k4bTQrdTU2c0pUd1prMFA2VUdZM091Qk1rZ0tBajNZUW9rV3FNb1dudWJ5MU1zUlhLSUJDMmpZdjhuYVBtaXB0a2JvRTZ5UzlpTjR2Q3B5aFBQbGdNOU1sR1dVWkhRUDl1aWtXcWc3Tzk2MVVTcmp6ZE05bXoiLCJtYWMiOiJhOTUwZjQ3NjA0MjYwNGExZDBlZGE3ZjQxMjAzNjNkM2E5NzkzMDljZWFhYzQxYWU2N2NkNTA3ZWZmZDhiYTRlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVER0kzNFhvVUJoS292aVYvM01ic2c9PSIsInZhbHVlIjoiOHV3STM5ZDJjVXZSQnlFWWNvZFZCZFhzZlYyTkppYXlqQnlWVkxXTXgyUmZVOUFZRDlxcDk0dm1JQlBsQ3Y4cWU5WUpXWlhaeFRzMDBLcVlhUmRpd1hWYkNXYTd4TlB3NEUwemV2S3U4ZHkzQ29sQVArMXdmVkE0dzlYODF6NHJwV0JqajZPYlFXK0hiR1RCRkhmcVI4TnRTMldCbDJJQndSZDl3UUE0UERBN3lmOU9PU0xQZlYxeERLQkoxclkycmhzN1FjVDRNdjRrN29TcWFJMU5zazdEZTJnK2tlWUpQMGp3Y2ozWWdtZVIvVHJBTXgzY2NCWXdzKzJJcmE4RHNaWGZuWG00VVd2bm5hbFJMRGZxajNRa0dKd3g3L1pCbklGamxVN1JkUU81WU5FZDJqcDcxSDhmeUI4bmt4T1l2NlkvM1VvZmtDc3Y1VFEwcy92UDhERTR1dGRTWGZSU1pOSktzRnp3Rm5qcytnRmhiVGVUZlFtN1Z4Vmk2eDBOcnBGTEJZanBMKytQOHhRT1o0OXgzUDRsRDhJcStqa1pyc1RuZzJwUmh6c0Y5TWsvbGI4VDV4NmYvSVAzWUJQNDBydEoyMmtLZE5jUFJ1enZERUlaQkpGRkdVdEU4UGtsamZKc2JlTzEzN2hrRWZtUURBOWJncS9JSWVjVSt6cUEiLCJtYWMiOiI4NDU0Mjg0YjFiOTkwMzg0MTVjNGU2YWEyMDIyNDEyYjliMmUxZWVkMTE4MzNiMmM5YjY1MTc5YTUzZmFmODY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621973197\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-541360709 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-541360709\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1629046719 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:28:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5tTnNJdTg3ODhENTBMZE12QjVhVFE9PSIsInZhbHVlIjoiMmI3K0V5SzA3U2xtcC9pd2ZXMkVKRnNNMGVYT3N5WlFObFhjYUhKYmlUVEJjSFZqbWozK1plU1U0cUdKbTJVT2ZDTlBWZUp3S25YTkVFTUNhU2Q5eXYxbldVVk80ZEpxVTlCVWkweUlpV2phUFFpKzB0RnlxTXFYb0p0WDlCWnJPQTBQQ09qcUJLS0JiM09ZaVMyb09jTUNib1ZLS09mQ2N3MDZLOFlEYVdqQmhBbGQybDJXcHVpczZHKzBndXNJUXEzRTNUY2Q3VDltZlN5V1grVDFNVHBnVFB5TWE3ZDRyRFBiTzJ4dnlvS0EyK2RIaUZQVjU2cG5Xc3BNdzN4RUhuZWlmTGt3Znd1K1hOR2xMSlVmL2x1QzFzdCthS1VXQjJwcTNEaUNuOXFzRnc5MlVzSVNuRGdSZWpRUUZ4WnRuU2lVc0ZHS0lrWm5WU3RLTHFIRlVvUXJybE1LVHBUdUVZa3NFUFpna09Sd1ExOHpOS3ozdUV2R3NBdHZnQzEvRFMvNS83bXliYSs2TnFFNDBrbXNzSC9BRi8veDlCcWVrWFdMQ2luK1NiTUZ3Q2llRXJwclhUZGcxR2JrNGQveFlrM2p3WFB5bmdVbGRzV3FlNzRMbG1PUXFGa1NFL25DWFlxVm8rVGNlZHlwc2dJNFNKTTZTUkluT3gvUG1rQjQiLCJtYWMiOiI1YmM0MTgxZDExOWI3MDExODcwYjg0ZmYxNzYyODg4Yzg3NGE2MjliNDU2ZmY3MWY5ZTQzYTE2OTkyYzdkN2Q4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:28:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjlGMWJ0TlpoWm1vb2xDTU50cTEzUWc9PSIsInZhbHVlIjoiN29KRE9kUWR2dEFFOUFsRnR4K3hGRjFkb05nWlhFREVhVGFlelJYWFhNUjJrdjV3VzVGemdXb1g4SjRRZjBhQ3c5OVA1cEw0UUt5ZGFLalArRUJ1ckpleUsxSHJKUGU3bGhKUEJQODJYRmh6SmRVK3o4UGV6N2hud2pzdlRYUTdSRElEK1J6Z25scm9idGFzb2lIYThHNm9ORjBYUlhibzR3Q0c1K2J6S2Z0SFZaR1k0WjA3M2xHdldmL0Y3YUQ3VSs2bm1vaHloRjg5TWlnSm5xRlgwSmtsaXZCRi90WTJzNm5YS1JqWFQzdm96NDZaM2ZKaE1qMDRncmpLQkl4SUNSZ3B5VDFKS25CenhiajNpWU04NTk0RDBmUlU0dWYrVy95YnMrd1p2WVFxVi82a3JvK3NLNjNwdDNKMEpZR05jSExKd203ZHI4ZEt0OFp6UVEzUnVkdkZJaHZaK0RhTzJIYWRyYzdpTng3WVd6cXVwaVdFRWhaY2RLa0hRdTdwcEJXM2hqRFZtWDJkU3BGUXA0eldoMWFTWWl2VEVYelBhUHRyR0tiTnFQZFdJOVZyWHBWcVRVbnM5a3huM3R0K0RLUE1Qd0lTSW8zV2E1UjJzZHlFYXR2MXVrdit5SXlnZFNMbGsvaHQ4ZDhjVGplR0Zac2JLSDhrY3JHQnpBSFAiLCJtYWMiOiJhNDA0ODhjNzM4NjEzOGY2MTQxOTdlYTY2MDExZDYwYzA2NjVlMTU2MzljMDQ3ZjI2N2E4MTg5NGYyMmI3ZWI3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:28:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5tTnNJdTg3ODhENTBMZE12QjVhVFE9PSIsInZhbHVlIjoiMmI3K0V5SzA3U2xtcC9pd2ZXMkVKRnNNMGVYT3N5WlFObFhjYUhKYmlUVEJjSFZqbWozK1plU1U0cUdKbTJVT2ZDTlBWZUp3S25YTkVFTUNhU2Q5eXYxbldVVk80ZEpxVTlCVWkweUlpV2phUFFpKzB0RnlxTXFYb0p0WDlCWnJPQTBQQ09qcUJLS0JiM09ZaVMyb09jTUNib1ZLS09mQ2N3MDZLOFlEYVdqQmhBbGQybDJXcHVpczZHKzBndXNJUXEzRTNUY2Q3VDltZlN5V1grVDFNVHBnVFB5TWE3ZDRyRFBiTzJ4dnlvS0EyK2RIaUZQVjU2cG5Xc3BNdzN4RUhuZWlmTGt3Znd1K1hOR2xMSlVmL2x1QzFzdCthS1VXQjJwcTNEaUNuOXFzRnc5MlVzSVNuRGdSZWpRUUZ4WnRuU2lVc0ZHS0lrWm5WU3RLTHFIRlVvUXJybE1LVHBUdUVZa3NFUFpna09Sd1ExOHpOS3ozdUV2R3NBdHZnQzEvRFMvNS83bXliYSs2TnFFNDBrbXNzSC9BRi8veDlCcWVrWFdMQ2luK1NiTUZ3Q2llRXJwclhUZGcxR2JrNGQveFlrM2p3WFB5bmdVbGRzV3FlNzRMbG1PUXFGa1NFL25DWFlxVm8rVGNlZHlwc2dJNFNKTTZTUkluT3gvUG1rQjQiLCJtYWMiOiI1YmM0MTgxZDExOWI3MDExODcwYjg0ZmYxNzYyODg4Yzg3NGE2MjliNDU2ZmY3MWY5ZTQzYTE2OTkyYzdkN2Q4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:28:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjlGMWJ0TlpoWm1vb2xDTU50cTEzUWc9PSIsInZhbHVlIjoiN29KRE9kUWR2dEFFOUFsRnR4K3hGRjFkb05nWlhFREVhVGFlelJYWFhNUjJrdjV3VzVGemdXb1g4SjRRZjBhQ3c5OVA1cEw0UUt5ZGFLalArRUJ1ckpleUsxSHJKUGU3bGhKUEJQODJYRmh6SmRVK3o4UGV6N2hud2pzdlRYUTdSRElEK1J6Z25scm9idGFzb2lIYThHNm9ORjBYUlhibzR3Q0c1K2J6S2Z0SFZaR1k0WjA3M2xHdldmL0Y3YUQ3VSs2bm1vaHloRjg5TWlnSm5xRlgwSmtsaXZCRi90WTJzNm5YS1JqWFQzdm96NDZaM2ZKaE1qMDRncmpLQkl4SUNSZ3B5VDFKS25CenhiajNpWU04NTk0RDBmUlU0dWYrVy95YnMrd1p2WVFxVi82a3JvK3NLNjNwdDNKMEpZR05jSExKd203ZHI4ZEt0OFp6UVEzUnVkdkZJaHZaK0RhTzJIYWRyYzdpTng3WVd6cXVwaVdFRWhaY2RLa0hRdTdwcEJXM2hqRFZtWDJkU3BGUXA0eldoMWFTWWl2VEVYelBhUHRyR0tiTnFQZFdJOVZyWHBWcVRVbnM5a3huM3R0K0RLUE1Qd0lTSW8zV2E1UjJzZHlFYXR2MXVrdit5SXlnZFNMbGsvaHQ4ZDhjVGplR0Zac2JLSDhrY3JHQnpBSFAiLCJtYWMiOiJhNDA0ODhjNzM4NjEzOGY2MTQxOTdlYTY2MDExZDYwYzA2NjVlMTU2MzljMDQ3ZjI2N2E4MTg5NGYyMmI3ZWI3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:28:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1629046719\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1995786495 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1995786495\", {\"maxDepth\":0})</script>\n"}}