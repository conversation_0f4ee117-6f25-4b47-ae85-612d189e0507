{"__meta": {"id": "Xfc3411f550286f5a34ffec63ea7f5e7f", "datetime": "2025-06-27 02:15:41", "utime": **********.438573, "method": "GET", "uri": "/payment-voucher/20/confirm", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990540.994909, "end": **********.438589, "duration": 0.4436800479888916, "duration_str": "444ms", "measures": [{"label": "Booting", "start": 1750990540.994909, "relative_start": 0, "end": **********.355016, "relative_end": **********.355016, "duration": 0.3601069450378418, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.355028, "relative_start": 0.3601188659667969, "end": **********.438591, "relative_end": 1.9073486328125e-06, "duration": 0.08356308937072754, "duration_str": "83.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46888208, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x voucher.payment.popup", "param_count": null, "params": [], "start": **********.41232, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/voucher/payment/popup.blade.phpvoucher.payment.popup", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvoucher%2Fpayment%2Fpopup.blade.php&line=1", "ajax": false, "filename": "popup.blade.php", "line": "?"}, "render_count": 1, "name_original": "voucher.payment.popup"}]}, "route": {"uri": "GET payment-voucher/{id}/confirm", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@showConfirmVoucher", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.confirm.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=146\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:146-149</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00226, "accumulated_duration_str": "2.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.389993, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 79.646}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4020221, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 79.646, "width_percent": 20.354}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/20\"\n]"}, "request": {"path_info": "/payment-voucher/20/confirm", "status_code": "<pre class=sf-dump id=sf-dump-1088279757 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1088279757\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1747961166 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1747961166\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1813630161 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1813630161\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1065857282 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990540466%7C12%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkFrWUYxTmFIVEFvOHVpUGdna1JoeEE9PSIsInZhbHVlIjoiZVA4T0Y3Q0lxZDdPL0FHOTRBTWdocEdVU1BCM2txcHVwZDFiNGszODVrYmlEMHI3V2lvcHpvSmhXMlNZVjYrTXdhVklGclFmNDdRQXN2dkdzbm14UHFzRE01OHIxT0ovTjJNeVFkUk05SS9tSTVMVUt0Z2xkRFFDNmdDTDg5ZDhLVStMR2RzWGZ2TGl6b1YrNlIrQXVwb1VXR3Vab2NCTXpCMnlmZ2hTenRpeE4rNFdxK2Q2Y2lGK3ZFME1zc2lFNUJIUzBIZ2FzTE9nWFlFTU12dWtJTHFnVkZzNGRtZmFUNFEzdTlWQ0tQY1M5OHRxQzk2dGdqMUx5S05iUmVydFNEMjRzakg2bU5zMDZsTjk5eFJ1cWxlTFFXWG02UUdMQnhjUkFuNHZqVDVmZExROG53WnF3MldzWk94NnlNRVNhc2MzbTlpTHlXQWYxS2tNMzJFUFlKZzFpVWdwb005cEs0M3V3NTVQN056Qm5ZMlFGdE8yK2NxbWVNUHJ5Qkc4TkVmTml5SlErd2dINmN3ODNoNzVyUVBOclF4VkV6WnBHMkhDQVFKcDNFeGJsa0ZQUEU2SUY4WFViUFF3Q1hRVERyT2hTNTFrQ1hNU2tVcUlITmk0MnVNc1BaWDl1UjU2NzJZNCtPTWQ2cjh3TzhXNzcxeGdmZVpmdWJMMy9HeXEiLCJtYWMiOiIzMTFiZDk4NTVjYTAyZWUyMjU3M2M1YTkwMjc0ZmJjYjNkMzAyYzg2ODc1MWM1YjFhMjFjOGJlZGRlMjQwYzljIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik5qRmRaOGVQYUpJZkhjQ1RnZGpVY2c9PSIsInZhbHVlIjoiVUxKK0Zyd0lKajRnblVGVnpQMURuL2xaRzVxdkllMmgrNVNzcCtQUXc1MHNuclY4dDRGWlRlM3ZKQUFTQlI4ZkQzZzZHMDhVSFgxamlmQjl3M0tDaldnOWE3MkhEeUlTL1ozYXFFd2Y2QzV5c1VWMTRsY3JLNEVUeTArU2ZWL0psOXRjODVzb3BNZ1JnZyt3VmdpQWJRUCtHMXpjUVd3V205TXpEMmJaMHRhOXB4TFNQdXppdWI2bjROY2lvWCtUc3VkTTdDVXM0Mm1RdGRxYzYxYVhmSjJwQWVMTWljUlNIdWluUUQ4cE4ycVZhZXBxS3dJVmZsZ0dDMzk5b29jVSsweHU3RXlhS2dXRWJISExTaXkxK1AvTlB0WFpiMjZKbEg1UW5rUlp4eEI5VkxYb2R1eVdVTVQrNkNEcE9BUUlpNlRRYkNmT2xaZmoyMzlEY1N6RGdHa1hDMzBqcWJRenpqZkg4U0I3d2hSSDMybkw1dm1VL1FRYmNvR3B5a1FaTk9GZXNvcWpTWHhIcGR5YStPeHNSZkhSK3hrTXVmTUh5TXFwcGlkM0xjc3RjR2VYUnpGY3NVNFRnQWM2SjdEdHMyOEVGU2IzOEx5R0k4Qnp0cmhZSU5aK2NjMGEwTE9icDRNTVdLeEw4d0ozQ0d6T0pjNXVHVHczZWorZXh3VFciLCJtYWMiOiJkYTZkNTQzZGQyMmI4NWZhN2Q3NTg2ZDNiN2ZiODI0MGYyNGZjZGYyNDE3MWFkZWE1M2I5ZTEwZGM5MmY4NmUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065857282\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1763692096 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1763692096\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1543566357 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRSbmFuV1dSOCtHSjdkbTd1d084NVE9PSIsInZhbHVlIjoiZXZZN1h4VEVmSC9WSmRNSUlrcmtzVnpMdk84V1hVeVJqcUsvZEhlbHdhVkRudjlQZVJneGdZeHQ2c1NwSGFpNnRRcXpRSE9xRjdZWlBoSWplVHpGM1ZyUEN6eWZEdEhRUlhicFMyenRHeE12K3pJRWtXYitUbXNJcjIrM2YzK0JOcm93eFRoQlVOM2VjWHhNTGhVeFhhTFRqMzBpRUM4M0JBQ0xoTDhvcUd2SVM4Znl0a1pGRXJzeEpQeEJPY3hVMG1KdnYraStzMzl5UldhY20vQXNTTXJqeHNIY1Z2b3RWMzlJMHBCRXNTMmpsMnJFV0xoTjExcklhUXh4SXNYNVdUL2xoKzM0ekJ3RFJNeXRCZUtvSWlTVkxZUXVlcXdWUkdJYzlEWGc1dytQclRqT0JMT0JkTmZpMWhMREV5OGoyY3dpNUxJM3hmcXBreG5KTkpJMTBtRmd0dCtHMGFZS1JVTGgwU0k4RXFVanJpZFJUYXdVS0RlYXRGQzRtZHJMclk0R1d4N1hxSnlZZmVtSnQwR3hoTjRDQVN1clp5NEVlMUZ2QVRDOTZKc2FBTk5pRHdJLzIzSmJmVzRpQ2JjaHNJRCtUTUQxeGRXT3I2dTZsT3NrM2JKMnpPVDRvVjB5NFJHaHhYWjU0Skp1SlQxNWhTM0ZLYnY3S2x3azJhL2UiLCJtYWMiOiI4YWM0MGJhOGQyY2RhNzdjMjMxMjZjYjE3NmIzMzRhZTQ0NjI0NjYxNTQwNTczYWE4NDJhYzRiYTkyNjRkN2NhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFMYmMwU3IzU2puQURVMk1hLzZDM2c9PSIsInZhbHVlIjoiV3BDSkFIbU1IL1VtckdKb1RoUjJWNnQzcUUyVzdvZlM2VzBJUmxkalpDSVVTb2lTMGs4c3luZi9aYjVjOGpnTGszYkx1ZWoyUDdnZmZwYm1wTmtlYTVsQTlYaERKdHNyUEZjeWJ3WlJ4SXNZakRmRFhNT3pHenRGOXhmdEJkUXk3SnYxQWxpQ1ZJV0pVVHBFMkwvdnphSmNBSU5JR2lzSmZmUUNpQ25KM3c5WDc1WUI2ajBYdndHbjRXSkoxTlB1bFh4R2N2Q2hab1IraHlJWElsdUZaNVg2ZFJVU0s3WWJvdjlFZE5HeU1ib3h4R2Rya2hiRWVOelQ1eUhhRDVFdFpvSlBxTW1UbHhQNU9tckhxMEQrQlR4YkdvM1gyTXR6cU8xa2ErMk51bjMzNEdWQ1lzMXNaZ0toSHpVWGwxK3AzZGFDS015SEp3NXBMRkx1cnMwVElKUmQwbGcyMExpSHhSVkRoM05CcFNtRGtDODkwRXBPbS96QjZuU21MZERGbEIxcUQ5R2lobUpsUXZqeGVBVTJUSHp6SDlGa3VjQUNsaEpNZFVOcDkyYi8xTTZhM09EdGFFV0hWMzdhcDhIU3BIZUl3amlTZHFlSmxxTElMNXhWOWZSdUFPWDNWdWNzOHp1dmttaWJheXpBWTJBcTRZYUR3azB0YVROSkZ4bk4iLCJtYWMiOiI0OTFiZDI5YWZlMTc4YTVkMWY5ZjMyODNiNTk1OGFlODYyYzY0ZTViMDg1MGQ2OTczMmQwYjAwZDc5NDU5M2Q2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRSbmFuV1dSOCtHSjdkbTd1d084NVE9PSIsInZhbHVlIjoiZXZZN1h4VEVmSC9WSmRNSUlrcmtzVnpMdk84V1hVeVJqcUsvZEhlbHdhVkRudjlQZVJneGdZeHQ2c1NwSGFpNnRRcXpRSE9xRjdZWlBoSWplVHpGM1ZyUEN6eWZEdEhRUlhicFMyenRHeE12K3pJRWtXYitUbXNJcjIrM2YzK0JOcm93eFRoQlVOM2VjWHhNTGhVeFhhTFRqMzBpRUM4M0JBQ0xoTDhvcUd2SVM4Znl0a1pGRXJzeEpQeEJPY3hVMG1KdnYraStzMzl5UldhY20vQXNTTXJqeHNIY1Z2b3RWMzlJMHBCRXNTMmpsMnJFV0xoTjExcklhUXh4SXNYNVdUL2xoKzM0ekJ3RFJNeXRCZUtvSWlTVkxZUXVlcXdWUkdJYzlEWGc1dytQclRqT0JMT0JkTmZpMWhMREV5OGoyY3dpNUxJM3hmcXBreG5KTkpJMTBtRmd0dCtHMGFZS1JVTGgwU0k4RXFVanJpZFJUYXdVS0RlYXRGQzRtZHJMclk0R1d4N1hxSnlZZmVtSnQwR3hoTjRDQVN1clp5NEVlMUZ2QVRDOTZKc2FBTk5pRHdJLzIzSmJmVzRpQ2JjaHNJRCtUTUQxeGRXT3I2dTZsT3NrM2JKMnpPVDRvVjB5NFJHaHhYWjU0Skp1SlQxNWhTM0ZLYnY3S2x3azJhL2UiLCJtYWMiOiI4YWM0MGJhOGQyY2RhNzdjMjMxMjZjYjE3NmIzMzRhZTQ0NjI0NjYxNTQwNTczYWE4NDJhYzRiYTkyNjRkN2NhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFMYmMwU3IzU2puQURVMk1hLzZDM2c9PSIsInZhbHVlIjoiV3BDSkFIbU1IL1VtckdKb1RoUjJWNnQzcUUyVzdvZlM2VzBJUmxkalpDSVVTb2lTMGs4c3luZi9aYjVjOGpnTGszYkx1ZWoyUDdnZmZwYm1wTmtlYTVsQTlYaERKdHNyUEZjeWJ3WlJ4SXNZakRmRFhNT3pHenRGOXhmdEJkUXk3SnYxQWxpQ1ZJV0pVVHBFMkwvdnphSmNBSU5JR2lzSmZmUUNpQ25KM3c5WDc1WUI2ajBYdndHbjRXSkoxTlB1bFh4R2N2Q2hab1IraHlJWElsdUZaNVg2ZFJVU0s3WWJvdjlFZE5HeU1ib3h4R2Rya2hiRWVOelQ1eUhhRDVFdFpvSlBxTW1UbHhQNU9tckhxMEQrQlR4YkdvM1gyTXR6cU8xa2ErMk51bjMzNEdWQ1lzMXNaZ0toSHpVWGwxK3AzZGFDS015SEp3NXBMRkx1cnMwVElKUmQwbGcyMExpSHhSVkRoM05CcFNtRGtDODkwRXBPbS96QjZuU21MZERGbEIxcUQ5R2lobUpsUXZqeGVBVTJUSHp6SDlGa3VjQUNsaEpNZFVOcDkyYi8xTTZhM09EdGFFV0hWMzdhcDhIU3BIZUl3amlTZHFlSmxxTElMNXhWOWZSdUFPWDNWdWNzOHp1dmttaWJheXpBWTJBcTRZYUR3azB0YVROSkZ4bk4iLCJtYWMiOiI0OTFiZDI5YWZlMTc4YTVkMWY5ZjMyODNiNTk1OGFlODYyYzY0ZTViMDg1MGQ2OTczMmQwYjAwZDc5NDU5M2Q2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1543566357\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-525698028 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/20</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-525698028\", {\"maxDepth\":0})</script>\n"}}