{"__meta": {"id": "Xc06164656c56c6a9f9caa88158dcf46f", "datetime": "2025-06-27 02:27:24", "utime": **********.707864, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.291117, "end": **********.707879, "duration": 0.416762113571167, "duration_str": "417ms", "measures": [{"label": "Booting", "start": **********.291117, "relative_start": 0, "end": **********.654139, "relative_end": **********.654139, "duration": 0.3630220890045166, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.654146, "relative_start": 0.36302900314331055, "end": **********.70788, "relative_end": 9.5367431640625e-07, "duration": 0.05373406410217285, "duration_str": "53.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45735016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00245, "accumulated_duration_str": "2.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.684484, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.061}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.6940339, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.061, "width_percent": 15.51}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6992762, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.571, "width_percent": 11.429}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-520789451 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-520789451\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-113839997 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-113839997\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-118556667 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-118556667\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1507249261 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991242209%7C34%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxIZHNYNDZxaVN1VnNTcE9HQ2YrVHc9PSIsInZhbHVlIjoiZERLL1p4TTdMQUZ5ZDVDbWlYZWZaaEtDS0ZXUStFR0dJS1VXMVBrRXpTMlB2ZGxRRTZNSHNKQkdYS2w4VWJCaXhzV3VhZmJTdUZOMTc5VnJjWUN3WVFwK08rSVB5dEttRHhpUGNhMWVBaDRBbi9qd2VPR2hDeXZQR0RrRCtjVXB0Y1JHS0Q1eFNoWTJBRWg5WGRuWGxYYklzMUl5anhhZVJiNTlheG9La01tZ1Y4azJseE96Qm0yTVRYRnlZck44VGQ3YndhQThyZkJDaUJJSXY3UUlZQVphYjBtMVYrNlNNa2xwMTdYeUFkMVhFd1VtQ0QvdWRORmZOa2l3MzFKOGZjRlA4WmNRa2g3N1E4VTA3bis3c05DUEF2STM1V01XUXNkV1lrR0J3bGh0MGlGMTVwZHBhNmJ0REVqRkVMdllFckRkS0lJZGJUdldtbUZFTCtGR0JoWXNzRUQ4bHpkbUhwL2YwdmNDcXNKRlk1T0d6OWNvbTNsa2U2OVZDbmZCd3d1Q09OQllvazE0MUQ5aHNlMGQ3UHpOS3dnWnl5NjhNeVcxeldhdnN3Y0c3WEJRNG56aE9xOHJ4TmZVTDRYeGJRdURFalZsNDduNFNDV1JyNzFOUWh4bDJXRU9mWEwvTksrUHowK2xYVDVIcDhaQ095Q3hhN1JIQlpqeU54L00iLCJtYWMiOiI4ZmIyZjMzYzI5NTczY2JkNmFhOTZjYzkzMjA1YzllOTQ4Yzg1MjEyNjliMzk5ODJlNTI4NDU5NzBiYWM5MjdkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVHWXZrM0NUS1VBYXFGcUM4VHp3b0E9PSIsInZhbHVlIjoiSEFrcldzZTN1NmkwT3lzMm14QWJWRFB4MVhjZjVxNmlQM0gzdnJhK2dTb01Bc0xKTjFXS2xDTG16NlZYcWNXVGVkcUZIZnd5Z2pQYXVLRkZpWENxMGtZMWdta0ZsWTd0OVVkVmYvNEVJQ3AzME10dnIzMlk4UWdiOVlvVmZGOHU5MmJCVVZtMURpMTdBRjhxYmVsc2g5dnJOZVZvYXNBOTRWbmRhNU1FU3k2dGpZMTRPK3Z1ZkhqblBkaUl2ZXlFRnJXdGp5U2xyTFpOeWdmYVRpWmlXcmpoUjl1TzdtVGVqbGJMMnUxWG9aOHo1aEtSZVgvdXBQKzh6SzRDbmhlbEJwUjhoTjdDVGhDWE8raURQanRqMGpOY2hwbnY5R1k4WDNwSEhmQ1VvOXhvOEdJdUpPQ0Z3MU1BYk5VUUpwMzJVdi9INjVLMWM0ZW9HaDJCeXJtVTBCVUorNHB0SnpDbnU2SFdaaWQ0VDVGUWNKS3BHdkVieTI1cGNEcm1pT0FJVXM5aEJZTEt2MVMyaXBxTjVXSTgvTXhtL0x3UU4za1VQdXJBWlRmNmlMOHR2L3I3dCt6QjRoNzQvdjdmVWtCUWhsWnNBVFRPRDJZS1dGbmsvK3BqYnZ6Sjd2WXFMN25yRTZwSVhQemVUSUZ6RzB6OWdDU214Uk00RTAwQWljeFIiLCJtYWMiOiJlZDk2YTQ1ZjVhMWUzNTY2ZGFmMDRlNDFkN2JjNmRhNzVkY2Y2MzBmNDI2ZDFhYjE0MDg3YzM0MzY4N2RjMWEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1507249261\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-387118838 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-387118838\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-589506548 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InphdEFjTTNzVmczL0NjM1IxNGt3RkE9PSIsInZhbHVlIjoicXZmNGRvYjVyc3U3aUZPQko1ekd1VERQTWdwOG9ESmVsQ2ttdDlJWWgyWUYzaWNuWS9lTE90MUFFckRoaHpIRGYwajZnNHdWTGpyVGNXdEwwZGVpa2ppaThONUxsNFhHQ3RHWE1SM0oxaFlZU2lpcHdaWm1tQy9IY0VTelA5UG4yZUEwU1kzSUJYeFlnR2hFUTN6dlZjWG9DbmFPa3NyVHFoZEhGYVgraTBva05XTUZuWDNwRk0vOU8yK2RjSkVRY2pmSTB1SzkvR2hJbnFnTlBKcGM1bXNpZDY2L09GODdjZnN5dC9VZTdkVFQwVXlSNXhPVjBQQ3ltdXJ2YU9BT1kvVkMrZmd3bzFQc3A2Sm8vRW5VQnlNd2xWZnZRTTZaNGlZNWVITzhXbmZITWhJQlFVVlV5UENoU2pQd3lxblJzQUxXN1JpQ3laWjB1cWkrbjlJS3pCWmdIbzBNbERzY2h1MmlYWGYvNWNTVVk5bEowb2Y5RkFGR1QvS2YrRGZXcVNCM2NhcHlkMTNEanZrd3YvdmllcDV2SDQ0b3cyTzdsMGZhYXVxajA5Sjd3ZzZjUHBIQTlIOERreVNHbHlyMkozejIvMHZOS1VRYmtodzlPeC9WZlBWK3VZZHlaeHkycEVBN01LR0YwalN1YkdscVphQm9QL0hMeVl0MXFYWjgiLCJtYWMiOiIyNTdiMGMwNGQzOGRkMjk2ZGI4YWU2ZTRkNGU4OGZhNGFjNzgyYTM5MzExYzJhZGI3NTc5NGU2N2Q4NzM2MjBmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlNEcVdsWmtweC96b2pETUVmbzRPbnc9PSIsInZhbHVlIjoiUzhkUjRTMWpFV1RNd0lmeU1hVm9WbEhLMFJUb2M1MGlyb1VMa3llcUVFZXpVMTJESUloK1pnVDZVaXNSUitNdnpxK2JTbzEvVkttRmZiUDd2YUFRQWUrRGVMOFl0YVd6NEdVMTJHSDlUVTVUVDRFSFN0T2M0UlVIS0hVdkVSWFo5SFBiQ2tLTGdRUFU4KzNuN2tobE9LUjI4NDFhOC9yV2tZbXdjRndweTdnUUhpR2dZVVY5ZDlwMUtucVdHSjJWSGk2NzVMRDQydy9oL3RKajEyV0JTMkxvSTRUVE1EL3ZoNjdieDArOTlOdWlkNWFHODVIVnJydm1kNmdzeFhFRFBZMDJUYjZlcGs4N01vL25Od2dsMDk4S2Mrb2tsOXI1aFBnajBpSjJmNzZiZUE4QnAzQ3hWZlRCaW9WMzhlK0RWbHBSV2dIc3NQcXUrV0dtS1JXYjM5cjhJeGJpTXJLNks4WUFKcEplYWhvaTNFbUlhMFlFVnpVL2lSVVkyNytqVVA4RTNqbVY3a1dlTWk0ZXc1bWprN2VMa05icnhlUVM4VDNZTk5LQmZ2bC9uWWpLMXR0eHIyMEFCYWl6NzVpSFNOL3hlK3NzcjllOGhTYVRRWG9neGh3TnZOZjVzWWNyd2N0ZW1HYjk1S2l2T1F1b1luN2llWVRyWmJPbXpGZmQiLCJtYWMiOiIwMWJlMGNjODUzNjkyMWI4MzAzNGI4NzU2YmZlZWFlMzgxMWM1NzY4ZjVjMjY1OThkMGM5MzFhOTZhNzQ0ZjMyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InphdEFjTTNzVmczL0NjM1IxNGt3RkE9PSIsInZhbHVlIjoicXZmNGRvYjVyc3U3aUZPQko1ekd1VERQTWdwOG9ESmVsQ2ttdDlJWWgyWUYzaWNuWS9lTE90MUFFckRoaHpIRGYwajZnNHdWTGpyVGNXdEwwZGVpa2ppaThONUxsNFhHQ3RHWE1SM0oxaFlZU2lpcHdaWm1tQy9IY0VTelA5UG4yZUEwU1kzSUJYeFlnR2hFUTN6dlZjWG9DbmFPa3NyVHFoZEhGYVgraTBva05XTUZuWDNwRk0vOU8yK2RjSkVRY2pmSTB1SzkvR2hJbnFnTlBKcGM1bXNpZDY2L09GODdjZnN5dC9VZTdkVFQwVXlSNXhPVjBQQ3ltdXJ2YU9BT1kvVkMrZmd3bzFQc3A2Sm8vRW5VQnlNd2xWZnZRTTZaNGlZNWVITzhXbmZITWhJQlFVVlV5UENoU2pQd3lxblJzQUxXN1JpQ3laWjB1cWkrbjlJS3pCWmdIbzBNbERzY2h1MmlYWGYvNWNTVVk5bEowb2Y5RkFGR1QvS2YrRGZXcVNCM2NhcHlkMTNEanZrd3YvdmllcDV2SDQ0b3cyTzdsMGZhYXVxajA5Sjd3ZzZjUHBIQTlIOERreVNHbHlyMkozejIvMHZOS1VRYmtodzlPeC9WZlBWK3VZZHlaeHkycEVBN01LR0YwalN1YkdscVphQm9QL0hMeVl0MXFYWjgiLCJtYWMiOiIyNTdiMGMwNGQzOGRkMjk2ZGI4YWU2ZTRkNGU4OGZhNGFjNzgyYTM5MzExYzJhZGI3NTc5NGU2N2Q4NzM2MjBmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlNEcVdsWmtweC96b2pETUVmbzRPbnc9PSIsInZhbHVlIjoiUzhkUjRTMWpFV1RNd0lmeU1hVm9WbEhLMFJUb2M1MGlyb1VMa3llcUVFZXpVMTJESUloK1pnVDZVaXNSUitNdnpxK2JTbzEvVkttRmZiUDd2YUFRQWUrRGVMOFl0YVd6NEdVMTJHSDlUVTVUVDRFSFN0T2M0UlVIS0hVdkVSWFo5SFBiQ2tLTGdRUFU4KzNuN2tobE9LUjI4NDFhOC9yV2tZbXdjRndweTdnUUhpR2dZVVY5ZDlwMUtucVdHSjJWSGk2NzVMRDQydy9oL3RKajEyV0JTMkxvSTRUVE1EL3ZoNjdieDArOTlOdWlkNWFHODVIVnJydm1kNmdzeFhFRFBZMDJUYjZlcGs4N01vL25Od2dsMDk4S2Mrb2tsOXI1aFBnajBpSjJmNzZiZUE4QnAzQ3hWZlRCaW9WMzhlK0RWbHBSV2dIc3NQcXUrV0dtS1JXYjM5cjhJeGJpTXJLNks4WUFKcEplYWhvaTNFbUlhMFlFVnpVL2lSVVkyNytqVVA4RTNqbVY3a1dlTWk0ZXc1bWprN2VMa05icnhlUVM4VDNZTk5LQmZ2bC9uWWpLMXR0eHIyMEFCYWl6NzVpSFNOL3hlK3NzcjllOGhTYVRRWG9neGh3TnZOZjVzWWNyd2N0ZW1HYjk1S2l2T1F1b1luN2llWVRyWmJPbXpGZmQiLCJtYWMiOiIwMWJlMGNjODUzNjkyMWI4MzAzNGI4NzU2YmZlZWFlMzgxMWM1NzY4ZjVjMjY1OThkMGM5MzFhOTZhNzQ0ZjMyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-589506548\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-559260596 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-559260596\", {\"maxDepth\":0})</script>\n"}}