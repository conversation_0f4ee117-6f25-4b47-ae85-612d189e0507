{"__meta": {"id": "X9c23d749567bb64c48fd9880fa9c6f83", "datetime": "2025-06-27 02:34:21", "utime": 1750991661.00758, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.587275, "end": 1750991661.007594, "duration": 0.4203190803527832, "duration_str": "420ms", "measures": [{"label": "Booting", "start": **********.587275, "relative_start": 0, "end": **********.93242, "relative_end": **********.93242, "duration": 0.34514498710632324, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.932429, "relative_start": 0.3451540470123291, "end": 1750991661.007596, "relative_end": 1.9073486328125e-06, "duration": 0.07516694068908691, "duration_str": "75.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02696, "accumulated_duration_str": "26.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9576318, "duration": 0.02624, "duration_str": "26.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.329}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.993003, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.329, "width_percent": 1.447}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.998891, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.776, "width_percent": 1.224}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1948186221 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1948186221\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-592710508 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-592710508\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-428554376 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428554376\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991646682%7C41%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJ5UzNvY0hERkd6dEt1a3paWjJQcVE9PSIsInZhbHVlIjoiVURkSmZDL0did1BkSkltdGZjNzRxTWs1dmNPZzNkRkhsdVJUaEVuN3RHQlMwdEdaMU5zNmV4Z1JvMmNMR2d1NzN0bnMyMjNwK21yMDZTK3dOY240bXpuaDdKZDVrUVVFRityTlpHaTFFRW1lRjhGOTRmMWxsNUVFRENtc0xmUGtsZjAySnN4THAvMGR4cjRodzJnTkEvOG9sUG1qdDUvbmdqU3R5eTMwUGFoZmpaQU9FR0Q3bHMzYVBIckc3WGpGazNCa281a3YwYkhBeHpnYzh0d1hZcTBJNUcraTIxK1NtUTJMSGZTSlZFYytsRUpLV1dtMjNmcDBjMG84czVrRGNmNUFGdC80OFQwVVBGUjJGV0Q4TVVqb25QKzljNTI4RXBMQjEyRit4U2UrWHJScXBkWGVpd2kwRE82clg0L3lqdjh6Qm12WVBhZGhyV01KQWlicWdzWGxnUmsySVFxTmJiMzBGZVBnOTJqV2JiTjl3UWRkR1ZVNHAvUVkybjdESU1qalByenVyN2Y4eHNJYzhRelBhRjJKaGNtbURsQng4WERDRHBEeWlOTkczVUtGNEIvMTVFbHBNMkhBM3dwZFFvQ0tCZXRBVHZLNHN0TmdzZzRmN1FmR0dyMGEzOGRTMys1ZmJyUjltZnNiYitkT1NUQzRXRGhoVmFMRFhrNTIiLCJtYWMiOiI3OWIwZjBiMTUzOTllNTJmN2U4OTllMjgzNThhMGUyMjFkZTJiMzE5YzY0Njk0YzNhYzkwYTMwNzc2MTkxNGZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjEvb1ViallGL1d3R2VrVytOUTNGQ2c9PSIsInZhbHVlIjoiY1haOFVMdVN5UHRGVGZlTTI5SGJROWJMYXJVOElpNEpCUkt5aGx0VGd6WHBoOURmSWZoMnhKcXFHY21TZkNSZ2JSM3hvSHdxd0NGNzVBZnQ1Q3NsV3VhRFljQk9CbldGeFdzYlVPUlh5NE1maEdQWmx4d3NDY3R1N0M3QjFqQ2JlQXVzZXFuVnpCMkNTSS9JZGtkKzN2RzhxSXBkQWtWd3ZPSHlkMEpNTytwN3lZZGdBMHVjQ2FZVEJQbEhNODY3MlRmdHhLUkl6TFlObGxHbUJEaXpORVp5dG1tVHdEQnpUSEc0M3J5NHdOelArSUlmT3lRZUphWUlWeE9BSXo1Unk2SEFxQndkbDV1dlR0SFVYdVo1TkZGdE1QZzBYRFFzeEF2YzIrT2xnK2F1cTFuVVVGaVN6YnBlOC9XZHZISC9hYWhYa3lhcnNIVmVISlZEeUUrZFdqVk5YcGRTK3FGRlFka1A2dEwrYWlUMlo1UmhoNno2TDNWRDlzelYrTFN0WVNqc3BIMkJwNjR4RGJpTnRIZDUyYzlWNG1rdlRzNVNRQ2ViV3ZwcTNUN0F3Y2NPZWR4U3Y0T2VUWklyMlJIaVhtdnF4c1F6V2YyaVg4dm1GWUgxazJ5L3hXMDZOa2NqZkVUcFFnbndtMG45SUxCL2VxN2grR29wV2pMY2w0WFEiLCJtYWMiOiIzNTUyYjMyYmE4NWZkOTY5NDViNjhlYWFhZGE5YmY2MjE2ZTg4OWQxNmMzYTMyNjJjYzE5ZWZlMzRjYjA5NWYzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1948882805 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1948882805\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImYwUXo0NUpiN1ByaW1LTm5BUy9Wb2c9PSIsInZhbHVlIjoibmhlSkdQdmxlR1g4dGFrRHoweUVWV3NhVDVoNkl6VmljYVY5eXpuZHg4d3RlSVVvUExmeUpNYnJZOGN3aE04ejgvVVQyWXRsdmM5clZHQnVBcHh2ZU1vTXh3Umo3WHdzcC9pMUtmckdveWRtMmo2Mm1pRk1TTkt6VHpBYUdsK3lWNmFXcEhTRlJtdmxDaGpDOG4wN1ZtUTRWeDdPTmZXYzdZZlBaTWRxRXYxSjRhRVNEWk1nVEFPMEVSNlc4SmFXbys4K1RiSWhQRGxtTk1qUHFwTXdueWJlZkFZUldHS1AyRmkwZm5FY05TQ3FYQW9LRUx1ZmJQSFl3OEdPVko5VEcyTHNMaDZtZnlqd2hJSVZjbWZOU2doV1JVMzN4L3BHTlVqMkJDOEVET0VhU3J3emFXaEc5UVVOY2NFQjIzLzQ3YjQ3WnZXMG5mdEVBRU8za0pueFJNYStCeGx2THVPNU1paXFkV3pleGF0Q3IyRHZQb2F0UEFNbmlrcEE3R3Rvd0FHRytrN3Y5MHU4S2MweDhRaTRPTGxMaFV6SHJZQVB4QWFzd004REtvaVpHbTU3ZGNiWGVCcTk2NUNLNGRiM2hDclVRbVYzV3hpRTRoM2VFUWJXTmFDQ1p2Wm1kcFkzQ0tGUlJFUDN2M1ZoQ1VmMHdWVWhKWjIrUUYvYXFMSFkiLCJtYWMiOiJmYTZmZGMxMTRmY2E1ZTc0NjQ0MWNkYzRlYWMzZTBkZjE1NzY4ODAzNmZjM2I5MWNmYjY5OGM2MzA1NDZjYjRjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkRLYkVzRjd5eXZwMzBlby9YUDlTRmc9PSIsInZhbHVlIjoicElOYkg1V3Z6aEwxcXdBM3ZUamdtQ3hoMndQSlB1cGtQT091NjQ1Ym4wNjZSUm9CZEhYWXdHbHRkUmlmd254ZGRoTDU1NDRoOXh5Z3g2bnBGUDFyRnZvcTJzMEVFelBuck1zam5oMDFNS3VySGpLdnd4UStCQnZsODkvVzB0SXpkb0t0MVpmLzVWUWlUSGZPWEFXeUo0QjRPZCt5elZJRkttVGxpYVFhQW9vQWhsRUFkS2cvazI1R3NZb3dtZnlpNFdrbnAvYkRORzZVSVZhRit4S2tjcGRpbEFWUDAvM3FVUnZ6cFVVMmpDdUZ3VkpTVUdXRHlrOTVzc3JsaE9CTFNBeXAybVY1VVUrSFNvTlYyenBndDJ6cElXNTlsWnpYZU44ZDczSVk2OS95S2puNUZkbERIdzE0UURuS1R3NWg5MU5ZNFRqY0pXYUY4MVR1a0FPQ2dQbmN1SkhFRjRESjhBUzdBTnlSZ3N0OWFJYTljRDZuZkxrY1Q0N0ZDdEVsNC9qMVRxT3U2MUUyZlpWaTVwemlBRXZ4cnJNektCSHNCNUkwOVQzM0ZOcW5KWk1penVvZi8rdkJqWjZoSFdVcGhOcjZ4V1dpa0ZjTFBsQ2tob2NMMEgzV1pSZUI1cTFDNlZreEhzSmVSVmg4MUJES3hjOHBPays3OTZqd1BHenQiLCJtYWMiOiJmNTdmMjY4M2QzOGJmMTU2ZTExYTg4MWI5NmNlMzg1ZjA2YWM1NGJkYTc3NDk1Y2Q2ZGRkODMxNDFlYmFlYmVjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImYwUXo0NUpiN1ByaW1LTm5BUy9Wb2c9PSIsInZhbHVlIjoibmhlSkdQdmxlR1g4dGFrRHoweUVWV3NhVDVoNkl6VmljYVY5eXpuZHg4d3RlSVVvUExmeUpNYnJZOGN3aE04ejgvVVQyWXRsdmM5clZHQnVBcHh2ZU1vTXh3Umo3WHdzcC9pMUtmckdveWRtMmo2Mm1pRk1TTkt6VHpBYUdsK3lWNmFXcEhTRlJtdmxDaGpDOG4wN1ZtUTRWeDdPTmZXYzdZZlBaTWRxRXYxSjRhRVNEWk1nVEFPMEVSNlc4SmFXbys4K1RiSWhQRGxtTk1qUHFwTXdueWJlZkFZUldHS1AyRmkwZm5FY05TQ3FYQW9LRUx1ZmJQSFl3OEdPVko5VEcyTHNMaDZtZnlqd2hJSVZjbWZOU2doV1JVMzN4L3BHTlVqMkJDOEVET0VhU3J3emFXaEc5UVVOY2NFQjIzLzQ3YjQ3WnZXMG5mdEVBRU8za0pueFJNYStCeGx2THVPNU1paXFkV3pleGF0Q3IyRHZQb2F0UEFNbmlrcEE3R3Rvd0FHRytrN3Y5MHU4S2MweDhRaTRPTGxMaFV6SHJZQVB4QWFzd004REtvaVpHbTU3ZGNiWGVCcTk2NUNLNGRiM2hDclVRbVYzV3hpRTRoM2VFUWJXTmFDQ1p2Wm1kcFkzQ0tGUlJFUDN2M1ZoQ1VmMHdWVWhKWjIrUUYvYXFMSFkiLCJtYWMiOiJmYTZmZGMxMTRmY2E1ZTc0NjQ0MWNkYzRlYWMzZTBkZjE1NzY4ODAzNmZjM2I5MWNmYjY5OGM2MzA1NDZjYjRjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkRLYkVzRjd5eXZwMzBlby9YUDlTRmc9PSIsInZhbHVlIjoicElOYkg1V3Z6aEwxcXdBM3ZUamdtQ3hoMndQSlB1cGtQT091NjQ1Ym4wNjZSUm9CZEhYWXdHbHRkUmlmd254ZGRoTDU1NDRoOXh5Z3g2bnBGUDFyRnZvcTJzMEVFelBuck1zam5oMDFNS3VySGpLdnd4UStCQnZsODkvVzB0SXpkb0t0MVpmLzVWUWlUSGZPWEFXeUo0QjRPZCt5elZJRkttVGxpYVFhQW9vQWhsRUFkS2cvazI1R3NZb3dtZnlpNFdrbnAvYkRORzZVSVZhRit4S2tjcGRpbEFWUDAvM3FVUnZ6cFVVMmpDdUZ3VkpTVUdXRHlrOTVzc3JsaE9CTFNBeXAybVY1VVUrSFNvTlYyenBndDJ6cElXNTlsWnpYZU44ZDczSVk2OS95S2puNUZkbERIdzE0UURuS1R3NWg5MU5ZNFRqY0pXYUY4MVR1a0FPQ2dQbmN1SkhFRjRESjhBUzdBTnlSZ3N0OWFJYTljRDZuZkxrY1Q0N0ZDdEVsNC9qMVRxT3U2MUUyZlpWaTVwemlBRXZ4cnJNektCSHNCNUkwOVQzM0ZOcW5KWk1penVvZi8rdkJqWjZoSFdVcGhOcjZ4V1dpa0ZjTFBsQ2tob2NMMEgzV1pSZUI1cTFDNlZreEhzSmVSVmg4MUJES3hjOHBPays3OTZqd1BHenQiLCJtYWMiOiJmNTdmMjY4M2QzOGJmMTU2ZTExYTg4MWI5NmNlMzg1ZjA2YWM1NGJkYTc3NDk1Y2Q2ZGRkODMxNDFlYmFlYmVjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}