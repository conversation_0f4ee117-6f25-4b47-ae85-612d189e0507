{"__meta": {"id": "Xb17b6ec79919cecf83f6faaebd1266ee", "datetime": "2025-06-27 02:27:04", "utime": **********.205955, "method": "GET", "uri": "/add-to-cart/2299/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991223.743479, "end": **********.20597, "duration": 0.4624910354614258, "duration_str": "462ms", "measures": [{"label": "Booting", "start": 1750991223.743479, "relative_start": 0, "end": **********.120026, "relative_end": **********.120026, "duration": 0.37654709815979004, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.120038, "relative_start": 0.3765590190887451, "end": **********.205972, "relative_end": 1.9073486328125e-06, "duration": 0.08593392372131348, "duration_str": "85.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48701192, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.006500000000000001, "accumulated_duration_str": "6.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.156646, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.385}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.166824, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.385, "width_percent": 7.692}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.1825972, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 33.077, "width_percent": 12.462}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.18499, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 45.538, "width_percent": 5.538}, {"sql": "select * from `product_services` where `product_services`.`id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.189697, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 51.077, "width_percent": 6.769}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2299 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2299", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.193593, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 57.846, "width_percent": 37.385}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.197357, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 95.231, "width_percent": 4.769}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1203856807 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1203856807\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.188761, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2303 => array:9 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"id\" => \"2303\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2300 => array:8 [\n    \"name\" => \"جالكسي كيك البندق 27جرام\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"id\" => \"2300\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2299 => array:8 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 1\n    \"price\" => \"2.99\"\n    \"tax\" => 0\n    \"subtotal\" => 2.99\n    \"id\" => \"2299\"\n    \"originalquantity\" => 12\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2299/pos", "status_code": "<pre class=sf-dump id=sf-dump-2094423411 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2094423411\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1205145203 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1205145203\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1246620664 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1246620664\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991174265%7C33%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtUSUhBZkJvdWs5dDJ5cnRNdGkrbVE9PSIsInZhbHVlIjoiQlMrdUdGcEovMzFQN0JwTS9hNWpwcmpUdXNyRHdJMytSVFJUMlUxaDlKZUYwSlM0M2ZBN1FtWkpmNFV0SmpVbi9HSHhvMkdXOERqSFNsNmZhTE1PN1MxSTFpaktLUzRnTzdrZENydlNBdExDZTlYN2Y1WlRoU0FzazYybU5rWGJhMW9mdElST1lHcCtCYko3NnU2NE9ONFV4SEhWYjNFY08rVFFXdGJmQW9mU2ZZV2Z0aWpvdjUrWWhJbG1MYXFmTkY0OW5DbkVjRk9pUGlNOHdNR09NN0R4eU1OdjhhLzlDcTdNdkIyN1FIakhOVzRQSUZWVXJYQkdqMHpHbkluakdNSmUvSzdxdnc4SzRIVHV5eXd0dm5YTHltRHJxZ0tjbjZnOVVsU2FqdFl0eG82TWEyKzQ0aW50bWFWZGJqRFpIanlZc3N4b0V2RFFwQ1BOWXRKSGc5K0pENmJtWFE4VjZqVi9qL1ZIMWJFK1BWM2puT1FsMFhJcnpQK0lpeThXY2ZVcnRKeGdnT1UvVmZWc3Y5d1crcDlvMEVpN0JwWmRWdVVjN3QzK1ZWWTdCdzhnRXpFRlZlb29JQmNPa1YwYWhkdFRFQ0NzTzg0YUVlcVdaQyt4eGRTR1pJL1N6ejFIeVZmNmZBM291STVibGhLVGJ5bm8wbHRha0pTbkQ2VDgiLCJtYWMiOiJmNTk4MmJmYzU5M2RkZDM4ZWJkZjRmMjAzYTk5ZjI1N2Q3ZDNkOWQ3MTYxZjM1OTRlYTBlODNhNDFlMDA0MDczIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtraUpNWEdjNU5KelVPRVNwUFIrWHc9PSIsInZhbHVlIjoiT3A4K0pQZkNQZ0Jrd1grYkVrdUNmOFdQbHI2TXoyM2xaTWU1UzJRTzM1V05UbEl1SkQzUEQ0eS9Rcm1LWTZmcVVpQmxFM3hsbmZOZEhieFpvUGJGWEtTZ1NvZHR0UlhqTm84WFRxSy93a2F1SEJoY2RrU0dSQXhhZm5sMFFObDRlRGV3VVkrZGoyMGFMd3QvT1AxZzEzMG5SNDY2UEs3a0J0bzd5VW45VUtGVTUwUWlIZDBycFZTd2F5VThCeC94WDlHOWlFL2Qyd3pDbHVmR0pVcVNCZDJVdzJEdWtnMnNYSG5vWTJMeHZWMlFBanpaTnpxRm8vU3VjN0Y5a25tYTV3cHVWeEIwOUJVay9tTENDV2lIcnFuT3hzL3lwcEZtcEN2U2hEbmswNHZNTjhmZ3VQUE5DYk1SMGNhcjlGV2tQK2tFMHNaQWs5M2ZhenJKMWZ4c2NJOExwSXZ1V2tvOGkvZzhsdVNnaDkvS2Y1V0NaSVRTSTJtZllvTGNpQUpNS3h5MkVzK3I2MGZCanBoRUFMUS9LUkFrT3Y1b0x3ZGJlZGVlNTJ6WkM5ZjhsWVZWVmpaZEtpZVRPbnVvYm1TZW5vQkc4NU1IZ2h4Vzd3UDlYc0cxWVZhcDUyVkZrM1d0USsxWUFUeXp3SVZ4R01kemcyZ3Q4dkd4Q3dHSzVmeUMiLCJtYWMiOiJmOGVhYmRmMTdiNDVlODY5MTI5NzdkNzVlMDAyMzM1Y2NiZjNlMGIyYWE0OTA4OWE0OWQ0N2VmOWVhODI3OGJhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1145857679 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145857679\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1099588903 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:04 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJRQXFubFNIbDBrc0xKR3J6NDVoN2c9PSIsInZhbHVlIjoiVFRnbWR3ZXlhQWdOWlZhMU1QTWRNYmRDYjIvemgydTJWTmQrbUYrelNramhPdDRjMW81VGgzbHFoWi96d1hxTmJKZlpRREIrN2lEZHNXTlEwZjYxcldnOTFYcUdaMUZEdmpYSHh3Zngvb1oxL2xGY1lUKzR3QjBPa0tEa1dCcWNPTXJKYnJOM0phNzFQTFFJNzhjNlhPdklyWVcwT3NxOXpxWW1HdDMvZk5RbkFjWm5WR3Fsc0pRbnpmNm5vVE52dG96cnlzdFV3anB0dnpXTVNGSDcxdmVzTEtrQ3JJMmczMDd0Vy9wUEZjQjlrTXArVkdjOUFhSm5NdUhhUG9XbjFyV0ZYWmM0WUJaUkZTVXd2M1VlTEZqbmdKSzJpMTN3RGl2aFdtNVpSbWtmSkpxb0UwM3Yyb1lPNStoeWExMUlxN3IvMlo2YmRIeGl0eERrK2JycCtZRStXbDFMY0RGVGpEWitId21heDNHZURHbXpqTkcwcUtOQnRxbjhMZUtJSjJSalllUnF6MkxrWkNqYWRveGk0RzE0aFR5ZDBMZFNNNDZqS1gzVzZGMGdDb2NMNytHSWJuZG55V202ZG1YeHFFTEtCaGRDSzRST2xuUG9hUkhrSVBYTWJXYnIyeEVUUDJEWTBaYUJtRFlURks5dXU0eC9WUVJnbmJtelVCbVMiLCJtYWMiOiI5MmVlZWQ4NmU3YzI1ZGNmMjZkZTllNjZjNGI4YjVkZjk3NDEyZjEwYTM1MjMwYzU5OTNhNGFkMWZiOGNjZjlkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:04 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVGTnlWMjRrM3lJL1kvYktLWmIzd2c9PSIsInZhbHVlIjoiOGZIb2VZSHQ5eUhzK1htRmlTRzRPS2p1MVJsUHJhd1ljOG01MnlQUEtZQjc2ZUNDRDZrZlBHamJaY21KcWlYbVhiOEE0b045MnVUZFZLUGRwU2M4eDZwQWVKR1Q0YXpFei9mS1p0QmNYVmtMTHp2Qk5TMTd0U1l1eDZKUGs0OWZxUFN3bTdRbjlhN0FzOFNVMGtxbDU3UDFVT3c2NExlUi9jL3JNcDRhZnR6VWRIV2ZqcDBteUt2bXEvcDNLSEtYaE4zYVNJU2dTTEtxeGYyblZwaGdLS0oxS3o5MGxOaGgvUlV2alE1d0xlZWhiVGE5K3NRUkF2aHFoZnJYUHc1SzMvbDJodVJDcjlNY2pEWjBHWlA4TC9OdGU1UWlCVmRVNHM4aVpxWTBncjdENUs3YXAzV1l0RTc4ajN0RTlwL0ZtTWIwMHRqZTROVGtNbHZ4OHZYRDJ3YjNPYkQrK2t4L1RVejdFZDdNZEZNaWlSMXR4eGhTdkdLc0lnd3NZVXdQd2FPbmFMdmw3UVZ2Um5yenh5TXgrVGhvWWtGR1FVSTZDaVRCWnlNVitEYjk0WGF5UVRxNzBiWjBIWHZOUVBmaEovRndGdU4xYmsyMUsyUkY5OFBqNWUzR3AwOWJxalQ0ZXppQ0lmMm94clNVZTA0dE8xSnFSNWVoMmNKVDMzV2IiLCJtYWMiOiI5NTE3OWFmNTFlNjFkNmE5NzlhZDcyMDNkNjY3YjgwOWQyMDdmMDg1YTBkZGYzMjgzYjJhYzJlMTdlZWZmMzQxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:04 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJRQXFubFNIbDBrc0xKR3J6NDVoN2c9PSIsInZhbHVlIjoiVFRnbWR3ZXlhQWdOWlZhMU1QTWRNYmRDYjIvemgydTJWTmQrbUYrelNramhPdDRjMW81VGgzbHFoWi96d1hxTmJKZlpRREIrN2lEZHNXTlEwZjYxcldnOTFYcUdaMUZEdmpYSHh3Zngvb1oxL2xGY1lUKzR3QjBPa0tEa1dCcWNPTXJKYnJOM0phNzFQTFFJNzhjNlhPdklyWVcwT3NxOXpxWW1HdDMvZk5RbkFjWm5WR3Fsc0pRbnpmNm5vVE52dG96cnlzdFV3anB0dnpXTVNGSDcxdmVzTEtrQ3JJMmczMDd0Vy9wUEZjQjlrTXArVkdjOUFhSm5NdUhhUG9XbjFyV0ZYWmM0WUJaUkZTVXd2M1VlTEZqbmdKSzJpMTN3RGl2aFdtNVpSbWtmSkpxb0UwM3Yyb1lPNStoeWExMUlxN3IvMlo2YmRIeGl0eERrK2JycCtZRStXbDFMY0RGVGpEWitId21heDNHZURHbXpqTkcwcUtOQnRxbjhMZUtJSjJSalllUnF6MkxrWkNqYWRveGk0RzE0aFR5ZDBMZFNNNDZqS1gzVzZGMGdDb2NMNytHSWJuZG55V202ZG1YeHFFTEtCaGRDSzRST2xuUG9hUkhrSVBYTWJXYnIyeEVUUDJEWTBaYUJtRFlURks5dXU0eC9WUVJnbmJtelVCbVMiLCJtYWMiOiI5MmVlZWQ4NmU3YzI1ZGNmMjZkZTllNjZjNGI4YjVkZjk3NDEyZjEwYTM1MjMwYzU5OTNhNGFkMWZiOGNjZjlkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:04 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVGTnlWMjRrM3lJL1kvYktLWmIzd2c9PSIsInZhbHVlIjoiOGZIb2VZSHQ5eUhzK1htRmlTRzRPS2p1MVJsUHJhd1ljOG01MnlQUEtZQjc2ZUNDRDZrZlBHamJaY21KcWlYbVhiOEE0b045MnVUZFZLUGRwU2M4eDZwQWVKR1Q0YXpFei9mS1p0QmNYVmtMTHp2Qk5TMTd0U1l1eDZKUGs0OWZxUFN3bTdRbjlhN0FzOFNVMGtxbDU3UDFVT3c2NExlUi9jL3JNcDRhZnR6VWRIV2ZqcDBteUt2bXEvcDNLSEtYaE4zYVNJU2dTTEtxeGYyblZwaGdLS0oxS3o5MGxOaGgvUlV2alE1d0xlZWhiVGE5K3NRUkF2aHFoZnJYUHc1SzMvbDJodVJDcjlNY2pEWjBHWlA4TC9OdGU1UWlCVmRVNHM4aVpxWTBncjdENUs3YXAzV1l0RTc4ajN0RTlwL0ZtTWIwMHRqZTROVGtNbHZ4OHZYRDJ3YjNPYkQrK2t4L1RVejdFZDdNZEZNaWlSMXR4eGhTdkdLc0lnd3NZVXdQd2FPbmFMdmw3UVZ2Um5yenh5TXgrVGhvWWtGR1FVSTZDaVRCWnlNVitEYjk0WGF5UVRxNzBiWjBIWHZOUVBmaEovRndGdU4xYmsyMUsyUkY5OFBqNWUzR3AwOWJxalQ0ZXppQ0lmMm94clNVZTA0dE8xSnFSNWVoMmNKVDMzV2IiLCJtYWMiOiI5NTE3OWFmNTFlNjFkNmE5NzlhZDcyMDNkNjY3YjgwOWQyMDdmMDg1YTBkZGYzMjgzYjJhYzJlMTdlZWZmMzQxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:04 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1099588903\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2300</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1576;&#1606;&#1583;&#1602; 27&#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>2.99</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>12</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}