{"__meta": {"id": "Xcf08ccdd124fdaa664e59244f63acf36", "datetime": "2025-06-27 02:23:36", "utime": **********.246757, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991015.783944, "end": **********.246772, "duration": 0.4628281593322754, "duration_str": "463ms", "measures": [{"label": "Booting", "start": 1750991015.783944, "relative_start": 0, "end": **********.177034, "relative_end": **********.177034, "duration": 0.39309000968933105, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.177045, "relative_start": 0.39310121536254883, "end": **********.246774, "relative_end": 1.9073486328125e-06, "duration": 0.06972885131835938, "duration_str": "69.73ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722184, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01389, "accumulated_duration_str": "13.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.20618, "duration": 0.01262, "duration_str": "12.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.857}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.228986, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.857, "width_percent": 4.464}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.235816, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 95.32, "width_percent": 4.68}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1116199015 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1116199015\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-770688223 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-770688223\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-903338120 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903338120\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1726385274 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991013146%7C17%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFVTVF6aFJpaHhBdEQrVWhkK3ZQd3c9PSIsInZhbHVlIjoiZWkyWFM1TWF5elpaYXN1SXZlSUR2cW1LbkdhUGxId1VIR1p1MGVoTlVsRUg5b2JRdnB4TE8zbC9aclpJZjlXRmlVUjdqOHh3UFdPbDBjV2ljTERVTmdJenpOSmdXbjdzRUdmaGpONTEvSmVSY0dEKzhNK2xhaDR6eVgreEk4N3JoS3gvc0xtMjN0WWdBL3pGUFk0dDV6clduNms3bk9ycDB6ZFEvQlA0am9CRjRwYVVlSUJsTGtmUFRnRnpvQW9hR2tIRlFlbjZCeEZydTY2NUNrUm1yblcrbk9zakhCd3ZFZFlESkFSLzFnTlA5NFFhZXlTNTd6SEc0SktyY296cDFZRGdNWWRicjJmYXZNZ3dSbDkxWVpQMmd6THBSTXFJNmNHdEkyczQ1TGwwcnhFdTA0NjVWUW45VTJia1J5ZHVMaklydXB2OW1CVFczL3dOZm0xMDlubjBsa0grMlIraGVYdjNrVG1VK3c3Z2hWNFRJaU50Njd6L1RxdHF4UGhvM3lYaVJ6UnRLeERmMTNBRTVyWmJKUXhzZlVnTmVHcndkbXRkNEI2aE10ZHZqZ0tzemlrUFFiWWg2bk5zbkZYOUJhbTNzUnJLeFAwejdDOW92aGY1bDM5K3puMmNsK05tSEtPbnpUanM2NjRLS05XaFFEeWsxZGEvOHhWaTJqMnIiLCJtYWMiOiIwZmYwOTZkMmU0ZThjYTU5ZWVlNWRlZGZhNzY2NWIwYzlmNjI0MWQ3NGYxZWU4MmFkMDE2NTg2Mzg2YWI3NGJiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBZVEs1dXRLNzlwYytxNjVaWlhHWEE9PSIsInZhbHVlIjoibmpCNVQrTHpWb2c4enBRckRYa0F3Tjd4ZWV5c1JLK1pCMUY5Q0tCSDl1UXV0bmlZeUpaN1FuTGxCbnBXUnF6cmh4SkJqU3VDWmdoYjI4QmlkNkxzeWpDVE5ldzVDOUhlQWQrQldqZGQ1WVY4aXBFeHVDcWVDTE9RV3d4MnNpLzhFMzN1dGN1NzhWNFJsN2x5OVVzNkUxNTEwMTM0RjhxWWcrbEFZVDR4UElSSS9JOUZTRWNKc1p0RjFpRXhQb25tNHFLa09pWExBYUtKd1VsT2U4SHN3QXlUbEQ5eHQvbkt3bEpTcFUvVzNTeVlkOXdWSkNYMjVkekZCR29ZVllPa093NHVNUGg0RzVzM2s5MXZxQjlmQTN2V1hJL2F2c3BzMGpMRTcrdENTK1I2ZS95bXdxZEVvbjNTbmdYTEwraGNhNzFIN1JxMDJad1pxQ004UDlxeUVETmRrWSthQThPZWQ0K1RNaHZOb0drSVZnM3o2Q0gzQjU0QTBmZ0Y3bjBSWkgvU0czWjQ4SmRrc2RCMko1QVBWWVN1VFhrSHplTnJDUk0xYXBMRHB4a2lkQ3UvVysxRzZGa0hUeVpLQnZQMnlPbW5hSHdvZlVndFB6SEYwVEh4c2FXOEovdUR4eXdsMG1JVjJ2QURGTWZkRENhazNrMlVjUGE1cXlwNFcvanUiLCJtYWMiOiI1MGFmNjljOWYzMGU0YjQ2ZjM5ZjY5NmVjNzFjZmYzZWI4NmE3MTQ5Mjk2ZjVjNThiYTBjNDEyZTc5ODMwODdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726385274\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-307311418 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-307311418\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-411438372 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdncHR5SFkwWXQ2cTJWYVFIbDlCNmc9PSIsInZhbHVlIjoiYmthRmhKWE1RZVRhQmtVWDFCRWRpeW1leEJzR1BSdHF1US9JUlNrWVZxT0tmWmFZMWNFK09HcDNEWTdaZlhrTElkVmpZRVpFZzYxNlVPM1Q2dGRkZjRMZU1wUlNOa1BmcXdwOXBldzgxbVIya1pPS2xXMFFiVy9Zd1REZ3R5SHJEbnhNZldaTFBzeTY5Zk5qaThYTmc5MU5JdU40WkxoaFErOUg1RCt0c29PSWQybndWUnh1VzdLNTlRUUdUMUNkYVV1Wk5vUXNzNU4xWTBIMzV6RGlFNGtZajdXQzR1a2NGMmJJM1VmVDkyL0RiYVV3ZnZnbnE0K0htczBpMU9IcnNodk50Ymt4QjR0OExsMENoM20yN1c5MlV5ZkswWUNhcmNhLzl4aFZSayt6d2hYQ1p0dHY0MVVQSzRRSnJ3bnI2Vm5UQWZFL3pJbE9QckQwSllNWWozVnA1ZzR1NE40UDgvSlhkSktNcmdoR2F2cTNFSnpqUE9tanVpQ3NQRnd3dTNMU0RCNjdwRE1PWklVMWR6STk4SFBSa0FoZEVLSmhrdmoyckswcHFQNk9YdldjNkZpOENEejA0TWZlWXJDbUZ6U3RkcmFYTm9vQTg0cUtmV3ZtcXdRWmtzR0RTSHdSb2pEeXJUaGRra2twL01CVDA0UzJYTTYvVXIzMFBwa0QiLCJtYWMiOiJlNGUwZDk2NDE5NzU5MWYwYjJmNmQ1ZjI4YzVhNGY0ZGMyMDU5NzYyN2JkODQ5MGM5ZDhjZjBhNDc4OWFlMjIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZyZzQ5aTVMY2tyeFpDZlhQYlpxenc9PSIsInZhbHVlIjoiYjNWT1lKVlQ1R3VISzE1Q1J2S3BvWmd6RUJrWWJ2MGZxeU1UdmVCamZvUlh3K0MzVWxaUkVwMnJpaUsxeWtlc2FxbVpQN0VOemN6S0JvRldyT1Zib1dLN1lDR09VVm1sczVsYmFYb1RiUzk0aXBKOXRmczVuYjZpK1A4azdaY0d5OGh0SzgwWkRUY3pJR3dYcGpmTU55L1dBamtmWXZOZHl5NTRDWlJhSGE3bTNCbldWam5nYUthWHUvbHBNQnIybEljVEN5UUVjVWtvalNkVytjKzBkT0hJMnMzTUMrbWxsdXdNaE1xSDJORDY5MFBaZUdnK1FXcUVhbjlaQUIxZU14UVllMmlYbVJvL0ZVNUliOFd3UG0rby9iMmZTaHVBWGZadUxGQm5CblJQYVFvMnZtejNFblJScDhnTEViMVk1UVlJaE9aUGRYOEpPcGtjMjFiWThKYTBKVHVzN1NEcS8wa2REQnJKSVEwTUp6cVJyZHduNDV0b1pJdkMrTmpPakI2NlR2dzluSzJPeUFlekNJVGg4NmptTHg3ZmlvZzIrc243YnQ3RDVSUmhkYjFrMzJTYnZ4THFIWFFCTEFWTk1Pd0RSZGZxbWNsNm82ZXZscTAzeWU5S216QmJhdlZ6NU9Kd2huOG1qSmZ2akViYlpsaTZkKytIckVZNjZ2QU4iLCJtYWMiOiIxZjkxODAyOWU3ZjQwNzY0ZDlmZmY5N2M4ZjQxYjNjZWI2NDdkYzY2N2MwYTFiN2E2MGI1NWRmNjA3MDA2ZGU0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdncHR5SFkwWXQ2cTJWYVFIbDlCNmc9PSIsInZhbHVlIjoiYmthRmhKWE1RZVRhQmtVWDFCRWRpeW1leEJzR1BSdHF1US9JUlNrWVZxT0tmWmFZMWNFK09HcDNEWTdaZlhrTElkVmpZRVpFZzYxNlVPM1Q2dGRkZjRMZU1wUlNOa1BmcXdwOXBldzgxbVIya1pPS2xXMFFiVy9Zd1REZ3R5SHJEbnhNZldaTFBzeTY5Zk5qaThYTmc5MU5JdU40WkxoaFErOUg1RCt0c29PSWQybndWUnh1VzdLNTlRUUdUMUNkYVV1Wk5vUXNzNU4xWTBIMzV6RGlFNGtZajdXQzR1a2NGMmJJM1VmVDkyL0RiYVV3ZnZnbnE0K0htczBpMU9IcnNodk50Ymt4QjR0OExsMENoM20yN1c5MlV5ZkswWUNhcmNhLzl4aFZSayt6d2hYQ1p0dHY0MVVQSzRRSnJ3bnI2Vm5UQWZFL3pJbE9QckQwSllNWWozVnA1ZzR1NE40UDgvSlhkSktNcmdoR2F2cTNFSnpqUE9tanVpQ3NQRnd3dTNMU0RCNjdwRE1PWklVMWR6STk4SFBSa0FoZEVLSmhrdmoyckswcHFQNk9YdldjNkZpOENEejA0TWZlWXJDbUZ6U3RkcmFYTm9vQTg0cUtmV3ZtcXdRWmtzR0RTSHdSb2pEeXJUaGRra2twL01CVDA0UzJYTTYvVXIzMFBwa0QiLCJtYWMiOiJlNGUwZDk2NDE5NzU5MWYwYjJmNmQ1ZjI4YzVhNGY0ZGMyMDU5NzYyN2JkODQ5MGM5ZDhjZjBhNDc4OWFlMjIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZyZzQ5aTVMY2tyeFpDZlhQYlpxenc9PSIsInZhbHVlIjoiYjNWT1lKVlQ1R3VISzE1Q1J2S3BvWmd6RUJrWWJ2MGZxeU1UdmVCamZvUlh3K0MzVWxaUkVwMnJpaUsxeWtlc2FxbVpQN0VOemN6S0JvRldyT1Zib1dLN1lDR09VVm1sczVsYmFYb1RiUzk0aXBKOXRmczVuYjZpK1A4azdaY0d5OGh0SzgwWkRUY3pJR3dYcGpmTU55L1dBamtmWXZOZHl5NTRDWlJhSGE3bTNCbldWam5nYUthWHUvbHBNQnIybEljVEN5UUVjVWtvalNkVytjKzBkT0hJMnMzTUMrbWxsdXdNaE1xSDJORDY5MFBaZUdnK1FXcUVhbjlaQUIxZU14UVllMmlYbVJvL0ZVNUliOFd3UG0rby9iMmZTaHVBWGZadUxGQm5CblJQYVFvMnZtejNFblJScDhnTEViMVk1UVlJaE9aUGRYOEpPcGtjMjFiWThKYTBKVHVzN1NEcS8wa2REQnJKSVEwTUp6cVJyZHduNDV0b1pJdkMrTmpPakI2NlR2dzluSzJPeUFlekNJVGg4NmptTHg3ZmlvZzIrc243YnQ3RDVSUmhkYjFrMzJTYnZ4THFIWFFCTEFWTk1Pd0RSZGZxbWNsNm82ZXZscTAzeWU5S216QmJhdlZ6NU9Kd2huOG1qSmZ2akViYlpsaTZkKytIckVZNjZ2QU4iLCJtYWMiOiIxZjkxODAyOWU3ZjQwNzY0ZDlmZmY5N2M4ZjQxYjNjZWI2NDdkYzY2N2MwYTFiN2E2MGI1NWRmNjA3MDA2ZGU0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411438372\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-863739298 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863739298\", {\"maxDepth\":0})</script>\n"}}