{"__meta": {"id": "X443fe3f0a56cd4ea11595747b9f653c1", "datetime": "2025-06-27 00:14:56", "utime": **********.863182, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.364298, "end": **********.863199, "duration": 0.4989008903503418, "duration_str": "499ms", "measures": [{"label": "Booting", "start": **********.364298, "relative_start": 0, "end": **********.788613, "relative_end": **********.788613, "duration": 0.4243149757385254, "duration_str": "424ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.788627, "relative_start": 0.4243288040161133, "end": **********.863201, "relative_end": 1.9073486328125e-06, "duration": 0.07457399368286133, "duration_str": "74.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043376, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.015700000000000002, "accumulated_duration_str": "15.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.826134, "duration": 0.01438, "duration_str": "14.38ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 91.592}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.849139, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 91.592, "width_percent": 2.93}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.855103, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 94.522, "width_percent": 5.478}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983290489%7C46%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InovQ3R1V2x5YnYySW5tK1I2YXlqM2c9PSIsInZhbHVlIjoieThvQld0R3l6QWczSzY0eHJsamV6OFZCTmlVdWhpYlk1ZVJVSmdoM2ZXaldoOVZxRzBNNStuL1VPcitjbFMva09Zam5IS0NkdGVJczV3MStzUzYwNDlxWnpORHRlUGpyMEgxYjJMNjgzMlB1REZrcXBoVi8zait5L3NBOE91TWhIQ3c0YWsveTF4RkRKb2svaEhLQThIY1ZvSWVvVjA5VVBYdGtQRFUrNVpPSTJjQXA5N0I4YXVKRkpuVU5nU2VZSVVxRGJNWjZZUUlDT0ovUWVISmZJcjB6RjZabHRKSVBqSE1zSzBYcXVwazdJSzNYSXpvdWhqcC9LOU9EbDIzU0lrdlVrbHF6L29WVFoyM285M1VJUURLZkM0cytiaVdIbDFBWDlpM0E4TXFnbTdpNzZob1kvbEp2N08wYkRWcDk4NnBTZTRKbmpscVIvR0RUZ05TVm11eGdRZlRXaFRYa3ZEeGIvMWVqejh3bHNlYkZHRHF2cGZQT2hQa3JlcGNJUGhJbEZaaFAzZUlKcHJaMEFSbzBsdHd0UTU3MWFPZ2RGU3ZRWXNQakI0Wm1DYmpQRGYzM0lYQWVHb0F0b3pTTzN2Y3ZSYXlmUlB1QTZqcWNncWlhcHdiT1RvOXlLQTh3aTRGVGJNT3VHK1o0b092VTN2d0xOcHhlMDE2ZGZJNkkiLCJtYWMiOiI3NWYxZDIyMWVmMjAwMmJiNWViNmFiY2M4YTNhMWNhYWJjMTI1NWI4MWUwZTJiNjFkMmZkYTExZTRjMWQxYzNmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik9tY2dIb0xkcDFHdEZzSHhjME5NZGc9PSIsInZhbHVlIjoiWFJ0eG0yMEdVQU5kcDRsMjVKMVh6bDVDcDFibDRFdDhScGlqZmI5eGpQZVVjS3crd1VBek9vZGtBbldqMDNVVWhKL01aako2dW02UmczOC95RlZDV3VqWjcxU2RqakJiRStUSFMweG9GdXVxNUFwUEN3dVU0N0dWTHRuYUFFL3JLd1dOL1dHdmNXZnY2NnpiNWZQRzIxWXplZGxrY3dWVzBGRjF6d1BXbE9VVU1RTW1GeTRuYXJidmJZSnRwQmJydDJTNW9UQ1o4b0pSZEVPcHk2VExuQmVQNEdJWU5nbmZldWlOc00wOHJaYUJyalV3YnhJblR1b0pmRkJPQ04zejU0SldBWGd1c3dxdjJ4M3NEaDRYU2V5dEM1dmZFTDBrZktNcTE5c2NIeWRIdDc5SDB6a1ZEMXpCSDZrSmVMKy9rYVNFMzNpMXdWTGRrSk83R2pWTFFJS2ZZOUo3Uzh2Q0NwWDEybnBKbnhtandlODhNZmdzQW0wa3RuZWFSZWpiU2VnYzBBT3VkRzlCT1hjd3Y5a2ppM3AzSktXVFZteG5lajN5eFNOblhWb3JqcDdnR1ZXR2NuZTJpczFOeDlOWnJaV2FGdm9rRmNDd0xmandTeG1OTTFwMzZZTmhvb255OTVaUGlnb2lwdnFtNEw4NHlLYXluSTVUOE9JY2FpKzciLCJtYWMiOiI5NmRiYjc2NmQxNzRkNzNmZThjYzU3OTI5YWQ1ZWRiNjQ2MDQwOTlkZjUzMTA4YTUwMTM0ZTFjODE5YWJlMGI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJremcrSzl1ZVZLYll0Z29adlFLY3c9PSIsInZhbHVlIjoiUnZzcjRXUTRBaE9mbUUwRHc3TFJDZVNIUVpCbG5JaTRFS3ZqNStrby90a05YQVZCYW8xT1UyNlI3cmlESm4zZTViNnhUeU9pN1hnOEUzYmVSZlU1Y1NTalNGbmNxRFQrb1V4L0RrcThwd1hDb2dkUTkzNml3TmEydTFoaTBDdFJSYTlXSUplWTVEZHR1UkJsQ0ZNSXdCa2tlTW5KYU9HV1JEa2NPbWtWTFhSUW1aM1JBaGx0LzRsbXQ0RVFpRGZVYmR6ZVRJY2cxVVdtL0Fya3daSTc3aG1lWnl0UEVoRm9sdnRNRkQ4TXg0cExsMDYvNmR1b2p0WVpzLzBTYmV1MVNQVGdpVWM0WDdXeFpWVE5oOUZycWl0Nk9SRFk2ZUs5VGNMTW9ZdWQrc1IvSVpuYUpBZDVkTEFDMlZBQk40NVJ0YndtaDZIeG1YeFpZY2t3bFM5bWtZekFDUm9VWEl5a2JuU2libUNTcnNCUHJod29ZNi9JaC9RUHZEUjBlUjI4dEJVRk00d1hyYis2QU1NL01oVTEzZjBRM3ZrOHN0NWxINHVLMjJNejY1dEVBZFNLcldYN2J1eWJZMEx1VVNzNVBsVEhwZFdlQnplbzdWbzhhZ1d4NHRMdXMzRFpteWtYZ3BzRStBUWpWOGJYTU5jalllQjVKT2lPTGFRTGdIZWIiLCJtYWMiOiIwY2Q4ZDRkY2I3OGMwOTAzNTVlOThjNGRkMTcwMWY1NTdkZmIxZTg4NjkzMmY3ZGFkN2QwMmIzOGEwNjgwNTNiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ill3R0JvUTg4MnBEVnJSNHIzMGtxY1E9PSIsInZhbHVlIjoiSFhPYnFDWENYRHJoZEsyRmc0WUhkOVNybXFLWVl2cVFHT1E2bGdoTm5OZytrYklsYzl6blZhYU9UZUZFNDhtSmQ5SVpJS3oxdk5ndnJNV2JYZlYxM1VIT3c5UlI2SWZTVThRdm04YndJNU9QQkJYSm9YTzExNi9YRnFtNHNsandMeWJNNWlhbE9hVllmYzRyK1RsMHgyVzYzZVBDK29KUEl1ZjEyQ3JTV0wvSUh6REJTNWJmOTJIbk1Xalk2YVZ3QXp0dUZBcWZMNnZybFJYM1B1dzhGdWZRZktRUEhOMXo5QnpudVNGWjFESm9oU0lqckhHUGdGUkNOQm1BTmlHUEQ0dmJsdXh4L3ZiRndvQlJJRHpEMzRPdEREQkZ6TkU4MjRpVDRPRUZIak11c2R1NEhqSzM0OTVYVkpPMXJwQVVzdE84VmdoK0dwb2RGWDlzanVqWWNvbW9VLzhUOVhZL2Y0QVhEL3A0UjhVd2FFZWpEMkw4cldocjltbmlFSWpNbmZvQmlwMWNpUzJLazdtc0hFQ3lsRE1ucVRIVUQ4Q0ZIWVVCZ1h4ODhSRHd3eWJLdGFUbVJJWGYwVVhDK3cwM0crL0htRmRlWUFacE5COWNqeVZxdHFKbHNkbjcwQlFXaGErQ2FDbkZvL3JGZUFPeWpEM3JZcFF3NUFwUHduZ2ciLCJtYWMiOiJlMDRkMzc4MDEzODM1MWM1NWVhODZhYTM3YzdmYThhOTM0YzA3MzgzOGM3MGNmM2FlZThkOTVjZTBlYmIwNDk5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJremcrSzl1ZVZLYll0Z29adlFLY3c9PSIsInZhbHVlIjoiUnZzcjRXUTRBaE9mbUUwRHc3TFJDZVNIUVpCbG5JaTRFS3ZqNStrby90a05YQVZCYW8xT1UyNlI3cmlESm4zZTViNnhUeU9pN1hnOEUzYmVSZlU1Y1NTalNGbmNxRFQrb1V4L0RrcThwd1hDb2dkUTkzNml3TmEydTFoaTBDdFJSYTlXSUplWTVEZHR1UkJsQ0ZNSXdCa2tlTW5KYU9HV1JEa2NPbWtWTFhSUW1aM1JBaGx0LzRsbXQ0RVFpRGZVYmR6ZVRJY2cxVVdtL0Fya3daSTc3aG1lWnl0UEVoRm9sdnRNRkQ4TXg0cExsMDYvNmR1b2p0WVpzLzBTYmV1MVNQVGdpVWM0WDdXeFpWVE5oOUZycWl0Nk9SRFk2ZUs5VGNMTW9ZdWQrc1IvSVpuYUpBZDVkTEFDMlZBQk40NVJ0YndtaDZIeG1YeFpZY2t3bFM5bWtZekFDUm9VWEl5a2JuU2libUNTcnNCUHJod29ZNi9JaC9RUHZEUjBlUjI4dEJVRk00d1hyYis2QU1NL01oVTEzZjBRM3ZrOHN0NWxINHVLMjJNejY1dEVBZFNLcldYN2J1eWJZMEx1VVNzNVBsVEhwZFdlQnplbzdWbzhhZ1d4NHRMdXMzRFpteWtYZ3BzRStBUWpWOGJYTU5jalllQjVKT2lPTGFRTGdIZWIiLCJtYWMiOiIwY2Q4ZDRkY2I3OGMwOTAzNTVlOThjNGRkMTcwMWY1NTdkZmIxZTg4NjkzMmY3ZGFkN2QwMmIzOGEwNjgwNTNiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ill3R0JvUTg4MnBEVnJSNHIzMGtxY1E9PSIsInZhbHVlIjoiSFhPYnFDWENYRHJoZEsyRmc0WUhkOVNybXFLWVl2cVFHT1E2bGdoTm5OZytrYklsYzl6blZhYU9UZUZFNDhtSmQ5SVpJS3oxdk5ndnJNV2JYZlYxM1VIT3c5UlI2SWZTVThRdm04YndJNU9QQkJYSm9YTzExNi9YRnFtNHNsandMeWJNNWlhbE9hVllmYzRyK1RsMHgyVzYzZVBDK29KUEl1ZjEyQ3JTV0wvSUh6REJTNWJmOTJIbk1Xalk2YVZ3QXp0dUZBcWZMNnZybFJYM1B1dzhGdWZRZktRUEhOMXo5QnpudVNGWjFESm9oU0lqckhHUGdGUkNOQm1BTmlHUEQ0dmJsdXh4L3ZiRndvQlJJRHpEMzRPdEREQkZ6TkU4MjRpVDRPRUZIak11c2R1NEhqSzM0OTVYVkpPMXJwQVVzdE84VmdoK0dwb2RGWDlzanVqWWNvbW9VLzhUOVhZL2Y0QVhEL3A0UjhVd2FFZWpEMkw4cldocjltbmlFSWpNbmZvQmlwMWNpUzJLazdtc0hFQ3lsRE1ucVRIVUQ4Q0ZIWVVCZ1h4ODhSRHd3eWJLdGFUbVJJWGYwVVhDK3cwM0crL0htRmRlWUFacE5COWNqeVZxdHFKbHNkbjcwQlFXaGErQ2FDbkZvL3JGZUFPeWpEM3JZcFF3NUFwUHduZ2ciLCJtYWMiOiJlMDRkMzc4MDEzODM1MWM1NWVhODZhYTM3YzdmYThhOTM0YzA3MzgzOGM3MGNmM2FlZThkOTVjZTBlYmIwNDk5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n"}}