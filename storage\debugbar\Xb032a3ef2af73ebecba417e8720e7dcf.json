{"__meta": {"id": "Xb032a3ef2af73ebecba417e8720e7dcf", "datetime": "2025-06-27 00:25:55", "utime": **********.576407, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.141459, "end": **********.576422, "duration": 0.4349629878997803, "duration_str": "435ms", "measures": [{"label": "Booting", "start": **********.141459, "relative_start": 0, "end": **********.51217, "relative_end": **********.51217, "duration": 0.370711088180542, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.51218, "relative_start": 0.37072110176086426, "end": **********.576424, "relative_end": 1.9073486328125e-06, "duration": 0.06424379348754883, "duration_str": "64.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01667, "accumulated_duration_str": "16.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5385718, "duration": 0.01566, "duration_str": "15.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.941}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.562982, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.941, "width_percent": 2.939}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.569139, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.881, "width_percent": 3.119}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IkZXTnEvODd1YlRaVkNaWG5xZ1BmN3c9PSIsInZhbHVlIjoiT1R0b0ozejc5eWUwTzRIWXFnK0o2dz09IiwibWFjIjoiYTBjMTE3ODdkYjM1Y2I4MjZjZTk2M2QwYjhkMDJlODNkM2ExZTAyZjE1OWFlMzg5NzE1MGQxODZlMTNhNDNlYiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-558110267 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-558110267\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1364310594 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1364310594\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1218837047 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1218837047\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-272732357 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IkZXTnEvODd1YlRaVkNaWG5xZ1BmN3c9PSIsInZhbHVlIjoiT1R0b0ozejc5eWUwTzRIWXFnK0o2dz09IiwibWFjIjoiYTBjMTE3ODdkYjM1Y2I4MjZjZTk2M2QwYjhkMDJlODNkM2ExZTAyZjE1OWFlMzg5NzE1MGQxODZlMTNhNDNlYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983952525%7C56%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilg4NDRHaHQzK290S1FOK0dyVUNGc3c9PSIsInZhbHVlIjoicWUzU1Nqak5kdDVpNXhWaW41SFhRaWVtUC9ETVBlek0yajhsSFlqRjViOGVDUzRKVGJ5dGZ6NmY4bWZubk9aNkpJanNVam9OUjRvdG5HU0s0Q3NuWEt2SDViRWdKc2RZcmJBcGFnb1A4N0tKUVhoRUorMkg0QkU0alRNVFFjQ3dHa29oTXhGVnpDd1VtSk1kUDJQaWF6RzdtTnJGdHJQQllVUlhzdW41cExxQXFZNmt4TTZzdGxhNVFQYXNQSGh2ZytmOFNIL1FCZHIrVUozZklMRDVKRUdTREtTMTJCMjJrTFgxWllMMXgrQm1VUVNiNklNNFdTSFNFdmFZQVhVRTNsRDMyVUNuR09CY3hoaitJWHgvYkJuSFRQOVRXaUhmckpvV01sa1hJSTFKMlc3eUxiQ0t0V2RRWTcwMlpydHVucmVHSVM0YU5hUnlFUGw5aCtNUGJQZzR5bHFVL0VxdDZPSDJmYkNsUWFxQWNJckZ6UzFMc2swVTE3eU9Za2c1ZGpCRDJLa0pZQmFNMHNBUE5oS3pHYjRESzFPS0ZUV1NNSEVhZER2cXE1ako1NzQxZ0pwTXhheWhYMmlzRlZkZGxabkV1VDBvWk55SjRxa1VoNXg5dkZ4Z3BxbnhyYlFIL0ljZlBsa3FISmhBT20wZmFtWFdybTBram1qajdzOHoiLCJtYWMiOiIyMThhZTQxODI1NzUwODJhMWY0MmE5OTVhMDY0MjcwMjk4YTE4ZGRlMTgzOThlNGU5MDM0ODkxZGQxZTY4NDhiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlE0elU2Nm1CMkd5T05zb0VBdVVQOFE9PSIsInZhbHVlIjoiWVZoOVlPUUkvQ3JtZDdlTzI2S3JtVkIxNnA4akFRclM1UlRhMHNRWXBjdDY0U1hvVUFPOFJRWGZ5MFV3Uk5UMVg2TFhLaXBuQTFCOW5vWFBBQkNKanoydnFVRS82S1licGQ0OEJHb1lkR0Y2b24vQ2dncmFDUnhLUXBtNXAvckFzVFluT2VmZTh1STk4SnZCSkpqUXBBNjFlYkwraFFRZjVtK2pILzJhcXVZZGhuVTNrTkJGUXVycUhMUDArb2YyTE1VSThndWFzbFAvS0tDN3F2S2wrOGZZYmJyWlZxNGxsS1JXbzU1c0YyUjM4WTYwNklhVjErWk5xRmZ4UHIwQVF4RUEyclZnRFY1aDhlNGNwcXMvNktuWUhOKzNScWdsNkIrbll4czYyK2QzSFd0RG82RmNiVWNoMHRZQ1pIV2oxZmxwT3ZDSm0zaU9kQXBaa3kzRkFHT2JBVUxBWnJwUlkxTVU5ME9pZTdhZzJKYXBUU3JlS1ptWWQ5Q3RmRHNKZ3BlQVRlN08rOHFuMUw4RjhhejRVaTZVWGVNUkdYT01XS2tsOVhtcGFsVk92b05ka2tLUFJkR2ZieUxoYW9rRFczb1pYZUtCSGhnTTFhWmFRQlg2ZFM2bnJHQ2hhbmJzNlQ5b0Z0TXJzaS80MFlaeDVaZmlXSmtyaWZnVkdIOFIiLCJtYWMiOiJjYWZlOGE0NTIwOThlMDUzNmFkZDJjNTg3NTgyODQyODI2N2U1YWMyZmFiNGRiOWRiMWU0ZDc4OTZlODBmOGY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-272732357\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1892910489 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1892910489\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2024915864 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:25:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJIUitxSHdXQzhkOHdzMU5WbnFidmc9PSIsInZhbHVlIjoiU3pVWGRaUkg0N2F0SStadDZPN29LRGx6d2pXdHNxWWNkTmtSUjBrNmNOendKcC9weFFHd2NIZjZxMXNvSmhXb2VJM0FyWjRKVHdVcU1OVGY2KzQrM0tiTDdTMVFoVUFFY0NhNFJnZ3VGTlNsUjZscTlIaENIa2Y5Vzk2dHBPL3VBMGRlc0RLaVA4c1dRWlZBUmNHV203MWlmTmMvWWh3MDhyUmlyL2xoWTFQTEViQi94Q2VUVlFrOUZnZGVmNk00TWswUVVZSlc2Tm02Qnk0WGJsUkZlaHVBcE5WcUpCWDNNK3BEdjByZFJhUnFjK0VEOCs2TTFhOVphb2VHRDAxMEJoUGEzcTB4dzdIU1AwZGdJVWNKYkNFY0dUbjlveFE4U1BkS1paVzFuTUlESEFKQ21tUlNRRlpFZmdhdzNjZlVZUnBYQ2lFU1RsTzJWUHZJTi9acVNTd0dNbGFmV2Fsd3Z0VDlqZzNmQTFrNzZwd0Q0ZDN4Uld6ZXVoMGpnZzVsZ0NybGQ5TThjRWxOSmZJUUZScE1wVWdCakdFUkp0K1BPc1BRWkt3TVliNnRiODl4MkhHbEJqTEQzYlFjWWM2Yy94bmt3RkhGaXhKT1oySVVuZ1hVQ0p0NGFZQ2FJUXdtMFhFOXR2N08xTGw5ZCs0OTd3b3Z2dUI3YmIwRGJOK1EiLCJtYWMiOiJiM2QyOWMwZmExMGUwZjYyZDJkMjU4YTYyYzVlOWJiMzkxMTk2N2MzMmNlZjdkMDhhMGVjNWFjMmMxOWI4ODA2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:25:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlloMVlZa2xCcUlTR1JhZWk2TmRSN1E9PSIsInZhbHVlIjoiWTBYL2VjbXJyQ2lJZ0lnYjZRcTVzK0g4d0NlcjdSeU1pSDhSSXZEZWRCTEVWZTNRR3hzZ3JkYlYxSDVjOTRjTEJzWmEvZUcwWWlhY0pqL04zc1VnMTRPbUl1bGU0SmkwN3pHWU9UZ2Jncys1ZmNtak5WNy9VNDZrWHdreUJyaUYxcS9xaW9ZY3NBK2hpa1hGL1dYazBoYkhlT3BkMk5hem5ySkJUcS8raTRWRk5mUFZ6WnNhU2xablBtczdoU1Q2bEw5SlhxaURwaTNZajNBeWFGRFZEVGJlczJ3NnhtWFFLQlRENGdCekpMQmJpVEdqS2V3MWJqTG1oQUVRV2g4OGVnUHVUQ1I2a0tuSmVLTlpzSWUwamY5Y25NaUlNRDNCWUVUaGV4Y0VIaFFWeXdWMWZ1RlBVMWZ0VG9oQ05OZjF2S1ZCRm1VdmlNSVU5bXIzcVZnSzFiMkRaNS91SGkzSmxhRUNtV3NmQm9xSnZZekMreTBTRURkbFZiT0NmZ1lHdk81RU1seHNBODRnYUFGY3o2eCtST0V4dmJrdTIvd1BpMjY2WG9EakdpcWt2R053S0RidUlmTWNEVHRsZHhBYXYxZW5sTS9vV1hUeFVZVHZFdHo3Z253andwMkhES1pHbW5jSk5tZC8raDlsOFMwcjY1TVdLeDRrWk8zL0tyWWwiLCJtYWMiOiJmMWY3OGJhMzY1MzJlMzQxMTQ4MmMxYzk5ZTk1MmIxZjNjNTcwZGQ1ZDk0ZmExOWM2ZDUzMTg1NTIxNTc1OGRkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:25:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJIUitxSHdXQzhkOHdzMU5WbnFidmc9PSIsInZhbHVlIjoiU3pVWGRaUkg0N2F0SStadDZPN29LRGx6d2pXdHNxWWNkTmtSUjBrNmNOendKcC9weFFHd2NIZjZxMXNvSmhXb2VJM0FyWjRKVHdVcU1OVGY2KzQrM0tiTDdTMVFoVUFFY0NhNFJnZ3VGTlNsUjZscTlIaENIa2Y5Vzk2dHBPL3VBMGRlc0RLaVA4c1dRWlZBUmNHV203MWlmTmMvWWh3MDhyUmlyL2xoWTFQTEViQi94Q2VUVlFrOUZnZGVmNk00TWswUVVZSlc2Tm02Qnk0WGJsUkZlaHVBcE5WcUpCWDNNK3BEdjByZFJhUnFjK0VEOCs2TTFhOVphb2VHRDAxMEJoUGEzcTB4dzdIU1AwZGdJVWNKYkNFY0dUbjlveFE4U1BkS1paVzFuTUlESEFKQ21tUlNRRlpFZmdhdzNjZlVZUnBYQ2lFU1RsTzJWUHZJTi9acVNTd0dNbGFmV2Fsd3Z0VDlqZzNmQTFrNzZwd0Q0ZDN4Uld6ZXVoMGpnZzVsZ0NybGQ5TThjRWxOSmZJUUZScE1wVWdCakdFUkp0K1BPc1BRWkt3TVliNnRiODl4MkhHbEJqTEQzYlFjWWM2Yy94bmt3RkhGaXhKT1oySVVuZ1hVQ0p0NGFZQ2FJUXdtMFhFOXR2N08xTGw5ZCs0OTd3b3Z2dUI3YmIwRGJOK1EiLCJtYWMiOiJiM2QyOWMwZmExMGUwZjYyZDJkMjU4YTYyYzVlOWJiMzkxMTk2N2MzMmNlZjdkMDhhMGVjNWFjMmMxOWI4ODA2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:25:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlloMVlZa2xCcUlTR1JhZWk2TmRSN1E9PSIsInZhbHVlIjoiWTBYL2VjbXJyQ2lJZ0lnYjZRcTVzK0g4d0NlcjdSeU1pSDhSSXZEZWRCTEVWZTNRR3hzZ3JkYlYxSDVjOTRjTEJzWmEvZUcwWWlhY0pqL04zc1VnMTRPbUl1bGU0SmkwN3pHWU9UZ2Jncys1ZmNtak5WNy9VNDZrWHdreUJyaUYxcS9xaW9ZY3NBK2hpa1hGL1dYazBoYkhlT3BkMk5hem5ySkJUcS8raTRWRk5mUFZ6WnNhU2xablBtczdoU1Q2bEw5SlhxaURwaTNZajNBeWFGRFZEVGJlczJ3NnhtWFFLQlRENGdCekpMQmJpVEdqS2V3MWJqTG1oQUVRV2g4OGVnUHVUQ1I2a0tuSmVLTlpzSWUwamY5Y25NaUlNRDNCWUVUaGV4Y0VIaFFWeXdWMWZ1RlBVMWZ0VG9oQ05OZjF2S1ZCRm1VdmlNSVU5bXIzcVZnSzFiMkRaNS91SGkzSmxhRUNtV3NmQm9xSnZZekMreTBTRURkbFZiT0NmZ1lHdk81RU1seHNBODRnYUFGY3o2eCtST0V4dmJrdTIvd1BpMjY2WG9EakdpcWt2R053S0RidUlmTWNEVHRsZHhBYXYxZW5sTS9vV1hUeFVZVHZFdHo3Z253andwMkhES1pHbW5jSk5tZC8raDlsOFMwcjY1TVdLeDRrWk8zL0tyWWwiLCJtYWMiOiJmMWY3OGJhMzY1MzJlMzQxMTQ4MmMxYzk5ZTk1MmIxZjNjNTcwZGQ1ZDk0ZmExOWM2ZDUzMTg1NTIxNTc1OGRkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:25:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2024915864\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-546295455 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IkZXTnEvODd1YlRaVkNaWG5xZ1BmN3c9PSIsInZhbHVlIjoiT1R0b0ozejc5eWUwTzRIWXFnK0o2dz09IiwibWFjIjoiYTBjMTE3ODdkYjM1Y2I4MjZjZTk2M2QwYjhkMDJlODNkM2ExZTAyZjE1OWFlMzg5NzE1MGQxODZlMTNhNDNlYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-546295455\", {\"maxDepth\":0})</script>\n"}}