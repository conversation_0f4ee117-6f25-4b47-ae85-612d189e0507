{"__meta": {"id": "X0d781e46b82191adcb180fabfecd45a8", "datetime": "2025-06-27 00:26:02", "utime": 1750983962.005946, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.533052, "end": 1750983962.005961, "duration": 0.47290897369384766, "duration_str": "473ms", "measures": [{"label": "Booting", "start": **********.533052, "relative_start": 0, "end": **********.953067, "relative_end": **********.953067, "duration": 0.4200150966644287, "duration_str": "420ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.953076, "relative_start": 0.42002391815185547, "end": 1750983962.005963, "relative_end": 2.1457672119140625e-06, "duration": 0.0528872013092041, "duration_str": "52.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0030499999999999998, "accumulated_duration_str": "3.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9808028, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.328}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.992154, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.328, "width_percent": 19.344}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.99902, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.672, "width_percent": 20.328}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InBCSnZpbFRhcjdqQWg3T0NjbEk2L0E9PSIsInZhbHVlIjoicjNXTDArVnRYcU4ybXoySEZtL0pIUT09IiwibWFjIjoiNTVjZDE5MTMwODk3ZmE0MTZkM2E3MWQ2Yzk4MzI2NjAyMzAyMzc1MmI3MmY2ZjJkY2MwZGZiY2M5MjA0YTg1NiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-534880476 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-534880476\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-463519775 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-463519775\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2031003173 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2031003173\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InBCSnZpbFRhcjdqQWg3T0NjbEk2L0E9PSIsInZhbHVlIjoicjNXTDArVnRYcU4ybXoySEZtL0pIUT09IiwibWFjIjoiNTVjZDE5MTMwODk3ZmE0MTZkM2E3MWQ2Yzk4MzI2NjAyMzAyMzc1MmI3MmY2ZjJkY2MwZGZiY2M5MjA0YTg1NiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983955568%7C57%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZPZ1k2S2VmZmhjVkZGd3BvZEpTY1E9PSIsInZhbHVlIjoiTkdZejREU1hXZDZBVzkvRUhQdEZMMDR1d3Uvb3FucFZZMGh6RjA1UmFqdlQ3bHBxTExMZmlQTHVPazJUcVJxSjB6RC9TaU5GTEladnJVU2JBdFhoQUNCSVpnSGFuNUZySitmUzRQVHlmUzVpTTRYTVRWa3d6VVNkbi9pcEExWjF2TDNUNTE1bUNxVnUvMWxodldzZE1yQjlrckdRMGg5ajh0T3JOQ0MzN1FIQnN4ajV0QXVxZ0NLK1p5dGJ0L2FXS3lhQmF1TzdvN1RzUzdVRVJaVDdlaHM3WWpwaEt1Vjl3b241VCtobVRDREsvN3pXUkxpL0t4OG5pcUNqT2pkUkVyVjdsQnRGallVdkVtM1VPZlphTUF5VXNoWkduaWF3RmVXNVhHcHVTdjVmRHdJNVVYZk5yQ0o2OG45Rm00d2VXcGNIT3d3dUtYSlV5SGJRWUFGbDd0UUdGcmFINzJUSHc3QUhVYzRrM0FLemVYR2xicWdzU1hjUHQ3eEdtOEIybzdLTld5OW5HVlhBOER5ZzhRTk5zM1YyR0tDZW5CYVZocE9TdnBPc0xMUDFZbndtb2llNEQ0ZzNEaW1lOE5ZMFU1bHdESmNXa29adXJqRFdzbGRSSFFKcFFoWjhaaVNWbHNVQU81NzZLNEhQQkh5MUNoWk4zeDRHV200VFJsZ3UiLCJtYWMiOiJkNjdhZTc4OWU5MjkxNDFmMjViMTg5YTcxOGRhODg4YTdmMGQ1YjQ0ZjIzMjAwM2NjNmViYzUzYWFhNjQ4ZmM2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Inl2TjJmOERpd2tSM25KRnB4aXp0REE9PSIsInZhbHVlIjoiMk43dUw3MFYvdmE1US9jbjI4Z1pLaUt6TUhvMEVwc3orWHpaR3FLOVJ2NlRaR2xXT0JIYzZYZEZMc2ZSMHY1K3VTaFZpUm9GRnBSMDlzTTR2VFF0TWpHUCtRdzBNd3l3MXZMYzQyVTl0SVdUSVVmT3pwSFh1WE8vN1ZzWVZYUjVVL1pudFZGR2liN0tLeUpGTFJRTG1NblJmTDY1dU96T0FRYXVRaGRIRFJRWWhzM3pVWWlpc2JDTnFSYkNxVDYzTHpEbmN4SHRucXpuQjUwTTg4OFhMVE0wN0VWMDFxaUxkQ2M1TEc1Y3NWVkhtSDVTMlJmNUhQbXRlZng5bzRxa2ptS25wVHJGakF3UUg5TWE0NVVrK1FpdHpzMytqZi9UUDBheUVxMTlQYnFNU1ltZW1yd2tYTkx2SGN6YjBEUlhhZmo4T1lGZDdJZjZwTTFPTDB0WEIxNHdmb2VNODVBNXd0aythQ05xU2VOSERsZjZKd2NNUzQ5Nms2eWpwNndyU0hIcGRmVEN5NTNBeGoyeDhmVGtYWDJVbUlTNlphU3A3NjFiQkRqS0t2TkhkbkJNaC9DVVpxa1BTd29xNDNERWRLSjg1aGYvK2NmejJqNGt3M245eFVGUFN2TXZWMWh4Wkp4QndLUXVOZE5RTzlGQlNCc2UrSWJJb2xxTzB6VnciLCJtYWMiOiJmNGZhNTkzNTE2MWQyM2E1M2EwNmRiODFkZjM5MTcyYzQ4OWI0ODE5ODQwNTFjYTBmZjkwMDYyNzU4NGJmNmU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1003288622 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003288622\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1178230540 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:26:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhmLzVLWDU0MmJhcVg4eWVHZHhjMnc9PSIsInZhbHVlIjoiYlQ2aDAxVEhOYUtlYVorRzNHMDlVbkJma1R0NFhhMmk5elBDd0tMT1ZYbkNIK1loTkYrWHphamhGRU05MG9sRzdacC9kQWtxVjRxR05UOTF2K3hNSVBkelgrRm5DSXdLb1ROSFVGZFNwZ2lPK3FhVHI4NCtYZ016ZmFyTzRsOTZpR0Z2OU9vSlFvWmNXaVFnTnlNb3AySnhKUUpqTnlCT2hjZTZJaDJYeHpaYjRGV083MThwRG1VT25nN0FEV3lXUUFmenIzZklQcGpOUUFHUHJ3YUlMODFJT0VKOFhkdS9zbGYvUVZhcnhwRCs1c2JRREFLZTNvZzRpUmtnTlllY2ExblJKSFhoQ1J6S1F0N1l4RjhXQ0pGSlI0WVh4dlc1bmlQL216V1dqUVZ1ZjFzZHltRTY2YVp2MnVJRDNlK3FURHhuWUE3bTZCWGN4bWlRY1ZQVzhJSk4zRGNGVFRUMi8rMk9pQVVEa2VGVXROWXhPZkRQdFZ3ZGpOZjFKdnBEL3ZVc3lHM0hQWUFRU3dkUi9iZGZjMHEyZ0krRGFDRGo4WjlCZDZxaDNmUCttM05XanpwK2Y2SVU2WTUrbWFOOHNZMWRyd256eWNiY1JvRnhlcjltbFN1ekVXNzZObnVBYVBQVGZHemlVSWU0ZmNXM3QxRWFRenVsMlc1R0swRFYiLCJtYWMiOiI0YTJmMjEwMWI5Y2U4YzQ5NjU2OTU3MmExZWEyY2ViYTZlZjQxNzdhZmNkZGUyYWY1NGE3Njc5YzAyMjExZWYyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:26:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVnUDRucG1vRU1LQWRHSzlRdnRiSWc9PSIsInZhbHVlIjoiQmROU1Vucmw3QlZrMmZZTjJvQW5WQmQrb2RETWpLZERaVGZ1Z21UUnJIdnNFcTdzTzMxMVhnSG40b3JQZ3lycmV5RzIzNUxLaGZqVFJRWjhwREdNQy9FdEFCSVFZM1FTZFM5UUJrVDVSaitoQWtyRGE2T1k4enJFNGhCRGRZRUw4RUVzZTk1cFdaOFNkaGdnM1RXNXBDT0dmTTh0c3dGRExRVXJNR3kyMkthRjM1OHNwQjhUT2VTZk1GQi9iVituL0EvcHhiZGhNSXFxcWZjYnhEbVBPazRycWx6OXdQVkl0YVFrZTh1dVRuNFg4cFVaNHpCMlpVL0s0Y1FPbFBMZXlWM29RQkFJS29tRElxamRreEp1U0Z3Y2tsRXhjdFRacXNzejV2RG4ybVo3TzM1cU1kd2RZRGRiMlRubGZNaVRvY0s3UFhpZGozN0I4SUtDTTdKUHI4NkVBUGg4aVdESjdhV2EwQ1Roa2ZaV3BVSEpPbEhCMVVXR2cvS29BR2U5MnRzaE1qK1puaitkaFR3NVZuUGpRS2ljSngyYmNGYnFKc1I2MXJoUFdTUGdOTE9ibEt5akxUWmVyRVc5L3pDT2xXK1JoNXNXeWxBRGlNZllHQ0tyRStCTHFWaWhjOEMvUm91S09rSW9vYkZCQU5uY2xwbEtqS2pFb3cza2pDek8iLCJtYWMiOiJkODMzYzkyZGNkMDI4MjQ1NjQxZGRkYWRkZmM0MzA4M2I5ZDVjYWUwMGI4NzkwYjZlOGM5ZTUzZWZkN2NlN2FlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:26:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhmLzVLWDU0MmJhcVg4eWVHZHhjMnc9PSIsInZhbHVlIjoiYlQ2aDAxVEhOYUtlYVorRzNHMDlVbkJma1R0NFhhMmk5elBDd0tMT1ZYbkNIK1loTkYrWHphamhGRU05MG9sRzdacC9kQWtxVjRxR05UOTF2K3hNSVBkelgrRm5DSXdLb1ROSFVGZFNwZ2lPK3FhVHI4NCtYZ016ZmFyTzRsOTZpR0Z2OU9vSlFvWmNXaVFnTnlNb3AySnhKUUpqTnlCT2hjZTZJaDJYeHpaYjRGV083MThwRG1VT25nN0FEV3lXUUFmenIzZklQcGpOUUFHUHJ3YUlMODFJT0VKOFhkdS9zbGYvUVZhcnhwRCs1c2JRREFLZTNvZzRpUmtnTlllY2ExblJKSFhoQ1J6S1F0N1l4RjhXQ0pGSlI0WVh4dlc1bmlQL216V1dqUVZ1ZjFzZHltRTY2YVp2MnVJRDNlK3FURHhuWUE3bTZCWGN4bWlRY1ZQVzhJSk4zRGNGVFRUMi8rMk9pQVVEa2VGVXROWXhPZkRQdFZ3ZGpOZjFKdnBEL3ZVc3lHM0hQWUFRU3dkUi9iZGZjMHEyZ0krRGFDRGo4WjlCZDZxaDNmUCttM05XanpwK2Y2SVU2WTUrbWFOOHNZMWRyd256eWNiY1JvRnhlcjltbFN1ekVXNzZObnVBYVBQVGZHemlVSWU0ZmNXM3QxRWFRenVsMlc1R0swRFYiLCJtYWMiOiI0YTJmMjEwMWI5Y2U4YzQ5NjU2OTU3MmExZWEyY2ViYTZlZjQxNzdhZmNkZGUyYWY1NGE3Njc5YzAyMjExZWYyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:26:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVnUDRucG1vRU1LQWRHSzlRdnRiSWc9PSIsInZhbHVlIjoiQmROU1Vucmw3QlZrMmZZTjJvQW5WQmQrb2RETWpLZERaVGZ1Z21UUnJIdnNFcTdzTzMxMVhnSG40b3JQZ3lycmV5RzIzNUxLaGZqVFJRWjhwREdNQy9FdEFCSVFZM1FTZFM5UUJrVDVSaitoQWtyRGE2T1k4enJFNGhCRGRZRUw4RUVzZTk1cFdaOFNkaGdnM1RXNXBDT0dmTTh0c3dGRExRVXJNR3kyMkthRjM1OHNwQjhUT2VTZk1GQi9iVituL0EvcHhiZGhNSXFxcWZjYnhEbVBPazRycWx6OXdQVkl0YVFrZTh1dVRuNFg4cFVaNHpCMlpVL0s0Y1FPbFBMZXlWM29RQkFJS29tRElxamRreEp1U0Z3Y2tsRXhjdFRacXNzejV2RG4ybVo3TzM1cU1kd2RZRGRiMlRubGZNaVRvY0s3UFhpZGozN0I4SUtDTTdKUHI4NkVBUGg4aVdESjdhV2EwQ1Roa2ZaV3BVSEpPbEhCMVVXR2cvS29BR2U5MnRzaE1qK1puaitkaFR3NVZuUGpRS2ljSngyYmNGYnFKc1I2MXJoUFdTUGdOTE9ibEt5akxUWmVyRVc5L3pDT2xXK1JoNXNXeWxBRGlNZllHQ0tyRStCTHFWaWhjOEMvUm91S09rSW9vYkZCQU5uY2xwbEtqS2pFb3cza2pDek8iLCJtYWMiOiJkODMzYzkyZGNkMDI4MjQ1NjQxZGRkYWRkZmM0MzA4M2I5ZDVjYWUwMGI4NzkwYjZlOGM5ZTUzZWZkN2NlN2FlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:26:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1178230540\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-325285155 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InBCSnZpbFRhcjdqQWg3T0NjbEk2L0E9PSIsInZhbHVlIjoicjNXTDArVnRYcU4ybXoySEZtL0pIUT09IiwibWFjIjoiNTVjZDE5MTMwODk3ZmE0MTZkM2E3MWQ2Yzk4MzI2NjAyMzAyMzc1MmI3MmY2ZjJkY2MwZGZiY2M5MjA0YTg1NiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-325285155\", {\"maxDepth\":0})</script>\n"}}