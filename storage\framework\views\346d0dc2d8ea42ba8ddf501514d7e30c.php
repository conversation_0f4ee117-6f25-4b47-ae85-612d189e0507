
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('POS Summary')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('POS Summary')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/datatable/buttons.dataTables.min.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div id="printableArea">
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th><?php echo e(__('POS ID')); ?></th>
                                    <th><?php echo e(__('Date')); ?></th>
                                    <th><?php echo e(__('Customer')); ?></th>
                                    <th><?php echo e(__('Warehouse')); ?></th>
                                    <th><?php echo e(__('Sub Total')); ?></th>
                                    <th><?php echo e(__('Discount')); ?></th>
                                    <th><?php echo e(__('Total')); ?></th>
                                    <th><?php echo e(__('Payment Method')); ?></th>
                                    <th><?php echo e(__('منشئ الفاتورة')); ?> (<?php echo e(__('Invoice Creator')); ?>)</th>
                                    <th><?php echo e(__('الإجراءات')); ?> (<?php echo e(__('Actions')); ?>)</th>
                                </tr>
                                </thead>

                                <tbody>
                                    
                                <?php $__empty_1 = true; $__currentLoopData = $posPayments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $posPayment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td class="Id">
                                            <a href="<?php echo e(route('pos.show',\Crypt::encrypt($posPayment->id))); ?>" class="btn btn-outline-primary">
                                                <?php echo e(AUth::user()->posNumberFormat($posPayment->id)); ?>

                                            </a>
                                        </td>

                                        <td><?php echo e(Auth::user()->dateFormat($posPayment->created_at)); ?></td>
                                        <?php if($posPayment->customer_id == 0): ?>
                                            <td class=""><?php echo e(__('Walk-in Customer')); ?></td>
                                        <?php else: ?>
                                            <td><?php echo e(!empty($posPayment->customer) ? $posPayment->customer->name : ''); ?> </td>
                                        <?php endif; ?>
                                        <td><?php echo e(!empty($posPayment->warehouse) ? $posPayment->warehouse->name : ''); ?> </td>
                                        <td><?php echo e(!empty($posPayment->posPayment)? \Auth::user()->priceFormat ($posPayment->posPayment->amount) :0); ?></td>
                                        <td><?php echo e(!empty($posPayment->posPayment)? \Auth::user()->priceFormat($posPayment->posPayment->discount) :0); ?></td>
                                        <td><?php echo e(!empty($posPayment->posPayment)? \Auth::user()->priceFormat($posPayment->posPayment->discount_amount) :0); ?></td>
                                        <td>
                                            <?php if($posPayment->status_type == 'returned'): ?>
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('مرتجع بضاعة')); ?> ↩️</span>
                                            <?php elseif($posPayment->status_type == 'cancelled'): ?>
                                                <span class="badge bg-secondary text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('ملغية')); ?> ❌</span>
                                            <?php elseif(!empty($posPayment->customer) && $posPayment->customer->is_delivery): ?>
                                                <?php if($posPayment->is_payment_set): ?>
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('تم التحصيل من مندوب التوصيل')); ?> ✅</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('جاري التحصيل')); ?> 🚚</span>
                                                <?php endif; ?>
                                            <?php elseif(!empty($posPayment->posPayment) && !empty($posPayment->posPayment->payment_type)): ?>
                                                <?php if($posPayment->posPayment->payment_type == 'cash'): ?>
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('Cash')); ?> 💵</span>
                                                <?php elseif($posPayment->posPayment->payment_type == 'network'): ?>
                                                    <span class="badge bg-info text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('Network')); ?> 💳</span>
                                                <?php elseif($posPayment->posPayment->payment_type == 'split'): ?>
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('Split Payment')); ?> 💳 💵</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('Unpaid')); ?></span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('Unpaid')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo e(!empty($posPayment->createdBy) ? $posPayment->createdBy->name : __('غير معروف') . ' (' . __('Unknown') . ')'); ?>

                                        </td>
                                        <td class="Action">
                                            <div class="action-btn bg-info ms-2">
                                                <a href="<?php echo e(route('pos.show', \Crypt::encrypt($posPayment->id))); ?>" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="<?php echo e(__('عرض التفاصيل')); ?>">
                                                    <i class="ti ti-eye text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-success ms-2">
                                                <a href="<?php echo e(route('pos.thermal.print', $posPayment->id)); ?>" class="mx-3 btn btn-sm d-inline-flex align-items-center" target="_blank" data-bs-toggle="tooltip" title="<?php echo e(__('طباعة الفاتورة الحرارية')); ?>">
                                                    <i class="ti ti-printer text-white"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="10" class="text-center text-dark"><p><?php echo e(__('No Data Found')); ?></p></td>
                                    </tr>
                                <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
    $(document).on('click', '.pay-button', function(e) {
        e.preventDefault();
        var id = $(this).data('id');

        if (confirm('<?php echo e(__("هل أنت متأكد من تحصيل هذه الفاتورة؟")); ?>')) {
            $.ajax({
                url: '<?php echo e(route("pos.collect.payment")); ?>',
                type: 'POST',
                data: {
                    id: id,
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(data) {
                    if (data.success) {
                        show_toastr('Success', data.success, 'success');
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        show_toastr('Error', data.error, 'error');
                    }
                },
                error: function(data) {
                    show_toastr('Error', '<?php echo e(__("حدث خطأ أثناء معالجة الطلب")); ?>', 'error');
                }
            });
        }
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\erpq24\resources\views/pos/report.blade.php ENDPATH**/ ?>