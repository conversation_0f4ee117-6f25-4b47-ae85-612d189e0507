{"__meta": {"id": "Xf789323e2fd425085616faf90b2f7c6f", "datetime": "2025-06-27 01:03:22", "utime": **********.201111, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750986201.725621, "end": **********.201126, "duration": 0.47550511360168457, "duration_str": "476ms", "measures": [{"label": "Booting", "start": 1750986201.725621, "relative_start": 0, "end": **********.138275, "relative_end": **********.138275, "duration": 0.41265392303466797, "duration_str": "413ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.138284, "relative_start": 0.41266298294067383, "end": **********.201127, "relative_end": 9.5367431640625e-07, "duration": 0.06284308433532715, "duration_str": "62.84ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00335, "accumulated_duration_str": "3.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.17303, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.955}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.185725, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.955, "width_percent": 15.821}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.19325, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.776, "width_percent": 15.224}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-968346421 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-968346421\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-968346848 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-968346848\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1101417639 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1101417639\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1974165183 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986199489%7C73%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InYvaXArcHk0S0xHdGpDMVRlWWRCL1E9PSIsInZhbHVlIjoidENod09NRGk3Y3JzOXdTTVhFNHFlMkFLd09BMlRFM0FuR3hBTURPSkMyWUQ5YnZuNmJ1NVI4aS9yRzk2UmRBb0NGeGJ5QzJ6cEZLQTVqTndTbzFSZEdLbGI0a0pYUUZwLy8zVVdrYWJFZUJmbEhaYVdHZWNXaE1naUFSdnROV0xtMHJidzJ6NTVvQ0tvUmJwY3o4UGtVbjMxSHc3dGxtbnk4bGZ5NHk5TDBGVDkrZFo4N2plV1dzUkc0QXlCck12YVo4am1vY210WEliMEd0YW1iRHpwampwZWdOdWlyUXZXT1ViMGxGQTBVbmp1NjVzT0tHcURtcGhMVFp0blR5U1VpcUpINFozTkNmbElFakt4dVdRdzZtSGRuZzBBU1llVkprUzcxUEU3YTUvOVFkY2JDcS9KRW1xU2VOK094RnJHSGVvM1pSZ0xGMTRWUDNEU3h5QUNTOG15R3BlaDVwbHZCQnlxa20xOGgrNnJBZTArazQ5N3VXYnpPTkxPbGd5cStZdnc5WExsWVlBUFN0QUNVbktuR1lxNGsrT1VNSkFtNHkwMFZONnIzSnd3ZWdYR2l0cmNGeE0zNC9wd0JCaWVCbVZsR2ErOGN6N3htaHZIRWRMb0JoSGZubUwrUEYrYTBWczVxLzlNcHVBQWt0STMvVVhTUmxNdktqS1lZOXgiLCJtYWMiOiJjODNhNmVlNGI1MWIwNmJiNDE5YmNmMzNhZDUwNTZhZDcwZjRkYTNmMDE4MzVhYTNhYmJmZjYyYjJhNDRjYzUxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdOckVMeUxoU1htZytiMWFJZGVadEE9PSIsInZhbHVlIjoibmlEOWRlZnlKaGFBQ1RxR1EzVFlPcmhKZ2V2VEFDM05qK2l4OUJUb1hGNkVBL09DT0NaTnBKT2laOWlRUnRWVll2MGkwcTczN3gwNEh0cXdINUY0WThQcGZNaWtGdGxucERReU8veG1hNzhpb1BtSnhYMXJQdWdaS0RvWjdicUhvR21zSytEWTExTDl0aFc5dS9VOGxiUlp0eS9qRXFOTkRaWDZWTDA0U29UY3VwUnhwckRNa3poWGFQY3dWRjY5WS9HeVRhdTVkenJodFZuMUQwT0duT1hCZFZjVUgySFpFbXBERjNsdzZpeis3V0tmOWtaU0NDbWZrVzFhMVJScmdVaDEwTDh6RUpJZHFOVlA4cFNCVzBIc0svVXNJNEZKNHNTS29MOVNmM1ZjRjN0bHk1WFZPR25PcHdTQkRNK2FCb1c4dXlncVJMZ2YxbDRzWkdtdzRGR1MvYkhXdHdhdXpwMWwxdEJxRUd0MDc3ZjB3QzhJazR3amZEREwycDlCWnpCZzJJM1h3a2NaZnA0ckk1ZmRweWVLZTRxc24wVkpsZGRJWk43TXRUdVcwWUNjTkJHNUxEcHUxYkgwZVh3QVBOcWVOYzgvcFVjUWkwTm9SRDdZQkFCb1VaaWNpcWJnWm1VcjhuRktOcmgwN0x2UDBiOHpYbTJKZDNOYzR5NWMiLCJtYWMiOiJlZmEyMDhkNmFhNGUzZjIwNTVlNjQ1MDY0MWE2YjIzOGQ2YjM2MjA4NGYxMTQzMzY3OWU4ZWY0ZDVlMDJkODM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1974165183\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1672675369 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672675369\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-432142857 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:03:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhnWkQ0dkZXODJjc1BDNGVpeVVtZHc9PSIsInZhbHVlIjoibXRnL2cvSG1MQ1NiN0s0SWNHZjhWVFB3QWF0WEdaQmVSMjB1Qmk3RFppYVdBdDBOSnNIdnZvMkk3SUxUc1RMSmlrcVdWWkF4UUJRblFzT21rQ3U4TE1OZUtNM1A3VWRIc2RoRWd1NHRJQk1MNHI1SUVGTU9EUWc1bTFLK1llVm9PSWhLNldRaW5TZWJmUlNVblRHRE81L3B3ekFmSU1YdnhFTzV4NGhBcmtlSm5QYkNEUktEWStGSEM5MUVEMHYvd2tzcEhtVmt0SW9lSjIyZFR3d0huY0h3azJ5THZJTHFkdTJPV0NCalVmMVdrSk1uVkZhM2ZaUnVhRFNvTmwxaG9RQTRqVjJMWVBuaWJ2T3F0VjZJTnBVTk53cytYZ0ZUTGp0T1ZPSVdYNCtvMjBEU2dnV1huM2xEOFNtSUNFc3lNY2htZUxOVzlVVGUweFpTQjFsd2ZuSW5UUjFmem5SYjNuUzFveTgwTjBzckgyWDFsbGVlMHc3OC9YMCtiMUowSDdPRnhrczBvcjZWOXJMSVI4TlhhK2c1VVE2bGZPditud3J2c3htaSsyd0sxUWxDeUZ2U3hxZnU3V05raDR2NUYreDNsdXdLVG50aWdnWkFRVThlVEpkSEUxVVFncDMrZ3NYMnRwQnpQQkkxYVMzdTRoMzBMVEN3dW5DWmNxMmciLCJtYWMiOiI4NzU0MDE2YjIxNTY0YTQ0YzNiNzFjZDcxMDg3ZDFjZTUzYWU5MDczZjc4YmU5Nzg4OTljMGI3ZmNjNjIxMWRhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:03:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJTQkpCT1VnY01rUDk1TlgvM2xnb2c9PSIsInZhbHVlIjoiV2w4L2p2ZGxqWkxSZmtseFVERGtzUHErNE9GRW4wZ0NwSHZSRXhjT2svQ1NGdnQwcUt2eFdxUnJRMDFveUhaY0IrVHJkT3liNDJtU25tVmM0dERseVFDV1IvdGpCUng0SkxpNEErZG5lZkNoTnpteFRPMGFPMzNtNkFQMzBnNmpxaWhJVjR5ZjZEWHFPblBIVUtESW1aVU5JZnZJV3hGaGtrRUpNNENaNXp2elMzc3Y2S3JkQW1aSkYvRGR0SHd1M2xtc2t2Ui9iVEh4UVFza1JNSW5OVGYyblp0VVpESjU3N1BFNDB2MEl0Y2I0ZzRmTUpOcmxEWkxBTGRuTS81WGxuV2hITUxlV0hYaHNORDNmUTVhLzQwd2ZMRCtxcklGYWNCY1MrUkJseHhiK01XaHdaUWU0ZmlaNTdVbmRrdnpWTjZKSUhpWUlYN2lHOXFsK0Y4aWFYazJSV1BJSExRbWFkc2ozYXhMNEc1S3FpbjBhM05RL1l3VFVWOGlmMmNLaHQ2eCtJdDhUaGxZVTZucUpMcWNFWExQRUh3Um1XTXhQbzgyNGdJbk9xU2hlRTNaUFlxMDZIWnc3ZGIzbU9GKzRrVmx5SzhhaW5XZHZBTVFnNytYNUo1T2pMelR1UEVOOWw1NUU2VHFBWXNRdU1NSjQrdEU5bHNWWTRaMHpSRVYiLCJtYWMiOiJhOGQwOWNhMTFjYzgyNmJlNWUyODAxY2VmMjM3M2VkNTJkZDA1NWMwMWE1NjRmNWUxMGNlN2MzMjJlNGYyMWI5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:03:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhnWkQ0dkZXODJjc1BDNGVpeVVtZHc9PSIsInZhbHVlIjoibXRnL2cvSG1MQ1NiN0s0SWNHZjhWVFB3QWF0WEdaQmVSMjB1Qmk3RFppYVdBdDBOSnNIdnZvMkk3SUxUc1RMSmlrcVdWWkF4UUJRblFzT21rQ3U4TE1OZUtNM1A3VWRIc2RoRWd1NHRJQk1MNHI1SUVGTU9EUWc1bTFLK1llVm9PSWhLNldRaW5TZWJmUlNVblRHRE81L3B3ekFmSU1YdnhFTzV4NGhBcmtlSm5QYkNEUktEWStGSEM5MUVEMHYvd2tzcEhtVmt0SW9lSjIyZFR3d0huY0h3azJ5THZJTHFkdTJPV0NCalVmMVdrSk1uVkZhM2ZaUnVhRFNvTmwxaG9RQTRqVjJMWVBuaWJ2T3F0VjZJTnBVTk53cytYZ0ZUTGp0T1ZPSVdYNCtvMjBEU2dnV1huM2xEOFNtSUNFc3lNY2htZUxOVzlVVGUweFpTQjFsd2ZuSW5UUjFmem5SYjNuUzFveTgwTjBzckgyWDFsbGVlMHc3OC9YMCtiMUowSDdPRnhrczBvcjZWOXJMSVI4TlhhK2c1VVE2bGZPditud3J2c3htaSsyd0sxUWxDeUZ2U3hxZnU3V05raDR2NUYreDNsdXdLVG50aWdnWkFRVThlVEpkSEUxVVFncDMrZ3NYMnRwQnpQQkkxYVMzdTRoMzBMVEN3dW5DWmNxMmciLCJtYWMiOiI4NzU0MDE2YjIxNTY0YTQ0YzNiNzFjZDcxMDg3ZDFjZTUzYWU5MDczZjc4YmU5Nzg4OTljMGI3ZmNjNjIxMWRhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:03:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJTQkpCT1VnY01rUDk1TlgvM2xnb2c9PSIsInZhbHVlIjoiV2w4L2p2ZGxqWkxSZmtseFVERGtzUHErNE9GRW4wZ0NwSHZSRXhjT2svQ1NGdnQwcUt2eFdxUnJRMDFveUhaY0IrVHJkT3liNDJtU25tVmM0dERseVFDV1IvdGpCUng0SkxpNEErZG5lZkNoTnpteFRPMGFPMzNtNkFQMzBnNmpxaWhJVjR5ZjZEWHFPblBIVUtESW1aVU5JZnZJV3hGaGtrRUpNNENaNXp2elMzc3Y2S3JkQW1aSkYvRGR0SHd1M2xtc2t2Ui9iVEh4UVFza1JNSW5OVGYyblp0VVpESjU3N1BFNDB2MEl0Y2I0ZzRmTUpOcmxEWkxBTGRuTS81WGxuV2hITUxlV0hYaHNORDNmUTVhLzQwd2ZMRCtxcklGYWNCY1MrUkJseHhiK01XaHdaUWU0ZmlaNTdVbmRrdnpWTjZKSUhpWUlYN2lHOXFsK0Y4aWFYazJSV1BJSExRbWFkc2ozYXhMNEc1S3FpbjBhM05RL1l3VFVWOGlmMmNLaHQ2eCtJdDhUaGxZVTZucUpMcWNFWExQRUh3Um1XTXhQbzgyNGdJbk9xU2hlRTNaUFlxMDZIWnc3ZGIzbU9GKzRrVmx5SzhhaW5XZHZBTVFnNytYNUo1T2pMelR1UEVOOWw1NUU2VHFBWXNRdU1NSjQrdEU5bHNWWTRaMHpSRVYiLCJtYWMiOiJhOGQwOWNhMTFjYzgyNmJlNWUyODAxY2VmMjM3M2VkNTJkZDA1NWMwMWE1NjRmNWUxMGNlN2MzMjJlNGYyMWI5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:03:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-432142857\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2092156217 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2092156217\", {\"maxDepth\":0})</script>\n"}}