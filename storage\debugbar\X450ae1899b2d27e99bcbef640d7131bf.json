{"__meta": {"id": "X450ae1899b2d27e99bcbef640d7131bf", "datetime": "2025-06-27 00:22:58", "utime": 1750983778.002869, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.518126, "end": 1750983778.002883, "duration": 0.4847569465637207, "duration_str": "485ms", "measures": [{"label": "Booting", "start": **********.518126, "relative_start": 0, "end": **********.926284, "relative_end": **********.926284, "duration": 0.4081580638885498, "duration_str": "408ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.926294, "relative_start": 0.40816807746887207, "end": 1750983778.002885, "relative_end": 2.1457672119140625e-06, "duration": 0.07659101486206055, "duration_str": "76.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023219999999999998, "accumulated_duration_str": "23.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.958566, "duration": 0.0223, "duration_str": "22.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.038}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.989784, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.038, "width_percent": 1.637}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.995764, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.674, "width_percent": 2.326}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IjB3aUYxS0ZxQU1LWXVzS2R4VU1rMlE9PSIsInZhbHVlIjoiV2lsL1lPQkxnYXFIa3FvQ0ZLNUk2QT09IiwibWFjIjoiNmFhOWU4ZDM1ZmZhMDRhMmZjN2Q5ZDNiMmFiMGJkNTUyNzQwNDFmNGE0OWVhYzIxODFhZWVmYjljYWJjYWYyYiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-108791319 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-108791319\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1768754156 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1768754156\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1056589480 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1056589480\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1601789844 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IjB3aUYxS0ZxQU1LWXVzS2R4VU1rMlE9PSIsInZhbHVlIjoiV2lsL1lPQkxnYXFIa3FvQ0ZLNUk2QT09IiwibWFjIjoiNmFhOWU4ZDM1ZmZhMDRhMmZjN2Q5ZDNiMmFiMGJkNTUyNzQwNDFmNGE0OWVhYzIxODFhZWVmYjljYWJjYWYyYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983511681%7C51%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJxdGdsNmZaN1ZyVE1nclYwUjlmNUE9PSIsInZhbHVlIjoiL25YS3hNZ3ZISVo3UlE1ZWVaODBtc25uSVR2OFVRdHM0Q29JUWlnaHBMTTU3YWJmdXpoMHBWd1FDRElzYVN0aEtURm1pemlYWUVvYUsrN2xlMHpqOWtLQmMxdzlxZUZqVzl3QitnRnQxK25KODNBenBVL0pDK3g0RWE4bUNGVUVXeEJNcUVmTlEyV1ZiaWd4RXZjOHdqTzFFajZuY29DM0t5SU51dVVna3g4YTNpTW1uRFRJbTRMbjA3WVhOZ0ZMeUpLM21rak96RS9EV0ZFWG44d1M5TmtlM256THdMaXpheVRXNlRjNGxDRWQ1UWhnQjVNT2FTSVBsZkt2WmZIbnErV2J0T3E1eEFMUDhuUkN0NEQ0dzBNRTljTlo0VVRiOFRScXlrYzJrTXk5QklKdExGNDRmbUJmTHpDNmp1YThuZGpTdDV3dTdXUlVxUE8wcVYzUGRCTkpUVG8zZjdqQXlaV2dIVnJhc1NjVllUQ1g5cGdxYk84LzFoMnU1YUl4RG1rOXVMbVczQndVWCthcmIvR2FyNXNwR3BtbUtDR09LME9tZ2N4aFFJNVlLeWN3MzNzOWFOYjI0bWVRZWFHbTM4SnU3YS94WEowNmFMaUNTM0J6UGFQUmZwRkViOEtFdmRZcG9xd2E4NzRaeldJMGY0RHU0RG9hQmJOZ1FrTTgiLCJtYWMiOiJmMTc3ODZhMTIzNmYzMTVjYzg0ZTE0Mjk2NmJkM2QyNTI0ZjdmZjQ0YjJiYjQxYjA3NGJlMWE0NTUyNjU5MjM1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZzMmY0eWFnSzFaeHMyQlVtd1RWZVE9PSIsInZhbHVlIjoieHpPQlM3WFFPeEN2MWd6eTVKNHQwM2NXY2dUT1J0UHkxQkhXWERrek1xblRmT0FXSlUyTFZ3dCt2YmhtZ1lJenBYSzB2Y0NJMUpqbzRmei9sVTRCNUREZWpHVUIvdkt4Mk5TTWdvaExaYWljM0c4dXFLS1NGTmYwT29CNGpMV1p5bjhRViticWNWNExQMzVFZEg0MTd6OGMxRnE2d0RJa2Y4aTlLeExSLyttdGtHMG9LWjgrYXFXeU5TTUVBMW5mRFlXd0VWMzNpejh0Qzh0dW02NFNxVXE0dkVCVFpCNUNUUmJZZWdURHh2Z0tqbUo5S3RyZy8yeGhsVWhzbWZzL3FWbHhpckU1TnB4dUFNV01rb3F0WFNkVisvMWJ2bEFKVnVCTWNOTkVqZTlteVpnQW1tWGZLVXYxNGpHa0RQUzZPcWtNRzVrcGpHZHpBMXY4Y2szM0pKUi9DclBhS2ZQQ1BWVHN1MlJDVUUyUU5mQ3FoQ1hmNUplVGRXRHFUN3ArRm5CNFM0R25yUk9yUUFJY2EvczViaFpaWCs2Mm1HL21YVHRRTVp3UFdrUVpEWVduVklQVUs3QWhJUWhjYWIxM2N2aTJGTUErTHgvMXJFQW5tUGZkaTNSalpjd05uUVFndkVqanZQT01JWUF4VGRXNVVOdk8zTERCNmdFVUE3UjYiLCJtYWMiOiI3MjBmMmQyZWUyODI2M2UwYTI0NWU2ZjlhMTU4MDY1MjI1ZGI4YWI4YTEwYmQyYWM3MTFiYzg5ODA2YmE0NmY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1601789844\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1759659301 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1759659301\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2115494949 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:22:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImIwSjlGZzhsTVp3TmlaQWwvc2dXREE9PSIsInZhbHVlIjoiZWVXNDZpd3VPM1hFY01scVZsYlJWa1g0ZFFCQmg2aWlheTl4VHFxZ3R6Wm1HdmtqK1IxdmFMYThXY05yZHdXVjFCdENaeTg3MWZQZk5mSVlJWWYxcEU1bjhEOWY1NVc3KzlreWQ0U2RsV09IdGpnNTh3V2pES1pjVFJ2cVZ2Vkt3VC9Zd1BmNG1ZSkRtSE1kSElVRFV0c0prdFZLT29IUG9nRnRPQW5vZURYTVQxNUJPM0hzNDA0WEE5SVpkdXZQVVQ2L1VVcFZUVzJvU1NpczZIMG9ydkowMHVHd3pyN2xkRmJMMm1wb2Y4cmFDU0VkQ1JUb1pXVXFpdmhHRjRZR0NoZlZLcUQ3VjlmL1MrOENNb1NpZTJCekRXVmY3ZDViM1hNSnBvK3JaUVVzRDhRTnVOMThmYmNSOEFINVJoaDVFRUYwVUJ3M3h5WDZkOGJnRHk2VFNFL1pxMzhXL0pSd2lERUN3NXQza05mbWtqazJ5SE9WZ3NpUmFxTXdHVGM1cFRxODIzSGk5WXkyWXNobEF0dWxxNmRFNUZsci9BL3A5OXlSOG1PcXExckJKYW1KZXpqWTJNL1ZRM3ArRlBRZjJWWXEvNjZ2M2tyRnNBaXYwZXRrK3NObU9JU2NRMWRlRS83aTJYbzVvT1hUcVZFWVowb2lzbnpWOG5nT25RZ3IiLCJtYWMiOiIyNDk3NjQzZGU4MDY0YjU3ZDNmNTgxZjZhNzliMmQ3OTg3ODc4Y2M5ZWFkNTRiODMzMWE1ZmI0ODY4OThkODczIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:22:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjhHbHFGYnNFdDAyMW5aVkFIc3BuRlE9PSIsInZhbHVlIjoiT0RtTklvbzlLbDFQTkc1bzNHR1QyL2p2QnlHWTZtaG9xanpNWkgxZk91R1ZxSmdNNTlDcm5IalBuaWJ1cTNnTTh1a3pkdFpzSmhEbE9uZk1PNEFTWG9xT1Rrc2k5ZGl2RnVLWEkwbXpiTUVhZnZBSHZlVGk1QTVXYU9zQ3VHVXErWUpjdThQMmUxbFExUHl2eEF5SzlUSG9qRzlSUXdBWG53eHIvY28xMXp3alJzcnZLanlUVEJkdEcrMFMyQUpFRElXbkh1MkVtYlBqeXoyby9YcENnaHVDT2o4dHZsejUvSEJKYVhZTkZudHRaVmxlZlMrdHRYdXdiblRmVThRdmlUVVB6TzZJRHZWTlZZa0hTVEtIZGhUTEUrRzFhRjZFcjVwMFJiQ2JycHJuYlJtQ1kzQVJRSWJNRk5GSk0rNUsxSHRUaHRxRERYTlNDSDFjNHlFSDNqcjRYK1hMczBQL1dEczNUTytka2ZTWWd6bEU1UllVemVuM2F5RktncFI1bU5HdXFYdnlnZTgxUWlGVjJFTVdYbWRERDRtL0FneVZrS3VwWlNnNVprVWM3VGQvdFpPSEZXdDdwdXcwT0tCa0x0ZlpBbTVIdU11YkNNckZTUjZGNVlVZEhCMDBUQ2F6Ujl5b3ovdDd5TzUwNVorQVR5OVVmT3NtbUYxTTczdjgiLCJtYWMiOiIyMDI2NWQ2NGUzMDVjNjVlZWY2ZGJkNzliMjRmYWZjNWY0ZjU2NGRkZTZhOThiY2RhMmE0YWI2M2E0NGUwZDUwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:22:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImIwSjlGZzhsTVp3TmlaQWwvc2dXREE9PSIsInZhbHVlIjoiZWVXNDZpd3VPM1hFY01scVZsYlJWa1g0ZFFCQmg2aWlheTl4VHFxZ3R6Wm1HdmtqK1IxdmFMYThXY05yZHdXVjFCdENaeTg3MWZQZk5mSVlJWWYxcEU1bjhEOWY1NVc3KzlreWQ0U2RsV09IdGpnNTh3V2pES1pjVFJ2cVZ2Vkt3VC9Zd1BmNG1ZSkRtSE1kSElVRFV0c0prdFZLT29IUG9nRnRPQW5vZURYTVQxNUJPM0hzNDA0WEE5SVpkdXZQVVQ2L1VVcFZUVzJvU1NpczZIMG9ydkowMHVHd3pyN2xkRmJMMm1wb2Y4cmFDU0VkQ1JUb1pXVXFpdmhHRjRZR0NoZlZLcUQ3VjlmL1MrOENNb1NpZTJCekRXVmY3ZDViM1hNSnBvK3JaUVVzRDhRTnVOMThmYmNSOEFINVJoaDVFRUYwVUJ3M3h5WDZkOGJnRHk2VFNFL1pxMzhXL0pSd2lERUN3NXQza05mbWtqazJ5SE9WZ3NpUmFxTXdHVGM1cFRxODIzSGk5WXkyWXNobEF0dWxxNmRFNUZsci9BL3A5OXlSOG1PcXExckJKYW1KZXpqWTJNL1ZRM3ArRlBRZjJWWXEvNjZ2M2tyRnNBaXYwZXRrK3NObU9JU2NRMWRlRS83aTJYbzVvT1hUcVZFWVowb2lzbnpWOG5nT25RZ3IiLCJtYWMiOiIyNDk3NjQzZGU4MDY0YjU3ZDNmNTgxZjZhNzliMmQ3OTg3ODc4Y2M5ZWFkNTRiODMzMWE1ZmI0ODY4OThkODczIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:22:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjhHbHFGYnNFdDAyMW5aVkFIc3BuRlE9PSIsInZhbHVlIjoiT0RtTklvbzlLbDFQTkc1bzNHR1QyL2p2QnlHWTZtaG9xanpNWkgxZk91R1ZxSmdNNTlDcm5IalBuaWJ1cTNnTTh1a3pkdFpzSmhEbE9uZk1PNEFTWG9xT1Rrc2k5ZGl2RnVLWEkwbXpiTUVhZnZBSHZlVGk1QTVXYU9zQ3VHVXErWUpjdThQMmUxbFExUHl2eEF5SzlUSG9qRzlSUXdBWG53eHIvY28xMXp3alJzcnZLanlUVEJkdEcrMFMyQUpFRElXbkh1MkVtYlBqeXoyby9YcENnaHVDT2o4dHZsejUvSEJKYVhZTkZudHRaVmxlZlMrdHRYdXdiblRmVThRdmlUVVB6TzZJRHZWTlZZa0hTVEtIZGhUTEUrRzFhRjZFcjVwMFJiQ2JycHJuYlJtQ1kzQVJRSWJNRk5GSk0rNUsxSHRUaHRxRERYTlNDSDFjNHlFSDNqcjRYK1hMczBQL1dEczNUTytka2ZTWWd6bEU1UllVemVuM2F5RktncFI1bU5HdXFYdnlnZTgxUWlGVjJFTVdYbWRERDRtL0FneVZrS3VwWlNnNVprVWM3VGQvdFpPSEZXdDdwdXcwT0tCa0x0ZlpBbTVIdU11YkNNckZTUjZGNVlVZEhCMDBUQ2F6Ujl5b3ovdDd5TzUwNVorQVR5OVVmT3NtbUYxTTczdjgiLCJtYWMiOiIyMDI2NWQ2NGUzMDVjNjVlZWY2ZGJkNzliMjRmYWZjNWY0ZjU2NGRkZTZhOThiY2RhMmE0YWI2M2E0NGUwZDUwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:22:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115494949\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1422347607 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IjB3aUYxS0ZxQU1LWXVzS2R4VU1rMlE9PSIsInZhbHVlIjoiV2lsL1lPQkxnYXFIa3FvQ0ZLNUk2QT09IiwibWFjIjoiNmFhOWU4ZDM1ZmZhMDRhMmZjN2Q5ZDNiMmFiMGJkNTUyNzQwNDFmNGE0OWVhYzIxODFhZWVmYjljYWJjYWYyYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1422347607\", {\"maxDepth\":0})</script>\n"}}