{"__meta": {"id": "X9b9892b7db64dafa3e8445c8abfb0b04", "datetime": "2025-06-27 02:12:25", "utime": **********.81678, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.376397, "end": **********.816797, "duration": 0.4404001235961914, "duration_str": "440ms", "measures": [{"label": "Booting", "start": **********.376397, "relative_start": 0, "end": **********.747174, "relative_end": **********.747174, "duration": 0.3707771301269531, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.747183, "relative_start": 0.370786190032959, "end": **********.816799, "relative_end": 1.9073486328125e-06, "duration": 0.06961584091186523, "duration_str": "69.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45394408, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01417, "accumulated_duration_str": "14.17ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.781294, "duration": 0.01317, "duration_str": "13.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.943}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8064709, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.943, "width_percent": 3.811}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.809395, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 96.754, "width_percent": 3.246}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1888137889 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1888137889\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1143556462 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1143556462\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-236091959 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-236091959\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-690692978 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990342712%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFUTERlSC8xZ1JIdVZDV1RuLzIyMlE9PSIsInZhbHVlIjoiWHAvZ3RMaDlsV0dPSHVFOWcvdk5BZjB2eVNhMk03alpMdXB3K3ZidVV3MzFyWUxFNlUxOFJTNHJHSStvM2lrQUJ1VXJ2d2FGSHV6M3VEcnNqMGZKZHdDcXpuejI4REh2V0lSMmg4MXhpSGljeGMxcGJzd1FEdEFjUkRlYmZYWSs2OVhpSUVEdGJ1NklxZFVkRmJzSkkrVDgySTROOC9lYnpkQXAwSHJNdklwNHBobEdHcURBdkRiSm5qbGVyYTN5bloxWWpYNjU4dGhVWVgrM3RnL0xXNUdORXB1Tkhrc0x5M2E4Zjk5TXhzNmFNellWVENSS1NZNmxIb3E0UTl1eWU2V0V4SGNVQmJrKzlsNGZ5S09aTGVXQThkZTJyTTAyZDMzNEVQUE40VE51cU9oQUcwYjdjYk0yN1Q0dTduT3hSLzI5VHdQbW9oUGhQb0pYWlRzb2VyRFNCYjNnYiszd1pBRlRHZWdZWjJKTE16YkJublZ6enE4cnVid2ZtZEpMRGZWbmtqeERTN1dDcU1NZVJSa3NFU0JjTnRUR01ZWHpEa3lRV2dMTm5RRFV3SVR1QUdJb2VRM1A0WHljUlBuMG14TGhUdEJyaDA1c3JlLzV1a3UzWjlRNnNURFNwRDhnTGxxRDQ0dnBDUFhja0NLdW13WmNaU05FbEdmWHh0dysiLCJtYWMiOiI5ZTc3YzU5NmU2NmI4NDUxNjY3ZWZkZjQ2ZGE5NzBmMWUzODY3ZDU4MDA3ZTBhZjY0YWNjZTE3N2I3Njc1ODkwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlA3TGk3OFMzQzlkSjVQN2VrS0oxZnc9PSIsInZhbHVlIjoiZFg4M3IxQzFYSnNab0hDYWptUEV4cmZTSXkwODMzMjBGUkxQN2IzaWl3M3poTTNUdnN1TVg3TnEvTU9EUTVjSDB0NTQraDZRd21IK0ZkQlZoWjhUenZNV2RwN0FVL1pJNkxWYmlBcHlvTGhqOEdKT1cwM0ZnQnd1dEc3dXVzMXFsTEU0TEJ1N1AwakRicUYrWCttaWk3aENsSHQrZXcrSlZydWhMZGFaR2NIQWRRYjVyN1lBQW9RTzlIQTFhbVBXbTZVZE5rRGtIeUZuL3Jnb3BjSjd3RmdQMHM3dkZ3aWtkKzNYV24yVUtMQ0hnTy82cE5ZRCswRFlDbURjMGFGdmhIYlYwcFlHZXJycWpqdnVkbTNCblVIN3YwV0I5aTYrSFc2cU43YVo3UldOenAvR0pHMXlhTTh0NGpWbldzMyszaDhEVUtBZlNvZzc4bjVQZzhNNVZxM21pZDRKQlh6OEF5SGVJTVpXSjRWd3htdEdWcnl6UUdndHIzcVNBYkFpcVNiMzViVVhXTTlJTGllc2ROWm5ORWIvbEwzL0tUMWE1K01lZ3JVQ0c3ZUNLZ2JibzZSaWZyc1ViTmVuVXBTWnNIcUZTTTc2MVJ6L2lOQmloak1WRUxtT1lIYWRsY1MrcFV4TjNhemRXZmY2Tk1Ea1g0N0hkTkl3RjJ0d1JsRDgiLCJtYWMiOiIyNWFhZmMxYTA1YWU5ZGUxNzYyMmRhYTQzMjkxMjk1NWY3NjA2YjQyODQ0M2EyMzZiY2U5ZTZlZDg2NjUzOGI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-690692978\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-627600720 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-627600720\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-819533967 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1ONXRVR1NSVEtUQnBGNE5NMHpqdFE9PSIsInZhbHVlIjoib25XaEtVT2F3RnJZR09FallKL3BMRUlubWs3aFdDK1dGTk84cmp6bjhNNFpQcFZqaml2QUVjNFMxY0FpbmNuN3poeDJwTmJHQ0ZSdUlXYUtBbm5ES3d2NVJub1lTa0pLQzc0bTB6ZFlRQTVOUklMamNHdmhJb1hsQ1BoLzlhNkdhKzFSeEFEOWVhdExtVXF0bnY0ZWM3V2pNMEJ6SGEzUUs1amZXNmgxMVJkNldIZ3lzV3B4eTVmL1lndi84NkFOMHFNeTdSTmswa1MyeFkyMm5zY1RaSVc5TFR4WXlRa09CV1FzTWxXL0pTTGRwTVQwL29qMWVXK3ltV0gvOGRNVE9RRHBmSFcvVFVYNis2eEZSZWcvUXRNYWZLWHZrSDZzWVFkeThXcjFOaDRxNXRDL096bU52Tnp1bUR4RTlPbURhRVhGQVhYTjZYRWU1dkV0UzluYkFMSTUydHVudWIxNDh2eHc0RTJPWGZPWmJ1cW9tLzZrTFBOdUk4YU54SjBkZCtpWWo2Mi9CcExaNGpyWEtpSUVsTGs5d2x3bDV1aEV1cjdoWU84OWh0WWtSVGlodEMzWlpKbU5WQjVKbmNKMGYzaDB0UE92OXh1dEk1WkxuYWFnWlE3bkM4VDl3a2p4a2M2bTF2YzRQbk53V1JWbGNCS0RkNTRJSitmSEhmTU8iLCJtYWMiOiJkODdhZTMyM2NkZDI0Yjc3N2M5NTdiNDFhNGY0YzYxYjdjMDliY2E4MjZjYTQ2MWI1YjEzYzc0M2VkNTBlZjkxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImUyV3Y3VnNEL1NMclZWS1NSVXZDMFE9PSIsInZhbHVlIjoiWmJxOGRYOEhwdFZCQlg1bVJWUzk1bzZhM3dMdm44QVVPcjZSQk5jdE5KWUtGQ1o5aHNyaDNKajY3cGxGRXVMRU9qZFByVW54UkIrNDhXS3FkeGpvY0tkZkVSMGxIVmpudnE0clRBWkxiemJkaGJ2ZExIUzRwMnc1TU90L201YVlNbWpKT3dmY0pxVlMyM0lzZ0xsQlZFMnAxU3hBSzRIdUJWWmcvY0xnaENGL0QrQWFicjdNU0YxektpakFBdUtickhoaGdNMWZxd1h5bkFsQWdTdTNKZjRpUS8xUEUxRW1SMWFaSWlnMVNmRmZUVTNiMnNKbFh0c0cxMG9NeVQxSGVnc2NsZmlneGRMSW1LbkNUYjFFSGVwV2lDdWhpaU4vbFE1NEI1Wkw1MXFsMkRmNk54NGpCQnI2c2w3NHRvcHpOajd6OW4vRnRBYkVUcUd1ekNxTzZ3cFEzTExNT3oyRGc2TTUrZFJZb3dGbEI5RzhnWlAxZEpVNmpjcWhxWUdaUWRGaGV1Y0JkNGJ6SnJpaWo4QVY3WVhFb3ZGYm1NNTNPUlRCTUxiWnU0U2YrRTU3akw4OExMaTBISVpRMDVVSDJ2TXRyK0RhSHBxckJoVDBXUUg0QzQwVUJDOXE0aU5UcitMUGNxSytVTGdDUTBnRkd1QnR1cm4xRThJa0Z5d2IiLCJtYWMiOiJlZTE0MTI1ODkxOGNjYWFlMjEzYjBlYzIyM2YzNTVjY2ViMzg0MTg0N2E0MTgxYjFmY2MzZjQ2MzUyMjYzYTMzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1ONXRVR1NSVEtUQnBGNE5NMHpqdFE9PSIsInZhbHVlIjoib25XaEtVT2F3RnJZR09FallKL3BMRUlubWs3aFdDK1dGTk84cmp6bjhNNFpQcFZqaml2QUVjNFMxY0FpbmNuN3poeDJwTmJHQ0ZSdUlXYUtBbm5ES3d2NVJub1lTa0pLQzc0bTB6ZFlRQTVOUklMamNHdmhJb1hsQ1BoLzlhNkdhKzFSeEFEOWVhdExtVXF0bnY0ZWM3V2pNMEJ6SGEzUUs1amZXNmgxMVJkNldIZ3lzV3B4eTVmL1lndi84NkFOMHFNeTdSTmswa1MyeFkyMm5zY1RaSVc5TFR4WXlRa09CV1FzTWxXL0pTTGRwTVQwL29qMWVXK3ltV0gvOGRNVE9RRHBmSFcvVFVYNis2eEZSZWcvUXRNYWZLWHZrSDZzWVFkeThXcjFOaDRxNXRDL096bU52Tnp1bUR4RTlPbURhRVhGQVhYTjZYRWU1dkV0UzluYkFMSTUydHVudWIxNDh2eHc0RTJPWGZPWmJ1cW9tLzZrTFBOdUk4YU54SjBkZCtpWWo2Mi9CcExaNGpyWEtpSUVsTGs5d2x3bDV1aEV1cjdoWU84OWh0WWtSVGlodEMzWlpKbU5WQjVKbmNKMGYzaDB0UE92OXh1dEk1WkxuYWFnWlE3bkM4VDl3a2p4a2M2bTF2YzRQbk53V1JWbGNCS0RkNTRJSitmSEhmTU8iLCJtYWMiOiJkODdhZTMyM2NkZDI0Yjc3N2M5NTdiNDFhNGY0YzYxYjdjMDliY2E4MjZjYTQ2MWI1YjEzYzc0M2VkNTBlZjkxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImUyV3Y3VnNEL1NMclZWS1NSVXZDMFE9PSIsInZhbHVlIjoiWmJxOGRYOEhwdFZCQlg1bVJWUzk1bzZhM3dMdm44QVVPcjZSQk5jdE5KWUtGQ1o5aHNyaDNKajY3cGxGRXVMRU9qZFByVW54UkIrNDhXS3FkeGpvY0tkZkVSMGxIVmpudnE0clRBWkxiemJkaGJ2ZExIUzRwMnc1TU90L201YVlNbWpKT3dmY0pxVlMyM0lzZ0xsQlZFMnAxU3hBSzRIdUJWWmcvY0xnaENGL0QrQWFicjdNU0YxektpakFBdUtickhoaGdNMWZxd1h5bkFsQWdTdTNKZjRpUS8xUEUxRW1SMWFaSWlnMVNmRmZUVTNiMnNKbFh0c0cxMG9NeVQxSGVnc2NsZmlneGRMSW1LbkNUYjFFSGVwV2lDdWhpaU4vbFE1NEI1Wkw1MXFsMkRmNk54NGpCQnI2c2w3NHRvcHpOajd6OW4vRnRBYkVUcUd1ekNxTzZ3cFEzTExNT3oyRGc2TTUrZFJZb3dGbEI5RzhnWlAxZEpVNmpjcWhxWUdaUWRGaGV1Y0JkNGJ6SnJpaWo4QVY3WVhFb3ZGYm1NNTNPUlRCTUxiWnU0U2YrRTU3akw4OExMaTBISVpRMDVVSDJ2TXRyK0RhSHBxckJoVDBXUUg0QzQwVUJDOXE0aU5UcitMUGNxSytVTGdDUTBnRkd1QnR1cm4xRThJa0Z5d2IiLCJtYWMiOiJlZTE0MTI1ODkxOGNjYWFlMjEzYjBlYzIyM2YzNTVjY2ViMzg0MTg0N2E0MTgxYjFmY2MzZjQ2MzUyMjYzYTMzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-819533967\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1404529790 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1404529790\", {\"maxDepth\":0})</script>\n"}}