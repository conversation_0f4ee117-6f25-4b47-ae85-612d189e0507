{"__meta": {"id": "X5e88cb1c95d14dd4e6c44bfc85848b91", "datetime": "2025-06-27 01:04:02", "utime": **********.048482, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750986241.630162, "end": **********.048497, "duration": 0.4183349609375, "duration_str": "418ms", "measures": [{"label": "Booting", "start": 1750986241.630162, "relative_start": 0, "end": 1750986241.997104, "relative_end": 1750986241.997104, "duration": 0.3669419288635254, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750986241.997115, "relative_start": 0.36695289611816406, "end": **********.048499, "relative_end": 2.1457672119140625e-06, "duration": 0.05138421058654785, "duration_str": "51.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043928, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0028799999999999997, "accumulated_duration_str": "2.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0264292, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.181}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.036451, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.181, "width_percent": 17.014}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.041909, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.194, "width_percent": 11.806}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1032427306 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1032427306\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-673239047 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-673239047\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-512185561 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-512185561\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-255888109 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986235075%7C75%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpLNENmMWYwMXBlRk0wVFJqZnFWTmc9PSIsInZhbHVlIjoiRkJrR0NCRDhKTXUzQjFNd0xtUHlyZ1BQSmJDRzVJSWlJTzNLdmZwcmZZVlpNOHArcU9MaXI4aE9abXBaS1pQalVYZnNRNWt2aDl1MlRNN2NQQVVyWXk3dmR5TWYwWTZ6OWVCTE5BajFvQlo1UkpBb3NpZS9MTENrUnlHL1VCayt0cm1xRXd2MFBNa0F4bXVwNks4c3E0bjd6RFhvcDFSd1ZlalR5UW5TclJ6T0diUDhpQW1jV1ZMc0x1UW5veUQyTU1kK2liWGtkS0JUc2dCQ2ZaVlN5YjRoTUt6TVdsOWIzS0JQQWRyN2VIbGJ6WE9XbTFZQXp1MlNFMDl1TDB3Qk5XQmVoN0gyUDNQMjEyZzdHQy9QaGVQdkU1TmY1Q3dqcEE2M1o0YXowUVRCYVJjeCs0clNZRXRqUmFOaXVibVRsMTVNa1ZoVkF6Zlk2aUh5SDNiRDhiZXlDV3NRNDV3OFoxS2N6T2x0TC9JSVpiTnFJc3JEdHV2SEJrNkRKVjkrSFE0ZnZ3THhGU2VmWS9qaHFmd3JLQUVtcHhwS3FaaERLMHd0aUN0ZXEweU83cndFdFpqOW91bzN5dWVxekIyUmZKYkVmekhHcyt1dzd2SExFSTN0R0RIRmdYelVvaS9PRTFEQ08vSFJIMlZLdCtxUDBoZDIvRFUvMWZZQUgvemYiLCJtYWMiOiI2MThjZjg2YTAyNzU2OWE5MzNjMzM4OTNjNzhkODQyYjg5ZjFkODBkNWU5YTAyMTM2NmRhNDA5MDUyZGZhMjQ5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InVXRk01VXo1bDFhbTcvNXBPR1hkV0E9PSIsInZhbHVlIjoieE5iaHlGL0VOR0lsQU40OEtVU25MWFVRSi9KMi9yeTB5LzNMZ2hGcUJWOGJ6STVKenpHY3h1UmdaR0JVNXF1dDI0Q09Tc3E0bVBCbWNFVFQwNStNWDJiVllERWNBTVMrRVVKNlBFanFrUlpTa1NBVVF4eWsvai9JYzFOOUk2Y0VHY3FsL2RvYmNudWtZY1ViNTJFeEJTeHJPclBHK2ZjN0ZaK0FUcU12cWZvWFdXMWhZSXNLQTBvdXRJWTJCOTZNMzZxQXBZcll1a0JkdmZNeDdtNFAvWjc0a2RrZ2NzQmxJUkp0YmtqNW5QclQrQml3b0FZUnA1TVF1ZjJncC9IbU8xQUFrMzE4ejZ6c0pYeFRPeFhnbmZEMFkrcEw2ckJ6SXJEVmF2NkdpMWJBQ2tHa0R6bEc5ekhTa0JqWHk3alU4QlBOK293RHdXZCtocHFINlhWSVROUDkyMU9oeFo1ajFobTJLQUI4d2RDOVB3OXlxeFVkVk5sWTlSNmZuT1owUFR4a08rT29XTkdBZ1N1M1NzY0JqWmZldzlBZlhYbi95dWcydC9ISy80NmZMMVNlMzNXUGVxK1ZpUjEvVDhVMm8wZ3g1UGFrcDFjK1RsekhXU0p1Tk9rY3E5TERMd2ZXWHJ4U3M5Z0hkZG1pcU4xYllFV1RFNEI5bmtSY1F2T2MiLCJtYWMiOiI2MzA2YTc5YWIyZjE4ZjUzYjNmODAyNWE0M2FiMjY0N2FjNzYyZjE2MDJjNjkwNDFlMmNjYzQ3NGViYmJmNzFlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-255888109\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1692420930 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692420930\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1344703764 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:04:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdiRnA5Mmc1S0pLYnBPOTRCczBocGc9PSIsInZhbHVlIjoiVzJTcVpBNmNhUkRGMDdEVEpIN214bllFOGY1NTVCQ2p3SFZVb2xqMEVoRC9INXpSeno3OUZZVkF6TnMwY0VGZDBCOUlkazUxc3lBbVVBV0pkeDcrZURucXhpREtodXBTYWs1cFZLMUF0T1VJTGZCYVROQ1EyWjZxTndaUTh4YlNPSVU0YzR2YUxIYWRhSGtXQ0ZMQXU1SlorcXI5ck9JWldoWE5ueTg0TDI2LzdVMEUzQktZbkdXNXRsK1c5Z3hkUmtPaHA3K0ZEbEJSNUtQUUpiOEQ0akEvaWw1ZHUyYmFoZW9qTmd1M1V3Mkk5Y0JZNlhMbTRtaTdjcTBpSG5IQmZySlFkaWNKb2pIM09pTzh1VXlzNTRYTDJPN0t2TDFkR2JuOE1DdFF0V2ZpZWF3WENMZk04SThIL2U0Y0tqcUlSUDF2UlFsZVIyRmxJUXFHQWtuclVHWThCd1pIQXJjMGpjLy9GRVJ3c2JrUU9QdkRoWVJUeDFHZ2wrTGtjdDRBUDY4RmhGUjVJWHprRmIyVjFhbDZ6S3ZUaHFTZ3RobWtoSmFzN3JLdVpuWFNzMEFVYm9ZaUVlM3NOQ0taVjFxT2JpWjRzeDJad2l3ajZqZS9wRXpHRGo3NFZoVzJ4ZXhPcWhFdFNCbnFqa2RFdU45Vll2anBkdytMa3VqRnBhdFciLCJtYWMiOiIyZDkzNDAyYTM2OWM3ZTNjNzM4NTNhZWU1NTVkNDI0NzBkNzdhZWZmMmVkZGQyOTk4NTk2NGQ0NDI2ZWNhMjJkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:04:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlRjRGFlVVdGVGg0MUNpc1BJRmVRMWc9PSIsInZhbHVlIjoidFJqdnRwcnVPRllJNW4zRVZlNEgvcUtwY1hXRFd1U2F5T0hXeWhUWEtHQWJIWXlETUR0eE5ZaEtDL2VueDUxTEVlczdmY1NpdVJ1UmRac01KOFpJa1hCOVNKMmFjSy9hYjlwem9VUGRKYVZacU44ZFdoeWwvK2MxQjVjem56QitRT1VqVnpNRXFhdGU1MnhMMEl5VE9RNEhsYTYzZEQ2Z3VucldOMjVWYXhVaGpNUGRrc3NmdTFpd2UzZTdnTkdDUTFUL210T2lNYnlPZ2dBb05IVWQySUdCbm0xMWRGNWkwVm4ydmxBeE5WVjUrSHZGOC81NGZ3dWJNTEJvdEFxZXZZZHhibDBBaUlHYXFIT1IzdnMyUUFreE9BSWc1NTFqcE5qOGZJZkduWklWTTB0QnA5N2NhTXd1Mk9SNzByUG85blFOMEhaZXYyd3NkWHMzWHlVV3VHT0xMZjFoYlRRYXhPd1FuckJoOEdDMGt3WHpQQzZsMldsT3JIUGRod3lYcWtsZjJjWThONzJuNTFEenB4NzNSRktQU3h5UG00VUtxKzRDMGJUTVdhemgzN21hSFFvWk9UZXAwNzFmRm9UVjUwV2Q2azVTSTdCQThoc1RydVFCVkZld0VHUExkQU9aTkpXMkk1SUJiRVM5WE41Q2lkcFJKYW4zajV6ZC8zakkiLCJtYWMiOiI2ODQzZTEwZmI1ZDk5Nzc0NTBiNTQ1MDY3NDA1NDMwZWNiOGUxNWIyM2I0ODM3NGFjZDJkODIyN2Q2YzJhYzMyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:04:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdiRnA5Mmc1S0pLYnBPOTRCczBocGc9PSIsInZhbHVlIjoiVzJTcVpBNmNhUkRGMDdEVEpIN214bllFOGY1NTVCQ2p3SFZVb2xqMEVoRC9INXpSeno3OUZZVkF6TnMwY0VGZDBCOUlkazUxc3lBbVVBV0pkeDcrZURucXhpREtodXBTYWs1cFZLMUF0T1VJTGZCYVROQ1EyWjZxTndaUTh4YlNPSVU0YzR2YUxIYWRhSGtXQ0ZMQXU1SlorcXI5ck9JWldoWE5ueTg0TDI2LzdVMEUzQktZbkdXNXRsK1c5Z3hkUmtPaHA3K0ZEbEJSNUtQUUpiOEQ0akEvaWw1ZHUyYmFoZW9qTmd1M1V3Mkk5Y0JZNlhMbTRtaTdjcTBpSG5IQmZySlFkaWNKb2pIM09pTzh1VXlzNTRYTDJPN0t2TDFkR2JuOE1DdFF0V2ZpZWF3WENMZk04SThIL2U0Y0tqcUlSUDF2UlFsZVIyRmxJUXFHQWtuclVHWThCd1pIQXJjMGpjLy9GRVJ3c2JrUU9QdkRoWVJUeDFHZ2wrTGtjdDRBUDY4RmhGUjVJWHprRmIyVjFhbDZ6S3ZUaHFTZ3RobWtoSmFzN3JLdVpuWFNzMEFVYm9ZaUVlM3NOQ0taVjFxT2JpWjRzeDJad2l3ajZqZS9wRXpHRGo3NFZoVzJ4ZXhPcWhFdFNCbnFqa2RFdU45Vll2anBkdytMa3VqRnBhdFciLCJtYWMiOiIyZDkzNDAyYTM2OWM3ZTNjNzM4NTNhZWU1NTVkNDI0NzBkNzdhZWZmMmVkZGQyOTk4NTk2NGQ0NDI2ZWNhMjJkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:04:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlRjRGFlVVdGVGg0MUNpc1BJRmVRMWc9PSIsInZhbHVlIjoidFJqdnRwcnVPRllJNW4zRVZlNEgvcUtwY1hXRFd1U2F5T0hXeWhUWEtHQWJIWXlETUR0eE5ZaEtDL2VueDUxTEVlczdmY1NpdVJ1UmRac01KOFpJa1hCOVNKMmFjSy9hYjlwem9VUGRKYVZacU44ZFdoeWwvK2MxQjVjem56QitRT1VqVnpNRXFhdGU1MnhMMEl5VE9RNEhsYTYzZEQ2Z3VucldOMjVWYXhVaGpNUGRrc3NmdTFpd2UzZTdnTkdDUTFUL210T2lNYnlPZ2dBb05IVWQySUdCbm0xMWRGNWkwVm4ydmxBeE5WVjUrSHZGOC81NGZ3dWJNTEJvdEFxZXZZZHhibDBBaUlHYXFIT1IzdnMyUUFreE9BSWc1NTFqcE5qOGZJZkduWklWTTB0QnA5N2NhTXd1Mk9SNzByUG85blFOMEhaZXYyd3NkWHMzWHlVV3VHT0xMZjFoYlRRYXhPd1FuckJoOEdDMGt3WHpQQzZsMldsT3JIUGRod3lYcWtsZjJjWThONzJuNTFEenB4NzNSRktQU3h5UG00VUtxKzRDMGJUTVdhemgzN21hSFFvWk9UZXAwNzFmRm9UVjUwV2Q2azVTSTdCQThoc1RydVFCVkZld0VHUExkQU9aTkpXMkk1SUJiRVM5WE41Q2lkcFJKYW4zajV6ZC8zakkiLCJtYWMiOiI2ODQzZTEwZmI1ZDk5Nzc0NTBiNTQ1MDY3NDA1NDMwZWNiOGUxNWIyM2I0ODM3NGFjZDJkODIyN2Q2YzJhYzMyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:04:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1344703764\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-796112894 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-796112894\", {\"maxDepth\":0})</script>\n"}}