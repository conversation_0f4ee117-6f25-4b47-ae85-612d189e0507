{"__meta": {"id": "X4ccf3241bad328b038d9ccce60602dac", "datetime": "2025-06-27 02:12:34", "utime": **********.245365, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990353.798984, "end": **********.24538, "duration": 0.4463958740234375, "duration_str": "446ms", "measures": [{"label": "Booting", "start": 1750990353.798984, "relative_start": 0, "end": **********.178933, "relative_end": **********.178933, "duration": 0.37994885444641113, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.178942, "relative_start": 0.379957914352417, "end": **********.245382, "relative_end": 2.1457672119140625e-06, "duration": 0.06644010543823242, "duration_str": "66.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45409120, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016479999999999998, "accumulated_duration_str": "16.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.20927, "duration": 0.015529999999999999, "duration_str": "15.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.235}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2357872, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.235, "width_percent": 3.034}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2389772, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 97.269, "width_percent": 2.731}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1466903266 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1466903266\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-785591627 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-785591627\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1467082711 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467082711\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-792519563 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990349613%7C5%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imx4QVA5ZUphek1vY2VvWVFMcEFsOGc9PSIsInZhbHVlIjoiUlhxNnVPeUt1MDVtQmYyMEgvaFFodWI0TzhybGgwUW9aV2hkSTVJdkl1Z0RBL2xsOUdDeU9DSlYxZmNLMk5aVHdlclR6VnNxSFFsWWVuZG9Oc1BlaCtLNit1emZPb2JIM3VVTWw0K3VZOS9ZVWE4U1R1VmUrZnFZaXYwYmJUQnVROFlyUDYwbm1Ccy9sTEtNakwxNVFrc0NjNVA0czF0OEJGU3NOTWpHTUk5Qk5MVG80dE5zTGt4NVF5Y3pTL204ZFMvZzZyaVdCMm1CNW5FS20vQUpXVGpHRlpITkptSnlpYkNPdVBSa2s4VjV6RmZjUzFWVVpPTWoxSGJMZThFUWpQbzRwL2U3Unh4cmZqN05NeTlRSjN2UkZkYjhNK25PYXRleUthMFdnS081SnVYN2dKd0Zzemlpb25HSXpVOVVLS09URmJnbnV0SE1Va0tPeU1nY2NFZWpYWVRabGhRd3FPOHVuQ3ppUGlyWXk1NFk5TnVZQk1ZS3EwWXk0VjlLQVFKbHpHRFNoVjR4eXRhNThZWFlUSVJERGF3QXkwZndjeVQ2YnpxN3ZxbWRwUzBRdmJGMk9vWHdHNENoZWh0c0V4VEVGaEhnM2FidEc0dzVJbWNaSlFSTWdtQUQyMUpsVENDRmMxWjc0N0dINjZSbDJsUHN4dys3RE5wMUo5YTEiLCJtYWMiOiI4YmQxMTgwOWRkZGVlMjJlMDg3MGRjOTQ5NDg4YjExMjJhNzIxZWQ5ZWZlNWJiOTU5NWUyNDFiMzMwMWYwNTg1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJ1SVJSTGxkeU5rZHluTHN2bkkvanc9PSIsInZhbHVlIjoiZzlUWVBYMjlhcGxGZXpyRVBSTWlueFZ4czNsRjU2ZjNxQWVuUG8zbkZFQ2pZN3lNTDQ1cm1mR1FwQ3AxcGw1bHRleVJhbHVjdkRCM2xWNjhYS3dMMEQ2TDJRVjRDM3NLMlhGK2tKd1Uvcit1SERVcFMwemhWZGwrMzB1aFRPdWV6b3FhMXM2MmsrQVNndlVUZnBaMWV1Yjh3R1hsd1llVnhGbTBTWUFSNjUrcVZRS0ptcVpKL2NDZkkxMGhOa0UzT0FnYUczZXhwVm9oL1g0Zzd0bCtieUtRVHoxdUxxbFRGUlhTbkZDNWhWODh6V04xb3BhOGZPREd5bmRtOTFlQk1iQmdEcWgzL1didFFvZTNoL2E5SXFmVkJxTHZpVmNMUndKclVjd000U2hkRFdDVzZlWklzRklLL1lnOS80WGZiSUhmd2txQ1JmSmw4NldoenBMR2dDUUJiKzZYbmdGZVZSOGxGeTQ2SlBOc2VYTDlXQlRLRkx0K0JOekxaSUVTcEd0MHhRNGxUTTdKOW5EVENXR1B1VWFzOEJoNWE1WGxjbmdUYk1JbzhXTFV6NndBS1VIR3BKU2tTZ0dHTGw5UWJqVVFyYzFJdThqWmoyeGxWZGNJZFpCL0xKcExKZi9jaGZQenNsYkxaK1BiNVZoQXhuYjBYSnA4YkZNVDBpeW8iLCJtYWMiOiIwODFhMDEyZGRhODExMTA2YTBlMGFiY2E0NjU0YmE1MGQ4MTg0NTc1ZDFlYmIyZTEyZjA1OGE1NjE1M2UwYWI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-792519563\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2021160041 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2021160041\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1938503338 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImU4V0xRL3FjNkg0SENSMzgvM0VvN0E9PSIsInZhbHVlIjoia0FsM3lwemhmUTZoYm9CaUkwRWh6UTkvTXZqVExleS9jK0VHVXBrakJnK3RldjVWejZtVzNGNCtZVkhyS0lQVmp2Zms3eXNZWFdiNHc5aGxQN3VtN25HblVDZGV4MTV1elJYSWxtUU1NNkdZQWVENG9tRENuOCsrSFBHM2poUURwUlNmcDhLZTNmZi9wNWVLSE01SjJaV2tNMjNEMjRaU25CekJITzVuNUtXRlU4cUwwaVpuNlNoYnE5WkxTMVRHNXBQSEg3M0dPRFhJVk9nQi9TN0V3ZTFTd2d6Qm5hck1Gei84S3ZSQ2FJMDExdUYzZXRNblMrSVNMSkp4RU54VXJ5THAzMTZTK2lSZlZXTnF4UlFzOE5mTy9jdEhQaktCQjNXMWNyancxTHRaRFJMRXVZeWpuVmxpZnJXNk1jbHQ2Uy9aS2c2Z2NJckkvbGgvWkF2dnZtZjRGMTZzSHFzU0F0NklYUERKOEdENklZWmQrWEI2WHN1eVAwbUlreEFqR0x6YjVLdlNnemRSZ3hNTWRyR2lWckw3cmJwQnBmeGVlNU4ybkhvMzI1cENVaGFDQitxWGFGSzNNdnhuQUxIaXJ2RjBHQTNNdnBXOXRxWitkcWZXU1d5dUNqdXRidXZJd3JzdGRVdERsUlQvL0lvQmlWU3kxQ2VtUnNQQU02TFEiLCJtYWMiOiI1ODA3ZWY0YmZjYjYwZTRlYzcxN2I3ZDczMmVhMjExNjgxOGM0MWIzYzc4YmViYTNlMWUwZDZjM2RmOGJkMzA5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImtjRFd2cmxGSDlpUGg0dmNoTkFpY2c9PSIsInZhbHVlIjoiZ2oxUStwamx1aTlwMVRZdndyWmh6Q0xsYTFWTU5pV0MzdThLYVNKdmQxdjFWME1zNVh2elpQVkJWNHhFalNrMGsyN3VIR2kwR3pCLzlKMEFFRDFreDRuQXFVQkpORkRGQzRpRVNYT016Qm9CQWxDZERSaytRVDJuUTVTZXg4QUtXVU44c0RvcElZelhzSFAwNnkxKzhkbml1bE1oR1NESWd3TW1BeE50RGJZNUNRQTQ5eVFyS2VNZ2o3MFBEbnE2MkhuSStjS3gvNDlPWTlJenh3SjNMR3FlbVRRWU9CZkV3U0ZHMzNoWkQ4czEycDBrSWU4Vi9SQU80ellBd3d1eXQ1NWFzbW5rMDc5d25GY0RhZ3llL0lyOU5makJBSjFYaWlvaTUyLzl6WksxTGpNQWZLREM5ajJTZGd4a3FFZStCMGZ1NUJSWDRHbGxDL25GYWlZVDl0NXpjSkc0UGgrWFRUZzZUS09hWmhkTXEwKzJtWlZrQzZRYzRxVVg3NVVmZjJaNmJXeFZuN1VPVi95MTIvaEtndU9UNThFTDlUSGZra1FueHZmR01KOWM1TmdheDJRdGYvWlpvOTZNL1FEanZJaHJqVjJ2U09YR3pyN2lKZEdpbVlLUEQrN25LWU1LTzZ0eWRSbENQNUxQZkJmbWQvNWNZb3BWc1pwdXFCRUUiLCJtYWMiOiI1MTkxMzUyMzNkMTYzOTQwMWM5MGNmN2EzZTRiZDg0NzE4MGFkMzQyOGRkNmVhMDU3MzlkOTMxNjU5ZGRhNTRhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImU4V0xRL3FjNkg0SENSMzgvM0VvN0E9PSIsInZhbHVlIjoia0FsM3lwemhmUTZoYm9CaUkwRWh6UTkvTXZqVExleS9jK0VHVXBrakJnK3RldjVWejZtVzNGNCtZVkhyS0lQVmp2Zms3eXNZWFdiNHc5aGxQN3VtN25HblVDZGV4MTV1elJYSWxtUU1NNkdZQWVENG9tRENuOCsrSFBHM2poUURwUlNmcDhLZTNmZi9wNWVLSE01SjJaV2tNMjNEMjRaU25CekJITzVuNUtXRlU4cUwwaVpuNlNoYnE5WkxTMVRHNXBQSEg3M0dPRFhJVk9nQi9TN0V3ZTFTd2d6Qm5hck1Gei84S3ZSQ2FJMDExdUYzZXRNblMrSVNMSkp4RU54VXJ5THAzMTZTK2lSZlZXTnF4UlFzOE5mTy9jdEhQaktCQjNXMWNyancxTHRaRFJMRXVZeWpuVmxpZnJXNk1jbHQ2Uy9aS2c2Z2NJckkvbGgvWkF2dnZtZjRGMTZzSHFzU0F0NklYUERKOEdENklZWmQrWEI2WHN1eVAwbUlreEFqR0x6YjVLdlNnemRSZ3hNTWRyR2lWckw3cmJwQnBmeGVlNU4ybkhvMzI1cENVaGFDQitxWGFGSzNNdnhuQUxIaXJ2RjBHQTNNdnBXOXRxWitkcWZXU1d5dUNqdXRidXZJd3JzdGRVdERsUlQvL0lvQmlWU3kxQ2VtUnNQQU02TFEiLCJtYWMiOiI1ODA3ZWY0YmZjYjYwZTRlYzcxN2I3ZDczMmVhMjExNjgxOGM0MWIzYzc4YmViYTNlMWUwZDZjM2RmOGJkMzA5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImtjRFd2cmxGSDlpUGg0dmNoTkFpY2c9PSIsInZhbHVlIjoiZ2oxUStwamx1aTlwMVRZdndyWmh6Q0xsYTFWTU5pV0MzdThLYVNKdmQxdjFWME1zNVh2elpQVkJWNHhFalNrMGsyN3VIR2kwR3pCLzlKMEFFRDFreDRuQXFVQkpORkRGQzRpRVNYT016Qm9CQWxDZERSaytRVDJuUTVTZXg4QUtXVU44c0RvcElZelhzSFAwNnkxKzhkbml1bE1oR1NESWd3TW1BeE50RGJZNUNRQTQ5eVFyS2VNZ2o3MFBEbnE2MkhuSStjS3gvNDlPWTlJenh3SjNMR3FlbVRRWU9CZkV3U0ZHMzNoWkQ4czEycDBrSWU4Vi9SQU80ellBd3d1eXQ1NWFzbW5rMDc5d25GY0RhZ3llL0lyOU5makJBSjFYaWlvaTUyLzl6WksxTGpNQWZLREM5ajJTZGd4a3FFZStCMGZ1NUJSWDRHbGxDL25GYWlZVDl0NXpjSkc0UGgrWFRUZzZUS09hWmhkTXEwKzJtWlZrQzZRYzRxVVg3NVVmZjJaNmJXeFZuN1VPVi95MTIvaEtndU9UNThFTDlUSGZra1FueHZmR01KOWM1TmdheDJRdGYvWlpvOTZNL1FEanZJaHJqVjJ2U09YR3pyN2lKZEdpbVlLUEQrN25LWU1LTzZ0eWRSbENQNUxQZkJmbWQvNWNZb3BWc1pwdXFCRUUiLCJtYWMiOiI1MTkxMzUyMzNkMTYzOTQwMWM5MGNmN2EzZTRiZDg0NzE4MGFkMzQyOGRkNmVhMDU3MzlkOTMxNjU5ZGRhNTRhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1938503338\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-843294447 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-843294447\", {\"maxDepth\":0})</script>\n"}}