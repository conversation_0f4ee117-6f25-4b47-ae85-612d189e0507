{"__meta": {"id": "Xfad4b92b15fa20bc7a5808f454d41fd0", "datetime": "2025-06-27 01:13:37", "utime": **********.712785, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.303713, "end": **********.7128, "duration": 0.4090869426727295, "duration_str": "409ms", "measures": [{"label": "Booting", "start": **********.303713, "relative_start": 0, "end": **********.659202, "relative_end": **********.659202, "duration": 0.35548901557922363, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.659211, "relative_start": 0.3554978370666504, "end": **********.712802, "relative_end": 1.9073486328125e-06, "duration": 0.053591012954711914, "duration_str": "53.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45032288, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00348, "accumulated_duration_str": "3.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6871622, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.54}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.697838, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.54, "width_percent": 14.655}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (23) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.704307, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.195, "width_percent": 15.805}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "23", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-173590376 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-173590376\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1885685602 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1885685602\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1685939796 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685939796\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1503126463 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=tu6zsp%7C2%7Cfx4%7C0%7C2004; _clsk=1ggm1jz%7C1750986807338%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im13YXB3TFNtbkQyNWhsaVVJdnNteUE9PSIsInZhbHVlIjoiYTVmdWZFR3VuZnIrUGpFZis0UkQzV3lkb0R6U1FITGhkNXFoWEpqTC9LZ0ptR3J2L2YxRWdUN1JQZFdNYTdmSlVHWkFEVlB4TnVhSDRyRFZWYW44amRHWU1lMkNtRXI1amVsZURVWXBVUXJtZTgxS3pMaVdVeFZUd21QZUUvSWI2TGd4Zmo3YnlFOXZVZWlXOFdENkdOTUxWYlYyZ3hVVm5xbkVWSXBQQWw1NzFEVm1JZDhTNVYrR0s0WGk1WVhiUnlrc2Jrc3Z3U2hnajlZOFpLVnM4TnFEZ05tZFZxYXQ0b1VKNWJkYUhiQ24zRW5HSjludzB3bTZoWDNxRlZJUUZ0UU4vWXBmY2RhZW8rV2N1c1pZdDVlVFJWamxuMDNWNG9LbmhOUmZIbGREZFY1WWpzalJrUGxuZ2w3NkdzbGQrQWwyQTVzdDA3S0tjT1Q2QkxWRVU4N0FuUHA0MmxiV2VmOVV2ZXV0U2xEYUNFR0NkODlKTzJDWExIQjdQZmxjcEtNeWtxMkp2VTdVaTJNV0tmUllhRysvS2JrLzlicC9FWGtSYW1qOFRYbGoxT1VtL29lZlZIWWNrVkp4azVqUDcydWJqdVlOREh3WHl0WFBqWVpQc0V2cXcyLzAxN2hDQ3RSVlExOG85Y3hxTjFGYVNmZ00wZjZScW5CdVlDN28iLCJtYWMiOiJmYzIxMDAxYWI0YmEwNjI0MzAwOTQ1M2Y0MTc3ZmYxNjliZjFhMDg5N2I2MDBlNTVlMGJkZjIxY2IxZjRkMjcyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9iZ0hvL0hHYjJMNlRNbmg3b2t4eFE9PSIsInZhbHVlIjoicEVtdkpVd2F0RHpoMk9vaGQwOTlqT29EeDlaeXZta2ZyOW9SZ2tWMWd6WlE3eXBnbEU1dS9tREY1dWN6RW1rQ284WDc0Q05rTWhUN0JaUmVHMVJ5SVVBbVUwZWwwOExXZEU2Tm5hTXJkRUMyeFplb0Ntdlg5MnJMdTFhVFpkNDFyZ0dua1VVV1VpWkVubzVNV3NwK1RPV2ZtTU96YlN4U3BNdWRRWFBWbWthbmxrS25VYkY0UDlTL3ZURk5GRS84NEI2QXVVV3lUTGJqdFo2L2pOVTFqT1haM0tLTkZrV3VtNjBmdENoRmpQNUl2Q2wwS2svUkJvQ2tGK2R2UmN3YW93Y3FBaTVPYjRHZUVMZU1oVWlZNWYzWmh6RXptc1FRc0dxZ1BJaWh0UnByc0Q2c0VnbWRnQS9wRmpBZlI0enEyNWZHMlJ3cU40UGhhancwaE9HdlBteW5iTXp0Q3hxVW91ZjU5ek1PbVFFT2RYY1ZJL01xL1ozWUpwbXNWV3RCbTlYM3Jra0t5Q2MyWjVPcnhIdHhvT2VjQlJweGs3YUVhY0o3QkppTzRmWTFIa0hCOVJyeGJDL2FLR2txaDdPcHF2MFVKZGNiWlIrdG5IZzdhTVh5STJrVGRaVkpDQXA5RTRjYVRUbHRyb1Z2WXBZMzYwemlvOVlFVjFjVUpKYlQiLCJtYWMiOiJlZTMyNTNmODQ2NjI1NzU3M2I2NjExYjNkMTZjNDdlMDc3NGQ5NDljMDhhNzdlM2M2ZjMyMmVkYzM1Y2E4NWUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1503126463\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QVqhpWieKZCLT3nmskTMtatIDNwBdc2egPwBt6XG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-286106654 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:13:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ikp4MlgzbnpaaXUwVGlpTkFSeTV4dlE9PSIsInZhbHVlIjoiYnRHVUUyYTJqVUVvWnV3Mm0xQk9YNHM0ajRBVWhOWjhRRXhvb2lsc09qRDRHSjJWWEV5MGFrWVJIN0FUMVplUWo5YTNIditzYXJYUGIyYTlmUnF3bkJ6emdBNUYxL2xtUkduQTY3TXNpbkRKeWFud0dXT3oxZDdRZGFPUTRIS3JvOWt3N3ViRE9zSURPTzNIU3RnN1p5b2ZaT3k3S1R3VmFNMi9MQnJLeUU1S2tNaStsK3pNdit5R0Y3bldZak52aEp0ckZrRzVIZWtBNW9YV0J4MW9tcyttYklXSytuSnE1dThkSEhLeTB3V1ZqdmNMb2hjNnRwNDV3TTdLdk9QTjZabFhJd0NyS2JiM2xieHNKWGtBWHkvZGFhRFBWczdObVplUUFrUWE2WldDYTI0aW0zanpoQWVEdlBJRXZrVldFWHhQaTYwanljaHE3dEVENlFpLzh3Y3RCZHB1S2l0eGdZYlZHMkU2dXpxMTM1dXNTclFtcVJYRE9RNzZOMExyQWpobUxna0RpTG9aQ3hoNTQwZlE1M0ZEdUpxUTYwU0IwUEpNNWJDbDdWMTNhZGsrdjJwNzdLNm14eXdpOFowTVl2blV4MEhORVcvbjlPb1lSLzRuWnU1eitZU2VEdEtSVWJCSnVuU0NxaGJ1QVZWeFJ0WlZWbzZOUVBNM0grckEiLCJtYWMiOiI0MjFmNjQ1NjJmNGY0YTU4ZjU2ZWE0ZTRmMjQxZTNlMDA1ZTg5YjU5YjU5NTJhNDEyMmU0NTlmYzZlZWEzYzQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBtMFpYR2o0R2IwWFJnN2VBMVA2cmc9PSIsInZhbHVlIjoiZis1bWdvamR6RFdVK25FbWpsa1ZpZkU2QlIyaXBmZDE1dGtHTjc3ajBvbXBIampBS2ZqZUo1MDZXSWZtck4xNnVQNmZyUlV4aUltcnIrL1NVN3lxYlg4bXFlcjNPZ1VUdDlUcjlmUi9IMXk1TmdNVndEbmxUbllmUWkwQ2YwZXhmcHRnQ1lLc2F3Nlg2M1B3a2RqYmd3U2kveDFwcUhrSWFjYXRaVFlCZWFVemJCaGM1UjJlcGd5S1VVc3V5Umx0dXlhUGRCeFJXdEZ4WTFIVEIxd2dzTHFpeHI2UGVESXVVV2hBZzByc2NFS05FY3FXdWQramtqOGdZOWUrSkNDNWM2SEQ5U3lpSzhKYUkrOTl3TU0wNEpUY1ZNY01KdUdiV1BmbENDb2F1UUtTTXdjY1J1K2NxSWlQY3AyTWlZUmQ4cXFwb1lKUUFZb3N5L09IUEpNbGdZdlNxRlZBd2tDd0wxRDlCTmpaTjUzdGtOSmloUlRiQmJyVEZ3UUQ4dXFxZXlKdmpGeHRpZTAzT1BqV0dRUXZFV0YzODhlUGpKUGs4Q2hMVUpQbURLWkVxc1kyaW5NSEpWWWNFakRJUVRjUkJEdzVOTndsZkh0VkQ2cEtjZWZ3NkFMZ3EzVFR4MHBzVm9wRDhIWTBpMFF5Z2Y4QUltSVdYOW5SSW1mNXgwa0IiLCJtYWMiOiJlMjMzYjM3YWZjYjIwNTM3YzliMjQ4NTdhZWU2MjVjMTBmYmNhZGFjNTE0ZWY2ZmY4MGY0NDcxZDc0ZGQwYzQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ikp4MlgzbnpaaXUwVGlpTkFSeTV4dlE9PSIsInZhbHVlIjoiYnRHVUUyYTJqVUVvWnV3Mm0xQk9YNHM0ajRBVWhOWjhRRXhvb2lsc09qRDRHSjJWWEV5MGFrWVJIN0FUMVplUWo5YTNIditzYXJYUGIyYTlmUnF3bkJ6emdBNUYxL2xtUkduQTY3TXNpbkRKeWFud0dXT3oxZDdRZGFPUTRIS3JvOWt3N3ViRE9zSURPTzNIU3RnN1p5b2ZaT3k3S1R3VmFNMi9MQnJLeUU1S2tNaStsK3pNdit5R0Y3bldZak52aEp0ckZrRzVIZWtBNW9YV0J4MW9tcyttYklXSytuSnE1dThkSEhLeTB3V1ZqdmNMb2hjNnRwNDV3TTdLdk9QTjZabFhJd0NyS2JiM2xieHNKWGtBWHkvZGFhRFBWczdObVplUUFrUWE2WldDYTI0aW0zanpoQWVEdlBJRXZrVldFWHhQaTYwanljaHE3dEVENlFpLzh3Y3RCZHB1S2l0eGdZYlZHMkU2dXpxMTM1dXNTclFtcVJYRE9RNzZOMExyQWpobUxna0RpTG9aQ3hoNTQwZlE1M0ZEdUpxUTYwU0IwUEpNNWJDbDdWMTNhZGsrdjJwNzdLNm14eXdpOFowTVl2blV4MEhORVcvbjlPb1lSLzRuWnU1eitZU2VEdEtSVWJCSnVuU0NxaGJ1QVZWeFJ0WlZWbzZOUVBNM0grckEiLCJtYWMiOiI0MjFmNjQ1NjJmNGY0YTU4ZjU2ZWE0ZTRmMjQxZTNlMDA1ZTg5YjU5YjU5NTJhNDEyMmU0NTlmYzZlZWEzYzQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBtMFpYR2o0R2IwWFJnN2VBMVA2cmc9PSIsInZhbHVlIjoiZis1bWdvamR6RFdVK25FbWpsa1ZpZkU2QlIyaXBmZDE1dGtHTjc3ajBvbXBIampBS2ZqZUo1MDZXSWZtck4xNnVQNmZyUlV4aUltcnIrL1NVN3lxYlg4bXFlcjNPZ1VUdDlUcjlmUi9IMXk1TmdNVndEbmxUbllmUWkwQ2YwZXhmcHRnQ1lLc2F3Nlg2M1B3a2RqYmd3U2kveDFwcUhrSWFjYXRaVFlCZWFVemJCaGM1UjJlcGd5S1VVc3V5Umx0dXlhUGRCeFJXdEZ4WTFIVEIxd2dzTHFpeHI2UGVESXVVV2hBZzByc2NFS05FY3FXdWQramtqOGdZOWUrSkNDNWM2SEQ5U3lpSzhKYUkrOTl3TU0wNEpUY1ZNY01KdUdiV1BmbENDb2F1UUtTTXdjY1J1K2NxSWlQY3AyTWlZUmQ4cXFwb1lKUUFZb3N5L09IUEpNbGdZdlNxRlZBd2tDd0wxRDlCTmpaTjUzdGtOSmloUlRiQmJyVEZ3UUQ4dXFxZXlKdmpGeHRpZTAzT1BqV0dRUXZFV0YzODhlUGpKUGs4Q2hMVUpQbURLWkVxc1kyaW5NSEpWWWNFakRJUVRjUkJEdzVOTndsZkh0VkQ2cEtjZWZ3NkFMZ3EzVFR4MHBzVm9wRDhIWTBpMFF5Z2Y4QUltSVdYOW5SSW1mNXgwa0IiLCJtYWMiOiJlMjMzYjM3YWZjYjIwNTM3YzliMjQ4NTdhZWU2MjVjMTBmYmNhZGFjNTE0ZWY2ZmY4MGY0NDcxZDc0ZGQwYzQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-286106654\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1825249393 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1825249393\", {\"maxDepth\":0})</script>\n"}}