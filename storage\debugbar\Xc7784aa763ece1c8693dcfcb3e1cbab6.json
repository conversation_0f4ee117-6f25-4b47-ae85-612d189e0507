{"__meta": {"id": "Xc7784aa763ece1c8693dcfcb3e1cbab6", "datetime": "2025-06-27 00:40:45", "utime": **********.358597, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750984844.940696, "end": **********.358611, "duration": 0.41791510581970215, "duration_str": "418ms", "measures": [{"label": "Booting", "start": 1750984844.940696, "relative_start": 0, "end": **********.301501, "relative_end": **********.301501, "duration": 0.36080503463745117, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.301509, "relative_start": 0.3608129024505615, "end": **********.358612, "relative_end": 9.5367431640625e-07, "duration": 0.05710315704345703, "duration_str": "57.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041768, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00284, "accumulated_duration_str": "2.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.335297, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.915}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3447652, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.915, "width_percent": 16.197}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.351141, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 77.113, "width_percent": 22.887}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-773826890 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-773826890\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-871658367 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-871658367\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-867293297 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-867293297\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1711989764 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750984840791%7C60%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlIycG9GK09PVllxNXQyWHl2YWlDOVE9PSIsInZhbHVlIjoiVTBtZ3pDNmNqcm1JdFNZOEdMVUlXRzMreHRIaTV1d2QzZFd4bDBFMExySmFZVnZKSnlBKzlrVmZmU2pPUThkL3orbFlUbXkycGxBY2hHaGVFWlQwanE0cnlMa2hDWHUwZEZ3SEp6QS8wZUJ6Q1RzWUFiQXNuWERNRDF0YzlORCtPaHQ4R0RFRUhzMTYxNlY2Q0UzS2Z0MGRaL2N6V3JaRlBVR01jbmVaZXJlNGdiaVhJYzhJdkdpeGRHWVBGNWxtYlRtM1BLZEZIVmJmdGxJUGMzdkc2L2JoZFBmaWViU0tEc1RuNEVhSjR4N1pNeE9xVGw2eGpOeWlJNEg5YTMrbkpoZzVSZ2oxaG96NmEzZFhtUm1VUDR0L0hSTkNqaUxoUjY2b012Q1VOUGZNRlkxb3JlS0t2aVJudFNzRmdqOVNHcE96SkQ4V21QZGJPRy9pUWFOc29xQVprNW9DdXZLdjA1WlRhdHpNTTQ4USt3cDlPanZsOTF1K0h0KzVrWGJOdk1LVFFDNTJqWjdSbnB6NDlRQTFFdjJNOEpnaG5NQnpaSnR4N1JjV1MyMHlMZHVrcm9xaTkvaENIZVJaNWhLaE50UDJDN29Pekg4TTZFUERsczNpSnQvclRRU1BUOThIY2o4STJmdmh1V1Z4MlpMQ3pXRnpTSlEyV3RZMG15UXciLCJtYWMiOiI4OTY5YTU4NGEyYzBjYzczNWE2MWY5NDljYTQ5NzdiMmY0YTZjNjIzODc4ZTA5YjIxODNhMjExZjVhMTI2MmIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJqNXo5S0NkbzlHbG8zY1M2dTVrU2c9PSIsInZhbHVlIjoiMzd1T2lPR2dmTjRpQmhUT09OOEpsVHNEY2FVS2V6Qld5NmlDK2hSQitVN2l1SHlqZW53S3VwWlozMFhaRDF3Sis0ak15RnhKYXdoR1dUdTAvazFhMDJ5ckN5Y0dzb043cGwrOEJpQlV3MEJ3YUVrazBQMXowc0FPN0IrNFgvck5mT1lEYWJPQTFsODFFSldySkh0SDZGWFFpWkxRNldtcUFkYzNqWWZuNEQ4OUtPRzY2ZlN5VlJHeENHVzZndU01bEN1R0V5OHltaVBicEtWY1Q4VXo3NlFJK0NqNlczR3lhYzFoV3BBbm5GSDRmcUM2aGtjUVNVdlFMS2hIV1hrbHhobUJYNzBieDE3ZllMckpwakFaL21kZncwcEwxWHQzVkxENC93c3lTaWsxcWxKZldYWTg4aGVMM1B1MHM3YkM0UG5rRm11WjF2bURrbXBxNFZ3LzYzTWdSZFkvdUhjdlRmbkVmREs1clNXcENjYWY5N0RoMEJVT0pNeFVDODN3VDBmR3JyWkNPL09xaUM3N3E4cEFIczdSL01FOFp5UStFamc0ZWRsRlVYQ09lMlhkSDNTVFBiakNUMGRkWW5EVHlnTzk1VjhycXhuQVVabm5vU2FNRlVpV005V25PcmJlclp3Vml0MUVTMW1pRlZmTGd6bm1TeitlSnl3MDlaSGEiLCJtYWMiOiJmZDc3YTQyOTE4MDU0NDBhMjliNWNjMzAwODdlZmM0MzAwNGRkZmI3OTYyNTU0NTBmMjJmZjBkMjc0Y2EzYTQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1711989764\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1641947045 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641947045\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-404268785 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:40:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5kSnBQYkhYUERwK29tRDFXLzFUR2c9PSIsInZhbHVlIjoiL3hhelJkSlg4WmpvWGE5TzJjUUFLRkZwMXBhMkNNeHVBcGJHdjhkMmFZRzlzTVFzVDIvM2ZTTWhZMXlmQU4zTXNPUEROU2pFWTBqaTFCMWVVOWk0bWxyWXBLNEZtQ2R4YU1hRmxTdTE5YklJejl4aGFKUDZnTjd3ZFg4OWJTUUl3TXhFQ2NyYjRqZ0FRSFQwNWl0WHV6RTBuRUh6VVM2YWh1R3ZUT0Q2bEpIdlBDVmsva2YwMml3a3E1N1ppU1E3MzBSQWF2dGMyMGJvSW5pVThQRCsyZHkzTUJqWFZWODJLMjVwTVZCUDVjVlZXcE9iSldLY2pkZzNaRXc2OWFKNmNhUTJOS0VreTdrWnUrMmpUU21NekN3aW1KS1MyWnRjLzBEMEozT0h1VEVPRS9mNmtaSkNRWmREMDRXZnhnTWVUZHN2bjVlUy9DRjlwOWJVRzZOODl4RWVQTmhJZEpuVzg2VWM3WHBtaEJLOGdNRDY5TDgwZTNzUjZ2MHpKZUxCdm1OR3NOcHJRRkJsNjZzQXpvRC8vVVZaaUpDZnZ3MjdZcFRxRDBYOCtRWGRJcDMyWmZwWk5FakFuOEdoc1pNeW5SSFFwZWxTY0NPKzNMZHFtNUEwVkRFNkhFaWdpSUFhaTdFblpQREFOdUdHd1c4S2NUa0RibXluRWZTeE5EK0kiLCJtYWMiOiJkMjRlOGJmMDFiMmE0MTVmYzU3YjE0MzkzZjA1NDgyNjZkMTk0YjU0MTMyN2I0MTJkY2EwN2FkNjU1NWY3MDlhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:40:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijk1VEQ3M2ZVRmVVQndWYnZOOUxuSlE9PSIsInZhbHVlIjoiWjNNK254bk8xVzJyK0JJcFFxTnNiRnFLSlo2TnNKdzk0SWRhK3FTQzVDU2tIQ1J6U24vWFdmQjgxRlNYd0VaS2ZlTStXdFdLeS9wM3RFckdaNG8xcW1yODBHbm5KWXFCZzdhRG9RQ01QRWsvZDV4TzlaSlZCblZ6L1l2RUxiSUErMWdPK1ZRQm9ZV1hjRW9PTXRwK3NUbWVmRkFIa2pMcHY1Y0Nic25BU3NwUWpWdjRRS0xTNjAwenkzTFlPR1pJZm83Q1pVODJ1eHFrTzhHUWZ2UEVFMlhjQkFEU2RGOU5vdi9OY0c0VndSa0g3TXZtb2lOMC9DdDAxZ2Nnb1pBanRvbitNNE0rZzNGbUlOeUk5b0ZISFhITWI5eXRQNjN5YTR2cDNMRGJ1UFgzN3A0cTFkdEoxMklsd2NaRURiWG1ZZHdiMG42M25HQ1RUbG9EOHIxOTJxZE1yQXN5RFZuQitIbG0wQittRGV4eEEzQ1JiZ2lQQStSdUtGRE5SamI1Z2Q5b240YWxBQWY4MVBqallONjVWWGtQQk5ja010dS9DcE43c1dZTkZXZzFtRlljOC9DdnROYURwRWZhZEFoWnU2elk4elBNUG1LQ2pZdGpSNDNURkNZelhMS1JZZE5jbjRNaHd0ZlpnV2Z0bXArODAxOHBpSHdQWG5BN3BrYTUiLCJtYWMiOiJiZDlkMjE5NzljNmJkMzBjZDNlMmZjZjE2Mzk0YjIxM2FhZDRjZDU2MjE5ZDIwNDIzMGUxYTllMmJlZGY3YmM3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:40:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5kSnBQYkhYUERwK29tRDFXLzFUR2c9PSIsInZhbHVlIjoiL3hhelJkSlg4WmpvWGE5TzJjUUFLRkZwMXBhMkNNeHVBcGJHdjhkMmFZRzlzTVFzVDIvM2ZTTWhZMXlmQU4zTXNPUEROU2pFWTBqaTFCMWVVOWk0bWxyWXBLNEZtQ2R4YU1hRmxTdTE5YklJejl4aGFKUDZnTjd3ZFg4OWJTUUl3TXhFQ2NyYjRqZ0FRSFQwNWl0WHV6RTBuRUh6VVM2YWh1R3ZUT0Q2bEpIdlBDVmsva2YwMml3a3E1N1ppU1E3MzBSQWF2dGMyMGJvSW5pVThQRCsyZHkzTUJqWFZWODJLMjVwTVZCUDVjVlZXcE9iSldLY2pkZzNaRXc2OWFKNmNhUTJOS0VreTdrWnUrMmpUU21NekN3aW1KS1MyWnRjLzBEMEozT0h1VEVPRS9mNmtaSkNRWmREMDRXZnhnTWVUZHN2bjVlUy9DRjlwOWJVRzZOODl4RWVQTmhJZEpuVzg2VWM3WHBtaEJLOGdNRDY5TDgwZTNzUjZ2MHpKZUxCdm1OR3NOcHJRRkJsNjZzQXpvRC8vVVZaaUpDZnZ3MjdZcFRxRDBYOCtRWGRJcDMyWmZwWk5FakFuOEdoc1pNeW5SSFFwZWxTY0NPKzNMZHFtNUEwVkRFNkhFaWdpSUFhaTdFblpQREFOdUdHd1c4S2NUa0RibXluRWZTeE5EK0kiLCJtYWMiOiJkMjRlOGJmMDFiMmE0MTVmYzU3YjE0MzkzZjA1NDgyNjZkMTk0YjU0MTMyN2I0MTJkY2EwN2FkNjU1NWY3MDlhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:40:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijk1VEQ3M2ZVRmVVQndWYnZOOUxuSlE9PSIsInZhbHVlIjoiWjNNK254bk8xVzJyK0JJcFFxTnNiRnFLSlo2TnNKdzk0SWRhK3FTQzVDU2tIQ1J6U24vWFdmQjgxRlNYd0VaS2ZlTStXdFdLeS9wM3RFckdaNG8xcW1yODBHbm5KWXFCZzdhRG9RQ01QRWsvZDV4TzlaSlZCblZ6L1l2RUxiSUErMWdPK1ZRQm9ZV1hjRW9PTXRwK3NUbWVmRkFIa2pMcHY1Y0Nic25BU3NwUWpWdjRRS0xTNjAwenkzTFlPR1pJZm83Q1pVODJ1eHFrTzhHUWZ2UEVFMlhjQkFEU2RGOU5vdi9OY0c0VndSa0g3TXZtb2lOMC9DdDAxZ2Nnb1pBanRvbitNNE0rZzNGbUlOeUk5b0ZISFhITWI5eXRQNjN5YTR2cDNMRGJ1UFgzN3A0cTFkdEoxMklsd2NaRURiWG1ZZHdiMG42M25HQ1RUbG9EOHIxOTJxZE1yQXN5RFZuQitIbG0wQittRGV4eEEzQ1JiZ2lQQStSdUtGRE5SamI1Z2Q5b240YWxBQWY4MVBqallONjVWWGtQQk5ja010dS9DcE43c1dZTkZXZzFtRlljOC9DdnROYURwRWZhZEFoWnU2elk4elBNUG1LQ2pZdGpSNDNURkNZelhMS1JZZE5jbjRNaHd0ZlpnV2Z0bXArODAxOHBpSHdQWG5BN3BrYTUiLCJtYWMiOiJiZDlkMjE5NzljNmJkMzBjZDNlMmZjZjE2Mzk0YjIxM2FhZDRjZDU2MjE5ZDIwNDIzMGUxYTllMmJlZGY3YmM3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:40:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-404268785\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-436482676 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-436482676\", {\"maxDepth\":0})</script>\n"}}