{"__meta": {"id": "Xb8425a6e8ca16e81c5e07bf16533d099", "datetime": "2025-06-27 02:30:59", "utime": **********.809187, "method": "GET", "uri": "/add-to-cart/2299/pos", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.305659, "end": **********.809208, "duration": 0.5035488605499268, "duration_str": "504ms", "measures": [{"label": "Booting", "start": **********.305659, "relative_start": 0, "end": **********.706093, "relative_end": **********.706093, "duration": 0.4004340171813965, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.706102, "relative_start": 0.40044283866882324, "end": **********.809211, "relative_end": 3.0994415283203125e-06, "duration": 0.10310912132263184, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48691152, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET add-to-cart/{id}/{session}", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@addToCart", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1320\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1320-1544</a>"}, "queries": {"nb_statements": 7, "nb_failed_statements": 0, "accumulated_duration": 0.019370000000000002, "accumulated_duration_str": "19.37ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.750131, "duration": 0.014060000000000001, "duration_str": "14.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.586}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.77236, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.586, "width_percent": 2.839}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.786145, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 75.426, "width_percent": 3.098}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.78809, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.523, "width_percent": 2.065}, {"sql": "select * from `product_services` where `product_services`.`id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1324}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.79251, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "ProductServiceController.php:1324", "source": "app/Http/Controllers/ProductServiceController.php:1324", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1324", "ajax": false, "filename": "ProductServiceController.php", "line": "1324"}, "connection": "kdmkjkqknb", "start_percent": 80.589, "width_percent": 2.581}, {"sql": "select sum(`quantity`) as aggregate from `warehouse_products` where `product_id` = 2299 and exists (select * from `warehouses` where `warehouse_products`.`warehouse_id` = `warehouses`.`id` and `created_by` = 15)", "type": "query", "params": [], "bindings": ["2299", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 155}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1328}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.796488, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:155", "source": "app/Models/ProductService.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=155", "ajax": false, "filename": "ProductService.php", "line": "155"}, "connection": "kdmkjkqknb", "start_percent": 83.17, "width_percent": 15.126}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/ProductServiceController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceController.php", "line": 1397}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.80072, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 98.296, "width_percent": 1.704}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\ProductService": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 22,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1845341716 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1845341716\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.791649, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 2\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 5.98\n    \"originalquantity\" => 7\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2300 => array:8 [\n    \"name\" => \"جالكسي كيك البندق 27جرام\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"id\" => \"2300\"\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/add-to-cart/2299/pos", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1248641444 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1248641444\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1522292453 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1522292453\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1856656950 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJaTXFCQXorOXkySWpnWElRRHlmdXc9PSIsInZhbHVlIjoib085SDQ1Z2hGbEFvUkRtYk4vTTA1K2tuOTdEUW05U1BYQW1jRCtFbGFaU2IyclFZZmNISGRwRWU5eU1BWnZEQ3R0ZGpnOVV3SmQvUm9HWS93ZkJXdy95L3MxUFlUa01ESHlLMENFd0E2b3JObisxRytMUmZIUUJSZWRDRi9kZGk2RmdsZldJTEpVVkhXeThIOXNKVy9YQzFqTmJVN2FsZmNoRlRkaWQ5Sm1aSVY3bXJnYnFzUkhYNFI0QVNDRlhZTFkxZVFXdG5HTktDTkVuSFV5Y21OZ2pkbzlIYy9yei9NbVYwcXl4THk2VFNKeVlkYXlUaEZHdU92eGh1TG5EcmRKUFJVb1BmVTRZWm5pSDdhWnFhVWE4SzVHVkFjU2Q1OGlZNUU1UVFndHNZSVR0MUVnWmUxZU1MZnRrK3VQUkNManlHVWhYMTBGQkFabGtEKzREQW9QUSs4MG5rOWxQVDVkQzdEY3ROd2ZEeURYbWtlQzVKRXc3N0JYbnV4cU9LTXJxNXNUejJHdXFPQnBJTUlFSWVTNVkwb3FUaG9sMnRWNit5bGhaVGZiMHJmdWNJTlF1Qy9VK09JTThwaDZWZUJDckJCcW9ZODZMN3BFRHZZZXNiMUVFTUN4dFJCSmMvVlBMK1NUM1Y4YzRsQkJzSVA2YzFZZ3FGa0NqTy9PY1AiLCJtYWMiOiJiMDE5YjQxY2VjNzBmOWE2N2QxZGJmZWIwMjE1MDM4YjlkNjE1MjQxOWIxNGY2MDYzNDcxYjg0NmM3OGJjYzI1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjR5RVJHUnFPNGVvaml6MnFKR0s4OUE9PSIsInZhbHVlIjoiWUUrcXVXRUtCLzh6Z2kzTWxsY3U5V3V5cFFSRWZyZmZlZkNFa2VxY2t1VWJROXZZR3FxOHdybG12UWdxcmp6Rlh5ZElDL1FWNEo4ZzlFWW9SY2g4MzZDckRhMGd0ekpWZStOSW5PQ1NRU29zamNScWNtSWREWW5HMkhSdWNyYmlQOUFySHA0TktHNXBVQllNTTIzYkt2eUlOL2NubjVqY25aZGpwTTQ3VHZlNE5hRzVDYUV2SjJ1ZkYxMjAvSGFTeHBZeDIybUxaVVBkTGdPRnJNN2kzdlh3RUZVU1EwUGFlc24yUmE5MzlmR1lVS1FONjFEKzNWV0tLK1NDUzdQU21YbERULzc3NHFzVXkvYkgxR2VSLzQxU0p2VWZIbGFQWDIxUkF0bGU4Z2dDa2hJREorRTVJeWRMWXd6Y0UzOXB0bTFROXZ3eTVnU3E3aFJQWkRIbnFmZzY3RG9qZ1lXM0dLSW9jVVhySzRidmZsZk9pVjhuRnV0bUhTRGQxeDlwb241ekhhUXhxdVZVMERnY1lMbDRnY1ZqYnFrbFk5UWtEQTdJbzhuWlJRQmlYNk0zcnB5dzVzUERCSzJTeFphOHZHdmFNNHZmZEJ2Q2VuVmxvblZKa0c5ZW1JNTNKei82VUhSZ2ttVnhRdFJxRHo1V2diTnJGNGlOWUt2RDhEN08iLCJtYWMiOiIwM2QxYTA0NDU0MTRiZGRmYWFmMTVlMGUyOWQ1NGEyY2FlNTllNDIxYzc0NWY0YzUyNjUzZWI0NjhmMmZlMWMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1856656950\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-186815134 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-186815134\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:30:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlFNVUNDSjZVa3dybGdhRDBTdmh0d3c9PSIsInZhbHVlIjoiWERYZ2lLQU1odzVsY0RPUFZoYjRiSnM4S1N0NVR2VWhOM1pPMHo2VVc4dU52SGJiTlVhYWxYV0w0Y1lvZWVRWllhWmhNd09ibmoyQm0yb3JrMVpPU3VDNWwvOWRQNWZ6TXZ2aDV4OHRsRzg5SFlNZlROVS8xQThxaXBLM0psVHVJb1JPLzNEYW1ML1ZGdFliTnBEWG9OemNUOTl6Zm5qWDZzWEwzbjBXdUpJL05tT0lqSGFJaVpnMDhPa2JTYzJ0eUNVaEx4WnZzRENtUnVnVUdOMTZtZWt2NzQ0S1ZNeU1IYTFFSGZodTdKUGNKaVFMbE1wVUdwRXNVc0J2MlJKcCt5a3dhaFBiRnphcXVlQllEQnFKaDR0R0FGelVmaXBYa0d2UWpQVmRPMmJoMDBUbVR0bVF2UnpKc3IyMXoxN3M3NWs5V21Qa0V6ZkxYNldmWXIxYUZyM3BnbGlhanlVVk5SR0FGQzlSWXFzU2RQdi9BUyt2WlJ4U3FFY2c1UFdMTytrSENzUW1wS21QbzBqaFNod251S3g2QUxCZWMweEkwd0F3K242amE1cVREUlpvaHBzd1pnVDJ3cGl1YUNldUpUV1dJS1NsQlpVaGh1YlBpdW9RYmFCVS8vS3lRRHJWeGp3bnhzUXU1UXVHUlhvVXNaWlpCMEFRQ0RHbEZpODUiLCJtYWMiOiI2NTk2OWFjMzA2OGY2MTM4YTRkZDhlNTM1OWY4NmMwZjc5YzRhZmUwZjQxMDJkMGU2MWQ0NWYwYmZkMTc4Y2YzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:30:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjR0ZVQ3b0VuUFcwME54c0ZyV3phZkE9PSIsInZhbHVlIjoiYjVTamR3SVdhR3VWbzFnZk1zZzY2T1BVNnozN1hEYkE0blhsU0FTazFBZERRUmtjb0p5NXp6UzZOcEkvZExOMkY2UnJmR3hndjAzV2FabzFyZG13R0hqbWhSeUVaMzk5dU9EYTdwNlg0SitrY1hsaTQ4Ym1aQXUvMStMdWxWTGVET3JlNzdCUlE3RnliRjc0MVZiQVRZbHoveDFSUnVjZGZUNW5TcHh3MjViQ1NPejh6aThocGRvQXFyRU1IdDlZaStoK29mMlViMmQ5NFVpdDA2dmJqVHhBUVAvcWFDaVpDdzh2UzVrNXkvYVIxUGREOTVtTEFWU29lY2FSR28xRUJJSjVwYTV0bTBubTNvN0RFd2VFbDdwVGtaYkVxYzZ4N1hqdmdWVlR4YkQ1K0ltb2tGUUd4MUEwOEhLR2hNdStwaXBLb3RhTHk3K2xTc3pMQ0pGeFZLMVgrRlcvT0lKRXVhekxIdUU0RE1hZ0kreWRvY2hqYUV0OStzK1V0V3RndmVaS3BnNHcwbWRiU0NvbGRGSVJacUFOQmpSVnF0SHZVOEwxenNRR1RXY3ZlQUczaEpOU1hjRllNejd1WTBVVUp0MHhRQjg2OGRyeDBnYzl5SjFWL3VxY2F2Z1JhWDlxQzhMdlJUczJUQmFYS2phRmY3NDFQV1czTUErTnpOZ2YiLCJtYWMiOiJiYzMwY2Q2Yzc2YWQ5OTIyMWNiNTI4MGE2ZWMyMjdlY2IxZWM1MDBiYzhkZTA5MjI1ZGY2ZDA3N2JmMGI3YmE3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:30:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlFNVUNDSjZVa3dybGdhRDBTdmh0d3c9PSIsInZhbHVlIjoiWERYZ2lLQU1odzVsY0RPUFZoYjRiSnM4S1N0NVR2VWhOM1pPMHo2VVc4dU52SGJiTlVhYWxYV0w0Y1lvZWVRWllhWmhNd09ibmoyQm0yb3JrMVpPU3VDNWwvOWRQNWZ6TXZ2aDV4OHRsRzg5SFlNZlROVS8xQThxaXBLM0psVHVJb1JPLzNEYW1ML1ZGdFliTnBEWG9OemNUOTl6Zm5qWDZzWEwzbjBXdUpJL05tT0lqSGFJaVpnMDhPa2JTYzJ0eUNVaEx4WnZzRENtUnVnVUdOMTZtZWt2NzQ0S1ZNeU1IYTFFSGZodTdKUGNKaVFMbE1wVUdwRXNVc0J2MlJKcCt5a3dhaFBiRnphcXVlQllEQnFKaDR0R0FGelVmaXBYa0d2UWpQVmRPMmJoMDBUbVR0bVF2UnpKc3IyMXoxN3M3NWs5V21Qa0V6ZkxYNldmWXIxYUZyM3BnbGlhanlVVk5SR0FGQzlSWXFzU2RQdi9BUyt2WlJ4U3FFY2c1UFdMTytrSENzUW1wS21QbzBqaFNod251S3g2QUxCZWMweEkwd0F3K242amE1cVREUlpvaHBzd1pnVDJ3cGl1YUNldUpUV1dJS1NsQlpVaGh1YlBpdW9RYmFCVS8vS3lRRHJWeGp3bnhzUXU1UXVHUlhvVXNaWlpCMEFRQ0RHbEZpODUiLCJtYWMiOiI2NTk2OWFjMzA2OGY2MTM4YTRkZDhlNTM1OWY4NmMwZjc5YzRhZmUwZjQxMDJkMGU2MWQ0NWYwYmZkMTc4Y2YzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:30:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjR0ZVQ3b0VuUFcwME54c0ZyV3phZkE9PSIsInZhbHVlIjoiYjVTamR3SVdhR3VWbzFnZk1zZzY2T1BVNnozN1hEYkE0blhsU0FTazFBZERRUmtjb0p5NXp6UzZOcEkvZExOMkY2UnJmR3hndjAzV2FabzFyZG13R0hqbWhSeUVaMzk5dU9EYTdwNlg0SitrY1hsaTQ4Ym1aQXUvMStMdWxWTGVET3JlNzdCUlE3RnliRjc0MVZiQVRZbHoveDFSUnVjZGZUNW5TcHh3MjViQ1NPejh6aThocGRvQXFyRU1IdDlZaStoK29mMlViMmQ5NFVpdDA2dmJqVHhBUVAvcWFDaVpDdzh2UzVrNXkvYVIxUGREOTVtTEFWU29lY2FSR28xRUJJSjVwYTV0bTBubTNvN0RFd2VFbDdwVGtaYkVxYzZ4N1hqdmdWVlR4YkQ1K0ltb2tGUUd4MUEwOEhLR2hNdStwaXBLb3RhTHk3K2xTc3pMQ0pGeFZLMVgrRlcvT0lKRXVhekxIdUU0RE1hZ0kreWRvY2hqYUV0OStzK1V0V3RndmVaS3BnNHcwbWRiU0NvbGRGSVJacUFOQmpSVnF0SHZVOEwxenNRR1RXY3ZlQUczaEpOU1hjRllNejd1WTBVVUp0MHhRQjg2OGRyeDBnYzl5SjFWL3VxY2F2Z1JhWDlxQzhMdlJUczJUQmFYS2phRmY3NDFQV1czTUErTnpOZ2YiLCJtYWMiOiJiYzMwY2Q2Yzc2YWQ5OTIyMWNiNTI4MGE2ZWMyMjdlY2IxZWM1MDBiYzhkZTA5MjI1ZGY2ZDA3N2JmMGI3YmE3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:30:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.98</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>7</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2300</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1576;&#1606;&#1583;&#1602; 27&#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}