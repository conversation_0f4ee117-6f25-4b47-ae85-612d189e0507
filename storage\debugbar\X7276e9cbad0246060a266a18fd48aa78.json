{"__meta": {"id": "X7276e9cbad0246060a266a18fd48aa78", "datetime": "2025-06-27 01:26:57", "utime": **********.091877, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750987616.618497, "end": **********.091895, "duration": 0.47339820861816406, "duration_str": "473ms", "measures": [{"label": "Booting", "start": 1750987616.618497, "relative_start": 0, "end": **********.033002, "relative_end": **********.033002, "duration": 0.4145050048828125, "duration_str": "415ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.03301, "relative_start": 0.41451311111450195, "end": **********.091897, "relative_end": 1.9073486328125e-06, "duration": 0.05888700485229492, "duration_str": "58.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45027056, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0024400000000000003, "accumulated_duration_str": "2.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0654778, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.246}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.077899, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.246, "width_percent": 22.951}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.0836768, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.197, "width_percent": 16.803}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1144059468 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1144059468\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1046158013 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1046158013\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1935993444 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1935993444\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-410603375 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750987603196%7C96%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJidzg5d0xpWkdNeGgySFZwRmlXenc9PSIsInZhbHVlIjoiNERjZnpCOEFWTW85UnNtR09nMTZEU1daRzR2UGFjVFdhZ1BIQURNMzV0YUhPR1FFTU1PMVdOdFl2RE5rd3dHYmdjZnpCQTRRbUJ6TlE0NGI3ZWhQRksxbTNyYWozaURwaTc0S09TalNmMTBxS212SUpxRjZXcUVpQ2hhYmNpSmdRNFpuWmZWTG8vWEJtR3hmT01uc1lQZDYzK1hmWnRheEc1T2NvZk5lOHhCNlJBYno4NlczaFJFMFNxcm9EcEIzc01hK01xZ3JmNFh1U01ZWDNGUlVFRlBZejlyQXNpZ3ZmQnI4SEg5dzdDL2FyaVZ2Z2x6amFlVVhob3hHcyt5b2UvSWJ3SThsamkvZGJCbEh0NXQyVmkwQzhrdmNhVGZaOXpPUzVqWEdveUo1cHRCWUJQeGVDYzBBemNjYVBJOWREV2VvSk4wODYybUcybG1YY3hKaGh2Q0NUeGo5YXRpVmp5dGw3ZUJ2M3huK1l5b3ZSWldOQVBYaHVheWJOMnBtQVhLRG9SM3VRR0s0OERYdzNhc2NKQWl4dWFCNk5EelM5eUZGYVBqTUh1M3BNWjhqdnVhQldNUFBiVGlZRE1WcGVXcDJoOWljS0dOamFEamo5YXlCRG55R3g5dms3Z2pKUlBqYkdJTndTbHE0NWdIWFkycVl5MFJpdnpYcG1DMHYiLCJtYWMiOiJmODJmZTEyMThmYjJlMDBjZWM5MzgyNDNjYzFiZjVhNzU0ZTc5YTZhNzJjODUyYTU2YWM3MmZmODMwOGIzZDhmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii8wQ2FrMWl4empJOXFGOVhsZVFZRHc9PSIsInZhbHVlIjoiWnRKRHorN3pGN3grdGN6bm1ETUtwS2JlaXZHUkoxNndBaFAyZkJra2dZRWpEYlMzWGtvR0pDRDZrOWxzUVNrK2twaFBmQVZmcm5xSUhTelF5Q1pQcXRTMnp1UmRxZThZU1dHT1o2cWRBOUdValFvVzJWcGYvS1JBdEFhZUZ4a1l6TGxCOG1oV3BsSkNKT016UmZ2WnFhRGJhcHlvSjYyWUM4ODlqTGR1RUZKbzVsTTVLM1ZLWm5zL3ZDV011NDJXdUJ5RGkzaUhKVGtldS83bTNReXBXRThBLzJ0bGQ5TW5zUVZQYndWRmJ3ZWZxREszTUl1dGltZVJzbXpPcnNIQXNvYmZZZWRqODZJdy9YUkkvYzNnS3d3N09MMzgrYXVUK2xKazlHeVlHN1NickcwMTBDbHdwYW5iRkF3cHN6WWFGTTJHS0RRQjhQaGoyRnR2em1lRXpJNGlFZXVlYmd4OUpScGRtSW1VYTRMWkFOTnM4ZG5WTUUyZ1RYYVc5cFpJekpVeVRncHFHam5NNzNkQWU4bEQvem92VWc0SnVFa2trZzM4cGs3Nm5paE9RT1BEOGFlQ2xWSUFYREt0dDJhK1NpRG9ydGltUEhObFovZTk1OGFIQk1ZQkFlSFVwQnIzdHMvQlpyUW9oVEFMeUdyc0I0YlAwTzJEQVFJMmN2UjAiLCJtYWMiOiIwZWYyY2FhODEzZWM4Y2M4YTYwZWYyMjY2NTA0MmUyODVlYWY5OTcwZGI0N2E5M2MzZWFlNDI3MjNmZmFiZDA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-410603375\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-140519793 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-140519793\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-986934875 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:26:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpBeHFzclhPUUp0R1c3Z29taGVJNUE9PSIsInZhbHVlIjoibEEwWXd5WFh6TkdnVTRNc3hselNGT080NjNZdUpKbGlHWEtqNjR3c3c4aUFsQytUT1B0emlWN0g0WWdFaHlWRTBWV3NRSnpoT1d5ak5nbDh5TWZldGFYN25zRzVkYkkvUUxtNFVkRmMwYTAxQzBsMWg2UDM3SWtjWUpSSVpDa2p1UkJyQW85MEZlZUtOMGpvTE10MFZFeC9CSVFObXhUcHBzM3FTNVZBRzNMZTAwN3hqMlNWckpuMG56MFRBdWhmQk9nUGppUFZJZ04yU1RtaTN0WEw2M2tpWldGSUh2ZTQ1SVBKOTFxVkIyN01FQUY0Mm9PbUd6Wm5RMEJ2dEpKRXQ3cTZCRGZkT1RaalU2Z0tUMEE2SDFTZytod1dwVk12M1pMeUMwRmVLRWxnRmdpWnJnMkhvc0syN0puaUtoV2QwZnJjRGFjSUxVNk5UNGMzcEhITnJRTTZObUE1T3dxYjJLU1NuN3hTKzZPVjRBamE2K0xRa2JqeXJHa2FxdDdEb0hlVWVJR0VZOTRLRW9CWUV3R1Z0R2lpMExBaGFmRFQ1UmoxNzJONWV4bDZhdUhVZkZ5aFVlcTNqNXYxRzlCK2puMUkzZEZ1Y3V4LzVJS1kzMlNuUWlmNlJxbml5cFB4cGZOeis3WGJaYURDNzhVUDFOMGZMY2xkMWViR1U1bjEiLCJtYWMiOiI2MjMyODkwYzEzZTVlZTYxZmIwNmI1ZDExYmJiZWRkOGNkNmRkMDI0OWY5NmE5NmVjODQzNjY2ZDkzNzg3MThjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:26:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkVNaVlmT0lIMmVUYzhZajBPWGVEMnc9PSIsInZhbHVlIjoiWU9aVWtkS3hWOTZLOGtXQzJsYVdFSGpxcUFrMmUwU1hNcHFUWUZLRzMvd3p4c1NseWphYnE0TVZwdzBDMlh1eGt0U1RWTUR5QUZtdkE2MkxJOFUybDEwSUZCendVUTFYemxFdXVZa3hNaFNxT08xcnZ6aVVVbCt3WEt2YStDNDNMYnhQeFBmWUxDdnQwcVBWMmR6SHA0eDNSdk5BMlNsT0tteENubHpNTlV3UHRyOGdYc1BpNENtRDE0S05ZT01HZjZ6K0dvMmFEYlJZY2NYK0hWMHpQQ01Za29uUmt2d0ptb25paWtIWCt6N29uSkczWUllSnVWQ2R4eEF6QXNoTmhOVmdhRVlFNElqZG5lQjQ3RFBoaUxnMFp2bjRDWGFXakN1YXFVN2hXQk9hempYQU50dGxkb2ozNkhxY0VmZWN4RXdSRTc4dytQQzBpaGtnRHBBTUdsMXc2N3FqK05FUEdGRXljc29VM0I0SG93MzRROGJzNDBKTThtbVhGeUhDOHZoTnN6S2wwazNhandUVTFrTjhoQ1BtaHZuMkJoS09lZVFtem0wd0FOZFlORXQ5WHhDR2l4a2J5eFJyaUNMTlA1UXBXaytoL2hBbmVxWHdZOTJ2K05GRnZ0bU04QXNicE5VN3FnM0YvOVJJbUlhbzNVQmZmdmdraDZTUXYrVUMiLCJtYWMiOiIwZmM5MDQzMzcxNGU3YzQwN2M4ZDY5MGJlNDQyNmFiN2I0M2QzODY5Nzg2YWM2YWUzZjMzMzc3NGQzMGYzNTQyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:26:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpBeHFzclhPUUp0R1c3Z29taGVJNUE9PSIsInZhbHVlIjoibEEwWXd5WFh6TkdnVTRNc3hselNGT080NjNZdUpKbGlHWEtqNjR3c3c4aUFsQytUT1B0emlWN0g0WWdFaHlWRTBWV3NRSnpoT1d5ak5nbDh5TWZldGFYN25zRzVkYkkvUUxtNFVkRmMwYTAxQzBsMWg2UDM3SWtjWUpSSVpDa2p1UkJyQW85MEZlZUtOMGpvTE10MFZFeC9CSVFObXhUcHBzM3FTNVZBRzNMZTAwN3hqMlNWckpuMG56MFRBdWhmQk9nUGppUFZJZ04yU1RtaTN0WEw2M2tpWldGSUh2ZTQ1SVBKOTFxVkIyN01FQUY0Mm9PbUd6Wm5RMEJ2dEpKRXQ3cTZCRGZkT1RaalU2Z0tUMEE2SDFTZytod1dwVk12M1pMeUMwRmVLRWxnRmdpWnJnMkhvc0syN0puaUtoV2QwZnJjRGFjSUxVNk5UNGMzcEhITnJRTTZObUE1T3dxYjJLU1NuN3hTKzZPVjRBamE2K0xRa2JqeXJHa2FxdDdEb0hlVWVJR0VZOTRLRW9CWUV3R1Z0R2lpMExBaGFmRFQ1UmoxNzJONWV4bDZhdUhVZkZ5aFVlcTNqNXYxRzlCK2puMUkzZEZ1Y3V4LzVJS1kzMlNuUWlmNlJxbml5cFB4cGZOeis3WGJaYURDNzhVUDFOMGZMY2xkMWViR1U1bjEiLCJtYWMiOiI2MjMyODkwYzEzZTVlZTYxZmIwNmI1ZDExYmJiZWRkOGNkNmRkMDI0OWY5NmE5NmVjODQzNjY2ZDkzNzg3MThjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:26:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkVNaVlmT0lIMmVUYzhZajBPWGVEMnc9PSIsInZhbHVlIjoiWU9aVWtkS3hWOTZLOGtXQzJsYVdFSGpxcUFrMmUwU1hNcHFUWUZLRzMvd3p4c1NseWphYnE0TVZwdzBDMlh1eGt0U1RWTUR5QUZtdkE2MkxJOFUybDEwSUZCendVUTFYemxFdXVZa3hNaFNxT08xcnZ6aVVVbCt3WEt2YStDNDNMYnhQeFBmWUxDdnQwcVBWMmR6SHA0eDNSdk5BMlNsT0tteENubHpNTlV3UHRyOGdYc1BpNENtRDE0S05ZT01HZjZ6K0dvMmFEYlJZY2NYK0hWMHpQQ01Za29uUmt2d0ptb25paWtIWCt6N29uSkczWUllSnVWQ2R4eEF6QXNoTmhOVmdhRVlFNElqZG5lQjQ3RFBoaUxnMFp2bjRDWGFXakN1YXFVN2hXQk9hempYQU50dGxkb2ozNkhxY0VmZWN4RXdSRTc4dytQQzBpaGtnRHBBTUdsMXc2N3FqK05FUEdGRXljc29VM0I0SG93MzRROGJzNDBKTThtbVhGeUhDOHZoTnN6S2wwazNhandUVTFrTjhoQ1BtaHZuMkJoS09lZVFtem0wd0FOZFlORXQ5WHhDR2l4a2J5eFJyaUNMTlA1UXBXaytoL2hBbmVxWHdZOTJ2K05GRnZ0bU04QXNicE5VN3FnM0YvOVJJbUlhbzNVQmZmdmdraDZTUXYrVUMiLCJtYWMiOiIwZmM5MDQzMzcxNGU3YzQwN2M4ZDY5MGJlNDQyNmFiN2I0M2QzODY5Nzg2YWM2YWUzZjMzMzc3NGQzMGYzNTQyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:26:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-986934875\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-840620129 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-840620129\", {\"maxDepth\":0})</script>\n"}}