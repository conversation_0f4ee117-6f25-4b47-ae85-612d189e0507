{"__meta": {"id": "Xdd395524a2068e8d73718b6a29e50e0d", "datetime": "2025-06-27 02:12:18", "utime": **********.572842, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.103342, "end": **********.572862, "duration": 0.46951985359191895, "duration_str": "470ms", "measures": [{"label": "Booting", "start": **********.103342, "relative_start": 0, "end": **********.509213, "relative_end": **********.509213, "duration": 0.4058709144592285, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.509222, "relative_start": 0.4058799743652344, "end": **********.572864, "relative_end": 2.1457672119140625e-06, "duration": 0.06364202499389648, "duration_str": "63.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45734648, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029300000000000003, "accumulated_duration_str": "2.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.538852, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.068}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.550207, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.068, "width_percent": 20.478}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5563428, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.546, "width_percent": 19.454}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-354682597 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-354682597\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-885954950 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-885954950\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-372271979 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372271979\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-168862229 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990332098%7C1%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNUMUxHQmJhMFAvdlFmck5UNmZFd0E9PSIsInZhbHVlIjoiWThERkJaRkpZc21Ib3hmb1ZBbmJQMnZTOTcwSytYZkc0MERqb3puVGlzWkdYM0hxNnk1SXRQMm93V1YzNWZaU3J6WitJc0FjZHFBYmg1czlLWktBakpJc01MeHhUMTNHYWg4ZkZnSjBINWhBb1BJc25LVk8vS3Y1N1QzVHU1TGx1cWsrYUZibmd6cTluMkJubTI1V3JpOU5oL1VJTTMzNytLQnkxZVZOV3pSMytXNkh6RGRWVElRaDZKbi9EUW9mdFpJd0hwQkZVME5kbU4vcEViNzNhYkJleFdZQlVZT1hkdW5YZ3ZtaWxEb0xsNDlxUWU4ZlBQK0g4WTBoaWFUK2FFZ2c4Wm5ldlJ3VSs2R0d0d2Q4aDlvVzZXNkorMDRPK1pKZXh2OFhKWW1pWm0yUWdjWlN5dmFMLzBZSnZaYXhmVDJQYnZrZUg3bXpFcFEvcm44MlJRUGRSTW42TVcxczlpYWJlUkc0VnQ0YXNXVXlsSGZVU3ZvdXRuRWNUNlEvbEJPcHQ3UEh2WHEvMk9EczQ2bHB3dXc2VEtreGYvUzVwNnUraE5xdzJLS2l4d0EvSEd2eVBOdG5qMGN1VHk2UVB5OUduMlBkMGxNTEN2UmdsSW9hMUJoYkc5V2Z6RkZBT3FxbGt5WWFDNjlLV0lTcGd4eVJxSkF4MmhrbzVqWGwiLCJtYWMiOiIzZmVkM2U0YjgxOTYxYTYwNDBmMDAzMjcwOTY0MmYxOWY5OTEzYmY2YjI2ZTczYzAyZGZkNjg4MjJjZTBjMWE3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJIYjdKSHh1dkdObjJvMkxVQ1diUnc9PSIsInZhbHVlIjoieHBKT2ZwVHZvdVhNRlVSWkJGS2RxaE0xUzZvSEIzdkg5aTVaZmdRQzk5ZkNlV3ZyZEsxR1Q5RndZYWlsa3JqeTBoYTgxeDJ4SWkrd3R2VUZwWW40bTBnZmgrNWRoVWlmUVdTb21uSThwT0xnOTB1MUFrRjdKMHkzeFFZOUVJZS9WS1RJRG55V28xc3krTFRRMXNtNmhvTWZMckVJakpvMmxOMDhrWC90aVp2UDJxYXFxZ2MrZnFPckFLWThVZ0FhRVAvYzl3L1BkbTBHK1BvQUU5cjFCNG1wQjN1WmsxUDZIeDd1YUV2WE8zME5Qa0I3ZEdGRmNzWVlGc0drb21oejB1NEh6K0FlR0Vrd1FPTUE0RWtqbTI3VEpFRU9lTllwZTlLWFFZdk4wNGF3RTlGZElVdHgyejlRVkRGYlJpNlJSbDZuQ3l2QzA2Q2U0STh0RU9pTGloSnVSdVhINXp1Q1J5VUVJcWRIU1dZcHhqZEsxdmF6Ymhob3RPUktjcm13OC94UHFKdHdOQjhxYVRxMW8zekNDUnRyRnMxQjdaZGpnd0U1TkRtYTVnV1NRUUtjaWk4MmdsUHdqeVVzdTk1SGVmMFNMNXM5a0p4RStUWXhwV0QwWGpoK3JsMFhMRjJxbk8vZnR3aDhRWGJaMVRYbnk2SWRudXI3WkxvSEEyeFciLCJtYWMiOiI4NWYxZWFhYTg0ODJlMmZlODdmOTUzYjVjNDg0ODJiNWJkZWQyNTMzZDlkN2U4YjA2OGY1YTU3N2Y4ZmJhOTRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-168862229\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1102390875 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102390875\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-155286604 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhIQk9rRzVIdUMxaTJrcDlObGsvSWc9PSIsInZhbHVlIjoiYjNiWW1NN05lbWYySXVFdjc2VnAwTWZUazRpek1qYnU0VlZzNWNrY0tka21uWElhWkxhekFCYlVkWGF0dHZZQ2JYVXB1NEQ4U1dVU0Z4aExqYmc4dEFoVUQ2VGV0WmZqZ0YyL2VnTnlxOFNGbkRmZW5MamlhdGMyQXNBVFA0bGMvWGlWcERzZHI1T0htSlB5OTArY0crcTU4c0RrejRpaE03TC9oeFoxTE1aTlRqcXNGek9SdWxwRHpPYTVpY1NES09PSWJnWkMwRmZhYU5MSEMyU1VMK09peGtQdVY2TEptT3RiWEZseVA1VUxSNnFsV29ISWJhYnNOS2t0dkdmM1Vlb1Y3YUJwTGpDZ0dRSzJxNHY2Qm5tSGFyMjRwQVc3dWE5ZS9pd01TS0FJaWc5dnViaHd3SVpwMGJsQ01DNzdBZXJoMHRWYjl5UG9mdHQvQnA3SWptWVFpZ2xpWTRyWUc5UkFVTmlVL2JZYUVmL0JHYWdDd2x3SDVXektvdm9KazY4UUtnV3FNaWxEVkc2MDg1d1dBak9sc2QzYit5a1BEUHN3UWtCQklxcU9vaW1wR0NsRzE4U25lb0NwK1NGUEF0SzhYYml6R3NuVEV2TUplN2MxbTg2VlJrOWdwUzJsMTJCelpNL2F5dVB3Vk9TcStUZWVCbUVEN3NMOGJsN3kiLCJtYWMiOiJmODVlNjQ5MWQwNDM2ZDcyNzU1OGYzMGJjNDkxZjVjODg0YTE1Y2EyZTEyM2IzNmZhYjRkYWJlNzRlNzdlMjU4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImVCT1E0NVVmSHZ0dnpyYTd0UU5qZkE9PSIsInZhbHVlIjoicHlIN1B1VmpEeTRuQUxpUUtxSjRyem40Tzh1OXJrL05QMldCcVVHSWpOMUdDNUh5WlVlSURKbmR4SU9ZNVVPQmJleDQxUHB3RTIyMmhnOHNYbVhDdElWaW16dE5mK0hYRitGekpuc1EvZnZQNDFhMmVTVmNXSWxtR3hDYnh5Mkx1NDNneEJXMnpPdFNVcGxRQXBDZmNaTG8wd1Z0WXlqUjdEc0lMOU5SRXpUSDJjdGI0U29IN1NDbnd5d2V5ZjNtYlY3WmM2TDVzZ1Jqb04zMzhjcVN0ZlpCYll3dlV0UHlNQUxVRUE4bFo2SzhZWXFQVnpPK3dBZmJBZFROZWo2elhUUmR0dk1XZXVSYTU3VTRiS29zWE1OZ3VUVk5sWU1Qak5BRlF1VWV2NGlwbno3L0w0bTFGakVCSmo0MWZqVmlLWEk0ZFRGN2pSVEVKNlMxZ1R2SG85cFhaVytub1J4Wk8vRWZTeGdnTU43Y2wyVDh3YmFxTVVod0xHK2llbzlDdFdqMzFtVkxYZVNNcEdTTVM1N2ZMYnFhZ0xtTGlEOG1LR1IrNm9jY1dGQlFWMTZWZ2tJTmRxUXBIZENDaXFZa0g1RXV1b09qaks2QVZTVkV0Q09kS21STEJRVE1FVTlnNjNqc3ZVM1dKdG1lY0ZDNStpYjYxQVA4YnFoUm93K2IiLCJtYWMiOiI2MTUxM2I1MDg1OGIwMWUzN2NlZjJmNTMyYzQ2Y2I1MWEyZDkxZmQ0YmQ4MjEwZWNiZDUyNjM1ZjE3NTA1YTM4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhIQk9rRzVIdUMxaTJrcDlObGsvSWc9PSIsInZhbHVlIjoiYjNiWW1NN05lbWYySXVFdjc2VnAwTWZUazRpek1qYnU0VlZzNWNrY0tka21uWElhWkxhekFCYlVkWGF0dHZZQ2JYVXB1NEQ4U1dVU0Z4aExqYmc4dEFoVUQ2VGV0WmZqZ0YyL2VnTnlxOFNGbkRmZW5MamlhdGMyQXNBVFA0bGMvWGlWcERzZHI1T0htSlB5OTArY0crcTU4c0RrejRpaE03TC9oeFoxTE1aTlRqcXNGek9SdWxwRHpPYTVpY1NES09PSWJnWkMwRmZhYU5MSEMyU1VMK09peGtQdVY2TEptT3RiWEZseVA1VUxSNnFsV29ISWJhYnNOS2t0dkdmM1Vlb1Y3YUJwTGpDZ0dRSzJxNHY2Qm5tSGFyMjRwQVc3dWE5ZS9pd01TS0FJaWc5dnViaHd3SVpwMGJsQ01DNzdBZXJoMHRWYjl5UG9mdHQvQnA3SWptWVFpZ2xpWTRyWUc5UkFVTmlVL2JZYUVmL0JHYWdDd2x3SDVXektvdm9KazY4UUtnV3FNaWxEVkc2MDg1d1dBak9sc2QzYit5a1BEUHN3UWtCQklxcU9vaW1wR0NsRzE4U25lb0NwK1NGUEF0SzhYYml6R3NuVEV2TUplN2MxbTg2VlJrOWdwUzJsMTJCelpNL2F5dVB3Vk9TcStUZWVCbUVEN3NMOGJsN3kiLCJtYWMiOiJmODVlNjQ5MWQwNDM2ZDcyNzU1OGYzMGJjNDkxZjVjODg0YTE1Y2EyZTEyM2IzNmZhYjRkYWJlNzRlNzdlMjU4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImVCT1E0NVVmSHZ0dnpyYTd0UU5qZkE9PSIsInZhbHVlIjoicHlIN1B1VmpEeTRuQUxpUUtxSjRyem40Tzh1OXJrL05QMldCcVVHSWpOMUdDNUh5WlVlSURKbmR4SU9ZNVVPQmJleDQxUHB3RTIyMmhnOHNYbVhDdElWaW16dE5mK0hYRitGekpuc1EvZnZQNDFhMmVTVmNXSWxtR3hDYnh5Mkx1NDNneEJXMnpPdFNVcGxRQXBDZmNaTG8wd1Z0WXlqUjdEc0lMOU5SRXpUSDJjdGI0U29IN1NDbnd5d2V5ZjNtYlY3WmM2TDVzZ1Jqb04zMzhjcVN0ZlpCYll3dlV0UHlNQUxVRUE4bFo2SzhZWXFQVnpPK3dBZmJBZFROZWo2elhUUmR0dk1XZXVSYTU3VTRiS29zWE1OZ3VUVk5sWU1Qak5BRlF1VWV2NGlwbno3L0w0bTFGakVCSmo0MWZqVmlLWEk0ZFRGN2pSVEVKNlMxZ1R2SG85cFhaVytub1J4Wk8vRWZTeGdnTU43Y2wyVDh3YmFxTVVod0xHK2llbzlDdFdqMzFtVkxYZVNNcEdTTVM1N2ZMYnFhZ0xtTGlEOG1LR1IrNm9jY1dGQlFWMTZWZ2tJTmRxUXBIZENDaXFZa0g1RXV1b09qaks2QVZTVkV0Q09kS21STEJRVE1FVTlnNjNqc3ZVM1dKdG1lY0ZDNStpYjYxQVA4YnFoUm93K2IiLCJtYWMiOiI2MTUxM2I1MDg1OGIwMWUzN2NlZjJmNTMyYzQ2Y2I1MWEyZDkxZmQ0YmQ4MjEwZWNiZDUyNjM1ZjE3NTA1YTM4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-155286604\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-980361242 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-980361242\", {\"maxDepth\":0})</script>\n"}}