{"__meta": {"id": "X60aae4f3a0baf485a28cd56f91e124c9", "datetime": "2025-06-27 01:26:25", "utime": **********.717564, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.250483, "end": **********.717579, "duration": 0.46709585189819336, "duration_str": "467ms", "measures": [{"label": "Booting", "start": **********.250483, "relative_start": 0, "end": **********.661911, "relative_end": **********.661911, "duration": 0.41142797470092773, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.661921, "relative_start": 0.41143798828125, "end": **********.717581, "relative_end": 2.1457672119140625e-06, "duration": 0.05566000938415527, "duration_str": "55.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0035600000000000002, "accumulated_duration_str": "3.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.690758, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.348}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.702614, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.348, "width_percent": 13.764}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.709177, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.112, "width_percent": 14.888}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-613884587 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-613884587\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-24527060 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-24527060\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-908921943 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-908921943\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1007767186 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750987476327%7C94%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxSbUpaYjhJVWJJN1F1S0NwelR2dUE9PSIsInZhbHVlIjoiYjFsWUxzdllZaEJ2bi9jVU9GUU9tc3owOUhhSW05WUNLWGoxRW1ObDNwdUNReDdZRjUrUGpjcXVFZkYycUxRRVpCM1Uwa0pxRm9xcFZuc2RvdWU0Z0FMcFZrM29sU092TWtXOE1tSmg3UE5oUkdaVUFjSllOSksxYTBvanBQbVppZS9xYmdHZS9YTnU5blc3ZFg2cGJ3RkJlSGJEeUZZNkhpR2VlVkJIcXY2a2Iva2ZUOUZrZUJIaUhNMzlPYTc4U0NjRzdNbWFOQUNZa1hCVEE3NFppak9OV283QXR4YkpqY1lnUk96OE5BT0wzRkR2YlZaT2REcFNhL21pdGJYYWowVDRuUStEZENkK00wdThBQmlYenNEcFFaS0FVWlZLeU0vOXJJQmNYQ1laUGVNUUl1M2xDK2ZEaS9wdVp3U0tra0ZnWU1ySVVua1lMMjRzZ21NdWYxWjlySWdDSEh0S0NDeDg3VldKbEFkeGpjOEJGbWxUWVgvTE9JZFVLWXJoekZwaGswU0JwaDZSN1RMWXBvOVd1YTJhcXJyZ2xEeFRsbmUvMm5YTUg5SGs5TmNIb3FQdnpwM0orVUxEUVM0VSsycEtxeEhqR0dhazNBUmZNUGJWOVhOK24wM0pkK0dEWDNFTEtOVGVCQmZPdW1kNnF5L24zWVkvWUlHdEtTRzciLCJtYWMiOiI0NGE5MWZiNDA4OTdjMzAyOWZmNDQ1ZWQ0MjBmNWZkNjhiYTMzNjkzMWFlMzFiYmE3OWFkYTVlOTNlNjM3YmI3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZmZFJuTVVrQUJYUUtlVDF2Z2hwUlE9PSIsInZhbHVlIjoiVWdrNzdwc09sM0FsaGNpVTVLT2VMMk5IelJJSnNTVFdqVkZXUURHcjhUaW5mM2xNQnk2ajlnNFVnZjlTRHNsMzkzMStvWU1Ka1B5Q0o1c3FRay9xa2pvM2NZaWJneUdHT0Z5T3g4WWNPTldOQ1pjZUNHM3d3ejlDU2NxNFhaWXhsNSs4RFE4UU9kRzUyZzNBd1dGZ2orUi9DbXZyQ3cxRTBhTVlITWtlVkJQaUY3SWFvYi9TeS9lMWVReURXL0JKR3VCaHNva3NVeUdDRkozbFhsSVVtRW4xYW91MmdtNFVhdHJmZ01JK0phNnlxY0RpekVORFNhWDdRY0xQTlM0U1BBcWRzRGMyZUJpenZZaTFSSURLUGwxK2NGNEk1ek5CWmUvNGdRSFIxQUYwUTk3SG5UVmJoRUtCMndYclN5MVhhbUVLd01NeUhOc1Y3cmF4MWZ5aU5IZDJ4azhsZUMxWjJqZmJ6dUwzSU9OYXM3emRyUW9oR0h3cU0wOHhCNHNrRzlqYjJMOVRKbDRpUG5tUVZkNmFkc0J4ZFlHVWc1czNhNzg0Sy8xb1dCYTYyeWZNWEhGcTZwc2UwbzRXViswc2l6WkJlRWV0SDZReGtodlFVYVFQMXdZN1dFaDFybm55blhkcjczZlBoNWVJKzEvdllRRWNST1ZXZjU0YzV4VmUiLCJtYWMiOiI3ODNlMTQzYjk5NjcwMmUyMDFmOWI5ZGMzNDg3MjI4NWUyYTUxNTAzY2U2YjMwNTkwZmI3ZmUzNWZjN2JkN2VhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1007767186\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-727430705 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-727430705\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-834660463 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:26:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFuY2RmUFk2VVhTRXJ5U3hDM1hPQXc9PSIsInZhbHVlIjoicm9vNXo3bzhkbDBna2I2VGtCQjBnVkhkTFllcjh0TzloRDd6UDNSVlVLYWNaQnJvZTJDWkF4TGk1UjE0aTRFTS9IWkRFV1BadXlvY1BsVmh3VWNSMldSZU1BeGRBUUJMYXgvYkhha016L00wT2ZWbWFJN1NaYzkxR1JaVDB0ampRbTFQUjZBTUkrTVRGUTNPekthcmU1WStrZVh5Y0ZUb1dtZG8xRGpMQ3lHYlVXKzgyY2VwM3lhQmlTemxndFB1UHJyWDl6cW40bWVTcjYxcTROMEk4MmRsaFMzekZaNlJndG16S0tKMTVCQVp4TW1hMUw4blArY092OHl1cW8rb3VkdVdwR2VxS3pjTEdyVVNTcEphZkN1TEQ2SEh5S29OTU1qMkdSbk9GaEtWVW9oV1pBdmhlNllPQVRvM1g3UEVSdndmTkFZRHYwVHNNZE8yT2hyUGtUY0lFa21jWkorNWJhWDJpaTVYb3pFT0JpbThHdnljdzFUeW5uL1ZNUDJlRWRaQ2RjMTh3bGFTOXUrT29aR1lrMTM1NllPNjY2bzkrdWlwMHFWaWRrRGk0bHJwd2hMUFNQQi84M1ZMTUVVZUE5SVcxQlVXNHA3bWZ0TlVyQTlmSVE0MSsrd1UvbmtiYmdKRE52bFAzR0FQeFlDaXBkcEJmZWpweWlsVHRXdEwiLCJtYWMiOiI4ZDZjN2FhMDg3N2U5YzkzYmI2Y2MwNmFkOGEzNmQ0ZWJiYjFkZjI4ODU0NTBiMTMxOGI3MGUyNjFhMGY4ZDAxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:26:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNYZ3RpQ01hVUMzeElRWHVVdzczdEE9PSIsInZhbHVlIjoiZnRJZVU3VnVsU2xnZ1AxbG1nTnpRNVAyWkRyQm94aTFZTGRtUHhoM0g1NmIvbWs5NkcxcUlPUkszOUdJTDBzWVdKMnlzTjEzbXBRZ2dNYlI0OC9nVnd4djVkUG9kdldCVjM3U3psVjZWd1ZOdVc3UUhTTWIrdVUwbmQ5cVI2UUhHaVNQSDJBWENqT0Irb3hqZFRzYjIvVm1qejBTSXhyQmo2SjJiU2ZZTkYwak9rQkJlZ0lUY1ZodnhQS1ZUZ1ZUeVlKTmgvaW05a0hVZUVoR2tSSm1XVVVFR2NsWFFRbVFTazVzR2owRzN4WVZkWkMybDdTUGVCZGFWd0FET05IblFwZ2ZWYkh1bXpVQ3IzaE5XYVlDTWI1MzdEVjAwMmRqUWpHdTc4aTJOME5wVE5UQzI1WUpLckRzM1M1UElyVld5SzM1RlZ3dlFXNThQUlhpUUY0a2NGamxkOU1vVkR1Q2JZcVlna0U0YlpydEFNaGE2dlAxazJoREtaazNDZ3JUT0xuK0ZwTG53RnI2N3pEcFBlYkluWG0ybVpZbDVRUXg1bXZ4VUE3cXdsb2wxa2R0SVRreFJpQ2QvSjJ1bHZxdTJqOHBscWNTV0hmcmdVdWRhZXhYMHV2R3hTZXZLRDNpaWRmWXYvUm1EckVUKytmRjBoVFFraThRNEpyV2xIa20iLCJtYWMiOiI1YTZhZjkzNjkwN2UwMDNmZTMwZDE1ODNiNDI0NGFlZTdkNDhjZDE0OGY1NTlmMDVmNDM3NWVjNzZiMjMwOTFhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:26:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFuY2RmUFk2VVhTRXJ5U3hDM1hPQXc9PSIsInZhbHVlIjoicm9vNXo3bzhkbDBna2I2VGtCQjBnVkhkTFllcjh0TzloRDd6UDNSVlVLYWNaQnJvZTJDWkF4TGk1UjE0aTRFTS9IWkRFV1BadXlvY1BsVmh3VWNSMldSZU1BeGRBUUJMYXgvYkhha016L00wT2ZWbWFJN1NaYzkxR1JaVDB0ampRbTFQUjZBTUkrTVRGUTNPekthcmU1WStrZVh5Y0ZUb1dtZG8xRGpMQ3lHYlVXKzgyY2VwM3lhQmlTemxndFB1UHJyWDl6cW40bWVTcjYxcTROMEk4MmRsaFMzekZaNlJndG16S0tKMTVCQVp4TW1hMUw4blArY092OHl1cW8rb3VkdVdwR2VxS3pjTEdyVVNTcEphZkN1TEQ2SEh5S29OTU1qMkdSbk9GaEtWVW9oV1pBdmhlNllPQVRvM1g3UEVSdndmTkFZRHYwVHNNZE8yT2hyUGtUY0lFa21jWkorNWJhWDJpaTVYb3pFT0JpbThHdnljdzFUeW5uL1ZNUDJlRWRaQ2RjMTh3bGFTOXUrT29aR1lrMTM1NllPNjY2bzkrdWlwMHFWaWRrRGk0bHJwd2hMUFNQQi84M1ZMTUVVZUE5SVcxQlVXNHA3bWZ0TlVyQTlmSVE0MSsrd1UvbmtiYmdKRE52bFAzR0FQeFlDaXBkcEJmZWpweWlsVHRXdEwiLCJtYWMiOiI4ZDZjN2FhMDg3N2U5YzkzYmI2Y2MwNmFkOGEzNmQ0ZWJiYjFkZjI4ODU0NTBiMTMxOGI3MGUyNjFhMGY4ZDAxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:26:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNYZ3RpQ01hVUMzeElRWHVVdzczdEE9PSIsInZhbHVlIjoiZnRJZVU3VnVsU2xnZ1AxbG1nTnpRNVAyWkRyQm94aTFZTGRtUHhoM0g1NmIvbWs5NkcxcUlPUkszOUdJTDBzWVdKMnlzTjEzbXBRZ2dNYlI0OC9nVnd4djVkUG9kdldCVjM3U3psVjZWd1ZOdVc3UUhTTWIrdVUwbmQ5cVI2UUhHaVNQSDJBWENqT0Irb3hqZFRzYjIvVm1qejBTSXhyQmo2SjJiU2ZZTkYwak9rQkJlZ0lUY1ZodnhQS1ZUZ1ZUeVlKTmgvaW05a0hVZUVoR2tSSm1XVVVFR2NsWFFRbVFTazVzR2owRzN4WVZkWkMybDdTUGVCZGFWd0FET05IblFwZ2ZWYkh1bXpVQ3IzaE5XYVlDTWI1MzdEVjAwMmRqUWpHdTc4aTJOME5wVE5UQzI1WUpLckRzM1M1UElyVld5SzM1RlZ3dlFXNThQUlhpUUY0a2NGamxkOU1vVkR1Q2JZcVlna0U0YlpydEFNaGE2dlAxazJoREtaazNDZ3JUT0xuK0ZwTG53RnI2N3pEcFBlYkluWG0ybVpZbDVRUXg1bXZ4VUE3cXdsb2wxa2R0SVRreFJpQ2QvSjJ1bHZxdTJqOHBscWNTV0hmcmdVdWRhZXhYMHV2R3hTZXZLRDNpaWRmWXYvUm1EckVUKytmRjBoVFFraThRNEpyV2xIa20iLCJtYWMiOiI1YTZhZjkzNjkwN2UwMDNmZTMwZDE1ODNiNDI0NGFlZTdkNDhjZDE0OGY1NTlmMDVmNDM3NWVjNzZiMjMwOTFhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:26:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-834660463\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1468624857 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1468624857\", {\"maxDepth\":0})</script>\n"}}