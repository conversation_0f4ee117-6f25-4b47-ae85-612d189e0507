{"__meta": {"id": "X410a89f18ef7b604580ab7eaf2103f2a", "datetime": "2025-06-27 02:12:24", "utime": **********.734553, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.260804, "end": **********.734571, "duration": 0.4737670421600342, "duration_str": "474ms", "measures": [{"label": "Booting", "start": **********.260804, "relative_start": 0, "end": **********.607631, "relative_end": **********.607631, "duration": 0.34682703018188477, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.60764, "relative_start": 0.3468360900878906, "end": **********.734573, "relative_end": 1.9073486328125e-06, "duration": 0.12693285942077637, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50176128, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.061669999999999996, "accumulated_duration_str": "61.67ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6375632, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.708}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.647512, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 2.708, "width_percent": 0.665}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-27 02:12:24', `closed_by` = 22, `shifts`.`updated_at` = '2025-06-27 02:12:24' where `id` = '25' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-27 02:12:24", "22", "2025-06-27 02:12:24", "25"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6580908, "duration": 0.055729999999999995, "duration_str": "55.73ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "kdmkjkqknb", "start_percent": 3.373, "width_percent": 90.368}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-27 02:12:24' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-27 02:12:24", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.716363, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 93.741, "width_percent": 6.259}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "success": "تم إغلاق الشيفت بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-886002090 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-886002090\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1867582485 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1867582485\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1937227487 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">25</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1937227487\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-849558565 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990342712%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJrMHZocGsyR1BnRHJaL2dlV3diNnc9PSIsInZhbHVlIjoiL0pqV3Y1NTlUWTZRaFZ4ODFaRmkxck44cTV6ZDd3V1lvMnFpT2ZXSHFVV1ZSNy9JOVN4SjdxTzN4WTVDaTZDSDJZUGJWc2tVV1M5cnJOTXNPSVowU0lMSk9WcDUyTGp3eXZIbUx4YUN5ZTcvU1BVVjc5SzluMkd4cjRuZXA0NGU1cDBPOFpGR3VMdEpuZFcwRGtmR2VqWmR3bHlkTEZsdGpqeGcvWkVpUFpqWVVnT0RBZTNxYzcwN25EOStmYjRIbWJxNEdQMkNRMmhUc01TS04wSUVkOVBHSEIvTE9JS2Uxai9BTUFsTERwZk8rMW9PSUhaeit1eERpN3BDZmk0SzFsQXF1N3M4YkVZNkhxbXFraVdOUUJlRzd5YUJGZzBZT1hSZkFPNE5BSUhqNFhkUEZybE5RNG1zSkVWNEdkU3ViRXIraENud2JkR1dicWs0RmFmeitrOExyRUNrbGIrSjVianZqamdzdHZOS3U4VWlPeWs4VzlvaXVhbGJvZDI2NDZteEZ6QlRLQU5XSkYwNlJIZWxxMUtlMFhTWnppZ3dUUmtZc0E1aHRjcms5YkhVRlhLMXNBYWVRQStYR3RzNmFNRzU2YUc0UW1CU0s2WGhWTWpXRGF4WkFlK2xMVUFHdFhxUC9jUW5hQVRMRVdBQnByYzFrZXlucWgyMDhoeXkiLCJtYWMiOiJkZWM3ZDQ0YmU3MzllMWRmMTM0NWY1YmEwNTZhODBiNTNjODZhYjJiMWYwNDFlN2Y5ODZhZjk0YjJiNjM0ZjFjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVVN3ltbnFRVGVjc24rZ2NrT2RZbXc9PSIsInZhbHVlIjoiWk1pRndQS2RMcEc4c05oMVJablUvMUJnWDNqMGczZC9Zblc5cEMvMlZMSkk0R1pwZE9qQVVtZmxvcHh5WHNIZGlqSVlsQmdlVkZVdGNESjJvZ3BQMXA5MzZmSHdCZ1VoUDVROWRFckYvaVQxRVdFdXRhUVFuOHdQeVRkcUJzczZiaElTNU1sV29QblBpcXFuT01OcXNPZ2YxekluQkdOZzBqY2J4bmRxMmtBbWhrbUJXOXJQTXlRVlNQdW9uNUtxRGhSMG43ajhZU1h1aVU4T2ZDaUlyNFN0KytvM05jcTlod3JZeU5nVWNmd0dnR25pa2JZY1JscCtuTW9yNy9vRStqakNkTXVndnRLRDN6WUI5U210SHlLUXlpMUZSM3BpZVU0T1N2b2hnWVlyQ0VZdUU2R05kT1pQRjJjUlNkOGxCcTdJblhTWTY5V0FRZlFBWm15N1Q4d1N3dEhWKzdZVmxqYjRSZVd1U21FSjh5MHlHeDc2cjJuRDM0TFlNNHYzK3drSTVUV3FDQzhYTEkvYThpRFZINFJMcFBWbzUyOHJ4eE9kYkNtRUdlV2NDSVM3Z2R2VUZzeUVlSG1PUFN0UWhlRW92NlhEb3pRa0YwbmRiMDFZK0xhUnk0ZmNEUEZpaWVKU3FyWFgxYnhBRE9vMXVSMXdldG9uVUNsc210aGgiLCJtYWMiOiI3MTczNzk0YTc5NmJiMGRmYjIyNjAwMTZlNjllYzI0NGE1MWI5OWJhMzYwNDAxYzQ5MWY2NTNkYWEyOGYzOTA1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849558565\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1247009079 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFLMXlwanBNeFZJdmRNc2ZVRUFYNUE9PSIsInZhbHVlIjoidTBEeXpGYnlZcTJtR1UxalBBTVM0cjc2ZVNIaWxFNS9rNEFkN1hTWkw3aWJXY3VaZmEzYTdaNEtFRjBnL2dzMHFBRWlsNU9IclFPSHpkc0E1aTc1MWVneTFNa1RXZHJ4TldDY3U0WGxZZG5HRjlmWGpjY0ZPbGVSbmtnYmxaTzc2NUI2czlxb1EreWoyeTExTmNxbWM2NWZkUlFrb0FqT01PRndaMnZudlhSNXlZNW5MMEFBdExDeHNYOEZ2UStaSHVDMUV2N2VPMXg2SUR2Zk9HT0NGZFB2VFo5Vk4wK201YldTTGx3dDczZkVNeTlhdk04Uzl0amxtTmE0Wi80cE1CNWxYMTBUVWIwci9FL1M5ZnVOeGgrRWJWWlA1RWExQzdMQytvZFNpK2dMVEc4RDZMV1JUTVRzTkIvV1lFQzI1b2pFUkhHNjNaQmZXSU1mTVJWSENXVEhIUHNVZkhjR2t2Q0RPeFExWG5wbTZaR2UxR290d3RUdzg1V0FSNThVUnRnbmx6SUhkYml0VzZwRjUvUXBORVhNem0zUEU0WDgvV1VsTlpQR21QVzBGdUtKYngzRXhmbHNBbnE1TzVjenFHWDQ4RXkrNi9LbGNJb3VZU1RaaG5HZjYrWG5NNUI2c1I1RHBJcHc2RlBLZ0RwRURJZ0lUQWhDS3FPK0J6VkkiLCJtYWMiOiI2NjkyNjQ0ZGMwZDg0N2I1Nzc5ODIyYWY0NjgxMDczZGE0M2UxMjJiODU4MDEyNzZiMmZkMzFlNDY3OTRiZGQ0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ind3QTlJb0J1ZEtnM05hNmhBeEJXRUE9PSIsInZhbHVlIjoic2E3VlZlcmVMcjVBaGNYeTgwSkdiWmsrUHlENVdVSU5sc2tMKy9DQ3pZZ0ZMbnZZdkJXVGVuRjl5N1c5RksvUXdYVlVJVjdPelZOaXZOYk1WQ2hKVDlGK1pHR25malVnbk1sQkVVVEUwR3BRVlJPd0FzcEZ6cE5FdXZTVWtBc2ZPb1c4em9KSFFuY3E2RHFpM0grUmFJcnlod1RtUkJmNW14WENiQzd0T3FhSHVURkE5aHdlYXMxU1hoMDdZSTVNUjhuWUx5cWZLWVpyZnJPRnh0TkJoV3U3RCtDeFJMQUlFSW9OYUZGdVA1WEp3ODhHbDR5cWRLejh6NzVodzg3MXhqaHZ0R3hWZ0JMTmJpb3gvakFTcGg2cHk2S2dCTHpXOWZLMEgzMFhWcEZHa1R0aVFFVHRqYXNoelV5UVZTOTRNazlQYmxqQ1hFRzVrL0ZXdHJnTEd2Ykw2V3RvdVVvS0xuZzlHd1FYRUxod2h5TjFENVdGQnV3cWtsd3kzWTQ1c1NmRnVyNFZ1RG9YWHVOQ2tGMlc2WnR1RUZBcS9QRUNZNmxrWGtWWEZ3bUdxZFhoWmcvbG5NRkMvRUI1S2dLNVlFMFFvRmhmUVdOMFBVWUpCTFBZdk93Yngyb3dvcGFBbU5VT0dMNWE5R0FyRGpDUmRidlU2OFN1SHlTQmVZWTkiLCJtYWMiOiJhNzIxMDg3NDgzN2YwMjY3MjY2ZWQ5NDg5YmIxOGZlMTY4MzY5MGQ1MDc4OThkYzBlOGE5NmQ1NWYwMGU5MGYwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFLMXlwanBNeFZJdmRNc2ZVRUFYNUE9PSIsInZhbHVlIjoidTBEeXpGYnlZcTJtR1UxalBBTVM0cjc2ZVNIaWxFNS9rNEFkN1hTWkw3aWJXY3VaZmEzYTdaNEtFRjBnL2dzMHFBRWlsNU9IclFPSHpkc0E1aTc1MWVneTFNa1RXZHJ4TldDY3U0WGxZZG5HRjlmWGpjY0ZPbGVSbmtnYmxaTzc2NUI2czlxb1EreWoyeTExTmNxbWM2NWZkUlFrb0FqT01PRndaMnZudlhSNXlZNW5MMEFBdExDeHNYOEZ2UStaSHVDMUV2N2VPMXg2SUR2Zk9HT0NGZFB2VFo5Vk4wK201YldTTGx3dDczZkVNeTlhdk04Uzl0amxtTmE0Wi80cE1CNWxYMTBUVWIwci9FL1M5ZnVOeGgrRWJWWlA1RWExQzdMQytvZFNpK2dMVEc4RDZMV1JUTVRzTkIvV1lFQzI1b2pFUkhHNjNaQmZXSU1mTVJWSENXVEhIUHNVZkhjR2t2Q0RPeFExWG5wbTZaR2UxR290d3RUdzg1V0FSNThVUnRnbmx6SUhkYml0VzZwRjUvUXBORVhNem0zUEU0WDgvV1VsTlpQR21QVzBGdUtKYngzRXhmbHNBbnE1TzVjenFHWDQ4RXkrNi9LbGNJb3VZU1RaaG5HZjYrWG5NNUI2c1I1RHBJcHc2RlBLZ0RwRURJZ0lUQWhDS3FPK0J6VkkiLCJtYWMiOiI2NjkyNjQ0ZGMwZDg0N2I1Nzc5ODIyYWY0NjgxMDczZGE0M2UxMjJiODU4MDEyNzZiMmZkMzFlNDY3OTRiZGQ0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ind3QTlJb0J1ZEtnM05hNmhBeEJXRUE9PSIsInZhbHVlIjoic2E3VlZlcmVMcjVBaGNYeTgwSkdiWmsrUHlENVdVSU5sc2tMKy9DQ3pZZ0ZMbnZZdkJXVGVuRjl5N1c5RksvUXdYVlVJVjdPelZOaXZOYk1WQ2hKVDlGK1pHR25malVnbk1sQkVVVEUwR3BRVlJPd0FzcEZ6cE5FdXZTVWtBc2ZPb1c4em9KSFFuY3E2RHFpM0grUmFJcnlod1RtUkJmNW14WENiQzd0T3FhSHVURkE5aHdlYXMxU1hoMDdZSTVNUjhuWUx5cWZLWVpyZnJPRnh0TkJoV3U3RCtDeFJMQUlFSW9OYUZGdVA1WEp3ODhHbDR5cWRLejh6NzVodzg3MXhqaHZ0R3hWZ0JMTmJpb3gvakFTcGg2cHk2S2dCTHpXOWZLMEgzMFhWcEZHa1R0aVFFVHRqYXNoelV5UVZTOTRNazlQYmxqQ1hFRzVrL0ZXdHJnTEd2Ykw2V3RvdVVvS0xuZzlHd1FYRUxod2h5TjFENVdGQnV3cWtsd3kzWTQ1c1NmRnVyNFZ1RG9YWHVOQ2tGMlc2WnR1RUZBcS9QRUNZNmxrWGtWWEZ3bUdxZFhoWmcvbG5NRkMvRUI1S2dLNVlFMFFvRmhmUVdOMFBVWUpCTFBZdk93Yngyb3dvcGFBbU5VT0dMNWE5R0FyRGpDUmRidlU2OFN1SHlTQmVZWTkiLCJtYWMiOiJhNzIxMDg3NDgzN2YwMjY3MjY2ZWQ5NDg5YmIxOGZlMTY4MzY5MGQ1MDc4OThkYzBlOGE5NmQ1NWYwMGU5MGYwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247009079\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1073713026 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1578;&#1605; &#1573;&#1594;&#1604;&#1575;&#1602; &#1575;&#1604;&#1588;&#1610;&#1601;&#1578; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1073713026\", {\"maxDepth\":0})</script>\n"}}