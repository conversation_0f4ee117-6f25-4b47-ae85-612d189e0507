{"__meta": {"id": "X4f4b3f1d9b0779b9c560135382e24fbf", "datetime": "2025-06-27 02:27:21", "utime": **********.274571, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991240.827577, "end": **********.274586, "duration": 0.4470088481903076, "duration_str": "447ms", "measures": [{"label": "Booting", "start": 1750991240.827577, "relative_start": 0, "end": **********.216441, "relative_end": **********.216441, "duration": 0.38886380195617676, "duration_str": "389ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.216452, "relative_start": 0.38887476921081543, "end": **********.274589, "relative_end": 3.0994415283203125e-06, "duration": 0.05813717842102051, "duration_str": "58.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45720304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00298, "accumulated_duration_str": "2.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.246089, "duration": 0.00183, "duration_str": "1.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.409}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.257151, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.409, "width_percent": 18.121}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.26454, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.53, "width_percent": 20.47}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1010823080 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991174265%7C33%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldBQUh1VEtjS2djck9RUzk3RUFDT1E9PSIsInZhbHVlIjoiNG55Um9NSjNiR2RDTy9SUkFBbTl5Ym5DVm9UdWJ4dTdxQ1NmaUJHY21paFB1MFdBYStPL1dSVmhtalZrY0RFRzg3ZnhFOWFoaEk4QkFsSzk0N0czWXVUYU1tS0tISGhIMGFJTCtoT012T2xNK1JzYVNMb0QyMFlEdUZCbGlla3VqaDJoYk9RNHI4eGZUc2lpZ2hVRTcvTVJ6UVpUTUphczIwYUVCRkJFRWJXdnFNYlExK0IrOFBielowUUJCdUppbWNKdkhUblM3bEhScHIvWklJTFBIbmgzZUVYSzFXTUlud3QwNnJ6ZmdiWXdYY1pqeFE3K1QxYkNNZURWdVpGMWx6NGtCM051L0pBZ3hRZ2hIOCtIdTJIZVFsOHlSWjlJSWJVQ2xMWHh4L3ZOTHpSTElxbnJkSUNBT1pMMENBZDkwb1VHOFkzMm9yYnpNM2wreXhCbE5YWVc5SVJia0VaRkN1UzY3dmVsWTJHbm84K2w1K2dTb2pJeFdHWk0wcG1lUEwxUWZjbkFEUndFRFd0SnVHanZKRXJBSGp3VFlhdi9oTmhGaW1kMnVoU3B2VUFzYXY0elBzYVpDNm9sMmpQY21sdE9XKzN3YjVDZHRUd09VN3A1VGJYZHBsWG80dkdFaktwdXphaFBDQmNYZHg2dlgzZnNvNUo1dFozQ1grS3QiLCJtYWMiOiI5Yjg2ZjlmM2IzOGEzMWJlYjg4YzkwMjkzM2U2YmIxYmNhYjcxYmU3YTFiNDFjYWZjZTM4OTdlYzUwZmYxMDY2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Img0cHB0cEVLeVM1YjRyMkhSYXNnSHc9PSIsInZhbHVlIjoidjRkNVRCWFdxVWJqVlZTT0kxbVFPVmVTSFZoS0RQOE56RTlmK2tFZmltbGNHTWIydldiWFRFeVhGaUlFRFhvZGpNMVpqTDQ0VTdFR3gxTndxdjNZNEh5S0FxV3Fma0RzdXpleDJ5K3VYcTAyWEN6NlJOQlhHVnhDSlREZWJLaVpqQ0tNb0tSSEZWWklERG5BNENBVUY2WGNYTjI4aVcwYzMwNVBZbEM5QUhqaExRUWhyUTZ3U2ttMjZxYU42c3RoaGFjSmRzbU9lWG5ZTHFQSkVVcTRtVGs3TXRrZlkxZUxMZjdYdHN3cXQ4aXlndFhCanI4MW95bzdVdG9MQ1RQTnAvNDk0Q3ZtNmhPcTlNNkRsUXR2VmVCTVBqSUVLTThrYkNkMGxMUkRLQUx2eDNBdG45NUdFdUg0aHp5RjAyV0ZDOEtqSklJYUc4eVdxN2J2MmsyM3JvR0dQUXFreElISExqLzU2UFk4NVhxTUV2aDM2Y0JtZUNUa1BiWk1NTE1LVldsbmdqanFDOExWOVVuNWxITTZBNVJHbWJhd3FNTUtseStTdDBWU2N0b25nTGpiRGxnaUtoaXhmVGN0THJ1Rm1OMGExamN5R1ladDVoWFNEaTc3TC9OV1lHeDNiNjcvMDd4K3d3S00zNjJaQTdxRlFncUViamZ2eHhxbjNiSEIiLCJtYWMiOiI2MmE3OTRhYzBkNDRmZmVjYWVkOTU3YzVlN2MyNmE1OGI2OWZlZTU0NWFkMWZlNDkwZTQ2MWUzMmM2OGRmNDg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010823080\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2051313637 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051313637\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2015466388 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImcrNlUzSU1aQzF2OFM5OWdUZmR5ekE9PSIsInZhbHVlIjoiRmk4Nk1Fd2ZYNUtrYU9oL0tKWmRFM0gxZFNNSVJjazIvQmRmODNJU3lPRURxS3hFdXUwak9jL3pCZDVHNXpPL2h4UVIyWWFnWjk3eGwyaWRLUGJWZEdreis3eSt3KzBrUzZNZnBvMFBac1BVRmdnQk5yMjIxWjQ5RmtKbWZMWnZMMEVTQm9ZTUJHSU1KNGFhN0V2dVVvK2RpTENIN2MzUWVuSzRFcnhucUIwckc5WXVFRjJuOU9wYk02WjN6N0tnQjFxZklYSzV4Y1dCTkF6dUFGdS9URXl6eDNGYm8ycXFwUWd4TWVYdmY5VDhHZzl1MWRRamVRNnpkSzVTajdDZWlzeDZjb2V2NVBLVytvcG5MOFZIckNPcUVZOVIxczNqWTh2ZG5lWHdHWjl1SXd0eFBaVUFkOVlaN3pKZWF3dUcxQUhvNlpTanpab3AxN0Zyd3hhWGRBUStkODFMQUZHTGhmWWpiYXZwcUVqbEIzM0FEQ3E1RGNVaSswK0o3UStNNFJLTnBybEFkQnhGdHRBNjhmTk9BMUt6MnJmOXNRSVBsbko0QmRlQ01GbG1GcDhMdkhibGpoZEVGYnhFVnBvemdBcjZpd1hpM3ltTSt4TURuVEs1dGpUczFxNWkvS1dSeDYxRm83Q09NVG42Tk9aYTNYUklRMVRYSXF3eFFoRmEiLCJtYWMiOiI5N2JiYTFlZmM1NjI1YjNmM2FkYWIwMmExNjUwOGNhMzUzNzgzNmJiYWE2ZTk0M2JjN2NjM2YxN2QyNzNiZDJhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InN3VEY5bGQzdWJNOFpvVElJNlUxZFE9PSIsInZhbHVlIjoiV1BVQWxOOXVLWGNLYnlMU2Z4YWpJTGlMem5kbXBGTzlkdkhlWFo0azVsS1lVeExGMFhETWRET2VRWCtJV3NlWmxjWE9YU3dkS2Y0MW9tdlJhOVI3dFZLdXZFcktsSFd4Y1FmaTI2djRzdUpLSnNRVVJFSE9XQWNyNE9iamlWcktYQjFJMW01OHpqcmlwaUR5OUp6aEdUWEd3bEV0Q1RyeXVzTUdZYTdvSndaM1hodWNPTkttcHJTZFVUVHN6MVdTWmxsdThuZDg2Z3pVVklVOCtUeUpMY1RFbHZZSWtzSzEyWUt5d215OXNVNTU0WXJxTlRXeGVXOUxhS2E0U0dZbEJrSEZrbWc3WmpHbXdZMGZKdE5hcEhGbzl4UzFqNHk1ZVJYRS81cUhRcEc4Qkdxa3k3Y3ZKaHVqS1FYanp5WW1DeS9FZTRtWW1VRmFzMTNZbnNQQ25ZUWc5OGhRTU5xb3A4dy9GZlFLQ2xBOUhiaW1XQ3VpcjdNZHI1cGRnc2xLY3dGTDFWbTF2eUF2aXhDM3lzWUR4T0VuR2ZOWUZaTXRHdW1SRXY4a1A4QXVZWmFSRUlCMFJoNTFvbkdpbEJGSmtPMFcyckgvYTBQckYwNVZxQUpFN0krSUtyM1M1TWxORnhZY1pvaEVKODl3WlVOays5cERDUVJiemExMHRGNVciLCJtYWMiOiJjYzM0YjM5NTJlMzBlOGU1YWMyMzNjYjZkMGM0MzAxOWJlNTY4NjVhMTMyZGZiN2FiY2UwMGRiOWNhNzA4Y2M1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImcrNlUzSU1aQzF2OFM5OWdUZmR5ekE9PSIsInZhbHVlIjoiRmk4Nk1Fd2ZYNUtrYU9oL0tKWmRFM0gxZFNNSVJjazIvQmRmODNJU3lPRURxS3hFdXUwak9jL3pCZDVHNXpPL2h4UVIyWWFnWjk3eGwyaWRLUGJWZEdreis3eSt3KzBrUzZNZnBvMFBac1BVRmdnQk5yMjIxWjQ5RmtKbWZMWnZMMEVTQm9ZTUJHSU1KNGFhN0V2dVVvK2RpTENIN2MzUWVuSzRFcnhucUIwckc5WXVFRjJuOU9wYk02WjN6N0tnQjFxZklYSzV4Y1dCTkF6dUFGdS9URXl6eDNGYm8ycXFwUWd4TWVYdmY5VDhHZzl1MWRRamVRNnpkSzVTajdDZWlzeDZjb2V2NVBLVytvcG5MOFZIckNPcUVZOVIxczNqWTh2ZG5lWHdHWjl1SXd0eFBaVUFkOVlaN3pKZWF3dUcxQUhvNlpTanpab3AxN0Zyd3hhWGRBUStkODFMQUZHTGhmWWpiYXZwcUVqbEIzM0FEQ3E1RGNVaSswK0o3UStNNFJLTnBybEFkQnhGdHRBNjhmTk9BMUt6MnJmOXNRSVBsbko0QmRlQ01GbG1GcDhMdkhibGpoZEVGYnhFVnBvemdBcjZpd1hpM3ltTSt4TURuVEs1dGpUczFxNWkvS1dSeDYxRm83Q09NVG42Tk9aYTNYUklRMVRYSXF3eFFoRmEiLCJtYWMiOiI5N2JiYTFlZmM1NjI1YjNmM2FkYWIwMmExNjUwOGNhMzUzNzgzNmJiYWE2ZTk0M2JjN2NjM2YxN2QyNzNiZDJhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InN3VEY5bGQzdWJNOFpvVElJNlUxZFE9PSIsInZhbHVlIjoiV1BVQWxOOXVLWGNLYnlMU2Z4YWpJTGlMem5kbXBGTzlkdkhlWFo0azVsS1lVeExGMFhETWRET2VRWCtJV3NlWmxjWE9YU3dkS2Y0MW9tdlJhOVI3dFZLdXZFcktsSFd4Y1FmaTI2djRzdUpLSnNRVVJFSE9XQWNyNE9iamlWcktYQjFJMW01OHpqcmlwaUR5OUp6aEdUWEd3bEV0Q1RyeXVzTUdZYTdvSndaM1hodWNPTkttcHJTZFVUVHN6MVdTWmxsdThuZDg2Z3pVVklVOCtUeUpMY1RFbHZZSWtzSzEyWUt5d215OXNVNTU0WXJxTlRXeGVXOUxhS2E0U0dZbEJrSEZrbWc3WmpHbXdZMGZKdE5hcEhGbzl4UzFqNHk1ZVJYRS81cUhRcEc4Qkdxa3k3Y3ZKaHVqS1FYanp5WW1DeS9FZTRtWW1VRmFzMTNZbnNQQ25ZUWc5OGhRTU5xb3A4dy9GZlFLQ2xBOUhiaW1XQ3VpcjdNZHI1cGRnc2xLY3dGTDFWbTF2eUF2aXhDM3lzWUR4T0VuR2ZOWUZaTXRHdW1SRXY4a1A4QXVZWmFSRUlCMFJoNTFvbkdpbEJGSmtPMFcyckgvYTBQckYwNVZxQUpFN0krSUtyM1M1TWxORnhZY1pvaEVKODl3WlVOays5cERDUVJiemExMHRGNVciLCJtYWMiOiJjYzM0YjM5NTJlMzBlOGU1YWMyMzNjYjZkMGM0MzAxOWJlNTY4NjVhMTMyZGZiN2FiY2UwMGRiOWNhNzA4Y2M1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2015466388\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}