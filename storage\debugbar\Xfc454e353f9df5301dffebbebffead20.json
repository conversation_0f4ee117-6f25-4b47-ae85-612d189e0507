{"__meta": {"id": "Xfc454e353f9df5301dffebbebffead20", "datetime": "2025-06-27 02:27:08", "utime": **********.980423, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.57964, "end": **********.980438, "duration": 0.40079808235168457, "duration_str": "401ms", "measures": [{"label": "Booting", "start": **********.57964, "relative_start": 0, "end": **********.940131, "relative_end": **********.940131, "duration": 0.3604910373687744, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.940141, "relative_start": 0.3605010509490967, "end": **********.980439, "relative_end": 9.5367431640625e-07, "duration": 0.0402979850769043, "duration_str": "40.3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43902056, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0023, "accumulated_duration_str": "2.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.968986, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 82.174}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.973755, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 82.174, "width_percent": 17.826}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2303 => array:9 [\n    \"name\" => \"ماتيلدا فيسينزي 3 لفة ميلفوجلي الأساسية من ماتيلدا دا\"\n    \"quantity\" => 1\n    \"price\" => \"10.99\"\n    \"id\" => \"2303\"\n    \"tax\" => 0\n    \"subtotal\" => 10.99\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2300 => array:8 [\n    \"name\" => \"جالكسي كيك البندق 27جرام\"\n    \"quantity\" => 1\n    \"price\" => \"3.00\"\n    \"tax\" => 0\n    \"subtotal\" => 3.0\n    \"id\" => \"2300\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n  2299 => array:8 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 5\n    \"price\" => \"2.99\"\n    \"tax\" => 0\n    \"subtotal\" => 14.95\n    \"id\" => \"2299\"\n    \"originalquantity\" => 12\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1194637407 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1194637407\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2080266925 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991174265%7C33%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFjTnF0NmlZakdvSDVmTE5DRDZlMEE9PSIsInZhbHVlIjoiQS9FeG9SVUFlMkFhaGpaRTR3Ni9Iek9yZWpFQXlMN1FNdUV5cUd3QXNaK05oKyt1aWs3bjBoSnQ5TVJuN2tLM0xwWEU3YkNZbHphR0FRNFpSZUluNzZMaVhwOU5RbFNqRnJqam5qUENvVEJiU2puQ21SQTRuQ1hHaGg4K3FSdC9RTW1IY0xrTmV3WDJGQ2RZcjF0dWFnUzMxMDQ5VlFjUlBoK1ltbjZ4R1JrU2x1dVQ2WGhJRWo5ZXRaaXpXemt5c2tnK1o3VGpidmNyZG0zZlB4NmpaYmgyS1NLdUVZWmFpNVdDS0RIdXNZQVlSWlhGWUxsZ2Z5OEovSk9aV0JnbGhsTmVBaklxZFduYWZOTG1jNlZHdDRKdVR0RFVNckRaQTF4cWV0bm5kdWF6VDdzUTc5RFRKa0tub1pyV25rRytTVDNTR0NqdGhMeVdtZmVieFpaVy81ZWtNem5vUVpueXg1TW5xNm16Rno2dmFTeHpJR2ZFbDRKY1FPWWV5bUgyV1pKQ3NkdHFwTjQvRU5FYkVuczdlemc1azJTT1BwQ3llUmJtRm42MThvN1YxbkpaclJGTExad1h2cUI0aUdmOVUxdTlqalBXU2x0S3NqZnlWTEoxajhXVGxVYnJtdGYzTWlKM3lTM3B3UWE0UUJMU252cnZmMFhrem9Md1VpVTYiLCJtYWMiOiI5ZGE3N2U3NmJiZjY1MmEzMWJlYWZmODkyNmYwMjVkNzUxMTE0OWM5NTFkMjEyYTBhYzEzZTFmMmFhYWIxZDk5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9DRFp4RXNlcWVTdkdPU0hhT2pYa1E9PSIsInZhbHVlIjoiNGtHYzZNdXpsUWNFeVo4cUFFR1owOUlvaHd3UnBzbUFjOUw1R3gxYmhjV28vUjVlZVJtTUVRSHAxL2FlSkRYNjY4dXB5UWRiT1hrbVRKS1BoVmxqWkozTWR2bmZkdHdDU0ZreFU3SFNySU5mVEp3VkRDVStSZjYxandKUkhZbXFiY1BJUGI4TXBKWFJuVG5DU3d4WmRaRExtTmZGbXhkMnl4ejhLdDRpK2c1SmhKNzR2VnJYN0t2S3pHeUtyRXlMTHhDSVhkOEd2Rk5RZFB6eXQwQkQ4Z1ROSmo1bVBFQXpKUi9NZjA5UDJCUmw2UHNuRGZsclZ6Wmdya3lnWHJUSmxpV09tb0Nha1N1Z0dHbktPbm54Sm83a242M1p4bzdhcmYwY0gyTXE1YUI5T01PS1F3aEtweENqbTdjbVZCSVVtanFScmI4VDRZNmR0WTFiTFZmRjlCVGFpU0RVb2hSNHJ4S2dGQURkZXRPQ1NFVUloSW1saHErMjVjOHpxNFY4dGRyZ0Z6OWljdzFEYWlJaGZRakxzZDhvRXN0NUVrN1hDSG4xT1AwdThuL0FXNEtoQmluWjdkM1F6Yks2Mk9uUENHWkhkRDFMcmg0VFRReEhEOWNNQnd2QmNyT3JOMm1BRDR5NHRFUXEvNUFPWEZSa2pFalRmOFZYK05KTDJMemIiLCJtYWMiOiI4ZWJlN2U4NzEwYjhjYjFmMDllY2VkZmYzYmVhYWNjMTBiZGRkZTA3ZGExMjU3NWIxZjVkZTE0ZjAxM2Y1ODAwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2080266925\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1721282791 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721282791\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-244279361 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVYZWxzWHM0T2gxVlV2eVVVUHdRRVE9PSIsInZhbHVlIjoia1A2Y3FodlovL0V2L3YrYnUwanhoTWhZSEdIQ1JZWXZNZHpHRmxkVVhCek9ZN3ZLaldmaDB4WTg2bGg4dERUckJmOUxVVm9DNUh1R3JJMWdtMlJ1TXR5b1BTSmc3ZytLZ252a3ArSW5aZ3h3MWhDZGMzMFMwcW1OekRTYmVjK1RBcDN2RThGUklLVndrMUpnNGtTNGpzZHA0WmQ5ekpjd2tXTjlmbkk0MDFad3pLTDBrRE1FVWppVTRycnlCazNvVCtGbFpNM2ZCSUI2SXdVUStIRUkzY0J0MkxZVlNxWG1TK0hZNTc1MVZaWGpiQTBsOHNLM1QvR3BSOEN0Ty9YMVhrWHFFY0taNThiL2pQVWRkaG5yYkEzS2JQVnIwRjM2QXFVY01HaHJYZTFFUWNVOFJWRGtYRTRnczBSeWpXZjRrZHRZdFpNTG5SRHAvRDd4ME1HR0VCU0k2Z1NrdXVBVUY1MDE4QS9OYnRiT015ZWNWZER6MWs3RUJ4YUxEM0g1MnlRd0wyeWJldVFISDhkbjVkOFB4NE5FN2tiUkpIUmp1aktnYUQ0ZHF2S3MxUlBlZTczSGVTTGhIVU9ZUjcvbXFiNUs1UEg0Q2ZGVXdyVkx4UnlFK0FnZGNoQ1gyTFYxTS8wcnNOOWdUVWhpVEJwMlIyU09NWURISFFaU0NaeTkiLCJtYWMiOiI1YjJjOTEzNDU1YjExYTFjMjE5NWFmMTIzNmFmOTJmMTU4YTk4NDJiMmVjZmQ3OGRhMDczOTFjYTRhMTg0ZmM0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVHaFo3Tnlrb2pUSGpMWmJPWm1BNWc9PSIsInZhbHVlIjoiS3ZhK1NlRFQ2M2IxYVRVbUlZZzZCbEZ0bG50ZDNDQWJYM3lHV2Z5aGROWWUyRlFDMGozSTdyY3BlRURoRlhzT0pSMjRHSC9nQzFnNDExWDQxUVlWMG9EckdlSCsyaWNrVmd4bXRkOWROWE5HRjlTQWVMakZVNzJPM21HbW83NmxsanZYWWNRSUJjUVRtMVVySXplOVpvRGVFeVBBeFJYT3RENDdBeGc2ZDFCRkREMTEycHpUUEIyWUN6Uk9ya3hoVVRzekJMYmhDU0V2Sjhha2M2Snd2LytvYkorZ2NFRElReEdRRWdDSmM1YjYwOUIrNlcxTjgxYjdyOW9Ibms4bEZwbWJjM0wzS0VzSGZ3clp0MjlFVzYxVUpOZmlPUlhLSWVsYmliRkhwQVFPTVdyckgvUTJVcG1OY1J2dmRENUcxU054YkZjL3hWUkxvV2ZjUWk0QkFlQ3lPMWh6VnJYL2lhMW5RSEJxNlk5ZUl2TUJNNTcxLzV3Um9vWEk1Y2Z1UXg0V3ROSmRCZDVGczFYTXNob3lhZWNMYWcrdDk4QUFoWHhONkRROUlnYzBGNCtMOHkvd1pCQk05M3M5c3p5ZDNCeGtnWE03ZDJpK3RqQjQ2YXNzRzNzS3QrbG5OZlVvand3cWxnUFBLWTBmTDFsdWoxMk1EWTF0Y0g0K2F6VkgiLCJtYWMiOiI0MTViMzZhNWE0NDM1MDE5YzI0MzBhYWJlMjczMzRiODcxMjVmZTFjN2Q5NGExNjZlNWNkMGUwN2MwOGYxMTAyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVYZWxzWHM0T2gxVlV2eVVVUHdRRVE9PSIsInZhbHVlIjoia1A2Y3FodlovL0V2L3YrYnUwanhoTWhZSEdIQ1JZWXZNZHpHRmxkVVhCek9ZN3ZLaldmaDB4WTg2bGg4dERUckJmOUxVVm9DNUh1R3JJMWdtMlJ1TXR5b1BTSmc3ZytLZ252a3ArSW5aZ3h3MWhDZGMzMFMwcW1OekRTYmVjK1RBcDN2RThGUklLVndrMUpnNGtTNGpzZHA0WmQ5ekpjd2tXTjlmbkk0MDFad3pLTDBrRE1FVWppVTRycnlCazNvVCtGbFpNM2ZCSUI2SXdVUStIRUkzY0J0MkxZVlNxWG1TK0hZNTc1MVZaWGpiQTBsOHNLM1QvR3BSOEN0Ty9YMVhrWHFFY0taNThiL2pQVWRkaG5yYkEzS2JQVnIwRjM2QXFVY01HaHJYZTFFUWNVOFJWRGtYRTRnczBSeWpXZjRrZHRZdFpNTG5SRHAvRDd4ME1HR0VCU0k2Z1NrdXVBVUY1MDE4QS9OYnRiT015ZWNWZER6MWs3RUJ4YUxEM0g1MnlRd0wyeWJldVFISDhkbjVkOFB4NE5FN2tiUkpIUmp1aktnYUQ0ZHF2S3MxUlBlZTczSGVTTGhIVU9ZUjcvbXFiNUs1UEg0Q2ZGVXdyVkx4UnlFK0FnZGNoQ1gyTFYxTS8wcnNOOWdUVWhpVEJwMlIyU09NWURISFFaU0NaeTkiLCJtYWMiOiI1YjJjOTEzNDU1YjExYTFjMjE5NWFmMTIzNmFmOTJmMTU4YTk4NDJiMmVjZmQ3OGRhMDczOTFjYTRhMTg0ZmM0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVHaFo3Tnlrb2pUSGpMWmJPWm1BNWc9PSIsInZhbHVlIjoiS3ZhK1NlRFQ2M2IxYVRVbUlZZzZCbEZ0bG50ZDNDQWJYM3lHV2Z5aGROWWUyRlFDMGozSTdyY3BlRURoRlhzT0pSMjRHSC9nQzFnNDExWDQxUVlWMG9EckdlSCsyaWNrVmd4bXRkOWROWE5HRjlTQWVMakZVNzJPM21HbW83NmxsanZYWWNRSUJjUVRtMVVySXplOVpvRGVFeVBBeFJYT3RENDdBeGc2ZDFCRkREMTEycHpUUEIyWUN6Uk9ya3hoVVRzekJMYmhDU0V2Sjhha2M2Snd2LytvYkorZ2NFRElReEdRRWdDSmM1YjYwOUIrNlcxTjgxYjdyOW9Ibms4bEZwbWJjM0wzS0VzSGZ3clp0MjlFVzYxVUpOZmlPUlhLSWVsYmliRkhwQVFPTVdyckgvUTJVcG1OY1J2dmRENUcxU054YkZjL3hWUkxvV2ZjUWk0QkFlQ3lPMWh6VnJYL2lhMW5RSEJxNlk5ZUl2TUJNNTcxLzV3Um9vWEk1Y2Z1UXg0V3ROSmRCZDVGczFYTXNob3lhZWNMYWcrdDk4QUFoWHhONkRROUlnYzBGNCtMOHkvd1pCQk05M3M5c3p5ZDNCeGtnWE03ZDJpK3RqQjQ2YXNzRzNzS3QrbG5OZlVvand3cWxnUFBLWTBmTDFsdWoxMk1EWTF0Y0g0K2F6VkgiLCJtYWMiOiI0MTViMzZhNWE0NDM1MDE5YzI0MzBhYWJlMjczMzRiODcxMjVmZTFjN2Q5NGExNjZlNWNkMGUwN2MwOGYxMTAyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-244279361\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2303</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"53 characters\">&#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1601;&#1610;&#1587;&#1610;&#1606;&#1586;&#1610; 3 &#1604;&#1601;&#1577; &#1605;&#1610;&#1604;&#1601;&#1608;&#1580;&#1604;&#1610; &#1575;&#1604;&#1571;&#1587;&#1575;&#1587;&#1610;&#1577; &#1605;&#1606; &#1605;&#1575;&#1578;&#1610;&#1604;&#1583;&#1575; &#1583;&#1575;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2303</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>10.99</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2300</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1576;&#1606;&#1583;&#1602; 27&#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>3.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>14.95</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>12</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}