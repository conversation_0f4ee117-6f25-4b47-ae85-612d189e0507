{"__meta": {"id": "X77a16ad95c2508c202502a7983abc514", "datetime": "2025-06-27 00:46:50", "utime": **********.70503, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.245176, "end": **********.705047, "duration": 0.4598708152770996, "duration_str": "460ms", "measures": [{"label": "Booting", "start": **********.245176, "relative_start": 0, "end": **********.653883, "relative_end": **********.653883, "duration": 0.4087069034576416, "duration_str": "409ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.653893, "relative_start": 0.40871691703796387, "end": **********.705048, "relative_end": 1.1920928955078125e-06, "duration": 0.05115509033203125, "duration_str": "51.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026800, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00294, "accumulated_duration_str": "2.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.681056, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.707}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.691864, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.707, "width_percent": 15.986}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6973722, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.694, "width_percent": 15.306}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-280352334 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-280352334\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1833677082 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1833677082\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-504997627 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504997627\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2105936293 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750985006999%7C66%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZScFRiVDhSNzY1Y2VSckQ5OUlpbkE9PSIsInZhbHVlIjoiYnYrdFlTeWtmUFZiYWE4cCtMS2VwczIra0p6OGQ2ZmdPUUgvS0t3Z3pQRlluTkFaUVhhOFdtZHlqOTNxZ3N1bnByK1lpcm1lRHdXVzQ4L002Ry94bnFINzVWTkNObjcwY3hUdG9ZY3VQYk9rTHNEZFIwQnI5endhOGpOVlNFcjZZRFBXZnlSNWlsSCs4Q1VQZzNjUHM3WkN0Uk9pUnUrUnZCNEpkcmtBZjhwblhWY2EvdnhlRUtoa2tBN3lxT1NucmJzdTRSaGl3T0YwWnlZTXBRdzJkSDhzcERHcDFla1NCQWVGQkZZcTNVdkRXelZ1SUJLRCtKcTlBUXAxQm9GTVZuYjN6bncwQ3NMQXc2QkxLMnpJSzZtYzBLNE02amRQdWMrYjFhN1gvbmpvSUlYT2hPd0tRQWM3TjJzeGVvWTc2NTRiSGEyNGZZSTlCWVl3OG9ZUWxNYkRDTnllU1VTbEh5SGg2L1pOWFBIc1h0OHEwZW44Q2lNeEFFS3BPNjRpV0orbVlsZk96Tis3eGhNN1hrL0pQUDd6RjhQOFh2ZGtYeFoyTnRvN3gyL0Ntb3E5YW8yaU9jV08zNXBmajlNUm8vTUpQeUhaa3NhRzFua3NGL3A1N2t6eDc2WDZ5VFBzRUkzNDM5MHVWYnQzYTFyMDVCRUp4RVIvRFFJcXA4dEgiLCJtYWMiOiJjN2JmOGY1ZjIwZDVjMDBkMjUzYWI3ZWNhYWUzYmVhMDQ1NzRlYzBhMmViNGUyMTRjZDg5OWRhZWNjYjEyNDZhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImlQMWZTdk5tK3c2a3dkckNDZTZrV0E9PSIsInZhbHVlIjoiWWRGZmZYNnFZVlROVktEVGk1dGdNRjE1djRUM2dtcStpblYzOEdZYlZFMEQwVDhvR21SSUkxL1hZZ091Z3Axc2hQWnE5RU5EQXJCQndPR0VldHFRWUQ2VlNsVFNNTVJBcDIzN2xFY3gzQitBYWYyejc3STNZaEZxeU5mODN5SU5kT21iekhiUm9FVUo4aGo5MUh0VVRhTXF2LzRPOHExY3hFUDNKaWo4YUVUQktBSjdZL1k2bDdqbG9jeG15UXBKcHduckhIbXBLa1NiZHJPR3BwVDI1NmJDeUd5b1pFV0xPalp1NExnNTRUS1JyNU54Z2RSRUF1Sk1YUXdIRjJOcy9ONnlJU2hCd3hGZm5ZYmpod0kzcGR2SlQ3L2NZRlllV1ZOcUxxY3ZFazFRdzUrTDhGQXdnbVhma0Z4UHpkUzlKdzFobG51Ny9KeDdpZm9lalc2WTZka1F3cWV4NXczNEo3ZjlFbmxJYmN6WE5KT0EzcWFxNk5rYkYyVDk0UHRmVUc1UndWN2lvNDlrS1JYdUNJaUpETTlFUUk2bnZudFBWZnFoV2ljUFovaGx6Ri83cGZtS0NDM3U4cWVSQ0QvTExDbzB5VHFWMDNMOUJ3UGl3NTNmZGpQMWF1OTk1aFoxMDl4amI0V25HZEdQTlNHcUdoZG1WcitsOHF6VDl1UzgiLCJtYWMiOiIyM2MwOTdlZmRmZjNmMDU0NGFmODY2MmUyZDIyYTM2NjAxNDdjNjEyZTI4YzgzYTllNzY2ZGMxYjQyMWY3OGU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2105936293\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1627779784 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1627779784\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:46:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhseVdKYVV1OTNYa2dmcWVTTE1TWFE9PSIsInZhbHVlIjoic1dDdWZCclNoS2o5RnNSVmlYMy9DaTB4MUJHTDB5MUZTVjFHSlNmWVNIQS9ZOUs1V21DdVpWdDFTY3czb1piTDlodTY0SVhoeXR5LytiRDFmTGVJZmphdXdDVHc4aGhZMVgrTmxpeHlvaFdqYlRLbDFXM0c4cG9xTWJWZXVmdnJsbThCOUhmS09lazRHTlhBUkloT3NVMHdPc01MdnU2clFMQXdhd0NocU9SSG1HUlI4STI4cEVqMVk4K2I0QVlteUtNMnUrZERyOWxycWhIYUM2Nm9YQ2VXMU5YUGxpRlU1dGJ6WkFTY1BmWkJGU3FvV0Q3dUdSQVVlU09jVlpMQ2EyTzMyVXprcmF1dTVPV2J6YnNMM0wyWHFucWU3d0FWaWhUd2tWYXpuMFRLTlQ3QXE2WDlaeHExZmI3Sm9UbG93MFRDMk1obzVGd2dESnFYUmNDcHg1dXdUcHE0cjJSUkVYM2t0djFRektwR1NtdHVZRnp2ejNIQVBVOGtCMURqOUtwU2ZvOU91MUlnOTRFSVN2NlNYM093blBBN0NYc0VPbW83aFlzdFFjL2ZmYUUzcFRmNXcxazBvdTlxbXpaeHdPb0E5WHlUWnJHTHN5RHpKbGRkSDdnbHpsczNRSUY4RFJWeWpyNXVGbnpYa2hmT0NhUmh1MWVTaUc0eFVSVEwiLCJtYWMiOiI5ODhjYTg1ZDA0YTQ5MzBiZDE1ODg0NzRmZDZjZDU1ODczZDMyZGU4ZjhmOTA0NTRhZDRmNTgxNjFhZWUwOTU3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:46:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlhDRUsyQURyUWNkTXNrV1VSTEhzbHc9PSIsInZhbHVlIjoiN3k0d2pXR2Q0RTZ5bHorcTMzTEcvZi9weHlWOFpGS0IrS2ZQRitkSXJndkFSZ0FML1dJM1hkTVFBaEJJSlh4RzVkdnVNaWpxd2V3dlViVXFXOG1VL3dqcmlCa2FBVUdUSVAwSzFYTmlzMkN3dHBpakRxcEp5UHp0RVBhc3pCbitxbVUzRENnOGM1VkowNjRySnAzejlkWlVURU4zcDRPTmRROFlrcXEyVjdOWGFsZlRFVGdMbnRYZjN4bmZ6eFJpL0Y5MWFDYk4xYWh2YTRIcEs4RGJSYk5IQkxJVlJRRUFVV2tLdVpScWlycGpVNDdnQ05rOUw5YnZGVDdQMi9zNFFZV1RNL1JTWm5pZjZDNTMrSUdGVG52MHpqa2VMeFpGRUhJLysrSC8vYVJZSjNjVXdCWGltdzZIR1V3NDFMOUlHRm0xc3l6T0xHYjlqbmlBYUliNTVHdzFMcHo3MXNIY2FsVjFHcGtWWmQ2RlM2aGhTd3dreVFGZDNadE9OU0R0eHQ1d1RQNW0rL21Ga3ZRa25ERHZBdzFHK3Jnc29hK3I1eFN0dTZIdTRqUGVuTVN4Y2J6ZEdUTmFJQXlzVnczTGJnT01Pa3NJN2lkSjBJdEEzOTVFLytRcnB3bWp5cU8xN2RDdTdFUUx4VGdSTktqZUlLVXI3QXNocHhNbHBIaWQiLCJtYWMiOiI4YjQyNDQzNGUxMjdkY2M2OGI0NWViNmRjNmYwMDJkNWM4YjUxMTVmNDYwNWM3OWVlMjJiODgwOTAxMDRlZTUyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:46:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhseVdKYVV1OTNYa2dmcWVTTE1TWFE9PSIsInZhbHVlIjoic1dDdWZCclNoS2o5RnNSVmlYMy9DaTB4MUJHTDB5MUZTVjFHSlNmWVNIQS9ZOUs1V21DdVpWdDFTY3czb1piTDlodTY0SVhoeXR5LytiRDFmTGVJZmphdXdDVHc4aGhZMVgrTmxpeHlvaFdqYlRLbDFXM0c4cG9xTWJWZXVmdnJsbThCOUhmS09lazRHTlhBUkloT3NVMHdPc01MdnU2clFMQXdhd0NocU9SSG1HUlI4STI4cEVqMVk4K2I0QVlteUtNMnUrZERyOWxycWhIYUM2Nm9YQ2VXMU5YUGxpRlU1dGJ6WkFTY1BmWkJGU3FvV0Q3dUdSQVVlU09jVlpMQ2EyTzMyVXprcmF1dTVPV2J6YnNMM0wyWHFucWU3d0FWaWhUd2tWYXpuMFRLTlQ3QXE2WDlaeHExZmI3Sm9UbG93MFRDMk1obzVGd2dESnFYUmNDcHg1dXdUcHE0cjJSUkVYM2t0djFRektwR1NtdHVZRnp2ejNIQVBVOGtCMURqOUtwU2ZvOU91MUlnOTRFSVN2NlNYM093blBBN0NYc0VPbW83aFlzdFFjL2ZmYUUzcFRmNXcxazBvdTlxbXpaeHdPb0E5WHlUWnJHTHN5RHpKbGRkSDdnbHpsczNRSUY4RFJWeWpyNXVGbnpYa2hmT0NhUmh1MWVTaUc0eFVSVEwiLCJtYWMiOiI5ODhjYTg1ZDA0YTQ5MzBiZDE1ODg0NzRmZDZjZDU1ODczZDMyZGU4ZjhmOTA0NTRhZDRmNTgxNjFhZWUwOTU3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:46:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlhDRUsyQURyUWNkTXNrV1VSTEhzbHc9PSIsInZhbHVlIjoiN3k0d2pXR2Q0RTZ5bHorcTMzTEcvZi9weHlWOFpGS0IrS2ZQRitkSXJndkFSZ0FML1dJM1hkTVFBaEJJSlh4RzVkdnVNaWpxd2V3dlViVXFXOG1VL3dqcmlCa2FBVUdUSVAwSzFYTmlzMkN3dHBpakRxcEp5UHp0RVBhc3pCbitxbVUzRENnOGM1VkowNjRySnAzejlkWlVURU4zcDRPTmRROFlrcXEyVjdOWGFsZlRFVGdMbnRYZjN4bmZ6eFJpL0Y5MWFDYk4xYWh2YTRIcEs4RGJSYk5IQkxJVlJRRUFVV2tLdVpScWlycGpVNDdnQ05rOUw5YnZGVDdQMi9zNFFZV1RNL1JTWm5pZjZDNTMrSUdGVG52MHpqa2VMeFpGRUhJLysrSC8vYVJZSjNjVXdCWGltdzZIR1V3NDFMOUlHRm0xc3l6T0xHYjlqbmlBYUliNTVHdzFMcHo3MXNIY2FsVjFHcGtWWmQ2RlM2aGhTd3dreVFGZDNadE9OU0R0eHQ1d1RQNW0rL21Ga3ZRa25ERHZBdzFHK3Jnc29hK3I1eFN0dTZIdTRqUGVuTVN4Y2J6ZEdUTmFJQXlzVnczTGJnT01Pa3NJN2lkSjBJdEEzOTVFLytRcnB3bWp5cU8xN2RDdTdFUUx4VGdSTktqZUlLVXI3QXNocHhNbHBIaWQiLCJtYWMiOiI4YjQyNDQzNGUxMjdkY2M2OGI0NWViNmRjNmYwMDJkNWM4YjUxMTVmNDYwNWM3OWVlMjJiODgwOTAxMDRlZTUyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:46:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}