{"__meta": {"id": "Xb4fff642e2b1cd39ccb73eb5210f4048", "datetime": "2025-06-27 02:26:22", "utime": **********.672727, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.245662, "end": **********.67274, "duration": 0.4270780086517334, "duration_str": "427ms", "measures": [{"label": "Booting", "start": **********.245662, "relative_start": 0, "end": **********.58079, "relative_end": **********.58079, "duration": 0.3351280689239502, "duration_str": "335ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.580799, "relative_start": 0.33513712882995605, "end": **********.672742, "relative_end": 1.9073486328125e-06, "duration": 0.09194278717041016, "duration_str": "91.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48198040, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02751, "accumulated_duration_str": "27.51ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.610216, "duration": 0.02265, "duration_str": "22.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 82.334}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.640387, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 82.334, "width_percent": 1.272}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.653487, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 83.606, "width_percent": 1.854}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.655494, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.46, "width_percent": 1.49}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.659765, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 86.95, "width_percent": 8.179}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.664227, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 95.129, "width_percent": 4.871}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1324637030 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1324637030\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.658795, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-70834893 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-70834893\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2128283787 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2128283787\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1922624409 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1922624409\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1896238694 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991174265%7C33%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFidWNmWGlpazJQNVBVbVNIblplUVE9PSIsInZhbHVlIjoiZjhjSjljR0ZZbmxjc2M5ZElBV1N2Qys1djRmUk4vTVhTaGMvMHJDQkhBMG9vT2NiSm11bG1RVmdoYXRpK3doZEtwY3FlS3hadUlvZWlKWC94YmVjeW1oTFl2eCtFSE9PNGt0N3pmUHBEa3pTWDl3Z3lyeEZMdU9HcEgwS2IxMkdwMy9vaDdSMXZiRkFnWVpMU3FIZFYyZXRNSnBCUjJlaWNBV2hZYWJuU1ZhOU5RNk15R1VPSjZ5aU5OakdIT1hhQ1MzeVFJNDRvQWd3enNSUThHcHhsNXdHMW5RNjl5TTM0b1EwTlQ0K2h1V1A5MzdEdThqQ2J3WWs1NTJqelBrM242TmdYMHJJbmtoc0dJY1lrZzJueGlxU1BlMzBkQXJMcnRqbk5ONXo2ZVlVS1J6L0FRMmFDb0g0bmhLTEVtZjh3bTJNV1ZNL2xzSGU3dWZ4ZjZhcW44Ri9uTkdZcGJ6MklLQ08xM1J5SkdhVTlEVGdXUVdrU2RCRzlSN3RBZHI1TkVTUTdxVkFUbzFDeE5XWG92SUtFK3BQRjdveEsrYUtpOU1YQXdCT3ZWdlU2cFJGdEFFM2taSlR1dFFVNUZ0N2k0ZlFIS2ZuMDhjMGxRaW45akJNUmJHd1JjUzRvejVwUW1jeUlGblVPeTVwMWR0V29zbG9aOFI4VWpSZmNoNlAiLCJtYWMiOiJjMzk0YzY0NTU2NzFmMGZmMDhhMjRlNmYwZjRkN2Q4M2NmODU4MjdiZmMzNjI3MjA4MWJjMmU2ZjYzZGM5NDdlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InE2bWhtQ3R2ZVpxd01xcGxEd2N0Vnc9PSIsInZhbHVlIjoiRTA3dHFJTy8xVFV5SG5wTXhNSm9sRkF1a3RqQVYwckZlQUMyNnR2T2MyRlNwOFRTSkpOalBlTGxuVk44L2tBcGU0QTB6RE45OGhBa1g0MzFVY1g0TmRnU29Nb0sxTXM4SGc0czBtanJ0MzYvajZhdmtocWV2STAwV29FTi84ZEN2MVJsZWs3cHY4angrYkZPUlFHUHkxbGh6OVgxR0dMa2crVXM3emc5cm4yM3h6QVlsUVVJWEJYMGJrcVduQzNjNUE2Y2JWQ1lmZHl6NnVhOGRzc0srZW50b3JUdWxOZzFUOTBmUk9TSTVTM0lOSXNxZnNVcUNUeUF5Tnl2UHhXdnF5UW9VOHcxay9nbUhRWkUydTlZY2FwODR2bVBodXZ5djEwREZKdy96RW1jaU1BLzgrMmhycmw0ejZsNGlKUGJCODBhK09tRnNqSUxScE5HeHJESXpteFUrcUhVbHBRckMvMDhiZnRFN0o1QjU2RUpPK29OSzNZSzk1WGVOZGNhV1B1SE10N3lnQXkyS3RGZmhETmhma0RCQU9OOWhIdW9QSkdicHZJTDAwdHVEN1ozYUtrTmxzM3U2c2twaGdQSTU0SDZxVkJ4OEk5Z211WjVPeUNIU3h4RHcxM0xWT1ZLNnJYaGQ0bjJINzhOV21HT2dEcURYRGQ0bENpMXBxOGQiLCJtYWMiOiI3MTYyODZmMjJmMTk1NGQ1MTk0NzU2YWQ5ZjU1NjI1NmMyZDRjYzc4OTU2YTJjZWU4M2E0NDk0ZDcyZWY2MDRlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1896238694\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-234111204 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-234111204\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-890274495 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:26:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNMeFFtTzBBN0pnTVVISlBMb0JTQlE9PSIsInZhbHVlIjoibERpYmprUlNLOXlLU2NlaUhFTTFoSTVyNEd3ODZLeHRQMFhTVTJFMEEzdHFWWURIVVpFaVNmdGcrTUNKaEREM0tLNUFmZldObkQ5OGhyS3pSeUhFN0JPWW5GTUlBWU9HNVZMR3l6dllPT2REM2xzS1c2RVNaWTdpcFB6ZGIwakFwR2l1bzMwMXQ5RkR0d3FCV3pXbUVJYWJUNThFTUROOEhSdXJ1dTVHRm95ZjdmUE1KSUd6dXVybTB2ZXhOYm9mMExQWStJeDQxVERWc3VHMHFZVW1PYVJISFBFUUptT0tnTDF0VnEza0UzTnVWQTBudkoyUFJGd0hObCtNb3E5Qk15b1Z1SkpQbWEvYTcyOGEyNnJDZklWWTFseDFRYUs5VXN6SmRnUkF4ajhSdk1YTTJ5UzRscUppa3YyazBNUGljSG1Ma2lPM1owNUN5dXlJR2JpeFVweit2UlFtZkJQRS85U1NwQnRXY1Z3V3lnT2pibElnejBScUcyTi9kS3ZYQ1c4V1ViVVhoQzJOSW1tZWxYcmVXZlR4cWdRRE15MEM4YWg1bVJhMWdPZEJ2bzZ6OEd2RS9WMWo5d3NvZWM4a2I4SjgrMXpTZFM3STVraFhlYlpWWUpPVVBVc0Q1cGlONmplYjdyT3ZnWi9KWXVxQjJrZXhuSFVYWDY1ZmJ5OE8iLCJtYWMiOiI5NDA5NDBkYzFmNWQ2OTA2ZGI0M2Y5M2U0YTc0NDZhYjhmMmY1NzllMDRlNGU2NDQxNWY5ZmIzYmE1YWE2NDMxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjA5eGg3WldLVVVmRjl0ZHdaUldFTkE9PSIsInZhbHVlIjoiOXlpK1QyOVZpMUY4UTUvSVIxRWRmZzFNOENQSlVQV1gvcnYrYSt6VE9MdlF1TnVYWUcxY2tMN1RmWDlDdUJ5UU9HajRRNkhlaFVkY2x0WFJzMGFWNkliR0pBZ09YUDRaUDFwMmZWRVRXUHpmMXFJYWNXeHljZndmd1ZRZ29makRwQURESEc2cmNDSE5GMGluUFk3RWhQU1ZLS0FNdWt4NW5Odi9FY0xvaFFiR1FyMi9BZGpDSGZ6bnNOM0FGbTM3REFRZzRzbG5ycGxXZ0dqekJBVjFNTngvaWttVHhHR041d3MwS2piRzc4NHRuS3BENFRGZGsrcVJCTHBKdFN3RGpQUkZQd1d6NGtoWE9zNFlaV1NncHFWNzc2UUswbWorOGwxa0wrV3k3WjEvaEk5OW4yWHVyZGFKTDI1bFdvWXpsclVVZUY4L2c3WTRtcEQxUDF1aFBoSGM0VHc5Q1JPenc4Njh6YmhHTmQwVXZ4bXlBNmkwLzY1dllaMUgxd1ZNOFpZUjhlbTBLZ24rS3JweGlMQ05uWXpTRlRPbDFobWNndUVheTZxN2FwV2hMM2p3ZGNMQ2ZIV01BZmtWUlBlT20zaDlKS1l0dkNJaXFqanBKWUhEc01Fc3d1ODNUZkJBRGxaL1VOWHlqR1QzRnI4OWcvdk9NMWlMWndIVTMrY1ciLCJtYWMiOiJlYTYzZjNlYmJjOTRjN2I4YmNhNzdhOGQ4OTYwOTI3NzNjZGQzMGM5NzAzOTk5NTA0MGJiYTQ5YTY0ZTNiMTk4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNMeFFtTzBBN0pnTVVISlBMb0JTQlE9PSIsInZhbHVlIjoibERpYmprUlNLOXlLU2NlaUhFTTFoSTVyNEd3ODZLeHRQMFhTVTJFMEEzdHFWWURIVVpFaVNmdGcrTUNKaEREM0tLNUFmZldObkQ5OGhyS3pSeUhFN0JPWW5GTUlBWU9HNVZMR3l6dllPT2REM2xzS1c2RVNaWTdpcFB6ZGIwakFwR2l1bzMwMXQ5RkR0d3FCV3pXbUVJYWJUNThFTUROOEhSdXJ1dTVHRm95ZjdmUE1KSUd6dXVybTB2ZXhOYm9mMExQWStJeDQxVERWc3VHMHFZVW1PYVJISFBFUUptT0tnTDF0VnEza0UzTnVWQTBudkoyUFJGd0hObCtNb3E5Qk15b1Z1SkpQbWEvYTcyOGEyNnJDZklWWTFseDFRYUs5VXN6SmRnUkF4ajhSdk1YTTJ5UzRscUppa3YyazBNUGljSG1Ma2lPM1owNUN5dXlJR2JpeFVweit2UlFtZkJQRS85U1NwQnRXY1Z3V3lnT2pibElnejBScUcyTi9kS3ZYQ1c4V1ViVVhoQzJOSW1tZWxYcmVXZlR4cWdRRE15MEM4YWg1bVJhMWdPZEJ2bzZ6OEd2RS9WMWo5d3NvZWM4a2I4SjgrMXpTZFM3STVraFhlYlpWWUpPVVBVc0Q1cGlONmplYjdyT3ZnWi9KWXVxQjJrZXhuSFVYWDY1ZmJ5OE8iLCJtYWMiOiI5NDA5NDBkYzFmNWQ2OTA2ZGI0M2Y5M2U0YTc0NDZhYjhmMmY1NzllMDRlNGU2NDQxNWY5ZmIzYmE1YWE2NDMxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjA5eGg3WldLVVVmRjl0ZHdaUldFTkE9PSIsInZhbHVlIjoiOXlpK1QyOVZpMUY4UTUvSVIxRWRmZzFNOENQSlVQV1gvcnYrYSt6VE9MdlF1TnVYWUcxY2tMN1RmWDlDdUJ5UU9HajRRNkhlaFVkY2x0WFJzMGFWNkliR0pBZ09YUDRaUDFwMmZWRVRXUHpmMXFJYWNXeHljZndmd1ZRZ29makRwQURESEc2cmNDSE5GMGluUFk3RWhQU1ZLS0FNdWt4NW5Odi9FY0xvaFFiR1FyMi9BZGpDSGZ6bnNOM0FGbTM3REFRZzRzbG5ycGxXZ0dqekJBVjFNTngvaWttVHhHR041d3MwS2piRzc4NHRuS3BENFRGZGsrcVJCTHBKdFN3RGpQUkZQd1d6NGtoWE9zNFlaV1NncHFWNzc2UUswbWorOGwxa0wrV3k3WjEvaEk5OW4yWHVyZGFKTDI1bFdvWXpsclVVZUY4L2c3WTRtcEQxUDF1aFBoSGM0VHc5Q1JPenc4Njh6YmhHTmQwVXZ4bXlBNmkwLzY1dllaMUgxd1ZNOFpZUjhlbTBLZ24rS3JweGlMQ05uWXpTRlRPbDFobWNndUVheTZxN2FwV2hMM2p3ZGNMQ2ZIV01BZmtWUlBlT20zaDlKS1l0dkNJaXFqanBKWUhEc01Fc3d1ODNUZkJBRGxaL1VOWHlqR1QzRnI4OWcvdk9NMWlMWndIVTMrY1ciLCJtYWMiOiJlYTYzZjNlYmJjOTRjN2I4YmNhNzdhOGQ4OTYwOTI3NzNjZGQzMGM5NzAzOTk5NTA0MGJiYTQ5YTY0ZTNiMTk4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890274495\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-857283444 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-857283444\", {\"maxDepth\":0})</script>\n"}}