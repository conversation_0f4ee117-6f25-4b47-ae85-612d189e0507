{"__meta": {"id": "X1085f2da68ca466792dcd4e707fb526f", "datetime": "2025-06-27 00:43:26", "utime": **********.888463, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.462941, "end": **********.888477, "duration": 0.4255361557006836, "duration_str": "426ms", "measures": [{"label": "Booting", "start": **********.462941, "relative_start": 0, "end": **********.835489, "relative_end": **********.835489, "duration": 0.37254810333251953, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.835499, "relative_start": 0.3725581169128418, "end": **********.888478, "relative_end": 9.5367431640625e-07, "duration": 0.0529789924621582, "duration_str": "52.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00298, "accumulated_duration_str": "2.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.866081, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.423}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.876078, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.423, "width_percent": 18.121}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.881715, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.544, "width_percent": 18.456}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6ImxBeXpYYS8wM0hxRlh4cXk3bzIrOVE9PSIsInZhbHVlIjoic2twNWRaa1lXWkFBeWJ5SklYMXV1Zz09IiwibWFjIjoiMTYyOGE5MjcyNzY0YjU4MTU2MTcxNjEzYTBjMGViYjQ1MzUxNDVjY2JmNWFlMzE1OTM3OGRmMTE1ZDJmZDg2YSIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-886633667 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-886633667\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-610780388 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-610780388\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1515476594 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515476594\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1844068706 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6ImxBeXpYYS8wM0hxRlh4cXk3bzIrOVE9PSIsInZhbHVlIjoic2twNWRaa1lXWkFBeWJ5SklYMXV1Zz09IiwibWFjIjoiMTYyOGE5MjcyNzY0YjU4MTU2MTcxNjEzYTBjMGViYjQ1MzUxNDVjY2JmNWFlMzE1OTM3OGRmMTE1ZDJmZDg2YSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750984993197%7C65%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJlaGVWb2RhRHRac1NNQmZXWnl6UHc9PSIsInZhbHVlIjoiUitmaUxFTGlHNVdiYnRnVENXanBhYllGRUkzY3NPci9KTmhUeUczdnNBL044S0pLM3ZkOEFyZnVLN3RFdXlxUmdOdkFhMEhHZnZmQVlheUN3MU1uc1lIT2YxNWFrK0E5MklIdVdrU25rcHc0YWliUU1IZXFVM3Y4VkN0T3IyWHBQaXpROEd6ZUxPMHE5Nm56SVgvQ3lPcHA3cVpibXpaRXRkVWEwS3FkL1FDdjB2SmZBekhqR1pyNDNidC8xejc1TlMzcy81YnJGZVVUSDd4OVRMQ1VJN2lOVzRuZEtxUk0rbXA0UlMvMmxZVVdmbDh6NlM4cW9EbHczeS92Yzk0TUFZLzVMSU9ZczRsNHNrNFIrYmVEUmtiamtjV0lRNlR6dGZZcm9XQmpPWDFXOVJvakptVS9XQmtZaEdLb0U0QmVjTldpMjQ4VFJWcnBRb1JmblppbmtEMEdNTUN6UXA2dW1YYmhaNFUrMi9NMmVuTlptWUROMVcvRnh1c1ozbmEvZVh4ZmlUK29ZS05namppUzI3S3BDejBSZXBGMG1NTUtHZlA5MUcyUFcyOVkrQjZDSmN1UHY1YlBmNCtUMkxaakxaazdIdk9Bb1JubGlKRHpocUo5UWJZUnJFZVFDTjlHZU9jU0pZaTN0ZFFPaFNzNGhxdk1FaU9SS3FBakVEM04iLCJtYWMiOiIxOTY5ZTFkYzc1ZjIxZTZkZTBlYWMzOTU2NTc3YzExZGY1MjEwN2I4NTg5OTk4ZGE3ZTI4M2ZkMjk4MThhNzQ5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9CY3d0UkJkQjBPUjVtbWJuYTFYd3c9PSIsInZhbHVlIjoiSU81TnUxRFlYcDltTkEybkp6bnNXaXhicG00V0RQWU9oME80S0U5SjlYOWdDczdEemhwdGJGaUtHKzh6Q3BoMnR5dEdTcC9IQ1IyWTNzeWlwL2syNUtHZi9GVGgyaWNzNFJ0ZjF2YXZEVVRzSmprY3hpNHUzVkZhN3c4QVFMVURiUlhtUllTd1U3cDRyajdvcDBUaDBaRkpSSGtOajNEUTRNTXRqbE5hejlRUysyYUMvall1MkM0eFdKdm5CbnQwV3N6M0RxaVd5SmRnVkdPNnVGS0RnMDJKY2wwSGl4V0VIdzI4em9ZYXZuYTRycy9LaW5YcmZTWVd4dXNYWFF1OEhhTWtxU2FIUERXbmJESEhPeVM4cFFwejU4OC9qdnFzeVlXTXZJTHhzMUxZaFd0eUE4VUhFOWhHZWxhcGJXSFMycElqekdwRWNvNDNPZHR4WjhZb0JOckRLTHlRQjVsY3lISHRDZEpxTmFLYjJBVVFuOTFYbXRzYUFFczBOMlhPNjlib1FOeEZ4eUFpckJlSWUrTzIrd3NoM2pkN00xN0JacEhMV2dEand5ZmZwNVZjUmQwNG9wV0lRS2hFU2ttYWk0Mm5UbFlyLzFpaGxSb3NWRjlhbGc4eFpTU3VsR3JSeDNtcEJpd2w4cnJBUDZic1N0RjZXL2c0dnBsQ1NTSTUiLCJtYWMiOiIxNGMwMzFmZTVhZjJjNzM1MzliMjc2ZDU2ZDJhNWU2NzNlNTdjNjIyMzM3ZDg3YmRjZWIzOTljZTYzZTBiNWI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1844068706\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-898793733 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-898793733\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1946332209 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:43:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlpcFd0VHdld2V6R0toOFBsTUlBbEE9PSIsInZhbHVlIjoiVVNyZXplOXZIRHBNWmFWZUpTM2l0U1E1dllNbDlOQTBBZkhhVG82RUJTTWF4TDRRUk01UXBmRGlXaitHbVlZT2d3M3BJMzJ0Q0I4UnVTR2lXUTQvdzZlampsK3NDMEV1UWVFVnlNZlVrWmhoempseEE3Z2RtY3h3d2ZtSzNTZlpJUGVXeVBvTHVhQnl1MTNzczVYY1ZMOGNib01QcEIyRXM1WTZEYkpOVC93NWRJTUlIMkd3bTIrV0YrcWZPbmUyckcwTFNwSzBhS2JuUjNBMEMrQTZUeGV5UXRqNjdEZUxLU3k2d09yN1BYR1NKSVVxOG4vUnZLTXZWVnZ0SStzV1R6K3B5cEdXZXRnTWVFSE5wWnhra3hWVWhKNTV2Zzk0TXY0MUpLdjhGRDdoT05ycVppL1p3SUNzU1FhMWQ5RXRlekt0eURzK1lqQVB0Y0VySE1VZytrTTJpRnNkRWowZlZua2htMnpEZnZ0K1VVSExhNDFkQWtlRTFRUXFsdWtUQ0t2Vk9NcVgwZWM0MU4rVzZpMHlDOUhRMVlEUGdsMllFU3phWCs4RUFUSk9ReFU5R0N2Y013Sy8wT0hacGVOT3VEN1MwMHFrZkwyMStNemd0bi9TVXU1SmNJTkNieVFEMDl4SUVxbmN2b05QdEI5OHVxZnhmeWkyNFhqK1BqUVkiLCJtYWMiOiJmYTliMTU5N2IyNjBkYTc1MDU1YTNiN2NkZmE3NjcyMDcyODI4ZDU0YTA2MmNmYzgzNDFjNzI5YTMxZDU5Y2U0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:43:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlI0b1Y2bW50YzU0cWZYckYzOWNXSVE9PSIsInZhbHVlIjoiMzVUaDFYeXU0SUp6Tm83QkFieU5wWnFOUzFTazNiWGIzbkZpZFo1REFRc3hHZkNMeUFwNThKaGQrMmdHa2pSc0VzYlR3NC9obnN5MmR6dEtFZlRTZ2treXo5YXFnOWNWSUFPaThqWHMzMHZ0UFJnVUVOWjdwcUdZdVVEKzFMNndlOFYveHNidERKKyszdGpNUUVFcWFBT2pOQjhJMllHZmQrL1lmMzhhWWpqYXhuOHA5dzVvMzlEK0hKanFYTXowQmNWQmpBZUtON1VrT3A5c2J3Q2xDNzJSUk9VVy9ram9PSmx5bEJHbW5uVVhmMVZYakpiZGJrMGJNS2tWYmx4NjZhWGR1VjBoWHhPQ3hmNlMvK0xXOEtZQXRDT0FmcHUvVnlwY2p6dlF5Q2VKMGdpMEJ1dHRYbXh6d01wZkRXVXd2bHg0YStUUUhCNDcrQW1wRVR0SlBmVW9ZeDlXdlhPbUxpVVNta3pUSzBUOEtJOEI4Y1RtdE8raGVRZ2N2ZEdXY3BqUlcwOU5jTjNKK0VBZE02dlVSbW81dXAwWG45NkZxOTZEQmRmTk1UNXFZbjBIajB6VTRpWjNsamY2bmRVYlFYUXk2ZWFQUEZkd1dha2crTk1wcEErYVVHUllHQU1vWElhQzlKZVU0VVdFUStzSm5aRTRuVXg0RFB5NVNnSlYiLCJtYWMiOiJlZWU5NWZkNjFmM2Q5Y2NjNGRmM2JmZWY1OTZmZDNjNjBmNmE1NDIzZWFlMWNmNTRjZDQ4YzYxZTZkNzliN2UzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:43:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlpcFd0VHdld2V6R0toOFBsTUlBbEE9PSIsInZhbHVlIjoiVVNyZXplOXZIRHBNWmFWZUpTM2l0U1E1dllNbDlOQTBBZkhhVG82RUJTTWF4TDRRUk01UXBmRGlXaitHbVlZT2d3M3BJMzJ0Q0I4UnVTR2lXUTQvdzZlampsK3NDMEV1UWVFVnlNZlVrWmhoempseEE3Z2RtY3h3d2ZtSzNTZlpJUGVXeVBvTHVhQnl1MTNzczVYY1ZMOGNib01QcEIyRXM1WTZEYkpOVC93NWRJTUlIMkd3bTIrV0YrcWZPbmUyckcwTFNwSzBhS2JuUjNBMEMrQTZUeGV5UXRqNjdEZUxLU3k2d09yN1BYR1NKSVVxOG4vUnZLTXZWVnZ0SStzV1R6K3B5cEdXZXRnTWVFSE5wWnhra3hWVWhKNTV2Zzk0TXY0MUpLdjhGRDdoT05ycVppL1p3SUNzU1FhMWQ5RXRlekt0eURzK1lqQVB0Y0VySE1VZytrTTJpRnNkRWowZlZua2htMnpEZnZ0K1VVSExhNDFkQWtlRTFRUXFsdWtUQ0t2Vk9NcVgwZWM0MU4rVzZpMHlDOUhRMVlEUGdsMllFU3phWCs4RUFUSk9ReFU5R0N2Y013Sy8wT0hacGVOT3VEN1MwMHFrZkwyMStNemd0bi9TVXU1SmNJTkNieVFEMDl4SUVxbmN2b05QdEI5OHVxZnhmeWkyNFhqK1BqUVkiLCJtYWMiOiJmYTliMTU5N2IyNjBkYTc1MDU1YTNiN2NkZmE3NjcyMDcyODI4ZDU0YTA2MmNmYzgzNDFjNzI5YTMxZDU5Y2U0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:43:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlI0b1Y2bW50YzU0cWZYckYzOWNXSVE9PSIsInZhbHVlIjoiMzVUaDFYeXU0SUp6Tm83QkFieU5wWnFOUzFTazNiWGIzbkZpZFo1REFRc3hHZkNMeUFwNThKaGQrMmdHa2pSc0VzYlR3NC9obnN5MmR6dEtFZlRTZ2treXo5YXFnOWNWSUFPaThqWHMzMHZ0UFJnVUVOWjdwcUdZdVVEKzFMNndlOFYveHNidERKKyszdGpNUUVFcWFBT2pOQjhJMllHZmQrL1lmMzhhWWpqYXhuOHA5dzVvMzlEK0hKanFYTXowQmNWQmpBZUtON1VrT3A5c2J3Q2xDNzJSUk9VVy9ram9PSmx5bEJHbW5uVVhmMVZYakpiZGJrMGJNS2tWYmx4NjZhWGR1VjBoWHhPQ3hmNlMvK0xXOEtZQXRDT0FmcHUvVnlwY2p6dlF5Q2VKMGdpMEJ1dHRYbXh6d01wZkRXVXd2bHg0YStUUUhCNDcrQW1wRVR0SlBmVW9ZeDlXdlhPbUxpVVNta3pUSzBUOEtJOEI4Y1RtdE8raGVRZ2N2ZEdXY3BqUlcwOU5jTjNKK0VBZE02dlVSbW81dXAwWG45NkZxOTZEQmRmTk1UNXFZbjBIajB6VTRpWjNsamY2bmRVYlFYUXk2ZWFQUEZkd1dha2crTk1wcEErYVVHUllHQU1vWElhQzlKZVU0VVdFUStzSm5aRTRuVXg0RFB5NVNnSlYiLCJtYWMiOiJlZWU5NWZkNjFmM2Q5Y2NjNGRmM2JmZWY1OTZmZDNjNjBmNmE1NDIzZWFlMWNmNTRjZDQ4YzYxZTZkNzliN2UzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:43:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946332209\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2091924231 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6ImxBeXpYYS8wM0hxRlh4cXk3bzIrOVE9PSIsInZhbHVlIjoic2twNWRaa1lXWkFBeWJ5SklYMXV1Zz09IiwibWFjIjoiMTYyOGE5MjcyNzY0YjU4MTU2MTcxNjEzYTBjMGViYjQ1MzUxNDVjY2JmNWFlMzE1OTM3OGRmMTE1ZDJmZDg2YSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2091924231\", {\"maxDepth\":0})</script>\n"}}