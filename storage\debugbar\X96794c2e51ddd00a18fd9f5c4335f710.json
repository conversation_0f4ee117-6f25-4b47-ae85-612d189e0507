{"__meta": {"id": "X96794c2e51ddd00a18fd9f5c4335f710", "datetime": "2025-06-27 00:23:00", "utime": **********.986597, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.523911, "end": **********.986615, "duration": 0.4627039432525635, "duration_str": "463ms", "measures": [{"label": "Booting", "start": **********.523911, "relative_start": 0, "end": **********.934919, "relative_end": **********.934919, "duration": 0.4110081195831299, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.934928, "relative_start": 0.41101694107055664, "end": **********.986617, "relative_end": 2.1457672119140625e-06, "duration": 0.05168914794921875, "duration_str": "51.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45027080, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029899999999999996, "accumulated_duration_str": "2.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.962278, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.552}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.9725418, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.552, "width_percent": 14.381}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.978407, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.933, "width_percent": 20.067}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1832885861 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1832885861\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-247142546 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-247142546\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1341556410 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1341556410\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-649052949 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983778279%7C52%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1Cc1pRTUhBY1dHRzd6TGN0M05VV2c9PSIsInZhbHVlIjoiY2x1T3UwbENrUVhQL1FMRmhYYnhNNkwwWkpWOFZKZnREZUZrb2lKNmtQdXRsdkgwa25qT3NSSFR1UkJnREp3aGJjazFvK2pFeFF6NC83LzNSbEY0N1JKeE95SE1aR0s3TTZwQU1iUGd1dFk0eWM4K2hEZk5pZXBBTTBkZmZyaHNHV2xoeEFGQU82OCt6UmRJMi9uVkJKYWZxNWlwQmU2YUhrM0EyTFJybFl2d3BUSW5DU3M3YUkzb2lOb1YvcXVta2dIek9jaU85U1JhbWZhcnNwcU85cmppR0xPWjFwcXpqL2RtbnhTdTVFd0RUSFhwa3RuK1FQUkw2QWdwZHBnYnZlQmpIbklxVjJXblJUankrZ0FwYnh0YW92aTAxTnUvSWxMakdVZ2Zua3lQUnZ5R3pLNDBxVmROUnhxSEcrczZJcEkxV1BwL25OMXF2ZmkwTzNrdnFYaEwyM0N6cUhCV0VQczZINzNRZ3lxbTllMUM5cDlDdEVSWTJxMkpXTjgzZDZRRGhFWlpSRDVoUVJQbVd0em5Gck9KZHZ6YnNMTER4SWhoNHV1QURPTHVER0xLYUUybWV5WmtrK25sV1M2Mzl0MHYvMmhOVWY5SVUvbVc3aXFqdmRMTUxDVi9WT1NuM2FKaWtGYlRsRWRjTXJkRUJkakNKZk9QZ3h6dG5HMTkiLCJtYWMiOiJjNmE1NmNmN2EzZDhmYThmMDI2ZmYzZDExZjg0MmE1MDBmYzZmNGUzNDYxNjljMTc0OWM5ZmZhNTdhNWQwYzE5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZRT0NDcTdaTkxJa0x0VGtQYmd0dEE9PSIsInZhbHVlIjoiL2s4aWpLbGVxZTkrWVQwME1lejZFQ2pPSU1sZnJvNEVwWktKS0hsQkRXTytHZWtqWVh6aXdTcGxYZy9IMjBNNkF6ZU1rRkd1UlNqd0o1MEF3RmYydndNVFFFdllncFQ2SXVuUGl1Njl1R1dqdHlLb3Y3U1pqQXRsbWhoQTg1RTk5UDRuK2VNeFZTOHNwbU1teUk3M0JCS2w1blR5ME5Xa2lVNGVFVGE0VWcwK0lvVXpJRTdrdldmd3pQNWp5UVBBRE8yR2RDblpQN0tYVCttNnZPQlByalR0NDR5WGtoakgrbFNvaFk2U25hdUpuL3BVcEpMdWxUTjd4MnQwemVjYjQvRkMzcDNxNFhlbmpJZDV5MDQwSVR0SysyMFkvV0hjTW5PZXJFSGxtVkhlZ1AvdEw2VjZDN2JlL05laE9vbURNRGl2alB4dkdpcWw3NTB4NlpiVDJTeGtNYkw1RkpONVV3Z2RLVVpTbHpZNmVlWmcwNWZvNCsvUXdlL1pzVklmeFZybW4ydS9TTithd2tpWGVVNkZIM2x0NzcxSUNUMmpmNDZBRGRDOHdjbTV6MXhremdTazZxY1JpcEx2MU9QeWpjRmdGQy9VbmZrK29iOFFCQW4yRHBlclZUd0sxZVpFVUEvN1hWN3NGUmJvZEtiSTkvVElsd2oyUGhvTGVWeVIiLCJtYWMiOiJmYTg1MWExNzllNTE4NmM4OGVkNmM3YWZjMTQxOWZiZDQ0ZWQzYTJlYTM3ZDhiNWE0ZTgyZWU5MzdlYzdiOTNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-649052949\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-501178478 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:23:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlQxV0RlNmZORFQxMmt0WkYwengwVEE9PSIsInZhbHVlIjoidVU4RFMwVGpzSzNKSDlxT0d3ZnFUWEw3MTJVbXRpek1JZ2I3bFc3MVZhK2lmU2xlQkcyZGNia2VicGU3N09ibDU5NE9VRWc3WERWUUpXYkVpTU5pY001Vm9UQ2x6VnhyOVhiS0pKeVhQYTBWdnBvYlE3a0FqRlhwbWs4enlKRWh6eU9nNk8yajcrL1VSVnB0NHBUZU9KWVpZdjBjbDV6SHNVZ0tvVEpYWE42NnhZTHV5bEdzWUdZYjl0L0RYSTFQZVdyMDZqS1pWeTk5WTBnTTAzTEw2ZzFoZHRDekhVcXVJYmFoRzRnR1NHYkVqektoSk5YUVY0Z09Qd0hMSDY2VFBxZTBtMmdPQlZsUG1nMmU0QWNjeWtYZ1JtNGJ0SHBzU3pxTTMrNHZ4SjFISXVLbUNjVjRNT0FzOFZLUkYvMTlwWWZTcVEvcFZsK09WRklrV3ZtR1ZxWTR5SVp4eHpENmgxVGlWcDZvQXN0VlJnUDUvY1oxa2dJeGNtc1VHVjY2Mi9YYVN4LzdLeWp5cGhNdE1pQy9pYWNUMnNPaXQ1bHIvY0FBZFJhMHY4Zy8xc1dCZHJscmVBRjM3SGpCWmoxeDRKVmRadkRaVU8zUnAzbTlROFBzRjZPY0dobXBzdmVYcHpDUzJZdGJCbGpkbGZxYmVxc2NJTmhGZTJFWDNKRjciLCJtYWMiOiI4ODU3YTE2NWU5Y2FlNDUzN2MyNjE1ZThiZTk0NmY3NDM4MmEzY2IyZDBkMTNlYzBkNjg1YWRjNWZkYjFjY2FiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:23:00 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZCUVU1ak0vNTVoaTFLdkdoQ3llUnc9PSIsInZhbHVlIjoialV5ak80djRXbGl1MTQrc1RRanA5VlRXMkRkUWdYUWc4WHFvcGxjWGozQVI4S2FnRWUrWEN6SlVockdJNVZlMWgrU2VubzgzZGw4MnNOT3FJRHpUVjBHM3lhT01vNDVLcm5jVk56Z0hyb3ZCYmtuZHUvNWF2WHFzdncwK1NsVHQ5b0NBQjRIaGRCWXlrQnA4RXk2SGRyUzVGcEJVY1ZnbC9KZ2RzMXJ3M3UwWkFqWThOMzBNREJ0aktOTytxNFdUdzF0ckNETmlMYzRmdUFmdzVJUjhkL2NTRjd2cTVpZzhkaXI1VE03SU9lcHY3ZlJObUdZT0pvSXF5T0lqV2FLcklYYyt0SnRYODF4MTdsZk54UjVhTElhVnJxa0tuVUtVaDFLL3NRZk94MDF4QmJuVGFRWHBXV0NGbU0xNjlKd3NFWHFDMWlBNllOVVhteWI1QnFpdEsxNkVaTzhOdFkvb0daWlJzVHRYVFZuTzJGbHEwalNzQkhibWpvZXZYMFZJOGZXdGtqWmJpelFqT2d5MDdtNWMxYXhYYVhUaEptcmFvWUR1N24yd1FPWGFCQ0pDZ3FyWU51K3hYa2tzR3VTUFEzT0J6V0s0OHR4ZmRWaFA0VWQrQjhCcHMwZEQrRS90ZkhOMzE2cUcxNitsM2JZYVVBNlpRdEIvVXVkcEFiTHAiLCJtYWMiOiJiMjQyZWZlNmQ1ZjEzYzFmMjEyMmFiNjc2MmRmZjc5NWQ0MzZiMzVhYWZlMDlkMjM0YmQwY2E0ZTFjMmIzZmFlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:23:00 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlQxV0RlNmZORFQxMmt0WkYwengwVEE9PSIsInZhbHVlIjoidVU4RFMwVGpzSzNKSDlxT0d3ZnFUWEw3MTJVbXRpek1JZ2I3bFc3MVZhK2lmU2xlQkcyZGNia2VicGU3N09ibDU5NE9VRWc3WERWUUpXYkVpTU5pY001Vm9UQ2x6VnhyOVhiS0pKeVhQYTBWdnBvYlE3a0FqRlhwbWs4enlKRWh6eU9nNk8yajcrL1VSVnB0NHBUZU9KWVpZdjBjbDV6SHNVZ0tvVEpYWE42NnhZTHV5bEdzWUdZYjl0L0RYSTFQZVdyMDZqS1pWeTk5WTBnTTAzTEw2ZzFoZHRDekhVcXVJYmFoRzRnR1NHYkVqektoSk5YUVY0Z09Qd0hMSDY2VFBxZTBtMmdPQlZsUG1nMmU0QWNjeWtYZ1JtNGJ0SHBzU3pxTTMrNHZ4SjFISXVLbUNjVjRNT0FzOFZLUkYvMTlwWWZTcVEvcFZsK09WRklrV3ZtR1ZxWTR5SVp4eHpENmgxVGlWcDZvQXN0VlJnUDUvY1oxa2dJeGNtc1VHVjY2Mi9YYVN4LzdLeWp5cGhNdE1pQy9pYWNUMnNPaXQ1bHIvY0FBZFJhMHY4Zy8xc1dCZHJscmVBRjM3SGpCWmoxeDRKVmRadkRaVU8zUnAzbTlROFBzRjZPY0dobXBzdmVYcHpDUzJZdGJCbGpkbGZxYmVxc2NJTmhGZTJFWDNKRjciLCJtYWMiOiI4ODU3YTE2NWU5Y2FlNDUzN2MyNjE1ZThiZTk0NmY3NDM4MmEzY2IyZDBkMTNlYzBkNjg1YWRjNWZkYjFjY2FiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:23:00 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZCUVU1ak0vNTVoaTFLdkdoQ3llUnc9PSIsInZhbHVlIjoialV5ak80djRXbGl1MTQrc1RRanA5VlRXMkRkUWdYUWc4WHFvcGxjWGozQVI4S2FnRWUrWEN6SlVockdJNVZlMWgrU2VubzgzZGw4MnNOT3FJRHpUVjBHM3lhT01vNDVLcm5jVk56Z0hyb3ZCYmtuZHUvNWF2WHFzdncwK1NsVHQ5b0NBQjRIaGRCWXlrQnA4RXk2SGRyUzVGcEJVY1ZnbC9KZ2RzMXJ3M3UwWkFqWThOMzBNREJ0aktOTytxNFdUdzF0ckNETmlMYzRmdUFmdzVJUjhkL2NTRjd2cTVpZzhkaXI1VE03SU9lcHY3ZlJObUdZT0pvSXF5T0lqV2FLcklYYyt0SnRYODF4MTdsZk54UjVhTElhVnJxa0tuVUtVaDFLL3NRZk94MDF4QmJuVGFRWHBXV0NGbU0xNjlKd3NFWHFDMWlBNllOVVhteWI1QnFpdEsxNkVaTzhOdFkvb0daWlJzVHRYVFZuTzJGbHEwalNzQkhibWpvZXZYMFZJOGZXdGtqWmJpelFqT2d5MDdtNWMxYXhYYVhUaEptcmFvWUR1N24yd1FPWGFCQ0pDZ3FyWU51K3hYa2tzR3VTUFEzT0J6V0s0OHR4ZmRWaFA0VWQrQjhCcHMwZEQrRS90ZkhOMzE2cUcxNitsM2JZYVVBNlpRdEIvVXVkcEFiTHAiLCJtYWMiOiJiMjQyZWZlNmQ1ZjEzYzFmMjEyMmFiNjc2MmRmZjc5NWQ0MzZiMzVhYWZlMDlkMjM0YmQwY2E0ZTFjMmIzZmFlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:23:00 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501178478\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-855640531 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-855640531\", {\"maxDepth\":0})</script>\n"}}