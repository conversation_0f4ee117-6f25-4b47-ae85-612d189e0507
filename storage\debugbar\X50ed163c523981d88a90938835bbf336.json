{"__meta": {"id": "X50ed163c523981d88a90938835bbf336", "datetime": "2025-06-27 02:23:56", "utime": **********.577802, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.159684, "end": **********.577815, "duration": 0.41813111305236816, "duration_str": "418ms", "measures": [{"label": "Booting", "start": **********.159684, "relative_start": 0, "end": **********.529515, "relative_end": **********.529515, "duration": 0.3698310852050781, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.529527, "relative_start": 0.3698430061340332, "end": **********.577816, "relative_end": 9.5367431640625e-07, "duration": 0.04828906059265137, "duration_str": "48.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45287704, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00252, "accumulated_duration_str": "2.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.560019, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.952}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5711172, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.952, "width_percent": 19.048}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1821320323 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1821320323\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1031281771 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1031281771\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1446443655 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1446443655\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-405710526 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991028607%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkcrV3o2Yit3Z1V4QUlTSEMyQVZWM1E9PSIsInZhbHVlIjoiOGk5MDdaR3VGSVpGUTFoT1ozQTdGT1BhdTdhbU1UQ0FhSFRRd2VVcndkOG1QUks0cHBmbUIwOGFjWGhPM3NnUjVUdmt0K1RBTW5ZNytmMG9OeU8zU2ZBMmJadzdSQW9rZWZYTnpYdHJxTzRHVyt2ZHkzVUQwT2FKZ2lFMHExaWROT0FXeXRuM0EwL2tkZXVabVJ2MGhjckFDOUdGeFZlTy9HeStMdjRwK2FEczEySmhJT3FiZkI5SmxNSUpMVC9UeE80OS9FR1l3NUl1QWc4cTlENXI5SUdpVFQ4ck5DM0tEOTl3L3BkRCs5N1J0bGd0dFY4SHRWeTNxZ1hHaVpVVUkxWVNDNlBIVFJ6TjZGem4wblpEcnhRdjMwa2tZRXR1bStNc3lVUW1UMVZaMjVkVzh5RG5paUVHcmgzOVZIbFRva3J2a0NVUWpoSFA3V0NyVUhnNGQ3cXdEenhoeTNnL0JQQ3FyS2UvRWhmcDBGSE1qMVhwcnhFcDhYWmYxK2NnV21wbVRaaTFVeElOVnRXaGw2M0kvSnV4eURyaXBoUHZhejhTc3dpd1J3aUNQQTRyaUV5OUliZXhHNVhWWWF4bXBIUjNTZ2J3eFd2ZVdsUDJQZlhTTk41a3p2UmZ3VS91YjdEampPMFRxUmhOT3hiVTNoU3RvaHA4eE5zeDhHcFoiLCJtYWMiOiIyMzdhNGMwYmQ4ZmVhODgwMjA2ZTRiNzIwMGI5OGFkOWM5MGQyYzYwODFiYTlhZWE3OWM0MTkwNGY0ZTEyODFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik9MY1E0Wk53Q2tJN0JBck1iWmdxL2c9PSIsInZhbHVlIjoibTU2R0F1N1FYMFRna0haVXFsNEltYnd1cXJudEcxYzZPZWVIdWkzYUJOVitjTDZoT3RIdmVVT2Z4QUdFclErRE9aVzdXcXhLcVViRjk5RHZqcGFBN1hkOEo0QVVsTDd2bU16RVMyN3JvMnRCVlFjczZUTnhJUHRpWkpidC90S2xrVzZqaFF4dWwzbXJmYzlkeEZqcTh4RmoxMGpWUG8wMnJOQTlKSE0yb3JjWlZKNlErVEdILzVyR3AvaHUxY3FybUJFNldzS3ZBNi85cGxlK256QWFOQS9mMy9reHVZcUhjS1BHd3BOSXFhUHV4a3JrR3RtSXVvZUdkM3ZxN3lpbnZUK05waDFYTUNUS3RjbFU5S3Rsd2RrL0ZYYUhhejh2WHVUb2EwR1p4QVlTbCtSTG9NQjlkZnB0bTlYdG5wRlBPYis2VktlQU5kb1RwZGVlODlPdHJHZ2ZtTmJpQVpJUTBSNklzV2t4TTFMR2IrbmJraStrcnhtc2NiUTBuRlBSWi9ESnBrOTN6K0tHc2Y4dEQ4U2FIM2hJaE8xTXArQ1k2WEY3aVBSRGsrc1AwaUZEcDZFSjd5UFNIbXNxNjJaSnFMUTByejV4SlFQczdQNGFIKy82cE9oajYyQWxNL0Uya1VVWHhzT3YzM1ladVYxOUtKL0RoS1hWMTloYXlDVmgiLCJtYWMiOiJhNDhjMDBhMmM2MDdlOTZkZjIxZWNiOTFiZDMwNWIwZjU1OGVmMzJiNmFkZmI4NDRiZjQzYWVlYmRjZTNiYjljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-405710526\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1067755071 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067755071\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-34260885 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImE0d0xsMjJZZ0ZlWVMybExtNzlUVnc9PSIsInZhbHVlIjoiV1U0T2Y5S0xvbzlTQjdGME5kTVZxSXprM0x4K05nc2NGK2NJWjZHdzl2MXlmQWx6cEpsQXdRdkVydDVuOGdsQmNMU0NCWE9EaER1K082aTNkZ0xmaERZZ1A5K3hoZzl1SHg1SHVxSkxlaXdFdGNGYjAwMU14d2VQNmhNVHRnVy9yYkt3NW9SVEJ0aDZoYXdVRTVmc3Vjb1Frdk9BYkFiWjY3MnBYeW9mR1kvKzU1MldNMmlkaGhna2pkWlpkZmdrdElYeGxUVnA5WEFiMTh4TUhHOG1JQ0EyUlU1OXZjeEdJTytlKy9nV3h4UkZkWHJwb01sZFhMWkdMMG1RN1NlRFpaZTNFL0FZRDZrbVl3Vjh0YzdqbnNsWS9PWGxnOTVjVTVpdWRES1JBNldGWit2U0VHSG56TFU0bmpYWEs3Qmt6Y1dpeTltcVIrQVJJK3ZBaW15K1dzcjRJa0pZYmlXOHJGcDJ4aFFtNGVUQ2g3TjUxbHYvcnhoTXg1TXJtOTl4MUFBNmg1ejQzYUtmTzZrTFQzeVY1Qlh5MkNCYnlMZlFHejg4ZU5uRWsxcUtvZmJGUkZvVjMrRTRqRUp0Umd0eWcrOE1tQnp1UGJaVSthY0hlM2t6azdvZGExQ0R6WUo2ZUNkZ1hnTldqaHU4RVJXS0IxS21uN2tRcW5QUHh1KzQiLCJtYWMiOiIxYmM5NzdlZGJhYzg3NzM1MGEwYjQ0MmZiYjg5OWJhYWE1NGE1N2IzZmFhMWIyMjg1YmViY2ExNDM1NjRiZjRiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1lN2RUL094NTY1eDUrTHAra21RVmc9PSIsInZhbHVlIjoiQjUyOTdwUG9QRldSdnk0RjRDTVBmd1NrZy9vSTRCWVZGczlGbXk2WWxZRzZ3NE5ZQXE5L3pYL3NoandXRVNsZ2IySFpUZ0VkTDVaT0U5bGgzM3BkNDNaN09wRHlhNVg5NDVQMmdPL3N5R3l3eER5WC8vKzBHeURya3E1QjlDYUpXYmJxVkdabzNQcTVSS2hzalFRTlIzelhJc3ExbnhkK0hNeGQwZEJvcWNEM0hMSFh4QTFFWnBMOTJDek53Y244WVJ2UnpTMHRTcWJHU0NxY0tRUWxTRXhQVEFjMFB2ekxGVmFLRHg4UExMMlVSODQzZ21QRUs0ZGVYQWRIa1Z6d2RyV3RiLys1S1YyWTEzdWQ3KzFzV2N3c0RVM0VCMnNPYlRSV2pXdWJBTEdxajQ5b0tIMVJiM1hVdkFXQUprWXhQbzEyVkdIcXlVZHVHVUhpdE1OM3BqWmExNkRSWklJR3l4THlEcEZEMzNLVVhmS2J5dFVpOUtyWkRVTEZWaXl3Z0dvaHBEWjE2UUJSZUxJYjZIeUV3Qk80Sy9yZWlwWjVkUnQ0aVRJNXV5dHVFQzZ6SFNPY211cWlUR2RPcENjbDU5WExJeTVrck9seWlJQjNRSVFvRmY0OW9ub3ZPbFlxL2dRekt0QncxcFF6bUlSOEVmSU9CY0JyUXpWZ1pIVm8iLCJtYWMiOiJiZDgzNzljOTUxOWIwNWUwNWU3YmUxOTAxYmMxODJkMjliMGQ4YzEyMjc2NjU2NWI2MWI1ZDlhNTYzZDVhYWFhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImE0d0xsMjJZZ0ZlWVMybExtNzlUVnc9PSIsInZhbHVlIjoiV1U0T2Y5S0xvbzlTQjdGME5kTVZxSXprM0x4K05nc2NGK2NJWjZHdzl2MXlmQWx6cEpsQXdRdkVydDVuOGdsQmNMU0NCWE9EaER1K082aTNkZ0xmaERZZ1A5K3hoZzl1SHg1SHVxSkxlaXdFdGNGYjAwMU14d2VQNmhNVHRnVy9yYkt3NW9SVEJ0aDZoYXdVRTVmc3Vjb1Frdk9BYkFiWjY3MnBYeW9mR1kvKzU1MldNMmlkaGhna2pkWlpkZmdrdElYeGxUVnA5WEFiMTh4TUhHOG1JQ0EyUlU1OXZjeEdJTytlKy9nV3h4UkZkWHJwb01sZFhMWkdMMG1RN1NlRFpaZTNFL0FZRDZrbVl3Vjh0YzdqbnNsWS9PWGxnOTVjVTVpdWRES1JBNldGWit2U0VHSG56TFU0bmpYWEs3Qmt6Y1dpeTltcVIrQVJJK3ZBaW15K1dzcjRJa0pZYmlXOHJGcDJ4aFFtNGVUQ2g3TjUxbHYvcnhoTXg1TXJtOTl4MUFBNmg1ejQzYUtmTzZrTFQzeVY1Qlh5MkNCYnlMZlFHejg4ZU5uRWsxcUtvZmJGUkZvVjMrRTRqRUp0Umd0eWcrOE1tQnp1UGJaVSthY0hlM2t6azdvZGExQ0R6WUo2ZUNkZ1hnTldqaHU4RVJXS0IxS21uN2tRcW5QUHh1KzQiLCJtYWMiOiIxYmM5NzdlZGJhYzg3NzM1MGEwYjQ0MmZiYjg5OWJhYWE1NGE1N2IzZmFhMWIyMjg1YmViY2ExNDM1NjRiZjRiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1lN2RUL094NTY1eDUrTHAra21RVmc9PSIsInZhbHVlIjoiQjUyOTdwUG9QRldSdnk0RjRDTVBmd1NrZy9vSTRCWVZGczlGbXk2WWxZRzZ3NE5ZQXE5L3pYL3NoandXRVNsZ2IySFpUZ0VkTDVaT0U5bGgzM3BkNDNaN09wRHlhNVg5NDVQMmdPL3N5R3l3eER5WC8vKzBHeURya3E1QjlDYUpXYmJxVkdabzNQcTVSS2hzalFRTlIzelhJc3ExbnhkK0hNeGQwZEJvcWNEM0hMSFh4QTFFWnBMOTJDek53Y244WVJ2UnpTMHRTcWJHU0NxY0tRUWxTRXhQVEFjMFB2ekxGVmFLRHg4UExMMlVSODQzZ21QRUs0ZGVYQWRIa1Z6d2RyV3RiLys1S1YyWTEzdWQ3KzFzV2N3c0RVM0VCMnNPYlRSV2pXdWJBTEdxajQ5b0tIMVJiM1hVdkFXQUprWXhQbzEyVkdIcXlVZHVHVUhpdE1OM3BqWmExNkRSWklJR3l4THlEcEZEMzNLVVhmS2J5dFVpOUtyWkRVTEZWaXl3Z0dvaHBEWjE2UUJSZUxJYjZIeUV3Qk80Sy9yZWlwWjVkUnQ0aVRJNXV5dHVFQzZ6SFNPY211cWlUR2RPcENjbDU5WExJeTVrck9seWlJQjNRSVFvRmY0OW9ub3ZPbFlxL2dRekt0QncxcFF6bUlSOEVmSU9CY0JyUXpWZ1pIVm8iLCJtYWMiOiJiZDgzNzljOTUxOWIwNWUwNWU3YmUxOTAxYmMxODJkMjliMGQ4YzEyMjc2NjU2NWI2MWI1ZDlhNTYzZDVhYWFhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-34260885\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1791061303 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1791061303\", {\"maxDepth\":0})</script>\n"}}