{"__meta": {"id": "X6aea8e33192b77020a56183229441e91", "datetime": "2025-06-27 02:34:02", "utime": **********.426975, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991641.990179, "end": **********.426987, "duration": 0.43680787086486816, "duration_str": "437ms", "measures": [{"label": "Booting", "start": 1750991641.990179, "relative_start": 0, "end": **********.351258, "relative_end": **********.351258, "duration": 0.36107897758483887, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.351265, "relative_start": 0.3610858917236328, "end": **********.426989, "relative_end": 2.1457672119140625e-06, "duration": 0.07572412490844727, "duration_str": "75.72ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45737176, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02172, "accumulated_duration_str": "21.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3813388, "duration": 0.02096, "duration_str": "20.96ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.501}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.411184, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.501, "width_percent": 1.703}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.417278, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.204, "width_percent": 1.796}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1041556206 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1041556206\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1837720280 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1837720280\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1988742607 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988742607\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2083080153 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991639383%7C39%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijk2NnJvV3JwSTdkalNodkwrZDVCNkE9PSIsInZhbHVlIjoiaS9LT2NaZlJ5UVhZRVF5azFXVXJDZjFSU2xuelRJdlRjR2NXUG5uVWlPSk9lVVY2NldFUlA3YkFlbU0wNllZWEdVK0hTMDVSSVVOLzQ4ZzBrM2lDSlJYZ0xCRGZQMHIrTEJDbmNuVWhvWjdsTEFFQ2lxU3pUMWczblZsT3ZUSE52N0pHTEhzUFpxcnVtbVp4dkc1bnU4LzJ5YnNrL091K1l2eit3TGJOZjFPMkFjdWxueCt3aVhtTVhWb25iSUR3bzdoeXk4cjg2QnlTZVpmbGpJN3dud2U5QVNGaklOditYTGV6ZU15U0l5TVQyTHFUVmVUMmptSzJMVXJDWDVxNDU3RXFQQm16TERqM3NQaFhGYmtYWjBHa3lFYjk4SFZ2Tm0wMUdKV1JJaGN5N244U1RHK1cvTWc5NTkwbjZYeDlHWUdCZGdHZ3dEL1hlNHp4NThDNUJSckVNNmNjNnFLSkhjdDRKNjQxTEtiaDkvNWEram05R2NNOXQrblNpWldSYWkwSUtDdmJncXlJemNNcTdkWUJBT1FobGZucUlMSzljYTdrb2VMZDMzTS95Q2FvK1dhbDF5UzI5MWZDTlhPeVQwS2xSUS9pS0dLMlQ1SXBEckd5YnhJTGhsOFZwdjJLNXgveGtqa3VSNmxvV3ZNcnhoYXc0aUljL2FxWHpJM3UiLCJtYWMiOiJhNmVkNTAwZWJiMDdkZTAxYzc2MWE0YjgyZDdjNWY2NmYyZmQxNzFiYzNhZmM5ZWNlOGU0NjRjMDliOTIxNDIxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZTY3J5SlNZcmI1cGxGZHBJR1kvcWc9PSIsInZhbHVlIjoid1NTSzR1dEpXSndhbFJRRW9MM2tjTVpmbER3QnZwQnR6VWs5QW8xc1ZqaUtpbXFuYTF4MmROWEs5b2pLWm1yRklhUzVtSERuYjF3OXd0bmZ1NzR1NkFVNHF5c1VNMExpYnhZL1NPMGRnc3NLTzlpKy9NcnhJaWFuL1JpTTJMdnBhWHJ1a0ZUUG4rRU1GeCtLN0xWKzNla0RzZzJaTHh5NE5aWG9KMXlLU0FHZUhBOTRHRmxpMHNUYUxURmUzTnZnTGFkUVl3UnF3RGhwTWZJcVJOR3ltRVNFbnJtNmZmVTNKd2dnK3hUaXJqZ1dzazFEV3BrVXVabFd0SlBMY0U1cjJIemJGbmR5VVlWZjBSTExOT2pWZWZBWGZUVko4bWNOUjJXZlNuRGh1ZDZENEVJdWYvazc1MXo0RjJMdGlUZWFETnIwcUtzQnlDS1IyVWNhNDBGZVRjdi8yOGMrVFF3aU51S1plMmdkTWlBN2dIMW9tV0o0bHhvU2oxZ3FIVSthUkMyNk1Vc3BrUEY4cm1DRXVsVFdVZDFKY0VGYUU1ckJCeVpCTVMvVkpKT0plWXpSc3d6SFBTMDF6SEtkQUNzSklSME5XSnU4Zk1qUVpHZWpvRTBhQ0pJRm1OTS9adEhkWUdNTVllSUxEUHlVWXlsT3FuZDBUNWpEM1ZGME5nSEEiLCJtYWMiOiIzZjg5MWVhY2Q4NDkzYTlhNzM1YmZmODI5ZDJiZTY1NzdmODdkMjg2MzA4NWZhNTIyMDA1MzFjNzY0ZDk5ZjdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2083080153\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1078326362 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078326362\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1477109095 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJpZFRTVmpCbzU5TE9TWFBhYWhXY0E9PSIsInZhbHVlIjoiWUNrVFN5a3pFUFVLdU5vbGI5bXgxYm9sMjhnVHMwMEpjc3dQamxybURHZmdIeDJIa3lHNERVOXNXbHZMMVVINVFYWkFrSytONTAwdkdsMUlLNFlMRFpxSUgyR1B1d1VnVzhsZGdHRXVVelhSbEVxQnN1cHAvbWo5YVIyY0lWWDI0Vi9kSmVaU0RpQk5PdTVYcHFOWE9aMlpta1Y1bzUvN2dsUHZ2Uk1pOWtLQlpSajUvU1ZLRlJWaUNxTnpIY0d5bldEdkpjL2RSZk5Yb1JCbE85WG9NNlNKODZlVzJhQTJNTmhuNjVPVlZpdTE1RlFaVXA4dUNsVHZhRi8zSWFmWEw1MmdOMFR4VzVNYXpjYTJRTlU4cEl0VEZWeGpFU1pSVVlPOGxjeFpiaGVtT1d5ekwvMm5QeVhLU2NpcWhXQzBhVHdwRkpwek9wQytDR2lUK0sycDBMNkp0M3ZzRzM1Y0lYYkJNeVRaWFBJSVNNVnVqZStOYWN4bmtKQ2htMytRUnVPaEN3T1puRk1hYW0wY0VteitNZnN3NG4zSmVUVm55OEQ2c0RkeVNnZjFyNlhNT0oxbGR2UDBqTVV0SmgxUDgwY0pncS8xcTd2cS94WTVOU1FDZHFpNGNyV1ZCTnBoNHBTOTd2bE5WOFd0NHJSS3gvQTJTOU9HbG9PblIyUE4iLCJtYWMiOiI2MjNhMzJmNDZlYzlhYjRmZjE4NWFmZDBmYjMyNjJkZWVmYmRjNTMyYmFjOTdlMmU5NGY4M2UwZjU4ZDFjZjRhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZ3RDFCYnJ3L2huN3VzbFFCL2V1MXc9PSIsInZhbHVlIjoiMTI3eXY3aXRjRGw1VUJ2em1ZWElrYWkrVjFDeUl2aXk3YUhGeGFoM1EwZUdLMGJhN2JSUUNadW05aVJlWHVVdG9XZGRIYlVRcHJaRFZUQnVFakx4MUxoY0RDRHgxbzIwQWRsSnFDK2l6NDZoekRaMHNrSUhVSkZONFBPNW5PeFQzVk1ocEZSUzEzelZsMEorUDZqWG1BbkhDd1ZxcDRMbm1ybHpLRUI3NlhldGpESU12TUM3MDd3Z3psV005eU4vY0dRMlpsN0VLa2Z6enpxcGFRZWxKaDJ5azNlNzM2SUVGNm5XaHpzbmk0blZFenBOMjg2NExhN0dobEFIR0UwM0hjMWxMOVFQUUJwNTZBSWpxRzVyd2hmVzk0QTVoU3FTdlBBZnc3TnNZVDh4VVJMaml5Sngwelc2clh1RWNzV0daN3RZZ2JUMTZZNjNSV1RCd2huU2RoSTF6K0NxaUZrS2VUcG9iSEJEblRCWmdiQlJrYjRNWUtlRHdONXpYTWtPTWFpZFQ4T0xCaUhMNS9FRXk5aGNRRmczVXN3dnUreWRFRCtyTGdKMnBnL2NzSlI5UzBXRVFDR0NEVDNJd3k1ZnRydWhQT2R0azBoZmIzN292MVpYVUtuTWlvRnJ3RFkvSXJWdlV5R1F6dGdHWGQ4czkySHBwZm1kbFMxMXBYdngiLCJtYWMiOiJkMTE2ZmEzM2RhMjdkNGMzNDc0NzhkNTU1YjljOWY3M2ZiNzM3OWJjMTY0YWRjY2JlYTlmZGVjNzVmYzdhYTc0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJpZFRTVmpCbzU5TE9TWFBhYWhXY0E9PSIsInZhbHVlIjoiWUNrVFN5a3pFUFVLdU5vbGI5bXgxYm9sMjhnVHMwMEpjc3dQamxybURHZmdIeDJIa3lHNERVOXNXbHZMMVVINVFYWkFrSytONTAwdkdsMUlLNFlMRFpxSUgyR1B1d1VnVzhsZGdHRXVVelhSbEVxQnN1cHAvbWo5YVIyY0lWWDI0Vi9kSmVaU0RpQk5PdTVYcHFOWE9aMlpta1Y1bzUvN2dsUHZ2Uk1pOWtLQlpSajUvU1ZLRlJWaUNxTnpIY0d5bldEdkpjL2RSZk5Yb1JCbE85WG9NNlNKODZlVzJhQTJNTmhuNjVPVlZpdTE1RlFaVXA4dUNsVHZhRi8zSWFmWEw1MmdOMFR4VzVNYXpjYTJRTlU4cEl0VEZWeGpFU1pSVVlPOGxjeFpiaGVtT1d5ekwvMm5QeVhLU2NpcWhXQzBhVHdwRkpwek9wQytDR2lUK0sycDBMNkp0M3ZzRzM1Y0lYYkJNeVRaWFBJSVNNVnVqZStOYWN4bmtKQ2htMytRUnVPaEN3T1puRk1hYW0wY0VteitNZnN3NG4zSmVUVm55OEQ2c0RkeVNnZjFyNlhNT0oxbGR2UDBqTVV0SmgxUDgwY0pncS8xcTd2cS94WTVOU1FDZHFpNGNyV1ZCTnBoNHBTOTd2bE5WOFd0NHJSS3gvQTJTOU9HbG9PblIyUE4iLCJtYWMiOiI2MjNhMzJmNDZlYzlhYjRmZjE4NWFmZDBmYjMyNjJkZWVmYmRjNTMyYmFjOTdlMmU5NGY4M2UwZjU4ZDFjZjRhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZ3RDFCYnJ3L2huN3VzbFFCL2V1MXc9PSIsInZhbHVlIjoiMTI3eXY3aXRjRGw1VUJ2em1ZWElrYWkrVjFDeUl2aXk3YUhGeGFoM1EwZUdLMGJhN2JSUUNadW05aVJlWHVVdG9XZGRIYlVRcHJaRFZUQnVFakx4MUxoY0RDRHgxbzIwQWRsSnFDK2l6NDZoekRaMHNrSUhVSkZONFBPNW5PeFQzVk1ocEZSUzEzelZsMEorUDZqWG1BbkhDd1ZxcDRMbm1ybHpLRUI3NlhldGpESU12TUM3MDd3Z3psV005eU4vY0dRMlpsN0VLa2Z6enpxcGFRZWxKaDJ5azNlNzM2SUVGNm5XaHpzbmk0blZFenBOMjg2NExhN0dobEFIR0UwM0hjMWxMOVFQUUJwNTZBSWpxRzVyd2hmVzk0QTVoU3FTdlBBZnc3TnNZVDh4VVJMaml5Sngwelc2clh1RWNzV0daN3RZZ2JUMTZZNjNSV1RCd2huU2RoSTF6K0NxaUZrS2VUcG9iSEJEblRCWmdiQlJrYjRNWUtlRHdONXpYTWtPTWFpZFQ4T0xCaUhMNS9FRXk5aGNRRmczVXN3dnUreWRFRCtyTGdKMnBnL2NzSlI5UzBXRVFDR0NEVDNJd3k1ZnRydWhQT2R0azBoZmIzN292MVpYVUtuTWlvRnJ3RFkvSXJWdlV5R1F6dGdHWGQ4czkySHBwZm1kbFMxMXBYdngiLCJtYWMiOiJkMTE2ZmEzM2RhMjdkNGMzNDc0NzhkNTU1YjljOWY3M2ZiNzM3OWJjMTY0YWRjY2JlYTlmZGVjNzVmYzdhYTc0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1477109095\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1094033559 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094033559\", {\"maxDepth\":0})</script>\n"}}