{"__meta": {"id": "Xeaf67e113aba07a5b754cb634544f9cc", "datetime": "2025-06-27 00:14:51", "utime": **********.132842, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.627265, "end": **********.132858, "duration": 0.****************, "duration_str": "506ms", "measures": [{"label": "Booting", "start": **********.627265, "relative_start": 0, "end": **********.039385, "relative_end": **********.039385, "duration": 0.*****************, "duration_str": "412ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.039398, "relative_start": 0.*****************, "end": **********.13286, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "93.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.04032, "accumulated_duration_str": "40.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0681071, "duration": 0.03928, "duration_str": "39.28ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.421}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.115715, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.421, "width_percent": 1.166}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.1235828, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 98.586, "width_percent": 1.414}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C**********489%7C46%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdmSStrMnJSRXZKelZGSlo1ZEc2a0E9PSIsInZhbHVlIjoiRmQvN2c2QnBjWVBVd0M1M1E3d1ZQalc2UkwvZ2dSQ1drYzQxOTZYUWl2Z0dXaWZVaE9iZ2JLV2lYazQ2T2dEaUlVRGFpK2hGQjRKbXh2aW52YTVnNmJ3dVhGU1dDd3NBa29LVGpKblVIbE1IMW53UllFbytXTUkyd0UxWUZFWWZ6a2pSUGYrTVZobkR5N2RYcnpwQXdEa0UvZjcybTIrY0MwWUpLTGhFMG5xRzVNVjZaUmdBMGovOTNVZGFLNUx3dG1qTDVtSHhOaVhkZzhrMHBodVBuVVkrUzRUTE9QU250SWIxMGk4THBvVERmREFMQ0Irc3RYS3EzU2Uwa1Fxd2xjUW5td1oyUTJNTHBPZUw2SUdjVUxOUFkyaXp1QWxielJOeHBhM2t1SUtOZ1JhV0MxaEgwQWhGMllkN2YrQWFrQVBJRUlSSVJJbmdnWVlEdjJnamttTUxMNkIxekgzTm9WVld0cWdneUl5eE8zRmg5SzVxazd0Y3ZUZzdjOHNQR2VWbTRSS242MENwV0xFMzN5aW1saHNsZlY5WXVNaHdNZE5yQUY2ck1oNCtvNlFsWkc2WXdkbGxRZE5QdUE1WWNzRlNReUtNem5mSGdnN0tWdzgxVkQ0djdBakpzMk9HTnZsRmh3b0VzTHk2S1loSzh4YTg3OG9aUGxiNWY2TWMiLCJtYWMiOiIzMTBiYTQ0MzI4YWM0NDBiMjllNzg1ZGY3NzJjNTJjMDM2ZDU5Y2UwZDkzMTg3ZmI3ZTZlNmVlYjIxNDZjNGJlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdJR1dncEN0OWVOMWhUbWhva2dUcnc9PSIsInZhbHVlIjoidXNjUFJxWGtuSkQzRnZCOG5wTUU2dWV5OGZ0UjVKT2ZFMVRycjZGL1htZUtGaTJrSFlGNkJ3WGlhSmhMTUFlODFoQ3lFM0NMNzYyUHBvQjBpRzJNbHBDZ204bEdBMjM0a3ovN0FrYnFyclNiSDRHa2lyL2VzcWx4a3RFYTlrRW51V21jbnEvaHBodVhsaUUyVTQvQThsaWJBeGY2dWVWNmQrNkIxS25MSFN3a202VWdyc3R2N0JJSWxWWkE4K1hpb1JmaDUxRlNPTTNhMnFMVFlCVHhLZVRXNCt5Qjk3NEN6Y2xnd25DT2NQdnlOdU1JRk5GVXVqMW1pckVCRlJ0V09WejhYZ3BYMnA3RTh1ejZqcEFzaXEveThNeFVETEdxTFFlRFFrb2VpWFZSY3JPOTNDaDRQRDB0RjZKdnJqcCtmS096N2xGenVIUy9RRnZ5cUROZEFCM09EL3d2Z0VLa0UzNEVyMGxCZEhyTFQySUZtbFg4VUpCazdYbmsxUGJnR3dGVWNXQTFvSCs1VGJyTVpudnd0UEJYOUpneEJnY1Fid2grQ21zWnFsa2dqTVZ1NFRWTFo1R3AzdndvUWZFU3ZaV3o4dFJVNnViaVNFclRGRGwvMlRnbGlXS3ZaeDBaWDV5VVVqNnVheDhzMktQY1JvOGJ1anlYUm5lZVJGQ1giLCJtYWMiOiJmN2UzMGYwOGIyNTQ1MDQ2ODUxOTUwMjJkM2JkYzNiYmM3ODU0YzY3NzJiNDc2ODE2MmI2ZmVmMGI0MjhlNGUxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1191441480 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1191441480\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik8wcmV4VGcvUGFSVVlGTjRvcUdrc0E9PSIsInZhbHVlIjoiRnpYU2pmU2Q5S1pxUXFRL3hKaUlOaldkQ1NOczNCQTJ2ZGRqTE1tNTJKam50SnRRelY3Z0JWejdCWDNWYmNZQU8yWkR1R0ZyemVKUXZRS3dqdXg3akNQTkl0eHd1czh6eHlFMVRIL09ic29nbGhydG5Bek5TK25ObEdEOWx2NmVETkRCc0lEaHBuL1lPT3gxVlA1cnY1QVFsZ1hRWHJjVkVtRm1nSHRLUS9YT2h2MHJnNFROWGFvQW9zeEVLc0pRWnZFRExuUDBCWU5mdEVxZXNjZXQ2UmxiaWNzKzNGb2s4Mk14UStQb1E5NnFLWUNVYlR3RHlhWGhxOHFwcmQvZ3d4M09CdlBZTUlpK1h1ZWhzR2dhaHE5emQxMThUVitpb2lxaGMwYWhRQlpSVmJadG01Uk1CM0hJWFZZRHI2T2E3eEZtOEU5MHZOZE4vWkFJZlFaV3hJbjdRL1o5OHI0TTA1SmpuSWdLMmFQOW82czJkbkJWb2w3T1AvMi9BWndDcDFFaHlyelRYQ3VVYzQyYlRHUFB2UXVERThpWW16RERNOEtSN0dpMlpLUjc0VjV0di9VcExLaGlhYlh6NEtnNWtYMW1ackgwQndEdHBKRUd3OUZabDcwbG9nUi9jVUNvblpGR0Fac2hiZ21Eb0dxMWExZC9vZmtSa3g3dDBsWC8iLCJtYWMiOiJkOWI4YmUyY2Y1MDIzYjBiMzEyODhmOWQ0Y2EzNzBhMTJiMWQxNjA1YjEzMjYxOWJlYWIyMDJlNGFjNzEyOTM5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRmOG56RVJ4RW9TOXVTUlU0Tnk4MlE9PSIsInZhbHVlIjoiTVNabXhrSXVSRzg2QldCbzVMOVlraVcwVlhTN3h4dVlyblZqbnNmOElzQWt5QW96QjN3bGUvNWFUNjkzQlRWN2E1NFZtUXI4bWFkWkthd3prZml2S1B1am4xMC9sOXZHTEFTbmdaOC9abDBaSGM4MzUxUFkzNi9uVVRMODIvRkhsc29LZlVRdk84S2FaWmgzY0szWkhRVDZvZis1ZlRJK1Bpd0JLTEZQcTJ6MldScVNKUjJpc1ozVjI5dkUxMzc2U1ZhU2hWbkJ2RFZDVTV0UHBlZFJCRzE0RVJLY2xFbHdoY3RwcGkrNTQ0VElUcmNLd1Bhc0Z6b3dDTE4zemRxS3NEeEl2OVcvL3g5MVBOT3dFS21EbXh6Z01VRDNqRm9uS0pZUlY0Smh6T0dvVjJFV0MrY294S2l1aEhSbURJYWNLTHd0eWloaGtMVjZvNTE0eTF6QXNlK3VzeHVYbkhzKzh6Rk5Cb3MzTmtPYjUvZXgycG9DYUFpU3Z2OThORFJVbFp5VHN3ckxBeEhpcFJNWEN0NTBqWEVOS1JWTHlXYVBXNEJ6TkRxWEIrazgvaVNaMGFUWUV2a1hJQzB6a0poU2pYVGM2bnJsSmdPdHBiNEwxK1Z3R1l1bmx3MHFhWW9QWm1CLzd3amZsaW5iYjNIMDZQQzhxcm0rb1pOdE4rNG0iLCJtYWMiOiJjYjE0MzRmZTUwYjY4YzUxZmE5ZDQyODI5OTAyYjBlYzBkMWFmN2IxMGQ4YjcyYzYxYWVkNGRiYzViMzM1NjMyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik8wcmV4VGcvUGFSVVlGTjRvcUdrc0E9PSIsInZhbHVlIjoiRnpYU2pmU2Q5S1pxUXFRL3hKaUlOaldkQ1NOczNCQTJ2ZGRqTE1tNTJKam50SnRRelY3Z0JWejdCWDNWYmNZQU8yWkR1R0ZyemVKUXZRS3dqdXg3akNQTkl0eHd1czh6eHlFMVRIL09ic29nbGhydG5Bek5TK25ObEdEOWx2NmVETkRCc0lEaHBuL1lPT3gxVlA1cnY1QVFsZ1hRWHJjVkVtRm1nSHRLUS9YT2h2MHJnNFROWGFvQW9zeEVLc0pRWnZFRExuUDBCWU5mdEVxZXNjZXQ2UmxiaWNzKzNGb2s4Mk14UStQb1E5NnFLWUNVYlR3RHlhWGhxOHFwcmQvZ3d4M09CdlBZTUlpK1h1ZWhzR2dhaHE5emQxMThUVitpb2lxaGMwYWhRQlpSVmJadG01Uk1CM0hJWFZZRHI2T2E3eEZtOEU5MHZOZE4vWkFJZlFaV3hJbjdRL1o5OHI0TTA1SmpuSWdLMmFQOW82czJkbkJWb2w3T1AvMi9BWndDcDFFaHlyelRYQ3VVYzQyYlRHUFB2UXVERThpWW16RERNOEtSN0dpMlpLUjc0VjV0di9VcExLaGlhYlh6NEtnNWtYMW1ackgwQndEdHBKRUd3OUZabDcwbG9nUi9jVUNvblpGR0Fac2hiZ21Eb0dxMWExZC9vZmtSa3g3dDBsWC8iLCJtYWMiOiJkOWI4YmUyY2Y1MDIzYjBiMzEyODhmOWQ0Y2EzNzBhMTJiMWQxNjA1YjEzMjYxOWJlYWIyMDJlNGFjNzEyOTM5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRmOG56RVJ4RW9TOXVTUlU0Tnk4MlE9PSIsInZhbHVlIjoiTVNabXhrSXVSRzg2QldCbzVMOVlraVcwVlhTN3h4dVlyblZqbnNmOElzQWt5QW96QjN3bGUvNWFUNjkzQlRWN2E1NFZtUXI4bWFkWkthd3prZml2S1B1am4xMC9sOXZHTEFTbmdaOC9abDBaSGM4MzUxUFkzNi9uVVRMODIvRkhsc29LZlVRdk84S2FaWmgzY0szWkhRVDZvZis1ZlRJK1Bpd0JLTEZQcTJ6MldScVNKUjJpc1ozVjI5dkUxMzc2U1ZhU2hWbkJ2RFZDVTV0UHBlZFJCRzE0RVJLY2xFbHdoY3RwcGkrNTQ0VElUcmNLd1Bhc0Z6b3dDTE4zemRxS3NEeEl2OVcvL3g5MVBOT3dFS21EbXh6Z01VRDNqRm9uS0pZUlY0Smh6T0dvVjJFV0MrY294S2l1aEhSbURJYWNLTHd0eWloaGtMVjZvNTE0eTF6QXNlK3VzeHVYbkhzKzh6Rk5Cb3MzTmtPYjUvZXgycG9DYUFpU3Z2OThORFJVbFp5VHN3ckxBeEhpcFJNWEN0NTBqWEVOS1JWTHlXYVBXNEJ6TkRxWEIrazgvaVNaMGFUWUV2a1hJQzB6a0poU2pYVGM2bnJsSmdPdHBiNEwxK1Z3R1l1bmx3MHFhWW9QWm1CLzd3amZsaW5iYjNIMDZQQzhxcm0rb1pOdE4rNG0iLCJtYWMiOiJjYjE0MzRmZTUwYjY4YzUxZmE5ZDQyODI5OTAyYjBlYzBkMWFmN2IxMGQ4YjcyYzYxYWVkNGRiYzViMzM1NjMyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-498500692 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-498500692\", {\"maxDepth\":0})</script>\n"}}