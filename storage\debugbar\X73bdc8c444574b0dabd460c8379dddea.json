{"__meta": {"id": "X73bdc8c444574b0dabd460c8379dddea", "datetime": "2025-06-27 01:13:27", "utime": **********.534639, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.118314, "end": **********.534653, "duration": 0.4163389205932617, "duration_str": "416ms", "measures": [{"label": "Booting", "start": **********.118314, "relative_start": 0, "end": **********.485417, "relative_end": **********.485417, "duration": 0.36710286140441895, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.485437, "relative_start": 0.3671228885650635, "end": **********.534654, "relative_end": 9.5367431640625e-07, "duration": 0.04921698570251465, "duration_str": "49.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45032288, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00287, "accumulated_duration_str": "2.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.510458, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.369}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.520856, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.369, "width_percent": 17.77}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (23) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.526397, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.139, "width_percent": 19.861}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "23", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-717330421 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-717330421\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-238491504 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-238491504\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1657307984 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1657307984\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1288186456 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=tu6zsp%7C2%7Cfx4%7C0%7C2004; _clsk=1ggm1jz%7C1750986797629%7C1%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjI3R0N3cmJFZ2pDa0h4VE5OWE9maXc9PSIsInZhbHVlIjoiajQ1aVJ6d2xvYWErOUZXK0NvMThMeXlzSFVvL3hoc0RnOVF4RU41TE10YmdqUm1VNWMwdFFmQWlsYWpxaHV1VGlaZXRDd2gzelNOV0V5cytxKzBGK3hIbWszTm1wVmlxOFFISzVrdzFZYkpGOVBGUmlYZTNzT2FCbk5jVjhwbmYwOW1nci9lUGdTc0E1aHRFVjlHZi9ZQ2s1SkZMT0cyMDI0Q2h2YU82Wng0Ym1KR2Q3eWsydFdNdGFPcDdZZVh6WHAvblIrYjJVZ2lpY20xbTk0QlZDbUp4WEZmR3BPeVZ5cVNiaHIyemJ2Q3V3Z2FwOFlHcHE1cFlXWnlGbnRJSTh3U3Q2VXVuYWl1RmxTdVhSSjhSV2tnb3lZVmI1eWtjVzMzbU5RUE1UUTNaZFJDV0U2OUZDS2x6eXI2SC9UUlFISENnMlBpampxRGMwL3VhcmZBYW1UWEpzZEx5R1FhK2ZkTmVRVGVEaVJlNTUySHhYMzZVYmFmVU52c3N2eFg3ZmZOaWE2UmtVbkJRK3hYVzFnbHNLU2UzZkxxV0JLNkttQmhkc3JJTGdqUXNLVGhZL3RRbVk0MTZYMk9oUkJRYU53elU4aDdCTWkwSHFuUEJZd3ZnOUl3aW1OVmZnQ0I1NURENDUyQW1TYXFOTW9QRlFtMVVlN0ZRTGxhWWMrUmQiLCJtYWMiOiI2NTk0NmU0OGRhNjE0MWIwNjc4NmU0YTQ2NjM0NTI1MzVhNDJkZTk5YTE0NDE4YjU5OGJkMjBlZTk2ZGQ3NTNlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJPbGJWOXpuVG5yRlZqa0dFMW81T1E9PSIsInZhbHVlIjoia01MYkdjOUZKdU5lbFdjZTh5NVc3ckhZMWU3cWhlVGtVWnJua28xOTNNVEpVV2VtS3dQMys0VGJ3SmErMTlCTUwzYk5CSS85dWxSZnNJd3ZkSVpvZDMxbVlqYjBmOHBNQUQ0LzZqb1UxeUVvdWt1VnpSMlVvSzVReUJ1MTRydVdFQW9TNldROTlpK1EyOG9HZjJTbHZRZU5udUVSdzF2bjMvRFFZSzBXOEZKM3YrQi8wRmJ6WTRuK0hxUTBST1dlUTlPZ0dqRk83Q00zOHBZekE1cHROalFYOExRdC9pSU1xNkZkRG5ENm9iSDdtNC9QdzIzS1VZYk55c1c0Wk92TWR5dWs1TGtTMm10aFNaUnk2OHlMajRWMkk2cW5vUERvbSs1Nk9wQng3WUNmRTh0RXUzalhmRDZoOGRmdzBGcVQrcGg3N1V4U1lBd0hXdkUvSW9sSnhCdFFBeHdMQWY1cmdtSitxZGIwelBMV1o5L1NobTdtaDZIYWxjWVlzaGlSNVNUNVhEdWxXRzlVeHI2RTdnbGR5anB4QUZCWVYzdUM2ZjQvWHBCZHJkVVNBbHl4Y1l6bmxhRHF5TnhBekdIQ1MvNVFzenBIZmZZSFRhcTA4UE9kQ09QN2Z5c1R6ZWhQYU8xRUthT2RiZGpvb1ZLNk1PdzI0MWZIU0V1NWFKTm8iLCJtYWMiOiI1NjdlZTZmNmEzNmIxY2I2Mzc5NTY2ZjI5ZmM4YzU4MDc4YmQwMzFkYjI2MjM0NGY3OWM0YTNmYzFhMGVjNDQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1288186456\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-266447115 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QVqhpWieKZCLT3nmskTMtatIDNwBdc2egPwBt6XG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266447115\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1308432201 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:13:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5vMUVHMmpLTFlCNnV4aDNqK2kyZnc9PSIsInZhbHVlIjoibTNESG5hZWxzWW03SmY3OHFETGlNaFpDNkVoaEVWNGZUbERLM2lraHpCenMrV2c2ZExIRng0T1FJQkM3WklaQTIrOWxYRGZyS0h0UkE1bENmMWNQcmZFMXNtRmdrc0lMN0dSVGZia0xvR3dNS2NKSzZ0MDZmN0xZL2Q5ZmN0Zkg1ZUhnMzNON3pPbmFESDEzMjBCR0thUXJZR2UyVDBTdU1IcVcyT0VndWhHdGUxZHFkK3ZPNkxaNFRyMERwdVowaVJYQUhtWXBxT3VMMG9Jd2lrbUw3Z0J4elc0Z2RlN1kwN0piRE9wOE1QUG55bVUrTTVHM2VWSU9XQ1Jld0xDd21xejlzcWpXck93Z25YZXRERnFQWjRaR0tjTXptQ2FxdEFnVVFHS0dTM1JLM21EU3BEZzZGZXBZN2pRVmsvbEYvV01yUDcrZWhTcjd5a0FiZVNhUzM3SndFQlpqczRQQWVIMVVpZ01xamhENjRkVUJIZCtkVVNDdVBGaTdnWjNTbFg2M2xGQ3IzN0FuWDd5azU5RjFrZ3JtOEtoQ2EyRTVuRmJVa2VWSmh0YktkcFpDeTZjbHc5OFRsQmo5ZzNRMVR3dU14T3RPUGZzaDBwdzN0Qml6TFpTVFNzVlZra2FwbzFRYnMxNHhieTVMdHRUL0NFalJRK2gzRFZxWVFFUUwiLCJtYWMiOiJjZmFmYWFmYTY5NDY0ZDQwNTJmMDZiNjZjOGIyNTlmMmY3Yzg1MGJmYjAyMDg0ZGNhODkyMDMyYmYyNDk4NTFkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlpsSFl5cjFyRmk5eHRwejJWdGRMT0E9PSIsInZhbHVlIjoiRXVqeDFzK0M1YW40ZjlwdHpibVppdFFjMFU3K0plb1VLcDRzYmU0VlBRNnJWb25VbGJKMERueE85WlpYdzRyQTUxTjFzeFRxY29yalV2cXUxNWhobHZqTHdYNTZHaHUzdElrdHBGSEtSMmVITGUzUWtGa0g2QjBYZHNDY2Z1c3hSQjYxYzJxd05lU3F3SmtQZXVnL01TN1AzTmRCV2lDVGpESUNiWjcxMEF2NmpnaVEwQTJqMC9IRHdxMDFhN3FYTW8xOXV2UHN5Ti8ycW11S1lvRXYrT3ZMN3Z2eGFDa1JXYVBOYmdYQ1hJUEV5bWhpU2FsalIvSkpsWWcrUFc1bXY5d2VNbUdONmlYS055UW95cC9uVXR6b1RSSWtLaUxCRUpXc1M4Y3cvdnF0aDk0WFlyNk9YSmc5cm92ajNZVzJ0ZXNFVlZIcTM3VzlTYjRpcEc3bEZyY2NmUVg1d1ZBeXpoUEJSRjh4aGdVS21nbnlXdFlCMVhxUEJ0bWhEQlhtZExLcHlSQ1k0dUU2dHcxYjBUN25MWm16Q09qSWFrSGliMGh1bWlNL29MZVhXZ2RSUU9ub1dKbEErblYvbGYyV2dRbWxvNGJTWld6NTVOcnE2RlY5cUJWQ2xObWN4TnhOSDBiZ3lmcjRPOVJkOFZnMlpxYWxhbzlqd05uWkYvNFciLCJtYWMiOiI4NzY5YWJmNDA5MzBhMzYzMWFlYmMzY2E5OTM4N2VkNTQwNTFhNmZjZGI5YzI3MWVjZDg3NjcxZWU5ZWY3MjRmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5vMUVHMmpLTFlCNnV4aDNqK2kyZnc9PSIsInZhbHVlIjoibTNESG5hZWxzWW03SmY3OHFETGlNaFpDNkVoaEVWNGZUbERLM2lraHpCenMrV2c2ZExIRng0T1FJQkM3WklaQTIrOWxYRGZyS0h0UkE1bENmMWNQcmZFMXNtRmdrc0lMN0dSVGZia0xvR3dNS2NKSzZ0MDZmN0xZL2Q5ZmN0Zkg1ZUhnMzNON3pPbmFESDEzMjBCR0thUXJZR2UyVDBTdU1IcVcyT0VndWhHdGUxZHFkK3ZPNkxaNFRyMERwdVowaVJYQUhtWXBxT3VMMG9Jd2lrbUw3Z0J4elc0Z2RlN1kwN0piRE9wOE1QUG55bVUrTTVHM2VWSU9XQ1Jld0xDd21xejlzcWpXck93Z25YZXRERnFQWjRaR0tjTXptQ2FxdEFnVVFHS0dTM1JLM21EU3BEZzZGZXBZN2pRVmsvbEYvV01yUDcrZWhTcjd5a0FiZVNhUzM3SndFQlpqczRQQWVIMVVpZ01xamhENjRkVUJIZCtkVVNDdVBGaTdnWjNTbFg2M2xGQ3IzN0FuWDd5azU5RjFrZ3JtOEtoQ2EyRTVuRmJVa2VWSmh0YktkcFpDeTZjbHc5OFRsQmo5ZzNRMVR3dU14T3RPUGZzaDBwdzN0Qml6TFpTVFNzVlZra2FwbzFRYnMxNHhieTVMdHRUL0NFalJRK2gzRFZxWVFFUUwiLCJtYWMiOiJjZmFmYWFmYTY5NDY0ZDQwNTJmMDZiNjZjOGIyNTlmMmY3Yzg1MGJmYjAyMDg0ZGNhODkyMDMyYmYyNDk4NTFkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlpsSFl5cjFyRmk5eHRwejJWdGRMT0E9PSIsInZhbHVlIjoiRXVqeDFzK0M1YW40ZjlwdHpibVppdFFjMFU3K0plb1VLcDRzYmU0VlBRNnJWb25VbGJKMERueE85WlpYdzRyQTUxTjFzeFRxY29yalV2cXUxNWhobHZqTHdYNTZHaHUzdElrdHBGSEtSMmVITGUzUWtGa0g2QjBYZHNDY2Z1c3hSQjYxYzJxd05lU3F3SmtQZXVnL01TN1AzTmRCV2lDVGpESUNiWjcxMEF2NmpnaVEwQTJqMC9IRHdxMDFhN3FYTW8xOXV2UHN5Ti8ycW11S1lvRXYrT3ZMN3Z2eGFDa1JXYVBOYmdYQ1hJUEV5bWhpU2FsalIvSkpsWWcrUFc1bXY5d2VNbUdONmlYS055UW95cC9uVXR6b1RSSWtLaUxCRUpXc1M4Y3cvdnF0aDk0WFlyNk9YSmc5cm92ajNZVzJ0ZXNFVlZIcTM3VzlTYjRpcEc3bEZyY2NmUVg1d1ZBeXpoUEJSRjh4aGdVS21nbnlXdFlCMVhxUEJ0bWhEQlhtZExLcHlSQ1k0dUU2dHcxYjBUN25MWm16Q09qSWFrSGliMGh1bWlNL29MZVhXZ2RSUU9ub1dKbEErblYvbGYyV2dRbWxvNGJTWld6NTVOcnE2RlY5cUJWQ2xObWN4TnhOSDBiZ3lmcjRPOVJkOFZnMlpxYWxhbzlqd05uWkYvNFciLCJtYWMiOiI4NzY5YWJmNDA5MzBhMzYzMWFlYmMzY2E5OTM4N2VkNTQwNTFhNmZjZGI5YzI3MWVjZDg3NjcxZWU5ZWY3MjRmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1308432201\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1941005128 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1941005128\", {\"maxDepth\":0})</script>\n"}}