{"__meta": {"id": "X1f74e54d981b28ce28104593bed1ebc9", "datetime": "2025-06-27 00:14:51", "utime": **********.613865, "method": "GET", "uri": "/account-dashboard", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.137428, "end": **********.613881, "duration": 0.****************, "duration_str": "476ms", "measures": [{"label": "Booting", "start": **********.137428, "relative_start": 0, "end": **********.556841, "relative_end": **********.556841, "duration": 0.*****************, "duration_str": "419ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.556854, "relative_start": 0.*****************, "end": **********.613882, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "57.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET account-dashboard", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@account_dashboard_index", "namespace": null, "prefix": "", "where": [], "as": "dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=81\" onclick=\"\">app/Http/Controllers/DashboardController.php:81-181</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032099999999999997, "accumulated_duration_str": "3.21ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.587593, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 60.436}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.598385, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 60.436, "width_percent": 14.33}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6063, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 74.766, "width_percent": 25.234}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/account-dashboard", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983290489%7C46%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVTWHdQckg0YjhDSEJUM0prd1VJNnc9PSIsInZhbHVlIjoibkQvZVA4c0JIREFVejM3VWF1TWl3Wmo0U2JvMFFpQ1E0YXRTNi91OXpqcnUrNlhYWHNXY25IbVZRUWlhdFNoUGJjZnJLYVlUTVo5RWhBTll1L0diVDRSNXkwQnM2R3BOSmFHWnZacStBa3pDYW03VDdSQVNjeVRzak9lZ3dMTkltUnhRTlBLZmlTbjVJRDQ4TjZURDVzejljd3A4bnhveC90MTI1YkUwMHVPRnpWNk1zZ2FtbG1rQmZYeFhmc0tiK3F0NnA2TXhIM1BtQXpuUTBFczFkVmtmVHd6amJWUE1jcmdLL042MGVncTV6eTl1ZlhiMzBUbmxrOWs2UGpOV3RnNXRnSjY2S1FMTDFPWkxpdkhOY3FUZ3lpc0dTdUx2cTIvRDI0L2JpMTVKZ0k5MjJyRDhQMGJtZ1FOekI5NloyWlpxeEhSVU0vMUZtaFRJUWxQR2E3cDhGWU9SRUNPOUhEZTc5enp0Tm1lRXpReXQ2ZTdYSHRaaERoQy8zZ1NJWXAzNjUvK09qalRpcjRMT0xIbGlFNktXWU5GL0ZteUc0WEJiN0tDYXBZR2ZJTVBuTEJ1KzlTN09iZUdOYzg5UktUTG9UZXk1b25iQ2VhNTZnUnpZVHc5WlFqRDhjMEpKb3lqRFFtQnFmWE45Nk14dUQrN0hVT3BMT1pUL2pHbjAiLCJtYWMiOiI4OTdlYzBjMjFjNzAwZDdhYzZhYjMwMzViMzdhMjIyMjFjNzI1N2VhYzc2MDk3NjI1ODRlNjYxZjAwODMwMTIxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNlTkFSMTlNVVhCWmhVM0FESGZac2c9PSIsInZhbHVlIjoiZ2JkaHVwV2d5R1UzQ1BvK1JTWTlTS0djQ3hDbzRWelhVODRYTmpCc1N6QzFMZ2RmUHQ3eWZLVVpLaldTTkpvakRvU2Zrb0xJRTRyQzNDaDNWdmp4WlVweXdqNml3aHRsSldDOTVlS0xHZGJLRVd1L0FSc0hoR1NiTk4yMlg1VEVEQXFvVWFSNVhGSWdtaHk5SUt2V3I1dDdKZ1ZQdUp3M3NmUHZBRG9pVVNuWW45Ny94cVVJanprUFRlaU92a1ZSTlVUS2dHNENNcFBjM0JVTFc2ejNWcnN2RVdtY2oxUGVNUVI5R2xUYXU4WlRmZWF3ejZmQVNDN0RJZXhTK2JsWTg1dXNGY0Y5WGc4VWlNZG1SR0J6dldFazRTbm83YnpnT3pRZWFOZWhXYXdvZjZFVDRadXBSVUltdldsMkM0bHdzV2tndWczdkx2cFdlSS9jR2lyL2JUc1JNcS9xQm9tZEhvaHBuZVJ4YjFvRDBiTmNzMDcxcnFnWFY4SXVURmpmdGxLR3F1OXZPbEtudFFCVGlOY2lwYU8xVFdOZTJnTldLU2NsczBHM20xK0hJemtZUkczdWMyWWZNUExHajlKSUpyWGJKUlFMVkEvemZGaFpaY01mdVhhSlIzN0J0UXhBdVFqQnZzVysrRDlvSkdoU1dyOTFOaTF5dUp5dmNYamgiLCJtYWMiOiI1YjEwMDAxNzJkZGZhNjQxY2ViNTk4M2EzYjdkODZlNGMwOWM4MzUzZDA2NjkzODM1YjVmMzFjYWI4ZDU3MGQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-567790871 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-567790871\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1693523987 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndmenlNd3BRY0t2OHVMT200NmlmaGc9PSIsInZhbHVlIjoiSWFHaGtwQk5YVmdSNGxOZEFwTE1UcXczam0rNndxVlVpdWJqeTcwaVdxZWtpalRweXR6SFdmcW80bDA2UUgxdklJWlhDZ3lQdXFXa1l5dklkd29XblBqODdLcTRGdEw3T2VoeFNyeVptMUVMR0twZUlwWkI1aENYUThkMTNyUmVzOXEvNHpqaXh6cWd4TjA4Umxsa2YyeksxZklYY0NuY1gzYVFpTS9ka25IcGlSUWN1cUxWK0l3aGhPQlg4cjNpc3RVUENKTW10Y3hLVUF1WFNNTmNzd1Q3bmRTeFZtNE0rWnpSMTU0TFEyZDBNLy8ranZRczhXVWhWSWRmcUlOQy9GOCtrSGNTYkdCQlBrS2l0RzBidTA1eHU2Mi9RTnJQbEJvaGJYM2FqblZqLzB1bFM2YkhyTVBOWWQ2TkJHZGtsRGMveTUreGxsVnZMRzRMNUdiS09zN0JRMC9qQUNTRU9BUm11UW1JZzBmUHBhaEY5bGtrMFVsOFJ1c0pIakdrN3V5Q083c0RrZEdRSmlvK2xjekY0NHM5UTVSV3NhdjZrdC9NR3JzVEIxeUNrM0orNkxvNmRobWVRcXkxdlBOYWxPZGlsZ1pDUGNrb0hDUWhROHZGVlJINC9ZTG1IUlBEblpRYWkvTHlsMjhvRWdnMDVvSjdxaTBNUU5XZkQ1cGMiLCJtYWMiOiJiZDIyYzgyZTM1ZGJmN2M1MjQ2ZDM1MDVlNWVkYzNlN2VlYjdjNzVkMGUyZDQxODQ2NTdkMzJiMjc5ZTUzNjBlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ill4d3gwTitZNXhRa3NFVGJhVW52R0E9PSIsInZhbHVlIjoiZ0dienl2NG04aWtXYzNlMzE3UzcxMlZnQXlnWmRxZGYyV0xaM1U3NmY0M01nVTNibmFUSXJXVVcwVTNoSGl3WHFYQTR1WURYN003K3RUNDJFbWNMQXNuMHdQQzE2SGZmekxqRUFzbVp4MXBYdklIcXQ2S0p2TTd1cnR4SjdYeFZEajBrZFpCbXJ1L2dveTA4VTA1UnRmQTVNbFRkZkpQaVdqakR6L2JMbWNGVy9OVGl6YUk3c1diOWJBN29lQkRQSVAxbW1teDNrRUF6RVJhbndqQWxKTnorUjBLVVR1a0R4SHY2WTdkVDc0OXBzSGIxNm1GMUY3SVJURkQrL0VLaW1vMUx1cXdNSFF2WnArUmloZXp0dkNHb0FSNjZUNWF3UjlRaUxkM1c3Nmwyc2xjRjNTNzZ6WXNKMFlHZGNobGhTc2M0UkJvS3BHL0FOck53ZlA2cnRxblZoc3Nmc2NFYWdhZlV2bnZVUVIyQ2RQMDN2RXVqUkllbkltMFBlT2F2dDV4eFRjUGhmTEEwMWY2ZVp4dXhORUZZNjF3NW1vZlcrL3ArMDM2YWpndGtOSDBDREFkelN5SEdJRnJLa001alhySm90Q2RVWUEwb3d2bUx5UGVUZkpybSt0TWFWZ3VFNFkxMjFhSGR6Yi9nVXdVQ0FKR08vS1VybllZYWpqcmEiLCJtYWMiOiI5NzNjMmYxY2ZmMDBmYzMyZTA0MmI1YTUxZWU2ZjA4NDljZGY5NmI5OWJkMWZlOTZlYTk3NzlmN2U2MmE4MjI4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndmenlNd3BRY0t2OHVMT200NmlmaGc9PSIsInZhbHVlIjoiSWFHaGtwQk5YVmdSNGxOZEFwTE1UcXczam0rNndxVlVpdWJqeTcwaVdxZWtpalRweXR6SFdmcW80bDA2UUgxdklJWlhDZ3lQdXFXa1l5dklkd29XblBqODdLcTRGdEw3T2VoeFNyeVptMUVMR0twZUlwWkI1aENYUThkMTNyUmVzOXEvNHpqaXh6cWd4TjA4Umxsa2YyeksxZklYY0NuY1gzYVFpTS9ka25IcGlSUWN1cUxWK0l3aGhPQlg4cjNpc3RVUENKTW10Y3hLVUF1WFNNTmNzd1Q3bmRTeFZtNE0rWnpSMTU0TFEyZDBNLy8ranZRczhXVWhWSWRmcUlOQy9GOCtrSGNTYkdCQlBrS2l0RzBidTA1eHU2Mi9RTnJQbEJvaGJYM2FqblZqLzB1bFM2YkhyTVBOWWQ2TkJHZGtsRGMveTUreGxsVnZMRzRMNUdiS09zN0JRMC9qQUNTRU9BUm11UW1JZzBmUHBhaEY5bGtrMFVsOFJ1c0pIakdrN3V5Q083c0RrZEdRSmlvK2xjekY0NHM5UTVSV3NhdjZrdC9NR3JzVEIxeUNrM0orNkxvNmRobWVRcXkxdlBOYWxPZGlsZ1pDUGNrb0hDUWhROHZGVlJINC9ZTG1IUlBEblpRYWkvTHlsMjhvRWdnMDVvSjdxaTBNUU5XZkQ1cGMiLCJtYWMiOiJiZDIyYzgyZTM1ZGJmN2M1MjQ2ZDM1MDVlNWVkYzNlN2VlYjdjNzVkMGUyZDQxODQ2NTdkMzJiMjc5ZTUzNjBlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ill4d3gwTitZNXhRa3NFVGJhVW52R0E9PSIsInZhbHVlIjoiZ0dienl2NG04aWtXYzNlMzE3UzcxMlZnQXlnWmRxZGYyV0xaM1U3NmY0M01nVTNibmFUSXJXVVcwVTNoSGl3WHFYQTR1WURYN003K3RUNDJFbWNMQXNuMHdQQzE2SGZmekxqRUFzbVp4MXBYdklIcXQ2S0p2TTd1cnR4SjdYeFZEajBrZFpCbXJ1L2dveTA4VTA1UnRmQTVNbFRkZkpQaVdqakR6L2JMbWNGVy9OVGl6YUk3c1diOWJBN29lQkRQSVAxbW1teDNrRUF6RVJhbndqQWxKTnorUjBLVVR1a0R4SHY2WTdkVDc0OXBzSGIxNm1GMUY3SVJURkQrL0VLaW1vMUx1cXdNSFF2WnArUmloZXp0dkNHb0FSNjZUNWF3UjlRaUxkM1c3Nmwyc2xjRjNTNzZ6WXNKMFlHZGNobGhTc2M0UkJvS3BHL0FOck53ZlA2cnRxblZoc3Nmc2NFYWdhZlV2bnZVUVIyQ2RQMDN2RXVqUkllbkltMFBlT2F2dDV4eFRjUGhmTEEwMWY2ZVp4dXhORUZZNjF3NW1vZlcrL3ArMDM2YWpndGtOSDBDREFkelN5SEdJRnJLa001alhySm90Q2RVWUEwb3d2bUx5UGVUZkpybSt0TWFWZ3VFNFkxMjFhSGR6Yi9nVXdVQ0FKR08vS1VybllZYWpqcmEiLCJtYWMiOiI5NzNjMmYxY2ZmMDBmYzMyZTA0MmI1YTUxZWU2ZjA4NDljZGY5NmI5OWJkMWZlOTZlYTk3NzlmN2U2MmE4MjI4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693523987\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1043105676 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1043105676\", {\"maxDepth\":0})</script>\n"}}