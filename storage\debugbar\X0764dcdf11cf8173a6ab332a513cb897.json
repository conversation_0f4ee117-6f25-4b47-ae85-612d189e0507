{"__meta": {"id": "X0764dcdf11cf8173a6ab332a513cb897", "datetime": "2025-06-27 02:12:44", "utime": **********.772082, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.340697, "end": **********.772097, "duration": 0.4314000606536865, "duration_str": "431ms", "measures": [{"label": "Booting", "start": **********.340697, "relative_start": 0, "end": **********.697482, "relative_end": **********.697482, "duration": 0.3567850589752197, "duration_str": "357ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.697492, "relative_start": 0.3567948341369629, "end": **********.772099, "relative_end": 1.9073486328125e-06, "duration": 0.07460713386535645, "duration_str": "74.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45272752, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02297, "accumulated_duration_str": "22.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.731813, "duration": 0.02266, "duration_str": "22.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 98.65}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.764012, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 98.65, "width_percent": 1.35}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-232003033 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-232003033\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1305258194 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1305258194\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-944710467 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-944710467\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1015100340 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990356916%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpuMUExNzNiMGRQM2dCUTB4SVBQeUE9PSIsInZhbHVlIjoiTWNuL1gzL1Q3M05PU1NwZk84aXA1ZE1zbUF5dG5zTi9ESGR5QkFxUlRQdDQ5T3kzSnY0NVBiUmpyZmNUQVJZQm50N0FqMHNOMU51SWlXZFhrMXMzTlJzU1JvdUdnTkNYanpoU3RCZUsvYVVCYVJIRXhuYkNINHNoTm5YRHQveEhjOHZkeHlVbnRSNlhpbkRSQjFqdVR6RzJoYmduNmxpc0ZsVmtFS3VKOWFPYzRkdXFBZVQ4L1MwM1lCb1hqRzNyUzFabnJjVEFyZVRJZGFRdWRqeXNkY0ZiZDZhSnozUE5qa0NySTk0UlZxSk5WTG85TytKamkwbG9vUURNWjkxQTg4TTBiU1lTM01EOHVKV0dNWXBGMnVObDNiQTE1VjdZZ3FuaTdOekV4NjR2TGRneFRLRzJGejd3M0lSa2FjbmROVGRWSU5Zejlnbkt1dHNTeTltOVlVZXljK0xMMSt4cHhEaW5JQTFNZEJ2NHJDR1VtTzFNMStFSGhoNXorZURzcStUSyttU3pRWXY5Mi9EZFBRVEh0cks5NG1YdDFLSFFrS2s3OGJXQXhtQVFxK2VudTg3N3lYU0RUMGk1SUpPbkErNDl6N0NpNzJZeWRnYWRVdVVwTEZBSE9ZME1KZE1IeHlWdk1adVN6bjNmd2thZnBWMlRVNnlaUGFiOTJaRkIiLCJtYWMiOiI3ZTE2MDY2MDE0MzI2MTBiYzUzMThhYWZkM2E4OTRkYmQ5YTBhYTlmNTBhNGYyZWVhZDUwOWEwYzcwMjViYzNkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImdDVXV4TEpEdURjdmcyTUtqWXBwWWc9PSIsInZhbHVlIjoiKytXMUlqVGM5ekNaU1lpMy9Bd0dzb0o2RTBZTzNmYVNvT1BOaXZwSy9rTWZSakM5d09HWmk2TUtpejR2bjZPUThBcXQzNHN6YVQwbWxqMVFKQnRxWjViNHRsTHZoRXVvRFFWenJ2NndzRDRCVzRGZ1liaWpzZ0hmbnBhQjQ1NnROdDZjM0VtM1JYWENsRFRvQmYyWDN4QUdIc0tEWXBET0VGSlNmQlBCUTEwK3FUQlhVQ05wMzdFZW41K3N3WGVnWGdEVmZnalFhRkpjSjN0blppeHRYT0ZkdUFKaGRUcWduZk9qL3ZEY0VlbmFKVDNLVGRPTEZ0ZWZLRGxQRUczdDlPcld2Z0dzM1RzYzExenRCRUVDbHdpc0QwZWdlTUJoT095bVpUeFQxY2xxOTE3ZkhsMTVzSUVRc1M4dytiNWJCOHB5YnlLWXhtZ20weGJ0Yy9yUDl2TlZpMlptMWpncmZQS2IyY2ZNazlEajRJUVQ0RExKZEhNbXRUSDJMWnVBQ0FZSnNjbVVYWldrczhsdWlEUVhWM0xDVExPVTBocGFQaE9rWmZ5TllMcXZsTnVvMGlXYkVCMlNVb2J3ci9TYXFsangzYUZhOHo4bHA2N2s4N2JjZW9oSS9qakwwWkx6V0FZczhublhpM1VUSDBBaDRPejIwc1V6bDlGczB0MHYiLCJtYWMiOiIxMDUwYWMzZjM4ODc3YThlMmVlMzg3YzFiYjE0NmFkYjUyYmIzMjdmNTEzMDIyMmM1ZGY4OGYyMTI0ZTJiZDBmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1015100340\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1956693393 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1956693393\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1404264685 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlPZzBGZ0loQlhaNFV5NjdiQzRMNnc9PSIsInZhbHVlIjoiOFFORHRWb052R3BRVi9jdUZWMWw3L0I1ZHVHeVNBTlRUYTZ3aXlLVVJlb1dWRk90NWdNSTAvQ2QvdkhCT0JCOFYvRFoySDI4YWVVWnFuK3NMR2M4b0xVQzZ4MlgxM0ZIajRtZm5wS3djbm5Vc0piN1V3Tm90U2VVU0xlNlQ2bXZCTGZka3BOR25BOGhna2ZhZ1ZCRU1wVkxpSG5QS292Q09TQlExSjgxY3AyREhXZVRKVzZ0UzJtL0dmcTQrSVlXTWJJa0lUYVdERWxhaHRqWmM5ZkZCWEplUmJNWW5DNTBCQ1F4S3ZwanNsL0tqQzRzNWg4THgzdkxSTGs3Q0YvS2JVaGNzeU5Ba3cxVzRlMTBhcFU4cERablU5R1Z5cStrNGxsVHlJODM3WHhYYitTcDdObk9MWkRRTlo5bGJRVk9JUk9lWlNwanMvQnk0TDU4Wkd2ci9oVGhGM0ZtSUFuVGZUeTFPbXhKNUl2UENacGdKQkNZMm5XM1d1NHJ1d1JkYUw4K2pqZDJtQ0l4Qm5FZVRPeTFRbjVxTHB6aCt3R0FWVC9YbW1GTjJsSldTc0pOclh6N2YxM3lveWlrd3BoaG9nMG9QVDlHZm1adWxmQVVDbkJBRmlleUFXT3prcDgwc0NrUFNNR3ZCamNnY01WcUxTME9jNEc5ZVc2d0FRUmYiLCJtYWMiOiIyNGU3YzJmYTAwOTAwN2M3OTBjYWMzN2YwZjJjNDY2MjVhODY5ODIxY2YzMGQwOWE1Yzc3MzYwNGFiZDk4NTMzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InV6MFcrOTFTOVFveWFGL1V5cFVHRHc9PSIsInZhbHVlIjoiVllTOGdndVp4OEFHaHpoZ25zaVJsNnd5bWZIMEsrbGdsOFR4T2hRSlNXNWRoamh6QTFEZXlxWEFTRHJ5c2pMYzI1WFgzTHBCanBQeDhJR0F5dXFnN243VzJGWllTQjE3SURPMy9uMm4rRmJqSndTeS94SUtxWlFvcjhDM1ozWEs5c3BqMkhlOHdCek5kbVoxSC9ITDZwVkttU2tjcEQrTitpZFcvTENVNWQ1cU9DdEFNcjVOL3hTSDVjdmdpT2dtU2syZll5T205eVZkd0owQnE4UEhIdHg3VFU2ZERySUV4TTVBVlAzZENMMHpxbjRtKytUWXZHTTl3bXVpd3R3ell0QUxkRVFzSUVpRy9LRmdPM3VPRUxyMmxacnBIZ3FPWVFGTVUwaEgwRW1ha0dYQXJqNGJ5WlF2cnZrbnBlaEF6K3g1Q2R2QU1Ja1ZsL3dUbTFNSzBlQTBoZ2hacTN2RFFJRGcxTzk3L1BUT3dISU1EMkxudElHRmI3UHhKUWdBS1NwdEg4L3orem8wWW82VlNBdVBuQlJBKzh3bm51R1BlU1V4R01xOE0xV2VETzd2c3F0c21xT3BoeWNQTXNjMkpabEpocHQzUWJkK0c5WkxrUXVKQ3NOWWlyeU1icENTZUtkVFZoYy96UGtWUitOSmNPNHJaeUJCUVFhL1NGMW8iLCJtYWMiOiJjYTQzMjAwZTMxMTI0OTgzMzIyYmEzMWU2ZWM3ZTVhNWFlZjQwMjMzMWMxNTZjNDIwZjBkYzI3ZTJjZjhjODhhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlPZzBGZ0loQlhaNFV5NjdiQzRMNnc9PSIsInZhbHVlIjoiOFFORHRWb052R3BRVi9jdUZWMWw3L0I1ZHVHeVNBTlRUYTZ3aXlLVVJlb1dWRk90NWdNSTAvQ2QvdkhCT0JCOFYvRFoySDI4YWVVWnFuK3NMR2M4b0xVQzZ4MlgxM0ZIajRtZm5wS3djbm5Vc0piN1V3Tm90U2VVU0xlNlQ2bXZCTGZka3BOR25BOGhna2ZhZ1ZCRU1wVkxpSG5QS292Q09TQlExSjgxY3AyREhXZVRKVzZ0UzJtL0dmcTQrSVlXTWJJa0lUYVdERWxhaHRqWmM5ZkZCWEplUmJNWW5DNTBCQ1F4S3ZwanNsL0tqQzRzNWg4THgzdkxSTGs3Q0YvS2JVaGNzeU5Ba3cxVzRlMTBhcFU4cERablU5R1Z5cStrNGxsVHlJODM3WHhYYitTcDdObk9MWkRRTlo5bGJRVk9JUk9lWlNwanMvQnk0TDU4Wkd2ci9oVGhGM0ZtSUFuVGZUeTFPbXhKNUl2UENacGdKQkNZMm5XM1d1NHJ1d1JkYUw4K2pqZDJtQ0l4Qm5FZVRPeTFRbjVxTHB6aCt3R0FWVC9YbW1GTjJsSldTc0pOclh6N2YxM3lveWlrd3BoaG9nMG9QVDlHZm1adWxmQVVDbkJBRmlleUFXT3prcDgwc0NrUFNNR3ZCamNnY01WcUxTME9jNEc5ZVc2d0FRUmYiLCJtYWMiOiIyNGU3YzJmYTAwOTAwN2M3OTBjYWMzN2YwZjJjNDY2MjVhODY5ODIxY2YzMGQwOWE1Yzc3MzYwNGFiZDk4NTMzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InV6MFcrOTFTOVFveWFGL1V5cFVHRHc9PSIsInZhbHVlIjoiVllTOGdndVp4OEFHaHpoZ25zaVJsNnd5bWZIMEsrbGdsOFR4T2hRSlNXNWRoamh6QTFEZXlxWEFTRHJ5c2pMYzI1WFgzTHBCanBQeDhJR0F5dXFnN243VzJGWllTQjE3SURPMy9uMm4rRmJqSndTeS94SUtxWlFvcjhDM1ozWEs5c3BqMkhlOHdCek5kbVoxSC9ITDZwVkttU2tjcEQrTitpZFcvTENVNWQ1cU9DdEFNcjVOL3hTSDVjdmdpT2dtU2syZll5T205eVZkd0owQnE4UEhIdHg3VFU2ZERySUV4TTVBVlAzZENMMHpxbjRtKytUWXZHTTl3bXVpd3R3ell0QUxkRVFzSUVpRy9LRmdPM3VPRUxyMmxacnBIZ3FPWVFGTVUwaEgwRW1ha0dYQXJqNGJ5WlF2cnZrbnBlaEF6K3g1Q2R2QU1Ja1ZsL3dUbTFNSzBlQTBoZ2hacTN2RFFJRGcxTzk3L1BUT3dISU1EMkxudElHRmI3UHhKUWdBS1NwdEg4L3orem8wWW82VlNBdVBuQlJBKzh3bm51R1BlU1V4R01xOE0xV2VETzd2c3F0c21xT3BoeWNQTXNjMkpabEpocHQzUWJkK0c5WkxrUXVKQ3NOWWlyeU1icENTZUtkVFZoYy96UGtWUitOSmNPNHJaeUJCUVFhL1NGMW8iLCJtYWMiOiJjYTQzMjAwZTMxMTI0OTgzMzIyYmEzMWU2ZWM3ZTVhNWFlZjQwMjMzMWMxNTZjNDIwZjBkYzI3ZTJjZjhjODhhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1404264685\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1653759597 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653759597\", {\"maxDepth\":0})</script>\n"}}