{"__meta": {"id": "X086bbf0935b32cb6ec8fbc8a381afd43", "datetime": "2025-06-27 02:29:31", "utime": **********.435957, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991370.991571, "end": **********.43597, "duration": 0.4443991184234619, "duration_str": "444ms", "measures": [{"label": "Booting", "start": 1750991370.991571, "relative_start": 0, "end": **********.381759, "relative_end": **********.381759, "duration": 0.39018797874450684, "duration_str": "390ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.38177, "relative_start": 0.3901989459991455, "end": **********.435971, "relative_end": 9.5367431640625e-07, "duration": 0.05420112609863281, "duration_str": "54.2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45273096, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00226, "accumulated_duration_str": "2.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4157429, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 87.168}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.4276412, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 87.168, "width_percent": 12.832}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1270080186 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1270080186\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1201894675 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1201894675\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-934097905 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-934097905\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1155071089 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFza1htK1hsRmpQajcxaU9DY0JBTUE9PSIsInZhbHVlIjoibGY0b0xraUhOMjMzblV1dmRXcGgwUExndFUyNkFabTdPcUVJenIyM2Nwb2VnMUErMGd4d24xM211VUljQkZKUUtPcFhKeUErNy9ZdlpJUVQzQWJ2MDZHbkRhc0ZlZldlM01acFI4b3ZzK2FZdXE4d2hhYVJkc3V3ZWh2Vlp4SHhheHdCZG1pRUNHUUxQMHhkSGFyQjNRb21KZnRWUkFPY3JXZlI4Y2k4RGQyMWhBQ2tPYkVydWNLT2xMV1kwRnhSYnVObjBNVmhCYTRPOFFJV0t5R3FJMVFpTlZJMVF3MVR1b1FRMFBvUkdTVTVEb085L213SVFYb2xoYloxL1ZTdmJZQXhpSXJ2TTA2dGk0alViNXVQZ21MVjFzeStQa2J1WklnN1JuSnRFVk10RnBOMW9DTGZBYXMxQXlkQ08wb0NHQmNyKzZGeTZpQXc1dWJHbzBRVHQrcEdmbTBlMGRUVEtxL0xVek1MVmpTVjc1czVmZlNFczlSKzJJaGJoUzFhN2p3eHZhdFdvaFo0YXcvRTZOUHdVOGFJQnJxQ2VjTkxhb2ZVaGtxT21HLzFYZzFnK0krUUZISFlSZDRsYXpSRkhHYyttaWp4ZHIvQ214SVR2L1hQY05oRFJHNVQzdE5lc1hTbG94aFJDeEZPUkQ4Q2ZDc0JaOTdjUUdlazFrNDIiLCJtYWMiOiJmNzNhZTNlMjc1MzhmYTBlNmU1Mjc2YzJlNmFjZjE3ZDEwYmFjYThjMTM2NTQzMDQ1ZWE3ZGIzMjJmYjVjOTY1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZVbEhmTEdXV21OQ1piWjZqZ0JLVEE9PSIsInZhbHVlIjoiU3BMZmE5d3V1V09OY1IrVG9kS0tYZk1yMm5DWXc4eVRPbWxnb2tRTC9FTk1UUTRlcGZXMGhWemlhMGlOMVNLY3NmbmZQcWdRNThzSzFHcHBXeExzQlVCSk9WMUhCd28xYVlnUUZwcWJRTm9yWklGQURFTWhaeW93L29SR0pvZ3pPbkptYTkwdWw0Y0JOdzVJaFV5NzYxaUNPWlVidHlKaUExUStteDNBa3hVYksrOEtLcHFiMmxKbXg3dzJHUE14eGpieUx3YkRaNGVKbyt0Y0JMaXRETU50RTBxdWhTbkpsOG5nWlpZNWdHNEh2UVhBaGwrTjF0bDNua0ZqTDViSEFuQi9NTzlaVk9zRjIyZDYzT1BuUmhNUVlVb1hwMDc1MG5majBXTjFNWFJEUUFJekNzRllVc056TE5oWURMNldLazRpQy9MeksrcjFMQnhKSlRlWWpaNTdJaXVDMzJmZ09FYmVaL1VxQ05PS09PaEFpdHFJUVNrUjBkUndmWmFXQXRWbmV2NnpBcUVlZ2o2WkphMmNRUVVjbTJBUHdReDEwWmw3NXZwbm9QZmU4ZWdzQ0xISnZ4RkZZOWhFdUpSS0dwS1pzOVhDR2NtdWJlellWOEdqOEZBSG1KdjI1dFZWWW5yZlBkRWxjdDhiMFoxUmx3dTk5d3RZNUdJdzRjNnAiLCJtYWMiOiI2ZGVhMDU5YmI2YjlhNzM3NDQ2MGM4ZmJlMjllNzRjMGJiODQ4YjM5YjYwNTg1N2RhYzcwM2M1MzMzNzdiZWVkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155071089\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-907764734 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-907764734\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-376113781 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:29:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlBnRWdJWWk0MjJIQlE0RzZaOUJFMHc9PSIsInZhbHVlIjoiRDgvdGFqUjBKTjhJRWEvUDk2N1hxdXZDZTNkT1o4anRoYlB6UU0xSXlxU2I4TTNxMXdWb3pCRHBqdjQ5UlpoMTJlZjY5Rk1sc2twZ0hKNHVzN0Jnc3Nnems3TDdEK3JTa1pOY3N1NFllK24xb3NPNTFqTURrbWpVaFYrUHl0eURLbk1tZnA3QzBVWUh1NmpHUWliZ1V1VXVFckdQc2w2Y25RdW1xSWtpcUdPY1U3TE1GTG9iOXpzQkYvdHdaNVdrRkg1T0tGUTQ2MWN1aFc2bnh2R3RGSmg5blEreUl3bW1VWUFnVGI1T3pNTmNtQjB2cDN1bCtKM2g0akJ3YjJid2Q2SjNLUktqbkFVK25NdDJXZXQ5TjZiS2xYWTlIL05kTzBiaXhLRlYvVW9JR3V3dGJvRVFoc244RjJkeCtRQ2VoaVNvdkJjcG9iZ3hsMERyVldTZGN5a1QwYzRGVFlQcDQ5enZOa1hlN1ZCdnNLWmQzYU9uYStrdUY2QVFjSDk3R1d3cFAzcnVZd0ZDa3dXL1B2VGlSbVdsNFlvV1owa3NoYnluaXUyNkJQbGNpeGRJd2x6NTk3UldZdy80bGhxalBBenN2SDhoRTBwVy85aWplTGNQNVA2K2ZrNitkUmVtOHdnTU5IcTdJWnRBN09KVjJkS0hsVWRTY1JUTFVibkYiLCJtYWMiOiI3YmNlZmJlOGRmNTVhNjg4YzJhY2ViYWNkNDAxYmFlMGNhZmU3MjhiMjIzM2VmZWIwZWJlODllMTNkNmE1YTgxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:29:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkhQTTk1TjZNbDMwYzU1VmdPaG8vc3c9PSIsInZhbHVlIjoiSktMaU8rZzd1K3NHK0Q2NThyd2VFb2x2OXMyRTE0VjY3U3E4czNGMlRtY0p0Z0pnRVJ4Z21ZL0J6c1M1NTU4MlAwdEs5V3IvT3dUMGFQYUlpejlaS0VDckhBb2pvMi80Q1lNVDIydFpDYktnUzlOWm1qM1dvK1JzU2ROdVdpcGVTckE5SzN6a3IzK20wOTV6NHRXMHE2K2Q3Nnh6UndZRTlKbmNJdFBPNkx0akdDRHhBcXVmK3ArWEd6cEppL0U2U2RzMmgyZlA1enBEN080dS8ybjJBK0ZjTWJ3czh1aGJMa2pycjVpOHB4S3ZQT1IvelY1RTAvSDBTUTdUcjJ6cUdlSVBsYzdrTWNrbWRVVFdVMzdhR3phdGVSanprTU1ZQnNVZ1ZmdmxmQW5YRjFjQ1ZYeFpOamVrREpEeDFGRVkybkdRRFhXSXg1VzZaOHN6NXNELzROS3RrTVJVYnNYZzBtTzFnYlI4M2ZBRjlWMTBZNTM2SmZnOURoQ2VuMktNRFJoanhQcW1CSkovOVFmOUVHN2FyVUtHR3lGSVBZSi9EZ3hWTXh4Tk1YVDhOVndlUTVqVU5zTHdtMEdOUThLYWRnNlU1TVBndEF6Zk5WQjduckxMZEQxa1Q1bnJ2TVZvc3o5V1N4MDR1dXJMeGoxdGxKZjZxZ0RGYWg0U3ZrVnIiLCJtYWMiOiI1ZjU0ZDgxNmJlYzZmYjJjNTdlMzg2ZTdjMDgwOTcyNWRlMzM1Mzk1ZmVjNmU4Y2EwODBlNWE4YzE4ZWE3ODY2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:29:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlBnRWdJWWk0MjJIQlE0RzZaOUJFMHc9PSIsInZhbHVlIjoiRDgvdGFqUjBKTjhJRWEvUDk2N1hxdXZDZTNkT1o4anRoYlB6UU0xSXlxU2I4TTNxMXdWb3pCRHBqdjQ5UlpoMTJlZjY5Rk1sc2twZ0hKNHVzN0Jnc3Nnems3TDdEK3JTa1pOY3N1NFllK24xb3NPNTFqTURrbWpVaFYrUHl0eURLbk1tZnA3QzBVWUh1NmpHUWliZ1V1VXVFckdQc2w2Y25RdW1xSWtpcUdPY1U3TE1GTG9iOXpzQkYvdHdaNVdrRkg1T0tGUTQ2MWN1aFc2bnh2R3RGSmg5blEreUl3bW1VWUFnVGI1T3pNTmNtQjB2cDN1bCtKM2g0akJ3YjJid2Q2SjNLUktqbkFVK25NdDJXZXQ5TjZiS2xYWTlIL05kTzBiaXhLRlYvVW9JR3V3dGJvRVFoc244RjJkeCtRQ2VoaVNvdkJjcG9iZ3hsMERyVldTZGN5a1QwYzRGVFlQcDQ5enZOa1hlN1ZCdnNLWmQzYU9uYStrdUY2QVFjSDk3R1d3cFAzcnVZd0ZDa3dXL1B2VGlSbVdsNFlvV1owa3NoYnluaXUyNkJQbGNpeGRJd2x6NTk3UldZdy80bGhxalBBenN2SDhoRTBwVy85aWplTGNQNVA2K2ZrNitkUmVtOHdnTU5IcTdJWnRBN09KVjJkS0hsVWRTY1JUTFVibkYiLCJtYWMiOiI3YmNlZmJlOGRmNTVhNjg4YzJhY2ViYWNkNDAxYmFlMGNhZmU3MjhiMjIzM2VmZWIwZWJlODllMTNkNmE1YTgxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:29:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkhQTTk1TjZNbDMwYzU1VmdPaG8vc3c9PSIsInZhbHVlIjoiSktMaU8rZzd1K3NHK0Q2NThyd2VFb2x2OXMyRTE0VjY3U3E4czNGMlRtY0p0Z0pnRVJ4Z21ZL0J6c1M1NTU4MlAwdEs5V3IvT3dUMGFQYUlpejlaS0VDckhBb2pvMi80Q1lNVDIydFpDYktnUzlOWm1qM1dvK1JzU2ROdVdpcGVTckE5SzN6a3IzK20wOTV6NHRXMHE2K2Q3Nnh6UndZRTlKbmNJdFBPNkx0akdDRHhBcXVmK3ArWEd6cEppL0U2U2RzMmgyZlA1enBEN080dS8ybjJBK0ZjTWJ3czh1aGJMa2pycjVpOHB4S3ZQT1IvelY1RTAvSDBTUTdUcjJ6cUdlSVBsYzdrTWNrbWRVVFdVMzdhR3phdGVSanprTU1ZQnNVZ1ZmdmxmQW5YRjFjQ1ZYeFpOamVrREpEeDFGRVkybkdRRFhXSXg1VzZaOHN6NXNELzROS3RrTVJVYnNYZzBtTzFnYlI4M2ZBRjlWMTBZNTM2SmZnOURoQ2VuMktNRFJoanhQcW1CSkovOVFmOUVHN2FyVUtHR3lGSVBZSi9EZ3hWTXh4Tk1YVDhOVndlUTVqVU5zTHdtMEdOUThLYWRnNlU1TVBndEF6Zk5WQjduckxMZEQxa1Q1bnJ2TVZvc3o5V1N4MDR1dXJMeGoxdGxKZjZxZ0RGYWg0U3ZrVnIiLCJtYWMiOiI1ZjU0ZDgxNmJlYzZmYjJjNTdlMzg2ZTdjMDgwOTcyNWRlMzM1Mzk1ZmVjNmU4Y2EwODBlNWE4YzE4ZWE3ODY2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:29:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-376113781\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-615560057 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-615560057\", {\"maxDepth\":0})</script>\n"}}