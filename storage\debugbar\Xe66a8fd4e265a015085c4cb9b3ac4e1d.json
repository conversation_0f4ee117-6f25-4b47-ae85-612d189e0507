{"__meta": {"id": "Xe66a8fd4e265a015085c4cb9b3ac4e1d", "datetime": "2025-06-27 02:26:01", "utime": **********.630083, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.210087, "end": **********.630098, "duration": 0.420011043548584, "duration_str": "420ms", "measures": [{"label": "Booting", "start": **********.210087, "relative_start": 0, "end": **********.555207, "relative_end": **********.555207, "duration": 0.3451199531555176, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.555217, "relative_start": 0.34512996673583984, "end": **********.6301, "relative_end": 1.9073486328125e-06, "duration": 0.07488298416137695, "duration_str": "74.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01819, "accumulated_duration_str": "18.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.586409, "duration": 0.01712, "duration_str": "17.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.118}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.614685, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.118, "width_percent": 3.134}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.620279, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.251, "width_percent": 2.749}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/21\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-530854984 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-530854984\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1439563906 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1439563906\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-476412131 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-476412131\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-38447544 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991158215%7C29%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im43MEpSM2E0K1ZRK2JjZGlFdG50cUE9PSIsInZhbHVlIjoiVGNxcEFtZENHLzUzdmY2YUJ2TXRRbkdhZXIrTzRTa2ZGRVVHR0F5ZW0wS2VOVDZ1eWFycWtHOUJoVDdVd2ZYME5kR2craXhidEp0YmdjT3FmTjczbG1ickFPRGFqeVZJNXNLeFhGTlhTa1JYN0NtRUM3cDNjelg5dGptdms3RmRRKzR2NGMydzM0YlFyTEFFUTBUdWZtYlJ4RlRuSVgxcllncEIxVXhEWklqK3RTNjFoMDhoQjNYNnRCT0xpeWFXeHlXZTlOSC9IaE5LOC8xenFLQlNxTTFxbWszdHVBaUQxVnlGSU1sUFJ4cWhsbTVlWG9IVGpSRmNDMXZSdUNlSlFEb3BySEs4d3l0U2dnT25NTXJEQmM3LzNyTzdPbFJ2VlZJMlJMZ1N3YlF1TGp0cVN0RGFDZTlKU2R6Rk1TbSszOHpXY0gyZFJVZWFlb0JiMXYwdHVIN0wvVG4xNTVCdXM1em9SRFN1Rzh5UWM5SVF4MG1sTzVwSkFhQUJMY3FCWHJXbWJob20vT1RrQU04Nzl3Y1MwZitVQ25RMktnWG1CbXhsVDBkbnBBK3NCYUYzNXVKYUlSSDNYS1FxdFFJcEJBN3lZK0tDR3k0d1A0Q2pWMTJwS1hxVzBzU0ZWMGVNeDNHN1k1NGJmOEllbjlQcUdWZUtpYUhLV2IrOVJHcXIiLCJtYWMiOiIxNGFhMGQ3NTk2MDA4OTIyNDc1N2JmMzQzYWM3MThiNjNjNjFlMWIwYWNiMmZhOWNiMDM1YmM1NDNmMjE1Y2JiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imdhdzl6MDRhMFpnN2pkemZkVmhGdkE9PSIsInZhbHVlIjoiUWtzUU9ZMWU4WUlrV2xoUiszRSs2UXg3U1VPbFNNQzBXa2EwdzM2djd0NXFxOEhXQ0dJLzJSd0Z6T3VGWDZVVTZBVCtxamhHSVpkdFI1Z3NVNU1NN3d0NThYQVZWSjJic21RR1diOEdzZnAxbWRkcDB3YlNxL3NNVkdWNDlJZUx2K2JQRDF2ZWZ4Tm9HRXhVUzIybWVmUFRKNVhVSEd3WGxPL0FCbTRXQzk1d05VZStTM0FaN0JDVVVlekJLOWtGeERXa0cxSy9obElBRVYvYUIwN0NBSldrbW13eW9GVzZSYTRkcjFhL0Mxbm9FZXdaOXJYSTUxZC8vWUxoQjgyVFY0aGtOS25sTFhldHVEYm0wclJlWEV0WElZamdkNE9Oa1lpK1M1eit1L0dROCs0empjclRhRy93M0dLK1dkNTNqWnJ5eEUwcUw1ZTlOcWZBWUd3ZWtYYjB6Kyt4dDFxclJCWW0zcXM4aU9WNWVnTFRsOHVRQTZVaUVXcGttbkNEcUMzUUZwV0hybXhqNDB3RndLRXpua0VUWjlZQ1ZCWkw0ZVVMd0hGczBrTUorcVJnR1NSZm9xYXJoU2F4OTV5aGlWMW81enNKbkRLTy90RmFITWQ4RG5TbE4zTm9xakxjaXJsTUNVam9VZ2NETk1VUmtEZ1pxcTVVbDd1NHIxWXIiLCJtYWMiOiI1MzcwZTU0ZjAyOTk5OWVmNmYxYjQ0ZDllYjQ5OGFjNzExNTU1YWI0YTdjNzk4MzdlNGJmMWM4M2JhZjk0MzUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-38447544\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-722399779 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-722399779\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1471744262 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:26:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklWQytwZ2tJMUdaTC90ZUpSUmFsVHc9PSIsInZhbHVlIjoiMDUrcWo4QTFqbmF0RGpERkNEVk5XcW9SRi9KVSt0OUxFVjRrWE1YSGtEcDhMRVFDYkwvK2Y1UXY1OG1pQzFtWlVkZ2htdFpKK2NwcUFJL09zWDl2amM3WEM3NTF6WFJSWVhDbkxDdzhYcmY4YWlnUGhyQ0dOOUROTmRra1JkbXZ2MVFZSlVPb05STFExODNlbmdJdFkwUzNMSVZidytNUUNlMVJrQVZLc1hPdWZnWC9KU1IzUTViNUZ6NnlGYll3djZaVGQ3T3doUDNXYVdIMmNPdDFjaGxYd05YelIyU01RSjhRU1FUMVd5WmM2d2dOa3NIaEt5K1lHTmlyalRlajU1RVFOVk11cytuUkllMjdUVERldHdKbnNGcWpyKzEzbDRwb3ZXdEZ0OXQ0aWUrdlFKVHd3TENRYWlrZ1RTeXFOS3NhY3J0c0FyK2VjQTcvM0w0Z2tqZWF2bUMrUi9mMVNoTlMyOEVSYkM3K1Q4QUxPKzdUSUtWS1lhSHdMVmNXSXFyTXQ4V2I2OHFsUFRNc2hzRXpwblpnOVg0Z01ZdVd1Zld1KzR6Ukx2UlQrWlRRaFRON1B5bFpJSjlYTURRUVZGL2xlKzhBM0ZLa1ZOaElwQ0dQMWw2bWtmc1pJbEdlK1hva1BsQWdiaWZCdjkwQVNIQ1krTWNHKzVjV0Z2SCsiLCJtYWMiOiIzYTg3ODk5Zjk3YzM1ZWY3MzcyMTFlMDVmMzQ0MWMzODk1N2NkMTdkMDkwNDY0MDM0ZDM2Mzg2M2I0ODNjZjhkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkVNNzY2SGVhN1E4bjQ4QXpXMkFwVkE9PSIsInZhbHVlIjoicGtiRFUraGdxcEVDaDRxOXphTkhrekhJL0Uzb2JWY1oxMDBKTEFvenZ5cTdxczFHMW9qbFRRL3VsdXNVMDVJQTRNZVhwamphVk5nVXVFOThJbEFUdEFORTQ0K056TWFvVXpTNm12N3BmOEUyTWJSMTB0K2JvZW4rR1VjWCs2cnpqeVAwMHdLKy9LbWYwT3ZOMW1hcTJxY0UyN1Z2TEwyT0ZQOW1YOGYyeThORWZkcmtteXhoQS9wLzU1RlBVbmdJUkdXTFVQVmNkbVUxMjFoTTVNeGZxSzR6RUY4NWJvMWJFQXJldTYxdmVTd0hDOW1lZnR3ZTNIZUwvcWl6Z2wzei9QRzYvcyt3RlR4VzZxMkFxUURuMXU3QStsTHJPOXlQR1RiRElXbFo2OFdaZjgwWEFEUWEvZWNTL0dMU205Tlhoc2liL0oyVlk3THlycDVLM3RySlJiYTlYdlBVWGRwTzFQZWp1NklnMHhzUUlMdTFaZllCRWlOZVQra3ZyZUE2Tm8rL3JqdEp1TjQ0RVBJbnY3bm1rcmM1TzY0WU52aWlmNjJhemVNRnNOUmkvYkcyT2lJVzcwUHJWaGo3NjZ1N1p4WE5XR0R4c01hdUJHNlNvR01hL2ZzS0tkd3FmWnFYVlMzaW9oMXNuRU1mUDRlMEs0d1JTa0c2c21OR1hTbWsiLCJtYWMiOiJlYzdhNjRlMWNjNTQyMmFjMzkyNzBiMGZlNTljZjMyOWVjOTYwMGQxZTk3NWNjZjY0OWQ4ZmFiMjYxZTMxM2Y5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklWQytwZ2tJMUdaTC90ZUpSUmFsVHc9PSIsInZhbHVlIjoiMDUrcWo4QTFqbmF0RGpERkNEVk5XcW9SRi9KVSt0OUxFVjRrWE1YSGtEcDhMRVFDYkwvK2Y1UXY1OG1pQzFtWlVkZ2htdFpKK2NwcUFJL09zWDl2amM3WEM3NTF6WFJSWVhDbkxDdzhYcmY4YWlnUGhyQ0dOOUROTmRra1JkbXZ2MVFZSlVPb05STFExODNlbmdJdFkwUzNMSVZidytNUUNlMVJrQVZLc1hPdWZnWC9KU1IzUTViNUZ6NnlGYll3djZaVGQ3T3doUDNXYVdIMmNPdDFjaGxYd05YelIyU01RSjhRU1FUMVd5WmM2d2dOa3NIaEt5K1lHTmlyalRlajU1RVFOVk11cytuUkllMjdUVERldHdKbnNGcWpyKzEzbDRwb3ZXdEZ0OXQ0aWUrdlFKVHd3TENRYWlrZ1RTeXFOS3NhY3J0c0FyK2VjQTcvM0w0Z2tqZWF2bUMrUi9mMVNoTlMyOEVSYkM3K1Q4QUxPKzdUSUtWS1lhSHdMVmNXSXFyTXQ4V2I2OHFsUFRNc2hzRXpwblpnOVg0Z01ZdVd1Zld1KzR6Ukx2UlQrWlRRaFRON1B5bFpJSjlYTURRUVZGL2xlKzhBM0ZLa1ZOaElwQ0dQMWw2bWtmc1pJbEdlK1hva1BsQWdiaWZCdjkwQVNIQ1krTWNHKzVjV0Z2SCsiLCJtYWMiOiIzYTg3ODk5Zjk3YzM1ZWY3MzcyMTFlMDVmMzQ0MWMzODk1N2NkMTdkMDkwNDY0MDM0ZDM2Mzg2M2I0ODNjZjhkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkVNNzY2SGVhN1E4bjQ4QXpXMkFwVkE9PSIsInZhbHVlIjoicGtiRFUraGdxcEVDaDRxOXphTkhrekhJL0Uzb2JWY1oxMDBKTEFvenZ5cTdxczFHMW9qbFRRL3VsdXNVMDVJQTRNZVhwamphVk5nVXVFOThJbEFUdEFORTQ0K056TWFvVXpTNm12N3BmOEUyTWJSMTB0K2JvZW4rR1VjWCs2cnpqeVAwMHdLKy9LbWYwT3ZOMW1hcTJxY0UyN1Z2TEwyT0ZQOW1YOGYyeThORWZkcmtteXhoQS9wLzU1RlBVbmdJUkdXTFVQVmNkbVUxMjFoTTVNeGZxSzR6RUY4NWJvMWJFQXJldTYxdmVTd0hDOW1lZnR3ZTNIZUwvcWl6Z2wzei9QRzYvcyt3RlR4VzZxMkFxUURuMXU3QStsTHJPOXlQR1RiRElXbFo2OFdaZjgwWEFEUWEvZWNTL0dMU205Tlhoc2liL0oyVlk3THlycDVLM3RySlJiYTlYdlBVWGRwTzFQZWp1NklnMHhzUUlMdTFaZllCRWlOZVQra3ZyZUE2Tm8rL3JqdEp1TjQ0RVBJbnY3bm1rcmM1TzY0WU52aWlmNjJhemVNRnNOUmkvYkcyT2lJVzcwUHJWaGo3NjZ1N1p4WE5XR0R4c01hdUJHNlNvR01hL2ZzS0tkd3FmWnFYVlMzaW9oMXNuRU1mUDRlMEs0d1JTa0c2c21OR1hTbWsiLCJtYWMiOiJlYzdhNjRlMWNjNTQyMmFjMzkyNzBiMGZlNTljZjMyOWVjOTYwMGQxZTk3NWNjZjY0OWQ4ZmFiMjYxZTMxM2Y5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1471744262\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1478779005 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478779005\", {\"maxDepth\":0})</script>\n"}}