{"__meta": {"id": "X5a6d4d98a02705c13eb125fe55096506", "datetime": "2025-06-27 02:15:13", "utime": **********.244998, "method": "GET", "uri": "/payment-voucher/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990512.842706, "end": **********.245012, "duration": 0.40230607986450195, "duration_str": "402ms", "measures": [{"label": "Booting", "start": 1750990512.842706, "relative_start": 0, "end": **********.167613, "relative_end": **********.167613, "duration": 0.3249070644378662, "duration_str": "325ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.167621, "relative_start": 0.32491493225097656, "end": **********.245013, "relative_end": 9.5367431640625e-07, "duration": 0.0773921012878418, "duration_str": "77.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46914280, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x voucher.payment.create", "param_count": null, "params": [], "start": **********.218142, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/voucher/payment/create.blade.phpvoucher.payment.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvoucher%2Fpayment%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "voucher.payment.create"}]}, "route": {"uri": "GET payment-voucher/create", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@create", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.create", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=58\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:58-63</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00231, "accumulated_duration_str": "2.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.198793, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.398}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2085948, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.398, "width_percent": 18.182}, {"sql": "select * from `users` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 61}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.210756, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:61", "source": "app/Http/Controllers/PaymentVoucherController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=61", "ajax": false, "filename": "PaymentVoucherController.php", "line": "61"}, "connection": "kdmkjkqknb", "start_percent": 86.58, "width_percent": 13.42}]}, "models": {"data": {"App\\Models\\User": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]"}, "request": {"path_info": "/payment-voucher/create", "status_code": "<pre class=sf-dump id=sf-dump-285709743 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-285709743\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-115011538 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-115011538\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-818483805 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-818483805\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-716196372 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990507470%7C9%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhxQjRkT2EyYnUzSzdWQk1YRHFPT0E9PSIsInZhbHVlIjoiejVjVWNGK1BlT1BkZHQyejVubStydVJXb0cwaFhjcGhMZUV5Q0JxT1FLUEVGNTJpR1BjUC9KMFJWcWMyZExoN0owbzFPS0lLaDNiRUg2SXJiM0VUdG5hQ2lmanJrMVZWQVRZNDNBQks4WWtyTGJDcDBQck5EbDJuUkdKWExINXVVblpoZEY2Y3BicG4xaHV2ZHQ1Q1FTRmRGWlBlRENYVnRqUnV5bzhiRGQvT1c4a0o3WEtWQnFic240aU0vcjdqWjhYOHhZMHd0K0pCOG9YSW5kdS90d2FmRTBNU0FacVVHR000TEJmOVA3V0hpWUFqRTl4bHlCTDdZRlZIY3ExTWN6L1dXY2R0VmJrbHlGSW5Kc0p4WURpNU8rTFVaemh4NHBnNXRnUFNRenhCb0ZWUnhLcGFRR1gwMXpxbytUN0FLamQ0RUR5MGFlSnhjVVJ1dEJCc0RkTHBuemZhNGVzUGR4WVI0cllJdnB4cEoreDdsY2p3cEJLS00wUDhPSnFnMFBkRWFZbWhRWXB2bmFkT21tUGh3NUJ4OTlnc2pFOGZzQS95OTBmUnphaGNzeEJvWHUwM05VdFJpelZFL2VPYTdOMnVjMzRxY1NMMUJDZjBTbWxWTmcyRVBvaVVsVEUyR1Y3NGxIVlMyRzZ2MzFwSmdXTXlDbjVXRVpYS09WejEiLCJtYWMiOiIwZDJlNDc1ZjY5ZjQ2YzhhY2Y4OTQ1NDdjZGQwMGY3YjBkZDEzM2QyMjY5ZjcwODRjYWJiMjM5NmEzM2Y4OTVjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii8zbVo3b3NxTW5GeWFkenJ1amNvekE9PSIsInZhbHVlIjoiLzdZVHlsWGJTU2h4UTJuOUwwZUJTU29SMUVGR3NacjZlY25kTWM0Z0xHeGlDQ3NZdVpjSlJmVXNOWGJzVjgxb0R6RXhPdGk5TXh3VVZNUVowTlB4L1dxMURBTjk2bmdjZTBjZjBWa2dLcm80QzRGbFRkVXlWR2hqdEQ2L2kzWC9EWVRXZnJQa1o4ekFpMHpKOU9rbldSTG9RVGJFeDQvWmZiWWRJNExYcXN2ZUJuSHBwamxVUGRzNWJ4THJycHVuUGk2cE1SYnJ1dGo5M05SMkx5OW1PbFhZZGo4SDZ4WkphR2ZFMHp3RUlSUlJTQUswTlBXQ0RycGo3VEI5enN2ZmFtcFRRb2IrZkpLYU9WcVhrenUxSDFidVZMMGh6NW9vdnFLQXJGV21tV1VRdVVQNW0yZVBIc2hJRUFxVE4wUFowTTVROWt1emFheTBnZzJIcWlPUEZFVExOR0xRUGJjMnJ3MmJENUNHQk11NHdqTFlENXB4czJhdWsxWUR4dDcveE9SSktTTmxwVXlPN3VyMjV6KzR2L2FYS3FJbE11b3p4SmxMRjRMRzEzMGpNV0kzWW5pUHF0NmtWb0J5Y0trSjEwV0FrdlZLMXdsWWp2b1Jzb2djdFNmTGlId250dWltZjBNaXBvbjNFQkJ6ck5DQkowNjBuRGZHa0grTTB4dmMiLCJtYWMiOiJiODQwMjI4ZTg4ZmRkNmVmMGI2ODY0YzcxMTQyMTQyMjU1ZTFlZDliNmI3ZmNkYzgwMmYxNGI5YjBlNTk2OTk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-716196372\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-203983302 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IitiRnJrMFIxcWRPUmwvdDNZNmlmbGc9PSIsInZhbHVlIjoiWEdJeCtKM3dXbE9yaHlJUmcxUDZFb3kzd1Awd1VGRURpNFdkOExnUUFIWG9BaWRvRjRHeGRsa253S2UvamRVUlo4QnVyNjhuNGF6QlRGc0F2aGtnNGdoMGlzbVlHaitPR0NoOXBDamVPd0UvSWFhQ3YyL1RmWGM3b3lkYXBzNUtSN3VYbmsyZDlDUDdkb2o0TG1odWsva2xTclFVdGV4Ujd5Q1ZDWi9nMHpvUFJXUksrWmJCOFJHZVBqenBDNGNaWThoM0FGRGtMNmhPSnpzU2pKUHF5SGg5VUdWS0FkMHFwdXNFZG1MZmFFL3JvVTZsZzBraGlrK2hsMnAvUXpTUHl2TE1JMGc0bWcxUWVOWkd6WVV4YXFNL203SG5WcFBVWDBkNWFIOGJ4SWpnTitheVlrWXZHMk1Qd2NsVkd5UklzQ2M0SkhWWHZJTm9tWFNIa0JIM3ROZDdEVlkvMllFRzRRQmJrNHRBemdzaW9uaDRHb3k0VTRjcDNTZW1IdmRFdTJaNkFUSkFtVXlPSVovMVNOVEZMclFWaTJrZ0ZZbHZuN0FQbzZ2SktMSURqRGoxNWRkUmx4NzVEdlBXb3B2T2I4Nk9YdHVyNld3UVBKd09PM1RXTXEyQ0dnNXpwTjlkUWFmQi9zRDAzNE1wZDh0Qzhtd0V3Y3NPOTdSVi8xd2siLCJtYWMiOiI3NTViODM2OWFlYmEzYmMwOTIwZGFjNDYzMGQxNTdlMTdlZDI3ZDc2MDdkNDVjNDAxOTcxYzkwZWM2Zjk4OWYyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InQ3c29wUjQyL3Nkak9KejJReE12SGc9PSIsInZhbHVlIjoiMXB6TTd6eElqcEROQlJCK2l0VG4yOWc5eStYSFlNUjRRRkVpOFQ3bFJZWWFCSTBSR3lwNjRBaUxPUnVBSDhZT0tvMnR3dGs5TFJlanVEWkxiSDhpamxjZ25IV2RHTGxKd2pnNytpQ3NaMFBzdFdSVlV4eWxzYkw4bVNoM0VqU0VzZlloSUd0RVBJNkM0T2VpY0xFVGlhWktYVXZIZGN1Unl0dzJ2TDhVYmpINmVWU0RJVFdubENnbDdmc2RhWW5IaGZ4cHZhdXBhNXpabytGL05WZVM4alhkaUd5L3plNXl2b29GRGdZaFdVK2pmMFpFVzZFZnhtL2xNK0tzeW5WaHlZYVF5RGgxS051MEhxUjF2bUxaeTJzTUhpZEF2eWIvNjROeUR6QlVxKzFYN1J0bW1td0NWM3lzUUxzMExLOG1xemVlUFhxWGhFTlJwMm82VW5VbmJoUS96L2JNVnAvTjlaS0kvcUswVnZxZytCVXQ1NkZVR0h6MEdUbTVDdTg5eWRmLzJUdzVkeFV1ZGxLYzBBY0toaUpUdFBmRG1hMnJud2JFcVMxL0lXRFNQQmVWN2JBS003RmxxLzdYMDJhSi9qRnVDSUhlTFRFMkJqY3l0dk1SSmsxM3BLWDlwMzRuRi9MQzRua2hFem1mUUhicU1zeGxXQUIrRDNoalR4VWMiLCJtYWMiOiJlMzc1YmNmYTMzZTRlNWM0OTQwODY5YWU0NmY2MWJhZTA2MWIxYzA2YzNkOTBiNWJkZDExYjkwZmM1ZWZkZDhiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IitiRnJrMFIxcWRPUmwvdDNZNmlmbGc9PSIsInZhbHVlIjoiWEdJeCtKM3dXbE9yaHlJUmcxUDZFb3kzd1Awd1VGRURpNFdkOExnUUFIWG9BaWRvRjRHeGRsa253S2UvamRVUlo4QnVyNjhuNGF6QlRGc0F2aGtnNGdoMGlzbVlHaitPR0NoOXBDamVPd0UvSWFhQ3YyL1RmWGM3b3lkYXBzNUtSN3VYbmsyZDlDUDdkb2o0TG1odWsva2xTclFVdGV4Ujd5Q1ZDWi9nMHpvUFJXUksrWmJCOFJHZVBqenBDNGNaWThoM0FGRGtMNmhPSnpzU2pKUHF5SGg5VUdWS0FkMHFwdXNFZG1MZmFFL3JvVTZsZzBraGlrK2hsMnAvUXpTUHl2TE1JMGc0bWcxUWVOWkd6WVV4YXFNL203SG5WcFBVWDBkNWFIOGJ4SWpnTitheVlrWXZHMk1Qd2NsVkd5UklzQ2M0SkhWWHZJTm9tWFNIa0JIM3ROZDdEVlkvMllFRzRRQmJrNHRBemdzaW9uaDRHb3k0VTRjcDNTZW1IdmRFdTJaNkFUSkFtVXlPSVovMVNOVEZMclFWaTJrZ0ZZbHZuN0FQbzZ2SktMSURqRGoxNWRkUmx4NzVEdlBXb3B2T2I4Nk9YdHVyNld3UVBKd09PM1RXTXEyQ0dnNXpwTjlkUWFmQi9zRDAzNE1wZDh0Qzhtd0V3Y3NPOTdSVi8xd2siLCJtYWMiOiI3NTViODM2OWFlYmEzYmMwOTIwZGFjNDYzMGQxNTdlMTdlZDI3ZDc2MDdkNDVjNDAxOTcxYzkwZWM2Zjk4OWYyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InQ3c29wUjQyL3Nkak9KejJReE12SGc9PSIsInZhbHVlIjoiMXB6TTd6eElqcEROQlJCK2l0VG4yOWc5eStYSFlNUjRRRkVpOFQ3bFJZWWFCSTBSR3lwNjRBaUxPUnVBSDhZT0tvMnR3dGs5TFJlanVEWkxiSDhpamxjZ25IV2RHTGxKd2pnNytpQ3NaMFBzdFdSVlV4eWxzYkw4bVNoM0VqU0VzZlloSUd0RVBJNkM0T2VpY0xFVGlhWktYVXZIZGN1Unl0dzJ2TDhVYmpINmVWU0RJVFdubENnbDdmc2RhWW5IaGZ4cHZhdXBhNXpabytGL05WZVM4alhkaUd5L3plNXl2b29GRGdZaFdVK2pmMFpFVzZFZnhtL2xNK0tzeW5WaHlZYVF5RGgxS051MEhxUjF2bUxaeTJzTUhpZEF2eWIvNjROeUR6QlVxKzFYN1J0bW1td0NWM3lzUUxzMExLOG1xemVlUFhxWGhFTlJwMm82VW5VbmJoUS96L2JNVnAvTjlaS0kvcUswVnZxZytCVXQ1NkZVR0h6MEdUbTVDdTg5eWRmLzJUdzVkeFV1ZGxLYzBBY0toaUpUdFBmRG1hMnJud2JFcVMxL0lXRFNQQmVWN2JBS003RmxxLzdYMDJhSi9qRnVDSUhlTFRFMkJqY3l0dk1SSmsxM3BLWDlwMzRuRi9MQzRua2hFem1mUUhicU1zeGxXQUIrRDNoalR4VWMiLCJtYWMiOiJlMzc1YmNmYTMzZTRlNWM0OTQwODY5YWU0NmY2MWJhZTA2MWIxYzA2YzNkOTBiNWJkZDExYjkwZmM1ZWZkZDhiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-203983302\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1994574877 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1994574877\", {\"maxDepth\":0})</script>\n"}}