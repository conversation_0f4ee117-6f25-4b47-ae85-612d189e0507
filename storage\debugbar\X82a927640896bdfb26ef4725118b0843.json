{"__meta": {"id": "X82a927640896bdfb26ef4725118b0843", "datetime": "2025-06-27 00:47:24", "utime": **********.688468, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.265838, "end": **********.688483, "duration": 0.42264509201049805, "duration_str": "423ms", "measures": [{"label": "Booting", "start": **********.265838, "relative_start": 0, "end": **********.636297, "relative_end": **********.636297, "duration": 0.37045907974243164, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.636306, "relative_start": 0.3704681396484375, "end": **********.688485, "relative_end": 1.9073486328125e-06, "duration": 0.05217885971069336, "duration_str": "52.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0023799999999999997, "accumulated_duration_str": "2.38ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.662956, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.95}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.676013, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.95, "width_percent": 13.866}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6813672, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.815, "width_percent": 12.185}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IlZYS1dreEsrU2p0cXhnVlJzZW5RakE9PSIsInZhbHVlIjoiY29IbU1YcFBGZEJJOTN1ZVRwTlpSdz09IiwibWFjIjoiY2U4MmI1ZjEzMjllNWMyODU2NGY1OWY0YTRiOTcyNjQzOTIxZjk1MWM2ZWJjYmRmN2E4MzBhMzY5NzE3ZjRmYiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-379292123 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-379292123\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1698219800 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1698219800\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-172411981 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-172411981\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-150032768 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IlZYS1dreEsrU2p0cXhnVlJzZW5RakE9PSIsInZhbHVlIjoiY29IbU1YcFBGZEJJOTN1ZVRwTlpSdz09IiwibWFjIjoiY2U4MmI1ZjEzMjllNWMyODU2NGY1OWY0YTRiOTcyNjQzOTIxZjk1MWM2ZWJjYmRmN2E4MzBhMzY5NzE3ZjRmYiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750985241332%7C69%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpjMDNDTW8wOEQ0YTBuSWxTYnVDNnc9PSIsInZhbHVlIjoiN1NTcTFGUFk2c2M5UGpKN0VIMWhNeE1pOGE2aHIzVThBd1lMNFh5cmVUeXRwQ2FDR0NuTHE1MVRKczdYVUorM0pEVWs5aFRJR2wzVGdDdlM3eXlUMzVMZCtEcHlBYmtYRGZkNXdHWkJ6ZC9Lc1lYRm5DeU5DRjV1Rnc5VzhwUDlRMmo0YTNJaEQrcHhjRW03aEpqQ1J3cGcwNVVnU3psWFZzSmNpVFhaUFpvWUJJaEJEZ0Q3K2x2dGVOVzJYUEkvY1JBbFFUTHVqaWVmWU1vTXdLTHVUYlp5NHEzeERZRlhkYWRrUHcyU1ZORU5aN25OUVNVdnJ0TlJQWnVKZHFSRHdTSFRLSU80WVg2SjFWTDJSUnBWMnpQRkdFbGEyYk56VHYwWXFIeW1uM2tJRURMVlZ3RHhzMnFaNGhoa1BPdUUyazllZjZQNE1wcisvL3BWekFaTmxqM2w3MWN1aUE2UnlJTFRxZFlyc2NGUTJ1VkN1L3JGc2xDOGx1OVVMMEs2Q3MzKzczV0dtcWZ0NXhPMlhRazh3VWp6amFUSHZQWitkbmljSXRxZGJRY2pVMFl5Q1RDa09GRkVxaTJKTEpFb08xK0NiSUVzaGNDc2syd21wQmFoTi9UN0RORHNDSW45S3lOY3BleW4ya1hoZjJPbHV2MU1lMUVGWDJGR2EvdlUiLCJtYWMiOiJhYzVhNWVhNGYxNWE4YTNiMjQxNzQzNTk3ZjAxNmM3NTRmNDlkMTE0ZTRiY2NmMzNmZTZiYmQzZTczNWI3MTEyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkhoNDFwZS8zRlFUWmc1eGJnaUdOSUE9PSIsInZhbHVlIjoiMjZ6cWRNSFN3a3BHR1BEQ09wMTh3TkVNMVVWOVNqZHRlbTdzanRNenY1blBUeDFqdkk0WlRLa0lVWExTZHNERDZVeEFWck51ek4yT1dqM0tXWC84aTJhZTN1MW9jNFZXd2hwbEUrWE5GTGtBYkVHQkQ1eGRITGhFMDFWankvUW9ZK1RpSVFRWWY1bzRGRjNhSE1sNU1YektRSHFZZzVrU1VhbDM0STRNSjNyck5iZlM2SlZkcE9ocnB2ZXBnbmJabVdtay9LdmR2bmhZMi9VTEhmbjZuOFcybWRGbFJRdmx6V3lxSkkrQm12UEMzWCtwUzZTSFNTcnhsZXZ6cUwyamtxUTVQV0orWWdOayt0OHdCVmJlTUNLVTRvMHdKbUJQT2FqM0w3TUZhNGVKcUxmRkxWaUpFTnhIL2F0OFhvOUxxYjRjUDFKb1FNVlVBMXVaVVR2eFVMK3ZLN0gvTndpUWdDWjAxam1idmhwMHk4RU1CZ2ZNWjVidFhnZjlFMnFIcVl3VW1PVy9yWVYydmU4YmhmQ2dnZXJRNWgwdFJMT2M4aWliQkxzb2tFUk1pUVRFaDdaVDBYbEczbXNNT0JOdng0TWxLR2NlN2hSem1GbzJMMnZnbFFiYjRCTWlsVm80MHI2OTlhU2RmUXpqQktyMi9QbVl5QnlITW5sZjFDYXkiLCJtYWMiOiI0ZjU3NzVjNDM1ZGM0ZDQ2OTRmYTNkMmY4MmM1Njg3N2VjMDEyYWZkODkzODIyMWJjZTczMmIzNzZhYWM0OGMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-150032768\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-926771599 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-926771599\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:47:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFhMTJYc1hGWGN2NTNQVkZMMjRqS3c9PSIsInZhbHVlIjoibThTUXJURVFvckJXVmlNVUtZeEVtQXljRXZBZzh3OUJTUFk0Q2lvQmhXWExKYllQa3luTzBTSmhkYzhDU2JrRFNaSjdUTmNwVWp0Z2VBV3RVc2FlbHNkVTlHMUY4QVM1YzMra1hZdG9rVWtDcVNudnBSKy9DbVlpeE82TTQwUVVNWGVRWjcvRklTcGVuMUZScnlZQk5CakloeEwwTE4wS1YrMmpmYWgvQk1Dc2RUVVpKQnhzU3NXV0QrcXlBcW40VXVPVVU4eDlydldoN005ZC9LTDZpVHlOMTVnNjNmM2wzTll0OUNtUTB5OEdGZEpuWUZpWXNoMDNTMXlySzBudHNIVEJvV1VIU3IvQ0FtOTVuRTNKN3lVNzNtbEpnZXN5WUVIQXo5VXVWNzBtTnlZUGJrWU1iZTBVU1JKNy9jbE9PQVlxd2JqbjN6Y2FZUDk0aUhUTXQzR3FwUzFIbVZnaWRaUzZWdTFWVzFlSHVPTnNIbytEektLZDhXZVNWTDVXNnBrTTlMYitFMlFzN1dicjg3MzNOeHdTY2pzTjRZeWUxbVFGdWdjekRReVYzdDhMeUx5bVc1eVJ5K3FqQ3M4Nm9kdlZBN09TMkNoZXdTVDgxZXVVaDJMbEZYMDR6QWV0TEFoeUI2VEpUdjVhbDhCdGZiSXdjVURSdVUzRGhjMSsiLCJtYWMiOiIyZjllZDc0ZWRiNWEyODY2MDg3MDEzZmJjNWZhZDZlMjAyODc5OGIxZWFlZTY3NDExZTM3NzVlOWZhMGM3ZjA3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:47:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBxMkRiREFxTUszVjhoK0tXYTlKT0E9PSIsInZhbHVlIjoiRS9nTzJQdEd5UHdTUFlLdmdKVk1EVEZRamhxUXJWZS8zR21QZGtCRmViYTZmczFiRkJ3V1lMckVZL3BIajBzRUhHQmMrMVg5SzZaZnluNGVFRWQzcHphOXFkT010aTNDS2tCT2RyWXM4anNMdjNCZ1AvT3RPTThBcXRaWHlqdjRRRDEvZ2NRTzU3MzQ2aWhyZDJTMExXMFpGVzMxZCtkZG0xQkp0NFJ1Y3ZHOVczSURVa3ZrblUxZUEvd3pFTTFZVTRQQkhISUZidXNCUUZwamN1S083TnNCY21xb1E5U05uL0Z6WDljaHVWNWxFSi9aODI3VlZzSjJodkwveVNxVmZ4N1JoaDMxZGRISkRYb2FDTnh1Skt5cjdSL0NsaHBTV1NzZlhCTjd0THlMMEJJSjA5NXFTN3p2ajk4YmJNM1BJSkwwOU0ydkRRZGVGUUt1c1M2bzE5d0x4NHR4eFEwRHhxbnpIZE9pcW1KV0ovRklOSjZMTnM1OFFsNUc3TTZvMVh0YUJ3a3R1S25ndURxUE5iWjVKWlRReDFXUWI2MUJ4RVhhVk9mbVY5OVBTYVV4R3BtamlzUWlua085STZLclQzTjA3VWNDVDNGOC9NVEs0VHUxdmF6UXJYVldISDlMa0xiTE1YeVVBVW4xaXhHT1ZybmNMUmJqS2wvbEV2YVYiLCJtYWMiOiJhMThjNWE1NjY4Njg2YjUzNmZkM2VhMmNkYzhjMWU0OGZhZDNiYjk5NzQ5ZTUyZjYzMmJmOTUxMGY1NTdhZjdiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:47:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFhMTJYc1hGWGN2NTNQVkZMMjRqS3c9PSIsInZhbHVlIjoibThTUXJURVFvckJXVmlNVUtZeEVtQXljRXZBZzh3OUJTUFk0Q2lvQmhXWExKYllQa3luTzBTSmhkYzhDU2JrRFNaSjdUTmNwVWp0Z2VBV3RVc2FlbHNkVTlHMUY4QVM1YzMra1hZdG9rVWtDcVNudnBSKy9DbVlpeE82TTQwUVVNWGVRWjcvRklTcGVuMUZScnlZQk5CakloeEwwTE4wS1YrMmpmYWgvQk1Dc2RUVVpKQnhzU3NXV0QrcXlBcW40VXVPVVU4eDlydldoN005ZC9LTDZpVHlOMTVnNjNmM2wzTll0OUNtUTB5OEdGZEpuWUZpWXNoMDNTMXlySzBudHNIVEJvV1VIU3IvQ0FtOTVuRTNKN3lVNzNtbEpnZXN5WUVIQXo5VXVWNzBtTnlZUGJrWU1iZTBVU1JKNy9jbE9PQVlxd2JqbjN6Y2FZUDk0aUhUTXQzR3FwUzFIbVZnaWRaUzZWdTFWVzFlSHVPTnNIbytEektLZDhXZVNWTDVXNnBrTTlMYitFMlFzN1dicjg3MzNOeHdTY2pzTjRZeWUxbVFGdWdjekRReVYzdDhMeUx5bVc1eVJ5K3FqQ3M4Nm9kdlZBN09TMkNoZXdTVDgxZXVVaDJMbEZYMDR6QWV0TEFoeUI2VEpUdjVhbDhCdGZiSXdjVURSdVUzRGhjMSsiLCJtYWMiOiIyZjllZDc0ZWRiNWEyODY2MDg3MDEzZmJjNWZhZDZlMjAyODc5OGIxZWFlZTY3NDExZTM3NzVlOWZhMGM3ZjA3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:47:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBxMkRiREFxTUszVjhoK0tXYTlKT0E9PSIsInZhbHVlIjoiRS9nTzJQdEd5UHdTUFlLdmdKVk1EVEZRamhxUXJWZS8zR21QZGtCRmViYTZmczFiRkJ3V1lMckVZL3BIajBzRUhHQmMrMVg5SzZaZnluNGVFRWQzcHphOXFkT010aTNDS2tCT2RyWXM4anNMdjNCZ1AvT3RPTThBcXRaWHlqdjRRRDEvZ2NRTzU3MzQ2aWhyZDJTMExXMFpGVzMxZCtkZG0xQkp0NFJ1Y3ZHOVczSURVa3ZrblUxZUEvd3pFTTFZVTRQQkhISUZidXNCUUZwamN1S083TnNCY21xb1E5U05uL0Z6WDljaHVWNWxFSi9aODI3VlZzSjJodkwveVNxVmZ4N1JoaDMxZGRISkRYb2FDTnh1Skt5cjdSL0NsaHBTV1NzZlhCTjd0THlMMEJJSjA5NXFTN3p2ajk4YmJNM1BJSkwwOU0ydkRRZGVGUUt1c1M2bzE5d0x4NHR4eFEwRHhxbnpIZE9pcW1KV0ovRklOSjZMTnM1OFFsNUc3TTZvMVh0YUJ3a3R1S25ndURxUE5iWjVKWlRReDFXUWI2MUJ4RVhhVk9mbVY5OVBTYVV4R3BtamlzUWlua085STZLclQzTjA3VWNDVDNGOC9NVEs0VHUxdmF6UXJYVldISDlMa0xiTE1YeVVBVW4xaXhHT1ZybmNMUmJqS2wvbEV2YVYiLCJtYWMiOiJhMThjNWE1NjY4Njg2YjUzNmZkM2VhMmNkYzhjMWU0OGZhZDNiYjk5NzQ5ZTUyZjYzMmJmOTUxMGY1NTdhZjdiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:47:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-76365326 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IlZYS1dreEsrU2p0cXhnVlJzZW5RakE9PSIsInZhbHVlIjoiY29IbU1YcFBGZEJJOTN1ZVRwTlpSdz09IiwibWFjIjoiY2U4MmI1ZjEzMjllNWMyODU2NGY1OWY0YTRiOTcyNjQzOTIxZjk1MWM2ZWJjYmRmN2E4MzBhMzY5NzE3ZjRmYiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-76365326\", {\"maxDepth\":0})</script>\n"}}