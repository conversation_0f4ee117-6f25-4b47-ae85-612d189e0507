{"__meta": {"id": "X9259fa4501a11547406bc703142bc387", "datetime": "2025-06-27 02:23:32", "utime": **********.997458, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.543904, "end": **********.997474, "duration": 0.4535698890686035, "duration_str": "454ms", "measures": [{"label": "Booting", "start": **********.543904, "relative_start": 0, "end": **********.934921, "relative_end": **********.934921, "duration": 0.39101696014404297, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.93493, "relative_start": 0.39102602005004883, "end": **********.997476, "relative_end": 2.1457672119140625e-06, "duration": 0.0625460147857666, "duration_str": "62.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00326, "accumulated_duration_str": "3.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.970918, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 70.859}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.982348, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 70.859, "width_percent": 14.724}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.98831, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.583, "width_percent": 14.417}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1264544769 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1264544769\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-848779521 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-848779521\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-303949089 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-303949089\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2006564882 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991011173%7C16%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlhvQy9vRFhsTm5sMjR1cmVJdlEyb3c9PSIsInZhbHVlIjoiSTM5ZTl3VXpWazlqOXRuY2dveW82eW16S2E2aGxmSWNKOFAxbWVVTjVZNnVtVG1GS0tZdS9XU2I4cS9QaDIyS0NSN2NaTnlnTC9vV1N6TUJQQzVVTERkd3NEU0krZUZwZlZ4dldtcGRVVnJSVVhSbUdkMWNJRkJ4d1ExMnhKS3YySU5jbEpsc1BieWVBZUkzV0J4a1ZjYzE2RnhESGNXNlllb0tQVVJ3ejk0c3ZGcngxdUFuaDQwbXphNG05eGhIUmwwNUNvbHZvM3dsVkpldDhHaUgwdjBkN2pNK3NvOXhZaFFvMVJubkpJbzdVTUMrRXJDbjFxRTlFVmlDMCtBcnZrTGp5bUttUHUyVkRYeFhWMVc0UGNFVE5XWmh5TVhQcXdpWUlrQVNKUFRiNyt4d1IveTM0ZGpGYW1vRWFkNmgyenlmbWRaS0hFcGJBR2l1NlNyZnc1UTd3eHhNRzN3VXdiM2Z2MEhINjduZllqYUhkUUQ1T3UvVE1EYjBnNGs5RXkxZzhHTVlDeks2WGVrdEFUcmJ5RGlXNWpiTFpTUGRBdkZsVjJBQkpSMTFENnNsOGFRanRnYlI4WDhmb2ZJUTZVODl0SWpVN3BWRS9ZTXpDK2JyMjhYd2ZUd1EwOHk0ZjcwN0g5TkZoaGRKWCtVT0FPKyt5TFJUNlJoYytHdU4iLCJtYWMiOiJkNDIxZTc4YTY0OTg0Yzc5MzAzNGY4NjMxZWJkNThjODY2YmU1Zjg0OGI0MjgwOWQ1MGI5NGM5NGUxNDk4YTdmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ink3cVp5S1gwMkxUNEdFb3Nrd0IxOEE9PSIsInZhbHVlIjoidkZNMCtRbUd3NmZIY3ZDRkpGYk9kcUVxdnpnRWtUc0s0cXhYL0NjWlVJMGNHVW1tSStmdm55N01PVGJYQjNhaGxQVXplUzRUVTJhVjR6VzNHNlVOTVJidXpLYmVVWXZNVzB5SWJqazdIZHlHYmFyaXVZQ044R3FiNGpqTmxZYkppK1YrMlpwbHVCS05YSkRNbHhSbzR1MUI2YjZNc2wwaXJLay9HQXpJTHVXTXpTRm9kNThNWVBNYmF4YWcydm5jNW9xZ0pqdG0xSklDVnVTZjBEclgxUVZFRHlkUUNtQ21GWGNudU9IQzdNclZGNE4zZVdFZjhXdE1IN0oxRUN3cEhZMnNkOEFIcmlCVWM1ZGhjMlZnVFpEbnpwM0ZsVmQvRTg2WUdiWlpNK0lXeHJOckxWaTBtNHJoY2ZiVzA4VjZramdNUGRLdnJiMWI1SlZITkpBaHNQSlZwd3prakhQY3llSjRONk5RZVQ5QWJ2Z0pOenFBODRiZW9DQ1ZKTVppQmpzK1hSaDVpcVdTS1hNMUd4alRHeHJzN2gzUUZzVjJYMXBPOGdzNmN6dHVtSkFnUXBBQi93NndwbmNlTk9OajlxZVo5emZwQzBsY3AydUp1Tmh5Z3ZWclIzQTA3eXJacGMwR1NLVnJMYnFYdDhxaVdjWm16cmMvQ1JrUU4yS28iLCJtYWMiOiI1MzU5NTc1YzU2OWYwNTQ5ZjQ3ZjAwY2U3NTBjMWE2MmVjZWFmMDE1Zjg0OGM1OGQwOGEyMzRiNTdhYzU0NjY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2006564882\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-765363386 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-765363386\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1710529315 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InczakY2RTVIK3VtNXJHY013NUl1L1E9PSIsInZhbHVlIjoiZStKc3ZVYlhQai92N043WmJmN3Bsb1h1RENlNnlYeVZPMEZ5L2VLc2VuSGNDUHFCZVRFQlI0MFUxS1piU0VkYWRtVm5uM2FBRjZHYmZtQXJXaUN4MUgxOXdMQlFib2lQT2I0aHFMbFAzek9tbHNrbDB1Vzd4VFN0dTMzV2lsNFFIemtDZncxRDBpTDJ5QTd3aUxLMDkwM01ZMnI2V3IwbTdIZk16Q3lWN0RpbW03NUN4NzI0MG93VGdFVFRIRDVYUDM2V3Vac2p3SUs3ZlVSUk5Ob3ZNUXFvOHpZS2VVRHd1K21vU2VZMHF6ZG0zNmZuOXI0bUZ0bFFPT01jQkhNdDlNMGxaUmxLTTc3T1dWUnRiVFkwcmRyT3dmVCtKVWJ2SnNFNlhSZTBrZ0FYQ2E1RFhQYUlBMzRSbUxZN0xUckJVUEVwN2tTN1hnakJtTjROQ0J4dlRVcE9wdmF0cFVaN0JmdUdkdW5HRHJJZFhaV2tqL0cxUWFtYmJ0Nitwa2JUVWdWN0JZaWFuNkV4RkJMU0lPcFpBTm5Nb1Y2ZWZmM0ovaU5hNHcyUkFMdWNSUFU0OWV5ZGFLVG5La0VOSXZENXArRDQzcEJxOHQ1dU1VcmprUXNaMldESmo3di9jT0lZOEIvVFhncFF2RHY3bGhvcS81eFg5RUlXeVRGRkNiMmIiLCJtYWMiOiIwNmI1Mzg4N2Y2MWJmMTM1MzljYTBjNTQxMGRhOTZjZGM3ZmI5ZmQxZDAxOTM3NDI1ZDYyYjM2ZDNjYjRhZmY5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:32 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlN2cVdxSEdTZk0wK09ZUGFwazlOSHc9PSIsInZhbHVlIjoidHR4WjdNM0ZPSFBPT1JUaFRPSEVZY1VLYkZwZmFEbERXaEFQajh5SDVMTkJweWwzNEdkRUdtZEtUenFpTXZFRE1WTlVPdWQzVmMybVR1a0JKTDgvS2NZQTNSaFBjOHpQTkU1UDJSUHBqSDJVSzk4WWRqc2JkZERWTXR6aVV3SGdmU3hhMUdvUVFrOVZYTDJRUERlK0lCSjJFdFFRT0hubUg3NzBLcmo4blovL1E2NUw2L2lyRnhFNk5sV1A1NHp2aSs3VG8wWVBoMHZhd2FtTDFoR0tBUjhGRXBPbVpVUFoxSklBQlpJQmZyS0dpR1Zvd2hMNXBHZWxBcEhqVVlVV281RFBNZnpZbEJxLzhJMEFESnk1UDBhWjE2QWxjNUpwR01zREczOFgyejRQVkxrTVRoNHZjRTJtdmc3WWZFcU4rUlhwSHFhZW9RSDl6a1IxcEpvTjRnbncrd05NVXRaOFhHQTl0dkt5RnFTWU5BUy9FSis1dWp1eVZ2dEZYS1MvTzJTU1ZkVUIrVUo3bnBTVk1SeHUxUTdMR24rdUpxVnE4bEU5Zm8veFI2ZEVHQWJUQjJNK29JNU9jK25kbzgvbDBMV1BhVEIra3gxRmpoV3Y5VDQyNGV5QldhZXJBa0lualpVa1NGTm4wNEp2RDN5VzliMDRJYmxvSG5tYUZvcEQiLCJtYWMiOiI5ZWQ4OGUwYmE4NjhkM2M1ODViOGUwNGMxZDhjNWI0ZWE1MGE2ODg0ZmEwYTc5MGFmNWQxOWFjZDViNjZmMGZjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:32 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InczakY2RTVIK3VtNXJHY013NUl1L1E9PSIsInZhbHVlIjoiZStKc3ZVYlhQai92N043WmJmN3Bsb1h1RENlNnlYeVZPMEZ5L2VLc2VuSGNDUHFCZVRFQlI0MFUxS1piU0VkYWRtVm5uM2FBRjZHYmZtQXJXaUN4MUgxOXdMQlFib2lQT2I0aHFMbFAzek9tbHNrbDB1Vzd4VFN0dTMzV2lsNFFIemtDZncxRDBpTDJ5QTd3aUxLMDkwM01ZMnI2V3IwbTdIZk16Q3lWN0RpbW03NUN4NzI0MG93VGdFVFRIRDVYUDM2V3Vac2p3SUs3ZlVSUk5Ob3ZNUXFvOHpZS2VVRHd1K21vU2VZMHF6ZG0zNmZuOXI0bUZ0bFFPT01jQkhNdDlNMGxaUmxLTTc3T1dWUnRiVFkwcmRyT3dmVCtKVWJ2SnNFNlhSZTBrZ0FYQ2E1RFhQYUlBMzRSbUxZN0xUckJVUEVwN2tTN1hnakJtTjROQ0J4dlRVcE9wdmF0cFVaN0JmdUdkdW5HRHJJZFhaV2tqL0cxUWFtYmJ0Nitwa2JUVWdWN0JZaWFuNkV4RkJMU0lPcFpBTm5Nb1Y2ZWZmM0ovaU5hNHcyUkFMdWNSUFU0OWV5ZGFLVG5La0VOSXZENXArRDQzcEJxOHQ1dU1VcmprUXNaMldESmo3di9jT0lZOEIvVFhncFF2RHY3bGhvcS81eFg5RUlXeVRGRkNiMmIiLCJtYWMiOiIwNmI1Mzg4N2Y2MWJmMTM1MzljYTBjNTQxMGRhOTZjZGM3ZmI5ZmQxZDAxOTM3NDI1ZDYyYjM2ZDNjYjRhZmY5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlN2cVdxSEdTZk0wK09ZUGFwazlOSHc9PSIsInZhbHVlIjoidHR4WjdNM0ZPSFBPT1JUaFRPSEVZY1VLYkZwZmFEbERXaEFQajh5SDVMTkJweWwzNEdkRUdtZEtUenFpTXZFRE1WTlVPdWQzVmMybVR1a0JKTDgvS2NZQTNSaFBjOHpQTkU1UDJSUHBqSDJVSzk4WWRqc2JkZERWTXR6aVV3SGdmU3hhMUdvUVFrOVZYTDJRUERlK0lCSjJFdFFRT0hubUg3NzBLcmo4blovL1E2NUw2L2lyRnhFNk5sV1A1NHp2aSs3VG8wWVBoMHZhd2FtTDFoR0tBUjhGRXBPbVpVUFoxSklBQlpJQmZyS0dpR1Zvd2hMNXBHZWxBcEhqVVlVV281RFBNZnpZbEJxLzhJMEFESnk1UDBhWjE2QWxjNUpwR01zREczOFgyejRQVkxrTVRoNHZjRTJtdmc3WWZFcU4rUlhwSHFhZW9RSDl6a1IxcEpvTjRnbncrd05NVXRaOFhHQTl0dkt5RnFTWU5BUy9FSis1dWp1eVZ2dEZYS1MvTzJTU1ZkVUIrVUo3bnBTVk1SeHUxUTdMR24rdUpxVnE4bEU5Zm8veFI2ZEVHQWJUQjJNK29JNU9jK25kbzgvbDBMV1BhVEIra3gxRmpoV3Y5VDQyNGV5QldhZXJBa0lualpVa1NGTm4wNEp2RDN5VzliMDRJYmxvSG5tYUZvcEQiLCJtYWMiOiI5ZWQ4OGUwYmE4NjhkM2M1ODViOGUwNGMxZDhjNWI0ZWE1MGE2ODg0ZmEwYTc5MGFmNWQxOWFjZDViNjZmMGZjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1710529315\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1881514900 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1881514900\", {\"maxDepth\":0})</script>\n"}}