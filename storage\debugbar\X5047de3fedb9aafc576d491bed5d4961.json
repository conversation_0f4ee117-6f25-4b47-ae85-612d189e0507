{"__meta": {"id": "X5047de3fedb9aafc576d491bed5d4961", "datetime": "2025-06-27 02:25:35", "utime": **********.308981, "method": "GET", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991134.845131, "end": **********.308995, "duration": 0.4638640880584717, "duration_str": "464ms", "measures": [{"label": "Booting", "start": 1750991134.845131, "relative_start": 0, "end": **********.216737, "relative_end": **********.216737, "duration": 0.37160611152648926, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.216746, "relative_start": 0.3716151714324951, "end": **********.308997, "relative_end": 1.9073486328125e-06, "duration": 0.09225082397460938, "duration_str": "92.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52225552, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.304589, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@index", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=33\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:33-84</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.014020000000000001, "accumulated_duration_str": "14.02ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.252263, "duration": 0.012060000000000001, "duration_str": "12.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 86.02}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.272959, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 86.02, "width_percent": 2.14}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2761972, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:37", "source": "app/Http/Controllers/FinancialRecordController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=37", "ajax": false, "filename": "FinancialRecordController.php", "line": "37"}, "connection": "kdmkjkqknb", "start_percent": 88.16, "width_percent": 3.281}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.2897902, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 91.441, "width_percent": 5.777}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.29232, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.218, "width_percent": 2.782}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 3, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1029328319 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029328319\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.296037, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1885592720 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1885592720\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.297196, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2120343388 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2120343388\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.297942, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-385619097 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-385619097\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-520108105 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-520108105\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-146403729 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-146403729\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1541464365 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991131557%7C24%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkJ4WERCWXYzdHB2dXFrMlZPakdKSUE9PSIsInZhbHVlIjoiMFVWODBsMUI3UTFWY1lmN2xSZkN6b3daRWFidngvZGxHcS9SMVRzb05PSk0rQS95anNXdzlWOVlQWW5meExLQzloS3lhM05KZitYNFhsdWxYdmJuQjhVckR6djVYaHAwYWhIZTh5eFdEWlVPVllVYUo2dEhPRkNqeFRnbVVMYmZVL1NiTmhnTGpNT0FTUWxyNE81V3gyLy9JRHlnN2xLeXg4L2NSWFlmczBQSTA5V1BFY3Y1eGpjZ1YxZUFEZEdCY2lMclRnYUZwRS91SSt5R3JqOEVlMk1kem9DQjhsR2o5OTk0Vk5uM0RlTWdNaHBRUmJtZmNLQTFSeU9FdmJiNXFHQjNKRCtUcjFoMnl2ZjN4QXZKakdlbkwvZ1lFR1ZqT3pQT0c3OUZMTy9KNklueThESEtyM1dsSDhqTGZIWlZzbXBsZklya096SWpwSkVZcm9iLzdMVnVPeWZWUVJDWktOa0l2SVFDWG9CZHBobTdKRjJ1WTlSWisrbUV4N0JIQ1YvYmVWWG0rV0RNTG8rdWcvOXIwdUtiU0VUVDRmYkV5cklESjgxVE85VDRTcFZuUHB4MFNUbGRXcEJLTDVaVS9Qa0NiaWQ0UEhBZ3VBT2FnVDZoRm1teEEzMUt1ckRyTjdhbXB3ZllyRUhsYjh3amo0VUJUQUVGNTZwb2FXNTIiLCJtYWMiOiI5YTE5YWJiZWQ4OWE1YzNhY2U5NThhZWQyOTY1M2YxZTU1NzNiNTczOTQ0NDg0ZWFmMTVjM2E5OWVkZDVkYWUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitJM3FUNFhQMUVldTk5N1VXQ1JzaWc9PSIsInZhbHVlIjoiM0RNK1JqZ2syMzE2Y3hBdmh3UVVFUi9WbEt3UlZZZ1FFd01xdHpOZkd5TStnRlkrQnFjK1ZqNHpaMEQyUnp2bU5hUFRVVXplS1RTSXVzeXYrVjVKSXplZjVzZ2lLM3c5NTFkMkZKZ0hGOUZtbVVjbXhsclZ4NlNrSjZ0a25hY0xja1NvSEZBQWdhemVVOG9LYWJNNEt2TlFrK00rSG1LMVFKK3pLRk81VzNtb1dNZHBHRzZZWi9wYlo4S1AvVFhlemJORXBSRkNQL1dpOC9hc25wMVYvQW9GTUJnUFBiTXRsVkZpZkJvaHhMdEcyM1BpNHhLMW9zcnNjVUw1aytYbXRUZXpJOTFUeXFZdCtYTForb0FTcGdxaldkOEQ3N25mNjRpLzdybEU1d0syWXYxWnpVUDArdjlGU0c1SzVDT2pKZVFRM3JxZ0hwNDNmaXd5S3lSeThQaXM2ZW81MUNBbkxXQmpKeUh6aHV1K3dRYXp4RVRWR2hjaWpQc3JuNFVONWFTbWtrTSs1R3R3RnlPU09qdUZuMStFMG16anQxZVRYTjB0SGlTUVo5d1krTFZJdlRaV2dPQmlHTGNxMHQ3OGFTeDdkYktlQ2hjaytEMmdZTTBoZTVLdTY2NmdjZ1hBeFZXL1dHTHRwYi9SbkpZT0srUnlDakx3VWczREhOQUciLCJtYWMiOiIwZmJmNmE5OWI1NGIzNzczNzk4YTJmOWQ0NDI2N2Q1OTFhMDM5MGZlNTk5MDNlZWRjMDE4NGVmY2FiMTczYTFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1541464365\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-596075279 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-596075279\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-671606191 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilc4ajJKV2h3N3BSL1pSSnVweG5HaVE9PSIsInZhbHVlIjoic0wxalRKbC9qM3BRbDlTeFhDQTNZNXlrZEVZK1E3eTk1NHVSaktteTM2VGdkUDg4NGx2N3c3ZEpOeUhkVzRXbE83cUp0VGgyK2VLY1NHdWxmaVVQd3NoejJuSFNMQnRFaExNSVNYbEJmYnkrcnJ3b3dTRGc2VmMxNEtqK1MrcC9IUEZjOVFpV3pXSURIdzhNOWR1d2pGYW5hWURLSHhWUERUOEtWeENsVXZOcnNoamxUVExlVmRpekZPby9EVmRyc3E3cVN4QjlSU2hqT1NLSnAyS0xUSW9VVnFaQkVkQ0s0aXM1TlVOajZXVEI5R0ZSdmRTSm11RjI0UlBySy9uZWJXR0dHeWQ2ckRKdkF2cE81N2Zwa1ZjdU1LdGpmblZjc3QvM0hiRGNKV01VRVExYzdhdFg4dWU3SHBSL2tXL01nWEQxSTB2L3NuTDNCcmhmbm9NVWRtbnoyM3B4ZnRZd3QreU9yMUJJU2ExWW9hYmpnTkR4cU02NTVMKzhJd2FGcDRZWTIrRERwMEtiR2JkSXpFWC9KRFAzazNGQ29mV0NLVmFrbEdZckxvRDk4bmZqQUZ2bVRTMmkyK2g1VjlKM2NqVktqaEdzL0NSalBVVFRhUHVmZkk1aDhtcFV1emszMW1wUndKU2w3U3RNeWRLMkx5RjVJTVA1c3BpOGRsK3IiLCJtYWMiOiIzMDE3OWMxODBkY2Y5NGZiOTQ1OGE5OGJjYWY0MDFiNzg5ZDUwYTVmMmJlOGIwMGM1ZThiYjUyNWI1ZmQ4OWY3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InR1NkVpUVNNTWFNOEE1L2IwNmNSWlE9PSIsInZhbHVlIjoiTWNVSnYvS2JGamtmMlBxdVpxRVVRMGFDNU1WR3RnRHNESXlUMEJzL1FkT2hhbzNjc3NEUEM2NlBmOGxIaUt5Z3lxZHJ0Y2J6UmhkWXNHQlArNzJiYkRQL3RaYkdjZ0xLcUlHZkU2SGxrM1BOdSt3WFUzRFpPWURkbHZlelFWVmpFdEVCbW1HbTczVEhhN3EvM3Y1QXdiQnVwS2p2bkNnUTZVRm41ZkV0OHliWTJoaDBRRDkrK2h2QkN5eVQ3TllYeUdMdFhkNkk1VXZFdXgxR3hIV0pSS3VqM1ZiWXNkUWlQa2g4Zkt6bE91SE9iMGVvUTdGK0lXM24wclFCSFpRVm5sRlZ1MmE5WmlXQTB1SUI2M2FUWGxQK2QvdXMvNk4yelJ2NVN6cU9Oc1pxa1ozSzcxVDhKTy9DWWxWQVRPa2tOS3FCc1JJK1B0Nkltb3hnNXV5ZkI1TkU3VXMxakhURTdLVzdMa2ZqeXZ4SUhnTXdVTkFBb1F0R1oyMVE4MW9LWFhpZEZMdWR6K3VHN281QklFR2xVOWh5cUlrbDRkNnVXckQxQXE2eE9hUkZYTURiRkpOdlZ0ZHdQbjJrNVJrdW9raVFIRlNsWGlZLzBEWGd6Sm14MVZlNkRZeXBkZkRadEVnK20xYk9CR0t5dEtVRG1VeThvaUZORFQwNHp0RnIiLCJtYWMiOiI2ZmRlODU4MTkwODlkNjFkOTQ4NzkxNjRlOTViZmY4MDdjMDgyYzVlYjYwMWQwZDMxZjhlNzY5OGE3YjVlNDEwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilc4ajJKV2h3N3BSL1pSSnVweG5HaVE9PSIsInZhbHVlIjoic0wxalRKbC9qM3BRbDlTeFhDQTNZNXlrZEVZK1E3eTk1NHVSaktteTM2VGdkUDg4NGx2N3c3ZEpOeUhkVzRXbE83cUp0VGgyK2VLY1NHdWxmaVVQd3NoejJuSFNMQnRFaExNSVNYbEJmYnkrcnJ3b3dTRGc2VmMxNEtqK1MrcC9IUEZjOVFpV3pXSURIdzhNOWR1d2pGYW5hWURLSHhWUERUOEtWeENsVXZOcnNoamxUVExlVmRpekZPby9EVmRyc3E3cVN4QjlSU2hqT1NLSnAyS0xUSW9VVnFaQkVkQ0s0aXM1TlVOajZXVEI5R0ZSdmRTSm11RjI0UlBySy9uZWJXR0dHeWQ2ckRKdkF2cE81N2Zwa1ZjdU1LdGpmblZjc3QvM0hiRGNKV01VRVExYzdhdFg4dWU3SHBSL2tXL01nWEQxSTB2L3NuTDNCcmhmbm9NVWRtbnoyM3B4ZnRZd3QreU9yMUJJU2ExWW9hYmpnTkR4cU02NTVMKzhJd2FGcDRZWTIrRERwMEtiR2JkSXpFWC9KRFAzazNGQ29mV0NLVmFrbEdZckxvRDk4bmZqQUZ2bVRTMmkyK2g1VjlKM2NqVktqaEdzL0NSalBVVFRhUHVmZkk1aDhtcFV1emszMW1wUndKU2w3U3RNeWRLMkx5RjVJTVA1c3BpOGRsK3IiLCJtYWMiOiIzMDE3OWMxODBkY2Y5NGZiOTQ1OGE5OGJjYWY0MDFiNzg5ZDUwYTVmMmJlOGIwMGM1ZThiYjUyNWI1ZmQ4OWY3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InR1NkVpUVNNTWFNOEE1L2IwNmNSWlE9PSIsInZhbHVlIjoiTWNVSnYvS2JGamtmMlBxdVpxRVVRMGFDNU1WR3RnRHNESXlUMEJzL1FkT2hhbzNjc3NEUEM2NlBmOGxIaUt5Z3lxZHJ0Y2J6UmhkWXNHQlArNzJiYkRQL3RaYkdjZ0xLcUlHZkU2SGxrM1BOdSt3WFUzRFpPWURkbHZlelFWVmpFdEVCbW1HbTczVEhhN3EvM3Y1QXdiQnVwS2p2bkNnUTZVRm41ZkV0OHliWTJoaDBRRDkrK2h2QkN5eVQ3TllYeUdMdFhkNkk1VXZFdXgxR3hIV0pSS3VqM1ZiWXNkUWlQa2g4Zkt6bE91SE9iMGVvUTdGK0lXM24wclFCSFpRVm5sRlZ1MmE5WmlXQTB1SUI2M2FUWGxQK2QvdXMvNk4yelJ2NVN6cU9Oc1pxa1ozSzcxVDhKTy9DWWxWQVRPa2tOS3FCc1JJK1B0Nkltb3hnNXV5ZkI1TkU3VXMxakhURTdLVzdMa2ZqeXZ4SUhnTXdVTkFBb1F0R1oyMVE4MW9LWFhpZEZMdWR6K3VHN281QklFR2xVOWh5cUlrbDRkNnVXckQxQXE2eE9hUkZYTURiRkpOdlZ0ZHdQbjJrNVJrdW9raVFIRlNsWGlZLzBEWGd6Sm14MVZlNkRZeXBkZkRadEVnK20xYk9CR0t5dEtVRG1VeThvaUZORFQwNHp0RnIiLCJtYWMiOiI2ZmRlODU4MTkwODlkNjFkOTQ4NzkxNjRlOTViZmY4MDdjMDgyYzVlYjYwMWQwZDMxZjhlNzY5OGE3YjVlNDEwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-671606191\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-353115601 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353115601\", {\"maxDepth\":0})</script>\n"}}