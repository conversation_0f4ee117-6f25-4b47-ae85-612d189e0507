{"__meta": {"id": "Xe15a54be106c249cfc0b04cc0d9c6902", "datetime": "2025-06-27 02:15:43", "utime": **********.099911, "method": "POST", "uri": "/payment-voucher/confirm", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990542.643733, "end": **********.099928, "duration": 0.4561948776245117, "duration_str": "456ms", "measures": [{"label": "Booting", "start": 1750990542.643733, "relative_start": 0, "end": 1750990542.973027, "relative_end": 1750990542.973027, "duration": 0.32929396629333496, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750990542.973035, "relative_start": 0.3293020725250244, "end": **********.099929, "relative_end": 1.1920928955078125e-06, "duration": 0.1268939971923828, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45902872, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST payment-voucher/confirm", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@confirmVoucher", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.confirm", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=151\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:151-179</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.06174000000000001, "accumulated_duration_str": "61.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.001092, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.802}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.0109909, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 2.802, "width_percent": 0.664}, {"sql": "update `voucher_payments` set `status` = 'accepted', `approved_at` = '2025-06-27 02:15:43', `voucher_payments`.`updated_at` = '2025-06-27 02:15:43' where `id` = '20'", "type": "query", "params": [], "bindings": ["accepted", "2025-06-27 02:15:43", "2025-06-27 02:15:43", "20"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 153}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.013455, "duration": 0.05485, "duration_str": "54.85ms", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:153", "source": "app/Http/Controllers/PaymentVoucherController.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=153", "ajax": false, "filename": "PaymentVoucherController.php", "line": "153"}, "connection": "kdmkjkqknb", "start_percent": 3.466, "width_percent": 88.84}, {"sql": "select * from `voucher_payments` where `id` = '20' limit 1", "type": "query", "params": [], "bindings": ["20"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.07141, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:159", "source": "app/Http/Controllers/PaymentVoucherController.php:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=159", "ajax": false, "filename": "PaymentVoucherController.php", "line": "159"}, "connection": "kdmkjkqknb", "start_percent": 92.306, "width_percent": 0.972}, {"sql": "select * from `users` where `users`.`id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.074681, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:159", "source": "app/Http/Controllers/PaymentVoucherController.php:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=159", "ajax": false, "filename": "PaymentVoucherController.php", "line": "159"}, "connection": "kdmkjkqknb", "start_percent": 93.278, "width_percent": 0.486}, {"sql": "select * from `users` where `users`.`id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.076074, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:159", "source": "app/Http/Controllers/PaymentVoucherController.php:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=159", "ajax": false, "filename": "PaymentVoucherController.php", "line": "159"}, "connection": "kdmkjkqknb", "start_percent": 93.764, "width_percent": 0.373}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 289}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.078245, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:289", "source": "app/Services/FinancialRecordService.php:289", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=289", "ajax": false, "filename": "FinancialRecordService.php", "line": "289"}, "connection": "kdmkjkqknb", "start_percent": 94.137, "width_percent": 0.615}, {"sql": "select * from `financial_records` where `shift_id` = 45 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 295}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.08036, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:295", "source": "app/Services/FinancialRecordService.php:295", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=295", "ajax": false, "filename": "FinancialRecordService.php", "line": "295"}, "connection": "kdmkjkqknb", "start_percent": 94.752, "width_percent": 0.599}, {"sql": "select * from `financial_records` where (`id` = 45) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 306}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.081967, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:306", "source": "app/Services/FinancialRecordService.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=306", "ajax": false, "filename": "FinancialRecordService.php", "line": "306"}, "connection": "kdmkjkqknb", "start_percent": 95.351, "width_percent": 0.502}, {"sql": "update `financial_records` set `current_cash` = -1387, `total_cash` = 63, `financial_records`.`updated_at` = '2025-06-27 02:15:43' where `id` = 45", "type": "query", "params": [], "bindings": ["-1387", "63", "2025-06-27 02:15:43", "45"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 306}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.083472, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:306", "source": "app/Services/FinancialRecordService.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=306", "ajax": false, "filename": "FinancialRecordService.php", "line": "306"}, "connection": "kdmkjkqknb", "start_percent": 95.854, "width_percent": 3.709}, {"sql": "update `voucher_payments` set `status` = 'accepted', `voucher_payments`.`updated_at` = '2025-06-27 02:15:43' where `id` = '20'", "type": "query", "params": [], "bindings": ["accepted", "2025-06-27 02:15:43", "20"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 170}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0888312, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:170", "source": "app/Http/Controllers/PaymentVoucherController.php:170", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=170", "ajax": false, "filename": "PaymentVoucherController.php", "line": "170"}, "connection": "kdmkjkqknb", "start_percent": 99.563, "width_percent": 0.437}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}, "App\\Models\\PaymentVoucher": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPaymentVoucher.php&line=1", "ajax": false, "filename": "PaymentVoucher.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/20\"\n]", "success": "تمت الموافقه علي سند الصرف بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/payment-voucher/confirm", "status_code": "<pre class=sf-dump id=sf-dump-541776517 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-541776517\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-658802336 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-658802336\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1062573744 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1062573744\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1469803613 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">53</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; XSRF-TOKEN=eyJpdiI6IkRkdTdnUnlGUDgxYnA0NzBKQ0JCYWc9PSIsInZhbHVlIjoiL3N6aW5sSUdSS3ZHRWRkZ0RQVG9tOC9pbm9lajVpeGdaK2JIeThFT0d4TG1iYU9GVHNvZEF2eklhU0tkTEFJQ2Q2QmVPN3F5K3QrQ1lBUEZwdUtUa282Z0ptbERLU2I5SEowUlpDMHBXY0FVeklKT2VDdFJ5RExnZmE3WVBJL2pjdjYvc1ZGeHpBKzBuMHdDeHJTSFdGaXhGa0l5b1FlN2t2MUlxR3NibHU4Vy9jdjhlNVh5Q3JhZGM0ZWFrOThSVGkrVFFPK1JQZFJyNTI2ekRDOEJSVjVTZEE2c1hWNis0cHBGUXQ4Q0VSUklXVDd0cGdQL2VEeHUyeWgxTjJVR0pzSmhPcDZsbDNlVU5GUDlFOTVRY0I1Q2IxV1BsaWJYRktGU01wdTFhVFUrNmlsWE9ERHJuWS81ZmlZWGtTc1NwckF6K25Ld3A5T1hPMm54TmwxSGdCSG9teGJyUFo3NTlPL0YrY0o0S0hCa2JiajFKN2tXZS81TmVGTkJYQ3JlNWtVMEFYM3VYR0xsNGFXMm9Jbnk0TVJRbW9EUEhzUjl3MmRwMjlMcXVaTXFwbHpYcEp1ZEt2dlF1V0ZhWnd1NTV5NHZHMWptSmNEQzI3M1NaaGc3SUhNaUdQVUVpMHdKMnkwOEtZSkVEUmNwWDVaV2tWVEVUZW5HbGVoa3g2YU0iLCJtYWMiOiIwODc3MDlkYmUzZTIzYmM0YjVhY2UyZTM1MzVlY2E4OWQ0NmRjMDQ5MGExMWZlYjdjODU0NzNiMWE5MTJlNzA1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJBcUtYYUJSWjI3SzM2bEk5V2pLV1E9PSIsInZhbHVlIjoiZEdrbk9jT3BCOU9KK2VBL2U2VW95QkNqSXNsWEFYeElsWElhMmVxZEkzd1Y3eUNwSEIxVk94Tk1oTG9zZElnYkVLL3padms3QXZlY1BaeS9yM0dDRVpPNUhBdVpmeXpoY004MUQ4NTlWdldUaFJ1cHY1cFZ4SkhVYzFPSURCQW1TRUNHcjZSQ1F2V0NWMDZ4VU13WmhPY2xRMTFzN2JvUGhBcG5IZjErRkl1bDdYQm55K25YRGhNcW84NEsya0s2VlcwaGttVVY1Q1pNeUxwZ3IxNGNqMGRwcG5SeG9SdGZ6SU5tam5oQ3M5QVp6Vy83V1BTMG02Q2NxVnBaQklrc1lRT0tPMmczQ2dKOGhQVDY5K0FsWEJtdXlWVExhNkd5TDNFRzJpMVF6MGd1c25scnhJOGJ4aFhtRi9acGhuMk5qRDFuWHA5VzBCNFg0c2ZHZVdZNUlGb3prVzJxK0lIemkxbmk4WXRPTjhGRlRHNDI0OUV4V2FDb0F2cU5JRWs4OTVTUzRYZVErbi91eGNSOEVhTGdpY1B6WklxWDNJLy9KeEJCMWFLWk4rajZUVjhjVEpScWY5eE1jamV4Yk9TbE5XQ3VXSE1oc3lGY1VERkRYbXNCNkVtV054eG44VEI0N2NFOXRoWTdnSElmQXRjaUlTV3UwYU1OTUhzbVZ6REciLCJtYWMiOiI0MGFiOGNhYjU2MGZmYjlkYmIzYTVlNmI4OGZhMTE3N2UzNWI2NzRiYTY3NjJhZDExZjA1YTRkZDI0MTlkZWNlIiwidGFnIjoiIn0%3D; _clsk=1wa64lz%7C1750990541673%7C13%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469803613\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-269962120 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-269962120\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1639276448 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVlRCs3ZU5xR1Bod25RWnVscHpqa1E9PSIsInZhbHVlIjoiSlA1cjNKUUE5R0FsQVhNS3hzem12L1d1TmJtbC9Qc2ZhN1VTTkwzQWttbExZRWNoSFRQd2g4U0o1ZGZ1WTZ0SXcwT0pDM3hrUkpOZXdwTTBlT20yZ3BLMjNKTER6QUMrMUZNeHh5VVdHaGhaNURuOTduWUM0bUlJQnZQS0dmZlRPdGJjV2ZrLzZRT0M2ZURrMmkyTHdkZDdXMmxycks5aDBFZWdxQXdrOW00TXd2UC9OYjREb1VqVFFqT0NyTHV0MlF5SXE5VWlweGREQ3pHcFBRZWI1MG4xQ1NlTitQdkZYVi9pZ1FDRW56d2tEU0h2WUw2NTZ2NWx4S3FXZWZmZ1hHUVpaSGxFTG1ZbDZvYmRoVmdyZDNNdkdBSkcxRDUxZTdGajAxV1JqbUZremlGSWxNQzhlTTdKZ3ZTQUJyNll0QzFKUU95UVpDRVJPQWxWRDFDZUJuWGdpUmo3VWkreVdaSWNQUXA3eHRaaGFDQUtXMmJaUnJNdnFxOS9nOHV6VXFJdlVNM215a0RseWk4UFlpVEhQaU5FZW5LK2xleUFST2s5M2V5OXpjWVhaa2lhcWZQRHhYNkJEZFpQVTlZZERBNUFVSHBxY0w1czhEb3R5SW5EWWkzRTlhSjk4M1A1SDhMV21DeldJMGtoS09xRzdlMk9VRXB1N3MrTXZJaTciLCJtYWMiOiI5NDZmMzUyMTc3NmU3YTcyMjJjNzM1OWM2YmUxZDczMmIzODJhMjY2ZjgwZjc3NzQxMDRjYjUxZTM5NzdhM2I1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRwQmtGUk13ak9WOGt2N3BHY0cvd1E9PSIsInZhbHVlIjoiUXljbFRJSDY1cUQzck5VbjRrUjlpTTFoQWxRSXRRVWdNM0s4aFVnYWVjVnp3YU1ZTk1icjBwNUhlc1Y0Uzl6bExDMGljTWpneVJIQW5sYllKWU9ZcTQ3Z202UE9vSkNwOXFZZXgrQldtcVk0MWlsWldobVBKalAxQnNQcGx3YTNnenlyYURzeEdObWtQWEI3c3FSS1cvYTAvUXlNSVdBNzIva3puN1FjRFpudTVtMFlud3ZkYit4UGpTZUJKcmRSOHN2QzRXQ3BTNGpoZ2VlRDEycXdaN0NhTlp3bjQwYlY1V2NXc2RqcURIdjJOdmVORUR5Y0ZSMndnNE9NMjlmcXREMWV1R3ZiV2tGZ0dJQ0RZOVpSQXpyaDh6ZU11TGFlU0pScjhySzgvdGhJeWxrc3Z5bElPL0RGVnNrTXJob1p1MEViMGM2QlFQSHFXNmFScDFnWThUNFl3SmRPTWdiNTczOXJiVWdvMGhJSkJGUDVFZjlhbm9oMGxyeXlVa3hwbDRMUlFXZW9PNmt4WFNjb2tDMS9zMlRPb2gzSEh4eVVDU2lyMWg2ZVNRWFU3Y3ZBWjJ2TmNLa0ZtQTR6QVFLYTZUT0xVSmJiQmJ6dGhEL1k4am16R3F1d3VBNkRiYXZEZ0RyRlVmWEJ6NUxIaEtsU1NPZ1pSY2x2RUdXa28vdzEiLCJtYWMiOiJjZTU0YmNlNzYxYTQ4YmI1OWQ5NzQ5Mjc4ZmJiZmMxNTg4NDM5NDNmYjY2Zjg4ZDNmMTMwMDU2ZDc4OGMxMDdkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVlRCs3ZU5xR1Bod25RWnVscHpqa1E9PSIsInZhbHVlIjoiSlA1cjNKUUE5R0FsQVhNS3hzem12L1d1TmJtbC9Qc2ZhN1VTTkwzQWttbExZRWNoSFRQd2g4U0o1ZGZ1WTZ0SXcwT0pDM3hrUkpOZXdwTTBlT20yZ3BLMjNKTER6QUMrMUZNeHh5VVdHaGhaNURuOTduWUM0bUlJQnZQS0dmZlRPdGJjV2ZrLzZRT0M2ZURrMmkyTHdkZDdXMmxycks5aDBFZWdxQXdrOW00TXd2UC9OYjREb1VqVFFqT0NyTHV0MlF5SXE5VWlweGREQ3pHcFBRZWI1MG4xQ1NlTitQdkZYVi9pZ1FDRW56d2tEU0h2WUw2NTZ2NWx4S3FXZWZmZ1hHUVpaSGxFTG1ZbDZvYmRoVmdyZDNNdkdBSkcxRDUxZTdGajAxV1JqbUZremlGSWxNQzhlTTdKZ3ZTQUJyNll0QzFKUU95UVpDRVJPQWxWRDFDZUJuWGdpUmo3VWkreVdaSWNQUXA3eHRaaGFDQUtXMmJaUnJNdnFxOS9nOHV6VXFJdlVNM215a0RseWk4UFlpVEhQaU5FZW5LK2xleUFST2s5M2V5OXpjWVhaa2lhcWZQRHhYNkJEZFpQVTlZZERBNUFVSHBxY0w1czhEb3R5SW5EWWkzRTlhSjk4M1A1SDhMV21DeldJMGtoS09xRzdlMk9VRXB1N3MrTXZJaTciLCJtYWMiOiI5NDZmMzUyMTc3NmU3YTcyMjJjNzM1OWM2YmUxZDczMmIzODJhMjY2ZjgwZjc3NzQxMDRjYjUxZTM5NzdhM2I1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRwQmtGUk13ak9WOGt2N3BHY0cvd1E9PSIsInZhbHVlIjoiUXljbFRJSDY1cUQzck5VbjRrUjlpTTFoQWxRSXRRVWdNM0s4aFVnYWVjVnp3YU1ZTk1icjBwNUhlc1Y0Uzl6bExDMGljTWpneVJIQW5sYllKWU9ZcTQ3Z202UE9vSkNwOXFZZXgrQldtcVk0MWlsWldobVBKalAxQnNQcGx3YTNnenlyYURzeEdObWtQWEI3c3FSS1cvYTAvUXlNSVdBNzIva3puN1FjRFpudTVtMFlud3ZkYit4UGpTZUJKcmRSOHN2QzRXQ3BTNGpoZ2VlRDEycXdaN0NhTlp3bjQwYlY1V2NXc2RqcURIdjJOdmVORUR5Y0ZSMndnNE9NMjlmcXREMWV1R3ZiV2tGZ0dJQ0RZOVpSQXpyaDh6ZU11TGFlU0pScjhySzgvdGhJeWxrc3Z5bElPL0RGVnNrTXJob1p1MEViMGM2QlFQSHFXNmFScDFnWThUNFl3SmRPTWdiNTczOXJiVWdvMGhJSkJGUDVFZjlhbm9oMGxyeXlVa3hwbDRMUlFXZW9PNmt4WFNjb2tDMS9zMlRPb2gzSEh4eVVDU2lyMWg2ZVNRWFU3Y3ZBWjJ2TmNLa0ZtQTR6QVFLYTZUT0xVSmJiQmJ6dGhEL1k4am16R3F1d3VBNkRiYXZEZ0RyRlVmWEJ6NUxIaEtsU1NPZ1pSY2x2RUdXa28vdzEiLCJtYWMiOiJjZTU0YmNlNzYxYTQ4YmI1OWQ5NzQ5Mjc4ZmJiZmMxNTg4NDM5NDNmYjY2Zjg4ZDNmMTMwMDU2ZDc4OGMxMDdkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1639276448\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1514573136 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"32 characters\">&#1578;&#1605;&#1578; &#1575;&#1604;&#1605;&#1608;&#1575;&#1601;&#1602;&#1607; &#1593;&#1604;&#1610; &#1587;&#1606;&#1583; &#1575;&#1604;&#1589;&#1585;&#1601; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1514573136\", {\"maxDepth\":0})</script>\n"}}