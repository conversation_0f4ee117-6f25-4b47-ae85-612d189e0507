{"__meta": {"id": "X4789809774dd7a0818dca8ae040d45db", "datetime": "2025-06-27 00:47:52", "utime": **********.469136, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.03777, "end": **********.469153, "duration": 0.4313828945159912, "duration_str": "431ms", "measures": [{"label": "Booting", "start": **********.03777, "relative_start": 0, "end": **********.405115, "relative_end": **********.405115, "duration": 0.36734485626220703, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.405124, "relative_start": 0.3673539161682129, "end": **********.469155, "relative_end": 2.1457672119140625e-06, "duration": 0.06403112411499023, "duration_str": "64.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041768, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01463, "accumulated_duration_str": "14.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.433524, "duration": 0.01374, "duration_str": "13.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.917}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.455215, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.917, "width_percent": 2.734}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.4608731, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.651, "width_percent": 3.349}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1713247331 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1713247331\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-568882212 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-568882212\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1891652933 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891652933\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1785780285 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750985255040%7C71%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVob2dJcnJNSk4wM1dnckU0RWt2UGc9PSIsInZhbHVlIjoiM0o5R2gvT0hueThJaWlqSDlFdUUwMTZxVUpKV2pQS2oxOEoycFp1SDR6b1hzSDduL0pTY0p3b0M3RVc2V1NVekluY1h1Nnpla1kzSU1QT1cxRmRvaDVCWEUzb2kyZnpVTFVqNDNkWUpwY2h2ZDZydlplMFRONHRvbjNETVBGSERSVG0zYXcyWVpWNHpTQVRld0VhemJyaFJja3QzamtEenRuRE40MWcxSzJUbXA0aUdXejJBSGt6a2tTYkMxcktUTEZmRXRVYSt4Um9tY3prOWtBVGdpZ0tqbWJhcVFkYXZodkJNTmdaSTFwNlQ1S21abFN1Q29hYlpXNVR3ODdGRFNtMmU1a0lNREdEZlRHOFduR01rSE9BaEF1V0I3SHh5a3pqQjFRWXZabTRyS09hcUgrUWkvMGFDK0tJbHRlN3dId0J4N1NSbS9Fa281RHJYSUJ1cGhCZ3FRT2kraHFSUUhKRmZLaWdFaXl4Qjlkc2YxTGVHWDZ0Tms5aUUrUGlpRlNlUXp0TjQ1RUFrZUJEcms1d3ZGRHdDL0NDaGtsQVpHeEpBcy90TjF3Y3Q5di9NaWhhVmtFdWZKR3U1cGJqNTZKT01wVlpzaWlzd01xVStSM1NyazRBSTdtVUFUVytNOW5ncjN6QkU1c2FBaWxnc1E3UC95RlRXc2ZVNUZrSWwiLCJtYWMiOiJkNmI3ZjFlZTc4ZGEwNWIyNzk4OTVhZjEwZWJkYTI4NmE3NTg3ZDhjYjRiYmU2MTEzZDI5ZWY2YzljZDM0YzMzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFtdTZYVWgxVmM4aytlMHR5bWR2aUE9PSIsInZhbHVlIjoiSFd0VnloVjZER3QrRDRrd0lCOS9kSTIwazdPZld3dlVCOWpINWU3aUVqQTN0OWxEbTBlYWQxVWRSN2hmdXpXeGN6bkRlb0QrYnlFZnJzdmtYVnpwaUxTUmFQZXBLNkFXMmlDOGpZTzJvVDV3Z1F2dmFaUTZ4N2xzTGtLRVZlZEVNbnlPcFpyNHhURFA2czk0QW4zanhVclRZbmRPYVIwSGh2MmhjKzVuajVzZUh2ZnMrVXhCcXlnd2svN2pJa3AwRlE5WGNjZFdRRXBsTHBSdDRZdmV5MEZpaERyL0lQd0tvUWtmU3VtajBlaXJXUmEvZXhFOW05NmhpeGZoUG9jcDg3VkJzNG50eHR0NzFIZGNsZnlzTkg0YjZDMHlaenJwWHpORHRZZVk1bzE1TXFWNGkyNzJhNFYwek9lSlpNNkFsUGxQSDZlRGdoQklyUWdhUThMWUd6R1hyaEpRSkZuTG9uOE1jdHpVOUh3VmZHMzBnQ3JWRGxZZTRMVC9JY1hGOHJ6MDRxUVBqWWlTYUF4VXl4SHZsUGthc1dDQTFkeEQva2pvWU41dWlORTd2eFlwT2ZFZ2RYNTdsV0djeVMwb0FBa0RTL1ZaeVFKRnJHU1I2L2JmYXlobzlDYW1yZ2lNbXAycnMwL1ZralRnc3puQ0Q0dGtIUC9ERS8rRmVFK2wiLCJtYWMiOiJkZmZlNDRkMzUxNTY0MTQxZGUxYzI2ZDIyMjViODZmZDkwZWNiM2M0ZTE0ZmY5MWFlNTZjYmNjN2Q3NjcxNDM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1785780285\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-185362588 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-185362588\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2195694 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:47:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9CSmFlaURLV2RPWHQzVERKQ2VDZEE9PSIsInZhbHVlIjoibFFxQVRxblRjSVpPMFBLeWU4QlRJaVlQT055VjQ3Nk0xRUxxdmpSNEhlR1p5VnlMNHU4Ni9LRzJjSm8wejYwZloyT09MSVlPRmlpeVFyRzJWMnM4N21NL1pCVXVWdU5PQnNNTlJsNU5lTlA0dVNYNlNGQVBvRjJLVnlwaHlzYkVQODd0LzFDOFBjekFvUEo4c2dXT3ROOUEvbklLT0FTaDRTVmFUS1JIK1NQZXdoMENXSWNYUVkrWkVJS0ZOQXRZRmlGRU9YeHJLQW1OS1dQeVRQTjFXV2JYSXpucTFjbGRjUDlJNFJLU1RIT0pVNnJQWXVkR0hqT0xRRnJsajQ2c1NKZWRYWVEwKzI4cmNxa0RkUXhWUnl6bDlCZlJyMGwwZExyYVdyeXd0TU1WNkpCU1Z4clkvRnZ6bEh0eURTTkFUdDVRN2ZtbEQ2NU5TaDBoS21NR0d2WGtRQTJxWTZaUjIyL3duWHpzVHZOOWlEdGhLV3RaMGRkdDh3VVNDa3Z4WmxwSmNZYlV5Zy9rMnc4NTJwMWRQbGhsemptZE5aNlZNTWRITlhvZ1dMMGxWNWFPaFlOMlU2QnZUYWhLWUkwWFJxTnlSdFVPd2NUNFFjUitIaXBpb3gwWUNrMHcwUmhtMzh0L3pzZU81NDlRNXVwR3FsUVg0VUZnd0NoQjRiWVoiLCJtYWMiOiJlZDcyMmMxYzg1OWUxZTEyYTVlNGVmZDMwZWIwNjEzNjdhYTgwN2Q5YTgzNDAwMjFjMmU1OWIzYmRiODdiMGM1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:47:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZlZmZuMGs5R3VRdUZRUmdMNzZWL3c9PSIsInZhbHVlIjoib21oTWZEWkFKeEg1RWhNWGRuaVVEZmdpb1JKOWxwODUxZWs5ZjVwMmlDMzhrKzNpYUpXWFhqT21NK0k0Q0VvUmZkeEtBek5oM0JMc2EzbFZVN01EaUM4alFBcEQvbjUvL3lXWVZyWXJ1VUdaK0RtbW9CbjR3TUE3Z01VOW4xdFBCamNmbHBSMWtQOHl3K0M2SHpaVGszR3Zlb1YzYllQYklBK3RwUHByNC9jc2ozb2pTK1pTR2NZN1BMSzhKZjQ2bS85aldtSmIyRnlYRnpFOGRWUTJ5M2kvRngxZUZ5bGZmRnBYV0tRUWhoVWIzYUJEMkpoc29zU2N5V0lISnlKUUVPMjl6QUVyUkpLaXdPcnFRM1U3SlBGL2pYQURTTnRSYkhkb2RBT21VeCtwMW90MjRxcHBZblhSOFZuVDVtbXJCRkRpb3haaG1tTjFld1g4SDJiRExuR2dETFVyZThxV2o3MEIxcXhFSHN4MklKTkQvZjZHNUZWYnAxZXAzYWJjRG44Wkw1SnF6enVuYlhLcys4ZDcvRmIvdXFmK0NzcW4rUy9paW9qbnJQd3JLSjlXQk8zT0w2WTIvVzFtVUhCMGVIckd1MmdpcEtNcDdLL0x3RDlpamVwYjdaY2dtZzd4Yk9zOXNFRjZ1V29od21pWGNycHZMeWkrM2JZOUZ3VDkiLCJtYWMiOiI0ZWZhMjRkYjExYjBlNjZmNzYwMDc0OWM3YWQ2NDkzNWJlM2NjYjA3NjNkYTE4Zjg3MTFkODQ4YzI1NjcxOTgyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:47:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9CSmFlaURLV2RPWHQzVERKQ2VDZEE9PSIsInZhbHVlIjoibFFxQVRxblRjSVpPMFBLeWU4QlRJaVlQT055VjQ3Nk0xRUxxdmpSNEhlR1p5VnlMNHU4Ni9LRzJjSm8wejYwZloyT09MSVlPRmlpeVFyRzJWMnM4N21NL1pCVXVWdU5PQnNNTlJsNU5lTlA0dVNYNlNGQVBvRjJLVnlwaHlzYkVQODd0LzFDOFBjekFvUEo4c2dXT3ROOUEvbklLT0FTaDRTVmFUS1JIK1NQZXdoMENXSWNYUVkrWkVJS0ZOQXRZRmlGRU9YeHJLQW1OS1dQeVRQTjFXV2JYSXpucTFjbGRjUDlJNFJLU1RIT0pVNnJQWXVkR0hqT0xRRnJsajQ2c1NKZWRYWVEwKzI4cmNxa0RkUXhWUnl6bDlCZlJyMGwwZExyYVdyeXd0TU1WNkpCU1Z4clkvRnZ6bEh0eURTTkFUdDVRN2ZtbEQ2NU5TaDBoS21NR0d2WGtRQTJxWTZaUjIyL3duWHpzVHZOOWlEdGhLV3RaMGRkdDh3VVNDa3Z4WmxwSmNZYlV5Zy9rMnc4NTJwMWRQbGhsemptZE5aNlZNTWRITlhvZ1dMMGxWNWFPaFlOMlU2QnZUYWhLWUkwWFJxTnlSdFVPd2NUNFFjUitIaXBpb3gwWUNrMHcwUmhtMzh0L3pzZU81NDlRNXVwR3FsUVg0VUZnd0NoQjRiWVoiLCJtYWMiOiJlZDcyMmMxYzg1OWUxZTEyYTVlNGVmZDMwZWIwNjEzNjdhYTgwN2Q5YTgzNDAwMjFjMmU1OWIzYmRiODdiMGM1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:47:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZlZmZuMGs5R3VRdUZRUmdMNzZWL3c9PSIsInZhbHVlIjoib21oTWZEWkFKeEg1RWhNWGRuaVVEZmdpb1JKOWxwODUxZWs5ZjVwMmlDMzhrKzNpYUpXWFhqT21NK0k0Q0VvUmZkeEtBek5oM0JMc2EzbFZVN01EaUM4alFBcEQvbjUvL3lXWVZyWXJ1VUdaK0RtbW9CbjR3TUE3Z01VOW4xdFBCamNmbHBSMWtQOHl3K0M2SHpaVGszR3Zlb1YzYllQYklBK3RwUHByNC9jc2ozb2pTK1pTR2NZN1BMSzhKZjQ2bS85aldtSmIyRnlYRnpFOGRWUTJ5M2kvRngxZUZ5bGZmRnBYV0tRUWhoVWIzYUJEMkpoc29zU2N5V0lISnlKUUVPMjl6QUVyUkpLaXdPcnFRM1U3SlBGL2pYQURTTnRSYkhkb2RBT21VeCtwMW90MjRxcHBZblhSOFZuVDVtbXJCRkRpb3haaG1tTjFld1g4SDJiRExuR2dETFVyZThxV2o3MEIxcXhFSHN4MklKTkQvZjZHNUZWYnAxZXAzYWJjRG44Wkw1SnF6enVuYlhLcys4ZDcvRmIvdXFmK0NzcW4rUy9paW9qbnJQd3JLSjlXQk8zT0w2WTIvVzFtVUhCMGVIckd1MmdpcEtNcDdLL0x3RDlpamVwYjdaY2dtZzd4Yk9zOXNFRjZ1V29od21pWGNycHZMeWkrM2JZOUZ3VDkiLCJtYWMiOiI0ZWZhMjRkYjExYjBlNjZmNzYwMDc0OWM3YWQ2NDkzNWJlM2NjYjA3NjNkYTE4Zjg3MTFkODQ4YzI1NjcxOTgyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:47:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2195694\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-317321144 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317321144\", {\"maxDepth\":0})</script>\n"}}