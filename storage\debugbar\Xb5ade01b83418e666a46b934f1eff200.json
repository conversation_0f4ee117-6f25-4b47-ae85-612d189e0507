{"__meta": {"id": "Xb5ade01b83418e666a46b934f1eff200", "datetime": "2025-06-27 00:14:48", "utime": **********.351288, "method": "GET", "uri": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750983287.889968, "end": **********.351302, "duration": 0.4613339900970459, "duration_str": "461ms", "measures": [{"label": "Booting", "start": 1750983287.889968, "relative_start": 0, "end": **********.26412, "relative_end": **********.26412, "duration": 0.37415218353271484, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.264132, "relative_start": 0.3741641044616699, "end": **********.351304, "relative_end": 2.1457672119140625e-06, "duration": 0.08717203140258789, "duration_str": "87.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47521888, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.show", "controller": "App\\Http\\Controllers\\BillController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=333\" onclick=\"\">app/Http/Controllers/BillController.php:333-393</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.01727, "accumulated_duration_str": "17.27ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2951071, "duration": 0.01537, "duration_str": "15.37ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 88.998}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3184211, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 88.998, "width_percent": 2.548}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3259299, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 91.546, "width_percent": 3.185}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.339979, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 94.731, "width_percent": 3.301}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.34196, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.031, "width_percent": 1.969}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => show bill, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1547354545 data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1547354545\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.34532, "xdebug_link": null}]}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-711842558 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-711842558\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1908989887 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1908989887\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1917283299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1917283299\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1810233446 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983282490%7C45%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVEb2lzNW5lcXNwUFFhcnM0ZjVxV2c9PSIsInZhbHVlIjoiM0t5K2hPSGFnbkY0VjQ5Q1BjcWpITm5Iclpqbmw0bWNIdFZSdUE2WHlXK2dCcDBtSlp2emhHQmdlcTMxeEFicnVTRWtUUTdER3pScnZtaGdxWW1mQ3lZelkzT1NocHVDWEZWS096clBrV25UL0VUaDhqWnBnSzZHaHU4amNtV1hoR3l5Zy82Q3lWSFlqcXRldi9rYXZ6TDU5UTFlMlYzTzBZRHl2Sk91bnFiZU9OLysrNEtlMEVxQ0FqYVdvNnFtRzNBbndJUFpvcFJxdXJTNWtWTkhxVGtTeHZIYUNCRGZuYTFXdUVvbnR3bVdzN3JiSVZqS3A3Ym5LQWVKU2ppN3pQMmNBcmxheHV3NTJlakRacURIMmJKMmtUb1lNRm02Y25EV21yWjBYTmpJek8wdHU4eUk0UUpOd1VYUmpDeVZXVnFhaUdQT25OeDFTejU0azJpcDMxNVBtQlBJN0FkVUhhclE4WitYdDV3dmVGQmcvK2IxOHdFa2k4cHU2aURuWjBqU0hLTStXdzg2WTByZ1FXc0RSWnhPTlE2aXlHeFdIM0pVWDVpMGpwM090TGNyR0Z1NXlIeE1EdCtTSkg1NTF5WmlRS0E0WkhBenNwdjRzOTVUK0FjaXNZdTV0VFZibXRRLy81enhCZzFEQzVoK2tHU1FTc0Rvc2YwVHV0NWgiLCJtYWMiOiJhODk0MDU1M2MzOWEyMTgyOTZhOWU0NDYyYmQyMzVjNTBmOWE3ZDhkYzJhNmU4ZTc2NDY4MDNhZDRlNzVhMGQzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhZNVRSVmFSeUFNNHdEZzZTalpMZFE9PSIsInZhbHVlIjoiQW5pVXZZNHdRRzRmYXRZRWR1cmFFZlFiR2IybnZPaGMrdUtTL29rSlBiRExCQ1IzTUs1bTF6WGdPdHFhb0RFc2psLzM0QUpaL2oyTkJlMnVTZkhJVGFuYU0zUmdQTE10MXF6eXVUUXR2MGJKS2V3TGdESWFqMDdOWDQrRjU3VG14U0JqZ0ZiZXpDSkcxbkdoajJ4UnpBK3A1Q05sQlB3Q1JiVW9WTi9iTFRmb1BuRjBINmhDUDFKbFh3RXZOVkJ3QWRmTlNyTFBjdU9FU0xvblM2MUpFejhBK0tiUDJ6SExmaXVUUDBmTmlJUGYxNTFDeGpLVEo2dG9lVGlZZ3RnOEg3N25lU0U4aUxCd1hvT3lKWndEeVBoWG81clZnMzBqTUFpbGVXSjh1UDJLTFliQk5MMGV6T3FIUFAyMkpJeHdvQnlQaTNnOC9XaURrVW1IbUZpWFNMcCtYTXcrT0R4UlVGLzRaYWV2T0pmUlI4RCtUZHA3ZHptQ1VrWWoxYU9FRkpVWThEd0hpVmhYT0tnOURwSnUxUXAzYVlyeTRyYm5jR05CMVMvZW9pVnVDeDEzbng4OXVrTTNwNVZWY1FER1NRcGpVdG1rSU1HUElvR3pEUnVNSTZkU1gxYnlzOWVPN254SmoybXozeGhyQnJxK2FTQ0VsN2g5aXF2WWF2dzAiLCJtYWMiOiI4MjMyMmIzMDYxNWJhNWMyZTdhM2ZjN2RhZjZmZTM5N2M4YjMxNTFmNDE3MmYwNTI0NWJlMTE4NmJhYzBmYTc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810233446\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-853707892 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-853707892\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-11001207 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InV3V3d1Zlh4NkoxQXZYZFZudzlPOGc9PSIsInZhbHVlIjoiTVI5bXlZa2ZiR1ltMHJNS1JsNFNLMWZ5UlBXaTI4N2dpZVhkWkdDM1A4bXNnZUUvS0t5eXZGMmg4cm1Tb0FCeGcrb2R3OWEwcE56dGtCUU81bXUvTUJMeDVNQ2lpZ2o3VjV1VllLK1FsNHEydDNrb1I3YUtSU0hnV3l3Unc2TlFDWXFsVHowSzFEWFo4a1FmSjJlWEFQR3pCY2ZoSGROTUpKSFg4VVR3alNzbmh5akZGNzVSNndUSHdhMTJsWElIOEdIMEhCbWIyL2N0UnFQcGdudXZiR2U5YkQ1bTBYUFZiY2RYb0RKRTYxcmJWclJyY0oza3VWNmVBWEc0VGYxZU8zeExKUnVjY0x5ZEFMSWxyM1BsSU9SK3FQd3BRWTk3dFBIRG10ZTBHWW0zNHNidDJxRjZ1UUFVLzVCaGxjdS9TMFZhQnI2b05hTk9UbThSREw1SFhMMGRaUXdhOFk5SC95bG9uOXFYOHZ0TU1LdGxHVnNFTmY0V3JjdFk0RHpKTzFXclF2UHdLdytlbC9Tcnl3azlyajg4ajVQSXBVZVB4dGIrRlhjSGlDMFF1Mm54bmRCRUdIOVh4alo1a2NZeWhrS1dKUk1lS2lXMjdTbWtGNDhQd1FOT1FZTFBGZHU2VHkyS2g1Y1hVMEFhVkRDL2hLZWhxdHJSVlp6VmxxOW4iLCJtYWMiOiIwMGI1NzE0NDM3MzkyNjg3ZmEwODdjNTEzZDZmY2EwNDYxMTUxMjJjYjQxMWE1M2JhN2YxZDY0OGIzZWQ1MDcwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJqbzVUSm5Nc2VYQlhHWFdpaDdqQUE9PSIsInZhbHVlIjoiQTR0K2tselpVWW1mY1o2R1IwdjhPRFd6MVhOaTMwaHc2Q0U1ajViK3FYTUcvL09ybEFBREJLTk51N2MvMEZGdi94N05mUlJMRDZkbWdwK2NTejM2Sk94LzVRUmJtZG40MFRTMkQyelhrcDl0U1F3cnYzQ1R0RkorcTgzeVorbFhTQ2ZRTG5zVUxaYzhsMktZUTBvb2JPK2JSd2tTQ3lxOWFWblRpQ2Y0dXpCd2NoTkZOZUtBc3Y5M0ZUNkRqY1FmNXZ5TE9wL3ZlRzdKTmZUWkRUK09uN1QxQ2g2ME96cXZ1MGY0aERKd055Q3I0dFBpM3dOb3RqNVNHdEEwSzB3cllvNm5KdHZmeTcybzRDNkljVVIrSk8zeGF6YUVScUlxbFVOOWFBMGg3MGFrZUtCLys4QzdjaDlHZVdVWFpCNG5xTlJrQklhSXpDMG5SSUZnWjNZWU5Nc3BicXN3UGRHQVA0U2VabnlTZzhlYk44aVpTRTkzOG5oRkI1WUpwU2ppZExxaDhrRk8wOElja1E0U3ZtSmQ5UFRYbmZMNUtYNm9PcUlBM3NGeUZsc29yUGFKZTErcWc4M3JFd3paT2pLRkNzVHRxWFRpeVhWc3R1eTduR0k0VFNvYXFTeWFoeXNJUXB2Zll2NkpCK0dyeDNBOWtQRDVscWtkMzVzUnJYWFIiLCJtYWMiOiJhYTkxZDQyNmI3ODI4YzdjY2JhMjdkODE2YzdlNTRiOTQwZGUxODRjY2JhZGZiMDAwYTFlMDEzNDZjNmMwNDZhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InV3V3d1Zlh4NkoxQXZYZFZudzlPOGc9PSIsInZhbHVlIjoiTVI5bXlZa2ZiR1ltMHJNS1JsNFNLMWZ5UlBXaTI4N2dpZVhkWkdDM1A4bXNnZUUvS0t5eXZGMmg4cm1Tb0FCeGcrb2R3OWEwcE56dGtCUU81bXUvTUJMeDVNQ2lpZ2o3VjV1VllLK1FsNHEydDNrb1I3YUtSU0hnV3l3Unc2TlFDWXFsVHowSzFEWFo4a1FmSjJlWEFQR3pCY2ZoSGROTUpKSFg4VVR3alNzbmh5akZGNzVSNndUSHdhMTJsWElIOEdIMEhCbWIyL2N0UnFQcGdudXZiR2U5YkQ1bTBYUFZiY2RYb0RKRTYxcmJWclJyY0oza3VWNmVBWEc0VGYxZU8zeExKUnVjY0x5ZEFMSWxyM1BsSU9SK3FQd3BRWTk3dFBIRG10ZTBHWW0zNHNidDJxRjZ1UUFVLzVCaGxjdS9TMFZhQnI2b05hTk9UbThSREw1SFhMMGRaUXdhOFk5SC95bG9uOXFYOHZ0TU1LdGxHVnNFTmY0V3JjdFk0RHpKTzFXclF2UHdLdytlbC9Tcnl3azlyajg4ajVQSXBVZVB4dGIrRlhjSGlDMFF1Mm54bmRCRUdIOVh4alo1a2NZeWhrS1dKUk1lS2lXMjdTbWtGNDhQd1FOT1FZTFBGZHU2VHkyS2g1Y1hVMEFhVkRDL2hLZWhxdHJSVlp6VmxxOW4iLCJtYWMiOiIwMGI1NzE0NDM3MzkyNjg3ZmEwODdjNTEzZDZmY2EwNDYxMTUxMjJjYjQxMWE1M2JhN2YxZDY0OGIzZWQ1MDcwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJqbzVUSm5Nc2VYQlhHWFdpaDdqQUE9PSIsInZhbHVlIjoiQTR0K2tselpVWW1mY1o2R1IwdjhPRFd6MVhOaTMwaHc2Q0U1ajViK3FYTUcvL09ybEFBREJLTk51N2MvMEZGdi94N05mUlJMRDZkbWdwK2NTejM2Sk94LzVRUmJtZG40MFRTMkQyelhrcDl0U1F3cnYzQ1R0RkorcTgzeVorbFhTQ2ZRTG5zVUxaYzhsMktZUTBvb2JPK2JSd2tTQ3lxOWFWblRpQ2Y0dXpCd2NoTkZOZUtBc3Y5M0ZUNkRqY1FmNXZ5TE9wL3ZlRzdKTmZUWkRUK09uN1QxQ2g2ME96cXZ1MGY0aERKd055Q3I0dFBpM3dOb3RqNVNHdEEwSzB3cllvNm5KdHZmeTcybzRDNkljVVIrSk8zeGF6YUVScUlxbFVOOWFBMGg3MGFrZUtCLys4QzdjaDlHZVdVWFpCNG5xTlJrQklhSXpDMG5SSUZnWjNZWU5Nc3BicXN3UGRHQVA0U2VabnlTZzhlYk44aVpTRTkzOG5oRkI1WUpwU2ppZExxaDhrRk8wOElja1E0U3ZtSmQ5UFRYbmZMNUtYNm9PcUlBM3NGeUZsc29yUGFKZTErcWc4M3JFd3paT2pLRkNzVHRxWFRpeVhWc3R1eTduR0k0VFNvYXFTeWFoeXNJUXB2Zll2NkpCK0dyeDNBOWtQRDVscWtkMzVzUnJYWFIiLCJtYWMiOiJhYTkxZDQyNmI3ODI4YzdjY2JhMjdkODE2YzdlNTRiOTQwZGUxODRjY2JhZGZiMDAwYTFlMDEzNDZjNmMwNDZhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11001207\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-918055774 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-918055774\", {\"maxDepth\":0})</script>\n"}}