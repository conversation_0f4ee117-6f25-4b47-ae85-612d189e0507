{"__meta": {"id": "X7a8123f7e0424174aa24d115864f46ea", "datetime": "2025-06-27 01:13:47", "utime": **********.401175, "method": "GET", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.007428, "end": **********.40119, "duration": 0.39376211166381836, "duration_str": "394ms", "measures": [{"label": "Booting", "start": **********.007428, "relative_start": 0, "end": **********.327398, "relative_end": **********.327398, "duration": 0.31997013092041016, "duration_str": "320ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.327407, "relative_start": 0.3199789524078369, "end": **********.401191, "relative_end": 9.5367431640625e-07, "duration": 0.07378411293029785, "duration_str": "73.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47586144, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@index", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=33\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:33-84</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00347, "accumulated_duration_str": "3.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.360589, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 44.092}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.3700619, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 44.092, "width_percent": 11.239}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 9 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.3733678, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:37", "source": "app/Http/Controllers/FinancialRecordController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=37", "ajax": false, "filename": "FinancialRecordController.php", "line": "37"}, "connection": "kdmkjkqknb", "start_percent": 55.331, "width_percent": 12.392}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 23 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["23", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.386161, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 67.723, "width_percent": 18.444}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (23) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.388052, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.167, "width_percent": 13.833}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 5, "messages": [{"message": "[ability => manage pos, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1863125650 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863125650\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.391891, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1453686402 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1453686402\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.393011, "xdebug_link": null}, {"message": "[ability => manage pos, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1495334359 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495334359\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.393772, "xdebug_link": null}, {"message": "[ability => manage pos, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1284974246 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284974246\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.394551, "xdebug_link": null}, {"message": "[ability => manage delevery, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1112579264 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1112579264\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.395483, "xdebug_link": null}]}, "session": {"_token": "W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "23", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-943976111 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-943976111\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-2147290246 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2147290246\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1399405760 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1399405760\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1943922167 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=tu6zsp%7C2%7Cfx4%7C0%7C2004; _clsk=1ggm1jz%7C1750986817753%7C3%7C1%7Ci.clarity.ms%2Fcollect; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D; quickly24erp_session=eyJpdiI6IkhGTy9KRmp5dDM1TFJuTDllNUVyR3c9PSIsInZhbHVlIjoiaWd1ZFAwckw3a01Mczdqb2l5MzRQK2hPRlYxdHI1eVRNM2h4U2NLZERzb3VxTHpQQ2RXZkJwRlN4VDVsQUtJeFNNN3RXZmFHanlHTXcwVTluOW5JYjBoRXhQZHRHQ0VqK0w5dGhsN29ZZ2dudzlGbDJ6TUdLUFhlamFibHVTekJjOXBVM0NJL2NzZ1IyODRweWh6K1FrUzNFT3ZiWVZGbkgwWlV6Z2E4aUxZVzg5WFl1YnFFRGxKNkN4dmxkeWw2b3RWUFpoQjZ4RytQaGdpNGhvanJpS1c0Y216S01mMlYwNTg4d21IYjU4RkZRK3hwN00rSHF5NmVCZ0FnQ0Z5bHI5c1g2NTFhdkNac0kwdmY2dENMVEVFdGxWWUl2QmlFT1dCaUM3ZG16T1FmT0R2TlNTUy9YSCsyUFM5bzV3clJmY2lROEhWM3dVbElldXJiSWt4MG1uWXV5dHBpL0xMdVJYTDhTTnpsMk40Mkd0Sis5QXMxUTlEOXR4dGNFelA5SEo2bUJhSklDRUEzQWM5Y29kUGZqUENBdjRtaWhNZFhZYW5tZ1M1akpEbVFRT3BzdHhSUXk5eVpoUVl6eUp4SUZGRmI2MTdybk00RTZPYTZyWHY4dW5FQzY0NjB6YUc1QkxERm82eWhTR2g5UXVNZWIreWhhNXAreFY4Wm1yOHMiLCJtYWMiOiJiZjQ5NTdhYTE2MGUyMTdhMjM2ODBlNGI3MDcxZmZlYzUyNmUxMmRhZjc3MmU4MGQxYWNlM2VlMmE1N2RkOTc3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1943922167\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1833096871 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QVqhpWieKZCLT3nmskTMtatIDNwBdc2egPwBt6XG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1833096871\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:13:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZDNjBudXJ3ai9SMjZ2WVpaZjJqVHc9PSIsInZhbHVlIjoiSWgySDdUVWVvZmVCd3R2Y2loN0JsOFVuZDJ1RFlHSHhDWmxRQytYUmdnNjludXdxaGo5RWpDayt3Tm9ZQm8rTTJ4U1A0ZzRJdDFKR1MvYlI0RU1tZWc0OEM4cmZHT25GWVVKYXl6L1RncWJjcGZGRURWQmFRMU5CWFhUZGprNGNha1cvY0pPUDQwMWhFSm1HbmJDYVVwWWl3bmE2VWRYTjdEK1Q3YlozREd3Z1UwZ1ZDekxHN0NPWm9oL3ZoUEE1dkJJdjJQYWJYWUZ1S3lDL3luWWMrR1FiOUh0SHZiZVQxY2lZRFhVTjg2R0k1S21MbS9qdTM2QklIWkFRcVh2NENxcGQraENhTXdSYWoyRFB3SEhEYkk1TWp3bmVTbFp0ck1haXdzNDZXd2tVeDdZMGxvdjVIeU5XWGo5dVB2ZHVKdm5UYm5zdW5KS3VQN2Fqa1JQWXB5Yk9kVkl2UFV4R002eFNvZEk1YW5jdjBobnZLdmdrdFZPREFqZDRtQm5HWXF3TS9jcFhZQXVuSmVDdU9sZVplbENpQitWQXpSVlZ0aEVYSzdMRUtpamRYb3lTYk1pK3cxbFM3RW1GdjZEc0EzTHZVM0tKanZhaGtoL1pCekJsZHFjQmFseWRWdmtrUjRTYXNlTjNodng5blRqL2NERlVJTmhBM1JKZWg3RXciLCJtYWMiOiIwYmM3Mjg1M2EzNDBmYzdmZTI5MDNhMzFkYWFiNGY0YjJiNjZjMDYxYzg1OWNjZjE2MWVkNGY5NmYwYmI2NmM5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Iit5dXhTbU1WeUNYVmtzNzZ1S1BZclE9PSIsInZhbHVlIjoibVlJeEZ2c1JTZTlnaTVOQkRRME93bldzbXU0RGt5czhPa0Y0czFLNmY2V1pLa0NFdnJBSUx0SzlHU1MvT09RdVlUQ3JCQVlYNXAxN0F5R0hrK2F5dW1lb2kxa2xQWk1Wa0FwUXc3aVVRM013THJjcmhyeFVuV29TU0J4MFF5SmUrZGpOSHFXZmRoZ0Fpa0hOMXJDZ0lyNmM0b3A4cmFwRHlwT1d6YWtFZ0NiQjg4ZHRJUktUZHJKd2xxRTAxLzI5OWxIMU9HVW9pNTNrbTUxSXlaVXpKcnE5VlNUUzdySnNhUU1NK0VqaG5GTmNYVThsTkhuNTJzQVhGVGg4dFpVRDVweWdBQTBwZk5GVEt4K0dMUWpZM1YzeWdzMU9vUElWSUNHMkt5QWdvSVY3WThidjZnNlNBaEt0OFExbFp0azNIYmV4NXlFdnVjRldSQjZtTnlwOGJ0WDJ2YzhTdStmWVNRZDdBcWdJSTVNNTF4ZWl2RXFRNVRNbGJBcHc5Um8vaGJGbmJmcXVOQmk3MTdkN05vQlorUWxMMElGMmFpaWdtRVF0T0tLbGh6NTRQSHBPcnJZbjZVRnNic0ZXb3JmNTNnalNPNEloOUFncUpnWGJsZnhOK0xwTnUwK2RnR0VSMHZtcjJocFRrOWJtQSt6aWZkekRpdmtXeHhyK1YycWIiLCJtYWMiOiJlNTcxNTUzZTRhM2QyNmNlNzMwZGVlZjk1OGVjMTliMjNkZTRjNWZhNmUyODg4NmYwYTkxNmZjYzZhNWZiZmE2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZDNjBudXJ3ai9SMjZ2WVpaZjJqVHc9PSIsInZhbHVlIjoiSWgySDdUVWVvZmVCd3R2Y2loN0JsOFVuZDJ1RFlHSHhDWmxRQytYUmdnNjludXdxaGo5RWpDayt3Tm9ZQm8rTTJ4U1A0ZzRJdDFKR1MvYlI0RU1tZWc0OEM4cmZHT25GWVVKYXl6L1RncWJjcGZGRURWQmFRMU5CWFhUZGprNGNha1cvY0pPUDQwMWhFSm1HbmJDYVVwWWl3bmE2VWRYTjdEK1Q3YlozREd3Z1UwZ1ZDekxHN0NPWm9oL3ZoUEE1dkJJdjJQYWJYWUZ1S3lDL3luWWMrR1FiOUh0SHZiZVQxY2lZRFhVTjg2R0k1S21MbS9qdTM2QklIWkFRcVh2NENxcGQraENhTXdSYWoyRFB3SEhEYkk1TWp3bmVTbFp0ck1haXdzNDZXd2tVeDdZMGxvdjVIeU5XWGo5dVB2ZHVKdm5UYm5zdW5KS3VQN2Fqa1JQWXB5Yk9kVkl2UFV4R002eFNvZEk1YW5jdjBobnZLdmdrdFZPREFqZDRtQm5HWXF3TS9jcFhZQXVuSmVDdU9sZVplbENpQitWQXpSVlZ0aEVYSzdMRUtpamRYb3lTYk1pK3cxbFM3RW1GdjZEc0EzTHZVM0tKanZhaGtoL1pCekJsZHFjQmFseWRWdmtrUjRTYXNlTjNodng5blRqL2NERlVJTmhBM1JKZWg3RXciLCJtYWMiOiIwYmM3Mjg1M2EzNDBmYzdmZTI5MDNhMzFkYWFiNGY0YjJiNjZjMDYxYzg1OWNjZjE2MWVkNGY5NmYwYmI2NmM5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Iit5dXhTbU1WeUNYVmtzNzZ1S1BZclE9PSIsInZhbHVlIjoibVlJeEZ2c1JTZTlnaTVOQkRRME93bldzbXU0RGt5czhPa0Y0czFLNmY2V1pLa0NFdnJBSUx0SzlHU1MvT09RdVlUQ3JCQVlYNXAxN0F5R0hrK2F5dW1lb2kxa2xQWk1Wa0FwUXc3aVVRM013THJjcmhyeFVuV29TU0J4MFF5SmUrZGpOSHFXZmRoZ0Fpa0hOMXJDZ0lyNmM0b3A4cmFwRHlwT1d6YWtFZ0NiQjg4ZHRJUktUZHJKd2xxRTAxLzI5OWxIMU9HVW9pNTNrbTUxSXlaVXpKcnE5VlNUUzdySnNhUU1NK0VqaG5GTmNYVThsTkhuNTJzQVhGVGg4dFpVRDVweWdBQTBwZk5GVEt4K0dMUWpZM1YzeWdzMU9vUElWSUNHMkt5QWdvSVY3WThidjZnNlNBaEt0OFExbFp0azNIYmV4NXlFdnVjRldSQjZtTnlwOGJ0WDJ2YzhTdStmWVNRZDdBcWdJSTVNNTF4ZWl2RXFRNVRNbGJBcHc5Um8vaGJGbmJmcXVOQmk3MTdkN05vQlorUWxMMElGMmFpaWdtRVF0T0tLbGh6NTRQSHBPcnJZbjZVRnNic0ZXb3JmNTNnalNPNEloOUFncUpnWGJsZnhOK0xwTnUwK2RnR0VSMHZtcjJocFRrOWJtQSt6aWZkekRpdmtXeHhyK1YycWIiLCJtYWMiOiJlNTcxNTUzZTRhM2QyNmNlNzMwZGVlZjk1OGVjMTliMjNkZTRjNWZhNmUyODg4NmYwYTkxNmZjYzZhNWZiZmE2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}