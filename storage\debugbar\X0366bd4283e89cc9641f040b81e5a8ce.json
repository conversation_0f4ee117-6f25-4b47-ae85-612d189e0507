{"__meta": {"id": "X0366bd4283e89cc9641f040b81e5a8ce", "datetime": "2025-06-27 00:14:43", "utime": **********.846702, "method": "GET", "uri": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.349517, "end": **********.846723, "duration": 0.4972059726715088, "duration_str": "497ms", "measures": [{"label": "Booting", "start": **********.349517, "relative_start": 0, "end": **********.760286, "relative_end": **********.760286, "duration": 0.410768985748291, "duration_str": "411ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.760298, "relative_start": 0.4107809066772461, "end": **********.846726, "relative_end": 2.86102294921875e-06, "duration": 0.08642792701721191, "duration_str": "86.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47521888, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.show", "controller": "App\\Http\\Controllers\\BillController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=333\" onclick=\"\">app/Http/Controllers/BillController.php:333-393</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.004569999999999999, "accumulated_duration_str": "4.57ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.795714, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 51.641}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8076441, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 51.641, "width_percent": 10.503}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.814703, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 62.144, "width_percent": 13.786}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.829316, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 75.93, "width_percent": 13.129}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.8314931, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.059, "width_percent": 10.941}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => show bill, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-994560347 data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994560347\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.835374, "xdebug_link": null}]}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-1406999130 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1406999130\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1613810416 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1613810416\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1905066155 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1905066155\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1219213961 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; XSRF-TOKEN=eyJpdiI6IkEwVEgxaGphNERCMDVDc2VoV1ZPZ3c9PSIsInZhbHVlIjoiS210eTFWZHAramxSbFlFWGZrdVZ0MnE5cU0wRy9xWExseUtnMlMwT3NhUEptSFpxb2pyUDdTWW1CVGlQS3Q3SDJscm1pbUJoYlNxVjRGZmlsK3U0S0ZsVG1mQW1nNzY3ckJxeldSOXgvcEdhbUd5UXI4UGRVeERtbUVPaDFCdXJaQmpEZ29RMno2dnl2L0tuTm5SMXBCV09KWEZ3QitBZklaUkRMTjU2dW1ud0RjZ290T0JxVzNWcGhnMzg0QVlwRmk4Zy9GS2lJOU1CNkNOWlBkbmFUUXB1RmRySDJEcHIyS0ROdHc3Mm5pNUw2bGpaM09YWWpzQ1J4cUxta0VxUDdOVkpOYkl1VkZ5RW5jcHpQUlpqeGVaOWl0d3hnbG1OcEZLYkM4STQxSzJpaDdxdjNVR290bDFaMys1WmFQN2tDZ3FiMUVWa1Z2OHJ5T0JjSWFlUXl0K21ZQ242b0diUUx6OFVHTFBhd1Y4dnJTYVBDcDRtbWtleWUxYWxtNjdyKzFEL3M2ZGZJSXovUXlqUWNKK25RTWtwZnRDTFBZdmk3V21UYUNUVHpUNnBnUVhDUDdwZTFvSncyZUhWYjRKYWFQOS9zbHdoM0lFSm0xQlJ6eURmcGtvUmZSVnd5REk4L2k2WFpjbTNiWG01L1NIekpLdFU5ZjBKbVBQRkhrMk8iLCJtYWMiOiIwNDk5NGZmM2MzYTUzNDhjMDljNzhiY2I2MTE0YTI2OGU3ZWU3NjM5NjY3ZDE4OTYxNjRmMjg5NjhhNjljM2E5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1qczRGOHFjY1BjelVvLysvNnlkTkE9PSIsInZhbHVlIjoialBaZUpWdHl4aXI4clkzbTFpeG1YOWczWkdwMlpXcGMyV1Q1aUV3WUIrOGk4N0c2U2czanNHM1NnN2JVYmhsYnJYdWFJQ1pHV1R0UUlySUZKMGpEd2JZUHJ6OC9PVHgraWcyTlpPS2N2d2lhN0lHWmhMQUFMNnhFUDRKMnRkQ3k2T1BHN2g0a2ZycWNlSkJaZHFlS2dhYWY4NURSZnkxeFNNcmtnWHE4eExVOFNQSXhUL2drZUo5dEp6eis2NktXbktwN1NFaFplYjdFOTdnYUlFdFJjNXZ0dGJBcTdna014VmZDYW14NjlieUNrcU9nL05ab2RHNkRtZ25NNWxzdFhrV3huMlJSOUs3WHpZSVBmYzJKVkl2WUllT0tqQVhqbXoyYXdLTmxmbjJFOFpyWXR1L0I4MTZPc2ZleE9UZ3Yxb3VSdk9ZS3NLYUJCOHZ2OVgvY3EwZ3RYMHFoV3FRcVlRVStDdDNNMzE0eTFXS004R05YUXZNejI4bFdiWVRJVTgvZ0FXSWkxUER3WGpyc0JneHlEU3J2Y09FYWcxMGpkckovODkxc1RnYUhFN1RVSEN4VXVoQjVwYmFueG03enN4ZjZZZThjUTJBRnlOUTV0TmgxMTVRaDlDVjI5SC9rQnJldlFyNTkzMHlycGVLMDlxaGltL29WRzg3S05CTzUiLCJtYWMiOiJiNWQxM2RjM2NjODRhNTg3YmQyMDk4OThkYmU2ZGNiMzJjOWQxNzRkMDI1YzA3MzFlOWM1ZGY0Njc5ZDAwYmM1IiwidGFnIjoiIn0%3D; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983282490%7C45%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219213961\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1885891217 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1885891217\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik94djBtM2MrUVNucy9BUzBhT2JWa1E9PSIsInZhbHVlIjoiUjBhVEE0SDBsa3VwTHRhVzJWOVBCbTNKSjR6YnhIYUlLdmw2Tzh4ei9TUElNZk1VQ2tZYjVFNG5qbzZBSHNBbmkzZGoyQXJjcVVoK3NHbTNVaGs1QTlvY3kwT21ZSWZUL0pFTjJIWG56YUhlbC92MzZ6SFZlcW9pS3Q1N0ZZQ2NkaTREWTNLeE90L3J6YVRaRzNxRmwvbkJ6cCtvKzFmN3lORGtWSWV0UHI5SXcvQXRiUFpURTNEU1pGVi9PaTFjTS8yT0VWT1YvcEJJVHFIOFE1REV0TjhDSlkrQzhKS01NTEhZTzVYMDg4MDhwWlJtME51L0VqZFYvNTk4bDNaYkcrN3hLVG10RWxVQVVoeXZ6UTRLWGJ3MWloclhFc09HcnVWSisrMG5rUTE4Z2p6ckdGY0VHK0ExSUVEQUFlTDZvaUt6MTBuQ25NV1pUSUdpOXpaa2UzQWlRY0Y3emtKSlY3Z3JCaVFMaENBbGFRbjlmMU9Ya016aE9QU1RoQlU1SVdUSnYxM3V1bzRoNXhhT0VjSnpSUXBmSi9CNEJxOFlnNHRGNnZza1dzYkpxaHpvbGd6T1RCMGc5R09EcXhJc09aUHFBdTkzSk5JL25FMXlxOWF0T0hDNUdoWlhJeEZOSXRnd24xeHFpWUdTSmFzcmpaazQvVkRzbTZCT3dpUEsiLCJtYWMiOiIxNGY2ZWZiMTFlMDExODA5MWZiMDUwMTJhOTBjMDIzZTg2NWE5YWRjOGNmNGNhZTQ3Yjg1NGE4MTYwMDUxOWZkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImZsY1JpMS8rL1AzT3lHUzJwUXBjL3c9PSIsInZhbHVlIjoiRUpxS3ZQVkpuRHZhTGZPSFBsNmJEdjhkanNsN21LYnNrUGJJVGRZKzBVSHpUM1FMNUhmOHFnengveERJbm0rTDFWYVlhNEgra1pNalc0TWVucXZEbTYrVitCSkE2K05weEJOK3E3UUw0ZytGQjdnb09pR1JVYVBjOElIdnpNQ3FnL2pxWW5YdHBJeUlhMFU5NFUxZHJRMGpURkFjWks2a2hNMEVRMC9pNERDTXB4Vy9GWjFuWk85T05oc0VtSkl5NHRXRzBpUEg0TlprdHpxSlNsVEhTdmNZRjB3Z0lFTWdvaHdFT2FaSHRMUnJmM2t2U1lkSHBkT0NVVThXd2Y1R3ZKejVZY3JhK0VKSjMwMjUydFdsbFhJRy94djVNeDBOdEgvL0E0bzJBVGd4VlBYY2lrUTlLalM4VTJXRXBSOTRWUUFocXlCZnIyTG1FVk05NFhqTmZFVmN2cXRVSGtJRlpwL2xuV1VzS2lxK01DeFJ5RDUrN2R0VGhhMTkvMkNxSnNtUWU2dDlVTW1YMVpqZU5Vb3g4aTg5bDg0Lzdhc09wSVZqQnUvOVlwQlBpWUw5R2ttZUdxZmt5MTl5S1c4aWQ0eXJ4YUMwMVc1Q0RhZ3hOSW12RmR4dE1HdW5la1NzZ0FtUGl3L3UrRVRQeVM1dmNXQXFNZWFPUEIyS1c4bFciLCJtYWMiOiJmYmFjODc2NGJiZDdkOTU5ODIyMTM5N2I5NmZmMGE2ZTg0MmM5NGQxNjUwYjZiMDczYWVmMDkzNzY2NWZmOWY0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik94djBtM2MrUVNucy9BUzBhT2JWa1E9PSIsInZhbHVlIjoiUjBhVEE0SDBsa3VwTHRhVzJWOVBCbTNKSjR6YnhIYUlLdmw2Tzh4ei9TUElNZk1VQ2tZYjVFNG5qbzZBSHNBbmkzZGoyQXJjcVVoK3NHbTNVaGs1QTlvY3kwT21ZSWZUL0pFTjJIWG56YUhlbC92MzZ6SFZlcW9pS3Q1N0ZZQ2NkaTREWTNLeE90L3J6YVRaRzNxRmwvbkJ6cCtvKzFmN3lORGtWSWV0UHI5SXcvQXRiUFpURTNEU1pGVi9PaTFjTS8yT0VWT1YvcEJJVHFIOFE1REV0TjhDSlkrQzhKS01NTEhZTzVYMDg4MDhwWlJtME51L0VqZFYvNTk4bDNaYkcrN3hLVG10RWxVQVVoeXZ6UTRLWGJ3MWloclhFc09HcnVWSisrMG5rUTE4Z2p6ckdGY0VHK0ExSUVEQUFlTDZvaUt6MTBuQ25NV1pUSUdpOXpaa2UzQWlRY0Y3emtKSlY3Z3JCaVFMaENBbGFRbjlmMU9Ya016aE9QU1RoQlU1SVdUSnYxM3V1bzRoNXhhT0VjSnpSUXBmSi9CNEJxOFlnNHRGNnZza1dzYkpxaHpvbGd6T1RCMGc5R09EcXhJc09aUHFBdTkzSk5JL25FMXlxOWF0T0hDNUdoWlhJeEZOSXRnd24xeHFpWUdTSmFzcmpaazQvVkRzbTZCT3dpUEsiLCJtYWMiOiIxNGY2ZWZiMTFlMDExODA5MWZiMDUwMTJhOTBjMDIzZTg2NWE5YWRjOGNmNGNhZTQ3Yjg1NGE4MTYwMDUxOWZkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImZsY1JpMS8rL1AzT3lHUzJwUXBjL3c9PSIsInZhbHVlIjoiRUpxS3ZQVkpuRHZhTGZPSFBsNmJEdjhkanNsN21LYnNrUGJJVGRZKzBVSHpUM1FMNUhmOHFnengveERJbm0rTDFWYVlhNEgra1pNalc0TWVucXZEbTYrVitCSkE2K05weEJOK3E3UUw0ZytGQjdnb09pR1JVYVBjOElIdnpNQ3FnL2pxWW5YdHBJeUlhMFU5NFUxZHJRMGpURkFjWks2a2hNMEVRMC9pNERDTXB4Vy9GWjFuWk85T05oc0VtSkl5NHRXRzBpUEg0TlprdHpxSlNsVEhTdmNZRjB3Z0lFTWdvaHdFT2FaSHRMUnJmM2t2U1lkSHBkT0NVVThXd2Y1R3ZKejVZY3JhK0VKSjMwMjUydFdsbFhJRy94djVNeDBOdEgvL0E0bzJBVGd4VlBYY2lrUTlLalM4VTJXRXBSOTRWUUFocXlCZnIyTG1FVk05NFhqTmZFVmN2cXRVSGtJRlpwL2xuV1VzS2lxK01DeFJ5RDUrN2R0VGhhMTkvMkNxSnNtUWU2dDlVTW1YMVpqZU5Vb3g4aTg5bDg0Lzdhc09wSVZqQnUvOVlwQlBpWUw5R2ttZUdxZmt5MTl5S1c4aWQ0eXJ4YUMwMVc1Q0RhZ3hOSW12RmR4dE1HdW5la1NzZ0FtUGl3L3UrRVRQeVM1dmNXQXFNZWFPUEIyS1c4bFciLCJtYWMiOiJmYmFjODc2NGJiZDdkOTU5ODIyMTM5N2I5NmZmMGE2ZTg0MmM5NGQxNjUwYjZiMDczYWVmMDkzNzY2NWZmOWY0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-20224740 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-20224740\", {\"maxDepth\":0})</script>\n"}}