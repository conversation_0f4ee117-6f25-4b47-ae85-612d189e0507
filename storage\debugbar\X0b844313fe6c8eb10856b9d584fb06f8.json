{"__meta": {"id": "X0b844313fe6c8eb10856b9d584fb06f8", "datetime": "2025-06-27 02:27:21", "utime": **********.274543, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991240.826577, "end": **********.274556, "duration": 0.4479789733886719, "duration_str": "448ms", "measures": [{"label": "Booting", "start": 1750991240.826577, "relative_start": 0, "end": **********.222124, "relative_end": **********.222124, "duration": 0.39554715156555176, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.222134, "relative_start": 0.395557165145874, "end": **********.274557, "relative_end": 1.1920928955078125e-06, "duration": 0.05242300033569336, "duration_str": "52.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45409488, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.003, "accumulated_duration_str": "3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.255729, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.667}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2661169, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.667, "width_percent": 12.667}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.268567, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 78.333, "width_percent": 21.667}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991174265%7C33%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldBQUh1VEtjS2djck9RUzk3RUFDT1E9PSIsInZhbHVlIjoiNG55Um9NSjNiR2RDTy9SUkFBbTl5Ym5DVm9UdWJ4dTdxQ1NmaUJHY21paFB1MFdBYStPL1dSVmhtalZrY0RFRzg3ZnhFOWFoaEk4QkFsSzk0N0czWXVUYU1tS0tISGhIMGFJTCtoT012T2xNK1JzYVNMb0QyMFlEdUZCbGlla3VqaDJoYk9RNHI4eGZUc2lpZ2hVRTcvTVJ6UVpUTUphczIwYUVCRkJFRWJXdnFNYlExK0IrOFBielowUUJCdUppbWNKdkhUblM3bEhScHIvWklJTFBIbmgzZUVYSzFXTUlud3QwNnJ6ZmdiWXdYY1pqeFE3K1QxYkNNZURWdVpGMWx6NGtCM051L0pBZ3hRZ2hIOCtIdTJIZVFsOHlSWjlJSWJVQ2xMWHh4L3ZOTHpSTElxbnJkSUNBT1pMMENBZDkwb1VHOFkzMm9yYnpNM2wreXhCbE5YWVc5SVJia0VaRkN1UzY3dmVsWTJHbm84K2w1K2dTb2pJeFdHWk0wcG1lUEwxUWZjbkFEUndFRFd0SnVHanZKRXJBSGp3VFlhdi9oTmhGaW1kMnVoU3B2VUFzYXY0elBzYVpDNm9sMmpQY21sdE9XKzN3YjVDZHRUd09VN3A1VGJYZHBsWG80dkdFaktwdXphaFBDQmNYZHg2dlgzZnNvNUo1dFozQ1grS3QiLCJtYWMiOiI5Yjg2ZjlmM2IzOGEzMWJlYjg4YzkwMjkzM2U2YmIxYmNhYjcxYmU3YTFiNDFjYWZjZTM4OTdlYzUwZmYxMDY2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Img0cHB0cEVLeVM1YjRyMkhSYXNnSHc9PSIsInZhbHVlIjoidjRkNVRCWFdxVWJqVlZTT0kxbVFPVmVTSFZoS0RQOE56RTlmK2tFZmltbGNHTWIydldiWFRFeVhGaUlFRFhvZGpNMVpqTDQ0VTdFR3gxTndxdjNZNEh5S0FxV3Fma0RzdXpleDJ5K3VYcTAyWEN6NlJOQlhHVnhDSlREZWJLaVpqQ0tNb0tSSEZWWklERG5BNENBVUY2WGNYTjI4aVcwYzMwNVBZbEM5QUhqaExRUWhyUTZ3U2ttMjZxYU42c3RoaGFjSmRzbU9lWG5ZTHFQSkVVcTRtVGs3TXRrZlkxZUxMZjdYdHN3cXQ4aXlndFhCanI4MW95bzdVdG9MQ1RQTnAvNDk0Q3ZtNmhPcTlNNkRsUXR2VmVCTVBqSUVLTThrYkNkMGxMUkRLQUx2eDNBdG45NUdFdUg0aHp5RjAyV0ZDOEtqSklJYUc4eVdxN2J2MmsyM3JvR0dQUXFreElISExqLzU2UFk4NVhxTUV2aDM2Y0JtZUNUa1BiWk1NTE1LVldsbmdqanFDOExWOVVuNWxITTZBNVJHbWJhd3FNTUtseStTdDBWU2N0b25nTGpiRGxnaUtoaXhmVGN0THJ1Rm1OMGExamN5R1ladDVoWFNEaTc3TC9OV1lHeDNiNjcvMDd4K3d3S00zNjJaQTdxRlFncUViamZ2eHhxbjNiSEIiLCJtYWMiOiI2MmE3OTRhYzBkNDRmZmVjYWVkOTU3YzVlN2MyNmE1OGI2OWZlZTU0NWFkMWZlNDkwZTQ2MWUzMmM2OGRmNDg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1918502013 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918502013\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1641607121 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJ0YjVab2gyRWM4djdkM0VpNkRPMHc9PSIsInZhbHVlIjoienhEWW1mcDRQRWtwOGpScWRJY0dCOEtEaC9Bczk2SnFaVm5EL2ZXMnFlUXRmZCtlbll1eEwzMVdqYTJYZitLL0gycmRSSjl2STVoMThDek9aTVJyNTlqL3JmZWh1dFc2MFk4OWl5aDhVaXFudVRNVmNzcFYxR3FwNjJ4cnh1Qk9wV2l4dEhPNEs5aUw3cEJkdkhQdVZmM1NjUkw5TVFmYmpOZjB5TmlhaGx6SzR5aVE5QkJJZWVMcHRac0NsRGk3NFJrV2pTZ2JMRWVWU2RKTXEvVDhpMW5WbE5ZY0srVi8yUk9JWFRjc2lITXZFY2JycmUwTHN0c0I3L3MxcUJrSUNzcTloVXJlZ05EQ01qYmNjTmRva0IxY2lCTWFVbUI0ZWlHMGNMdGM0Z05ySlFCcXU1UHkySjJRUnd6NndWVmZCVHBxZmxFQ1R1M2ZWakpLUUZIK2psVjgvQUI1Z0xnMVJkNVhDdnN1L3ZHSHZ5aXNjUFBuOE1Ub1c0LzFFQjZFVStObFBMN3diVXA4clBYdTdXc1Q0ak5JcEhJRnJTSVVjMkZxNEMxdXJBYVAyTFA5aEpUS1cxUGF0OVlROG1FMmR0TDZHZTBSVUExdHl2eHZIb24xQkcveGowUGV4N3gxdWpKZlBxWHRXY3JsdXJiZUppU3JXS0xsR3oyRDdicWQiLCJtYWMiOiIyMDdjNDg2M2ExOWI2NDBiMWQ3NTkxMDE2ZTIxMTc3ZjdhZDI1OTFhYzI2MTFiN2FmNmJiMzllMWUwZTk4MjU2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJjTklJVGtDb280U0NLUDRFSGVPQlE9PSIsInZhbHVlIjoiaWd4ZlliZGk3VXFsQ3dic2Q1Y1lMd0dUWGJSc0tPQ0V6VHhCVUJCK05DdVhsNld4S0tqaUFLVWo4S0N5ZDA4aWJGRFo1VUEwcnA3UnFBM1RGN3BjM1lDNHdzUkJaUm1qUVRIamtSL3FYQzNYTmNWMVVicUp4L3Q5eE5tSEZuY294T1ZOckl0cWp5TVpucHpyS0w1eEZQbU5hMFIrQ1pBQkEzZFFnaVIyM1VxSnZPbE52SlkwQ09tRXlUa1dPN2RiSUFBaVQxaGlLKzg5L1ZoNi9EWGEvSml4QnNZWHBCVFNQdFJJL2JOWXVrOTF2TTVldjUrVitSNFFKSTZxZUFLR2tSa1lmNmdHbXhNSlhkbEpoRFFaWEM3YjMydmw4RlNaZ2t2Zkc4TktjU2w2aDBoMnpUN2UvSzFUVVo5aUpyREJUQm5tTjB1ZnZoQy9YUjBudnNIU0VocEN4czI2b1N6SjBHN1VBUmVNeWN1L1V1ekRKZGM5Q0dMejRUMjlHVTNnZ1hrRC9FMWd3djBmWm5ZZTZXSVhRM2tMRkRSdmxQWEpuMU1tcjNhbmVoVWF2L3pWYlJFZld3RWNmSGVlLzlvbWl5ak44ZmZXR1Z1K0xlRUxNVEhEbUsxb0d4NHlYOVFBV0pHRm1zdmw3cGUra0Zxb3VwajQ0enVOKzdHdVhzVk0iLCJtYWMiOiIxNWVlYzcxZTlmMGM5MWI5ODg1NzMyMDljZWYwOTU4OGI1ZWE3Y2YwNWNkZGYyZTE4MmYxMjM3OGI3MWQ4MWY2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJ0YjVab2gyRWM4djdkM0VpNkRPMHc9PSIsInZhbHVlIjoienhEWW1mcDRQRWtwOGpScWRJY0dCOEtEaC9Bczk2SnFaVm5EL2ZXMnFlUXRmZCtlbll1eEwzMVdqYTJYZitLL0gycmRSSjl2STVoMThDek9aTVJyNTlqL3JmZWh1dFc2MFk4OWl5aDhVaXFudVRNVmNzcFYxR3FwNjJ4cnh1Qk9wV2l4dEhPNEs5aUw3cEJkdkhQdVZmM1NjUkw5TVFmYmpOZjB5TmlhaGx6SzR5aVE5QkJJZWVMcHRac0NsRGk3NFJrV2pTZ2JMRWVWU2RKTXEvVDhpMW5WbE5ZY0srVi8yUk9JWFRjc2lITXZFY2JycmUwTHN0c0I3L3MxcUJrSUNzcTloVXJlZ05EQ01qYmNjTmRva0IxY2lCTWFVbUI0ZWlHMGNMdGM0Z05ySlFCcXU1UHkySjJRUnd6NndWVmZCVHBxZmxFQ1R1M2ZWakpLUUZIK2psVjgvQUI1Z0xnMVJkNVhDdnN1L3ZHSHZ5aXNjUFBuOE1Ub1c0LzFFQjZFVStObFBMN3diVXA4clBYdTdXc1Q0ak5JcEhJRnJTSVVjMkZxNEMxdXJBYVAyTFA5aEpUS1cxUGF0OVlROG1FMmR0TDZHZTBSVUExdHl2eHZIb24xQkcveGowUGV4N3gxdWpKZlBxWHRXY3JsdXJiZUppU3JXS0xsR3oyRDdicWQiLCJtYWMiOiIyMDdjNDg2M2ExOWI2NDBiMWQ3NTkxMDE2ZTIxMTc3ZjdhZDI1OTFhYzI2MTFiN2FmNmJiMzllMWUwZTk4MjU2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJjTklJVGtDb280U0NLUDRFSGVPQlE9PSIsInZhbHVlIjoiaWd4ZlliZGk3VXFsQ3dic2Q1Y1lMd0dUWGJSc0tPQ0V6VHhCVUJCK05DdVhsNld4S0tqaUFLVWo4S0N5ZDA4aWJGRFo1VUEwcnA3UnFBM1RGN3BjM1lDNHdzUkJaUm1qUVRIamtSL3FYQzNYTmNWMVVicUp4L3Q5eE5tSEZuY294T1ZOckl0cWp5TVpucHpyS0w1eEZQbU5hMFIrQ1pBQkEzZFFnaVIyM1VxSnZPbE52SlkwQ09tRXlUa1dPN2RiSUFBaVQxaGlLKzg5L1ZoNi9EWGEvSml4QnNZWHBCVFNQdFJJL2JOWXVrOTF2TTVldjUrVitSNFFKSTZxZUFLR2tSa1lmNmdHbXhNSlhkbEpoRFFaWEM3YjMydmw4RlNaZ2t2Zkc4TktjU2w2aDBoMnpUN2UvSzFUVVo5aUpyREJUQm5tTjB1ZnZoQy9YUjBudnNIU0VocEN4czI2b1N6SjBHN1VBUmVNeWN1L1V1ekRKZGM5Q0dMejRUMjlHVTNnZ1hrRC9FMWd3djBmWm5ZZTZXSVhRM2tMRkRSdmxQWEpuMU1tcjNhbmVoVWF2L3pWYlJFZld3RWNmSGVlLzlvbWl5ak44ZmZXR1Z1K0xlRUxNVEhEbUsxb0d4NHlYOVFBV0pHRm1zdmw3cGUra0Zxb3VwajQ0enVOKzdHdVhzVk0iLCJtYWMiOiIxNWVlYzcxZTlmMGM5MWI5ODg1NzMyMDljZWYwOTU4OGI1ZWE3Y2YwNWNkZGYyZTE4MmYxMjM3OGI3MWQ4MWY2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641607121\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}