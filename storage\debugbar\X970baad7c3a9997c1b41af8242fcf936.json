{"__meta": {"id": "X970baad7c3a9997c1b41af8242fcf936", "datetime": "2025-06-27 02:15:07", "utime": 1750990507.019435, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.596399, "end": 1750990507.019449, "duration": 0.4230499267578125, "duration_str": "423ms", "measures": [{"label": "Booting", "start": **********.596399, "relative_start": 0, "end": **********.968441, "relative_end": **********.968441, "duration": 0.3720419406890869, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.968451, "relative_start": 0.3720519542694092, "end": 1750990507.01945, "relative_end": 9.5367431640625e-07, "duration": 0.05099892616271973, "duration_str": "51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45734672, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00222, "accumulated_duration_str": "2.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9963331, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.018}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750990507.0061219, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.018, "width_percent": 19.369}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750990507.0114162, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.387, "width_percent": 12.613}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-866544624 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-866544624\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1090295223 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1090295223\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-347215696 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347215696\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1183760875 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990505004%7C8%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpqMkMrbXNRU2t6ZXozSURIL0tHbkE9PSIsInZhbHVlIjoiQzN1b1REa09wcHlhbmJhV25CWHp3OEc0RHVKNERXODZhSjVVdklLNkE1SExVU040YUhSTnlSWXVnNmU0RWthS1d6VnhIT3JnQTlWQTZ4NEhtR3A2bXNTbGl6QTdFaEhXUVg3NmdlQm5EU3lTN0x0RnZPWU5qZ3Rnc2RxWlA0ZVRZRW9UTnlUMDFqQU9Qbkk5RnlpMG4wMmsrazAzNkVqUHlwYm4xYWk3OFYzUkROUWR0Q3pRQ2xMUm9Ha2VQUGl1eDE4NDVDbFdvQ1V2eFlSSzJkMzhsQjdzd25lbVpOMFQ2R2JzM0tvaGpTUUJ5RC9JSEV1WlZ0b1hJd2tjRTVmQ0xSYjRBbFlnUnREbW1JQm1PUUVzTVpUUFRLMWx1QzJjVkZ3cTVVektFV0hTNnlnalpzaGl4NmZuVkRrajhaZUFwcWhKWlBFVlZEdXZ0eTZzdGFLZnBFcXY0R0JUOE5JRWlWNGV5cW8xU1B1RkZ5UXlOYjVvbUFyZEZxVFFnMFQ2OHdtOWMxbFhoUmNkMThub2NXaGdOMkYxWXFjZHZNQVBCT1U0L3E1eDlrZ2FUSmdwZ1prSFZLVGRRU29BK01YcUk0eUJyZ1gzOHgrRW9JYnhMMHhSbEQ0ZVY4SVhDRjhQWTBwWkl1bkhJYVlwbFpZR09sS25nbTRtWTgxWmZPYkQiLCJtYWMiOiI0NTRlMzJjNzQ0YmVjZWQ1YjQzNDdkZmYwZTU2ZThlMTRkMDhkNzU5NTI2Y2I1MzkzMDA0NDdkOWJhMDYwNzJmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZidGpzRWhtRjI0cDhmSlFZTDhoaUE9PSIsInZhbHVlIjoiVm5PQjBwMk5MM04zTS91VTZ3UDB4L01xWWxoQzliSHRNbjB6K0hicHZtUmJFRFJHQjZKNXVESnBtdmliZnpmNWhNME9JdDdOWEdHTHF0ekxGOWQ5R0lPalBzUlNCckJpczIrZ3JvK1pjWjMyOU9UN21uOEdsOUhBS3ZGTCtYVWZMbXFuTnRZUFBXckNzSWcwMFg0bXBPYXIzdHQ5ZnlrbTgrQXZWMjY2eHFzWVVKVGZSV0ZhMW1CMVd3NGZxUVl3aFA4OTQ4bUIwRE96SzRWR09FMm1CMVQ4MjROTFlaWGQ5QWJVbWsvSldiOGhPVG9CeGxWOG1vb21PV3RvVWk5N052djVHekQ2NVNsZk5maUdsZ3FKMzdLNkNFNTNlczlwS2E1QTByTWdQL3JBTzBzbERUV2tPMXVLTjFIRk5ZRys2aGZjMllwbHljRHQvOTFOLzVLWWFBNGt6SERScTI3YmdzcGJka0E3UDRlSUZqQm9GbUJLVDdYNVh2UmdHTXRNc2pSR1E0ZHdYNkYzckVqK3EzRDdFMEozai8vMEswQTNCS2FtOXRSODREZEdVVHpZM0tLaW41SUxUT0ttQzJLOVpyZXZEaGNkOHNJVmQvZ3pIMit3SWxHYTM3QVNLNXBGTUtjaG92UDdja0liekVET1d0ZXFsUkJnWjB0NU4zK2ciLCJtYWMiOiI4YzRkN2E5YWMzMTRkY2NjOGIyMDM4ZmE0OTBiYzk5M2M0N2RkZDU5MDc5NWMyMzA0ZDk2MGU2NTczYjc5Mzc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183760875\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-17485858 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-17485858\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZEN3p3bHpmU25oNGJHRUZMbmh3Rmc9PSIsInZhbHVlIjoiaDVhMlpRQnBrbmFTZTRCK1N6QU1rcFl5NSt1UUs5TndvQ2JONUJBbXdvVG1TZ3EwK3Flb3MrcWhTQ1hkWnBjeXlUdWNhcnllcDVhUmxNekVMYWxTc0paZXBmdVFUZEM4NmlWcSsxQVpWTnpSQkp5TEFXb2JSTUZLUFpGbDFtNFRpSG9hajA3NEdVYWk5TXRqZWN5TXdYSHhPaXVLR0JGWk05eGFSeCtvTW9LanRzOGxwcS9PWHU0S1RlakVjVnJpdndjNHpmRzd1b3MzaWRKRktsbzYzMlZGWWprOTRVcmlhK3pGeDZBTkc2TmhJK09JblJHcTkydHorOEVhd0hZTFdOWU41TUFsMWl0ci84UFV1Rm1Xc3Q4QzJiVjFZN25IZnhoQU9xUkQ5ZE9ZZUIvNXYrWnNuMXpjb0wzN0dxeFpKSzFzWkY1b0tFT3BBSDZDYUIyOWNGMU1WSjQ3Q0FZZHowcmFjNlNDRWVGaEVuQ1lPaGN4WTJVTE03Z045YmoyZWZwMmMwV3pwcXNhY1dRU1BlSGtJNm9iUUo2ak1zVFV5eDY3RXhjVHZEWERwZi92RnRjOWlQMUxrcWlEaVdFcUFQUmZvQkFabXA4a1lMY202bHAvbDg3OWV1aGhreis4TytMczkrU2xpSVphbHdhdXhLUWsrV2F0cElGQ2Fmd1MiLCJtYWMiOiI2M2Q1ODE0N2YxZDViZDM0Y2ZkMTEzZDMxOTFhYWZjZGU3NzE2YjQyMWUwNjMwNTBkMTQ5MzBjM2RkNjdlMDA5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNNWG9aWGYzRWlZeVdFTnJ1M3ZBSmc9PSIsInZhbHVlIjoiamErM2JIcTBOYWNJRCtlcEdjTTBOYmhlcnlOUFA3VUxYdEpRMUQ4OWRxemloRER0QXRpVTl2WU1UU1pKclczbnhyUENpRi9kUE1rRVprdU9BTDJuM0xXR2lwVmRRUk9LcmEzT1FCL1ZDM2xTSWZsbGdZRVVaUjJUdEJ5Q2k5K3EwY2xMeUdHcjRWSHI1SjdVMUdrUmV0TW1GMENxamt5UzVsODZGZ09vVTV6UG14U3U3WGdZTmpaeDd2eko2RVhsNTFTRHNXRGVvRFJYa3Y0dFRuaE14M1UwYjJNR3ZhR1VmcTFNdjB2VzFHK0crTmJUb2k4Q3NOQnhwclFoVG84U2NHWW04d1d2ak5kcy85aTArZ3ozbDltQmlISW9VK1ZGRXJoV1o3OGEydk5aek5tamN1UjZsWjZFNi93Tkl4RkhEa3ZtRFNvdkxZalhNbWhkY2NXV0YrcjVocGdHQmliWEZRSzM5WldLOXNaZDh4SXVtV3d3Z0Y1Qkc5NjRlallTczlOUDhhT0g1TlpZNWNxLzhFUCt3T0hPWEFQY3dFZkg4S0luUTlZVWc5a2JmeHVRL1JuL1prUnd5NlVtQWFUTHRTOTRsQlZ6UE1hc0R0TmM0Wjl5L3NjaGZFOGV2MS91OEh3ZCt2WTJueWEwbVMraVdWU09Ld1g3VDJVajB6cHAiLCJtYWMiOiI5YjlhNWM4YmY3MTc1N2JjMGE2MzhmMWIzYzAzOGU5Y2JhMzZjZjJlNjc1MWQ3OTdlMzA4MmQxOTZiNzEzZmY5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZEN3p3bHpmU25oNGJHRUZMbmh3Rmc9PSIsInZhbHVlIjoiaDVhMlpRQnBrbmFTZTRCK1N6QU1rcFl5NSt1UUs5TndvQ2JONUJBbXdvVG1TZ3EwK3Flb3MrcWhTQ1hkWnBjeXlUdWNhcnllcDVhUmxNekVMYWxTc0paZXBmdVFUZEM4NmlWcSsxQVpWTnpSQkp5TEFXb2JSTUZLUFpGbDFtNFRpSG9hajA3NEdVYWk5TXRqZWN5TXdYSHhPaXVLR0JGWk05eGFSeCtvTW9LanRzOGxwcS9PWHU0S1RlakVjVnJpdndjNHpmRzd1b3MzaWRKRktsbzYzMlZGWWprOTRVcmlhK3pGeDZBTkc2TmhJK09JblJHcTkydHorOEVhd0hZTFdOWU41TUFsMWl0ci84UFV1Rm1Xc3Q4QzJiVjFZN25IZnhoQU9xUkQ5ZE9ZZUIvNXYrWnNuMXpjb0wzN0dxeFpKSzFzWkY1b0tFT3BBSDZDYUIyOWNGMU1WSjQ3Q0FZZHowcmFjNlNDRWVGaEVuQ1lPaGN4WTJVTE03Z045YmoyZWZwMmMwV3pwcXNhY1dRU1BlSGtJNm9iUUo2ak1zVFV5eDY3RXhjVHZEWERwZi92RnRjOWlQMUxrcWlEaVdFcUFQUmZvQkFabXA4a1lMY202bHAvbDg3OWV1aGhreis4TytMczkrU2xpSVphbHdhdXhLUWsrV2F0cElGQ2Fmd1MiLCJtYWMiOiI2M2Q1ODE0N2YxZDViZDM0Y2ZkMTEzZDMxOTFhYWZjZGU3NzE2YjQyMWUwNjMwNTBkMTQ5MzBjM2RkNjdlMDA5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNNWG9aWGYzRWlZeVdFTnJ1M3ZBSmc9PSIsInZhbHVlIjoiamErM2JIcTBOYWNJRCtlcEdjTTBOYmhlcnlOUFA3VUxYdEpRMUQ4OWRxemloRER0QXRpVTl2WU1UU1pKclczbnhyUENpRi9kUE1rRVprdU9BTDJuM0xXR2lwVmRRUk9LcmEzT1FCL1ZDM2xTSWZsbGdZRVVaUjJUdEJ5Q2k5K3EwY2xMeUdHcjRWSHI1SjdVMUdrUmV0TW1GMENxamt5UzVsODZGZ09vVTV6UG14U3U3WGdZTmpaeDd2eko2RVhsNTFTRHNXRGVvRFJYa3Y0dFRuaE14M1UwYjJNR3ZhR1VmcTFNdjB2VzFHK0crTmJUb2k4Q3NOQnhwclFoVG84U2NHWW04d1d2ak5kcy85aTArZ3ozbDltQmlISW9VK1ZGRXJoV1o3OGEydk5aek5tamN1UjZsWjZFNi93Tkl4RkhEa3ZtRFNvdkxZalhNbWhkY2NXV0YrcjVocGdHQmliWEZRSzM5WldLOXNaZDh4SXVtV3d3Z0Y1Qkc5NjRlallTczlOUDhhT0g1TlpZNWNxLzhFUCt3T0hPWEFQY3dFZkg4S0luUTlZVWc5a2JmeHVRL1JuL1prUnd5NlVtQWFUTHRTOTRsQlZ6UE1hc0R0TmM0Wjl5L3NjaGZFOGV2MS91OEh3ZCt2WTJueWEwbVMraVdWU09Ld1g3VDJVajB6cHAiLCJtYWMiOiI5YjlhNWM4YmY3MTc1N2JjMGE2MzhmMWIzYzAzOGU5Y2JhMzZjZjJlNjc1MWQ3OTdlMzA4MmQxOTZiNzEzZmY5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}