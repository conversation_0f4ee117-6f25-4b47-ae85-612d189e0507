{"__meta": {"id": "X8d34673599c899ad9229f108dfaeccef", "datetime": "2025-06-27 02:12:34", "utime": **********.253685, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990353.799985, "end": **********.253703, "duration": 0.4537181854248047, "duration_str": "454ms", "measures": [{"label": "Booting", "start": 1750990353.799985, "relative_start": 0, "end": **********.186912, "relative_end": **********.186912, "duration": 0.38692712783813477, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.186922, "relative_start": 0.38693714141845703, "end": **********.253705, "relative_end": 1.9073486328125e-06, "duration": 0.06678295135498047, "duration_str": "66.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722096, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01214, "accumulated_duration_str": "12.14ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2136462, "duration": 0.011179999999999999, "duration_str": "11.18ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.092}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.235415, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.092, "width_percent": 4.448}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2426808, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.54, "width_percent": 3.46}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-239743592 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-239743592\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1902118639 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1902118639\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1225867801 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225867801\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-769607615 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990349613%7C5%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Imx4QVA5ZUphek1vY2VvWVFMcEFsOGc9PSIsInZhbHVlIjoiUlhxNnVPeUt1MDVtQmYyMEgvaFFodWI0TzhybGgwUW9aV2hkSTVJdkl1Z0RBL2xsOUdDeU9DSlYxZmNLMk5aVHdlclR6VnNxSFFsWWVuZG9Oc1BlaCtLNit1emZPb2JIM3VVTWw0K3VZOS9ZVWE4U1R1VmUrZnFZaXYwYmJUQnVROFlyUDYwbm1Ccy9sTEtNakwxNVFrc0NjNVA0czF0OEJGU3NOTWpHTUk5Qk5MVG80dE5zTGt4NVF5Y3pTL204ZFMvZzZyaVdCMm1CNW5FS20vQUpXVGpHRlpITkptSnlpYkNPdVBSa2s4VjV6RmZjUzFWVVpPTWoxSGJMZThFUWpQbzRwL2U3Unh4cmZqN05NeTlRSjN2UkZkYjhNK25PYXRleUthMFdnS081SnVYN2dKd0Zzemlpb25HSXpVOVVLS09URmJnbnV0SE1Va0tPeU1nY2NFZWpYWVRabGhRd3FPOHVuQ3ppUGlyWXk1NFk5TnVZQk1ZS3EwWXk0VjlLQVFKbHpHRFNoVjR4eXRhNThZWFlUSVJERGF3QXkwZndjeVQ2YnpxN3ZxbWRwUzBRdmJGMk9vWHdHNENoZWh0c0V4VEVGaEhnM2FidEc0dzVJbWNaSlFSTWdtQUQyMUpsVENDRmMxWjc0N0dINjZSbDJsUHN4dys3RE5wMUo5YTEiLCJtYWMiOiI4YmQxMTgwOWRkZGVlMjJlMDg3MGRjOTQ5NDg4YjExMjJhNzIxZWQ5ZWZlNWJiOTU5NWUyNDFiMzMwMWYwNTg1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJ1SVJSTGxkeU5rZHluTHN2bkkvanc9PSIsInZhbHVlIjoiZzlUWVBYMjlhcGxGZXpyRVBSTWlueFZ4czNsRjU2ZjNxQWVuUG8zbkZFQ2pZN3lNTDQ1cm1mR1FwQ3AxcGw1bHRleVJhbHVjdkRCM2xWNjhYS3dMMEQ2TDJRVjRDM3NLMlhGK2tKd1Uvcit1SERVcFMwemhWZGwrMzB1aFRPdWV6b3FhMXM2MmsrQVNndlVUZnBaMWV1Yjh3R1hsd1llVnhGbTBTWUFSNjUrcVZRS0ptcVpKL2NDZkkxMGhOa0UzT0FnYUczZXhwVm9oL1g0Zzd0bCtieUtRVHoxdUxxbFRGUlhTbkZDNWhWODh6V04xb3BhOGZPREd5bmRtOTFlQk1iQmdEcWgzL1didFFvZTNoL2E5SXFmVkJxTHZpVmNMUndKclVjd000U2hkRFdDVzZlWklzRklLL1lnOS80WGZiSUhmd2txQ1JmSmw4NldoenBMR2dDUUJiKzZYbmdGZVZSOGxGeTQ2SlBOc2VYTDlXQlRLRkx0K0JOekxaSUVTcEd0MHhRNGxUTTdKOW5EVENXR1B1VWFzOEJoNWE1WGxjbmdUYk1JbzhXTFV6NndBS1VIR3BKU2tTZ0dHTGw5UWJqVVFyYzFJdThqWmoyeGxWZGNJZFpCL0xKcExKZi9jaGZQenNsYkxaK1BiNVZoQXhuYjBYSnA4YkZNVDBpeW8iLCJtYWMiOiIwODFhMDEyZGRhODExMTA2YTBlMGFiY2E0NjU0YmE1MGQ4MTg0NTc1ZDFlYmIyZTEyZjA1OGE1NjE1M2UwYWI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-769607615\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1988470813 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988470813\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-925554460 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkU1bXBXV1BNekxRM1Q1SDI5ZWJPTEE9PSIsInZhbHVlIjoiblBuNDNRMzU3WENERWY5R3AzU2x1YzBDaHpvY3BEd0RmenozOTdkUUJGZnk1WjZpeUJNV04xWDNxUE9YWGZnYzQ4TXlBNDhwS3JseE5lemdZeFFQT0EvYy9GWUl4bnZiTS9RVFhtZHphUFVHaEtDSStOWjc5b05zN0lqRmljQURkcy9qMjN1bTNMUGFTV1o4c3ZlZzVZd2xZZTNMdTJmWGdpcmZvWnpkcnd4YXBweTNrMG1YTlJOTWtVemx3N01xT1hqRy93Rk9rcGJ4dzlJWVQzbDRCQ0FZZ0VDeVZCMWRTYzZIOU5WdHhQNnZKS0ppUzc5M243UGl3MmlZWW9NWkxOOElDT1JXQTJlaEtjd2c2Tm5CTzIySmlMckM5VHJVRS9DbUpibU1xRHA4eEF6cFZzVE9EMVdQTW55ZmsyRlJLSGlZVVJYNXNmdDArQWlTOU1wRnpkc05STmUzWGZTWUtXZ3FyM0JpRlhEdGh6bDhzLzJ1VHpqSnpwVjVlS0ZGN3o1LzE4bW5PQzUzb2JteVFuZWdNZTJEWnhXRitNR2IxOXZYM2p0aUVXV0dGcWZYRUFOcFR2RS9URzh3QTUyTXVRQzlBdmF1OGJVMGZVbFdSOXMzYzl5TGVxODQrSXpTeWJtNGVWeWZlN1RJazh5U1hHb1Y2VEJ1ZlZYQ0t3a08iLCJtYWMiOiI0MGQ1OGJhZDAzNDRiYjc1YWRlN2ZkOWU1ZGY4OGY5ODM5ZDAwMWNiZTlkMGQ5NzBkZmZhMTQ3YmZiZWY4YjY4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImExUjRjaG5XU0gxbEtOb0lQSEx6Znc9PSIsInZhbHVlIjoiM3hNT1JZMkFKaTJjSWc3U05MdXNTMm5JMnlRc3NZRzFqK2tjUzdsMnRWWDRvb1Uzc0dGQ3o3ZzlaV05tODhsVzgrMVFnWkZCVmE5bytoLzdmWUlYeEhoa3RHTlZIZzVsVlVsRmMvcXQ5TTFYNU1VRTZBWWM0SnhuOWZnZ0RGZlRYdVNaejBvTGI1L2lsTlZGUVZlZldNT3ErVUZoakhSMG94Sll0K0hqcDJ0S2dOZis0ZkFTWVJPaDZmN3lkblRnd2x5QzZoQUxWV080cHZmVUlFMTJpalQvMHhDOU41QXVhcVh1Q1QxVHhkNDNrV0cxK3Vuay90Rkhxa2FEM2xpYnF5ekRhdkE2bkdsYUV2TU5sa252akhweTA1cThnUTFQU3lORzVObHBmckNSRHBrNFJvait0bXlmY25xUUlKalM0MVRNTS95d0xzNkQ0enlaYkFGZWs1ZmdNL080ejRVNUUwYUhxZ0Jmb2NiWFJHNktyZy9SRFAyNTA4cm1TeVVXVFA3WWVTNG9HenJYcWI0M3BFL1drYURMTEdEZnR2TVdoMXN6WThNRlYxZEdUU2pHTGdUaFpwVUVObnljaW93VkQ2aktRaUQvSVZ1SUQrMHU5a28yZUphdllEN3hFYVFmV3NKbjVzYjh6Vi9lTU5vcnJ4dVhRSG1BeEpBdFhQVlIiLCJtYWMiOiI1MTRkNmIwNGViMDVmYjhjMjgwNDMyMjExZGI1OWYxYjI0YzVmYTkwNDdkYjEzYjc1ZWY4MmU2NTA5YTY5ZjgxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkU1bXBXV1BNekxRM1Q1SDI5ZWJPTEE9PSIsInZhbHVlIjoiblBuNDNRMzU3WENERWY5R3AzU2x1YzBDaHpvY3BEd0RmenozOTdkUUJGZnk1WjZpeUJNV04xWDNxUE9YWGZnYzQ4TXlBNDhwS3JseE5lemdZeFFQT0EvYy9GWUl4bnZiTS9RVFhtZHphUFVHaEtDSStOWjc5b05zN0lqRmljQURkcy9qMjN1bTNMUGFTV1o4c3ZlZzVZd2xZZTNMdTJmWGdpcmZvWnpkcnd4YXBweTNrMG1YTlJOTWtVemx3N01xT1hqRy93Rk9rcGJ4dzlJWVQzbDRCQ0FZZ0VDeVZCMWRTYzZIOU5WdHhQNnZKS0ppUzc5M243UGl3MmlZWW9NWkxOOElDT1JXQTJlaEtjd2c2Tm5CTzIySmlMckM5VHJVRS9DbUpibU1xRHA4eEF6cFZzVE9EMVdQTW55ZmsyRlJLSGlZVVJYNXNmdDArQWlTOU1wRnpkc05STmUzWGZTWUtXZ3FyM0JpRlhEdGh6bDhzLzJ1VHpqSnpwVjVlS0ZGN3o1LzE4bW5PQzUzb2JteVFuZWdNZTJEWnhXRitNR2IxOXZYM2p0aUVXV0dGcWZYRUFOcFR2RS9URzh3QTUyTXVRQzlBdmF1OGJVMGZVbFdSOXMzYzl5TGVxODQrSXpTeWJtNGVWeWZlN1RJazh5U1hHb1Y2VEJ1ZlZYQ0t3a08iLCJtYWMiOiI0MGQ1OGJhZDAzNDRiYjc1YWRlN2ZkOWU1ZGY4OGY5ODM5ZDAwMWNiZTlkMGQ5NzBkZmZhMTQ3YmZiZWY4YjY4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImExUjRjaG5XU0gxbEtOb0lQSEx6Znc9PSIsInZhbHVlIjoiM3hNT1JZMkFKaTJjSWc3U05MdXNTMm5JMnlRc3NZRzFqK2tjUzdsMnRWWDRvb1Uzc0dGQ3o3ZzlaV05tODhsVzgrMVFnWkZCVmE5bytoLzdmWUlYeEhoa3RHTlZIZzVsVlVsRmMvcXQ5TTFYNU1VRTZBWWM0SnhuOWZnZ0RGZlRYdVNaejBvTGI1L2lsTlZGUVZlZldNT3ErVUZoakhSMG94Sll0K0hqcDJ0S2dOZis0ZkFTWVJPaDZmN3lkblRnd2x5QzZoQUxWV080cHZmVUlFMTJpalQvMHhDOU41QXVhcVh1Q1QxVHhkNDNrV0cxK3Vuay90Rkhxa2FEM2xpYnF5ekRhdkE2bkdsYUV2TU5sa252akhweTA1cThnUTFQU3lORzVObHBmckNSRHBrNFJvait0bXlmY25xUUlKalM0MVRNTS95d0xzNkQ0enlaYkFGZWs1ZmdNL080ejRVNUUwYUhxZ0Jmb2NiWFJHNktyZy9SRFAyNTA4cm1TeVVXVFA3WWVTNG9HenJYcWI0M3BFL1drYURMTEdEZnR2TVdoMXN6WThNRlYxZEdUU2pHTGdUaFpwVUVObnljaW93VkQ2aktRaUQvSVZ1SUQrMHU5a28yZUphdllEN3hFYVFmV3NKbjVzYjh6Vi9lTU5vcnJ4dVhRSG1BeEpBdFhQVlIiLCJtYWMiOiI1MTRkNmIwNGViMDVmYjhjMjgwNDMyMjExZGI1OWYxYjI0YzVmYTkwNDdkYjEzYjc1ZWY4MmU2NTA5YTY5ZjgxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-925554460\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1712070433 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1712070433\", {\"maxDepth\":0})</script>\n"}}