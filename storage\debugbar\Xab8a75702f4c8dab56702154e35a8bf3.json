{"__meta": {"id": "Xab8a75702f4c8dab56702154e35a8bf3", "datetime": "2025-06-27 02:27:18", "utime": **********.957983, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.531165, "end": **********.958012, "duration": 0.4268472194671631, "duration_str": "427ms", "measures": [{"label": "Booting", "start": **********.531165, "relative_start": 0, "end": **********.864936, "relative_end": **********.864936, "duration": 0.3337712287902832, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.864944, "relative_start": 0.33377909660339355, "end": **********.958013, "relative_end": 9.5367431640625e-07, "duration": 0.09306907653808594, "duration_str": "93.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48198040, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.027500000000000004, "accumulated_duration_str": "27.5ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.896001, "duration": 0.023010000000000003, "duration_str": "23.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.673}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9270298, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.673, "width_percent": 1.418}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.940129, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 85.091, "width_percent": 1.673}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.941894, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.764, "width_percent": 1.091}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.94586, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 87.855, "width_percent": 7.818}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.950207, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 95.673, "width_percent": 4.327}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-111423103 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-111423103\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.94498, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-192201821 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-192201821\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1484860777 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1484860777\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-411962886 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-411962886\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-839908827 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991174265%7C33%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjVHRDcwN0tjNnRRRHRpVVZ2b2hSdVE9PSIsInZhbHVlIjoiWmsyMlA0eStBbXZTemxmN2FiaVkrejdTcFJ0TjdaaHFybzE0bSt4emhUNUxNNm9nQjNWMU9wMGJCbDZxUS82aHdKWU9RR2xQclhoT3R6TjlKRXpaRnF6N0YvWUtabnBUdENWcXVIWDNOcytqczFmYU5Na1NTcHlkWHl2MExNY1c1RHBkYjI2U1ZTS2JaYUphZGxMd1Q3VnlTSVlyVGxFeVdzQnJsQ3hHQ0Q2WThRaUpJclI2RGdDVHVUTndDaXQwVVF1SU9mMGRhVVZWSjdVeUNZaVc3bmtGSE91S2l5YjY3MGIxMG5GSW9WcFFOWXFySUQyS3gxVk9GaG1hNkt5cHRpMksrZkNLb1J0SHcyVU8yUzZKaDIyc09VN1hFT010c2ZnZURDRXU4Q0U1eEpWYlBtU1RQaTg2WFI1cld6RGNpWDJLODhIc0svSituK3JOTmxGb3hQMnNvajFQNEIvcXg2MU1KVGtXbnJPU1hwV0xSZlNLeXBTeENxbmlCYnBaaE5ILzN5OXNWMlJNUzBJMXlMWHJ2TEZoVUxNNWZheVdHMm9yUlJrR05qRmFlVEZLL2M5amJIay9wWnRiZzRCR3NVQXV5YWRSNXNhMFk1ZG84TnRiVzUzVXlWUVlLL0d4ZnljK291ajBJTnU3NjNRRzNKV0YxbHp4UFZLS2dtOUYiLCJtYWMiOiI2NThmNTYyZDEyNWIyNmExYjFmYWNjYzY0ZjU0ZTJmYTBmMGE3ZTc2ZjQxMjFkMzhkNWFhOTQ1YzljZTcwOWFiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkFKc01mUmJYbERXdjdoUkowUnAxNXc9PSIsInZhbHVlIjoiTis3UlZVcy9kVFNTV0xCc1h1OE1PWGVqcW1BY29zN2IwRjNjdGpXR2VpejNoWkczT1ZJc25oMDZnaXhTbjVGdThGNENRWHBXekdYM1llUmdnUXJuVk1zN1FXbUlzdmU4NU95SFpCcmNjbnVqMkFOdzZIWmlMNlFhQ3FCeWpvVXIyN2JIYzhSZkJrMnVTQTJzT0dyMGt4UlpRQzMrZUpqdGQwc2EzUXhaQzRJNDZWMFFFdVdZcUJIRGlUTmYrNy9weGZpaEoyWVgvMkxYN21mdUlmWW1JRllrZG9BTWk0YUFoOXg3cCtLbVBubFlES2tiR2t1S2VpNnRaRjNJOE5lUitFV0h4WlkxaVFJTUdrWFVuQ05jTlJmZEovRU1XYXQ2UVdWbktiWGgwUnJxdlRLcU5oS3J6bUxySElsNnhUMVJUUnlBcVQzQ2VENnJVeW52RHRmUEdiUmxKV0NCaHIrU25aalNBYXB4aU55Y0NucG1hSnhIbWdaWW54RTZQbnpIUC8yd213Q0p5OEpJdWFnSkZlSG16T1VkMGNsV2dNb0VPR1NKK1d6MXVVOGlDNmNUbGZrY0EyaDc5cWk3bGFOenBNaGtUcHZCVzZNVW5RMGpVa3NqVVJmTHZrL3lkVHZSbndRYkhIbWZFU3ZvODlZQWZHSnU4RnFxMWQ4cnZsQUoiLCJtYWMiOiI5NjI3MDFlNGFhZTVjMTBiMzczM2MyM2Q1MGY4YjZjOGJiNGNhM2YyNTlmNmY3YjlmODBkMzNlMGE0NGQzZDcwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-839908827\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1105688586 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imk2TWR4UE5Gc1VhdXlwMHBldGczVlE9PSIsInZhbHVlIjoiMmx6NFViZHhwT3o2bTdDQlZjY0pQT1JuUy85NklVUUZkdXpNVlFXcWhMOWhuNWM1SlRzdjgrMVJramsxM1AvVlQwTUlXeE1OczBMVDUvemEvSFdGemVqV0xEaXczZDlVc0dCK3BxWmFVdzJPUlQxbEdjK09jY3ZqYW00bi9TYWJUaVplTEF5VFVJWEdPSzZVNDZsQ3dvMkdYdWxpMWNrSFhhdTVHemJYS3ZkRzNQMThyUTlmRFdmbklGUDRXSUFwWWV3WmdIa244YUNpU1k2dDBnQ1U4ODNQWlhobEt6bWlidFgvMHgxV01qSjE3cnozQzU5Ry85dTVOUk1rcHArUlluNWtIQTlKU0xtdi9LNjlPY2xtc1loY0pzYWxWTDNGRDVpVnpSbmMvVDZrd2t2d241RDlpemhUdTQ0Qlh0alR5QWErUTRXR2tOTFVlOWIrOHdFTE4vSWhrdDhkYVhyQk14Mkh5M0tLeUlGUlZvSEU1RnRHWWJFNDBuUUdzeCsvSStQbGd5STZwYmFLb2J4V1p2MnFSQzFXbjdaTlJ6Y1lnZTQyS1dKVnpRY3hBZVBqWTcvcHpDMVVNU0QvZDZuWWJGSDNSc0wxSmZlWHVNQUxPWFRVK1hrbmRBbDNWZVBIODU2a2tOSTZ6cE40VjVCNHhqbzFWNDRheVZyQ3RNL2giLCJtYWMiOiI3YTcwNGVmZjdkZmE0MGNhMDQxNGU1NGViMmMyYjhkM2I1M2I2MDEzYTM5MTQyZTdmMWNmODY2OGYwYjdhNTA3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkZCZG16ams1WHptVS96RWZYTDFCSHc9PSIsInZhbHVlIjoiVVp2amdWekVkMk5HYzdtWHY3SGRHV1lmaUFMbmMvSVlWV2V4ZnlPdmRnNTBIbzVYeVh2QXdjRU4wbXNwTmFhS0JXVTNxRVZJVFRtWmVQZGNrZEdmZnlHc20wQUpQRFU0c0xrL0hFK2lGT1N6dUNOVFdCQStYa0hYV3MwUlRqUjdxdk4zaHNwR1JORWxmYjYveFNaa21aWEV5OStGNFpJS0F6d09BcUVTQzRJU3BRTWFsQUUzbXY4QUpMeENVNmdVT2VPbHc1SGx2Q0g4TXcrMHltZ2FXcjYxeThwZDJXSVowUUYrK1lVbXUyRU52VlR2Uy9QSzUvdWJsZUZTV0NNUVBzMG1YdCtkdTMwT3dua2grbUt0ZXRJU09iZ1hSVnNLbTdVUjI1UE42RmgybHdaWmQ3Q1F2ZlNsNm5iUjJ2ZDBoemtXQmQ3UDRtOEcwRGJoSjcySEJ6K056MkJGb2VtU21yeWFLYVcyRktrdHE1UlUvV2g0Zm5GaGFBdEY2LzM2WW9ISnU4VGdiTTcwbkduM2RDd1Rydy9jTTN5U1ZoeXZOOGZtY3ZWWjd5KzV1NnFIRWU4cUlDNGh3cWQ5SWNCSklpN2EvWFV1N2Y1ZU4vaDVsUGhHM09JbDlVUjNXYXgzbmxWc3VZQVZlUGdWK0IrVUZGRklwZjErbGVnSDY1N20iLCJtYWMiOiIxZTI5OGRiN2NkYTUxMjEwMDcyYjBlNzNlOWM5ZGU3OGIxZWY3ODU0NmQ0OWUzNDdhOWY0MzFlMDczNTI5MTYxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imk2TWR4UE5Gc1VhdXlwMHBldGczVlE9PSIsInZhbHVlIjoiMmx6NFViZHhwT3o2bTdDQlZjY0pQT1JuUy85NklVUUZkdXpNVlFXcWhMOWhuNWM1SlRzdjgrMVJramsxM1AvVlQwTUlXeE1OczBMVDUvemEvSFdGemVqV0xEaXczZDlVc0dCK3BxWmFVdzJPUlQxbEdjK09jY3ZqYW00bi9TYWJUaVplTEF5VFVJWEdPSzZVNDZsQ3dvMkdYdWxpMWNrSFhhdTVHemJYS3ZkRzNQMThyUTlmRFdmbklGUDRXSUFwWWV3WmdIa244YUNpU1k2dDBnQ1U4ODNQWlhobEt6bWlidFgvMHgxV01qSjE3cnozQzU5Ry85dTVOUk1rcHArUlluNWtIQTlKU0xtdi9LNjlPY2xtc1loY0pzYWxWTDNGRDVpVnpSbmMvVDZrd2t2d241RDlpemhUdTQ0Qlh0alR5QWErUTRXR2tOTFVlOWIrOHdFTE4vSWhrdDhkYVhyQk14Mkh5M0tLeUlGUlZvSEU1RnRHWWJFNDBuUUdzeCsvSStQbGd5STZwYmFLb2J4V1p2MnFSQzFXbjdaTlJ6Y1lnZTQyS1dKVnpRY3hBZVBqWTcvcHpDMVVNU0QvZDZuWWJGSDNSc0wxSmZlWHVNQUxPWFRVK1hrbmRBbDNWZVBIODU2a2tOSTZ6cE40VjVCNHhqbzFWNDRheVZyQ3RNL2giLCJtYWMiOiI3YTcwNGVmZjdkZmE0MGNhMDQxNGU1NGViMmMyYjhkM2I1M2I2MDEzYTM5MTQyZTdmMWNmODY2OGYwYjdhNTA3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkZCZG16ams1WHptVS96RWZYTDFCSHc9PSIsInZhbHVlIjoiVVp2amdWekVkMk5HYzdtWHY3SGRHV1lmaUFMbmMvSVlWV2V4ZnlPdmRnNTBIbzVYeVh2QXdjRU4wbXNwTmFhS0JXVTNxRVZJVFRtWmVQZGNrZEdmZnlHc20wQUpQRFU0c0xrL0hFK2lGT1N6dUNOVFdCQStYa0hYV3MwUlRqUjdxdk4zaHNwR1JORWxmYjYveFNaa21aWEV5OStGNFpJS0F6d09BcUVTQzRJU3BRTWFsQUUzbXY4QUpMeENVNmdVT2VPbHc1SGx2Q0g4TXcrMHltZ2FXcjYxeThwZDJXSVowUUYrK1lVbXUyRU52VlR2Uy9QSzUvdWJsZUZTV0NNUVBzMG1YdCtkdTMwT3dua2grbUt0ZXRJU09iZ1hSVnNLbTdVUjI1UE42RmgybHdaWmQ3Q1F2ZlNsNm5iUjJ2ZDBoemtXQmQ3UDRtOEcwRGJoSjcySEJ6K056MkJGb2VtU21yeWFLYVcyRktrdHE1UlUvV2g0Zm5GaGFBdEY2LzM2WW9ISnU4VGdiTTcwbkduM2RDd1Rydy9jTTN5U1ZoeXZOOGZtY3ZWWjd5KzV1NnFIRWU4cUlDNGh3cWQ5SWNCSklpN2EvWFV1N2Y1ZU4vaDVsUGhHM09JbDlVUjNXYXgzbmxWc3VZQVZlUGdWK0IrVUZGRklwZjErbGVnSDY1N20iLCJtYWMiOiIxZTI5OGRiN2NkYTUxMjEwMDcyYjBlNzNlOWM5ZGU3OGIxZWY3ODU0NmQ0OWUzNDdhOWY0MzFlMDczNTI5MTYxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1105688586\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1539966947 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1539966947\", {\"maxDepth\":0})</script>\n"}}