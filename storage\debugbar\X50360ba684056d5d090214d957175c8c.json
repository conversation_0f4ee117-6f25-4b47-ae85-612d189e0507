{"__meta": {"id": "X50360ba684056d5d090214d957175c8c", "datetime": "2025-06-27 02:33:54", "utime": **********.444215, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.039317, "end": **********.444228, "duration": 0.4049110412597656, "duration_str": "405ms", "measures": [{"label": "Booting", "start": **********.039317, "relative_start": 0, "end": **********.373336, "relative_end": **********.373336, "duration": 0.33401918411254883, "duration_str": "334ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.373345, "relative_start": 0.3340280055999756, "end": **********.444229, "relative_end": 9.5367431640625e-07, "duration": 0.07088398933410645, "duration_str": "70.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48198040, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0067599999999999995, "accumulated_duration_str": "6.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4036348, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 21.893}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.412845, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 21.893, "width_percent": 5.769}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.425605, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 27.663, "width_percent": 8.136}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.427361, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 35.799, "width_percent": 5.621}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.431499, "duration": 0.00275, "duration_str": "2.75ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 41.42, "width_percent": 40.68}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.436336, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 82.101, "width_percent": 17.899}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1060634112 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1060634112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.430592, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1436656216 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1436656216\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-637591661 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-637591661\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1378112277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1378112277\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-5528734 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBQcWtqbzJsbmd6UktzT2RvSDN2emc9PSIsInZhbHVlIjoiaUpKdHEzOHIyK2VxN2Z4ZUdFeGN2djhOWWdVNm9xb0xVcElhVkF4Q2lyQkE2QzFmblFNbDRrdE12NnpDMjY0MzBaVXNpdDU4Y3VKK1M5WkY1Q3E3T09RVlU2dWxYTTBMU3dpWkVWWTlFSXQ4Y3FzK3hCMjBNbmRUUzBFeFdOSFdxQ3NOWWVSM01pa0FmbVlEQ1VrWkVCNm0rT2RLN1dTeC9vV0s2dTNlS2gycFptdTFGeTVMMnZTN01pL3d2VmQzQ2NLNHEvNDVSdXUzOHp0T1VVQWluQXNwcGZEQkhGdXZZdU5NUmJiU2VlU1A5OWZROWxDZWpLSWsyU2lEN1FVNlR5UmVpOHVOd05FYnh2K3JtWmFYNW5sSnhUdHhIcUJXUHBmM0p2YmJvT3pQc0JkeFhCdFR6WUpHd3BlNzdmTnBaUlJ5RzJvN29IK2YyWWpZeFlQQmF5KzhDWE1FaXFNK3ZGTnJKY3MrSzdndWxXb1VSRHd1Y0NEdlREQlNsNHJWSnU4dm5wZnE2Q1JLOVZycUZSNU5Qbnl3MGdQZGcyK1JtdThXaGZ4MCtFVWdiSHJldjJhWm82aXJaRmlZVnhVMnMxMHcyRDR1dTZ5cXhHQU9wUmE0QzByODZmQkF2emNpaG5yUlYxMWROSUF2TjFPSWxQUE5TcE1qVExSZlBlOEciLCJtYWMiOiI1YzA2YzVlMzY1OTI5NTMxYzQyMzJmMDMwNzgyNGVkZTk5YWU0MDEzY2Y5MDVkMTQ1N2QxN2M2NTk4ZDJjYTAxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImxVRXdJeXhJQU5yeHoxSGZUN0htR2c9PSIsInZhbHVlIjoiM0N1eTZNeUFtYVAxamJmc2Fmd0hQMXNIL1NPTnJVK1RGSGs3dzdsUnpEYmVza3NzYmpWOFBEclVra21ObmZ6YVg2VjJwak5yWm5ReEg4Sk5yOU0va2Y3Q3IvckdkV2JtWmEvN0VENlVKdWZvUk9VNTNadW5XMk5ScllYWFBaUnJUL3l4NDE1YW95Rkk4TjByRXMvM0IzRDZGL1p1Rmw0bkJqSTVQdDIzY0VEWlpVejZDNlJZUHVJTWpXeFNIUEFoSUhVWE9PakZVQ3hQUWRJeUlVMGNXQjhFek1kVDloTUt6UFFjcjJ0cHRjQktPcHZ4QVdLK3BpaFJEMTA4Tm1WeFBQbEV0dU8vclZuUjE3ZU1XVVY1Mk1ORFoxcUxrc3Z0bnJ0Q2U2ZW0vM203NHVjdkJvN2xoSnVUNVBiU080T2t2R0tyVTJDM3Z5WktLdDQyVmJvY2tDRXVlc1pnSnFiK0FUUkVqaCszR29YeU85V3lJaWh3Q09mbGJybmltTkZiRS8vQUxUMXZaT0pEYm9VclhFcmc3K0ZvSkU3SGE2eHRQWUhUeWpZUy82am1DNzI1SDFyQWRYbW9LS1UwL0ZZZTBQUXQzOGg3ZzRaTjY1TGRtR3FNWXBGZXpBdWNQTFdMQWVONXc3SmRXSUZtUVdBSnFZTEt6Nmk3RmtYdkN2L1IiLCJtYWMiOiIxMTBlMGE0MjNjMzBlZTk0MTk0OGExNDRhYzgwNjIwZGFhMTFlMjI0MjIwMWQ1MzY2MjllNzM4MGIyYWNmNzkxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5528734\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-793239935 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-793239935\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-604721274 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:33:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkwvZHZBM1ZJSjlMTmxiOElhSzE4VGc9PSIsInZhbHVlIjoiRUNpZy9yK0o0NDI4OXJVY2I2ZkNBZ0c4SnZuMGhjeGxhL2RPQjBROFdyQ21LNWk0UmZkL1hPLy9nSHgwQnZmdEtPMWlGWTRub1ViNkxqWERrWXhFRTVQZW0vL3VtTno3WmRSRVMwcllFa21ZWEd2cS9BajJKQ2lxelNYWmRtd0dycWdheTRFMStMSHhOMmI0eWhDTTBxblJGcGRVVFpmWk9WTDdPNzB5NDEreVJwQzRPemsyY3hYMm5DWkpvVWtoZmwyZzFCMlJlM0JQbXNFRkV3ZjI1S1UwODg4ZStXdUJUL0RBZnU5dnByTGpKZWZaYzdKekVVaFBqWWpmUWh5Rng1OHVRVjQzOXNWUW9QWjc4L2FieC9wbi8rNmdsajd1RUxvN2hDYUFzbEROd0tyOVN2QzB5QjJ3ZG5CSXB1SEJvYTRDY3pPbkpadG9NNVNROVBHK0M4NmxOU28wU3Z6NGhpTi9OcncvWWl0ejRLSGVmK1A0QTlpZ21oeHZ5R3gvaHc4TW1WRG0xaGg4K0JCekN5ejRlZUtneVd4VWEzQkt6aHVmOWliTVFkWS9TOUZMbVFQK3dWWXlsLzBxeFdLZGdGUko0R2FVSEtqeG1aWWpHRFBmNUZtMkNXenFPQzJqYVhBSG9vclp1aE5oU2hEY1dWbjNUOVkzT1dDYTU2WWQiLCJtYWMiOiIyN2Y5M2U4MjM1NzRkNDcxMDYwOWJjZWQ0ZWYxZDY2Y2I1NDgwMTFhZDkxYjJjZGUyZWMwMDRiMDdiYWI3Y2I1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:33:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InRXK2FJNEEraE9DRzdUUW5Sdi9aY0E9PSIsInZhbHVlIjoieFdjS2tNcHh0Q0ZMT0ZCVHp6Zksyd2dvZTJoc3NmcFVJNjdPRmFWOVlHWlNzZ0VNZTNBRzVDbm85Njk0SzVacVJhNkc4NHI4bWNMNmMrUU1TZTNyWXVRRDlHSncvTFM1MmdHOXVORHFhTjhyZmdaeDJNYWNtZ2Jkd1QzNkR2MFJyU09vRjd0ZG1TOHpLWlRpblE2S2hwVlFkYStqdkhNVWdHT1c2QlNCUTdDOGdMamNmRzl1UlphQzNoSi9EVTdVUW9JTlZTcFZPU1FsMk5FOVlDWTVFMlRmMjV3WHc2OXZHRWFKNzFSK1JJSHhOK3QrNE1lSUs2RHhJbkRReWhOREU3VFpzT3gvY3pVc3RudExxKy9Gay9RSTFyenZ3TTl0eDN5VWdWdkdoaWhUY0dySUkzY2w0Smp3QnRIQU5CYWxCWjlpU0hBdTlPWHFJTnlLOElXc09YWngvRkhra1IwSWIxdVRZZzFBdm1IeFdmSUxRdFI4cUVOQzNiWVVQZGsxelVBQ0RpUFlmcUNxdFp3L1U5L3M2ODNHM2ZYeWtmTnpUb0U1YmNueWNiRWhCeWpmM0ZrM0NZWWxTak8vb2h1SnBHS1Y1Njg0clRweWVpRitsS29wT1dKMDByTW5EN0VKdVVvejFYSlFmWHo4QXlMM1R5UWNvcytXQnQxOHZkd0QiLCJtYWMiOiJhOGRkYjEwYjU3NzliNGZhNjQ2ZDFhNzViM2YzNDZkZTQ4Y2EwMmY1MjczN2E5OWQ5ZDJkNzQ2N2JiYTc2MTY1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:33:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkwvZHZBM1ZJSjlMTmxiOElhSzE4VGc9PSIsInZhbHVlIjoiRUNpZy9yK0o0NDI4OXJVY2I2ZkNBZ0c4SnZuMGhjeGxhL2RPQjBROFdyQ21LNWk0UmZkL1hPLy9nSHgwQnZmdEtPMWlGWTRub1ViNkxqWERrWXhFRTVQZW0vL3VtTno3WmRSRVMwcllFa21ZWEd2cS9BajJKQ2lxelNYWmRtd0dycWdheTRFMStMSHhOMmI0eWhDTTBxblJGcGRVVFpmWk9WTDdPNzB5NDEreVJwQzRPemsyY3hYMm5DWkpvVWtoZmwyZzFCMlJlM0JQbXNFRkV3ZjI1S1UwODg4ZStXdUJUL0RBZnU5dnByTGpKZWZaYzdKekVVaFBqWWpmUWh5Rng1OHVRVjQzOXNWUW9QWjc4L2FieC9wbi8rNmdsajd1RUxvN2hDYUFzbEROd0tyOVN2QzB5QjJ3ZG5CSXB1SEJvYTRDY3pPbkpadG9NNVNROVBHK0M4NmxOU28wU3Z6NGhpTi9OcncvWWl0ejRLSGVmK1A0QTlpZ21oeHZ5R3gvaHc4TW1WRG0xaGg4K0JCekN5ejRlZUtneVd4VWEzQkt6aHVmOWliTVFkWS9TOUZMbVFQK3dWWXlsLzBxeFdLZGdGUko0R2FVSEtqeG1aWWpHRFBmNUZtMkNXenFPQzJqYVhBSG9vclp1aE5oU2hEY1dWbjNUOVkzT1dDYTU2WWQiLCJtYWMiOiIyN2Y5M2U4MjM1NzRkNDcxMDYwOWJjZWQ0ZWYxZDY2Y2I1NDgwMTFhZDkxYjJjZGUyZWMwMDRiMDdiYWI3Y2I1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:33:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InRXK2FJNEEraE9DRzdUUW5Sdi9aY0E9PSIsInZhbHVlIjoieFdjS2tNcHh0Q0ZMT0ZCVHp6Zksyd2dvZTJoc3NmcFVJNjdPRmFWOVlHWlNzZ0VNZTNBRzVDbm85Njk0SzVacVJhNkc4NHI4bWNMNmMrUU1TZTNyWXVRRDlHSncvTFM1MmdHOXVORHFhTjhyZmdaeDJNYWNtZ2Jkd1QzNkR2MFJyU09vRjd0ZG1TOHpLWlRpblE2S2hwVlFkYStqdkhNVWdHT1c2QlNCUTdDOGdMamNmRzl1UlphQzNoSi9EVTdVUW9JTlZTcFZPU1FsMk5FOVlDWTVFMlRmMjV3WHc2OXZHRWFKNzFSK1JJSHhOK3QrNE1lSUs2RHhJbkRReWhOREU3VFpzT3gvY3pVc3RudExxKy9Gay9RSTFyenZ3TTl0eDN5VWdWdkdoaWhUY0dySUkzY2w0Smp3QnRIQU5CYWxCWjlpU0hBdTlPWHFJTnlLOElXc09YWngvRkhra1IwSWIxdVRZZzFBdm1IeFdmSUxRdFI4cUVOQzNiWVVQZGsxelVBQ0RpUFlmcUNxdFp3L1U5L3M2ODNHM2ZYeWtmTnpUb0U1YmNueWNiRWhCeWpmM0ZrM0NZWWxTak8vb2h1SnBHS1Y1Njg0clRweWVpRitsS29wT1dKMDByTW5EN0VKdVVvejFYSlFmWHo4QXlMM1R5UWNvcytXQnQxOHZkd0QiLCJtYWMiOiJhOGRkYjEwYjU3NzliNGZhNjQ2ZDFhNzViM2YzNDZkZTQ4Y2EwMmY1MjczN2E5OWQ5ZDJkNzQ2N2JiYTc2MTY1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:33:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-604721274\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-363751809 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-363751809\", {\"maxDepth\":0})</script>\n"}}