{"__meta": {"id": "Xadebedca1ecca88584d870f24140efaf", "datetime": "2025-06-27 00:14:49", "utime": **********.765987, "method": "GET", "uri": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.29555, "end": **********.766004, "duration": 0.47045397758483887, "duration_str": "470ms", "measures": [{"label": "Booting", "start": **********.29555, "relative_start": 0, "end": **********.681003, "relative_end": **********.681003, "duration": 0.3854529857635498, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.681014, "relative_start": 0.3854639530181885, "end": **********.766005, "relative_end": 9.5367431640625e-07, "duration": 0.0849909782409668, "duration_str": "84.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47521224, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.show", "controller": "App\\Http\\Controllers\\BillController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=333\" onclick=\"\">app/Http/Controllers/BillController.php:333-393</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00426, "accumulated_duration_str": "4.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.720676, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 42.254}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7314, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 42.254, "width_percent": 13.146}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7386289, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 55.399, "width_percent": 21.831}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.753972, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 77.23, "width_percent": 10.798}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.756296, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.028, "width_percent": 11.972}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => show bill, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1046433186 data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046433186\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.759953, "xdebug_link": null}]}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-1946488460 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1946488460\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-711590548 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-711590548\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-84884681 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-84884681\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2001211869 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983282490%7C45%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjFvMHZVcnp2NjNkdGFTamlZSlJQVEE9PSIsInZhbHVlIjoiQnUvaVE2Wjc5WWkzVjV2czI4MlBzT2dFalV2UWhtbVQ2ZzJlMzdyZkZ3QllUMmIyWmd5TU1WUVpwREwwT2t4R0J2b3ZzN2pmQlI2dDlFYndFVkhmMURPSU0vUUlmTGJFT2Z5YkluR0Y5YjNkMWRXTjE4ajVycmZYUW1mYTQreTFPNHNoVXVQMzVYSDNNTlh1S1Z4YzltT3dzUzcvMlRSUGlTeEF4SXhPdDVQRlJoMVBVUWdyc3hMMGpLSVU2b3M0QmRUQk5oK0VqU1JZc2l4UHF2eGczOEtCeTVQdEg4dkdLNGN2cHR2cjFFN1JJWWVmRGRndE1JRy9QVHErZnZGaEtncXZJVlFJRldhenZoNHBjNnhkZUdUeHNXZ0FTbTZScFhZTW1JZWwzaFZBWlpmRVg0eTNtdnZkdFpCUmtEMHAzY21wZEF5a1NSb0hMYk1kNGVXRjk4Mm5FRng3TUx0ckV1Kzhib2IvVFhyMHE3VW5YMk1iK2tJY21UVE4xUHdkTWFYSlVlaWNjdXBsamplZm5LVzgrc0YrS09LS3RVNWFqV29hdmpqKzBieU9jL1o5aCsybmZpbXNHK0VaVVZiQlFuaGlRYXFVNWowVEp0eWRHcHJlQjNFSlZtbmJPU1lDZTBISThIOGNMcGFrNE0wYkFPdzh4cndUbjMrSDg2Q08iLCJtYWMiOiJkN2M5ZDIyOTUzZGQ0MTRiYmIxOTMwNmE0NTA1MmFhYmQ2NDFkYzI0YjlkZTcyOWY1MWI5YjgyYTI0MzQwYmE3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkxoNlF3bzVGbGZhUkZNYUdRS3N2MlE9PSIsInZhbHVlIjoianNvZGl4T1BYTTJoUTlHYjFCOGhWVjIxRjlrREpDZFMxa0pTbERxMGFhZmVlMnN0K2JZUlE0R1FtYkpkbXh0YTNJSXNCZkYzZ1dkSHdwcVBVeTA4a3BEb0svSkZrS0xNSmJVOFU4NjczdzMxOHB2WTlCOTJVK0M1bXNwRVp3bGtGbGNEVVExM0FHc0FvRTRvd1pINnpZK0pDanNhOW9OZktWNjdkSForUk94QWJoYVdiTlBFZkF6SzRiR29jdkRPUmlOclFTbVFBeEFoZVFTZkhrUEh1TWlPanUyMVNtcGZVdDAvZlY3Y29zWjZwNXZsaGtSYUZWWXoxRjhQSURPY0dTUjFGZ3d3M2gwWmpncUpEQUJwRDN6bStjM1RJSExJTDFZWVNXNEJUcjkvaGc0UXB1VWF5dW9CRGFVaVNtMG05QWlsSWJRNG5XbDNwYWYwT3pDQlRFWGpJYkFSaFJ0Y1JYZWk5R0l6YVVZaDgxNXRlYnE4WHVWSWpkckpZcHlFNE5JTFhHeXdlRWhrUVd0YksvanNaLzRUTXcxdno4YVJsMko1MTZELzJlaStVSXEwd1JLZlhBcnJ0T01IbWh5dXRVNjZNZkt1SHl4bVhjZEpQZmRRTU0renNYdk1CTlZNMmxPRGVWOHlTdjNudjV5YUk5SlBsZkthajlFNFlZQ0EiLCJtYWMiOiJjNmI2ZTY4OWRmM2MyYWE0MDQ1MTdmMDM0YzdmNjBmZDVhNTM2N2UwZmVlYmY5M2VjYjFjY2ZjZWU5N2U4ZDQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2001211869\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-874127121 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-874127121\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-763231465 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1OS1BteUZsMno3S0NWM3laQzBLYlE9PSIsInZhbHVlIjoiand1WktFbjMwTk1TeExFdWZhVVljM0tTaDIrbkNVUU9nREtrNENsaU9HRDhoa3hubmlhcUtYU2VQOS9LNGVLaElxN0pTT2ZlMzI4Rm1KbzRDblRvU1lsL1g2L2JZc0k0aXlXV2QwNGJnRUZQOG51SjNnc3BlSGdVT055T2trUmtkUU80cFJvQmpYb1VzNjk5VzBaUE81SERDemV5K2xpa3VPZEFCK29FSm16YmtHWi8xL0c1ZWpLU2g4SUFFU2FleUkrTHFHUkRIY2JtTjZKL2ROMVNDSThCWTJhTlRnenpKd091V3RGbExDK3pJaUJSRDR0WUhUeVRSWG9hc3pTUHloUCs3bDg2RXQ0R244WlRPV21hQ2RPbHFVN2owaXZ6Vm9uNzFiWXozZDd0ZzRUcU5tNFcrVFBIZTlyc3ppalk5eVluamc4cUl0YVZDZWdCY2Z6T3VwKzk0cHJ3cXhmNVJ4TEh4b0ovcWU4bUh4cXFSbVFEM2FwLzllb0pwclVKWGpvYnVFVXRkODRiRXp3V25WeTNXTFN1enh6MmNOcHZobnlFVUVWSWEzTldDWVh6cDVwQkxScmIrKzd0bmIzcXBRcnNFVnR2UjM0ZDc3THJzdXZKN2pwc2ZYSUg5dWI5VWlRUGVuZ1FIN2w2R0pjQkhtdXhuL0lCOEZNc3RIdGgiLCJtYWMiOiIwNjM5YjFkZmRhYjE0MGYxOGUwY2ZkNTc2NjYzZjRlODZiZDY5OTk0OGFiZDQ0YTRlODQ2NWUxZDk5MWRlMTJiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlN4ZStaRkZwcFZtOUZTOXNtSFp4ZGc9PSIsInZhbHVlIjoiYmwvd2Z5UUFYZCtYZWZvSzF4YmhVU1dZMDBPVDFDZnY1MWlVQ3V3cURnQjlPWW9ncmtUajlEVXpPdU93eUY5SzRxTE11TnRnRDM0Y1BvdjVxQXVVQ2VYREs0b3FsUDhJdmUxYTU2ME9nUFlDUFM0eDJCYWZMb2Y0OE5ORHRwUEYrNzZnYVdRQ082cTRyTnZCUjJzaUdIdEdwbUxnN3dwMG5iSWRKdmtVMmdzeFN6eTAzR1VOT25vK0x6d2FYZkVMZHdiN0xYK21USTNDR0trN09ESXhRT1ZNTjZQTFEzNnRyNGhnc3lBNytOR1ZLUnYwU2paWHcyWDBzZDcrZVEzUTJnV1kxQkowZThTZHhZL1VJME9GL2NoS0hyUThxVDh3cVVjVmlJTStVOU42Z083YVgxMUllckhXU3Z1d1R0M2hOUjJ5NnRoMCtQdEV3NWVNTUVZaUJJb2tEdVJYckhIVk5PS1dIMFJubXpFb0c4T0J4MUpZeEJ3MlRkdjhrRFdnazNoNWk3Rnl1TytqVTRQMVJGcTR5ZVlheUIzTzhoZEh3WEdLUkNIaFpsOGVsREdDaGVpTnhrVm5BeGVoOVAyYXlqL3RldmN5OHhmTHUyemdaTUpCck1ldUJqSWIrRXA1Q0d3aTdFanhEZWJXSEJObXhvdThwQUdXTFhSbklYV3UiLCJtYWMiOiJlYjdiNzlmODQyZWNhZDJlZjQ0NDFiNjYwOTYxMGZiMzRlNGI1NWQ4MDlmOTYyMWY0NDU2ZDk3OWZiMjkyZTE5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1OS1BteUZsMno3S0NWM3laQzBLYlE9PSIsInZhbHVlIjoiand1WktFbjMwTk1TeExFdWZhVVljM0tTaDIrbkNVUU9nREtrNENsaU9HRDhoa3hubmlhcUtYU2VQOS9LNGVLaElxN0pTT2ZlMzI4Rm1KbzRDblRvU1lsL1g2L2JZc0k0aXlXV2QwNGJnRUZQOG51SjNnc3BlSGdVT055T2trUmtkUU80cFJvQmpYb1VzNjk5VzBaUE81SERDemV5K2xpa3VPZEFCK29FSm16YmtHWi8xL0c1ZWpLU2g4SUFFU2FleUkrTHFHUkRIY2JtTjZKL2ROMVNDSThCWTJhTlRnenpKd091V3RGbExDK3pJaUJSRDR0WUhUeVRSWG9hc3pTUHloUCs3bDg2RXQ0R244WlRPV21hQ2RPbHFVN2owaXZ6Vm9uNzFiWXozZDd0ZzRUcU5tNFcrVFBIZTlyc3ppalk5eVluamc4cUl0YVZDZWdCY2Z6T3VwKzk0cHJ3cXhmNVJ4TEh4b0ovcWU4bUh4cXFSbVFEM2FwLzllb0pwclVKWGpvYnVFVXRkODRiRXp3V25WeTNXTFN1enh6MmNOcHZobnlFVUVWSWEzTldDWVh6cDVwQkxScmIrKzd0bmIzcXBRcnNFVnR2UjM0ZDc3THJzdXZKN2pwc2ZYSUg5dWI5VWlRUGVuZ1FIN2w2R0pjQkhtdXhuL0lCOEZNc3RIdGgiLCJtYWMiOiIwNjM5YjFkZmRhYjE0MGYxOGUwY2ZkNTc2NjYzZjRlODZiZDY5OTk0OGFiZDQ0YTRlODQ2NWUxZDk5MWRlMTJiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlN4ZStaRkZwcFZtOUZTOXNtSFp4ZGc9PSIsInZhbHVlIjoiYmwvd2Z5UUFYZCtYZWZvSzF4YmhVU1dZMDBPVDFDZnY1MWlVQ3V3cURnQjlPWW9ncmtUajlEVXpPdU93eUY5SzRxTE11TnRnRDM0Y1BvdjVxQXVVQ2VYREs0b3FsUDhJdmUxYTU2ME9nUFlDUFM0eDJCYWZMb2Y0OE5ORHRwUEYrNzZnYVdRQ082cTRyTnZCUjJzaUdIdEdwbUxnN3dwMG5iSWRKdmtVMmdzeFN6eTAzR1VOT25vK0x6d2FYZkVMZHdiN0xYK21USTNDR0trN09ESXhRT1ZNTjZQTFEzNnRyNGhnc3lBNytOR1ZLUnYwU2paWHcyWDBzZDcrZVEzUTJnV1kxQkowZThTZHhZL1VJME9GL2NoS0hyUThxVDh3cVVjVmlJTStVOU42Z083YVgxMUllckhXU3Z1d1R0M2hOUjJ5NnRoMCtQdEV3NWVNTUVZaUJJb2tEdVJYckhIVk5PS1dIMFJubXpFb0c4T0J4MUpZeEJ3MlRkdjhrRFdnazNoNWk3Rnl1TytqVTRQMVJGcTR5ZVlheUIzTzhoZEh3WEdLUkNIaFpsOGVsREdDaGVpTnhrVm5BeGVoOVAyYXlqL3RldmN5OHhmTHUyemdaTUpCck1ldUJqSWIrRXA1Q0d3aTdFanhEZWJXSEJObXhvdThwQUdXTFhSbklYV3UiLCJtYWMiOiJlYjdiNzlmODQyZWNhZDJlZjQ0NDFiNjYwOTYxMGZiMzRlNGI1NWQ4MDlmOTYyMWY0NDU2ZDk3OWZiMjkyZTE5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-763231465\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1291079909 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291079909\", {\"maxDepth\":0})</script>\n"}}