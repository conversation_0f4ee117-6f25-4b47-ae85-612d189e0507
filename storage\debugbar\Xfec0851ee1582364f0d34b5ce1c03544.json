{"__meta": {"id": "Xfec0851ee1582364f0d34b5ce1c03544", "datetime": "2025-06-27 02:12:46", "utime": **********.421804, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.014654, "end": **********.421817, "duration": 0.407163143157959, "duration_str": "407ms", "measures": [{"label": "Booting", "start": **********.014654, "relative_start": 0, "end": **********.364928, "relative_end": **********.364928, "duration": 0.35027408599853516, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.364935, "relative_start": 0.3502810001373291, "end": **********.421819, "relative_end": 1.9073486328125e-06, "duration": 0.056884050369262695, "duration_str": "56.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43883960, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01856, "accumulated_duration_str": "18.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.393949, "duration": 0.01808, "duration_str": "18.08ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.414}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.415589, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 97.414, "width_percent": 2.586}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-229574748 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-229574748\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-590784631 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-590784631\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1119174425 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1119174425\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990356916%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklVQTEwY0VtOTJuT29QS1NiTW5wSUE9PSIsInZhbHVlIjoiMDNjVGIzb3NmU2FhaWZVVHVIYVBvSkNQR2NFK1FQR2NyT1VtTTFsejNPZFBRZEk3Z1NPTGM1aC9aeEFCRjNGSnRYWHpBRHQxSHJtZkI1SGdJZ1NQRlNCUFVPVHpJNWs3VFFLU0dJeWI1NHVIenFneVVhV3JhbFNBQTZsd0pRaVVtOWFIeFBpTCsxVTR6R25xdTBvdUpaVUFJR3ovdktmekVJbmxuY1I4SkNYNW9HdjQ5M0VSdWlDVnp1NVFZWFl5M1RiVGFKTW0zVFM4VTVOZk9yVWJUT01HeFU3cXNvdGFIT1BSdjF1RDBnaTMvZElHampOMEJ6VUZROVZUTGt0ZkgwRHUzK2JiY3hDU1JCOHN1a0MrMlFPM3AwUHNBeE9Jdll4azl2K21jcG5LREJnYUVLUzN6d2FMS3RaVlRIMHBUZ2lPNWFySStTWm9XZ09yd1g3S3JjeHl0R3ovUVFOOUJyTk5DVk02NTVGZ3V5R2creEhnOWRSWFJDUkNTRDFpeUtqNlRsUnNlUTVkYkxaUEc1MHpHTGtCd3ROWnNCOXhEZWNxZGYrYW43U01lM0p4bXJpRXdncUlydEprQ25hMEFyY3JUTVhVMXBIRStxS25hSkhndjMvMHlENXkxV3BPY1RISFlxQ01YbmhWdks4VTFoN2U1WFVoWmlUOXBONlciLCJtYWMiOiJiMWY0NzkzODYzYmQwNWJkYjQ0N2ViNjY2N2E2NjdkYWI0ZjE1MWM4NzExNmU4ZDY1MzgzZjgxM2NlZWM0MzcyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImIxaHBSaGhVVTFHbDFvSHZHZy9yakE9PSIsInZhbHVlIjoiTGpBdm1janlMa0ppTk1VQ3VMSEtaSjRldDk2TGpKTjJ4NDY5NSsrQ3VDbDZSeDQ1aGNBQkI1MkhSaHNIK05kZDI5WlY3SmtraFExckllODAyZE1TQ1ZrRi80QWRQcXl2bklJUzhJb3dKQ0JqcVAyVjY1S1JySVRHUWhWUXNwdXQvUE5EaXZOT2pGQVZXblJjcEd1b1J4by9rTzFlYndoaFRqYXBmQXVwTzR2RnA0eG9qMCtpWEtTa1lwTnc4RHJaUUtEakJZcWlDcFFqdWJDYVo1QTR1WHM1dlhrd3AyTldjR2c2S1dHbWFpUExqNjNDK3N2a1BpR0hVNjREN3ZTdWN6WlQvTzVqa0xCV0Rnd1IzNldjTzI0QlczdlRjeWZYVVY1KzdHTlhJT3RXdXl3eDBLV09wVU1FTXpkcDVRcmxjTFc5RTRyMzA2TFRHdGhHejJ2UHFtSU9ZUUJYaDZvR0xub1VMLzhIWkl0d29Lb2pnR1RVWVlhcmlaNWhnYzUrYVJDcy9XQWtRK3NJWjVYdnR4SkEvL3hZZk5icWFxaXZJUzZYa0UwQVlZbC85amZhZXUyV0l1RWJiZDEzQ1djMFhlSVUrQ2JSdUg0bWZUQ2ZhdktpVU40WHRHYTBSWGxGVUpTeVQwa0dTa05QSXkzRmRqL2JPYUkrbjdYZnNsSWQiLCJtYWMiOiJhZTkyNmNjYjY3ZGE1NTA3Y2JmNjkxNjg0NzlkNmZmZDg0OTQwY2MwNDI2MzBlMjQ3MmM0Y2ZlYjUxZDY3YzExIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-937050535 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-937050535\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-250609191 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImRKWVR1VmJCcEEvcUVhK1ljN1FlQ0E9PSIsInZhbHVlIjoiVC9HUWNXQmduNEdEeUxkK0JnbXB6L2VmMnB2YWUxaE14dGJTVHlMak9icUk2K0E5WHpIaEwvaW85NTNCMXpGTE1PZXB2TU5OTm12UFQvdnhYNmxyeWZjWlJlUkgxR05XcnkzSW1qOEwzZlViWU1teWtCdGJrQTZaK2xLQm1oNmxFMmhlN0djRUZ3TlUrWk5OR2ZNeEtGMXFTMWRGNkxBZU5OR1JjQkxvWmlXZGJvdmZwNXBsMkdQb3BhQkR4WVVNa2I1SGQrK3BwNDBxQllLa3FqUGpiZlBtV1QvYXhmdDZTR21nL1dzblM5cGUxVkZRRU5EU3BGL2E1cEtld2NYVy9YOHA4RjJOTENOQmJaMmFIbE9ZQTJRS3pIOFJQVkZkYXVaSkdQclZZbHJxU1l1bUpGbytSU2swK2hVTFllSDh4QitIYnZzRTNqK3ROcXlFZE1FVnVPWFE5eE12bEJFRGdJTkRNMTQ3ZktQc0RTV1hGOG4rcTRabDNEam52cTlPa0I0QmtqanpabzdDTDVpdU9RSnZsemt0NVYxb0xEamdYYjJkaTBVVlFEbXdzemZsUE8vdG9iR25Wc0FyS2NjQzFwc3g4ZUM1OFdqd2NQWWFTbUJVVFVwWDMvelpSdlZ6b2I2MFlrdERTY2ZFcGwxdWFXa2FnZHhiWlRUQlNKYXEiLCJtYWMiOiI5NDVkOWNjNjlhMTQ4ZmViMTVjMmMzNmRhYTQyNzUzMGYwOGI0NjZiNzA3YzA0MGVmYjkyNzc5ZTZiZGQ1Njg0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdPSWhDQ1RIclU1L1F6eXFXTGFKZGc9PSIsInZhbHVlIjoiYnRPbnRCaGZIY3FTZzFoa1BwOThLSkVxSWRHNXRaSitXakh0QmIzRU9OUE1mUXl3ZWM5SUZ4RTBNcW9aWUUwWFNBSEFYeStaOE1hZnBaV290SndRVWpFY2hEYzRMMjNSZlczRWZycTZ5dnhnaGFReG9tQkNTZ2VUeGpJS1BhMmVlcDJnYWU0UWxmMlQrRk1LOEZ6dDBnbDV4dG1OdE4zY0M3a0Z3YlF0d1dIYUwvNk5qVHlkUFA1TDZLdVVER2kxNEVLQWFiRGVNUkJ2ZDBhTXVGdjJoZ0IwdTl3dTg3Y3hSajRkVDRYUUpDcmlFdGJaUHJqTWd3TENla2Qxc3UvSFczMUJSY3g5ZDcxaUEydUJiQS9XMkFzRWxMTWtUR1ltOHVLT0pMMy9ZNTFHeENHaHRCaVlKajN1UXRYVCtaT0czSzVWeEsveFpaRFJETUNKUWp1R0x5U0JEeHpURzVseG0wdkZ4REZpbm1VVmc4VVgxdk9DVVlDekQ4NDQvdEtZRktPK3d2NUF2dWhPMnNVOEdZL0VrY1VOWHFBVXlmZ211NmNhQlJucURpL1Fqa1IrY1VqaE9uYmp4eHJnd0tnT08xZjQyRnRnMnhTWG42M005U1p0ZW41cEc2TmpxaiswVk8xcTlXVDI1cGlIcjBnTU4rNUt5WkFrbXU5OGVldGYiLCJtYWMiOiI1NTA2ODgzNzdkNmJiYmRjYWZkOTc4MTdjYjczY2VmMmU1YWY5ZjM5OWJjNmY4M2U4ZmYzZWVkOWVjMDlhZjQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImRKWVR1VmJCcEEvcUVhK1ljN1FlQ0E9PSIsInZhbHVlIjoiVC9HUWNXQmduNEdEeUxkK0JnbXB6L2VmMnB2YWUxaE14dGJTVHlMak9icUk2K0E5WHpIaEwvaW85NTNCMXpGTE1PZXB2TU5OTm12UFQvdnhYNmxyeWZjWlJlUkgxR05XcnkzSW1qOEwzZlViWU1teWtCdGJrQTZaK2xLQm1oNmxFMmhlN0djRUZ3TlUrWk5OR2ZNeEtGMXFTMWRGNkxBZU5OR1JjQkxvWmlXZGJvdmZwNXBsMkdQb3BhQkR4WVVNa2I1SGQrK3BwNDBxQllLa3FqUGpiZlBtV1QvYXhmdDZTR21nL1dzblM5cGUxVkZRRU5EU3BGL2E1cEtld2NYVy9YOHA4RjJOTENOQmJaMmFIbE9ZQTJRS3pIOFJQVkZkYXVaSkdQclZZbHJxU1l1bUpGbytSU2swK2hVTFllSDh4QitIYnZzRTNqK3ROcXlFZE1FVnVPWFE5eE12bEJFRGdJTkRNMTQ3ZktQc0RTV1hGOG4rcTRabDNEam52cTlPa0I0QmtqanpabzdDTDVpdU9RSnZsemt0NVYxb0xEamdYYjJkaTBVVlFEbXdzemZsUE8vdG9iR25Wc0FyS2NjQzFwc3g4ZUM1OFdqd2NQWWFTbUJVVFVwWDMvelpSdlZ6b2I2MFlrdERTY2ZFcGwxdWFXa2FnZHhiWlRUQlNKYXEiLCJtYWMiOiI5NDVkOWNjNjlhMTQ4ZmViMTVjMmMzNmRhYTQyNzUzMGYwOGI0NjZiNzA3YzA0MGVmYjkyNzc5ZTZiZGQ1Njg0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdPSWhDQ1RIclU1L1F6eXFXTGFKZGc9PSIsInZhbHVlIjoiYnRPbnRCaGZIY3FTZzFoa1BwOThLSkVxSWRHNXRaSitXakh0QmIzRU9OUE1mUXl3ZWM5SUZ4RTBNcW9aWUUwWFNBSEFYeStaOE1hZnBaV290SndRVWpFY2hEYzRMMjNSZlczRWZycTZ5dnhnaGFReG9tQkNTZ2VUeGpJS1BhMmVlcDJnYWU0UWxmMlQrRk1LOEZ6dDBnbDV4dG1OdE4zY0M3a0Z3YlF0d1dIYUwvNk5qVHlkUFA1TDZLdVVER2kxNEVLQWFiRGVNUkJ2ZDBhTXVGdjJoZ0IwdTl3dTg3Y3hSajRkVDRYUUpDcmlFdGJaUHJqTWd3TENla2Qxc3UvSFczMUJSY3g5ZDcxaUEydUJiQS9XMkFzRWxMTWtUR1ltOHVLT0pMMy9ZNTFHeENHaHRCaVlKajN1UXRYVCtaT0czSzVWeEsveFpaRFJETUNKUWp1R0x5U0JEeHpURzVseG0wdkZ4REZpbm1VVmc4VVgxdk9DVVlDekQ4NDQvdEtZRktPK3d2NUF2dWhPMnNVOEdZL0VrY1VOWHFBVXlmZ211NmNhQlJucURpL1Fqa1IrY1VqaE9uYmp4eHJnd0tnT08xZjQyRnRnMnhTWG42M005U1p0ZW41cEc2TmpxaiswVk8xcTlXVDI1cGlIcjBnTU4rNUt5WkFrbXU5OGVldGYiLCJtYWMiOiI1NTA2ODgzNzdkNmJiYmRjYWZkOTc4MTdjYjczY2VmMmU1YWY5ZjM5OWJjNmY4M2U4ZmYzZWVkOWVjMDlhZjQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-250609191\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-785967113 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-785967113\", {\"maxDepth\":0})</script>\n"}}