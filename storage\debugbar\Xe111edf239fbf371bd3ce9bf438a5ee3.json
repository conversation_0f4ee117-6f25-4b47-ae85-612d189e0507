{"__meta": {"id": "Xe111edf239fbf371bd3ce9bf438a5ee3", "datetime": "2025-06-27 02:15:36", "utime": **********.462371, "method": "POST", "uri": "/payment-voucher", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990535.994239, "end": **********.462392, "duration": 0.4681529998779297, "duration_str": "468ms", "measures": [{"label": "Booting", "start": 1750990535.994239, "relative_start": 0, "end": **********.34434, "relative_end": **********.34434, "duration": 0.3501009941101074, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.34435, "relative_start": 0.3501110076904297, "end": **********.462395, "relative_end": 2.86102294921875e-06, "duration": 0.11804485321044922, "duration_str": "118ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45925728, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST payment-voucher", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@store", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.store", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=68\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:68-89</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.05699, "accumulated_duration_str": "56.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.374302, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.737}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.383832, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 2.737, "width_percent": 0.737}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.387691, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:74", "source": "app/Http/Controllers/PaymentVoucherController.php:74", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=74", "ajax": false, "filename": "PaymentVoucherController.php", "line": "74"}, "connection": "kdmkjkqknb", "start_percent": 3.474, "width_percent": 1.018}, {"sql": "select * from `warehouses` where `warehouses`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.390832, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:75", "source": "app/Http/Controllers/PaymentVoucherController.php:75", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=75", "ajax": false, "filename": "PaymentVoucherController.php", "line": "75"}, "connection": "kdmkjkqknb", "start_percent": 4.492, "width_percent": 0.579}, {"sql": "select count(*) as aggregate from `voucher_payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 77}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.392519, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:77", "source": "app/Http/Controllers/PaymentVoucherController.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=77", "ajax": false, "filename": "PaymentVoucherController.php", "line": "77"}, "connection": "kdmkjkqknb", "start_percent": 5.071, "width_percent": 2.614}, {"sql": "insert into `voucher_payments` (`date`, `payment_amount`, `pay_to_user_id`, `purpose`, `payment_method`, `created_by`, `custome_id`, `warehouse_id`, `shift_id`, `updated_at`, `created_at`) values ('2025-06-27', '1400', '22', 'f', 'cash', 22, 'PUR-المستودع الرئيسي-20', 8, 45, '2025-06-27 02:15:36', '2025-06-27 02:15:36')", "type": "query", "params": [], "bindings": ["2025-06-27", "1400", "22", "f", "cash", "22", "PUR-المستودع الرئيسي-20", "8", "45", "2025-06-27 02:15:36", "2025-06-27 02:15:36"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 86}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.395334, "duration": 0.05261, "duration_str": "52.61ms", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:86", "source": "app/Http/Controllers/PaymentVoucherController.php:86", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=86", "ajax": false, "filename": "PaymentVoucherController.php", "line": "86"}, "connection": "kdmkjkqknb", "start_percent": 7.686, "width_percent": 92.314}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "success": "تم انشاء سند الصرف بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/payment-voucher", "status_code": "<pre class=sf-dump id=sf-dump-1600765632 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1600765632\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1757687064 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1757687064\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-245532180 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-27</span>\"\n  \"<span class=sf-dump-key>payment_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1400</span>\"\n  \"<span class=sf-dump-key>pay_to_user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n  \"<span class=sf-dump-key>purpose</span>\" => \"<span class=sf-dump-str>f</span>\"\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cash</span>\"\n  \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>custome_id</span>\" => \"<span class=sf-dump-str title=\"23 characters\">PUR-&#1575;&#1604;&#1605;&#1587;&#1578;&#1608;&#1583;&#1593; &#1575;&#1604;&#1585;&#1574;&#1610;&#1587;&#1610;-20</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>shift_id</span>\" => <span class=sf-dump-num>45</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-245532180\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1278076113 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">131</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; XSRF-TOKEN=eyJpdiI6IitiRnJrMFIxcWRPUmwvdDNZNmlmbGc9PSIsInZhbHVlIjoiWEdJeCtKM3dXbE9yaHlJUmcxUDZFb3kzd1Awd1VGRURpNFdkOExnUUFIWG9BaWRvRjRHeGRsa253S2UvamRVUlo4QnVyNjhuNGF6QlRGc0F2aGtnNGdoMGlzbVlHaitPR0NoOXBDamVPd0UvSWFhQ3YyL1RmWGM3b3lkYXBzNUtSN3VYbmsyZDlDUDdkb2o0TG1odWsva2xTclFVdGV4Ujd5Q1ZDWi9nMHpvUFJXUksrWmJCOFJHZVBqenBDNGNaWThoM0FGRGtMNmhPSnpzU2pKUHF5SGg5VUdWS0FkMHFwdXNFZG1MZmFFL3JvVTZsZzBraGlrK2hsMnAvUXpTUHl2TE1JMGc0bWcxUWVOWkd6WVV4YXFNL203SG5WcFBVWDBkNWFIOGJ4SWpnTitheVlrWXZHMk1Qd2NsVkd5UklzQ2M0SkhWWHZJTm9tWFNIa0JIM3ROZDdEVlkvMllFRzRRQmJrNHRBemdzaW9uaDRHb3k0VTRjcDNTZW1IdmRFdTJaNkFUSkFtVXlPSVovMVNOVEZMclFWaTJrZ0ZZbHZuN0FQbzZ2SktMSURqRGoxNWRkUmx4NzVEdlBXb3B2T2I4Nk9YdHVyNld3UVBKd09PM1RXTXEyQ0dnNXpwTjlkUWFmQi9zRDAzNE1wZDh0Qzhtd0V3Y3NPOTdSVi8xd2siLCJtYWMiOiI3NTViODM2OWFlYmEzYmMwOTIwZGFjNDYzMGQxNTdlMTdlZDI3ZDc2MDdkNDVjNDAxOTcxYzkwZWM2Zjk4OWYyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InQ3c29wUjQyL3Nkak9KejJReE12SGc9PSIsInZhbHVlIjoiMXB6TTd6eElqcEROQlJCK2l0VG4yOWc5eStYSFlNUjRRRkVpOFQ3bFJZWWFCSTBSR3lwNjRBaUxPUnVBSDhZT0tvMnR3dGs5TFJlanVEWkxiSDhpamxjZ25IV2RHTGxKd2pnNytpQ3NaMFBzdFdSVlV4eWxzYkw4bVNoM0VqU0VzZlloSUd0RVBJNkM0T2VpY0xFVGlhWktYVXZIZGN1Unl0dzJ2TDhVYmpINmVWU0RJVFdubENnbDdmc2RhWW5IaGZ4cHZhdXBhNXpabytGL05WZVM4alhkaUd5L3plNXl2b29GRGdZaFdVK2pmMFpFVzZFZnhtL2xNK0tzeW5WaHlZYVF5RGgxS051MEhxUjF2bUxaeTJzTUhpZEF2eWIvNjROeUR6QlVxKzFYN1J0bW1td0NWM3lzUUxzMExLOG1xemVlUFhxWGhFTlJwMm82VW5VbmJoUS96L2JNVnAvTjlaS0kvcUswVnZxZytCVXQ1NkZVR0h6MEdUbTVDdTg5eWRmLzJUdzVkeFV1ZGxLYzBBY0toaUpUdFBmRG1hMnJud2JFcVMxL0lXRFNQQmVWN2JBS003RmxxLzdYMDJhSi9qRnVDSUhlTFRFMkJqY3l0dk1SSmsxM3BLWDlwMzRuRi9MQzRua2hFem1mUUhicU1zeGxXQUIrRDNoalR4VWMiLCJtYWMiOiJlMzc1YmNmYTMzZTRlNWM0OTQwODY5YWU0NmY2MWJhZTA2MWIxYzA2YzNkOTBiNWJkZDExYjkwZmM1ZWZkZDhiIiwidGFnIjoiIn0%3D; _clsk=1wa64lz%7C1750990513464%7C10%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278076113\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-142477126 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-142477126\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1642729315 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpXazVPMitWdmdEZ0VZUmhMT2FFYXc9PSIsInZhbHVlIjoiUmdFb3pQanB0THRuVmdyY2Exb1UyM3d6ZmdMMXpUSVpmQVlSbkgxMytvQmIvMlBqTFl0bE1ac1lCa2RDTUtSOStuUFovN1hrMWovY3AvSEFOQWlhTjlXYVhycTJGV2dsTkY5UDhpeDMwTnFXWGZuVjhVZUU2WTNia3Q0WWtSdkpaTkhoMkp6ZGtZM09TTHMyRGZlSHRDUEdYbWNOUFJ3UFhEaENoblVKaGdWUzdwdldlc1VUUEpPK2tMaDZFWUVGY3Q5dzF4YjBGZTMvRDBqbmplNzRPeDgwOXNCUDlZNWljYnVuQnJtaFFxbE54MG9wend4MExIRTNkaGpIOUlhV01SVkJ5OVVFN0ZOYS9hRFdXbUgxU0JPYk9jUDZEU0FNQnowK092SGVRZTdQZWFpQzFuVnpmb1MwY09BR242VE9maVBRaHhXQmxwdnVxRXJTSU5NTFRkY0M2WFNUS2xIc2VzbDB2bUQ4VEFPNjQvZGhkZFZyTlRtb1NkNldSSUJPdVA0WnBCWURGYmlxRHlzVGZWekk2K05aTURka0VtRW1RMS9RcFR4TFNqU0pnUmJ4R3BJdEdXMzFPa0tnVkptaW9TY0xaMkgyajRpQmM0SU4waS8vc3VkeVhISjM3NkVqWi83Q0ZiMjNvakZYWTViUnpWZjZxZFJmU1ZoQjlKQjgiLCJtYWMiOiIyMjhjN2JiYTZkYjRiNTA0NzgzNWU0YWM3OTRkZTljMTRjYTgyN2IzNDU5MDA3ZjZjNWEzMWJlMzM5YjU5YmNhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikp4ZS84b1pvQitrcU84b1FtTjVWcXc9PSIsInZhbHVlIjoiRjdJdk9Wd1JpbEVCbE9MdE1CZEtNdmRMV2NyOHZ5NldsMVhvallZSWg1VTg4YjdiM2NqSUg0L2EvMWJRMWhDa2ZzWUY1UkswcE5ma1VqYm9SYTdySDJYU0hZd25RL09JM1VzNWRXUDArN3ZvL1lvbEVxbWlzK3hyRXltWVVNaWhqdkFKYkxOTFhpbWpTWnlEQkFmR0xNSm1mTCtSQ2pJbTUvT2s5QkUrSUV3aTBvMXVvY3FZMkgwWEN0VEZveTNORjdjS0w5WmF0Q3B4VTNNVjNrdE9wRnpldEJxR1o0T2d0S0t0clA1N3grWXltd0dycmtYdXBlVkQ2by9PUitZbXRlbWdRZHZBamwzVjR5VlQ3OEhidHRJQmhpSS9kRXNUUmQzeUFITnFMNU9PcllDK2FMbkx0Z2w4ajk2VVBCUGFXQkMwdENIZ2xqUUtORzlOMWc1Q0RtcXI1T1VjS0lZU242V3BUbFhSbktzVk03MVk2YUdzUVRrZkIzb2JuaXoxc2ZIRFhHd2tleDMwL0Z2Y1lCTzYxaFZkWS9yaGJwdjloTm56dHdsN3k5Tm5nUmZsMm9DTjN1VVNkdWt5WmV0bTJITU5ieHhyVHFBeHpucnYvZHdtQXArSmpsTDViM1B6T2gyN3RocHl4QmsrZWVMTVBTLzNiUVpRWkdQeGFTQnIiLCJtYWMiOiJkZDJlYTEzYTA4YTAxNDRjNWY2NzA3ZTdlNzZiNTQyZDY0NTk3OTAwYzI1OTM5YjdiZjg4MGU2ZGJkM2MwNzdmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpXazVPMitWdmdEZ0VZUmhMT2FFYXc9PSIsInZhbHVlIjoiUmdFb3pQanB0THRuVmdyY2Exb1UyM3d6ZmdMMXpUSVpmQVlSbkgxMytvQmIvMlBqTFl0bE1ac1lCa2RDTUtSOStuUFovN1hrMWovY3AvSEFOQWlhTjlXYVhycTJGV2dsTkY5UDhpeDMwTnFXWGZuVjhVZUU2WTNia3Q0WWtSdkpaTkhoMkp6ZGtZM09TTHMyRGZlSHRDUEdYbWNOUFJ3UFhEaENoblVKaGdWUzdwdldlc1VUUEpPK2tMaDZFWUVGY3Q5dzF4YjBGZTMvRDBqbmplNzRPeDgwOXNCUDlZNWljYnVuQnJtaFFxbE54MG9wend4MExIRTNkaGpIOUlhV01SVkJ5OVVFN0ZOYS9hRFdXbUgxU0JPYk9jUDZEU0FNQnowK092SGVRZTdQZWFpQzFuVnpmb1MwY09BR242VE9maVBRaHhXQmxwdnVxRXJTSU5NTFRkY0M2WFNUS2xIc2VzbDB2bUQ4VEFPNjQvZGhkZFZyTlRtb1NkNldSSUJPdVA0WnBCWURGYmlxRHlzVGZWekk2K05aTURka0VtRW1RMS9RcFR4TFNqU0pnUmJ4R3BJdEdXMzFPa0tnVkptaW9TY0xaMkgyajRpQmM0SU4waS8vc3VkeVhISjM3NkVqWi83Q0ZiMjNvakZYWTViUnpWZjZxZFJmU1ZoQjlKQjgiLCJtYWMiOiIyMjhjN2JiYTZkYjRiNTA0NzgzNWU0YWM3OTRkZTljMTRjYTgyN2IzNDU5MDA3ZjZjNWEzMWJlMzM5YjU5YmNhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikp4ZS84b1pvQitrcU84b1FtTjVWcXc9PSIsInZhbHVlIjoiRjdJdk9Wd1JpbEVCbE9MdE1CZEtNdmRMV2NyOHZ5NldsMVhvallZSWg1VTg4YjdiM2NqSUg0L2EvMWJRMWhDa2ZzWUY1UkswcE5ma1VqYm9SYTdySDJYU0hZd25RL09JM1VzNWRXUDArN3ZvL1lvbEVxbWlzK3hyRXltWVVNaWhqdkFKYkxOTFhpbWpTWnlEQkFmR0xNSm1mTCtSQ2pJbTUvT2s5QkUrSUV3aTBvMXVvY3FZMkgwWEN0VEZveTNORjdjS0w5WmF0Q3B4VTNNVjNrdE9wRnpldEJxR1o0T2d0S0t0clA1N3grWXltd0dycmtYdXBlVkQ2by9PUitZbXRlbWdRZHZBamwzVjR5VlQ3OEhidHRJQmhpSS9kRXNUUmQzeUFITnFMNU9PcllDK2FMbkx0Z2w4ajk2VVBCUGFXQkMwdENIZ2xqUUtORzlOMWc1Q0RtcXI1T1VjS0lZU242V3BUbFhSbktzVk03MVk2YUdzUVRrZkIzb2JuaXoxc2ZIRFhHd2tleDMwL0Z2Y1lCTzYxaFZkWS9yaGJwdjloTm56dHdsN3k5Tm5nUmZsMm9DTjN1VVNkdWt5WmV0bTJITU5ieHhyVHFBeHpucnYvZHdtQXArSmpsTDViM1B6T2gyN3RocHl4QmsrZWVMTVBTLzNiUVpRWkdQeGFTQnIiLCJtYWMiOiJkZDJlYTEzYTA4YTAxNDRjNWY2NzA3ZTdlNzZiNTQyZDY0NTk3OTAwYzI1OTM5YjdiZjg4MGU2ZGJkM2MwNzdmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1642729315\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1317418740 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1578;&#1605; &#1575;&#1606;&#1588;&#1575;&#1569; &#1587;&#1606;&#1583; &#1575;&#1604;&#1589;&#1585;&#1601; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1317418740\", {\"maxDepth\":0})</script>\n"}}