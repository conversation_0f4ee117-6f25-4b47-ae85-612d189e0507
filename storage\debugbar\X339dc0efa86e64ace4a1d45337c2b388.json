{"__meta": {"id": "X339dc0efa86e64ace4a1d45337c2b388", "datetime": "2025-06-27 01:04:06", "utime": **********.09949, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750986245.665623, "end": **********.099505, "duration": 0.4338819980621338, "duration_str": "434ms", "measures": [{"label": "Booting", "start": 1750986245.665623, "relative_start": 0, "end": **********.040604, "relative_end": **********.040604, "duration": 0.374981164932251, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.040613, "relative_start": 0.37498998641967773, "end": **********.099507, "relative_end": 2.1457672119140625e-06, "duration": 0.05889415740966797, "duration_str": "58.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00335, "accumulated_duration_str": "3.35ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.07425, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.358}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0860739, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.358, "width_percent": 16.119}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.092056, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.478, "width_percent": 15.522}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IjNZY0FqazROYlRrdW1tbG94NXYxekE9PSIsInZhbHVlIjoiYWYzN0dCdjhKWFhMd2Q5aHhJUU5Ydz09IiwibWFjIjoiNTE1MjNkZjQ4YWFkMmUzMDkyZjEzODA3MjBhZDg5ZjM4YTg5MTJlNGZiMTRiYTgxNGZhOWJjNGQzMWRmNDE5NSIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2121254292 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2121254292\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-114528074 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-114528074\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-142468001 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-142468001\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2045252926 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IjNZY0FqazROYlRrdW1tbG94NXYxekE9PSIsInZhbHVlIjoiYWYzN0dCdjhKWFhMd2Q5aHhJUU5Ydz09IiwibWFjIjoiNTE1MjNkZjQ4YWFkMmUzMDkyZjEzODA3MjBhZDg5ZjM4YTg5MTJlNGZiMTRiYTgxNGZhOWJjNGQzMWRmNDE5NSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986241982%7C76%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRhaCtPR1VYR2U1WmM0YkZWS3R3NFE9PSIsInZhbHVlIjoiNktYZmx1ZTBLSjFBNGJQb0tHWjgyb1BOc1pOcWdZSnRDc0xaM1ZjczVtR0RicTlGRFEzMVdjSHNWZXprM2w2ZHA3R0RMRFp1MTh3QUc5MWhESU1yRCtFUEk1eWpRc0w0dWMrTTc1enlCVzhYTlBMTXBqZmxMMzhlbFpNeEhmSnoyMFFhQ0MyaWFVeXRMRTU2Q09WQnZOSERYQldiKzRkN3J4NC9aODlQV01XRUErc0VyN1NwYVNsaE12OVRTMWtXQ3ZRd1IwSnRWdlZpbmlacVhnU1UybjZZaEZwL0p2YXFqL3FjbnZjVnVSZm1sS0FyY21BblJGeGNJWXhTWVZqRktrOCs0cWhyZ05YbmFoZTc4dzdWMVdkbnRsMzhQclJ1ME5Mczc3MUxQTkQwempFMnU1WGNwV1A4eHhiUXpXYlE4ay9JWGdNV3V4Uzl5RU9Hd2ZCenE4ZmFNMGpBcmhWU25Pb2VBQU9ZKzVoNlhpVGMvS2E0RnlaOWMzS1BTb09HdUxkMEt2bms0R0lvbnVCSUVVZ1BIYzJrVEd4ckNrRmEzMHV1ODhPRjYrcDRDZkppVGVBT0dDY21hY216UVc3aWtXT0ZKdnVTMWlaZ0h0cTZVeUdPb3pyYUZBK0ZIQzZjMVNackNHWG91YmIwRXIrVm1KVzNXVFVuaVp6akIrb0ciLCJtYWMiOiJmNTBhZDYzY2ZjZTQ4YmIwY2NmYTc0MWQzMjkwMDk1NWUyYTkyODUyZjliMTdhOGQwODQzM2RhZDhhYTliZTFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJjaFN3aml5b2NjZGtxWTB4UmVudGc9PSIsInZhbHVlIjoiNHRKeEJGb2o3NDhRNFdaM2tUbnMwZDJnbHZPQ1NGTFZEcGw4eVBvRU0reFFIQi9HNGlkTDd5WmZwNkgwcnY5MThFVjFtUGltT1RTc2lobUMzOVVOQ3ZwYkJIR0d4M05JUENMWVlzZFN1ZFZ4SHp2dnMrS1FuQ0dOdkVZVEM5Ny9abHlNcm1iVzRWbzJ3Sm1ZL0NOc2pHa2hIVXh2eUd0ckdlTnVZZlRlSUZqUXVOV0kzTnZmSThNeDhuL2tFNUJLME8xdkdjTTluU0hGRk9ncjZmaVBjV1VlUGZGc1B3SHNFUnVjZFpSK05wMTVNaG9RMnZBWlVqeFFISjlRM1U4SmpiZlFyeWRBZFQ4cTU3YkZ2SDU2Y0lSUVQ2Q1BsNHU2MitqN1Jhd3pUSFMwNVdTUkdHdkoxa0JwRjdiQkIzK1I2MlJJZm1MWXR5SEYxc0dqNWtScitlSzk5dGlrOHNIS3NENXB3VkpoK05BclRQNXB4eFhib3hETTdYcHVSa05zOXVDMWQzaVB1QVh5SGFLQmFPdjdwMTB3QlB0Vlg0ZTRSZGVnOUhuQlM5NUpPem5KLzZPVU5hVXc0Ni9BeWh5K2JwYjZpVWlOTkNRb3VxTjcrcjF3T0lZWkhFcXpZZHpoTUI4ZVZabk1KMko3QnBlRUtDQ3V0ZVVjTmx0Qzc3dUsiLCJtYWMiOiI4MmQyNjI0MmQ0YTFlZTBkNmMxMmIxMDk1ODYwMDE0MDEyNmM3YmU3ZDJiMzhmM2Y3MWYxOTlhMWI1MTZmNjBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2045252926\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1779859736 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:04:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijh4VzZtS2NZOU9oRFNGSjZvZ2h3a0E9PSIsInZhbHVlIjoiUWVpdjJJOTltbS9SZ1NhcERQeGxGQWhMVFdSc0tiK3k5VThMSHkyWmM5a2hNUlUrNVA0V2hMd2E2dGNUNTVsTVB6K1VVZkJqLzFkamN0bC9uVWYzNWFtbHphcE9vN3RpWFFlK28wd0FEWFFNQWNJYWxrZHFPZWwycytyWmdjdjZMYS9QcXdpSVQ2SGhjZU5RbytlRkgvay84MDJESlViQmpvWk54a1JwbWVmVndsS0ZrbmFSaWtFOTZKQkNjRkVVU2QwbjRtdE9EN0xoc3pleVhZYmo3ZmM1MEh5QkJHWTRLWWU1TDQwOEMvVi9IRTllN1lPekxwV28xZ3pPN0gyK1N4enR2cEJEa1hXVXU5Rkk3dGpXci96aEpESUE0YXFRRWszcVQvd25rWjk4NnVyNGgyVzUxV2xod0VwVk85NXZ3L2lrOG9sUXVHcGk0dm9QUWZJNW9PTitYSEJNKzZlenNlVUlNWHcyaEtKQnZOMmJhcHdteEFvR2xKUi9zQURtbTlkb1ovQmFhSGZFd240VW1KT2RmMHc1dWNnMUdtU201ZEtvTE1mdWxwNGtNWHFiUE9nZlBGWlhSS3FGZVd2VVBtMVE1Nm9tRmVKQmZsaCtxUG1yK1QveGJyV0E2V3k0QjROV0dMckJ6VjVDdDNDTmZWYVRrNmduUiszQjRpYWEiLCJtYWMiOiIzNWY4ODVhOThiZTZkYjkwNjNjZTVkMWQ5MjZmZTZkYTczMmUwYjBiMWEzMWYxOGVkMDcwOTE3YTgxYjYxMDM2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:04:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlA3QVc0OURlcGVweDJsRGRZenJZSlE9PSIsInZhbHVlIjoiVGRsQmd1aVhoV0t6N3BvNDRROEdrc0E3cGNabURYbG84eUFkN0RCZ0tSSEFKVGNPdDdpbmpMMHBocCtvVGxzdFdLY2g3M3VycEs1QUNVd2xGRWdSMkJTclc1R0MxeUJVNlJPb2MwYWQ5WkZodE1ZWFR3TE5WMXAwMWxydC8xdTJHdFd2SnBTVzBuWjRuMUxyTXVlUGxKRldjQWhjVTJHK2dxTkhkNVZqWW5vUzlaUzZZeTlUb2F1WmxtYmMySG5SZUxRQVFWOGdBVGNMM3M1WTFSWUdiVlh4TW5SMUM3bVQySGl3REhuQW1hYkw5SW4zbjIvNVVrNCtVV3c0OGIvMUtSTWQ0VWh2c3BXV3VSZlR3WFR3bWRpUGZJd1g5R1psV1U1QXdYNk44eTZBRU52QzY4TFpkZW9HaU45Rk9wSlYyem5Oa3FMTEN0Q21HL1IwQ2d0dWJTSSsxOHJrWVZxclRxbHdWRlltV1UvS2pFaW92a2d5OU5JajRvVXJxZStUWG9pOWN4dUtPM2NvcUNrSmdUTkUyUFgxTktJbU1wUUtyKzZicXN1SzcxVysrY1dvclo0QllWVWp3RHl5aUExR2JBYldPR0ZPeVlSeVpaaUNHTlBENjJOUjV1NWRIY0swMVRyKzI4MlRRMDk4dkRSY040RGpjQVZISjIrQmt3WHkiLCJtYWMiOiI2NDMwMzc0YTZjZjFiM2Q2OGVjYTYyYmIyNWUxZmQ5ZjkwZDA0NGJhNTFiYzU3YTE4MWVjZjg0MjI0ODBiZTQyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:04:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijh4VzZtS2NZOU9oRFNGSjZvZ2h3a0E9PSIsInZhbHVlIjoiUWVpdjJJOTltbS9SZ1NhcERQeGxGQWhMVFdSc0tiK3k5VThMSHkyWmM5a2hNUlUrNVA0V2hMd2E2dGNUNTVsTVB6K1VVZkJqLzFkamN0bC9uVWYzNWFtbHphcE9vN3RpWFFlK28wd0FEWFFNQWNJYWxrZHFPZWwycytyWmdjdjZMYS9QcXdpSVQ2SGhjZU5RbytlRkgvay84MDJESlViQmpvWk54a1JwbWVmVndsS0ZrbmFSaWtFOTZKQkNjRkVVU2QwbjRtdE9EN0xoc3pleVhZYmo3ZmM1MEh5QkJHWTRLWWU1TDQwOEMvVi9IRTllN1lPekxwV28xZ3pPN0gyK1N4enR2cEJEa1hXVXU5Rkk3dGpXci96aEpESUE0YXFRRWszcVQvd25rWjk4NnVyNGgyVzUxV2xod0VwVk85NXZ3L2lrOG9sUXVHcGk0dm9QUWZJNW9PTitYSEJNKzZlenNlVUlNWHcyaEtKQnZOMmJhcHdteEFvR2xKUi9zQURtbTlkb1ovQmFhSGZFd240VW1KT2RmMHc1dWNnMUdtU201ZEtvTE1mdWxwNGtNWHFiUE9nZlBGWlhSS3FGZVd2VVBtMVE1Nm9tRmVKQmZsaCtxUG1yK1QveGJyV0E2V3k0QjROV0dMckJ6VjVDdDNDTmZWYVRrNmduUiszQjRpYWEiLCJtYWMiOiIzNWY4ODVhOThiZTZkYjkwNjNjZTVkMWQ5MjZmZTZkYTczMmUwYjBiMWEzMWYxOGVkMDcwOTE3YTgxYjYxMDM2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:04:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlA3QVc0OURlcGVweDJsRGRZenJZSlE9PSIsInZhbHVlIjoiVGRsQmd1aVhoV0t6N3BvNDRROEdrc0E3cGNabURYbG84eUFkN0RCZ0tSSEFKVGNPdDdpbmpMMHBocCtvVGxzdFdLY2g3M3VycEs1QUNVd2xGRWdSMkJTclc1R0MxeUJVNlJPb2MwYWQ5WkZodE1ZWFR3TE5WMXAwMWxydC8xdTJHdFd2SnBTVzBuWjRuMUxyTXVlUGxKRldjQWhjVTJHK2dxTkhkNVZqWW5vUzlaUzZZeTlUb2F1WmxtYmMySG5SZUxRQVFWOGdBVGNMM3M1WTFSWUdiVlh4TW5SMUM3bVQySGl3REhuQW1hYkw5SW4zbjIvNVVrNCtVV3c0OGIvMUtSTWQ0VWh2c3BXV3VSZlR3WFR3bWRpUGZJd1g5R1psV1U1QXdYNk44eTZBRU52QzY4TFpkZW9HaU45Rk9wSlYyem5Oa3FMTEN0Q21HL1IwQ2d0dWJTSSsxOHJrWVZxclRxbHdWRlltV1UvS2pFaW92a2d5OU5JajRvVXJxZStUWG9pOWN4dUtPM2NvcUNrSmdUTkUyUFgxTktJbU1wUUtyKzZicXN1SzcxVysrY1dvclo0QllWVWp3RHl5aUExR2JBYldPR0ZPeVlSeVpaaUNHTlBENjJOUjV1NWRIY0swMVRyKzI4MlRRMDk4dkRSY040RGpjQVZISjIrQmt3WHkiLCJtYWMiOiI2NDMwMzc0YTZjZjFiM2Q2OGVjYTYyYmIyNWUxZmQ5ZjkwZDA0NGJhNTFiYzU3YTE4MWVjZjg0MjI0ODBiZTQyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:04:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1779859736\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1491389109 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IjNZY0FqazROYlRrdW1tbG94NXYxekE9PSIsInZhbHVlIjoiYWYzN0dCdjhKWFhMd2Q5aHhJUU5Ydz09IiwibWFjIjoiNTE1MjNkZjQ4YWFkMmUzMDkyZjEzODA3MjBhZDg5ZjM4YTg5MTJlNGZiMTRiYTgxNGZhOWJjNGQzMWRmNDE5NSIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1491389109\", {\"maxDepth\":0})</script>\n"}}