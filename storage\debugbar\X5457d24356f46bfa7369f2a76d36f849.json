{"__meta": {"id": "X5457d24356f46bfa7369f2a76d36f849", "datetime": "2025-06-27 02:15:46", "utime": **********.157556, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990545.75767, "end": **********.157577, "duration": 0.39990711212158203, "duration_str": "400ms", "measures": [{"label": "Booting", "start": 1750990545.75767, "relative_start": 0, "end": **********.104059, "relative_end": **********.104059, "duration": 0.3463890552520752, "duration_str": "346ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.104069, "relative_start": 0.34639906883239746, "end": **********.157579, "relative_end": 1.9073486328125e-06, "duration": 0.05350995063781738, "duration_str": "53.51ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45736832, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029300000000000003, "accumulated_duration_str": "2.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.130638, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.799}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.141246, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.799, "width_percent": 18.089}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.147528, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.887, "width_percent": 19.113}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1987902253 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1987902253\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1578614037 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1578614037\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-871448761 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-871448761\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-114860895 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990544006%7C14%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5UcHJiLzkrQjRBUkJuMlFxMmx0Y0E9PSIsInZhbHVlIjoiOGRub2s5b3VhdTN0WmR1NnYrRXJkUzlTVjYyR3lkMTdnYVJiSDNWTVJkUWVaNUlabzRRSFFaRmlETzhhNHlBK3k3Rm1FbS9jb08vK253WTRvREF4TjBpcmJZbUdVUjVRbU1BUjFXNUl0aWJUUnh5Z1ppT3Q4aXV1K1VreE52S1VkNXdHdVBOdnpLcmZCd3VXSWFFQWJWM2NTZllSS0JBRzRkN0RSUldSQzF5Q0FhWkZLM2syZzdla05kdHVaUzVzTUozVkljZEhOUWlhUnA4WEFnK3Y0UXBkQ1lzN1FBYW9GMUlxeUxySnZoZ2hKMm5XdWRsdjNZZ25XR0dCMnJSMkJWY1Q3SnN6cG9qUWhuMEJjRXg0cXVhdEp1ZklnN0I3TGxoSjZFajc5cm1LNzJmS2p6Tlg2VStOMTJ0QURrU1hnWm1vVnZ3bmxFUHVVNkN1RlphMFJVcndPaDF6eXdNQ0JWTzlmeUY2TW9qaldiMUpab2FBMXNNK3RLdXpLWDU2akYwUnF3UnREZmJaV2I4eElod1BweDJleG9sdm1XSk95NXY4bTQwTEdtSTVUdFNFcmpqcExGemVja1pUK3pZUEQyZ3g4R1V4akNTVWNCbDhJdVc5RSs0VXpTeC9iY21UOUplVURoYUJjSkQ3QzBvdGs1ZHBtY0hpNDVGWHliZkEiLCJtYWMiOiI0M2NmMGFmMGI5MjU5OGM2ZjliZWEzYmQzMmY4NmZiMjZmNWFhNGI3NWI0Y2Y2MmRkNmQ5YTY1MDNhMDZiNmJhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlAxbmdRdXJCOFErcnhML0FqeC9pdUE9PSIsInZhbHVlIjoiYUtUZ1hZbEx2ZzU2MjNFNUpIcUtaM1BOamdtTCt3SSs4bStwdmljYzJMcCszdmI0L0JhbDJjZzU0aVlWOVJUUzJySnVmeWxTaFN6cStyalNkckZ6V05CNkxhc1VjUGJGbTVoYklLSUJjdkNHZUZkdGxlMDVVNkxGcXVOQm1qb0FwZUt0VlNac0VHcDYxRHdaQmhDbEw4MzRSMkVKY0tnZXllYVhyRWRtMjh5MHI1dG5OU29aOUkvd0RpR2NGY0gzSEhhQUx3ckRlaVpWb0YxcmFZb2NUbVF3K3ZtNitibDM2YjZ6RzE0U3d6ZUNtT3kwekdLM0NnUkIyc0hjQXd2RlNsMkNLblkxMU5LNEZLbXdCNmhweWc0L2laR2I4UVVBby9WQTRkdmtvQVFPMCtVZDdya3BWbnp6RGg2ZnN3VlBqNUNQaEVMZW55d0RWSTVMdVpPZmxiWG4yTGxtcGtkdWtJZUtjN20xRG5sQ3I3em83QmJqS1Z2bm41eGxJcGFERDRYYzhqeHMwT1JCOHJ0MExoZk5DZEROK25OREdLVS9hTkxYdkhDNFhVclo1VVg4cUU1R1dRM1hTbUh2OEtjaU9zWUZQdVppUytUVVNEbDNGYlVSRUZPMHVnUmhKdkRlaWgvVVlHRlRLUGxaNThUL3ZGL1dUTVhXOXF3WmFFekoiLCJtYWMiOiIxZDY1OTg3Y2ZmMTBjMTA1MGU1YjEzODdlODU1OTg3NDYxYmFhMTk5MzBjN2I3MTQ0OWU2MDcyM2Y1N2ZhZmU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-114860895\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1125203603 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1125203603\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1235534079 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJpN0YzejVpYmRvQjNjck85cE10Wnc9PSIsInZhbHVlIjoiYm9LTWVWUzcvcDc3dzBMMHpsMm9FTGJrNDh0aEhoVlczcGpSVnp0eWorTk5waTV6KzI1dkxoVk8yNk9wMXJINjRTcGxuTWs3NVVMZ3M2UDM3U0s5WG12WGpWYzBoTjhKWVkzNUF3bTlBMTF3dHV1VjhNdmNnaWszQ3hIS0ZFSHg2eGFrNks3a3NEdHdMaXU3M3lhRTRNcnVPM3NKa0Q4QzNmbGhhNGhxR0ZTMFNKSURINDhEQjh0aVVoaER0N0h4aFlQRjhMTmRtRXFaNXZ3NWI5Tm9ac2IzSDMzeVlDcEZsVTRxV3oreHZ0OExWeG1GbExJQzhWVFhqRFpSb2VTOGlicmZoVTFTS2NPQ1FUYjd0b2R0SjlKdHp4N3JnM2dVMllXclBEL1k1bTNjVDJ5dWhXK09pSkNMYkNpbnJlb1FpVG9jK29WZ0hLVGxWdlNteTNJTVA0bmZoTmtNVFFOa3UzaGd6ZkR5bVYwNDFiWmxnNk9ubFJxVWxqS3NLZlBqRTFXTmdEdlNQckJUNkZqU3pPVE9xemdZQjFkSHRmRkJHeG4rbXdXdThlTjhnd2VLZW1SdmUyK2dteW85SjlRQ08rNGV6RitLMGtmL1hRbUhpQlVaaVZQR1Vqb2FIbkF0MlpIZkZTdVMyMUJ3Vk5GVVBwVG1PMEErdG1uUWFMWkMiLCJtYWMiOiI0NmY2MDRjODE3YTA5NGFhYWZhZmRjZmNjODViMzYxMzYyOWZjZTcxMDk0ZjdiZTgwOWRiNjU5YzVjZjM0YzY1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlkwNDZZY3pqME1SSUtUcmlqRTlkUHc9PSIsInZhbHVlIjoiZlBISUtFYmdmU1BSZDVabUpoV3lpNC82U0pwMDRSeXZ4eVR3MHNkdWN5a1FSQTZFM295MlRSMFAwNmlJaHcwdytFbGNDVlpka1F5ZmVpNFBhR2pYaWlDV2ZjZkhUb3lHTnM2b3ZsT3hvWEJGSlB6a21NbWVRRURxYzJ4OS83dGJ3Q1hFZ3ZFTXVadnp3WXpwVFgyUGZDSFdKZUtLb2pQWXd0d2RWUFc5bCtTRWd3ZDZTR00yOWlKN3VYbDdidVV0T1FGOHhqMWFJSmdLeHRUamhyeFZHTXVlUUo4c0R4RjVuZFRwMmV3bFErdElWNjVsUmJnenFHbXIwVWFVQWs5S1pyY0dMZjVpRkxLWnZoa1doZWxiVzFmREFybERITUh2bVNlRGd2MVFtODhIVXdSMkdkMjdGZnVoNXlYMllQNXNURU1oYVZ2ZlhYMlRLYXNDeGthR0JGN2dzMWVJcm1waHZHa3RuSUhQYmFDNnF2ejRnbVh6SU81OW15QmRlLzcxaTVvRVdZbnQxUVltWUJrYmVtclhBR1RrZk1mRVNTM21PTVRLL1ZmZkY3a050aC9PbGJPQ21HV3lTeE5xM2FKK01QOC9qUjUzY2pmTzllUU1mVkRlZzFFRVhTZGRMYURVU2JGckV1ZXRNRXVwN0ZoVitGMmpPa1VhMU5IeTZCN2UiLCJtYWMiOiJjZDQ2OTkzMDdhODkzY2JmZGZkODgzZGExY2I2ZDdiMGNhNTYwMzNiNzkxNjQzZWI2YzY5ZDg3ZDVjODBjMDk3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJpN0YzejVpYmRvQjNjck85cE10Wnc9PSIsInZhbHVlIjoiYm9LTWVWUzcvcDc3dzBMMHpsMm9FTGJrNDh0aEhoVlczcGpSVnp0eWorTk5waTV6KzI1dkxoVk8yNk9wMXJINjRTcGxuTWs3NVVMZ3M2UDM3U0s5WG12WGpWYzBoTjhKWVkzNUF3bTlBMTF3dHV1VjhNdmNnaWszQ3hIS0ZFSHg2eGFrNks3a3NEdHdMaXU3M3lhRTRNcnVPM3NKa0Q4QzNmbGhhNGhxR0ZTMFNKSURINDhEQjh0aVVoaER0N0h4aFlQRjhMTmRtRXFaNXZ3NWI5Tm9ac2IzSDMzeVlDcEZsVTRxV3oreHZ0OExWeG1GbExJQzhWVFhqRFpSb2VTOGlicmZoVTFTS2NPQ1FUYjd0b2R0SjlKdHp4N3JnM2dVMllXclBEL1k1bTNjVDJ5dWhXK09pSkNMYkNpbnJlb1FpVG9jK29WZ0hLVGxWdlNteTNJTVA0bmZoTmtNVFFOa3UzaGd6ZkR5bVYwNDFiWmxnNk9ubFJxVWxqS3NLZlBqRTFXTmdEdlNQckJUNkZqU3pPVE9xemdZQjFkSHRmRkJHeG4rbXdXdThlTjhnd2VLZW1SdmUyK2dteW85SjlRQ08rNGV6RitLMGtmL1hRbUhpQlVaaVZQR1Vqb2FIbkF0MlpIZkZTdVMyMUJ3Vk5GVVBwVG1PMEErdG1uUWFMWkMiLCJtYWMiOiI0NmY2MDRjODE3YTA5NGFhYWZhZmRjZmNjODViMzYxMzYyOWZjZTcxMDk0ZjdiZTgwOWRiNjU5YzVjZjM0YzY1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlkwNDZZY3pqME1SSUtUcmlqRTlkUHc9PSIsInZhbHVlIjoiZlBISUtFYmdmU1BSZDVabUpoV3lpNC82U0pwMDRSeXZ4eVR3MHNkdWN5a1FSQTZFM295MlRSMFAwNmlJaHcwdytFbGNDVlpka1F5ZmVpNFBhR2pYaWlDV2ZjZkhUb3lHTnM2b3ZsT3hvWEJGSlB6a21NbWVRRURxYzJ4OS83dGJ3Q1hFZ3ZFTXVadnp3WXpwVFgyUGZDSFdKZUtLb2pQWXd0d2RWUFc5bCtTRWd3ZDZTR00yOWlKN3VYbDdidVV0T1FGOHhqMWFJSmdLeHRUamhyeFZHTXVlUUo4c0R4RjVuZFRwMmV3bFErdElWNjVsUmJnenFHbXIwVWFVQWs5S1pyY0dMZjVpRkxLWnZoa1doZWxiVzFmREFybERITUh2bVNlRGd2MVFtODhIVXdSMkdkMjdGZnVoNXlYMllQNXNURU1oYVZ2ZlhYMlRLYXNDeGthR0JGN2dzMWVJcm1waHZHa3RuSUhQYmFDNnF2ejRnbVh6SU81OW15QmRlLzcxaTVvRVdZbnQxUVltWUJrYmVtclhBR1RrZk1mRVNTM21PTVRLL1ZmZkY3a050aC9PbGJPQ21HV3lTeE5xM2FKK01QOC9qUjUzY2pmTzllUU1mVkRlZzFFRVhTZGRMYURVU2JGckV1ZXRNRXVwN0ZoVitGMmpPa1VhMU5IeTZCN2UiLCJtYWMiOiJjZDQ2OTkzMDdhODkzY2JmZGZkODgzZGExY2I2ZDdiMGNhNTYwMzNiNzkxNjQzZWI2YzY5ZDg3ZDVjODBjMDk3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235534079\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1987920855 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1987920855\", {\"maxDepth\":0})</script>\n"}}