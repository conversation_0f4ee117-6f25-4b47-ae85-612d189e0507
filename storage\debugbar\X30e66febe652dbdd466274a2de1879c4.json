{"__meta": {"id": "X30e66febe652dbdd466274a2de1879c4", "datetime": "2025-06-27 02:12:29", "utime": **********.073203, "method": "GET", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990348.634876, "end": **********.073217, "duration": 0.4383409023284912, "duration_str": "438ms", "measures": [{"label": "Booting", "start": 1750990348.634876, "relative_start": 0, "end": 1750990348.973842, "relative_end": 1750990348.973842, "duration": 0.33896589279174805, "duration_str": "339ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750990348.973852, "relative_start": 0.3389759063720703, "end": **********.073218, "relative_end": 1.1920928955078125e-06, "duration": 0.0993661880493164, "duration_str": "99.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52229592, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.048924, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@index", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=33\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:33-84</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00384, "accumulated_duration_str": "3.84ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.006721, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 44.792}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.017512, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 44.792, "width_percent": 12.24}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.020778, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:37", "source": "app/Http/Controllers/FinancialRecordController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=37", "ajax": false, "filename": "FinancialRecordController.php", "line": "37"}, "connection": "kdmkjkqknb", "start_percent": 57.031, "width_percent": 9.896}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.034786, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 66.927, "width_percent": 20.573}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.0367892, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.5, "width_percent": 12.5}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 3, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1485151579 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1485151579\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.040226, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-906147647 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-906147647\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.041299, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-215281930 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-215281930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.042053, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-1400284082 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1400284082\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1728644350 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1728644350\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-960751065 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-960751065\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990346043%7C4%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlHenZJaDZicVJGdEVMSVp4RkxGTXc9PSIsInZhbHVlIjoiSkFyRWZwODE1STd3RUJzamxXT1Q2UXBqUFloTFJSYUYvaDdWeG9FV1ZBWG9oNkt0WFBPUHBuejRiY1ZVc1BjNWY2SEtvYitwaTRUUXZoYmUwVmxzTGpBVlo0RHlEcTZIU3ZucmlMNWZYSUU3RUU0ck9EQVhNaWtWQk5xakVOcWdUZy8yRkorN3l6OThoaEM2eXNoYzQxTEpzTmJ1OFJmdHM3c1JhYnFFYnBQdlRWV2VVb1c2dG1VaXZvZ1ZsTzI1QUNNZHlWWUNiNkNmVjVDcFBEZjRyRTNKL2Q4aU03TGFBbE9zUllwckNTdlNtU2dKczRuSGdzNDJZOXg3UnNBYnRyeGFpTXVhVktOWHVZeVpGYnBIVWw2M2VEWGI5TXQ3L0JmS3E3dC9LMm51aWtWWUxWR1gxSE1WSERwYmxTZUhmTmtNeSs1NHIrd3RncnJIYmFtRHF0N2VCMld2WTc5amduM1NER2xZOUpUS3dGaFRFR3lSbnhDaGRleU5KR091ZjRJZU56UE1lcHlsY3dQSWM2WE9VUGwxUmt6Nyt1N0RRTGJMV2wxdDRyNDhSRmxjNlJqT3UvRFlRbUw5cTZuYVJvSy8zMlVIcGJHMU1ITVFtaFBlTUtLOEdGNzVpUzVMbzFsVlQ2WFJDdWh2amdtbmxLWkRXMGVwMFZrZjN5WGQiLCJtYWMiOiI5YzQ3MTRhMzU0ZTExMmQyN2FkMzQwODlmMDNjZDZlODg0MWM3ZDM3ZjdiNDAwZGNkN2ZjYTAxZDhlZjBmYzk4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InE1YUtBaHFYR1c2TVZUd005UVRuUFE9PSIsInZhbHVlIjoibFY3K0I0cGt5am92ZXlQL1lrRlRiQ0JkWGxDanU0NlJMWXpkVzNtbk1jeVRiOFV3QkRHNk9sYnQ4Zkk4MGRUdHFobFZ2MXE0V2ZBanNRYXZzem0rQm9DMzJFK1JkK0YvT1hGcXhIOGhRNW9ZNjE0NXNPdllVSXl1TWZGUGZlVkhsNlVhY2d5MXFDZEhnaWZ0azh2akR1VFlEMVo5SzA2dEVxWVZLMmJwVGRDMTk3RVk1dG9PYmowQm1tV2ppT2NxY0pTcjJVbGxjV0k3dmhxMHQweGgwRFNneHNjODZ0UGlmZ2NXZ2hQK1pPNXMzL05PUXZaQWRQZDY0L0J6clcwZ2Q2S1FGUk1JQSt6QmxTdThDQU95R3FycktaT09CR2FGM3gwK2poTnU1QkRCM1VzQldtTE5aMzAzeml0ZHJyR3dQQ0dPLzhMaHlneTFKdjVKaGtnbG5jakpieElrR1E4MnBaT01GQjUwNk5lRWJwelltQWFaZkNxS3pEd1JHQXVMOStXcUhxZ21pV2tNUjdDd2JGR1NTN0FSU29RTDFGZlhnK2FVbGtaeitpaThvYjlkL0xOdXZrdWh1SW80amVFU3RsbGlQcjhPdjkza1VDd2IxcUoyYWJ3UU5nbEJ3UEdLT2s4dEd0S1hpYzRXeWpVdDNUWjkrSEk4UEZlT2tjWmQiLCJtYWMiOiIxYWQyMTA3ZWU3NjIwNzMyOTg4MDc5N2E2MjNkOGRjMDQwYjAwZDQwOThkMDdjYjI2NmU1MWI2MTBmM2JiYzAxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-637343773 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlySWd6aGNOTGRsdHRtTUFCeWpkeGc9PSIsInZhbHVlIjoiakM3dUlKVEdOcC9nWnNGYm90ckxPSzBIeE9GanRmKzROcTR5ci9PeGlKQmZvQlAwb2FjclBVbnBWSkhQbkZjclFHeTFha00wMngzcS9KSmY2TFNtb1Bmc0FiU2xMVFJRbEdXdXZ3eGRmaGNXajBZa3NaVU5iQm0yQW53c3RDNFVFRVhOL01YRGg5dzJxcUV2NVlBaWVTSDJHek9WY3FQM2ZjOEU4NVU2eTVVd2ZRZFF3NERvRFZJeEdvbk1Nb1pCcERtcUd2U3hnZXMrMklmQXhSRVBPekpRNDBXTDY0ZVFjTmZTOTN6a3pDU01pa2hRSXhFb1dVVXFvMHg0RHZoRXJrcDJNRnorVXRMR3NsdEExRGJhMExHT2I1WVZVUGY4Y2VocVhYeWJQbUpiZkJ3MjRuenl4QjBtQUpudlF4bmNQeWxrNXp6T0tHQlZpV0JNYnEvcjAvd1g0OUZoY0ZUMm1XZk1zdHUxdWV2eUVYTXhVSElNdWJDcHRuNjZuSXVON29ZK2dsSU93bDdIanNHR1ZMYnoxdzRpM2cySEdFVWMxUVZSdE51T3Facm5td1N5MXpkaURJVFVINUh3QW1zeTBqdVNxZVdkU3YzVm9IMHlSSWlKWFkwVjZ2cVNiS2FnYTYvUEJraTlNQ2JrS1F0WERPRlpWNzI2Q2JPNW5RcWoiLCJtYWMiOiJhYjNjNDAxN2E1NWNkM2FmOTU1ODc0MmMzMzg5YzczZjhlNDJjNDAyODI5NDhiYjdiZWNiY2Q3ZGI5ZTkyZWU3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjB6Z1JUV3I4NTBBeWZNNy9icmhnUEE9PSIsInZhbHVlIjoiMC9mZkNlUHlyVDJpZFpYeXprYXgrWkZyS0pLNmd6T0tRVDJQU0R1c0tlQ2RwbFJMaUdmczdKR2RSTFR6SFJQeUNydGNhZGRESWdzUE15cm9OOE8yMW9CeU1HTGxrZThSKzJIM2Z6SUlGNEpuRHBxRS96S3dvYmVuckNCOXZUYXJCS0EwWUI5ZFJxZ0tRY05uYUZHdWtMb3dScFhRRVhDOTJyYmNPL0RKaVVsNjQ1dFdCdUwvaUZpbUZqMzJKNFhDWTQwQi9hTzFrM2NteURzWmZLS3Jac0JYcWtCMkMvUDVsWmg3bmdnWEZxL294OGVGM2ZJSUF3enRSck5rV0N1QWtzdFRQVG9CR0V0ZU5wOVlrZGlTQktwRDRSZEFBUXJzMzMzcmdDUi9NWnpCeXo3SzF5ZFdNZmVRZFk5QWoyTldHVTRlTUxKOElmVy9JMkJmYk9rZE1VUEExSmk3YmtmQXZtSWVKU05Xd0pmbm1oQm5oVDd6NnhJMldLb3F2dDlZR3FucjF2YjdCT2x5Ly9aaDBVZ21xeFZEQzBtNDYyUmZNOFBNZTgyTXBWMGZxc0g3RVpiRGVvMktXeCtnb3haREcwdGF6L3VtbytQdnA5VEdMY2xCWWV0Ry9waW5PeWpuNlFneExGK0VpMUVKZDAxTkNjL3QxSlEyYUxRRm1MQVMiLCJtYWMiOiJhOTVhYWI2YWYwMDgwMDJlOTYxYzY2Mzk2ZTY4ZjE1ZjQ1OTM5MjcxYmM4N2YxMjBhY2IxMzg2ZjNhNGI1ODg3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlySWd6aGNOTGRsdHRtTUFCeWpkeGc9PSIsInZhbHVlIjoiakM3dUlKVEdOcC9nWnNGYm90ckxPSzBIeE9GanRmKzROcTR5ci9PeGlKQmZvQlAwb2FjclBVbnBWSkhQbkZjclFHeTFha00wMngzcS9KSmY2TFNtb1Bmc0FiU2xMVFJRbEdXdXZ3eGRmaGNXajBZa3NaVU5iQm0yQW53c3RDNFVFRVhOL01YRGg5dzJxcUV2NVlBaWVTSDJHek9WY3FQM2ZjOEU4NVU2eTVVd2ZRZFF3NERvRFZJeEdvbk1Nb1pCcERtcUd2U3hnZXMrMklmQXhSRVBPekpRNDBXTDY0ZVFjTmZTOTN6a3pDU01pa2hRSXhFb1dVVXFvMHg0RHZoRXJrcDJNRnorVXRMR3NsdEExRGJhMExHT2I1WVZVUGY4Y2VocVhYeWJQbUpiZkJ3MjRuenl4QjBtQUpudlF4bmNQeWxrNXp6T0tHQlZpV0JNYnEvcjAvd1g0OUZoY0ZUMm1XZk1zdHUxdWV2eUVYTXhVSElNdWJDcHRuNjZuSXVON29ZK2dsSU93bDdIanNHR1ZMYnoxdzRpM2cySEdFVWMxUVZSdE51T3Facm5td1N5MXpkaURJVFVINUh3QW1zeTBqdVNxZVdkU3YzVm9IMHlSSWlKWFkwVjZ2cVNiS2FnYTYvUEJraTlNQ2JrS1F0WERPRlpWNzI2Q2JPNW5RcWoiLCJtYWMiOiJhYjNjNDAxN2E1NWNkM2FmOTU1ODc0MmMzMzg5YzczZjhlNDJjNDAyODI5NDhiYjdiZWNiY2Q3ZGI5ZTkyZWU3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjB6Z1JUV3I4NTBBeWZNNy9icmhnUEE9PSIsInZhbHVlIjoiMC9mZkNlUHlyVDJpZFpYeXprYXgrWkZyS0pLNmd6T0tRVDJQU0R1c0tlQ2RwbFJMaUdmczdKR2RSTFR6SFJQeUNydGNhZGRESWdzUE15cm9OOE8yMW9CeU1HTGxrZThSKzJIM2Z6SUlGNEpuRHBxRS96S3dvYmVuckNCOXZUYXJCS0EwWUI5ZFJxZ0tRY05uYUZHdWtMb3dScFhRRVhDOTJyYmNPL0RKaVVsNjQ1dFdCdUwvaUZpbUZqMzJKNFhDWTQwQi9hTzFrM2NteURzWmZLS3Jac0JYcWtCMkMvUDVsWmg3bmdnWEZxL294OGVGM2ZJSUF3enRSck5rV0N1QWtzdFRQVG9CR0V0ZU5wOVlrZGlTQktwRDRSZEFBUXJzMzMzcmdDUi9NWnpCeXo3SzF5ZFdNZmVRZFk5QWoyTldHVTRlTUxKOElmVy9JMkJmYk9rZE1VUEExSmk3YmtmQXZtSWVKU05Xd0pmbm1oQm5oVDd6NnhJMldLb3F2dDlZR3FucjF2YjdCT2x5Ly9aaDBVZ21xeFZEQzBtNDYyUmZNOFBNZTgyTXBWMGZxc0g3RVpiRGVvMktXeCtnb3haREcwdGF6L3VtbytQdnA5VEdMY2xCWWV0Ry9waW5PeWpuNlFneExGK0VpMUVKZDAxTkNjL3QxSlEyYUxRRm1MQVMiLCJtYWMiOiJhOTVhYWI2YWYwMDgwMDJlOTYxYzY2Mzk2ZTY4ZjE1ZjQ1OTM5MjcxYmM4N2YxMjBhY2IxMzg2ZjNhNGI1ODg3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-637343773\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-474165763 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-474165763\", {\"maxDepth\":0})</script>\n"}}