{"__meta": {"id": "Xf61d0e6e3b8922eff518bc46ff015b19", "datetime": "2025-06-27 00:14:52", "utime": **********.230011, "method": "GET", "uri": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750983291.764415, "end": **********.230031, "duration": 0.46561598777770996, "duration_str": "466ms", "measures": [{"label": "Booting", "start": 1750983291.764415, "relative_start": 0, "end": **********.146412, "relative_end": **********.146412, "duration": 0.38199687004089355, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.146424, "relative_start": 0.38200902938842773, "end": **********.230034, "relative_end": 3.0994415283203125e-06, "duration": 0.08361005783081055, "duration_str": "83.61ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47521432, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.show", "controller": "App\\Http\\Controllers\\BillController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=333\" onclick=\"\">app/Http/Controllers/BillController.php:333-393</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00369, "accumulated_duration_str": "3.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.181978, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 47.696}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.193862, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 47.696, "width_percent": 13.55}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.201509, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 61.247, "width_percent": 15.176}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.217466, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 76.423, "width_percent": 13.55}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.219343, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.973, "width_percent": 10.027}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => show bill, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1787202115 data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787202115\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.223407, "xdebug_link": null}]}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-906899705 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-906899705\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-59552217 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-59552217\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-823197964 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-823197964\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1989923798 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983290489%7C46%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImJ0OCtFZmIwM1dDd00vbkdPT3F0M1E9PSIsInZhbHVlIjoiUzJxTDhiVjAyS000WGIyYjAwMlNkNTlqZkRmcklKbE1DTGtCREZJZzByMlI0TzNycVd3NkZiUkt4MUFGTTJjQ0lDYjJpSHM5azNEY09oanVsZWl5MmFSMkg1M09lNzRNcFdKK2J4bXlaSTJhTzB3M2cvbWE1SDNROGVaMHZOTEtHT0lZWURTODhzRDdGYXdpbEc2UisvT2NuM202NWMzcDN2L2RGeHYybmJBMHlZQVlwZTZIb2tpOHUrd0tUSkVKRFB4NXFlek0yZnNyd2hFTmRPZkMweVlXZk1vNld1WDRRL01WN2RsSXBzdWdKWWZrZlhObmlub09QYUF0Yk9QdlNQN3ErTHlYVHF0bmVEbXVQTmpqKzVIRjFFczJNdTBhOEdDaFlnVXBNZWY1YVhuN3JsaHd0SVhtaTJvSkptU3d0eHJUMHU5SFBoNXpzNkxVVFdmckJwdFdObW50Z04vNjF5RklpdFk3dGpwYS91Vm41dTVSUk0zcit6RCtWdUQzc05CMlB3UDVoSkpWTDE1Wk83NVByK25STWNHTEFMeE83b0lOZVJHK2JyNW5LS1FCRjhHUWc1Z1QvS2ZDOVBzYnptOXU2dmRZbkZoLzJJeHBERFFFOVorWk5DakFIOXVDbjlvRUs4RFJ1bGRxVTNmWHZVbTA0MnZYWlNNV0JhOGEiLCJtYWMiOiIzMGViOGI4MDNjYTRjNzJiNDI4NDVjNGE2OTA2ZWNhZTU3NmNlMDA0ZWNiYmQzMDcyNTJhODc2NGRjMGRlOTBkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlRpcmdkMWd6U1MwM25jVzIvYlAvaUE9PSIsInZhbHVlIjoicElMelBucnFxTXROK1dkWjVpRFZYVXV1SjhjeVJvRENDNUdVYnRsa0ZmbzUwT0w0Yit3RDJ6SWFFdHRONUdHeE55VHNuZ1lwcGUvTTZ3aGc2V3p5NmFqSk50Q3grdGNXMG9FZVhSUm94V2tJcE1ucUdOdGVodUYvLzMxcnR3cm9qOFNCYnIwTWVkMENQd3BmdlNZak9XWTB1Sm90YnlCYkZiSC9TSUJQNVVnUHppQlFDVFJNTnB3RlhuVGxzYllRampNWWRLQVFscjJSR1lDUXVZbHRwSTRLNVJVN3lPMDNIUWkrWmptRUNkWW1JdDlGOXRyKzlSbnM3S1B2dXk3T0pGZkVFWjg5eWlLaVlRSFlVMmFsdEZrRTJ2OEM3cGx5dVhoUktWTmFvV21JRlJLZ25BczUybU1CM1JBSFp6OUk0a1JaZjRtbXIzWXRaVFlrL1h0elY5YlhsUVlCMFpaVUh5Y1F2am81ZHBYcFNUUk1UN1dia09Wd0VIVEl3YmJxdnNBVEJqQkdvZGthT3dRYW9LRFpZbDMweE1NS2Z6cmcyN0t6aHFCOHhyeGUxK0w0blozbnNibW1xVHZtVlBNdGFjSU1DS2xxRndTSGFxRnk0amVzRTlrcFZFcTBmL1NROFViWXk5WlF0ZmlITlV2UVVNZ0FaWWlSQmpkYmdER2siLCJtYWMiOiJjMTU4OWNiYmI0NmI1NzRmYmNhOWEyYjhlYWMzNjU3Mzc2YTk4YzgwZjczY2VhYzcwMmM5Y2UyZTFhZDA3YzMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1989923798\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1003734134 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003734134\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1556948619 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkFMeHNvdjRONThUZUhpbDhIQXgyU2c9PSIsInZhbHVlIjoiL1RtaitBbkJ3L2sybFYxeVBEK2J3Z0tSUWtsSFBzdkFSeU1JTzBCclovZVZuSFRBeldLSGs5aDlER3ZkelVJWVFuckpYdWRsVWFpRXZWdUthN2xoQTBtaFJoV21vN2hzay9sbjNVSFV3YjN4KzRpVk4wN05Eb05CZzhwZk1KS2dkcDAreVpMWGZEdHB5OWRweEN4RXV2QldrdWNpK2IyYTYvcGh2bHZFbG9hNzdaL2FwUmVRRTQ4cjNFTjBrdC93RFhyRllMNndBS0gxNXFtUldUVnRSNlBBQkt1VW45RmQ4a2xLTUV0bG10WU9WM1p6TUp1cHBqOWI0dDVsZmYwZldnYzNPcmRGOTdiMzZDZXY0bmlvR2pTUC9jQ1hCbzJvUHRRN3hSVTRTTGJEb2dhNi9uTjFYWnlka2NWUzhKeXNaZ0dVak8yNjlUSEZLdldtWjh1c3hrMERjdXVoRjAvNnJaODljaU01VmFTVXh6b0lDd3BCdUJXdTZiZ0g3UFpKWEpjaDY3S2QvemhwbHFGRVBtN2VnNU9ySm9hSGxEWFowY2hmbzR3RXhMOWtvUmJPTnNNV3VCMkFLNFRXZ3hBcWF2Mzl2c2FESXJKVDJZcEM3U1FMbklCRjhaVlNodUJmNURiaFBzVWMvNUVjT2prQWZwSk1KZEgvWGs5WGNzYTgiLCJtYWMiOiIwMGIxZWZiYzlkZjMwOWZkYmMyZDc1Mzc2NWRkNmI4ZmI3MGMyZmI3NDU0MzdhZTg5MGU3ZGJkZmEyNTFhNzBjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVtaTB5ZkpBcmdXc3Z2eGRXQjFrUFE9PSIsInZhbHVlIjoidlF6M1VrSFNRS1NPRmpHazBkVm9KZUlwcHRzZFZoajVWU0dYWHRtZEVweEFxNE9CQ0c2MnZWVThXcHh2WEIxMmxGdWpHT1hwNVlDL043K002VGxwOVEyaW9ua2FwMlRGK0ZvVTBnRXVKM0ljbnNnU1BlcXAyMFFhWFQ2SDNERnFldTRCTVpaNmgrSUdnTWZyRkk2ZmNWdlllRElPVWZjZEhGNFpSaGRoQkNyejkwT21Qc0ZNaFB3bFo3OUtIaUdoaDZXcG0wVVRKKzBwZVdkejlHcmxmNXBsazJlNWp1cHNGZ0RGY0lJVHdFQXBGVHFDb293aXF2RkxKUGNWNzhMWXRjU1R2M2hnUjZCRDRSQkp0R1FaaVVGaEdzOTJTeWZ2Nng4Ly9TSExROWdzYW94M3EzdDZadkg2TzBvRzgydzlxNCtqOWxZRmM1VmEweHVzTFh0enh6OUcveEpjQUtld1E5SEs4RlhuTTVjNVhrOEJFVy9SS0EzNGt3R2VBWjNSeEZSNjlWQkhIL2F4UGs4TUtIR2drclFoWkRyQ29Oc01pbDUzK3FCZURLaC9hTUc5ZkpRQTZaTzhtNTJQdmU0YjlOUlpyK2MyUkdSeldQYXhsWUVXZVFiWjFNSDFMaGk5SlhXbUorSXdHbVZwVTBYTEpCQlJYK0ZsSmo2WUVHUWYiLCJtYWMiOiJlYzk1YmExMzRkMDIwZGIxMWRkMzgyOGE0Y2UzZmNiYWI1MmMzNjc0OGEzNWQ5YWMxMGQ2NWFiZDFhMmRhYjk3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkFMeHNvdjRONThUZUhpbDhIQXgyU2c9PSIsInZhbHVlIjoiL1RtaitBbkJ3L2sybFYxeVBEK2J3Z0tSUWtsSFBzdkFSeU1JTzBCclovZVZuSFRBeldLSGs5aDlER3ZkelVJWVFuckpYdWRsVWFpRXZWdUthN2xoQTBtaFJoV21vN2hzay9sbjNVSFV3YjN4KzRpVk4wN05Eb05CZzhwZk1KS2dkcDAreVpMWGZEdHB5OWRweEN4RXV2QldrdWNpK2IyYTYvcGh2bHZFbG9hNzdaL2FwUmVRRTQ4cjNFTjBrdC93RFhyRllMNndBS0gxNXFtUldUVnRSNlBBQkt1VW45RmQ4a2xLTUV0bG10WU9WM1p6TUp1cHBqOWI0dDVsZmYwZldnYzNPcmRGOTdiMzZDZXY0bmlvR2pTUC9jQ1hCbzJvUHRRN3hSVTRTTGJEb2dhNi9uTjFYWnlka2NWUzhKeXNaZ0dVak8yNjlUSEZLdldtWjh1c3hrMERjdXVoRjAvNnJaODljaU01VmFTVXh6b0lDd3BCdUJXdTZiZ0g3UFpKWEpjaDY3S2QvemhwbHFGRVBtN2VnNU9ySm9hSGxEWFowY2hmbzR3RXhMOWtvUmJPTnNNV3VCMkFLNFRXZ3hBcWF2Mzl2c2FESXJKVDJZcEM3U1FMbklCRjhaVlNodUJmNURiaFBzVWMvNUVjT2prQWZwSk1KZEgvWGs5WGNzYTgiLCJtYWMiOiIwMGIxZWZiYzlkZjMwOWZkYmMyZDc1Mzc2NWRkNmI4ZmI3MGMyZmI3NDU0MzdhZTg5MGU3ZGJkZmEyNTFhNzBjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVtaTB5ZkpBcmdXc3Z2eGRXQjFrUFE9PSIsInZhbHVlIjoidlF6M1VrSFNRS1NPRmpHazBkVm9KZUlwcHRzZFZoajVWU0dYWHRtZEVweEFxNE9CQ0c2MnZWVThXcHh2WEIxMmxGdWpHT1hwNVlDL043K002VGxwOVEyaW9ua2FwMlRGK0ZvVTBnRXVKM0ljbnNnU1BlcXAyMFFhWFQ2SDNERnFldTRCTVpaNmgrSUdnTWZyRkk2ZmNWdlllRElPVWZjZEhGNFpSaGRoQkNyejkwT21Qc0ZNaFB3bFo3OUtIaUdoaDZXcG0wVVRKKzBwZVdkejlHcmxmNXBsazJlNWp1cHNGZ0RGY0lJVHdFQXBGVHFDb293aXF2RkxKUGNWNzhMWXRjU1R2M2hnUjZCRDRSQkp0R1FaaVVGaEdzOTJTeWZ2Nng4Ly9TSExROWdzYW94M3EzdDZadkg2TzBvRzgydzlxNCtqOWxZRmM1VmEweHVzTFh0enh6OUcveEpjQUtld1E5SEs4RlhuTTVjNVhrOEJFVy9SS0EzNGt3R2VBWjNSeEZSNjlWQkhIL2F4UGs4TUtIR2drclFoWkRyQ29Oc01pbDUzK3FCZURLaC9hTUc5ZkpRQTZaTzhtNTJQdmU0YjlOUlpyK2MyUkdSeldQYXhsWUVXZVFiWjFNSDFMaGk5SlhXbUorSXdHbVZwVTBYTEpCQlJYK0ZsSmo2WUVHUWYiLCJtYWMiOiJlYzk1YmExMzRkMDIwZGIxMWRkMzgyOGE0Y2UzZmNiYWI1MmMzNjc0OGEzNWQ5YWMxMGQ2NWFiZDFhMmRhYjk3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1556948619\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-317540058 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-317540058\", {\"maxDepth\":0})</script>\n"}}