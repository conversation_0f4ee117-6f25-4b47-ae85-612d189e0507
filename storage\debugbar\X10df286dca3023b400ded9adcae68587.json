{"__meta": {"id": "X10df286dca3023b400ded9adcae68587", "datetime": "2025-06-27 02:12:25", "utime": **********.806638, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.376397, "end": **********.806655, "duration": 0.43025803565979004, "duration_str": "430ms", "measures": [{"label": "Booting", "start": **********.376397, "relative_start": 0, "end": **********.75184, "relative_end": **********.75184, "duration": 0.3754432201385498, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.751849, "relative_start": 0.37545204162597656, "end": **********.806657, "relative_end": 2.1457672119140625e-06, "duration": 0.05480813980102539, "duration_str": "54.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45734648, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0030600000000000002, "accumulated_duration_str": "3.06ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.77915, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.32}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.789349, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.32, "width_percent": 12.418}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.794949, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 79.739, "width_percent": 20.261}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-224198250 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-224198250\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-180115947 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-180115947\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1462195958 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1462195958\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1192482085 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990342712%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFUTERlSC8xZ1JIdVZDV1RuLzIyMlE9PSIsInZhbHVlIjoiWHAvZ3RMaDlsV0dPSHVFOWcvdk5BZjB2eVNhMk03alpMdXB3K3ZidVV3MzFyWUxFNlUxOFJTNHJHSStvM2lrQUJ1VXJ2d2FGSHV6M3VEcnNqMGZKZHdDcXpuejI4REh2V0lSMmg4MXhpSGljeGMxcGJzd1FEdEFjUkRlYmZYWSs2OVhpSUVEdGJ1NklxZFVkRmJzSkkrVDgySTROOC9lYnpkQXAwSHJNdklwNHBobEdHcURBdkRiSm5qbGVyYTN5bloxWWpYNjU4dGhVWVgrM3RnL0xXNUdORXB1Tkhrc0x5M2E4Zjk5TXhzNmFNellWVENSS1NZNmxIb3E0UTl1eWU2V0V4SGNVQmJrKzlsNGZ5S09aTGVXQThkZTJyTTAyZDMzNEVQUE40VE51cU9oQUcwYjdjYk0yN1Q0dTduT3hSLzI5VHdQbW9oUGhQb0pYWlRzb2VyRFNCYjNnYiszd1pBRlRHZWdZWjJKTE16YkJublZ6enE4cnVid2ZtZEpMRGZWbmtqeERTN1dDcU1NZVJSa3NFU0JjTnRUR01ZWHpEa3lRV2dMTm5RRFV3SVR1QUdJb2VRM1A0WHljUlBuMG14TGhUdEJyaDA1c3JlLzV1a3UzWjlRNnNURFNwRDhnTGxxRDQ0dnBDUFhja0NLdW13WmNaU05FbEdmWHh0dysiLCJtYWMiOiI5ZTc3YzU5NmU2NmI4NDUxNjY3ZWZkZjQ2ZGE5NzBmMWUzODY3ZDU4MDA3ZTBhZjY0YWNjZTE3N2I3Njc1ODkwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlA3TGk3OFMzQzlkSjVQN2VrS0oxZnc9PSIsInZhbHVlIjoiZFg4M3IxQzFYSnNab0hDYWptUEV4cmZTSXkwODMzMjBGUkxQN2IzaWl3M3poTTNUdnN1TVg3TnEvTU9EUTVjSDB0NTQraDZRd21IK0ZkQlZoWjhUenZNV2RwN0FVL1pJNkxWYmlBcHlvTGhqOEdKT1cwM0ZnQnd1dEc3dXVzMXFsTEU0TEJ1N1AwakRicUYrWCttaWk3aENsSHQrZXcrSlZydWhMZGFaR2NIQWRRYjVyN1lBQW9RTzlIQTFhbVBXbTZVZE5rRGtIeUZuL3Jnb3BjSjd3RmdQMHM3dkZ3aWtkKzNYV24yVUtMQ0hnTy82cE5ZRCswRFlDbURjMGFGdmhIYlYwcFlHZXJycWpqdnVkbTNCblVIN3YwV0I5aTYrSFc2cU43YVo3UldOenAvR0pHMXlhTTh0NGpWbldzMyszaDhEVUtBZlNvZzc4bjVQZzhNNVZxM21pZDRKQlh6OEF5SGVJTVpXSjRWd3htdEdWcnl6UUdndHIzcVNBYkFpcVNiMzViVVhXTTlJTGllc2ROWm5ORWIvbEwzL0tUMWE1K01lZ3JVQ0c3ZUNLZ2JibzZSaWZyc1ViTmVuVXBTWnNIcUZTTTc2MVJ6L2lOQmloak1WRUxtT1lIYWRsY1MrcFV4TjNhemRXZmY2Tk1Ea1g0N0hkTkl3RjJ0d1JsRDgiLCJtYWMiOiIyNWFhZmMxYTA1YWU5ZGUxNzYyMmRhYTQzMjkxMjk1NWY3NjA2YjQyODQ0M2EyMzZiY2U5ZTZlZDg2NjUzOGI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192482085\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1978037381 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1978037381\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-282081563 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVLbjN0Nm5ET0ZXcENva1MxekF5dkE9PSIsInZhbHVlIjoiUjI2M3BTa1pWKytSQmIxODBPaDJOT0ZtY2VNWE5UZXM4MWQxSHd2aVQrRXduVG1rQndpZmZHRldDZWN1WVdiTjRVSkp4cU5VWGlYZzJzbG5vT3A5ZlhaMHhaV1JBeVB4QjkvNFI3WlpCOHFVdVluVUVobGYrd0VkbDVHRmROSHhQSXNoVU1pMHFYWTRmWEppMm1WNTMrUDM3VkZkUjhCQ0R3cHBlbTRyVEtuVTB5WmZONVRBelYvS1U1NWo2aUg4aHFLWGJhc01HQXRLNkUrR3dKdnNXWXhsRktkaUhrUmdETzdCbHJQbG8rWEhGSGYrWGpyV1hrWks5R1Zmb29zR2J2dU9lT0daTkhRMDR3R3gxK1VZN1VQM3VhUlFCZkJZazRPeUZXb3Y2T0lUaHZtTXRENkxnRUtOSDJkdnhXeGJrTlFEZFFPMmZBbWttU3JWRnk5aEVxcGdhd3lQbHExU2pzdTlnemZCWFZyRmdIUkhZdkFCMFkyUTRVc2NnUGV6ZGJGMk9jZGE2ekRZdytObGVNZG5MaEI5T2NvVG5IdDhETG16dmd6SVN3TER3ek8zeUJhaEw2VDRuek82OWhhZGJTWkVhSDUzQURWZXhLUFUwc2FreTJHZHNIQVhpR0VydnNvTlJmVmZ0Ykx0SXpYL1NCVUZVVmkzdUZwK25mLzUiLCJtYWMiOiJjMGQ3YmZhNGQwNWZhMjgzYjg3MzI5ZmRjYTJjODJjMmFmMmQyM2VmMjE4NzBjZTgzMmRiODg0MzhmMTdlNjE2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImdhaldaSSsvRkM5cGgwSzlwMDlhU3c9PSIsInZhbHVlIjoiZmNKWDl4R214aXhnSEtTNnlnLzAyVjRhYUE2UkthUU55ZW41ZkdvNWxKVDJtUFNnWEo3eDlmeWZRcVJqdVR2bDVCVlcyYzVuVi8rYkI1aWlxZy9vKzR0N01uQ2ZQSEZUSTBsN2RTc1lBd21XdWhRYjhvMFE1SDVXMzRaOWE1OGU0eVZPeFRlcTZoZGhKVnhGSXRTZ2U3dXJFKzZuakZTSEtxTFg5RlUrRkFmRUxXVTdVWjNMZmpmcmpWQVh3bmNIamNPK0tIQXk5QmxiTW1hNWN5ampyZEpiSTM5SlRRS1FPTW1WajlDTURKdUNuVlpoUFI3UzR0ZUZPOWhKcXFJVzZLTy8relZ4dFRPRWp3Y0dPWm96Zm5lNURVRmJac21YODA2S29TZ1poL3ZjT3ljbTlheTlJc0xscDFjVDV6VVo3d0lDUldwMlpCNEswWmV2U1FVNWdhN2wvYUZuNTdHelZzTEJmbG83SFRpRTVLUUE1ekZKN1NPVjNzS0lLRGlTNjdQckRNL0Q5dUxFbTNkSXYrNHNsMXZLbzJIOFVLOWs0OWhUUndFeTBOcTE4d0FyYndTUi9hVDQyVXYrT2h0Tng0NTFacDZLa1hHYXVCNnJsVmdoRWV6L01nelJldlpoVGNRUWRHR3M1QU0xcWtVSDZPU29acDl3eDYwVTVyRDYiLCJtYWMiOiJjZDkyYTE2NmQ1YjRjZjVkNDIwNjJlNmRiOGFhODc5OGM3ZjE0ZWYwYjkxYjUyOTY5NjkyOGM2MTUyNTdhYWRhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVLbjN0Nm5ET0ZXcENva1MxekF5dkE9PSIsInZhbHVlIjoiUjI2M3BTa1pWKytSQmIxODBPaDJOT0ZtY2VNWE5UZXM4MWQxSHd2aVQrRXduVG1rQndpZmZHRldDZWN1WVdiTjRVSkp4cU5VWGlYZzJzbG5vT3A5ZlhaMHhaV1JBeVB4QjkvNFI3WlpCOHFVdVluVUVobGYrd0VkbDVHRmROSHhQSXNoVU1pMHFYWTRmWEppMm1WNTMrUDM3VkZkUjhCQ0R3cHBlbTRyVEtuVTB5WmZONVRBelYvS1U1NWo2aUg4aHFLWGJhc01HQXRLNkUrR3dKdnNXWXhsRktkaUhrUmdETzdCbHJQbG8rWEhGSGYrWGpyV1hrWks5R1Zmb29zR2J2dU9lT0daTkhRMDR3R3gxK1VZN1VQM3VhUlFCZkJZazRPeUZXb3Y2T0lUaHZtTXRENkxnRUtOSDJkdnhXeGJrTlFEZFFPMmZBbWttU3JWRnk5aEVxcGdhd3lQbHExU2pzdTlnemZCWFZyRmdIUkhZdkFCMFkyUTRVc2NnUGV6ZGJGMk9jZGE2ekRZdytObGVNZG5MaEI5T2NvVG5IdDhETG16dmd6SVN3TER3ek8zeUJhaEw2VDRuek82OWhhZGJTWkVhSDUzQURWZXhLUFUwc2FreTJHZHNIQVhpR0VydnNvTlJmVmZ0Ykx0SXpYL1NCVUZVVmkzdUZwK25mLzUiLCJtYWMiOiJjMGQ3YmZhNGQwNWZhMjgzYjg3MzI5ZmRjYTJjODJjMmFmMmQyM2VmMjE4NzBjZTgzMmRiODg0MzhmMTdlNjE2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImdhaldaSSsvRkM5cGgwSzlwMDlhU3c9PSIsInZhbHVlIjoiZmNKWDl4R214aXhnSEtTNnlnLzAyVjRhYUE2UkthUU55ZW41ZkdvNWxKVDJtUFNnWEo3eDlmeWZRcVJqdVR2bDVCVlcyYzVuVi8rYkI1aWlxZy9vKzR0N01uQ2ZQSEZUSTBsN2RTc1lBd21XdWhRYjhvMFE1SDVXMzRaOWE1OGU0eVZPeFRlcTZoZGhKVnhGSXRTZ2U3dXJFKzZuakZTSEtxTFg5RlUrRkFmRUxXVTdVWjNMZmpmcmpWQVh3bmNIamNPK0tIQXk5QmxiTW1hNWN5ampyZEpiSTM5SlRRS1FPTW1WajlDTURKdUNuVlpoUFI3UzR0ZUZPOWhKcXFJVzZLTy8relZ4dFRPRWp3Y0dPWm96Zm5lNURVRmJac21YODA2S29TZ1poL3ZjT3ljbTlheTlJc0xscDFjVDV6VVo3d0lDUldwMlpCNEswWmV2U1FVNWdhN2wvYUZuNTdHelZzTEJmbG83SFRpRTVLUUE1ekZKN1NPVjNzS0lLRGlTNjdQckRNL0Q5dUxFbTNkSXYrNHNsMXZLbzJIOFVLOWs0OWhUUndFeTBOcTE4d0FyYndTUi9hVDQyVXYrT2h0Tng0NTFacDZLa1hHYXVCNnJsVmdoRWV6L01nelJldlpoVGNRUWRHR3M1QU0xcWtVSDZPU29acDl3eDYwVTVyRDYiLCJtYWMiOiJjZDkyYTE2NmQ1YjRjZjVkNDIwNjJlNmRiOGFhODc5OGM3ZjE0ZWYwYjkxYjUyOTY5NjkyOGM2MTUyNTdhYWRhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-282081563\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1526715148 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1526715148\", {\"maxDepth\":0})</script>\n"}}