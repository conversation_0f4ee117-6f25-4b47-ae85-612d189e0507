{"__meta": {"id": "X1efe1da47dfb18f9a2d6a5a6682af988", "datetime": "2025-06-27 02:27:43", "utime": **********.328718, "method": "GET", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991262.901354, "end": **********.328731, "duration": 0.42737698554992676, "duration_str": "427ms", "measures": [{"label": "Booting", "start": 1750991262.901354, "relative_start": 0, "end": **********.248587, "relative_end": **********.248587, "duration": 0.3472328186035156, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.248597, "relative_start": 0.3472428321838379, "end": **********.328733, "relative_end": 1.9073486328125e-06, "duration": 0.08013606071472168, "duration_str": "80.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52225552, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": **********.324379, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@index", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=33\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:33-84</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00372, "accumulated_duration_str": "3.72ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.281302, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 44.624}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.291274, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 44.624, "width_percent": 12.366}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2943308, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:37", "source": "app/Http/Controllers/FinancialRecordController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=37", "ajax": false, "filename": "FinancialRecordController.php", "line": "37"}, "connection": "kdmkjkqknb", "start_percent": 56.989, "width_percent": 10.753}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.308098, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 67.742, "width_percent": 18.28}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.310303, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.022, "width_percent": 13.978}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 3, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-494123833 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494123833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.314253, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-908862027 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-908862027\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.315486, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2082791280 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2082791280\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.316231, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-1659236541 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1659236541\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1358019936 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1358019936\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-486654343 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-486654343\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991259466%7C36%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBPcE81QzgvUzlQTXJSbTVBcFR4blE9PSIsInZhbHVlIjoibFlZZ2crajlWVFQ1d09KOWFmNXEwWmpSZi9pdnFrYm1ObkErVXY0aTVGTnp4NjdXZ0IvbFlETlJtTW9RUnpwenBVdWs2KzBIdVZXdHlJem9YQ1o2V08yT0FzZ1ZzU09uVkZLcVhwMC8zWXdXL2hzRm1hNFp4dFBVRjdGNFcwLzBXai9MZ3lHOWQ1eVVMeElGN1A1dzdWS3RjV0hEMXBWVThiUm5LNnNFOVppc0pTZkozeDV2TUxrczJIK0lub2k0VHB1cG04NHJNRnVSd3hCb3NTMVViTkZweW5aVnRPN0JNbUJZZmtGQUZPTWlhanBnd0dnVTl1c1pUNm1iZ0l1ZXNHKzByRTVuRjc4T0czWFZwazByb3dwUkt4bG11T3FvWG9pMHIwMi9WMGVIbExxT3ZwZHgxTEU4VXE4ZU0xZGpLZlo1Z2RVY2x5QmdIejdycG44QU1OQmUxUW5BeWUrNm1wWWt2UXcrWFFVeTdvVGNMcWtQTHJpRTk1cVV4dEZZSzZiSmhIWGhCb0NjZkg2Y2pVZFNudzhKbHhreGVrUmFzUDh5R045Vy8zZ3RQakU3cUJnTWpUOUZBRmlRQi94dWxuVitzaUNyblBrUTdiNFFvVnF0Skw2Z1A4czRXdjZMa09nRlV0Wm5oSDcyM1RGNTlqMGNwZ2hqdnJPMUtpTFIiLCJtYWMiOiJhMjM5MWI0N2IwN2I4NWZiZTllZmY0OTNlZmI0MWU4Yzk2ZTIyYjMxMDM1ZTA5NWI0NDg4ZDJhYzA4MDVmYzg3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFuNW9mNENhZ2JsblFETStuUm54QlE9PSIsInZhbHVlIjoiQjRpbVpab21JZjRrcDhUMTJyWDhwN296NzE4M00rTFNHTTdmZUM2cER2RmROSHQ0bGVIRUp5d2VLYWR1TjFJaEpMZUdUUWlmd1pKRm1ET0g1SHg0RVhjTzZZZ2tCMytmZWc1SVhyTXJMdjZMMEpzVW4zQ2ErRTljU25DSkFMYWZLM3hmcStUdVJUOUwxUVR6RmYxS0hrekVZa09hOXlUNjFJVmROUU42bS9kNk43WS80QVk1TEtPa0JGQjdzMTFScSs0ZEVtY2p6S1k2NXZJQUw5d2dXOHR4dUpDdFlhT1dDS0drbmJyOWd1VTg4RjVHTGdscUo3Si9BbkN5aFJGRzgxdXpSRFNqQVRneFFKQ0V1bEpCbWhCbGJVS1NEQ0VONUwyL09GeWNaZjJMV2g0ZUpmbnJ5RUMxRmRoV1g0UFJ5ZEFQTWRRNmxPODhmSk5MYlF2dXNkQklqN3RpNjBLTTFQV2FJY1NaQlNKNzFiVkJHR0ZRSXhzRWZSYmhzbDQ0VENXak10bjc4T2t4cTlQRmdrcXBRY01yUWRrZXZwSnl4TTlwS1lVR3ZNWkdMcEdDM0dJdFZ4d0FLRXNMdWZtUDl0cCtJbG93WkNwN0I2aXhhaU1rUFJvMGRpZ2JZRzVPVytsLzdJSHJaOXFIV1BGcTBPQjJ5YTFNaUtOTmRiTWgiLCJtYWMiOiJiNDdmOGY1ODIzY2RjYWIyMmIwZGU3MjAyZWM5ZjM4MjM3YTE0NzBjMDA3YzE4NDQ3Y2VlMTliMjgxODVhZDRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1255496593 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255496593\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-802700132 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktRZXdqQ0dHdytCRnpoYVNQMndyRnc9PSIsInZhbHVlIjoiVmlTa0lzcUY4ekpQd2xHUkFSeEV0NEptTUlwSFk2RHZtNTVVQXdpcjh2Z2tsTDF3eE1CVkp6RzQ0bzVxRmJ1NEFIcGVuajFRc0ZWNXJEYzVBWVExR0M3YlFNU01TUjRzTUs1dWIybEJ1Qm1kNk1Rd2lDaU1rMnhocWJmZFp6Uk5NN3h5K1BYK2xPWXdpd0xXKzkyS0VOTW1mYU8zd2dlMWxWRmpERmV4YU9QSXIvOUxKOGxzdXdUcW11K2RtRkJyYmQzamlUV2JKNkc3RnNtVG5rRUp2dFQ4d0cwSkJmMlM3ZkhHSDI2MU5QK1ZKRXVUQ3pCdWhCL2JDNDN6b1lZRUd0OWNDUkNLQkRVVnlIcnVjaFpOcDEzbEVLU245VjFXMUFGT3lhTWQ5Wk1WY09UUmdkZ0NYZTdrSjIxclF6aitoNkcrcDRXZFZkWDFnRVozNEhWbEVRTUhBQ0E5ajNVWm9aODJBVXJnWlNTaXZRODZLWS9YbTZIRjV3RzgyN0ZibS83TEpIQnFQMlRtTWpuc3MyMTdPMEtTOEljQXdWeEpjemZlWWd5dkZ6aVFGT0NJd0gvY29QeXFNSlJZT1hxT0ZQZHMrSUhWUkFXbGtraWJQUm51UHNiQ3YwbFlwU3R4MGdpWGRqNy9sRlN5VmJCS1NnK2RaY2doM3NXQlpWbVEiLCJtYWMiOiI5NTZiZjhmZmQyOTM0YTcxOTUyMzUyMzI1NjNlYWVmODg2Y2Q2Zjg2ZmI5OTY2N2Y4ZmZjZWFlYWQxNDJjMGJjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImV4b0N0YmFFR3VZRGt3UEhtZmphaUE9PSIsInZhbHVlIjoiTzdPc0kxVEUzUVNOVUpSVTB0ZmNZMWpUZU44Q29yWnFyMWNkOXBkeE1RejVHTmxQUWk4YXJFR1VNMG1HZEdDZDJBYmNYMjM4cDJDTC9ETzZuNFVpMGN3U2dadXA0b3JuYmN1a3k5NXpXcWdrOE4xZFA0Qk95TGE5L3hsL0pMYnZDYlVTdG4vb1FqSXY0K1hnMzBsRlhPaFEvNVJJWmt5L29PZTVQRUU5THdDamVBdGJ1dkFtR2F4SHNIakxEbVZSWVdNamlTdzFkaEtKcDdkLzBoeU05REsvMTFwSjhqV2RjcnVINnl4QVlaV2xuZGlOaEpPekdxcmxTbXB4MjB0dXBxUmlzMTBmd0pwSW1VUUpPbDVFeEhhT21RMmdWaEpRMXI5ZHIzVnJNMGtVVVpBZXQvNjlSem8vSFFsQ2d0cUlLVmNhazVzdnprUm4zRkNSYVhyaUh1T1I1VkYwdENBdVovKysyczZtMVVYSDZDL1l4anBlc2h3TzFzTHBsVlVrTlpJLzQrR2g1djZTMnFtQnpDSDI2bFF2STl4NVVZZjNYS1dOekZVUGk1QzJkOW9vWGIwOVBSZUVYckF1ejcwd2NrRmhTaHdyQzI1ejZCbWVhMHFoWkhaMWU1Z2VkMDlnNWdHWmV5K0pRbTFKblAvYWs1bldCdjc0OEhrRGV5ZDAiLCJtYWMiOiIxMjAwMmU3OTM3NTc0Njc0ODkwMGZhMjU5MmM0ZTBiZmEyZTkxZjI1N2Q3MTM0NTE4ZjJhNTNhNWFmMzA0ODM1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktRZXdqQ0dHdytCRnpoYVNQMndyRnc9PSIsInZhbHVlIjoiVmlTa0lzcUY4ekpQd2xHUkFSeEV0NEptTUlwSFk2RHZtNTVVQXdpcjh2Z2tsTDF3eE1CVkp6RzQ0bzVxRmJ1NEFIcGVuajFRc0ZWNXJEYzVBWVExR0M3YlFNU01TUjRzTUs1dWIybEJ1Qm1kNk1Rd2lDaU1rMnhocWJmZFp6Uk5NN3h5K1BYK2xPWXdpd0xXKzkyS0VOTW1mYU8zd2dlMWxWRmpERmV4YU9QSXIvOUxKOGxzdXdUcW11K2RtRkJyYmQzamlUV2JKNkc3RnNtVG5rRUp2dFQ4d0cwSkJmMlM3ZkhHSDI2MU5QK1ZKRXVUQ3pCdWhCL2JDNDN6b1lZRUd0OWNDUkNLQkRVVnlIcnVjaFpOcDEzbEVLU245VjFXMUFGT3lhTWQ5Wk1WY09UUmdkZ0NYZTdrSjIxclF6aitoNkcrcDRXZFZkWDFnRVozNEhWbEVRTUhBQ0E5ajNVWm9aODJBVXJnWlNTaXZRODZLWS9YbTZIRjV3RzgyN0ZibS83TEpIQnFQMlRtTWpuc3MyMTdPMEtTOEljQXdWeEpjemZlWWd5dkZ6aVFGT0NJd0gvY29QeXFNSlJZT1hxT0ZQZHMrSUhWUkFXbGtraWJQUm51UHNiQ3YwbFlwU3R4MGdpWGRqNy9sRlN5VmJCS1NnK2RaY2doM3NXQlpWbVEiLCJtYWMiOiI5NTZiZjhmZmQyOTM0YTcxOTUyMzUyMzI1NjNlYWVmODg2Y2Q2Zjg2ZmI5OTY2N2Y4ZmZjZWFlYWQxNDJjMGJjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImV4b0N0YmFFR3VZRGt3UEhtZmphaUE9PSIsInZhbHVlIjoiTzdPc0kxVEUzUVNOVUpSVTB0ZmNZMWpUZU44Q29yWnFyMWNkOXBkeE1RejVHTmxQUWk4YXJFR1VNMG1HZEdDZDJBYmNYMjM4cDJDTC9ETzZuNFVpMGN3U2dadXA0b3JuYmN1a3k5NXpXcWdrOE4xZFA0Qk95TGE5L3hsL0pMYnZDYlVTdG4vb1FqSXY0K1hnMzBsRlhPaFEvNVJJWmt5L29PZTVQRUU5THdDamVBdGJ1dkFtR2F4SHNIakxEbVZSWVdNamlTdzFkaEtKcDdkLzBoeU05REsvMTFwSjhqV2RjcnVINnl4QVlaV2xuZGlOaEpPekdxcmxTbXB4MjB0dXBxUmlzMTBmd0pwSW1VUUpPbDVFeEhhT21RMmdWaEpRMXI5ZHIzVnJNMGtVVVpBZXQvNjlSem8vSFFsQ2d0cUlLVmNhazVzdnprUm4zRkNSYVhyaUh1T1I1VkYwdENBdVovKysyczZtMVVYSDZDL1l4anBlc2h3TzFzTHBsVlVrTlpJLzQrR2g1djZTMnFtQnpDSDI2bFF2STl4NVVZZjNYS1dOekZVUGk1QzJkOW9vWGIwOVBSZUVYckF1ejcwd2NrRmhTaHdyQzI1ejZCbWVhMHFoWkhaMWU1Z2VkMDlnNWdHWmV5K0pRbTFKblAvYWs1bldCdjc0OEhrRGV5ZDAiLCJtYWMiOiIxMjAwMmU3OTM3NTc0Njc0ODkwMGZhMjU5MmM0ZTBiZmEyZTkxZjI1N2Q3MTM0NTE4ZjJhNTNhNWFmMzA0ODM1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802700132\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-611538198 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-611538198\", {\"maxDepth\":0})</script>\n"}}