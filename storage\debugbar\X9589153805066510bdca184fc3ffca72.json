{"__meta": {"id": "X9589153805066510bdca184fc3ffca72", "datetime": "2025-06-27 02:23:45", "utime": 1750991025.030395, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.578482, "end": 1750991025.030413, "duration": 0.4519309997558594, "duration_str": "452ms", "measures": [{"label": "Booting", "start": **********.578482, "relative_start": 0, "end": **********.953519, "relative_end": **********.953519, "duration": 0.37503719329833984, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.953529, "relative_start": 0.375046968460083, "end": 1750991025.030414, "relative_end": 1.1920928955078125e-06, "duration": 0.07688522338867188, "duration_str": "76.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45720280, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0244, "accumulated_duration_str": "24.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.979895, "duration": 0.02312, "duration_str": "23.12ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.754}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750991025.01175, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.754, "width_percent": 2.828}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750991025.0193489, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.582, "width_percent": 2.418}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1247639037 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1247639037\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-892564314 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-892564314\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2008305257 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2008305257\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-59822270 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991019246%7C19%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpSVWRRSjZuUStWVkEzaUxTakFPNmc9PSIsInZhbHVlIjoiNzVNb0tSOXBsZHN1c0JrR2FibktFU3NOSDZwVkV4ZWFBTklzZnVWcmxhaXo3V1BpU0NGdUV6cXpaMEpUYUY0L0YxL2ZNVzBZVTBoQll2TnpUbzZtRk5odzJwdFF6WSs5eDZGVm9ETDVmWTArUzJGK0l4VWZ5OW9ucnMrbE9UbVBCZFBTTzcwdVNUTDJ6M25TYXM3akFCNzJBQUFTUVJKK0NCV3lhbGZVemdTNCtvZXBRand2WkdGQnVST1NSelVsTlBmWjBCSm45a3Rubkc3VWxuN25PenQ2U2Ewck9EaXVFQXpjOGVIVUpsSitvdzFzTXlnSmZrQUtXa0UrM3BxeC9sZVplMENYbVRlSld3bkl0YkNqcEJTQ3JWenZWbnJSTHQ4YVc2MGlDbUFiZGdrTHYrb2dQcXRsOEUvK1BvWUdXTGIvSmdpZ2R1NlZETzJBS0plNW9hdHZpWDJ1TzZsa0FzOG1mY1lQUlJrd0pZRTNlMTRVOStxOHdmdkgvYzVZVUpSN1NqeE9pZms1WWxLbzhxd011ZTA5dnVuaHZjeFVOQ0JiTkd0M0pYWFZBOUQ2RW1xNzdydFl5amdEeWJnM1RwRG5EWTRESGRCRTgzbGJZWk51dldrY2kxVFVrdVltZzJWanpvb24ySmg4cU5XVkxUaVZLTEFkdmlLRjNmMGUiLCJtYWMiOiJkYWI3MzQ5MDI1ZDUyN2NkOTQyMDU2YTEwZjFkNWZiZWI3NzA4MzFiZmZkNGUwY2I5Zjc3MTJmNjI3MGRhYTEwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhQL3FQNXZsSWFad05VdWJXVHFXV0E9PSIsInZhbHVlIjoiMDArU0ZRU2U4d1RKa1I3V0ZweHZhOHRSNXFEUHdONmg1RTR2WFo5bW5GVmRraUJxQ1Y3TDJEckYxUW5BTjlpalVwd09NcEdPZlExbUVDZmlUb1MweE5STzNTeDV6MTNSbnFNR0JZc1habk85elNFZnE4WkN2djdXSFZUWTdkNHBuc3hDUVl1TzRlVXFsSDVLeWdNS3Rnb1hWMWV1MkdxcnNGb240a3FBRkJDc3pUdWloOXl1Y3dTZUtHWEM4eWJWUElsR0ZDUFRYZVYxSWNqaFBMOStuaWxzdXhMYWlDZmk1ZlB3U092RUJzVGJ6aWVlK20yVk9nL2lISlBHbTFIemRUdDN1Rk9LMm5hOEVGZnVPcEdCNFJSY0RodE90Ly9qbHEyZ0hOVzRsU2NJam50NW1YMWJHc3UrN3VVb1MreHhQc25WbU9QZm5aK09ic3QwTzc3cTJzeVhJemZKTzdCWXp0Mm45MXovd091bjlSb3h3U0NJSWM4T0FKaldzV0wvRWJpVkJhbzZ4RHpqZE9CMys1b3dRWk5FVHdCZWtOelN2ZlkvVWN3eDhEK0YvRDlnYk90YVRKK0ljNWNsaEFrVjdycmRSU0E3RDFZV25YcjhZaTNNVUpDREw3S054QXZuemc0b0lUZHJrTHBYZU9ZT1pZZGlDL0JSKy9sR01BV0EiLCJtYWMiOiJhMzA0YmZjMGYxMDBjOGE4MGNjZDYwNmJkOWExYmViMjg5MjI5ZTg4NDBlNjY5ZDgxZmU0MGU2MmQxMmY2M2Q4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-59822270\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1860513730 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860513730\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-725606567 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkJjOFI1WWhpQ2Z3R0E4czBCNWJ2ZlE9PSIsInZhbHVlIjoiMGJ2eU1PT1d0a0pGWkMwcTZwME5VQVJVclg5VEk1TjFxUWpBNUs1NytkeEhuZ25MdlFSd2V5QkttYUxPNFBiU2QrTE8xRlhlUDMxdTFRRS9CYzJEc1NUMy9UUnR5OEtiU2pSY2JvYXVvR0RBV3cyZW5CKzNPdDZ0TGkrSU1RaHV3Z0VVb0luWXNzSWxKOW1mbzRsUE1BeUQzMDVtemd3MUVncXlpbEtYMEdKSHVPMWlENXlyVG1QczZoRU5pdmpSUzBaQk03bGQ0RFVnQ3JDaXdrc0hVSmw5aFliRGNBYzk2UFJZMnhuRkpuZEdxc29DeHBISC9jbGdtVkYzTHBFT1ZKcmE2b1RpSGFvL04wb2p1MVNnb0JkaUFteEl6V1hYMWxTOCs4bXE5QlIzTFVSYlV2MUVscklmMWhHYk9POFV3a003MG5ZN2p0QVRKYTJ4UENyTzAzL3NBblFta3B3aHA3NDVSdW5PcDFpS0VyS3JESlVzZEpLSmtiT0xwT3dEY0hET1gvTlBFaThMTUJ4cVJ0TUVLcDNULzJMbGIzQXEzdDZ1UTZWNGRMK2laWSt2TWpkSlY0bEtxeERGSkZsWDJiYkFjaTF2VG9nTjYzYThyMEY5VU5CNVdCa3hPalZtZW1zMis4ZVFDRUhyZlhQQVkvNWY4Y2NBV2hhSHpvNTkiLCJtYWMiOiIyNTI3NGRiODNlNjQ0Nzk3MjA5YWQxMWYxMWNjZmQ4ZjVkYmNlN2FlOTU4Y2JlOTU4MzVkMjA1NmM4NzNlMTkxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImpkN0J0QUFSaGdEYm9oQjI4VWJLZmc9PSIsInZhbHVlIjoiSml5N1Fua2JpYnNIWHBYWGVvdGI2bTJPaCtVQzFUekswMmJ0dkxqazBMbU12dTBSL2ZsRHhiR3ZBS1ZDVG44bU0ySkdvSU1QVnE5SzZQTnJGTDZMNndaQ0tya1VScHlpQ3VMUGUyTGd4WUY2NFpLanpyUXRrek5wRW1IcjhoaCtLVFQwRHdZdEI1SGFyZW5JZGFmaHJVeHVXVUZXb2pCZW5XdUZsM3g4NzF0WEtRdEl6K1AvVlZjZ25GUlNtNmdBa0ZCUk1oOXZSeE5LVXJuVnpxZUNoN0JuUEdEOGxJblJoaHdCR3RLUmFMZWdRcnhXTC9tdEtvbGx4aCtQS3NZQ3o4MGxGYTY1N3A4SVB0YUovdFJKNDU2bERUa0xEN1NnUFJPWVZhQkQ5K1VJc3ozLzE2aXh2TlZuK3VaUTRPRFJtSzdNVTZGMEZoSFNUZFhvWUNCc3ptM3dTT3dvczkySnB3MmxtRUtGSFJzQW1mem1JY0hCRWRXSnFVTDB2Qm54T2FqaU5kMHBrcGlkRkRObUk4UERqQ1VkblpJd1Voc2ZIazIxb1VtWk1yOXVCdXhIUWNoZ3V1VkNjZ0NrMnlJN1VsZTd2UkN3Y2RGZExtaThiTHRnMXVMOWh0MExXTFJvaUxJTmM0NGJrM2ZqWjQ5RkszYytqaGNRRVZxTHNOUWYiLCJtYWMiOiJiMjZlN2JkODg3Y2JhMmE4YzViY2M4MzVlZDg2Y2Y2ZWY0NGZmOTJjYWY3ZDdiZDEzYmY0MjE3ODJlNDA4NjNhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkJjOFI1WWhpQ2Z3R0E4czBCNWJ2ZlE9PSIsInZhbHVlIjoiMGJ2eU1PT1d0a0pGWkMwcTZwME5VQVJVclg5VEk1TjFxUWpBNUs1NytkeEhuZ25MdlFSd2V5QkttYUxPNFBiU2QrTE8xRlhlUDMxdTFRRS9CYzJEc1NUMy9UUnR5OEtiU2pSY2JvYXVvR0RBV3cyZW5CKzNPdDZ0TGkrSU1RaHV3Z0VVb0luWXNzSWxKOW1mbzRsUE1BeUQzMDVtemd3MUVncXlpbEtYMEdKSHVPMWlENXlyVG1QczZoRU5pdmpSUzBaQk03bGQ0RFVnQ3JDaXdrc0hVSmw5aFliRGNBYzk2UFJZMnhuRkpuZEdxc29DeHBISC9jbGdtVkYzTHBFT1ZKcmE2b1RpSGFvL04wb2p1MVNnb0JkaUFteEl6V1hYMWxTOCs4bXE5QlIzTFVSYlV2MUVscklmMWhHYk9POFV3a003MG5ZN2p0QVRKYTJ4UENyTzAzL3NBblFta3B3aHA3NDVSdW5PcDFpS0VyS3JESlVzZEpLSmtiT0xwT3dEY0hET1gvTlBFaThMTUJ4cVJ0TUVLcDNULzJMbGIzQXEzdDZ1UTZWNGRMK2laWSt2TWpkSlY0bEtxeERGSkZsWDJiYkFjaTF2VG9nTjYzYThyMEY5VU5CNVdCa3hPalZtZW1zMis4ZVFDRUhyZlhQQVkvNWY4Y2NBV2hhSHpvNTkiLCJtYWMiOiIyNTI3NGRiODNlNjQ0Nzk3MjA5YWQxMWYxMWNjZmQ4ZjVkYmNlN2FlOTU4Y2JlOTU4MzVkMjA1NmM4NzNlMTkxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImpkN0J0QUFSaGdEYm9oQjI4VWJLZmc9PSIsInZhbHVlIjoiSml5N1Fua2JpYnNIWHBYWGVvdGI2bTJPaCtVQzFUekswMmJ0dkxqazBMbU12dTBSL2ZsRHhiR3ZBS1ZDVG44bU0ySkdvSU1QVnE5SzZQTnJGTDZMNndaQ0tya1VScHlpQ3VMUGUyTGd4WUY2NFpLanpyUXRrek5wRW1IcjhoaCtLVFQwRHdZdEI1SGFyZW5JZGFmaHJVeHVXVUZXb2pCZW5XdUZsM3g4NzF0WEtRdEl6K1AvVlZjZ25GUlNtNmdBa0ZCUk1oOXZSeE5LVXJuVnpxZUNoN0JuUEdEOGxJblJoaHdCR3RLUmFMZWdRcnhXTC9tdEtvbGx4aCtQS3NZQ3o4MGxGYTY1N3A4SVB0YUovdFJKNDU2bERUa0xEN1NnUFJPWVZhQkQ5K1VJc3ozLzE2aXh2TlZuK3VaUTRPRFJtSzdNVTZGMEZoSFNUZFhvWUNCc3ptM3dTT3dvczkySnB3MmxtRUtGSFJzQW1mem1JY0hCRWRXSnFVTDB2Qm54T2FqaU5kMHBrcGlkRkRObUk4UERqQ1VkblpJd1Voc2ZIazIxb1VtWk1yOXVCdXhIUWNoZ3V1VkNjZ0NrMnlJN1VsZTd2UkN3Y2RGZExtaThiTHRnMXVMOWh0MExXTFJvaUxJTmM0NGJrM2ZqWjQ5RkszYytqaGNRRVZxTHNOUWYiLCJtYWMiOiJiMjZlN2JkODg3Y2JhMmE4YzViY2M4MzVlZDg2Y2Y2ZWY0NGZmOTJjYWY3ZDdiZDEzYmY0MjE3ODJlNDA4NjNhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-725606567\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1357022078 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1357022078\", {\"maxDepth\":0})</script>\n"}}