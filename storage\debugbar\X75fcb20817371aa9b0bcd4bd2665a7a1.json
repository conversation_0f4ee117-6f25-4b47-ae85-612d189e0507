{"__meta": {"id": "X75fcb20817371aa9b0bcd4bd2665a7a1", "datetime": "2025-06-27 00:46:55", "utime": **********.30024, "method": "POST", "uri": "/bill/vender", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750985214.785732, "end": **********.300257, "duration": 0.5145249366760254, "duration_str": "515ms", "measures": [{"label": "Booting", "start": 1750985214.785732, "relative_start": 0, "end": **********.169793, "relative_end": **********.169793, "duration": 0.3840608596801758, "duration_str": "384ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.169806, "relative_start": 0.38407397270202637, "end": **********.300259, "relative_end": 2.1457672119140625e-06, "duration": 0.13045310974121094, "duration_str": "130ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46269000, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x bill.vender_detail", "param_count": null, "params": [], "start": **********.225354, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/bill/vender_detail.blade.phpbill.vender_detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fbill%2Fvender_detail.blade.php&line=1", "ajax": false, "filename": "vender_detail.blade.php", "line": "?"}, "render_count": 1, "name_original": "bill.vender_detail"}]}, "route": {"uri": "POST bill/vender", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\BillController@vender", "namespace": null, "prefix": "", "where": [], "as": "bill.vender", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=1205\" onclick=\"\">app/Http/Controllers/BillController.php:1205-1210</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00304, "accumulated_duration_str": "3.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.201328, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.447}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2117739, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.447, "width_percent": 17.763}, {"sql": "select * from `venders` where `id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 1207}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.214883, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BillController.php:1207", "source": "app/Http/Controllers/BillController.php:1207", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=1207", "ajax": false, "filename": "BillController.php", "line": "1207"}, "connection": "kdmkjkqknb", "start_percent": 84.211, "width_percent": 15.789}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Vender": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=1", "ajax": false, "filename": "Vender.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15"}, "request": {"path_info": "/bill/vender", "status_code": "<pre class=sf-dump id=sf-dump-2033807126 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2033807126\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1708446836 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1708446836\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-517997074 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517997074\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1325201788 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>4</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/bill/create?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750985213046%7C68%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpkemRGWTdBcmFjNFZ3M0NlM2ZuYkE9PSIsInZhbHVlIjoiODViY2lsdjFHeXcxVUdmcFIxVWNYWEF0dWs1NEt1b1U2aEd6YXVFcmFxTVdiM0tvc25tTjgrQUV6RWlSUHo0d2hseENmODhRanB1blEzaXoxeDE1RnBNdEpiUnc2VUxxSGE0Tk9LK2cyTWlIM3FNU3R0c3UxYnhNeXUrRTlzVVh5WXhKWEVUYlgyOHhLVTBxc0tMaDhOcWdQTHU4Q0RxV00ydDl6YWdIZ2UwdEs5RVdZeTdnL2pJSDh6aDZueFpaL29Zc2NrL25XS0M1YTNRSUtJQjhHUitUb2NyNzVjMkZwU3lKTGtpRjVrcU9FMFdwMm1Qc0FRQUpqTjdWZFpqQ2k2azRiVjM0anErMGx3ZEtkOHdaMnoyak5oU29CaEVJNW1HYVRPY3lTTTAvbGxGOUpmMUV1Z1V2UHEwM3hOUXp5dVRHS3JwdHBMUUhJUGtFK2lZNXd2eDFOYURTR3ZxcTcyNmhzRndZS09NRVNHUzJ0Y1BXSEQyTHZyVjVLT053bXJrMnJlOEdtOWNhZ3ZYdW03ZVFCT2hkOTZDcUJsRWQ1VHNaVGJZbFI4T25Td2QvektpK2NzQ0VZTFBCa2JiV1ZvaWNzQXhrY0RybFp3ZEkwVmhPdVZLbFZLN09FSGdnMEdrazFyNHBLZWdJdkZBaldvOGRqdDJiYVhWaENIVHUiLCJtYWMiOiJmYjU5NmNiNzk1NzhmOTQyOWQyYzE1Y2VmZDBjOTlkZWIyMTBkNzE4ZDFiMDI3YTA3ZjJjODZlZmM2NzkzNTYyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjVxSldPSm5RYm1tRCtjRHJWbGhIdGc9PSIsInZhbHVlIjoiZldZbU9zc0xMMDBEZGxoVVZnUjlQZXhMWG52WUNWZDZpZ3dXSzhTaFpDVThPWTlhUkxqOGZFZjBPdDI1aDlPSm1iWFJyU0VmODB2UmdZYlo5clpiODN6RjlMSEw0N3RBN0JtNmtqSnBwOEpqL1lGeXhtYnEzTTZCSnBrSzBNd1hDTnVMekxxOEVGb2VuOU9rWEd1N3hBcW9uekpZdVlJM0pmbEJHdzJLaktkempyWVAvUmxMbC9FeC9ZQnJlYTIxYU55MlU3b1lINW9pb21abzFsWlRPSUFES1V0UjRaWEF3c01ycVBkN3VSUnVFNDlkWTYwVTNQeVp2SldZdzFGZE5zWi9sNDhBRDNXbHVSaFBKTWNnb2FkajlZclI2ejczUkFrWmhSckF0ZjdiSmVrSVFKcnNYWDRicWN5MkgyTGYyUEVJK3Z1bm9SNERuNmJ3Q00wYWZXQ3hKRGY3L1NHUU5EbDJFUUZmT3RrQWZnMlpZV3I5OHFWMmNVQlQ3QkwvOGNVT1dyUjdEeHVsL2kySkVabjI3em1qMU9hWDVWM3JtcjNjVzZCd0FlYTFCNG1PVVp6OHV6cFN3WXBWOE45R0hIMnducStaSVBvN2syb0xoQmJjRlh2VUZYVjE1YlZUVWtoZVZIRnYvVUxVQklVU00zb25yNFBYbHRuRHdCVXciLCJtYWMiOiJjZjU3YWQ4MjliN2Q2NzkzYTgyMDFmZTVmOTVkNjI4ZWYxZjYxYzNlZjdmY2ExZWUxODFjNzJmMTUxOThlOTBkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1325201788\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1087554643 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1087554643\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2011177333 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:46:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImUvWURUNmhEWnV2VTNYZWR1SDRsdnc9PSIsInZhbHVlIjoicHVvbG01UGZKdUw5SFZBQ29ncHZYakxXWDZaYUFENlgxelJwMVVDbUUzUEYzY281cHpuNDFVV21rV0hMdzU2WkhvQnp0cE5OZXpxNDZUcm1pOHo0cEJxVldTRkNUTmRJcURRcTQyVmVnV2svUWh6TUxLZXJpZXNBOW1HV09HMUQvbldya04zQTRJTXIxc0xKQ3Q0UmlLWDhmUEdiOFFvaHNaY3laTGQ4SDFNc1FKRnR2eW1VbU5yQVFJdWdDKzFCQzRtWlJ4SlZIL0I1TnJuQ1RwY2V2YkhZSDU5S3IvRmFQeEFoWWM0Q1daL2c3RloyLys1MDJQbjVBTjVlSUt5Yi9FeGd0Z1VYSFF6VWVIY1g2Qk01cFE5SzB1REpvTGJhWk5rTWpHYnZKQnhXaU5XOXpUbUIwL3dpSXUrQWNLRFJRT0dxOEJja2xyREVZb0NiRkZzeWZDdFJJVlZMMTh1QkM5UWVoeHUwc3J5NWJ6eUcyVFFqRFJBaWZUajMzWG80eEViK3BVaWFtUWM4T1pqZzhVemkwVW5hVjYveFMvUCtUUjRtV1JYZGEwV29ObkwzeUxIU2pwSnhITEp4aElPR3l3NDVKbWhTWHhwb0hTTzlnVnFOTHJsOHhsRGt3ejdkR0d2ZGZTajJqU0R5RGVlek1tcWUzQi94YnpJbVBpUzciLCJtYWMiOiIyMDI4MTQ0MTBjODhjYTA3MjA0ZjVmNWIzZjZjNzA4YTExNTU1NzgxM2FmNDc2NDEwZGNkMDE4M2RlOGVmNjU2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:46:55 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InQ3TUFtZ3phQWhmaFlOeERMRWs3TlE9PSIsInZhbHVlIjoiS2FuazV1dXNoS1dyV0VmWVJQN2VNaC9iRytWSXFoNGJBSUdXWVllcmwxSkVRZjlJVnQyazh2NnAydWxVc1NlZ2FudFBwd1A1RmRGdXQ1S0RBWjcrT2dtQWhMV0I0a011S2U3V1Y4RnBRaW9udG5pbnJPeUpKUXpRZHlKNm1sNDYxV1hZZU9Ob092WGhKYzIrZkIrNG45WGJIZ0lkVzhhQzZPOENZNkNvQTFFTHRBSldINHlwWWN6MlVaZmV2RFJxNXJOaGRENmJZZzNHd0NDeFBjU0I3RE50bGcrUFlOUWZaeXJLcFRhUm43cnBMYTBjUzJBdFRtcG1ETGN2OTRuV3U0WFhZNU5BZWthZWUybmM2a1RTT0VGa1oyNFA1aXN5Ukc1VllEc2dBMlB1R0ZCYjFhajYzU0JvZTh0bS9yQjF2KythbGZkcEhwY2dDTENENlVGZVlseFBodzdyU2dWWHJ4UVQzdVV5MW1zVnM5NVZOMXZaNEduU25jS2YrL0w3MmM3eG9qbkNtWWFGYmZzbVQ4d0RnSVVIOG40Szh6dXQzVW1jSTNGelpQcmc2amliNkVmK1gzSGZTR29keHlQWmVmNzl0dTZSZWdvUTBvdURZMmJvR1F5UVZtSEFVcW56MXJSL1d3Q0lROEo5Njg5VWpGTTJwMWowK252UDV5VjQiLCJtYWMiOiJjNjU5NWYyZmU3MGQxNTliOGEwNmY2MjhkZGI0NmJjMWMzMjdmMjMyMGQxZGY3ZTNlZGM2MWRhMGYwMDcwNmRhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:46:55 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImUvWURUNmhEWnV2VTNYZWR1SDRsdnc9PSIsInZhbHVlIjoicHVvbG01UGZKdUw5SFZBQ29ncHZYakxXWDZaYUFENlgxelJwMVVDbUUzUEYzY281cHpuNDFVV21rV0hMdzU2WkhvQnp0cE5OZXpxNDZUcm1pOHo0cEJxVldTRkNUTmRJcURRcTQyVmVnV2svUWh6TUxLZXJpZXNBOW1HV09HMUQvbldya04zQTRJTXIxc0xKQ3Q0UmlLWDhmUEdiOFFvaHNaY3laTGQ4SDFNc1FKRnR2eW1VbU5yQVFJdWdDKzFCQzRtWlJ4SlZIL0I1TnJuQ1RwY2V2YkhZSDU5S3IvRmFQeEFoWWM0Q1daL2c3RloyLys1MDJQbjVBTjVlSUt5Yi9FeGd0Z1VYSFF6VWVIY1g2Qk01cFE5SzB1REpvTGJhWk5rTWpHYnZKQnhXaU5XOXpUbUIwL3dpSXUrQWNLRFJRT0dxOEJja2xyREVZb0NiRkZzeWZDdFJJVlZMMTh1QkM5UWVoeHUwc3J5NWJ6eUcyVFFqRFJBaWZUajMzWG80eEViK3BVaWFtUWM4T1pqZzhVemkwVW5hVjYveFMvUCtUUjRtV1JYZGEwV29ObkwzeUxIU2pwSnhITEp4aElPR3l3NDVKbWhTWHhwb0hTTzlnVnFOTHJsOHhsRGt3ejdkR0d2ZGZTajJqU0R5RGVlek1tcWUzQi94YnpJbVBpUzciLCJtYWMiOiIyMDI4MTQ0MTBjODhjYTA3MjA0ZjVmNWIzZjZjNzA4YTExNTU1NzgxM2FmNDc2NDEwZGNkMDE4M2RlOGVmNjU2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:46:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InQ3TUFtZ3phQWhmaFlOeERMRWs3TlE9PSIsInZhbHVlIjoiS2FuazV1dXNoS1dyV0VmWVJQN2VNaC9iRytWSXFoNGJBSUdXWVllcmwxSkVRZjlJVnQyazh2NnAydWxVc1NlZ2FudFBwd1A1RmRGdXQ1S0RBWjcrT2dtQWhMV0I0a011S2U3V1Y4RnBRaW9udG5pbnJPeUpKUXpRZHlKNm1sNDYxV1hZZU9Ob092WGhKYzIrZkIrNG45WGJIZ0lkVzhhQzZPOENZNkNvQTFFTHRBSldINHlwWWN6MlVaZmV2RFJxNXJOaGRENmJZZzNHd0NDeFBjU0I3RE50bGcrUFlOUWZaeXJLcFRhUm43cnBMYTBjUzJBdFRtcG1ETGN2OTRuV3U0WFhZNU5BZWthZWUybmM2a1RTT0VGa1oyNFA1aXN5Ukc1VllEc2dBMlB1R0ZCYjFhajYzU0JvZTh0bS9yQjF2KythbGZkcEhwY2dDTENENlVGZVlseFBodzdyU2dWWHJ4UVQzdVV5MW1zVnM5NVZOMXZaNEduU25jS2YrL0w3MmM3eG9qbkNtWWFGYmZzbVQ4d0RnSVVIOG40Szh6dXQzVW1jSTNGelpQcmc2amliNkVmK1gzSGZTR29keHlQWmVmNzl0dTZSZWdvUTBvdURZMmJvR1F5UVZtSEFVcW56MXJSL1d3Q0lROEo5Njg5VWpGTTJwMWowK252UDV5VjQiLCJtYWMiOiJjNjU5NWYyZmU3MGQxNTliOGEwNmY2MjhkZGI0NmJjMWMzMjdmMjMyMGQxZGY3ZTNlZGM2MWRhMGYwMDcwNmRhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:46:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2011177333\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1511753405 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1511753405\", {\"maxDepth\":0})</script>\n"}}