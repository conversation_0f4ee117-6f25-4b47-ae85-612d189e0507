{"__meta": {"id": "X09d690aaf3c673e89d070b8ac577c137", "datetime": "2025-06-27 00:25:46", "utime": **********.407769, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750983945.952628, "end": **********.407787, "duration": 0.45515918731689453, "duration_str": "455ms", "measures": [{"label": "Booting", "start": 1750983945.952628, "relative_start": 0, "end": **********.35434, "relative_end": **********.35434, "duration": 0.40171217918395996, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.35435, "relative_start": 0.4017221927642822, "end": **********.40779, "relative_end": 2.86102294921875e-06, "duration": 0.05343985557556152, "duration_str": "53.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0034900000000000005, "accumulated_duration_str": "3.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.382476, "duration": 0.00256, "duration_str": "2.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.352}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.394222, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.352, "width_percent": 15.186}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.400452, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.539, "width_percent": 11.461}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2097476445 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2097476445\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-287860707 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-287860707\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1162616430 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1162616430\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-228824049 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983943003%7C54%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im01Y0t6T0VHUTFtVUtWYjV1MlBoTXc9PSIsInZhbHVlIjoiZHRJS0VwQStkcVpDd3h4bDc1b1N1VVpwS3hONm5uVThGMEhTdUNMSDZjbEo1RlEraDJLNTIwU2J2cUY5dXhFODRJOS95V0Y4ekJ3bWcvakQwMGRKVytQc2syU0pxNVlwMVJwSmdKR0RXWnpNcm9LTExyeGQzQTAxSFlaUWVKVGYwa3BoUXludkpnczhibkpWc2JCdXkvczBKNlJPdXFXNXFWWFNEbk5uUnhWUlZXWGJLdzg0U0EvQmFkcFl1YVpocDRUWURXMmY0dUZlZ2RWUDVMdmpQd3pNU1YvdVIrNGtkOFd0Q1dLc0ZZUjRmR2JTVHJXYVFlN0g0dGt1K0NvRTE1WEp4SXBGWGFHQ2RkQnNyV1pjVUxJdXVtdWNSS0Njb2xybDZoM3l1Qy83STdNN0ZCWXM4cTNmZlBMR3d6ZW01bjNHeWRxcStURDREaUY0QWp0UEQ0eXloU1lsajJzQkoxeTFLdCs5VTlpNmNpU0RQYW9VWkxKcWNYUnU2THBQejczQSsvS3hQb2tXMUkvMTBTSk9OZW1TVFYzK2d5WitCazdjU2l6WFp3bGJ6anI3a0U5T3dDcm1ub2d1YXMrVUwrL1MxR3VkMUhETjEvbHkrVnJGcVhRdTF2WXE1OUdJTk5LVFFKRldqc1hBSU1VMi9Da0RqN3I1WjZNMDZtYUsiLCJtYWMiOiJlYTM3NDY0YzY4NTdiMmIwODliMzQyMDY0NTg3YTUyMTE5YzVlMTAyYWQ2YTQ3ODMxOGMyN2RhZGNlMzI0NjZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhRTm9FZGw5R01sTDhGQTQvWFgxaXc9PSIsInZhbHVlIjoiamgwRnhUSzJPN0tuSXF1b3AwMjdqeVBoRWxtRGdOaFdFbUVUbzhoRjRSTUVtUTNpMm44UytsTXdCdzUrV0ozdHpHZTl4R0VjOENCN21ZYVBINWl3MWVGUzE2eXhRTC8xa2dNUUlHeE8yV1VmZ0hZY0gxTGpiemhETXdOL2ZHUVZheE50VVlCbEYzby9INFVLUmloV29zVU8vbGlZNzQrT0NBeUNPT2NnOXBRd0VISlp2YWJSYkxqZDRPQzNsYllOby8zOEhxTVZGUFM4M2htbWxBaTZxZUtLNEJCN0cwNEtPZHVlRG51c0pRM0xPUXRDT0kweVkyTllSWGxQNlJxRWxKY3NmampQUlk2TnZUQkN5Tjg1RThlMlpIZVB2WDROSzZTOUxLMnV3a1pleU9VUk12UVpwaHdFejVzemU1ODVlV1pkZWFsY3FHMmo2Njcva3U5Y0dHNFRDKzJ2Zm5VQVdKUWp4bytaNVNjN1A1c2hEUmVtVWdRU2x2aVNXRzQxRGp4VStob2t4SDBGVHdPVjN6eUlOUGtmRnRaL0xPTzlyQVl1MnFLck9WbDJiNXdzdFJNRWtoYm1iNi9RdzhNdmVuYmlKOUEzVzZGY3ZzUmFQQTZjV25QZ2U3ZlllRURCNkdGcCtacUZSemhuN2hiM2hOM29xZVB6OUdBRnBoMk4iLCJtYWMiOiJlZGI3MTVlNGY5MzcyZjY1ZjEwOGQ2NDM4NGVjOGJiNDBmMDEwMTIzNmE3NmMzMWM5Mjg0YWY3MzBmMTM5NDAxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228824049\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-875726181 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-875726181\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1083860914 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:25:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D; expires=Fri, 27 Jun 2025 02:25:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFMdW9PTTJSZFpuME9sZ1ByeHIxVVE9PSIsInZhbHVlIjoiOVRJYjMvZDg0bzJnN1lsOExaN1VrWjc4c0hhSmx3VkRHTGpyS3Y0cHE2RFJjSkhxK0ROTk84ZERlNWQ0b3ZqYTJ3VjcyMk90ak9yQVg4Nk80YWV4akRPTm5WTXZlNWttQ0JIL0UyVElLSG9Ycnd5bk45QlZqWmpDQXN5K0hhQURGUlpPL3VjSi9oT0JxS1hIQUQwWDVodUlnRm5RbXRWUDBQNDBEU0V0aGVFZmVLbHF1NEZCSnBDc215TWZadlNBMGJsWGRXUUF0em94dktSbFBYWE1hSGk0aGhhY016T0l4Yjg2ektyR3ZqOUR6aHRvVmd5UVRrdHFoS1dNQStiRld5NFRldk9zS2s3UmpLdUlWKy84QjNQYm9QanRSdkVTM2lYNTY4M0hTcjZrTDNJUk9SbkRrUjJBUXQ4TW9kbmpRenZpS0I2L0tOOHduUSs1RklObjFVb1R0Z2J1d24xR2FJZEhrL0xvWWVxZHRpd201TVpMU25qVTVvTW56clF5M25Ua1N1Z2M0ZXRreEdRcGZkL3p2Nmd2YWpkTUxVbGI0SVZ1alU4Znd1Q0ROV00rT3Z4SlR4ajZpYUJZODZyb2UxbThCa0JkMENZdisyZmhLNmp1cTF0bVJYSkVsSHBrc1p6ZnNNTVdmY3FHUDRpWWZGd295RVgzY1BsVmZyeVMiLCJtYWMiOiJlNmM2NTc4ZWMwMGY5MzNhMzYyZjNlYWZiNjUxNzJkMzYzYjc0MTBkNWFiM2Q0M2EyZGUxZThmOWZhNjU3ZDUxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:25:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D; expires=Fri, 27-Jun-2025 02:25:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFMdW9PTTJSZFpuME9sZ1ByeHIxVVE9PSIsInZhbHVlIjoiOVRJYjMvZDg0bzJnN1lsOExaN1VrWjc4c0hhSmx3VkRHTGpyS3Y0cHE2RFJjSkhxK0ROTk84ZERlNWQ0b3ZqYTJ3VjcyMk90ak9yQVg4Nk80YWV4akRPTm5WTXZlNWttQ0JIL0UyVElLSG9Ycnd5bk45QlZqWmpDQXN5K0hhQURGUlpPL3VjSi9oT0JxS1hIQUQwWDVodUlnRm5RbXRWUDBQNDBEU0V0aGVFZmVLbHF1NEZCSnBDc215TWZadlNBMGJsWGRXUUF0em94dktSbFBYWE1hSGk0aGhhY016T0l4Yjg2ektyR3ZqOUR6aHRvVmd5UVRrdHFoS1dNQStiRld5NFRldk9zS2s3UmpLdUlWKy84QjNQYm9QanRSdkVTM2lYNTY4M0hTcjZrTDNJUk9SbkRrUjJBUXQ4TW9kbmpRenZpS0I2L0tOOHduUSs1RklObjFVb1R0Z2J1d24xR2FJZEhrL0xvWWVxZHRpd201TVpMU25qVTVvTW56clF5M25Ua1N1Z2M0ZXRreEdRcGZkL3p2Nmd2YWpkTUxVbGI0SVZ1alU4Znd1Q0ROV00rT3Z4SlR4ajZpYUJZODZyb2UxbThCa0JkMENZdisyZmhLNmp1cTF0bVJYSkVsSHBrc1p6ZnNNTVdmY3FHUDRpWWZGd295RVgzY1BsVmZyeVMiLCJtYWMiOiJlNmM2NTc4ZWMwMGY5MzNhMzYyZjNlYWZiNjUxNzJkMzYzYjc0MTBkNWFiM2Q0M2EyZGUxZThmOWZhNjU3ZDUxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:25:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1083860914\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2098706325 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2098706325\", {\"maxDepth\":0})</script>\n"}}