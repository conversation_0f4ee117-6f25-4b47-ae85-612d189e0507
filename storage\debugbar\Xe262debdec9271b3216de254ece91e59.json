{"__meta": {"id": "Xe262debdec9271b3216de254ece91e59", "datetime": "2025-06-27 02:26:24", "utime": **********.61363, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.18511, "end": **********.613644, "duration": 0.42853379249572754, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.18511, "relative_start": 0, "end": **********.566741, "relative_end": **********.566741, "duration": 0.38163089752197266, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.56675, "relative_start": 0.3816399574279785, "end": **********.613645, "relative_end": 1.1920928955078125e-06, "duration": 0.04689502716064453, "duration_str": "46.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45273096, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00197, "accumulated_duration_str": "1.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5965452, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 76.142}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6067312, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 76.142, "width_percent": 23.858}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1770137149 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1770137149\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1080254138 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1080254138\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1329887259 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1329887259\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2115745228 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991174265%7C33%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjNMeFFtTzBBN0pnTVVISlBMb0JTQlE9PSIsInZhbHVlIjoibERpYmprUlNLOXlLU2NlaUhFTTFoSTVyNEd3ODZLeHRQMFhTVTJFMEEzdHFWWURIVVpFaVNmdGcrTUNKaEREM0tLNUFmZldObkQ5OGhyS3pSeUhFN0JPWW5GTUlBWU9HNVZMR3l6dllPT2REM2xzS1c2RVNaWTdpcFB6ZGIwakFwR2l1bzMwMXQ5RkR0d3FCV3pXbUVJYWJUNThFTUROOEhSdXJ1dTVHRm95ZjdmUE1KSUd6dXVybTB2ZXhOYm9mMExQWStJeDQxVERWc3VHMHFZVW1PYVJISFBFUUptT0tnTDF0VnEza0UzTnVWQTBudkoyUFJGd0hObCtNb3E5Qk15b1Z1SkpQbWEvYTcyOGEyNnJDZklWWTFseDFRYUs5VXN6SmRnUkF4ajhSdk1YTTJ5UzRscUppa3YyazBNUGljSG1Ma2lPM1owNUN5dXlJR2JpeFVweit2UlFtZkJQRS85U1NwQnRXY1Z3V3lnT2pibElnejBScUcyTi9kS3ZYQ1c4V1ViVVhoQzJOSW1tZWxYcmVXZlR4cWdRRE15MEM4YWg1bVJhMWdPZEJ2bzZ6OEd2RS9WMWo5d3NvZWM4a2I4SjgrMXpTZFM3STVraFhlYlpWWUpPVVBVc0Q1cGlONmplYjdyT3ZnWi9KWXVxQjJrZXhuSFVYWDY1ZmJ5OE8iLCJtYWMiOiI5NDA5NDBkYzFmNWQ2OTA2ZGI0M2Y5M2U0YTc0NDZhYjhmMmY1NzllMDRlNGU2NDQxNWY5ZmIzYmE1YWE2NDMxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjA5eGg3WldLVVVmRjl0ZHdaUldFTkE9PSIsInZhbHVlIjoiOXlpK1QyOVZpMUY4UTUvSVIxRWRmZzFNOENQSlVQV1gvcnYrYSt6VE9MdlF1TnVYWUcxY2tMN1RmWDlDdUJ5UU9HajRRNkhlaFVkY2x0WFJzMGFWNkliR0pBZ09YUDRaUDFwMmZWRVRXUHpmMXFJYWNXeHljZndmd1ZRZ29makRwQURESEc2cmNDSE5GMGluUFk3RWhQU1ZLS0FNdWt4NW5Odi9FY0xvaFFiR1FyMi9BZGpDSGZ6bnNOM0FGbTM3REFRZzRzbG5ycGxXZ0dqekJBVjFNTngvaWttVHhHR041d3MwS2piRzc4NHRuS3BENFRGZGsrcVJCTHBKdFN3RGpQUkZQd1d6NGtoWE9zNFlaV1NncHFWNzc2UUswbWorOGwxa0wrV3k3WjEvaEk5OW4yWHVyZGFKTDI1bFdvWXpsclVVZUY4L2c3WTRtcEQxUDF1aFBoSGM0VHc5Q1JPenc4Njh6YmhHTmQwVXZ4bXlBNmkwLzY1dllaMUgxd1ZNOFpZUjhlbTBLZ24rS3JweGlMQ05uWXpTRlRPbDFobWNndUVheTZxN2FwV2hMM2p3ZGNMQ2ZIV01BZmtWUlBlT20zaDlKS1l0dkNJaXFqanBKWUhEc01Fc3d1ODNUZkJBRGxaL1VOWHlqR1QzRnI4OWcvdk9NMWlMWndIVTMrY1ciLCJtYWMiOiJlYTYzZjNlYmJjOTRjN2I4YmNhNzdhOGQ4OTYwOTI3NzNjZGQzMGM5NzAzOTk5NTA0MGJiYTQ5YTY0ZTNiMTk4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2115745228\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1372832471 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1372832471\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-998389424 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:26:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBPN2p4eGpCbTg5WWp4alNoR1hNbnc9PSIsInZhbHVlIjoiVm5DQkJtMCtHcnp6OGVqelZEU1lZeVJISzVrV3FyNlg3VEdSTVlPSTd0M0MwZDFFTm5ma1AwcklPejYyTUdzUVV2MlBsR29lTHhKeEFUR1pjSUh3U0JQRm94MUF2L0VlQ1FPcjNGQWlGOGY0MjJjbW93QWs0ZVB3LzRESVpoa0ZTM3dVNjBUb1RwcWU5aUpXcXQ5VjZxTUV6WFV3THl5T1ZjcCs4SlFFTGVuOW1sY3lUWFJUUVZPYWc2N1ZWc3REeXRjS1V2YWIwQVMyUit0UFpTOCtWOXpiby8xY0tVdVplYzRjZU1KRm5peWZmQUJxa3NLcDhxZ3Z5bndpN01MNndXd1BBTUlKZXRhZjVHVjhLSkZoWVBGc3RDcGZiY3BraHFMNzY5aEs2OVVTU1hMc1Z5aGtrRGcremh1dmUxS1JlRzBsZUVjV25OOEtLNHJaSHRySTNGZjNNY0oyZEgwdkh0Q3hZR2lJOUJCUWRSbjZtUWo0am9remlvME1rc3VqNlZxQ2w3Z0RseXJ1L0xSZ2hPNHh3RXIycFg2OEIrN1E1NnlTbnpiRmFCTHFZL3Z3QW16YVVVVnB0UU5saGtkRDVrZ0M2RXNscHdiREtGZ1RpZEN3NmtJNVl1eWxsV1FIMVBwWTNwLytnR25PR200YlFKQUgzUzk5UGVaa3pFREYiLCJtYWMiOiIzYjA3ZTNmMzQ0NThjZTc4NTNkN2Q0NjUyNzdmYmVjYzA2NGFiMDMwMmM3MDFlNTYyM2M1MDFiNDJjMWE5YjYwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlAwbzh1dEpPV0J3UEtrbEg2ODdOSkE9PSIsInZhbHVlIjoiV2ZPMm90ODV2bEUzQXJqMU1QM0RsNzdKSHpRREdyenZJcEJQblFhelhLQm5DejhzYWZQZFNhVDgza0JzRHQwbytOZzJRbzlVMG5ycEkrYlBBak5HalNXREVvamlLWVhaS1VXQnQ1QzZhejgzS1FhK0wvWGMzdWFKWHUyK2lFd3NnOXBhVDlXeXk1QnhWdmVLOVlZcVFkVHloajNpUG9GNWt5R3NKdm1mQWxjUnFocTFGR1FJTFFJM3hIR0lXbXMzRGN1NWtaZVltL1dHRjMxNGNpTnBhOE9hTUZGTVZUNDRqVWdOOHhpdTJid3hQYVRvVk5aZFJtUXk3cnhBdTYxdUFmN05BNnVnMXJRUlFGcDNzQU9XWlppazM5anlLS2VWcEdnNmVnN3NZZHJweGpwUjFGSVRIelVOWTM4T0dQaHhEcXVqdWFUQ0J1aXltcnluUlVLek5FQjB1dHlBNFhlSUpHaFVyRGRLZlN3cTlObWtMMU5DcDZPQzk5a3BteTNYdytwK0lLeWZ3NGVwb0hkQSs3UWJhNm9NZkQ3ZjBpNnhrTG9QWlY0SkNGOEVXWnpyQ0VLVU9qQ243K0hmV0ptZ1NCSCszVXQ2WjlLOWhCaUZabnJVUWV1Nmx3TUxzTXRZd0psdE9NaDBqL2trQ3lDb3RxeTRmMElvVkRQa2QzL0wiLCJtYWMiOiJjYWJhYTk4OWZmOTNiMzE4YWVjMTMwZThiNjc1ZDNiNmIxNjkxOWI4NjVlYzkxNmNmZjI4ZTAyZjMzYTQ1ZmZhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBPN2p4eGpCbTg5WWp4alNoR1hNbnc9PSIsInZhbHVlIjoiVm5DQkJtMCtHcnp6OGVqelZEU1lZeVJISzVrV3FyNlg3VEdSTVlPSTd0M0MwZDFFTm5ma1AwcklPejYyTUdzUVV2MlBsR29lTHhKeEFUR1pjSUh3U0JQRm94MUF2L0VlQ1FPcjNGQWlGOGY0MjJjbW93QWs0ZVB3LzRESVpoa0ZTM3dVNjBUb1RwcWU5aUpXcXQ5VjZxTUV6WFV3THl5T1ZjcCs4SlFFTGVuOW1sY3lUWFJUUVZPYWc2N1ZWc3REeXRjS1V2YWIwQVMyUit0UFpTOCtWOXpiby8xY0tVdVplYzRjZU1KRm5peWZmQUJxa3NLcDhxZ3Z5bndpN01MNndXd1BBTUlKZXRhZjVHVjhLSkZoWVBGc3RDcGZiY3BraHFMNzY5aEs2OVVTU1hMc1Z5aGtrRGcremh1dmUxS1JlRzBsZUVjV25OOEtLNHJaSHRySTNGZjNNY0oyZEgwdkh0Q3hZR2lJOUJCUWRSbjZtUWo0am9remlvME1rc3VqNlZxQ2w3Z0RseXJ1L0xSZ2hPNHh3RXIycFg2OEIrN1E1NnlTbnpiRmFCTHFZL3Z3QW16YVVVVnB0UU5saGtkRDVrZ0M2RXNscHdiREtGZ1RpZEN3NmtJNVl1eWxsV1FIMVBwWTNwLytnR25PR200YlFKQUgzUzk5UGVaa3pFREYiLCJtYWMiOiIzYjA3ZTNmMzQ0NThjZTc4NTNkN2Q0NjUyNzdmYmVjYzA2NGFiMDMwMmM3MDFlNTYyM2M1MDFiNDJjMWE5YjYwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlAwbzh1dEpPV0J3UEtrbEg2ODdOSkE9PSIsInZhbHVlIjoiV2ZPMm90ODV2bEUzQXJqMU1QM0RsNzdKSHpRREdyenZJcEJQblFhelhLQm5DejhzYWZQZFNhVDgza0JzRHQwbytOZzJRbzlVMG5ycEkrYlBBak5HalNXREVvamlLWVhaS1VXQnQ1QzZhejgzS1FhK0wvWGMzdWFKWHUyK2lFd3NnOXBhVDlXeXk1QnhWdmVLOVlZcVFkVHloajNpUG9GNWt5R3NKdm1mQWxjUnFocTFGR1FJTFFJM3hIR0lXbXMzRGN1NWtaZVltL1dHRjMxNGNpTnBhOE9hTUZGTVZUNDRqVWdOOHhpdTJid3hQYVRvVk5aZFJtUXk3cnhBdTYxdUFmN05BNnVnMXJRUlFGcDNzQU9XWlppazM5anlLS2VWcEdnNmVnN3NZZHJweGpwUjFGSVRIelVOWTM4T0dQaHhEcXVqdWFUQ0J1aXltcnluUlVLek5FQjB1dHlBNFhlSUpHaFVyRGRLZlN3cTlObWtMMU5DcDZPQzk5a3BteTNYdytwK0lLeWZ3NGVwb0hkQSs3UWJhNm9NZkQ3ZjBpNnhrTG9QWlY0SkNGOEVXWnpyQ0VLVU9qQ243K0hmV0ptZ1NCSCszVXQ2WjlLOWhCaUZabnJVUWV1Nmx3TUxzTXRZd0psdE9NaDBqL2trQ3lDb3RxeTRmMElvVkRQa2QzL0wiLCJtYWMiOiJjYWJhYTk4OWZmOTNiMzE4YWVjMTMwZThiNjc1ZDNiNmIxNjkxOWI4NjVlYzkxNmNmZjI4ZTAyZjMzYTQ1ZmZhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-998389424\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1374768746 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374768746\", {\"maxDepth\":0})</script>\n"}}