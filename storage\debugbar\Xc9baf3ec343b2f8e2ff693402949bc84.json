{"__meta": {"id": "Xc9baf3ec343b2f8e2ff693402949bc84", "datetime": "2025-06-27 02:27:52", "utime": **********.576449, "method": "GET", "uri": "/pos?warehouse_id=8&ajax=1", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.078517, "end": **********.576465, "duration": 0.49794793128967285, "duration_str": "498ms", "measures": [{"label": "Booting", "start": **********.078517, "relative_start": 0, "end": **********.460125, "relative_end": **********.460125, "duration": 0.3816080093383789, "duration_str": "382ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.460135, "relative_start": 0.38161802291870117, "end": **********.576467, "relative_end": 2.1457672119140625e-06, "duration": 0.1163320541381836, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52814456, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos", "middleware": "web, verified, auth, XSS, revalidate", "as": "pos.index", "controller": "App\\Http\\Controllers\\PosController@index", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=37\" onclick=\"\">app/Http/Controllers/PosController.php:37-109</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.018159999999999996, "accumulated_duration_str": "18.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.49763, "duration": 0.013699999999999999, "duration_str": "13.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 75.441}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.522536, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 75.441, "width_percent": 2.919}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.542111, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 78.359, "width_percent": 1.982}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.543828, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.341, "width_percent": 1.267}, {"sql": "select *, CONCAT(name) AS name from `warehouses` where `created_by` = 15 and `id` = 8", "type": "query", "params": [], "bindings": ["15", "8"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 50}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.547964, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PosController.php:50", "source": "app/Http/Controllers/PosController.php:50", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=50", "ajax": false, "filename": "PosController.php", "line": "50"}, "connection": "kdmkjkqknb", "start_percent": 81.608, "width_percent": 1.652}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'kdmkjkqknb' and table_name = 'customers' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 57}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.550252, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "PosController.php:57", "source": "app/Http/Controllers/PosController.php:57", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=57", "ajax": false, "filename": "PosController.php", "line": "57"}, "connection": "kdmkjkqknb", "start_percent": 83.26, "width_percent": 10.903}, {"sql": "select * from `customers` where `created_by` = 15 and (`warehouse_id` = '8' or `warehouse_id` is null or `warehouse_id` = '')", "type": "query", "params": [], "bindings": ["15", "8", ""], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5537462, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "PosController.php:69", "source": "app/Http/Controllers/PosController.php:69", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=69", "ajax": false, "filename": "PosController.php", "line": "69"}, "connection": "kdmkjkqknb", "start_percent": 94.163, "width_percent": 2.423}, {"sql": "select * from `users` where `warehouse_id` = 8 and `type` = 'delivery'", "type": "query", "params": [], "bindings": ["8", "delivery"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 72}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.5554981, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PosController.php:72", "source": "app/Http/Controllers/PosController.php:72", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=72", "ajax": false, "filename": "PosController.php", "line": "72"}, "connection": "kdmkjkqknb", "start_percent": 96.586, "width_percent": 1.872}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 517}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 74}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.558635, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "PosController.php:517", "source": "app/Http/Controllers/PosController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=517", "ajax": false, "filename": "PosController.php", "line": "517"}, "connection": "kdmkjkqknb", "start_percent": 98.458, "width_percent": 1.542}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "App\\Models\\Pos": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}}, "count": 5, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2059617687 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059617687\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.547113, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-100514851 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-100514851\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.557915, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/pos", "status_code": "<pre class=sf-dump id=sf-dump-1727919822 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1727919822\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-700895201 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>ajax</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-700895201\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-406304257 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-406304257\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-198556457 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklZT2Q0Z1dUcTNaQnlvUEpCNWhVL2c9PSIsInZhbHVlIjoiVnp5NzJaMnQzejJBbzRiZzR2MGlYUHdhdjk5emQ3Z1dta0g2SzQ4WVNuNUhPb0FqR0NmQiszQWdsNHQ0Uk1OQmhZRDY0bXhSeXMrQnhFbUo4a093eEVqdFM3Rm1MTC9uK0h1N2FxU2wrSjZ4cDRiQWd5ay9hNUliR0hUbVhRS2UxdHVkSWJyVFkxL2ZoSVdMY1NUR01uRi9lUVlYdFN0Z3EyTGlKaFowRWtjUjFISTFVdmpidE5qdEthTWEva1hEeTQ5WUdUWXJOZm9HWS9ZeXkzN0srdUgxVkNCUTZiWW5rTEd6VlVIMFcxYnhHNkoyckFid0tRamdYbEtzSW5BalNDNk9LQWlWSzJYSmJraFZJL1RlRUN0Tk5lb2FDa0ZhN3dQeGdUbDJwK2xkb2dBeXJ3N2d3d1ZRdzhlcWh0dnhiOWN2anNPQWdkbGlnM2xJTTRSbHJqSCtwOHFEVWJ2WjFoc1hycDhrTTZWbmFpUUc0aWIrMVJwWFBpNG9PYkNsMmk1K3FSRmY3Ymk0eFVBQ1MvMDRZZHVtRzIwVHhXU213c0MraTlqQWwwQjhib2JSTlpUQm4vcmVLV2NBMk9IaU5yWnRaUHM4Y245N1IwdWExdDVyRzRzbnZ4N2tZL1lvMFRuaFZHN1VzR0grekZXS0E4RDVrREdkV0F6MVJFdWEiLCJtYWMiOiJiY2FkYzExYzgzNzRmMzA3MzYwMTNhZWIxYzIwMGJiNjdjODYxNmUwNTU2OTkyZjM4MTdmMzViZGFhMDlhOGQ4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZISjZYd24zYkdabzJXUkM1MUdvbkE9PSIsInZhbHVlIjoiNnBvbS8raTJzbkxycjhIejlEUjdKZ0JuclJRV2RlMjBqTk5zRFhsclJxaVVDZ0NBQmg5a0c3dEZaYmV1NEhhNmlOQWlqTFc2VCtkMGM3MXJYeFRJQVhVWkFTUStpTTQwbFFpMWw2U0cycVJvcEVRd0t2VTh5N252S21yVTRRdDJMQjRtQWVJQjB3cFpYYkwrRE00V3BCR1QvVHJZalVnRDNXN1ZFclJveVlSQ0JjNHZKNXFFeWFFMFJ1YmRhbkFISUNyRVlsVzRnN2lWbnZVdTJkQUZIdEM4V0hUdU8xYWl2Q0dva1VuVTJFMUJzQy9OeGY5SFRVaGJESWVZZGcwYWZXN2lxc2pZemoyY0VTd1lzSHBiTkhQT2RLZXlTR05CMVY1aXZWRUM0YUhkb2ZFMXZ3aFRmWExveXhzeVFJZUNOUS9OaFR2RFZOdVAvOTVVUGRrQzVydS9tY0pEZHltRHFLSTVEOGdiQUJRRVh6R0RsUVdsaHp2RTdOOURBbHlMdUMzWFhZTWp6L1NGRVdBL2ZNV0JtNVVITDBFZXdkb2gzMTBPOVFQMUtINE5reXBKMGVhQWJMMWl0RVZBMENzd0tBUzhEK1J2Rk0wMTFhOWV3NGtUN2JQK0RvREMweHFuZ3JNbElZcXdaazRSUmllanZsWXAvOFNzOU5zK1RrUFAiLCJtYWMiOiJjZTVlNmI1ZjAwOTEyNTIyZjU1YjBiNjE3ODlhZjg1NzJlM2U5YTIyYWQyN2M0Njg5Njk0NGI1NmRhYzIyYmZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198556457\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-841191875 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-841191875\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2126691610 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImQwSWVCT0lRMXdWSTNCT2ptdzNOSkE9PSIsInZhbHVlIjoibU9mYjdKZEpLTTJqd1I4RmZqQVg0RFZETkpZZmtpcUZIMHJqanliR1JjUHU0cWlnU3BsRjNmQ3V0NGFBRUM3VVhnUHp3NXI1QnlaVGVGY3JPbXMwcTJGSm96dzJ2V0FPb0F5UkNIa3R0RUs4Y1lWTHUxeURRKy9nNW1ZUHJzU21XaXlDNG82WXJpZHA4UUhQaWtodDBybWZYVXdxZTVhK2tpREJTYWhQN1BoamV5SXllMlk1dUtuQkttdjVLWFdXTTNkWms1S2orRWJCcGIyZXcxSmJJT21ZZHNmU0QrRmtRRnFsVklVYUJockZVeFkyMFVadTN2dWFrK3BjR1cyVDFlWFhPd2M5V0JISG1ndzNrcXIwd09FMWxYcTd3cmFRd0RyeUhvckRFZjNWZmhPcURUblFrZ1hDWURwVjNRVHY0Zm5yTDl5akNaSHU2RERkLzYxSkxoSGlHRjNIMWlhenorYk9wTkE3Tnh2ZXRiZUJhdkwxdm9sbUFERE1nVHBkQkx3dGJIa3N0MlpTWWRkZVZZNVpkMFdSNXh2RzZPbVRCL3VGcms0bXJveFVlN1ZiYmlJOWtBK3hBUWp1NG1mS3hnK0VJSDdhdytrcE45bWdlVlZwNHN6dnJpbVA5UXcyRVVGRUQ2TXVjNmFscWxVZFN1ZC9ybVRxaSs5ekZwSisiLCJtYWMiOiI0ODM2ZmYxNzJmOTAxZTYzM2NkMWE1NDg2ODdmNjdmMDBjOWZkOWVmNGZjZGIwZTU1MzE0MDY3M2NlNDQwMTA2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNZNEFNTDJ1VGZPanN5bnZXL3NZS3c9PSIsInZhbHVlIjoiWU9yYWhEdE9PbjRCaGRkVjIzSEE3ZERpYlFzQkhiTERZTXR1dUd2c0J6cURyeWRudXN5RFJHd1Z6WEdWNlVPMFNjb3BXbUJnSkF6NERqcU4zRzRxSGJyZzJqYm8yOUlHUE9WSm5sWWYvVmlNeml3NWlWb2JDUVU3dHd5RXV1RTJPSzdia0JmZit6NVROV1NoY2pmbXVleThxdzlyb3pLN3FmdEJQbGNMb0NJUVN0dkNhWURGZ2twSTdCTDAyUG4yRG5OWFRxVExxa1Q1WGttdDVEdkhHQVNFQ0ZGMXVSTkFHY0xwSWZQR2RnU09MRmF5WndZcGt5cVkyODhPcXRzc2d5VWdoWHpQZG9IeG0rZlA5ZUFkdkRhNitIUWVQdm1MSmVBWmt6UEFNMWdFbGZGV01FajZvT1Q0bm5zcHpqa3BweWhscnJmK1NFRG5Rb0tjZEQxeEg1VVIwY1Z2ZkpicWpLUGJtdFlsUk8yYXJvTzNHVzQ4SHR6c1luMlE0OG9CWFd1YWxPZ3c4elh6TytaWkNiNTF1VExlQSs0TUlkdjdSVHBZb0VJSUZwNWN3bFVleWxUaEcvVzBlaFl5R3ZjQzcyUXNDckdvY0lmays5WWFEaHZqeVhNK3RmOXVkeGtiZitTMUp1MkMwSWF2MGI4Q2xQbStybjFvcnFxdTlyd3ciLCJtYWMiOiJjMGY3NzZlOTIxNzRkNjMzZWU5ZTk5ZGVhNDY4YmE3ZTIyZGY1MGEwYWY2NzYwNDFjNWViNjkzMzA3MDBhZjNmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImQwSWVCT0lRMXdWSTNCT2ptdzNOSkE9PSIsInZhbHVlIjoibU9mYjdKZEpLTTJqd1I4RmZqQVg0RFZETkpZZmtpcUZIMHJqanliR1JjUHU0cWlnU3BsRjNmQ3V0NGFBRUM3VVhnUHp3NXI1QnlaVGVGY3JPbXMwcTJGSm96dzJ2V0FPb0F5UkNIa3R0RUs4Y1lWTHUxeURRKy9nNW1ZUHJzU21XaXlDNG82WXJpZHA4UUhQaWtodDBybWZYVXdxZTVhK2tpREJTYWhQN1BoamV5SXllMlk1dUtuQkttdjVLWFdXTTNkWms1S2orRWJCcGIyZXcxSmJJT21ZZHNmU0QrRmtRRnFsVklVYUJockZVeFkyMFVadTN2dWFrK3BjR1cyVDFlWFhPd2M5V0JISG1ndzNrcXIwd09FMWxYcTd3cmFRd0RyeUhvckRFZjNWZmhPcURUblFrZ1hDWURwVjNRVHY0Zm5yTDl5akNaSHU2RERkLzYxSkxoSGlHRjNIMWlhenorYk9wTkE3Tnh2ZXRiZUJhdkwxdm9sbUFERE1nVHBkQkx3dGJIa3N0MlpTWWRkZVZZNVpkMFdSNXh2RzZPbVRCL3VGcms0bXJveFVlN1ZiYmlJOWtBK3hBUWp1NG1mS3hnK0VJSDdhdytrcE45bWdlVlZwNHN6dnJpbVA5UXcyRVVGRUQ2TXVjNmFscWxVZFN1ZC9ybVRxaSs5ekZwSisiLCJtYWMiOiI0ODM2ZmYxNzJmOTAxZTYzM2NkMWE1NDg2ODdmNjdmMDBjOWZkOWVmNGZjZGIwZTU1MzE0MDY3M2NlNDQwMTA2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNZNEFNTDJ1VGZPanN5bnZXL3NZS3c9PSIsInZhbHVlIjoiWU9yYWhEdE9PbjRCaGRkVjIzSEE3ZERpYlFzQkhiTERZTXR1dUd2c0J6cURyeWRudXN5RFJHd1Z6WEdWNlVPMFNjb3BXbUJnSkF6NERqcU4zRzRxSGJyZzJqYm8yOUlHUE9WSm5sWWYvVmlNeml3NWlWb2JDUVU3dHd5RXV1RTJPSzdia0JmZit6NVROV1NoY2pmbXVleThxdzlyb3pLN3FmdEJQbGNMb0NJUVN0dkNhWURGZ2twSTdCTDAyUG4yRG5OWFRxVExxa1Q1WGttdDVEdkhHQVNFQ0ZGMXVSTkFHY0xwSWZQR2RnU09MRmF5WndZcGt5cVkyODhPcXRzc2d5VWdoWHpQZG9IeG0rZlA5ZUFkdkRhNitIUWVQdm1MSmVBWmt6UEFNMWdFbGZGV01FajZvT1Q0bm5zcHpqa3BweWhscnJmK1NFRG5Rb0tjZEQxeEg1VVIwY1Z2ZkpicWpLUGJtdFlsUk8yYXJvTzNHVzQ4SHR6c1luMlE0OG9CWFd1YWxPZ3c4elh6TytaWkNiNTF1VExlQSs0TUlkdjdSVHBZb0VJSUZwNWN3bFVleWxUaEcvVzBlaFl5R3ZjQzcyUXNDckdvY0lmays5WWFEaHZqeVhNK3RmOXVkeGtiZitTMUp1MkMwSWF2MGI4Q2xQbStybjFvcnFxdTlyd3ciLCJtYWMiOiJjMGY3NzZlOTIxNzRkNjMzZWU5ZTk5ZGVhNDY4YmE3ZTIyZGY1MGEwYWY2NzYwNDFjNWViNjkzMzA3MDBhZjNmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126691610\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-572460523 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572460523\", {\"maxDepth\":0})</script>\n"}}