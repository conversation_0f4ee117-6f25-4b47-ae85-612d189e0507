{"__meta": {"id": "X1657c62e3807d97d367f08dbc3cee8d7", "datetime": "2025-06-27 00:43:10", "utime": **********.38625, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750984989.968497, "end": **********.386264, "duration": 0.4177670478820801, "duration_str": "418ms", "measures": [{"label": "Booting", "start": 1750984989.968497, "relative_start": 0, "end": **********.32936, "relative_end": **********.32936, "duration": 0.36086297035217285, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.329368, "relative_start": 0.3608710765838623, "end": **********.386265, "relative_end": 9.5367431640625e-07, "duration": 0.05689692497253418, "duration_str": "56.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041768, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0029899999999999996, "accumulated_duration_str": "2.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.363131, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.906}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.3736448, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.906, "width_percent": 14.047}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.379263, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.953, "width_percent": 14.047}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-222863572 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-222863572\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-919481353 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-919481353\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1345613015 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345613015\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750984954853%7C63%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InBUNXcwYnpTZFh6SExSYzJSbjFiNGc9PSIsInZhbHVlIjoiSHhEY1p4NXZXYVpTV2s1YUNIU3hoQUozck92bzlyOTd6dzZDUXk5MTVwR2d1NzlYSU1Rc0d0cWhhVGg0Tno0dTlic0t6QXdqRmRWYUdzSm4wRFdzbGRsbkpid1d1WFhEOElJTlF1RmhvR202b0RGTHNpeGp0NE1lQkNFaW5jNXF3cDBUTnd1RG13WURQcHZYLzhyYmdTUGVaRWpBWGo5b2dDaEQ3S09pZXlsS0p4K3JiRnkxU3c2am05cmFvRUg2QUF4bk9BWHp1SGVZcTZlY09mTDBjUDBabGVEVGRGc0VXVURLRmkvaldxak4vMW5nbENya3JtTy9XU3llbHVnL3lLbGluc1J0YmJjbk5GVUt0ZFc4OUtIV214bmdZWU54bUJQMW9SUExPSHIzQXdteW1DWFpPR2F4N3p3Lzdjam5jdDA2K2hGLzlSYXBFNXorZzl2NHovUUdtRStTRkp3cHhIcTR4anREY2ZvZHFRV0trQ0Y3eTMvNDU2aWJCalhYNXN5Z1VzRnM3cDBncSt1TmxIT0E0ZmovYllrRUZwbWR1R3p4a3VGRzdVZnppM0J0eHhFc2Vycmgrcld2eDhMLzlkNjhQNGxEM3M5bStyUzNJMk90TzR5OHAvamk1VXZkOU5zVkVtTXdiQk91SXR5dU53b0IwWHVZdWtMbDBBRDYiLCJtYWMiOiIwYzc5YmYyNmU1NmY0MWQ2ZjBjNjU5Y2ZmMjIxODc3MzBiNjhkZTFjOGVjNjE2NjM2NDFjNjg4Mjc2M2QwZjgwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im9UK1N1dnhKNzBZNVlVTStEMW4rQ1E9PSIsInZhbHVlIjoiM0FkYXZ5SVZJYlc2Sy9SM0xUcVJ4WXA1ZW1vQlhwdXBldjVqR3k2T0pDWGdEMjlNRytHakh6SHk3b3NqSGI1RElJRk0xQkk2MDdkMEhacnhuNlJkQUR5SmFUVkVaQnRQdHlUMEpIVTdmVUttbm5la0Nyb1hjbDc5NklTVlpsenJVSWswMlo2Ulc4bEZNYmRNYnpQQSt3QlVMT1F2OWVVMjA4RkpDdUhNYkF3MmxDRDVnWmc4TUllTCtkcngyZHkrdVZhQXZTdUJCeUZKN2J0ZUY4SFV2WlBIakFaWGN4UE8xczI2aXl3SzRmRFdXWnBaM3JCT01OUUxIZWIvV3l0Q3BxWE96ZXdmdDE1QVQ0eTI5TWxuakdhYThRZk9xbk9tYjRQWXR4L0NOVG4rdmY0UVprOUdKbEJIejBLVDJOa1NxQUJ3YUtPeUlEVUJocjUrQXRQN2srSkgxUEJZbHZFbjNRQndnSmZjNEFUYjlHTzdTT3BZM3dRNWlhcW1ET0VVK1BOd1JkTmFIMU5iSXdpSUJCMmxxTzhsenNUWVRGa2ZmcDB5M0FMeVNXaGRCRzBKRFpIMUE5QkVNMWJVY1V1b3pnQTB6emxrUXI4cW9maVJqeEhGZ3pDYU5rZzZyTllqNCtFZnhFaDVsbDNwOXY0c2VONUN2QlN3OHV0dGQ2aFoiLCJtYWMiOiIwMGQ4YTdjY2E3YzZlNDBlMmM0NDdkZDlkM2EyMWQ0N2U2YzJmYjY3MGI5YjlkYjA3ZGM0OGM3N2EyOGJjNjE1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-62565885 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62565885\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-228832606 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:43:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ijlrc001R2VIUWhFRjdIRVFhUFZNQlE9PSIsInZhbHVlIjoiUjI3QlRYUlV6ZElub1p1MlB2MzZVSS8rTG5jeE0yb3hnUjF5ZndEQW5aNUIwRzgreXBTcXJieVIvbzY1ZFlCcStHaXk1b3E3SkpsSjVFTDNpNTFIWFhxZmtreHhac1lrVXo2WUJiaHF1d25wUUJ2aTdxVUZsUmxSelZuZ3IvVkQ3empWbCtYTXFXaklWOXJuSVZ3SnRQeGk0cUVOZ0ZXU3dGR0JnbUpNYWxlZ0JjL2pKRWpCT2lyRG4zUnFheWZNcDg3dXJJdDEvc2V0UHBHOW5TejMxcFc4eGVpZDVvMUZIS1M1Y3RtdjI4MEhUT3pHUWcydFlaOWhBdFczelRDNmszS05CNkFuMHBvQzlLNjJtUE9YNGNUNVo5blFDZG5vR0hHU0k1SmRsTWhISTAyb01mVDB2RzlEeUVpdnczN0hVWmhTbWQvcmJUOGVxVEF6TXNkeWVrOW0vUjJoRDJqM2NFL0JlTU1ETk54aGJKUXBRWEpnNERTQm1TWUtGaGVmRGZFbjVpQmpaSnFsbmtIYWtHWVZmakl1TndmdkxVb3M0Y2lacUxsV1Q1elkwUXdOQlJXV01GcmsrV3BUclNUS21qdWxYSUdKem1SL0dqVXdiM1l2aVk2MW9uMGRvbFQ5TWl1WFQzeDBPMFRpQW9HZTBXMm9qL3ZIbTZvK0FiVUkiLCJtYWMiOiI2NDM4OTY5OTU1NzU5ZGFjYzY5M2EzODgwYjYyYzY0OGQ1OWIyZmY1MTI2YjE4Zjg1YzVmNDI5NTlkNDg0YmM1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:43:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InowM3g3N0RSN2RZT2o2dE9HdGZNdUE9PSIsInZhbHVlIjoiRTlYa3doMTJNSkpackwzaW1rcEZJWW1QQkJkamdPNFFDTHdtQWJLYURLKzZaQkN6YWovY0NlTGFvUU5mRjhyNFNtK2tLRzJJdjgvSUxtVjh4KzBkZXhqRjhOVGF6YnJXREZSdHh0bngrc3RhTHZLblNsYytINGtvNWQwWWpYT243aUUzeG1FTEY3QlZCb2QwWEd1c1BpV0FadjdaVjJwV0tSS2N5NzhrWUxpSFU3bVdCaTVqYXNKYWwvZHpZZ2pYRFdOdzZSbWdLZWc3dC9QYTBIU0MrSENOWmdrSXRGcllWRmtOTkpoOHJJczFpek9xa3dtWGRBT2FiSTFSYWsvQjJqUGN1Z3MydzhqSFcxM1oweTdyakV6WTE4OGhkZTN4aEFnYzhGWEZzYnFSRUZpdzZjam9jMWJNblE4RkZxeGJVcEMzTis5Lzh3YzR3NmJQRXZlSDdiQTI3OU9uamVUeFV6L0JLU2h4dVAzcmp4U1YvaFdQazM4bWNzQ2lya1AySGhNbHVmdmdnQzJCUmFjb1dQRGZxMnFLMmFBYU5WRytCYzJ6ZFBESTRFOHlVUlRkSm1ZWnE2NG11d2JpTHdJTzVSRS9PcE9HRkVVNmFBQzhzaXFhWUxVZlVVYWp2SEZMTktmZmFTeDdLSFBES05BOWF4L3hYRVZuM01MNlNWZEsiLCJtYWMiOiI4ODdkOWMzNTQ4NzZkZTQ3Y2QzM2RhNTU3ZTdiYzZlYWUyNDZkNWNjODUxMTY2MTVlYjBiNWE5NWRlYWNjMzJmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:43:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ijlrc001R2VIUWhFRjdIRVFhUFZNQlE9PSIsInZhbHVlIjoiUjI3QlRYUlV6ZElub1p1MlB2MzZVSS8rTG5jeE0yb3hnUjF5ZndEQW5aNUIwRzgreXBTcXJieVIvbzY1ZFlCcStHaXk1b3E3SkpsSjVFTDNpNTFIWFhxZmtreHhac1lrVXo2WUJiaHF1d25wUUJ2aTdxVUZsUmxSelZuZ3IvVkQ3empWbCtYTXFXaklWOXJuSVZ3SnRQeGk0cUVOZ0ZXU3dGR0JnbUpNYWxlZ0JjL2pKRWpCT2lyRG4zUnFheWZNcDg3dXJJdDEvc2V0UHBHOW5TejMxcFc4eGVpZDVvMUZIS1M1Y3RtdjI4MEhUT3pHUWcydFlaOWhBdFczelRDNmszS05CNkFuMHBvQzlLNjJtUE9YNGNUNVo5blFDZG5vR0hHU0k1SmRsTWhISTAyb01mVDB2RzlEeUVpdnczN0hVWmhTbWQvcmJUOGVxVEF6TXNkeWVrOW0vUjJoRDJqM2NFL0JlTU1ETk54aGJKUXBRWEpnNERTQm1TWUtGaGVmRGZFbjVpQmpaSnFsbmtIYWtHWVZmakl1TndmdkxVb3M0Y2lacUxsV1Q1elkwUXdOQlJXV01GcmsrV3BUclNUS21qdWxYSUdKem1SL0dqVXdiM1l2aVk2MW9uMGRvbFQ5TWl1WFQzeDBPMFRpQW9HZTBXMm9qL3ZIbTZvK0FiVUkiLCJtYWMiOiI2NDM4OTY5OTU1NzU5ZGFjYzY5M2EzODgwYjYyYzY0OGQ1OWIyZmY1MTI2YjE4Zjg1YzVmNDI5NTlkNDg0YmM1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:43:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InowM3g3N0RSN2RZT2o2dE9HdGZNdUE9PSIsInZhbHVlIjoiRTlYa3doMTJNSkpackwzaW1rcEZJWW1QQkJkamdPNFFDTHdtQWJLYURLKzZaQkN6YWovY0NlTGFvUU5mRjhyNFNtK2tLRzJJdjgvSUxtVjh4KzBkZXhqRjhOVGF6YnJXREZSdHh0bngrc3RhTHZLblNsYytINGtvNWQwWWpYT243aUUzeG1FTEY3QlZCb2QwWEd1c1BpV0FadjdaVjJwV0tSS2N5NzhrWUxpSFU3bVdCaTVqYXNKYWwvZHpZZ2pYRFdOdzZSbWdLZWc3dC9QYTBIU0MrSENOWmdrSXRGcllWRmtOTkpoOHJJczFpek9xa3dtWGRBT2FiSTFSYWsvQjJqUGN1Z3MydzhqSFcxM1oweTdyakV6WTE4OGhkZTN4aEFnYzhGWEZzYnFSRUZpdzZjam9jMWJNblE4RkZxeGJVcEMzTis5Lzh3YzR3NmJQRXZlSDdiQTI3OU9uamVUeFV6L0JLU2h4dVAzcmp4U1YvaFdQazM4bWNzQ2lya1AySGhNbHVmdmdnQzJCUmFjb1dQRGZxMnFLMmFBYU5WRytCYzJ6ZFBESTRFOHlVUlRkSm1ZWnE2NG11d2JpTHdJTzVSRS9PcE9HRkVVNmFBQzhzaXFhWUxVZlVVYWp2SEZMTktmZmFTeDdLSFBES05BOWF4L3hYRVZuM01MNlNWZEsiLCJtYWMiOiI4ODdkOWMzNTQ4NzZkZTQ3Y2QzM2RhNTU3ZTdiYzZlYWUyNDZkNWNjODUxMTY2MTVlYjBiNWE5NWRlYWNjMzJmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:43:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-228832606\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-593061146 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-593061146\", {\"maxDepth\":0})</script>\n"}}