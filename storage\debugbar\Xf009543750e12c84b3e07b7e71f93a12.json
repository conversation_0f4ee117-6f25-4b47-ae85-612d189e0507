{"__meta": {"id": "Xf009543750e12c84b3e07b7e71f93a12", "datetime": "2025-06-27 01:15:03", "utime": **********.355114, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750986902.936365, "end": **********.355128, "duration": 0.4187631607055664, "duration_str": "419ms", "measures": [{"label": "Booting", "start": 1750986902.936365, "relative_start": 0, "end": **********.285995, "relative_end": **********.285995, "duration": 0.34963011741638184, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.286004, "relative_start": 0.3496391773223877, "end": **********.35513, "relative_end": 1.9073486328125e-06, "duration": 0.06912589073181152, "duration_str": "69.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043928, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020860000000000004, "accumulated_duration_str": "20.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.311353, "duration": 0.019870000000000002, "duration_str": "19.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.254}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.341584, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.254, "width_percent": 3.068}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.348092, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.322, "width_percent": 1.678}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1013796051 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1013796051\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1725833622 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1725833622\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-937332063 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-937332063\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-486736233 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986887615%7C89%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJ2RzRpeEpPL1h2K0RKUUFkei9VT2c9PSIsInZhbHVlIjoiVGtUeHk3TkMvei9XR0t2M2huV3VSckR3TGhNb1J2VldEelpYdUlFNTR6Q1dyZzFWREJQRVhnd2czNVIraEdDMVlDNUlFRC9pUUpHZjBYNk0vOVU5ZmpuZ3p0eGVNaWRDZEZwckY5SjdHMFJ4dXc5cVh2STRhWC8rRm93SnJmNFcrMEYzUXo3RDZRcUF4ZUpadmc5dS92REh2b1ZzY2V2Zm5HZWd6UHJPMUVjeEZpTjZKaE9uMGVuRzFkUlpZMTMveWpvaFhGZy8wWitPc1BKS3Y4aXR2Nm55Lzg5Y21ZcWo3b1hZSThHUXdVTFZBN2cxV2d5N2lVc3BvbWFBbGJZR3hWNHB2OEYvYTFIbzgyTXdSOHJ6UWJlU01EOEh3WjhUS3NuWENmMTZzdjR3ekMvOWlmeGUvNG9YWWxkMUFYa1RXeGlNek8wYzNIaW4yS3dxOWRmYzRkb21mUHdkRFUrLzlqSUlOdXB0bDRJMFNPdDNIbTdQeDA4enNmcVZoRGJ3T1N5OUxHS2RnSGhpU0xYSFU1TVFpSFpsUjlDa1p3bktEaW43d2Znd3FacHEzdUhDK3JzeXpFRTVONjZyZm1CREF0bkRlcWNnWVZvazJBcDY5UitnL0FUVis4TGhlZGhpa2VBdFdRaklUTDNzUHdseXRBWWtCbTJnVmQ5dUgyUkEiLCJtYWMiOiI5MmFiNTk2YzkwNjBmYTFjMTg2ZDdjZGFiMTQ1MTk0ZmEzOTc1YWM0M2U5YWI3MWMxNDIyMDU3NTMyMmRhMGNkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhId25HOGZTZ3ZkVlRMWmpvQjYrNWc9PSIsInZhbHVlIjoiTFpDNEtEbUhBTEp2M09vMmUyb3RxQi9TNWd4VEdSUkRoN1ByUzF2L3lKM3FONktCNk1HZ2Zoa1l1MEk0OXBYczNQUmZsd0NTVlNuRGljaC9iSHhyankrVFdHVGprdDA0RkNsdDlPeENaa0xZUWduREZMdDhjNW5LT2IvdXNDWDZkQVBXazJqZSt5Qk50UjMwN3pvaCt1clg2Mkgzb2ZsODBadmFtNXMzR3pDd3BKelF3ZEJraWppNENZWHVKS1JVbGJ6MC9oU0xFTnVWZXNIekFGeUdLaXV0SXM2akpVWDF2aEg5WU5nelpEb2pwbGp6NFJYb0xoMWQ1ZDA2c1o3OHhlM0ZmVU0vWFU4UWoxOVN5QXI4bHBzMTl0R0FtT1FWeWVlMWk5ME8wYk1JSHFiRmZHMzRiY2JsVTF2NDdOTmV4SWJFaVdyclFkOTR4Q2s0SEIvQkVjTUY3NG5nUXJrT2drWTRmTFE5NmZKemF2d2JkKy9RdjZtQm1BWUVhL3lIVmxvR1RacmVHR3hIdTI3SDVNVEs2dzF5d0RLdXFVVy94NmNha2NLdm1zZ04vcSsya00welltTjNBdzZZbVhPai82cExHTi9qYjhrYU44MXhrNkRXUnBCVGpIWmxtcHJLWitDcmEvVW1EeDhna0dDcGtjbFR1emJTcU9QV0x0ZUUiLCJtYWMiOiI5YTY2MDc3ZGMxZjc3YzU0NDAxYjBlYmIzZmQ4MThlNThiYWNjMDFlMzExOWZhNDMxZTc1MjZiNDQ5ZTdhZjU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-486736233\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1144177727 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144177727\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2064987867 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:15:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1SNDlCUE1JQ053aHJKTjd0eTRDMVE9PSIsInZhbHVlIjoiWWZBU1p3RXNxckRvMDBOQjBmMGljaW5pNFowbkZpL1N0YWgweW1wOXcweHpIOGpaYWw0dFhnTk1vUW14R2VlZmdDWk02ajFJb2NpRlJRYncxRDM2OUhPMVFFQkJOS3JLcmVVSlFlbjlSUnBIeGxxWGk2Qm1xNlArOWJGK0lHWnk1STlMMXgwbU1mRUVoM1RyN2pHaFlXUXdQMlZsdVIyc3ZLdVBlczJnUlU5bTkyVG9kSzFtUkN5MnkyczgrZWhMZEJPd2pjOUN2Rk92R0NlVk9XR0hCVnV3VEdPS28xL2gyR000UGpMazFycDVGQ2VNVFNVd01xdU9BQm4vdjhWb2hzeXIwM3Z4T0VRT1gvQVNhbzRaekpESzYyeHI2NkxTcXV6eXViSE03QlR3WmFmd0cyVzNGbjJUaHZNcGtpYU9GN3lOK0RaVE5LeldHVVE1aWw2N0VWVlBnS1M0ZUdTdlM4eWFCeTIwZTVKZENZTzlEb00xRHpBNDI0WlpmZzlyc0JsT3FVSEc0RHIwWVVkcXZFNGpVeGY2RDREcFZMRW9zdkdqck1DMkhJVUJhVGhkaXExYlFCK2g5Z1dFb2NDRmR5cHhRUkx4SkZqcUFsQjVoaU8wcEl0U0pTQ1dROHJlYVE2eEw0UFNzNGlyaSt5K2NEQjkvSWJCa0NmdUtiZW4iLCJtYWMiOiJjOGYzNjAwNjhjZDQyZTVmOTZkYWUwZDhkZDFkNDFmOTg4OTI2MTc3NzMwZjc3ZDg3OWRjZjAzMzFlMTg4ZmIzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InVTT3ExZE1zdE93b1hjbkN1UnBYUXc9PSIsInZhbHVlIjoiQkpWSGd4UnpHSlN6SUE1OS92bEl3VVFxb1RnSVVRWHo4d0dvL3AwNXg1YTlKWDlFYldDYi82blBIT0hhR1hWbCsrTnUxNXBYdjJtNk1yY3NYOEh2SUFDcWJHeCtiSHdBRU9VTGRoNnF3Uk85RStQU3lYM05xZ3Q3V2NJVFR2U2NLa2ladS9Jd1pLNjBKcDAxSCtNcU9kTDV5aEJYOEZIZ0t1c0dGSHNLdjRZekY4aXB1VjloUkdBZkphQ0dmSjdFZ3RIT2kxVU9Tbi83NkdpSi9kU0p6NGxWYXowQlExdExjQ25zWkVGUmJneFZyODVCU3BTQnpTWC91YnJRZFBsTHUxejk0N0w3ZStXZkE3aWkxUWJNcnNwalVaTmwyT2FvQjFiTVgzS1BEOXFpN2E3ckp2bWJjTTlLZzJRbjZXT2V2UkZZa3ZKbVlFSlg4WHdYTUpkNy9rSmxXZzN3OHRlVHhjT0pzV2VhbU5iaGRxREpVWmdDNk0vaVFsei93WFZ4YUIxU0tRZHRWSDRHdUhLaXN2UzVNVzVYVkNzelByY29NWStYaU0vL0lwNk9iS0IrdXNsNU14K2VGUUhBYzk1TFNpd2thMk1YRzNGS0IrM3V4V0lQcXJwZFNLZUpGanpJSnNsS3JhWTdJMzFtSThyMFJxT2E0TWJGNStlb1NWT3AiLCJtYWMiOiI1ZTMzODUyYzBhNzBjNWVkZWEwOGIxNDc5NGUyYjBlZTc5N2Q1NDY5ODg0MTQ0YWNhZTI2ODA1Y2NiODYyMmY5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1SNDlCUE1JQ053aHJKTjd0eTRDMVE9PSIsInZhbHVlIjoiWWZBU1p3RXNxckRvMDBOQjBmMGljaW5pNFowbkZpL1N0YWgweW1wOXcweHpIOGpaYWw0dFhnTk1vUW14R2VlZmdDWk02ajFJb2NpRlJRYncxRDM2OUhPMVFFQkJOS3JLcmVVSlFlbjlSUnBIeGxxWGk2Qm1xNlArOWJGK0lHWnk1STlMMXgwbU1mRUVoM1RyN2pHaFlXUXdQMlZsdVIyc3ZLdVBlczJnUlU5bTkyVG9kSzFtUkN5MnkyczgrZWhMZEJPd2pjOUN2Rk92R0NlVk9XR0hCVnV3VEdPS28xL2gyR000UGpMazFycDVGQ2VNVFNVd01xdU9BQm4vdjhWb2hzeXIwM3Z4T0VRT1gvQVNhbzRaekpESzYyeHI2NkxTcXV6eXViSE03QlR3WmFmd0cyVzNGbjJUaHZNcGtpYU9GN3lOK0RaVE5LeldHVVE1aWw2N0VWVlBnS1M0ZUdTdlM4eWFCeTIwZTVKZENZTzlEb00xRHpBNDI0WlpmZzlyc0JsT3FVSEc0RHIwWVVkcXZFNGpVeGY2RDREcFZMRW9zdkdqck1DMkhJVUJhVGhkaXExYlFCK2g5Z1dFb2NDRmR5cHhRUkx4SkZqcUFsQjVoaU8wcEl0U0pTQ1dROHJlYVE2eEw0UFNzNGlyaSt5K2NEQjkvSWJCa0NmdUtiZW4iLCJtYWMiOiJjOGYzNjAwNjhjZDQyZTVmOTZkYWUwZDhkZDFkNDFmOTg4OTI2MTc3NzMwZjc3ZDg3OWRjZjAzMzFlMTg4ZmIzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InVTT3ExZE1zdE93b1hjbkN1UnBYUXc9PSIsInZhbHVlIjoiQkpWSGd4UnpHSlN6SUE1OS92bEl3VVFxb1RnSVVRWHo4d0dvL3AwNXg1YTlKWDlFYldDYi82blBIT0hhR1hWbCsrTnUxNXBYdjJtNk1yY3NYOEh2SUFDcWJHeCtiSHdBRU9VTGRoNnF3Uk85RStQU3lYM05xZ3Q3V2NJVFR2U2NLa2ladS9Jd1pLNjBKcDAxSCtNcU9kTDV5aEJYOEZIZ0t1c0dGSHNLdjRZekY4aXB1VjloUkdBZkphQ0dmSjdFZ3RIT2kxVU9Tbi83NkdpSi9kU0p6NGxWYXowQlExdExjQ25zWkVGUmJneFZyODVCU3BTQnpTWC91YnJRZFBsTHUxejk0N0w3ZStXZkE3aWkxUWJNcnNwalVaTmwyT2FvQjFiTVgzS1BEOXFpN2E3ckp2bWJjTTlLZzJRbjZXT2V2UkZZa3ZKbVlFSlg4WHdYTUpkNy9rSmxXZzN3OHRlVHhjT0pzV2VhbU5iaGRxREpVWmdDNk0vaVFsei93WFZ4YUIxU0tRZHRWSDRHdUhLaXN2UzVNVzVYVkNzelByY29NWStYaU0vL0lwNk9iS0IrdXNsNU14K2VGUUhBYzk1TFNpd2thMk1YRzNGS0IrM3V4V0lQcXJwZFNLZUpGanpJSnNsS3JhWTdJMzFtSThyMFJxT2E0TWJGNStlb1NWT3AiLCJtYWMiOiI1ZTMzODUyYzBhNzBjNWVkZWEwOGIxNDc5NGUyYjBlZTc5N2Q1NDY5ODg0MTQ0YWNhZTI2ODA1Y2NiODYyMmY5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2064987867\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2136158344 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2136158344\", {\"maxDepth\":0})</script>\n"}}