{"__meta": {"id": "Xf43791bcc069426414c5a03ec5b6cc63", "datetime": "2025-06-27 01:12:53", "utime": **********.173482, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750986772.694366, "end": **********.173497, "duration": 0.47913098335266113, "duration_str": "479ms", "measures": [{"label": "Booting", "start": 1750986772.694366, "relative_start": 0, "end": **********.11468, "relative_end": **********.11468, "duration": 0.42031407356262207, "duration_str": "420ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.114693, "relative_start": 0.42032694816589355, "end": **********.173498, "relative_end": 9.5367431640625e-07, "duration": 0.058804988861083984, "duration_str": "58.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041768, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0032600000000000003, "accumulated_duration_str": "3.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.14868, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.417}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.159349, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.417, "width_percent": 15.951}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.165566, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.368, "width_percent": 19.632}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1903254602 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1903254602\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-246927282 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-246927282\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-959875433 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959875433\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-892784861 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986360696%7C84%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImwzOTlMbFhjN0MwempXWFFuWTdObHc9PSIsInZhbHVlIjoiRFhVeE00d1lKTFNFK2NGOGd2dzI3a3hHUEorOUM3YjlIY3BqR0NQSWFuRmQrN1NQNzJGaHRidzA1UVRTb2FCU3NTUk1WaWU1NEJaU2gxL1l3NGo0ZldXK3M5OWd1YUpFamExUHZNNnMwQUdDUGVXL2hNODlhT3BiYmRaUTlDa2Uya2dGb2FZME96OFFhMmRxRk5yUXlzUUpKWXFubDVjOExRSHZZUUhIbjNrVDlOYkxDZXE3NHE2MVljU2pENkU1MTN4cHlvTElyT2ZQQ1RDTlNmdGpob253dWZJYWlqblBYaHJmTTBvS2N5ZXp6a0pCcngzTE9raFNuWjVrQU5zODhkMDhjQUtIVnp5V24wY210SFJid3FNcHh3R1l1Yzc5MU41M09ETFQ4dlVGQ3U5dXBxUVhMVGtteFNTaE5QUGlDRzhoWURGUWRaUVNLb2lvRFd1OGw1QlBvbWlSSDhKV2FQb0JxOGZxL091aGpQTVFJdTU2bjF1UWl1MWtMUGw5TzVWWGdyNG9Xeng2VGxpeXNEVEN0c3h2SEZBM3BKRkVjQ0dRa1cxeEpzUUtxQUxUVFZwLy9lbFBJTXFNclhRKzN1b2kwYmUxOENycnJjc2xUMzBqOUFCeFJiZHB2Qy93dm5VU3MvZGVEdlFtMVNLR0RIUklUd01IMFp4R3ZqN2MiLCJtYWMiOiJkMzIxYzg3OTNmNDY0NDI0MmY3Yjg2MDY4YzU2ZDBlY2Q0YjBiNjlkYzljNzlhNTgyNjA2ZTM0NWY3MmY4ZTkyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjNRcC9qNVJ2aUY3eUptellJOVE0SUE9PSIsInZhbHVlIjoiNDVMSVl5MEdrMGQ0S1RYeGorUFVSVTAra2tQZy8rRHhBOGo0eGh4cGs0Y09DMUV5UUkyUGlKbklrcUsrTityL21YQ3h0M2p0Q1EvVWo5dllMTURic2dUbWNSeUl3MnBLT2RrbjA0QTFCOHcwdHZQMUdyZTNuY1hVVWtQQjZKY2FPUVVIdC9nN2MwQ1ZqbDgzOFNKZGlCNUkxeXg2YVlQMXhBa01CWTQxRExzM3VjcUlJVUVST2ZLNDdCeDlZdWFOZUhDZjUvWERpT25ybnRJWlpWaGw5OHQ0TWpWYVEzOVIzUUpSUjVRcmtBS2ZRZ09Ybis5ZzBTaGJrUk5SNytWS1NyNjdNQUFzNEFYQjhyTUY1clEzTTM1ZkwzWjNlRmtnZjlLSUtiUXVBQkk2Sk5ycGs2T045b1p4ZEFjcXFHVjZIQkhrcmgzM1EzOUx6REp1cXhSSmp5OGpDUS9UYWVQNnhFWU00UnpYREtMOGRZMGsyVEdtRlZKcGJuczVJZDV1djhvYkR2akFRYTVjdS9hQXp1alY3dEFRZTY5Vm8wdGVVdFZvNmx1TFJ2Vk9yRmpxb0FuVG4yclFFb1hOc1Z3T2l4b085aDhTUHkzVFpBNm1qTFprVGV1cndtSnU3a2toYU0wQkg2Q0toNlRXZlVaNVl5cHhnTkQ3TjcyV3Q1bGwiLCJtYWMiOiI0MjFhNDk0ZTkwOTYzYmE2MTU5ZWM4OTc1MDRhZTAzMDdlNGRjMWE4ZGMzMTVjYTMwODViYmU1NDIyNTY1ZjdjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-892784861\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1174327122 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1174327122\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1774654030 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:12:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpDS21JUEROb1lIYTJ0OHNuLzk1S2c9PSIsInZhbHVlIjoibHRHWEljbmN1cDEweTZhUWJjQVNYK3N2RnIzZFRoU1I1VjErekE1dmpwdXN2andmN2xRUUpCNnIxWjJaZldNNzlXSCt2SzV6N0NFa051dWEvOEJnL1hEVXlqOElTNWlQR1FkdWUzN3RoTTJJNnVGTHJUSmx4NVJSWWVHYlZoNzU4R2dNR0o2c1FZNnNqV2lPQXVjdHh1cmNucVlsVUt2SGZyaE1NT2Z1d2RoVVNQMFdGZm12cENCNXEyMHdqRkplLzUrSW9ISDBiSUV0MFFUWU5QR29JdEJtU3VrcWFLMGVXMWhzV1ZsYzNFV3BpaHUyQWNQU0U3WkRnaXRJOTUrQ2JpSW05Y2dnL2RpcEJqY2lCTHBIbnlJV1dzSXdrUm9CZmhRbFdyMkRZRnN3YzJhQUJDT3VXTzE1WnpHUk81MU5CWlJRRHlLbXdKRUZkVWsxNUtieXRmOG1CQWg5NTJtVldrUGtyL1JJQ0lXM0tPT1pJZ3ZZWDZkU0tUT1JPTDh1RWVwR2VMYWRpSU80M0gvWEduUEdRN0JDcXZtUWVJdFhacTFRVk0wNjFSOVd5akx4N3N1RlRhaEJqWTlqQWsyYVVDSk5lYUdad0wxUWhmL3Y1SmhhOWRHZTZwSzJDNWZSU2t3L21tNW1hRzBBOHRFMFJ6aU9XQTkxckM2OGV3VUMiLCJtYWMiOiI2YjlkNmQ0MTIwODY4ZDg2Y2FiNjNiOWUwMWYwMTJiYzQxNWEwNjFkOTNkMWI2NmMwYjM0NmQwMTgxMWM5NDJhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:12:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik9Ia0RaNmhVSXIxUitnVjJYNERvWEE9PSIsInZhbHVlIjoiaXY2TjFHU1R4Y2dYRnlwcC9TVWpTdU1sdWtBWDlxYkZWRGdxVlZnakJXeFVTNFY1d1ZxTmJGR3VycG4rNDhDLzlHUVlLcm9mVHc1MjVrTEdxVVNOZkNOV293QzJZYXNRMHVLYWV3cktwVzl3Q3VTaTI5Q3ZVV3J2a080YVNJcHVySTZ2WDUyTDQ5ZDZiWHVld0JKRHNjQ0NLZ1hmeGM3UElRdnFBNkJYWndqbW1EaVpBa29YdjZ3dFYrcEJLYWhVTGJ2dkh5SXdJZkZMQUJlVXA4SXoyQlhWQXh1TkJPMlVFY3FQN1hQWDNLSXo1WUQ1emlzaTI5RDF5UGNibDYycWJiN3UrVTRjMlI3cWwzUXF1ZXgrb0twZWJNNkx4TkdIWUw4ZEJLMTNqQTBtb3UzVmRRUGtwSXJtR3lTQk5mSENKMW5yTTBOek5KRDZ2WEJqZUNjSGlTMVUwTUhxRVJvdFBJK0s2bnNVdkExejFueWdpL2I1clNTR0d6Q1psODgxY2FZNGhLVjRTcncrM1dNSFk0WXpQRkVUU0RYSWhhVWl0SjJEZkRWTm9EVklWVFROYk5qQzVHMU1tTGVwWUFROCt5M2VONzd2NU0vSGpXVEFFN1Z2VUlLa211RnI2ZUw2TkpVNExwbHVQQU1CaHJLWFRGRWdxaGhVRlhHeENaY1MiLCJtYWMiOiI4NDkxM2QyMzc4MDkyOGJlNTBmYzI4ZDQzYjU3YjE3ZTU5OGIwODlmNDkyN2RhM2MzYTM0ZTU0NjNlNDk1YzA3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:12:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpDS21JUEROb1lIYTJ0OHNuLzk1S2c9PSIsInZhbHVlIjoibHRHWEljbmN1cDEweTZhUWJjQVNYK3N2RnIzZFRoU1I1VjErekE1dmpwdXN2andmN2xRUUpCNnIxWjJaZldNNzlXSCt2SzV6N0NFa051dWEvOEJnL1hEVXlqOElTNWlQR1FkdWUzN3RoTTJJNnVGTHJUSmx4NVJSWWVHYlZoNzU4R2dNR0o2c1FZNnNqV2lPQXVjdHh1cmNucVlsVUt2SGZyaE1NT2Z1d2RoVVNQMFdGZm12cENCNXEyMHdqRkplLzUrSW9ISDBiSUV0MFFUWU5QR29JdEJtU3VrcWFLMGVXMWhzV1ZsYzNFV3BpaHUyQWNQU0U3WkRnaXRJOTUrQ2JpSW05Y2dnL2RpcEJqY2lCTHBIbnlJV1dzSXdrUm9CZmhRbFdyMkRZRnN3YzJhQUJDT3VXTzE1WnpHUk81MU5CWlJRRHlLbXdKRUZkVWsxNUtieXRmOG1CQWg5NTJtVldrUGtyL1JJQ0lXM0tPT1pJZ3ZZWDZkU0tUT1JPTDh1RWVwR2VMYWRpSU80M0gvWEduUEdRN0JDcXZtUWVJdFhacTFRVk0wNjFSOVd5akx4N3N1RlRhaEJqWTlqQWsyYVVDSk5lYUdad0wxUWhmL3Y1SmhhOWRHZTZwSzJDNWZSU2t3L21tNW1hRzBBOHRFMFJ6aU9XQTkxckM2OGV3VUMiLCJtYWMiOiI2YjlkNmQ0MTIwODY4ZDg2Y2FiNjNiOWUwMWYwMTJiYzQxNWEwNjFkOTNkMWI2NmMwYjM0NmQwMTgxMWM5NDJhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:12:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik9Ia0RaNmhVSXIxUitnVjJYNERvWEE9PSIsInZhbHVlIjoiaXY2TjFHU1R4Y2dYRnlwcC9TVWpTdU1sdWtBWDlxYkZWRGdxVlZnakJXeFVTNFY1d1ZxTmJGR3VycG4rNDhDLzlHUVlLcm9mVHc1MjVrTEdxVVNOZkNOV293QzJZYXNRMHVLYWV3cktwVzl3Q3VTaTI5Q3ZVV3J2a080YVNJcHVySTZ2WDUyTDQ5ZDZiWHVld0JKRHNjQ0NLZ1hmeGM3UElRdnFBNkJYWndqbW1EaVpBa29YdjZ3dFYrcEJLYWhVTGJ2dkh5SXdJZkZMQUJlVXA4SXoyQlhWQXh1TkJPMlVFY3FQN1hQWDNLSXo1WUQ1emlzaTI5RDF5UGNibDYycWJiN3UrVTRjMlI3cWwzUXF1ZXgrb0twZWJNNkx4TkdIWUw4ZEJLMTNqQTBtb3UzVmRRUGtwSXJtR3lTQk5mSENKMW5yTTBOek5KRDZ2WEJqZUNjSGlTMVUwTUhxRVJvdFBJK0s2bnNVdkExejFueWdpL2I1clNTR0d6Q1psODgxY2FZNGhLVjRTcncrM1dNSFk0WXpQRkVUU0RYSWhhVWl0SjJEZkRWTm9EVklWVFROYk5qQzVHMU1tTGVwWUFROCt5M2VONzd2NU0vSGpXVEFFN1Z2VUlLa211RnI2ZUw2TkpVNExwbHVQQU1CaHJLWFRGRWdxaGhVRlhHeENaY1MiLCJtYWMiOiI4NDkxM2QyMzc4MDkyOGJlNTBmYzI4ZDQzYjU3YjE3ZTU5OGIwODlmNDkyN2RhM2MzYTM0ZTU0NjNlNDk1YzA3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:12:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1774654030\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1647539259 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1647539259\", {\"maxDepth\":0})</script>\n"}}