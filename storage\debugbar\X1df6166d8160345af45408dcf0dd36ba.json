{"__meta": {"id": "X1df6166d8160345af45408dcf0dd36ba", "datetime": "2025-06-27 01:05:50", "utime": **********.864775, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.423697, "end": **********.86479, "duration": 0.44109296798706055, "duration_str": "441ms", "measures": [{"label": "Booting", "start": **********.423697, "relative_start": 0, "end": **********.814968, "relative_end": **********.814968, "duration": 0.39127111434936523, "duration_str": "391ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.814977, "relative_start": 0.391279935836792, "end": **********.864792, "relative_end": 2.1457672119140625e-06, "duration": 0.04981517791748047, "duration_str": "49.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45027152, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0028699999999999997, "accumulated_duration_str": "2.87ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.841399, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.505}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.85152, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.505, "width_percent": 17.77}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.857158, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.275, "width_percent": 16.725}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/ledger-report/274?account=274\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/ledger-report/274?account=274</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986325258%7C83%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImQyS0lRYjRoazZ0MWtWMWFQNVo0cmc9PSIsInZhbHVlIjoiWXZUaTJRSExrSlFQdk53bCtzZDV1MG45RG5xc29JT09OUW5ydGppNDY2Z2hLU1Z0MlVTK1lUd1NlUENMUjNzbG1xMm5iRm1kaFRwdmFRNGxkZ1BJWm1LRlZaSVdFS2k4MFIyNU1ZWHRoczYvV29mTThaa1QraGMzOUJqcFl3RXdMQVlCbmZWbzNyVFp0dkVZaU9sTUN4aUlySmk5eXN1WEM3T0NBSWxiU1d1YUd0Y1BFRHViZ0JaenArdjVYVHUyaWp4c2pROUduMnZGVkY0ditmTjlWMXhDUndOZWF6LzJkQjd2NnFOVFRPdWpBa3VxTlRuZnFFNVFXWVFpODZNQTd1dzVwWFdaK0lMM21kTHVPWkpqa29RMjhoZW9zYW1HR2o3MkU4TDlKVHpPeWpuWGpPeUR3ODRGTWpwbmRzMjdEYzdvcGRldURGS1MrK3duVmJNRFB5Uk8wS1pBd2ZEZFRDNC9LcDJOcmFxaDhJSVozWmt0aVIrL3dlSmNwUXpkUXlleVdOemk2alhYbHVWblRHSjFYQnFEalUwem05RU1QSXkrMnNLTU1KSGlSRkQ4eXRqSytmeE9LY3RjbEYzU24xa000U1FxcDlKT1RnbGUrNUZIdWRrNFNHZ2Mwb0dsSUpac1ZDSW1yTWd1YjNxK2V5UjI2VmgvbkdQQk5BQVgiLCJtYWMiOiIxMGFiY2RiYTQyNjhiMjNmNGRlY2ZhZmE1OGQ1NWQ2ZGUyYWRlNDhlOTExOWE1MzU1M2YzZjNiNWVkNDE3MmIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVUZGwrSEozTVNPZHhJS3VLdXd1ZkE9PSIsInZhbHVlIjoiMjNKaTlhUDRjcW1vazVjVEJ0TGZWODlTQ056Ny9OL1JSOUpEZ2xpRThGOWlSUCtydUp2S0RmWlNWbUlqRGRhNWJmeVlDZ3QyTXhRZm16OUlzNW5oT3NpcEhaQkZhLzdXd0g0TU9Cak9TYmYya2NxaDQyR3pUbGNZWW1qUHFWNUc3UStSSWY3akt2bFVtRUJSM3kyVGxLNkVZWDBFbTZRMXUvdFNMYjhDVjU3b2Jnc1JJeU5DVXV5b1RsL0d5V243ZVBIK1UvbHVXQytLUEZoQXRxYTJiL0psUnhscllJSDNNemd0bVZ3QTJIdEROSHY5WHYzb1AzZ3hqaTc5b25TMWxlRjZFN3hzaTduaVFhNUlDMkYxNGNGVlRBdEpzb2ZhRVYrb2FOaU00U1g4dVhxVmVQQUxkOEUvQUw2S2tockhqNGpuV285RUxuUmVEREp0Mi9xb3RQSnVUSlRRK2V2TTM3K2xvQUJTczFhOWppdDNNeG9qNVlTVGFvQXVEVC9Yb2xRaHdqeFlMVWlWSWkzSjdLZStuNGF3aVBzQlZxQjVvc1J4NXh1bWlPSlNLYW5Sa3l6b0ZReGlKRHFyRjlNK1Rja2NrdmJCNGpNWGtKcmo1azNiNHVaRHRCYks0aitkMVlST1RQZG9VUFliRkRUcHFBZGE1Y3VoZUdndSthYjAiLCJtYWMiOiJjYzhiOTdhNWRjY2Q3MjI3YjI2M2I1MGRmZDkxZGFkODMwNzFjMDI0NDVlMzZiMmZlMDNlMzE0OTE3NDcyOGEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1580880514 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:05:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Imt2MHBwZStZL215a2ZYVTRtOVNKaHc9PSIsInZhbHVlIjoiQkZXSW5GSE5hTHJLT2QvWTFaNDh6VDNJREZqNGpHNDFqOFd4ZkZzeGdaRXNGRkhmVUdEU0RSR1lqMnhuckZXV1JraDJPWUlaVnVteUtqdTJhTmZMY1F5UkdoYlhSVnRmcnN1NUtRb0dDUkVNU3FrYlBzWlUvVXVnYTZBM284UnpwUHlDNjh5Y09ycUNKNWdPSkJCUmlQbTk5c0ZnYVpDNW9HeTlLZGVVMHo0VVZ1M053Uyt3YmF6OHdVOUp3eHVNSmtmdm16L3JSRTRBVmJTWlhVNnlVMCtSQkpJRmtpNG40c0xRSThHVWxHelpsRVk0V3U4dS9nRjJzc1Q5c0FEN05lbitoeTl3YnE2V1ljMHA0aDRKaWRtODIxc2ZWMzFaNDU5UmhyYTJWOVg3SEw3S2JTZElpQmRPa0VSejFmMDZ2WGxxVEp4M3NObkFmUmhxV2JwVDdZVUtlcUZvaGVxWmhocGxib3h0R3pjdVg3ZFRZL01oQlRVcUNUblBWL2V6SzVpZ2Q0NkFXMXNBZFRlRGJNVnNDcW5ROVk2ZWZZREMwcDZIdlpyU0orYVA2SGJlS3l2dXFOQmNKVjVNK1pDWmNoVVdRMVQrZjR4YS9WbmZ6M3BGQTlMOURyMCtSZGhuRStFbTRiSHVDdmJBQzFrejV0UlFqMDlDVncwcEV1VXYiLCJtYWMiOiI4OWIzYTQ2ZjY4NDAxM2JkOWQzNGQxN2YyZGJlZmY2YTNmYjI5YmEzYzFhYjZmOGNhYWJhYzM2YmFjMzBmYTA4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:05:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlE4UUJOa0duMmYvbXU4cnNhaUJVNHc9PSIsInZhbHVlIjoiY0l0bk1yMC9GcHE2cEZWakFHeVlBcjJOcnFwT0wvMUF1dGZlQ3BYQW1Ic0lUS0dkSHRsZEtERDRaeVRZclE1TXJDc2JFRzlvVmtxNFdRVUtmbzY3WG9ZajJxZ1VLSmxDc1BqYmF1d1ljVWNyb21tZnFwWk1VWFYrRU5vUmtoczhiZGlSM3NRckNxcExYd0lkQWpzMW9PK2tSRisrZXNkcUdOTTR1MUpPcHBKV3RFanJ3ajlodXp3VmkwUEsyYk1KdEhqRmt1MG9GUUFXbVl0b0NVb3gxSHZseHRxZkc2eHpINHkzZFBLejRzeVpJbTFjUGlHUklONXMrSDF0SUp2MFJZVzk1N1dDYjdmTXNXMWlpbDdSZXViMVNtU1daUFA5S2M2aFY4WVN5akhmNGQxbHhzWlh0U1A2MVRjVUwycDh4ZDFJdXhCVkFYOVVsN3hpU24xTXAvWU5CNmdzWmkwNTJQVVJZcEF5VGpqNFJpRjNUVVV6VUsrRCtrVm0yNGoyMVRqa282a1Q2bkFuSUhQak1JZ1U5WnVVd2tYNklabUlma0RMcjE0RGgzMHoyZThTL2ZjYzluaVFrNjUwVFRWdWJ3SFRJUjY2U0JIb1JOZFZiUDdDa1J6LzdQNnAwaG45YnM4dlIrTnpLZmZpQzBlQ0YvSDU5VE5IQlVaMmJpN1oiLCJtYWMiOiI0NGFjZTg5MmJiZGQ1ZGUyMWRjMzk4Y2RkMzc0NGIyMjJiYzc5ZGNmOWFjNjU1MGIwMTZiN2FjN2M3NDdmOTk2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:05:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Imt2MHBwZStZL215a2ZYVTRtOVNKaHc9PSIsInZhbHVlIjoiQkZXSW5GSE5hTHJLT2QvWTFaNDh6VDNJREZqNGpHNDFqOFd4ZkZzeGdaRXNGRkhmVUdEU0RSR1lqMnhuckZXV1JraDJPWUlaVnVteUtqdTJhTmZMY1F5UkdoYlhSVnRmcnN1NUtRb0dDUkVNU3FrYlBzWlUvVXVnYTZBM284UnpwUHlDNjh5Y09ycUNKNWdPSkJCUmlQbTk5c0ZnYVpDNW9HeTlLZGVVMHo0VVZ1M053Uyt3YmF6OHdVOUp3eHVNSmtmdm16L3JSRTRBVmJTWlhVNnlVMCtSQkpJRmtpNG40c0xRSThHVWxHelpsRVk0V3U4dS9nRjJzc1Q5c0FEN05lbitoeTl3YnE2V1ljMHA0aDRKaWRtODIxc2ZWMzFaNDU5UmhyYTJWOVg3SEw3S2JTZElpQmRPa0VSejFmMDZ2WGxxVEp4M3NObkFmUmhxV2JwVDdZVUtlcUZvaGVxWmhocGxib3h0R3pjdVg3ZFRZL01oQlRVcUNUblBWL2V6SzVpZ2Q0NkFXMXNBZFRlRGJNVnNDcW5ROVk2ZWZZREMwcDZIdlpyU0orYVA2SGJlS3l2dXFOQmNKVjVNK1pDWmNoVVdRMVQrZjR4YS9WbmZ6M3BGQTlMOURyMCtSZGhuRStFbTRiSHVDdmJBQzFrejV0UlFqMDlDVncwcEV1VXYiLCJtYWMiOiI4OWIzYTQ2ZjY4NDAxM2JkOWQzNGQxN2YyZGJlZmY2YTNmYjI5YmEzYzFhYjZmOGNhYWJhYzM2YmFjMzBmYTA4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:05:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlE4UUJOa0duMmYvbXU4cnNhaUJVNHc9PSIsInZhbHVlIjoiY0l0bk1yMC9GcHE2cEZWakFHeVlBcjJOcnFwT0wvMUF1dGZlQ3BYQW1Ic0lUS0dkSHRsZEtERDRaeVRZclE1TXJDc2JFRzlvVmtxNFdRVUtmbzY3WG9ZajJxZ1VLSmxDc1BqYmF1d1ljVWNyb21tZnFwWk1VWFYrRU5vUmtoczhiZGlSM3NRckNxcExYd0lkQWpzMW9PK2tSRisrZXNkcUdOTTR1MUpPcHBKV3RFanJ3ajlodXp3VmkwUEsyYk1KdEhqRmt1MG9GUUFXbVl0b0NVb3gxSHZseHRxZkc2eHpINHkzZFBLejRzeVpJbTFjUGlHUklONXMrSDF0SUp2MFJZVzk1N1dDYjdmTXNXMWlpbDdSZXViMVNtU1daUFA5S2M2aFY4WVN5akhmNGQxbHhzWlh0U1A2MVRjVUwycDh4ZDFJdXhCVkFYOVVsN3hpU24xTXAvWU5CNmdzWmkwNTJQVVJZcEF5VGpqNFJpRjNUVVV6VUsrRCtrVm0yNGoyMVRqa282a1Q2bkFuSUhQak1JZ1U5WnVVd2tYNklabUlma0RMcjE0RGgzMHoyZThTL2ZjYzluaVFrNjUwVFRWdWJ3SFRJUjY2U0JIb1JOZFZiUDdDa1J6LzdQNnAwaG45YnM4dlIrTnpLZmZpQzBlQ0YvSDU5VE5IQlVaMmJpN1oiLCJtYWMiOiI0NGFjZTg5MmJiZGQ1ZGUyMWRjMzk4Y2RkMzc0NGIyMjJiYzc5ZGNmOWFjNjU1MGIwMTZiN2FjN2M3NDdmOTk2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:05:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1580880514\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/ledger-report/274?account=274</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}