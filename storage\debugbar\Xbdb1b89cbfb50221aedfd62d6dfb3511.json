{"__meta": {"id": "Xbdb1b89cbfb50221aedfd62d6dfb3511", "datetime": "2025-06-27 02:33:58", "utime": **********.57837, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.104016, "end": **********.578384, "duration": 0.4743678569793701, "duration_str": "474ms", "measures": [{"label": "Booting", "start": **********.104016, "relative_start": 0, "end": **********.508233, "relative_end": **********.508233, "duration": 0.404217004776001, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.50824, "relative_start": 0.4042239189147949, "end": **********.578386, "relative_end": 2.1457672119140625e-06, "duration": 0.07014608383178711, "duration_str": "70.15ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45409488, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.023779999999999996, "accumulated_duration_str": "23.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.538608, "duration": 0.022949999999999998, "duration_str": "22.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.51}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5700781, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.51, "width_percent": 1.934}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.572565, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 98.444, "width_percent": 1.556}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im4xdld6cFZEWHNCcElXMkMwSHIvd3c9PSIsInZhbHVlIjoiQjlZKzUyOE1HQnIyQlpqSU1tRW1oTDhxazVUWjA3WVpwSVhMMitFc3VrbU95eVFkRmZrdFZLb2VkUW1hK2FNUHJCZHpBZEhtOUIrZy96V1d4dnlja3lSV2RYK3hadlNKeG96NkdPemJCRGRUTVpYaE41MVdGbXdjVDdDU1NXSVJEL0JSbEFmdFZjNXhqc2o4S3ZTbjhCeFF1OUtTaHdDbWNPeGg3eHFrbVc1eEtVZ1RuekhjeWlMaWlzVzZzKzVFQUNpMXlrVFpmZW5sRjUydUxtY3plemxYQ0NOMXBqL0RrdkpkWVd5WHFpUFRTa01LZ1JtQVFKbExramxYMGFtMk9nSWFzRDZzUnVVY2dTb1hHdWFtejV0YzRKVVZaaXlYRFNHcmRPblI2TVhmeTRydk54bXpmT1hIVGh6RDJXN0VIU2ZrdjhWNU0yVHFWeDh6RjI5eUlUUzNGcTJ2K2trWmZzb1owZlZSREtaS2svTEovcE0ySkdLaEd3SFRzQzBub3pHekthd0NHM2VVRkNqdWlVQmdKazAweW82cEx1VDBFRmMzOGFjU3JyZllqNmhkSWdKQUUwU1hFaC9vVmowTit5ejgvQlFVY0JYSkwwMUhKM1h4NEV0bmY0Rll3bWZxZC9lby84dG45U1BXNU5OWkQwaVdBWjFjUW9uUjVzYjciLCJtYWMiOiJlYWZlOGYwYWY5ZjRhYTViMTA5OThhZTg1MjRlYWY3OThiNGUxZjEwMjNkYTIzMzlkODBiMGMwNmFmZDEyODQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtyaFRxSEFuUDBtWWhudUdoUFRSd0E9PSIsInZhbHVlIjoiZDVJVEx5Wi9BbmUwM1JsVko1K2xBNFM4Q1hzZjNOTjBKbnRhSWVVclF3b202end0d2hDcmErN1lQbmlCejFoQVNjWkpFZmg4R3Z6c1dpQ3ZlYUxmTit5c1pEVi9saHdTRHgxd011N3MzZmhNc1kvV0VqTXk5ZjBxaFJJL3dWSzNzdytvcldwZGx0TGRHR0c2U3BmYXFYUmRNSTZrejNEOVdiTy9BZWVFNm1CUEJaa1Y1RTJ4Q250dGhKQ1lrS2NtbUdPZUh0MFQzZkZ3YXVhUStMemRFb3hFZHB2bXQzS0FjWkg0UWJ4bmJUL2toVE52U2N6R3gwTTdOK2Y1L1BHYTNpSTd0YjNlMUJKTHEzSlNCdG4zMDBrOG9HUjMxM0t5NE84L2V2anlMRkEwQlB3Nmt0NVpzN3BRanVmTDRDZG1rK1dzVENwendMcHVuTVVMTENUeXZsMGxjMVYxOWF3YkdLZTVFVStVNGdIWlp5WDd1UlBsMUVOZVlaeFFSQWEzWnRzaVRZRWphNkQ1RjM1VzdLRWQ1WWY0Tm9Fc21TeUVFWWVXNmZ2ODVoZ0lKR3o0czJWZm9FeE5sSmZ6RUZJUUk3dk1La1diYy9XV0JhYjFMSXpFaU40Q3pCSVl6NkZzM1Iyd0prZi85bkVsWE5nRjIwM05DNjdEcVJyckxMMHYiLCJtYWMiOiJmMTEyYjBiM2E2MmEzN2YwMDNjZGI4MzhkY2I1MTA0YTA4OWVhNjJjOWM0ZWU0ZGE3MmZlZTdkZDY5N2NlNDA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1337893071 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337893071\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1506583422 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:33:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InZWdHRwL0tUOUJrRUI5N0o5Q3FrbGc9PSIsInZhbHVlIjoiSzZSRFUvdnJLNWFsZXhXYnRUSmZtQS9wejFkNmhINU1HdUV6YkE5R3N4SnEydjdZZVVaNm9YeGRIYkJkSmZ3TC94aGMrMVY2eTV4cDJDT09ZWGRTdmtOaFFXSmhIQm16VGpCSCs3QUxFS1lXSENqV0tZak91ZklFa3lwUHUxRU9pb3RlR0ZGNVNjcFJ2RDFhS3UwQnVveEdWUGFjcVpFZWdKRngwK0o2NHJZeElwWHJJR1RpREVJKzE2VXhhdGVQYyt2U0VYMzIxaXBLalY0OC9tMklzYzJCU0tEZnVkTkhqaXBwMm0vRVA0amswT3MrZ2JZNUdtcGdzSGVLQ2V1T3ZlV1pWZFkxUWt2cnlrR2NVcWczYkdVdjk3bmNOV3NoVEJQaVArWmVtSG1WbW9jd21ETzlBWWxxTGY0YnRaNURKMmRraDBibHRzQ1lrUmFzZHZDL0JBdnJsZXNTQUwxNjlFcmxFSU1qKy9iWVptSzMycDFmQUhOWWlpWXVETUJSZE9kSWZvSHdUeUJzeURFbHlKcWdMVnVhQkZDZXFJN3Y4MFltUGVwdDQzTEFTWHRtWXh4amVFbys1YU5wenJFbGtXdFd1NEFUWHFXYktEamhPYTB0SllGcU82SE5taG54RzI1Q29oUXhQRnRxMnZJTlc5TC90NUJuZUF2cm4ydVIiLCJtYWMiOiI5NDM0YTE4OGRmZjY3Zjg4NDkzZTc3NmU1ZjliYzI1ZWJjMWY5YTMxMDhkMzgwY2Y2NTkwZmRhMTVhZTg4MWVkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:33:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik5ubWI4d0FOd3J0Q1o2c2FVRkNMckE9PSIsInZhbHVlIjoiNThxVnN3L3lUR1NwOGJhV1dXQm81VWJhZm9ubVpMOTJXcGsxMmNLNU5yWDlpaDdETG8wUVZ3ZWVGdlNqaUtFamhoZ3dsc0NwQTRGSWtpTkxwTWZabkl2Unp0cExQaWowUG9NSFAxZGdWb1ZhWnY1ci9jS0Z6ZlorYW5tL3ljMXdEZWJzSWs0VE9iOFlaSWRtalNieGJpRnVJQ3ZVR2NiRHEvWmdTbnZQNHZTeHdiK2QrTTZXUnp6alJHOVB4SU9oM2ZXazdhVEZwcllORFZROHBnTmkwSFhTQmJLb2t2MHBQR21DMmJ4b0puWStCSGFRZWhzRkZCVXVhd215MUxvbm1XS295cWpaZTlrVVFMMnJGTlN3aDNiMHVnZjgyckZac3Yrb21oSzhZUGt0aEFoT3JxTENTWDJzWStHeWpFa1FHYzkwN1IyRFR5eWxIcjdDUDRvMDdKbkRIYzZxanloNG5jSzBrV0hXQ0FnY1ZiZmcyUVdpUmhyYnEzTm9jQ0x0YWlFZitTNStDV21zMU93ZzFRaCswVmFnczVvbVJ3WUVLMWpZRVprYkpNdGF2U1lleTlsb2RYRm9RT0NkWkN2bHE4QVIxSHZuWEVETk5ya3lPUkhNWGJwbFQwMVFVam4wTWZpakNKS1BBWEd3OUc0RlpuTFFVL1ByQ0J3eG1UZnciLCJtYWMiOiI5NGM5ODRiZWRiYjEzMTU2ZTIzNWIwNDRlNTFmODBlY2VjMTYwMDgxMDhmZWZlODA4MjI1MDQwOGJhN2IzNzgyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:33:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InZWdHRwL0tUOUJrRUI5N0o5Q3FrbGc9PSIsInZhbHVlIjoiSzZSRFUvdnJLNWFsZXhXYnRUSmZtQS9wejFkNmhINU1HdUV6YkE5R3N4SnEydjdZZVVaNm9YeGRIYkJkSmZ3TC94aGMrMVY2eTV4cDJDT09ZWGRTdmtOaFFXSmhIQm16VGpCSCs3QUxFS1lXSENqV0tZak91ZklFa3lwUHUxRU9pb3RlR0ZGNVNjcFJ2RDFhS3UwQnVveEdWUGFjcVpFZWdKRngwK0o2NHJZeElwWHJJR1RpREVJKzE2VXhhdGVQYyt2U0VYMzIxaXBLalY0OC9tMklzYzJCU0tEZnVkTkhqaXBwMm0vRVA0amswT3MrZ2JZNUdtcGdzSGVLQ2V1T3ZlV1pWZFkxUWt2cnlrR2NVcWczYkdVdjk3bmNOV3NoVEJQaVArWmVtSG1WbW9jd21ETzlBWWxxTGY0YnRaNURKMmRraDBibHRzQ1lrUmFzZHZDL0JBdnJsZXNTQUwxNjlFcmxFSU1qKy9iWVptSzMycDFmQUhOWWlpWXVETUJSZE9kSWZvSHdUeUJzeURFbHlKcWdMVnVhQkZDZXFJN3Y4MFltUGVwdDQzTEFTWHRtWXh4amVFbys1YU5wenJFbGtXdFd1NEFUWHFXYktEamhPYTB0SllGcU82SE5taG54RzI1Q29oUXhQRnRxMnZJTlc5TC90NUJuZUF2cm4ydVIiLCJtYWMiOiI5NDM0YTE4OGRmZjY3Zjg4NDkzZTc3NmU1ZjliYzI1ZWJjMWY5YTMxMDhkMzgwY2Y2NTkwZmRhMTVhZTg4MWVkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:33:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik5ubWI4d0FOd3J0Q1o2c2FVRkNMckE9PSIsInZhbHVlIjoiNThxVnN3L3lUR1NwOGJhV1dXQm81VWJhZm9ubVpMOTJXcGsxMmNLNU5yWDlpaDdETG8wUVZ3ZWVGdlNqaUtFamhoZ3dsc0NwQTRGSWtpTkxwTWZabkl2Unp0cExQaWowUG9NSFAxZGdWb1ZhWnY1ci9jS0Z6ZlorYW5tL3ljMXdEZWJzSWs0VE9iOFlaSWRtalNieGJpRnVJQ3ZVR2NiRHEvWmdTbnZQNHZTeHdiK2QrTTZXUnp6alJHOVB4SU9oM2ZXazdhVEZwcllORFZROHBnTmkwSFhTQmJLb2t2MHBQR21DMmJ4b0puWStCSGFRZWhzRkZCVXVhd215MUxvbm1XS295cWpaZTlrVVFMMnJGTlN3aDNiMHVnZjgyckZac3Yrb21oSzhZUGt0aEFoT3JxTENTWDJzWStHeWpFa1FHYzkwN1IyRFR5eWxIcjdDUDRvMDdKbkRIYzZxanloNG5jSzBrV0hXQ0FnY1ZiZmcyUVdpUmhyYnEzTm9jQ0x0YWlFZitTNStDV21zMU93ZzFRaCswVmFnczVvbVJ3WUVLMWpZRVprYkpNdGF2U1lleTlsb2RYRm9RT0NkWkN2bHE4QVIxSHZuWEVETk5ya3lPUkhNWGJwbFQwMVFVam4wTWZpakNKS1BBWEd3OUc0RlpuTFFVL1ByQ0J3eG1UZnciLCJtYWMiOiI5NGM5ODRiZWRiYjEzMTU2ZTIzNWIwNDRlNTFmODBlY2VjMTYwMDgxMDhmZWZlODA4MjI1MDQwOGJhN2IzNzgyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:33:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1506583422\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}