{"__meta": {"id": "X2fcf27b4bb32a6cf9cce9d6fa9d35a41", "datetime": "2025-06-27 02:12:09", "utime": **********.82111, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.406719, "end": **********.821131, "duration": 0.4144120216369629, "duration_str": "414ms", "measures": [{"label": "Booting", "start": **********.406719, "relative_start": 0, "end": **********.773726, "relative_end": **********.773726, "duration": 0.3670070171356201, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.773736, "relative_start": 0.3670170307159424, "end": **********.821133, "relative_end": 1.9073486328125e-06, "duration": 0.04739689826965332, "duration_str": "47.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43379592, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.00239, "accumulated_duration_str": "2.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8041482, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "yL6RI9Dzm7wWSneQaLee8nwOi8fTcp9yg3tQI2BA", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2131059912 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2131059912\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1719013065 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1719013065\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1965842329 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1965842329\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-792663442 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-792663442\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-499055810 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBOWWJDM20yTVUyVnhMWkxpN1Bjb0E9PSIsInZhbHVlIjoiR3ZjUlVXWkdBQ3M1QUwrcjg4L29FZHlUdUlUNSt6Yk13c2cvS2RWbW1KTzVPTUpEYjY2SEptU2l0NG1rVHA3T3ZnbldhMVNRekRQdVBsUHlOdmpBMjRBYWUvQnFGYnpFWmFFYjBIZ0drRS9aWm1xbWRuWklUZEdxeGhGeVlBbnF3dy9kK25tWE1OeXh3WWpwZGV2YStwWVhxaVJPWmxZdjZNNXlhc1lzVUNDenBXSlB5cDJVSGI3KzJkTjUzMHBYWlAybzdpT0xTWk5ZdmRDVEZHUVhYS1dvNG4wcisvSFFvUUUxdGJLUEppelNub0plbDVnR3JIQVZleFVYMW5GM3A5S0FDZFFIWVVrbWY2YkVqNFRtT0VsUEhUS2pLMDhLeUE3VWU1bTFqOWVpcUh5Tk1veCtlRG4zenAwa0x0eTNaYi9wRjZ2c2cvWFBDMVpYNlhLS0lDR1FyeXlaNWhCTzYzOVphVlZGd3dqRXRkaUF0QzUrcExUaHhodlBEZm11WEpFcVFwd1ZRcEJ6YlhNUjNOYm5TTkJvejVHNklrWEZHb0JNLzZYNWZhbUh5Uit4STRjc1BqRzlPVUJKL2FKL1N2b3dBcTJPWElXR3dZNUJDdkpUTEhYOFJWRHhhQzVwUlczOVlEZ3hwQkU1a3lEWEsrb1FjL3VHVnhDMjEwRzQiLCJtYWMiOiJhNTcxYWJhMzgyMjU5MGU4ODViODAwZWFkNjUyZDFjMzUwOTQzNzYyM2I5Y2ZmYTI3MjExZDk0NjQ1MDA3NjY2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:09 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJYSlZEZHQ4bjZSb0l0eGMwaHIrZWc9PSIsInZhbHVlIjoiU2Y3M2xMYjJkM3Q0c2Y2eVdBVWxxa0gvYk8wM0FBc2N1bFlyTm13MWhGY0NabEpkM2ZkYmpvbEkyOUVhcmE5YjdnZnhHMGJNSFZqbmR3eHhpWDRYR1p2QkE3WHl4WHpMdUZ3b0JDdDBIRlBoSmovNU1KVnhpNGpRWmpBeExYRXRRZ2xRWU1FZWU1QmZpN0dFd1dPL0g4ODJjRFpGZWQ2NGZvZ0wzZVppUmxKUG9zcUE5R2lmMEN4S1lKNmxINDhYbEZRMlN4TCtQRGs2YmpZV3BJeUNNckxZay80OXdCVlVqT0QwZXBKdERycVA5cy82eWRROEFTRFFCQUhMVUZ1SnZIMXJwb1NZcXF2WXk2dFQ1dUd1eWlpMXhWNUI3eUdzOG93UjhibExkWGtOTUc5NXZESm0xVXc1NXJUWEs1MmQweWdvYXZQYWFoOUZMcTJlVzN1SVd1ZkUxeEk3WHQ1NTNHajBFSTFlRVpzMi9rRldXckJlbmwzMGZNSldxb2tNM0diN2xNaXMxNTI1Y2tsMkFIalBBOGRYVFdBRjZSZlVCc0g4OXllT0RSa0xwRjgxSjI5cnB1UFFxNktNZGdQMVJ2VUc1N2ZNS0tkKzBDczl1TXAwWlVvbFFuMlNYYlo5cUNxL2EyR2x2dGVPbCtWMmVVdkhTYyt2YlRpOGdEZzEiLCJtYWMiOiI1NWZkODc2M2FlMTI1NDI3MjRhM2M1OWU3ODUwZDY0OTFkNmUxMTIxNGZjNjEwZjk0YTcxYmUzZjE2MDFlOGIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:09 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBOWWJDM20yTVUyVnhMWkxpN1Bjb0E9PSIsInZhbHVlIjoiR3ZjUlVXWkdBQ3M1QUwrcjg4L29FZHlUdUlUNSt6Yk13c2cvS2RWbW1KTzVPTUpEYjY2SEptU2l0NG1rVHA3T3ZnbldhMVNRekRQdVBsUHlOdmpBMjRBYWUvQnFGYnpFWmFFYjBIZ0drRS9aWm1xbWRuWklUZEdxeGhGeVlBbnF3dy9kK25tWE1OeXh3WWpwZGV2YStwWVhxaVJPWmxZdjZNNXlhc1lzVUNDenBXSlB5cDJVSGI3KzJkTjUzMHBYWlAybzdpT0xTWk5ZdmRDVEZHUVhYS1dvNG4wcisvSFFvUUUxdGJLUEppelNub0plbDVnR3JIQVZleFVYMW5GM3A5S0FDZFFIWVVrbWY2YkVqNFRtT0VsUEhUS2pLMDhLeUE3VWU1bTFqOWVpcUh5Tk1veCtlRG4zenAwa0x0eTNaYi9wRjZ2c2cvWFBDMVpYNlhLS0lDR1FyeXlaNWhCTzYzOVphVlZGd3dqRXRkaUF0QzUrcExUaHhodlBEZm11WEpFcVFwd1ZRcEJ6YlhNUjNOYm5TTkJvejVHNklrWEZHb0JNLzZYNWZhbUh5Uit4STRjc1BqRzlPVUJKL2FKL1N2b3dBcTJPWElXR3dZNUJDdkpUTEhYOFJWRHhhQzVwUlczOVlEZ3hwQkU1a3lEWEsrb1FjL3VHVnhDMjEwRzQiLCJtYWMiOiJhNTcxYWJhMzgyMjU5MGU4ODViODAwZWFkNjUyZDFjMzUwOTQzNzYyM2I5Y2ZmYTI3MjExZDk0NjQ1MDA3NjY2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:09 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJYSlZEZHQ4bjZSb0l0eGMwaHIrZWc9PSIsInZhbHVlIjoiU2Y3M2xMYjJkM3Q0c2Y2eVdBVWxxa0gvYk8wM0FBc2N1bFlyTm13MWhGY0NabEpkM2ZkYmpvbEkyOUVhcmE5YjdnZnhHMGJNSFZqbmR3eHhpWDRYR1p2QkE3WHl4WHpMdUZ3b0JDdDBIRlBoSmovNU1KVnhpNGpRWmpBeExYRXRRZ2xRWU1FZWU1QmZpN0dFd1dPL0g4ODJjRFpGZWQ2NGZvZ0wzZVppUmxKUG9zcUE5R2lmMEN4S1lKNmxINDhYbEZRMlN4TCtQRGs2YmpZV3BJeUNNckxZay80OXdCVlVqT0QwZXBKdERycVA5cy82eWRROEFTRFFCQUhMVUZ1SnZIMXJwb1NZcXF2WXk2dFQ1dUd1eWlpMXhWNUI3eUdzOG93UjhibExkWGtOTUc5NXZESm0xVXc1NXJUWEs1MmQweWdvYXZQYWFoOUZMcTJlVzN1SVd1ZkUxeEk3WHQ1NTNHajBFSTFlRVpzMi9rRldXckJlbmwzMGZNSldxb2tNM0diN2xNaXMxNTI1Y2tsMkFIalBBOGRYVFdBRjZSZlVCc0g4OXllT0RSa0xwRjgxSjI5cnB1UFFxNktNZGdQMVJ2VUc1N2ZNS0tkKzBDczl1TXAwWlVvbFFuMlNYYlo5cUNxL2EyR2x2dGVPbCtWMmVVdkhTYyt2YlRpOGdEZzEiLCJtYWMiOiI1NWZkODc2M2FlMTI1NDI3MjRhM2M1OWU3ODUwZDY0OTFkNmUxMTIxNGZjNjEwZjk0YTcxYmUzZjE2MDFlOGIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:09 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499055810\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-884379967 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yL6RI9Dzm7wWSneQaLee8nwOi8fTcp9yg3tQI2BA</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-884379967\", {\"maxDepth\":0})</script>\n"}}