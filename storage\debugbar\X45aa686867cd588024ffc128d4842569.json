{"__meta": {"id": "X45aa686867cd588024ffc128d4842569", "datetime": "2025-06-27 02:12:25", "utime": **********.820288, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.377394, "end": **********.820302, "duration": 0.44290804862976074, "duration_str": "443ms", "measures": [{"label": "Booting", "start": **********.377394, "relative_start": 0, "end": **********.743984, "relative_end": **********.743984, "duration": 0.3665900230407715, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.743994, "relative_start": 0.36660003662109375, "end": **********.820304, "relative_end": 1.9073486328125e-06, "duration": 0.0763099193572998, "duration_str": "76.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722096, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02368, "accumulated_duration_str": "23.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.771675, "duration": 0.0228, "duration_str": "22.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.284}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8034132, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.284, "width_percent": 1.858}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8094642, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.142, "width_percent": 1.858}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1044097795 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1044097795\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2137758311 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2137758311\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-177003778 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177003778\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1238753900 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990342712%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InFUTERlSC8xZ1JIdVZDV1RuLzIyMlE9PSIsInZhbHVlIjoiWHAvZ3RMaDlsV0dPSHVFOWcvdk5BZjB2eVNhMk03alpMdXB3K3ZidVV3MzFyWUxFNlUxOFJTNHJHSStvM2lrQUJ1VXJ2d2FGSHV6M3VEcnNqMGZKZHdDcXpuejI4REh2V0lSMmg4MXhpSGljeGMxcGJzd1FEdEFjUkRlYmZYWSs2OVhpSUVEdGJ1NklxZFVkRmJzSkkrVDgySTROOC9lYnpkQXAwSHJNdklwNHBobEdHcURBdkRiSm5qbGVyYTN5bloxWWpYNjU4dGhVWVgrM3RnL0xXNUdORXB1Tkhrc0x5M2E4Zjk5TXhzNmFNellWVENSS1NZNmxIb3E0UTl1eWU2V0V4SGNVQmJrKzlsNGZ5S09aTGVXQThkZTJyTTAyZDMzNEVQUE40VE51cU9oQUcwYjdjYk0yN1Q0dTduT3hSLzI5VHdQbW9oUGhQb0pYWlRzb2VyRFNCYjNnYiszd1pBRlRHZWdZWjJKTE16YkJublZ6enE4cnVid2ZtZEpMRGZWbmtqeERTN1dDcU1NZVJSa3NFU0JjTnRUR01ZWHpEa3lRV2dMTm5RRFV3SVR1QUdJb2VRM1A0WHljUlBuMG14TGhUdEJyaDA1c3JlLzV1a3UzWjlRNnNURFNwRDhnTGxxRDQ0dnBDUFhja0NLdW13WmNaU05FbEdmWHh0dysiLCJtYWMiOiI5ZTc3YzU5NmU2NmI4NDUxNjY3ZWZkZjQ2ZGE5NzBmMWUzODY3ZDU4MDA3ZTBhZjY0YWNjZTE3N2I3Njc1ODkwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlA3TGk3OFMzQzlkSjVQN2VrS0oxZnc9PSIsInZhbHVlIjoiZFg4M3IxQzFYSnNab0hDYWptUEV4cmZTSXkwODMzMjBGUkxQN2IzaWl3M3poTTNUdnN1TVg3TnEvTU9EUTVjSDB0NTQraDZRd21IK0ZkQlZoWjhUenZNV2RwN0FVL1pJNkxWYmlBcHlvTGhqOEdKT1cwM0ZnQnd1dEc3dXVzMXFsTEU0TEJ1N1AwakRicUYrWCttaWk3aENsSHQrZXcrSlZydWhMZGFaR2NIQWRRYjVyN1lBQW9RTzlIQTFhbVBXbTZVZE5rRGtIeUZuL3Jnb3BjSjd3RmdQMHM3dkZ3aWtkKzNYV24yVUtMQ0hnTy82cE5ZRCswRFlDbURjMGFGdmhIYlYwcFlHZXJycWpqdnVkbTNCblVIN3YwV0I5aTYrSFc2cU43YVo3UldOenAvR0pHMXlhTTh0NGpWbldzMyszaDhEVUtBZlNvZzc4bjVQZzhNNVZxM21pZDRKQlh6OEF5SGVJTVpXSjRWd3htdEdWcnl6UUdndHIzcVNBYkFpcVNiMzViVVhXTTlJTGllc2ROWm5ORWIvbEwzL0tUMWE1K01lZ3JVQ0c3ZUNLZ2JibzZSaWZyc1ViTmVuVXBTWnNIcUZTTTc2MVJ6L2lOQmloak1WRUxtT1lIYWRsY1MrcFV4TjNhemRXZmY2Tk1Ea1g0N0hkTkl3RjJ0d1JsRDgiLCJtYWMiOiIyNWFhZmMxYTA1YWU5ZGUxNzYyMmRhYTQzMjkxMjk1NWY3NjA2YjQyODQ0M2EyMzZiY2U5ZTZlZDg2NjUzOGI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238753900\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1192597863 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192597863\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-681705626 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZEbjR0eWNKYWNObTJZRFVhcmNNd2c9PSIsInZhbHVlIjoia2ZpYWZXUkhjUmU5Qkttb2ZnZWJsVWtvenJhUGlkRy9Gb0JZQ3RSUHlZOVNsRStCckNjMGZ2THZMQlNCV0grdFJmNHFGaTk5cEg1YTdxbE5tZVhuZVlLK3AzdXlHL3dGdUVPRTQ3U2FJdUtlZ2VmQXBoMWN6RVpBdFFBVXVpMWtQR1JBSi9TU3FTN2szdktOaGkyM294VjB6SjNqUDBQSmJ4aDlCR1dzcGNKOEdzVHRld2dlTjYwK2JLdWl1YVJRd1BTVUpRVTA3alB0YlpZcGhwWWtLZUd1aFlyWUVuTDZ0eVM2bmlkT0JMaUl6dTE1bnZMSjFDdTZ6aDhiTlZpdnYwQXd5YTNuZ3V1djRuclJQT0JFUXIvOC9zK1VsWnN5aExaYXJQK0hOc1RIdnB6ZnhFZTd2RUZiWGdKeWdPS09qYndVQUN2VkMvQVRmVTZibENxQUM5WFRRT1JzQXNZR1BvVWxSUmFKZ3hzQmN1Q0Q4bURxZm1ZWG8zMWRTOFdJSEZRcWtDT0h6cmtZdEtiaTBqdkQ1RzJSQjVkNzd3M0EyZG9yV1RLZE5TSW40aTZacms3K3hTUmp2Z2o1YVpjRDBSVStZVnNjTzBvekJwOGQwRzc4Sjk2UHdQR3YrTGlIQWc4VVI5QjFQWXI0K1QwMkZXWERSTkhqem9Va0thN2MiLCJtYWMiOiJmNWYxMTBmNDNhN2ZjOWUyMjcyMmMwMTJhMGIwNDA3Njc3M2ZmMWEzNTM0Njg1MTEyMWI2ZGJlYTFlNTAzZjViIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdiQnNMbThTS3d2RS9CeEtPTnpyNWc9PSIsInZhbHVlIjoicVphZnlReWF4aFJzV1JNaEVsTEJXemtJblc0QndMLy9mYjVLNUs5VGlON2w3NEtmeTAvSnRZK1BXeW5vRzgzQlJYUlIvQ1RmeklxZ1BoTDByRWMvejZNMEhTTjI1MGgvTCtmSnRJbkNpNFZZUW4vVHVmMFU2TTJWanREZ0YvQUpqaTEwdUVtRm9HR25rNERsZU5sZjF2U0VQaHc2MHZCOVRrUnJaem9SSnk4aVlncnJXRnBWZVcvTSs3V1RnWnlaNTN3cHNLay9GRDNicExuUGNleldzT3Z2eFFoTlFoR0g4cTRkYytzcGdqTmplM3k4YlVvUW5xWDY2RDdYRDdMT21qQi81UUVaWkE2UW1SRHltRE8yNjNpWUhFVVcwdTZWc21qRWpFWGdMeWlReW10K1JjMUZEb1Z0OGRzcmNCWnd0Z1ZSckhKaks4cUJwOTVEZ2FkTWRUSCsxRGVLZWNNczcrelBCSzdYa0ZKdVhqMnp1ZlU3dVljN1oyNUtlQ28zK0dlUFZsT0crdzBLU1dGRTIrbnEwUitUeDRORmFkRmErdXpLd20wM1E4U0g1enlHUEdCUjgwdlgvRVRQNWQ0TXk1by9iM0xXR1VQTmpLSG5pSVJqR2lLbVA1NkZFNm5NUEZzL3JCZXJTaFBza1JNT3pZQXhUNUNoL0g0eUtsSnciLCJtYWMiOiI1ODdkZWE0ZDlkZGM4MjM1NzQ4Mzk4Y2ZlZjNiODFjNGY0YWNjNjE0ZWQ1MDkzYjkxYmQ0M2E1N2FmZTk1ZjM1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZEbjR0eWNKYWNObTJZRFVhcmNNd2c9PSIsInZhbHVlIjoia2ZpYWZXUkhjUmU5Qkttb2ZnZWJsVWtvenJhUGlkRy9Gb0JZQ3RSUHlZOVNsRStCckNjMGZ2THZMQlNCV0grdFJmNHFGaTk5cEg1YTdxbE5tZVhuZVlLK3AzdXlHL3dGdUVPRTQ3U2FJdUtlZ2VmQXBoMWN6RVpBdFFBVXVpMWtQR1JBSi9TU3FTN2szdktOaGkyM294VjB6SjNqUDBQSmJ4aDlCR1dzcGNKOEdzVHRld2dlTjYwK2JLdWl1YVJRd1BTVUpRVTA3alB0YlpZcGhwWWtLZUd1aFlyWUVuTDZ0eVM2bmlkT0JMaUl6dTE1bnZMSjFDdTZ6aDhiTlZpdnYwQXd5YTNuZ3V1djRuclJQT0JFUXIvOC9zK1VsWnN5aExaYXJQK0hOc1RIdnB6ZnhFZTd2RUZiWGdKeWdPS09qYndVQUN2VkMvQVRmVTZibENxQUM5WFRRT1JzQXNZR1BvVWxSUmFKZ3hzQmN1Q0Q4bURxZm1ZWG8zMWRTOFdJSEZRcWtDT0h6cmtZdEtiaTBqdkQ1RzJSQjVkNzd3M0EyZG9yV1RLZE5TSW40aTZacms3K3hTUmp2Z2o1YVpjRDBSVStZVnNjTzBvekJwOGQwRzc4Sjk2UHdQR3YrTGlIQWc4VVI5QjFQWXI0K1QwMkZXWERSTkhqem9Va0thN2MiLCJtYWMiOiJmNWYxMTBmNDNhN2ZjOWUyMjcyMmMwMTJhMGIwNDA3Njc3M2ZmMWEzNTM0Njg1MTEyMWI2ZGJlYTFlNTAzZjViIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdiQnNMbThTS3d2RS9CeEtPTnpyNWc9PSIsInZhbHVlIjoicVphZnlReWF4aFJzV1JNaEVsTEJXemtJblc0QndMLy9mYjVLNUs5VGlON2w3NEtmeTAvSnRZK1BXeW5vRzgzQlJYUlIvQ1RmeklxZ1BoTDByRWMvejZNMEhTTjI1MGgvTCtmSnRJbkNpNFZZUW4vVHVmMFU2TTJWanREZ0YvQUpqaTEwdUVtRm9HR25rNERsZU5sZjF2U0VQaHc2MHZCOVRrUnJaem9SSnk4aVlncnJXRnBWZVcvTSs3V1RnWnlaNTN3cHNLay9GRDNicExuUGNleldzT3Z2eFFoTlFoR0g4cTRkYytzcGdqTmplM3k4YlVvUW5xWDY2RDdYRDdMT21qQi81UUVaWkE2UW1SRHltRE8yNjNpWUhFVVcwdTZWc21qRWpFWGdMeWlReW10K1JjMUZEb1Z0OGRzcmNCWnd0Z1ZSckhKaks4cUJwOTVEZ2FkTWRUSCsxRGVLZWNNczcrelBCSzdYa0ZKdVhqMnp1ZlU3dVljN1oyNUtlQ28zK0dlUFZsT0crdzBLU1dGRTIrbnEwUitUeDRORmFkRmErdXpLd20wM1E4U0g1enlHUEdCUjgwdlgvRVRQNWQ0TXk1by9iM0xXR1VQTmpLSG5pSVJqR2lLbVA1NkZFNm5NUEZzL3JCZXJTaFBza1JNT3pZQXhUNUNoL0g0eUtsSnciLCJtYWMiOiI1ODdkZWE0ZDlkZGM4MjM1NzQ4Mzk4Y2ZlZjNiODFjNGY0YWNjNjE0ZWQ1MDkzYjkxYmQ0M2E1N2FmZTk1ZjM1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-681705626\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1826021699 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1826021699\", {\"maxDepth\":0})</script>\n"}}