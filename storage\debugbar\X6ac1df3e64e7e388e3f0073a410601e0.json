{"__meta": {"id": "X6ac1df3e64e7e388e3f0073a410601e0", "datetime": "2025-06-27 02:12:22", "utime": **********.741593, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.315635, "end": **********.74161, "duration": 0.42597508430480957, "duration_str": "426ms", "measures": [{"label": "Booting", "start": **********.315635, "relative_start": 0, "end": **********.684254, "relative_end": **********.684254, "duration": 0.3686189651489258, "duration_str": "369ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.684263, "relative_start": 0.36862802505493164, "end": **********.741611, "relative_end": 9.5367431640625e-07, "duration": 0.057348012924194336, "duration_str": "57.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722120, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00268, "accumulated_duration_str": "2.68ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.712693, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 73.507}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.722188, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 73.507, "width_percent": 14.552}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7273111, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.06, "width_percent": 11.94}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-541409968 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-541409968\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1321446701 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1321446701\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-57844077 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57844077\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1285663562 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990338154%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InZKOC9pSXZqV3VrZlVSMnJaeTlpdGc9PSIsInZhbHVlIjoiZ3FQNWQzN3lqdWFUWkxWMHV3L1BkbG1QNTIvczZIbmhZRmUyUmFENTZTOGdFTm5YcEE1dkJRMmQwbTNtcEpFQkN1TUdDMmR1dlRJK3k4dnN4RHg3ZVZEdmpGSmNSbXFuWWFkZUhvN01Bb2pNWGNhSDdSZ0ZQWmlzeWxLazh2SGNLcHFYajNTdU5PekRRcDY5TjB4SEhSc0x5SEQvVDJsK2FveWlHVFRtMjkvUHovWVEyVUZoMXRxNm5RS0JDMTJBOThrOFFVbjd3R2tabXBZYWRCUWlRSXZRRmtZTTNSL3NFZjl3MEhDTUhTNlJXUjZyZlBxZW80MDRwbndBWmltUlV6dEhwUlhwRjRhQTFEaFA2ZXczdG9idnVoV0I1OHlkc2RUczIvWFo5U05WTi8xNDMrS2RRZXFjNmltQ05Ydm5TQkNjUmVUKy9ZZTJoR2JTOU1JaWJoZi8xaysvQXpEM1krb0lwclJsTXY3cHJJbFZuc0k1b2VZYVIrTGJ1WENBUzJ5ZGpWdVI4bVhDMDdPRU1yMi9UOGorQXgvS3FxL1Y2cGdCc3JETk9hVDNCc1QxUTZTdUtnbE5NZkxnSmpmVUVuS1hsbENlUHNGOXIyczZLWnRtc2NqL2poU0VpUHJReGNEeG40NThsdHJmZnUyUWJuemNaU1VlRU8xUFd3akkiLCJtYWMiOiJkN2M3YmE4MmUyZjg3MjA0MjU4M2EyZDM4MDkyMjVkYWI5ODBlNTZlNmM1YjFmYzI5MTQ1NzM5YzcwYjQ4ODYzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IldtYzFMUkZNMUFsb2FiSFVuWTFxK3c9PSIsInZhbHVlIjoibWUwUldDYVF4ZktEUTgwSnFTSFU2aWh5M1kyUHFmQ2VlbmtFZGtWQjRGZVorRVQ4cVBoRVNsQjlGSjY0bWt5RlBWa1I2WndZa2wrOFl4OTdtOVdZT2tiTjJrWjBtNWF2ZVpNa2lPUzJoSUNYSWNGdkVlVkxqVjF0MUhqUWpsb1UzN0tzZVZ0cjNXcEV6TlBmY2RFZkFwVXdYbWN6Nk1ONGNiTm5ENU9jak5aYmI3MnZVa1ZjNnlVUzlvV0huUzYwMWR0RWZaVWpKV0x6L0JNU082RGxwakUyZzRjMFdOVGkvbmFWYUZSVHU3dkZwUTFrSndHQkc4QVFPcERGMXI0YXBTem5sVnR1U0pIeDRSWW5laWJsNi9wUnF0Tk8wNDFRczA2OWxva1VsM3NWTWdUbHAyNE1RYWs4QW44SXZ1dlN5RWxydzRVNXkvQXlidjQ5d1F0cnhGcFg1dGRGOGhtUFNlYTN1M3JQa2ZDVnhXL0tEOHlxWnI1OFUyOVB2NUZwdDgySDljeGYrWWFvaWhGVnEyT1VOMlJPUEoyT3VaZU9kNmdlRGVSS0FkQ2xhOTkzODUwODltYiswUFNMQ2llZy9GYkxhQVI1dWNXY0NvK3Q2Nk02VXpDT1JkZzdXS3hUamVCdUd3TEVoRWJFb1pNemdpOE02eE1DU2dXNTF6bm4iLCJtYWMiOiIxYTFhMjNiMGE2ZmE5ODMzNjkyODZjNjljZjI1N2QyZmYwOWEwZGM3YmZiNzkwNmFkZTQ5NWI0MmNlNTEzMzA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1285663562\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1836397663 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836397663\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-848389789 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVidUxJeHpTTXpzSmtra2dRLyt6a1E9PSIsInZhbHVlIjoiSVp2ck94R3ZGSXc2ZDVNT0JTekFvOTJTSkZxaWx5cmFqTjhRVW55cGhLKyt2S3Z2RGkzUVhwNUIvZHAyNmFseExnZWNlcC90V0FhcDNldlRINHJxMUhrSGtTeWdLZ2FFSUE5c3lQQjM0K1ZqY2hYUFpyNk5OMG9yT3Axd0VPdXhac2VDV1ZONldSUVlkRnpXR2pCNU1wTFJnczhISjZNRkdlb2NWdUxCeHROZ2VoemxId1lSQzcvS01FY2R6a3hQakpNUTF6a01oZm1JMXI5RDl0NjRWR3hVUkg5b2NaTEM5TlF4YnZCYmE2NitVZ1dKSWFsbFFoRUNMT3NhNHc0T1kvOHpvVEJwTTI1OG9JVXd0TEo5YWRsUjN1dFd2UlU3QzM4U0ZUdDdDWWN6S0dFci9YMnZSN2NNanlzQTBCZm5xOTYwVDV4RnVJWmtocEJGQkNRWHdGV05aVzdySS96QUFTOVpqNklSLzV2TUJocCtOeFlPUkRJVkcyeHNadDJPSWJabDMrT2RBeFhBK1FDeWxLS3VDYWFPbkdjTjZsMlpiMnRUTmFEL21jd3VWaHhXOTVpRXRUNnJIRmdWOGJ1MWoxRHNTN25yZWtISE9pVExYSUNUK0N2Wk82UXZUMUZCTi9Fd09DdnFMVmFFWVRaczUxY2VyRlg0VDViR2VHMloiLCJtYWMiOiI5YmZmMDBkMjU2MjZiNTYxYjkxZWIxMjY5NzAyYzQyZGNkMzFkMGRkNWFkZWI0YWU0NTMwNjlmMWQwZmQyNmUyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:22 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii85akxQQzQrallydVBRZlRrTjY0cnc9PSIsInZhbHVlIjoiZUZzMlFoSC8weXBEczI0UGgveVVxN1ZtZzEwY0NBN3BCQ1FzY0Q5Q0JBUTBadFdmUGRlaGJ4aW5HMTBTU1V0blRXQ1dlSmllNERmRS8xV3A0WDNrNm9YRWJidUdZZktNSjJmaElQMGY1ck1ZbUo0NnBiOFk0dUpoQ1FLWFQzdS9qNk1xRnBvSG13L1dOMU9BY2FZUHNEVXdYcVp1YXRRTUJlR2xCM0N3MFZkVU80MnZHTGxCL3EzY3c5ZVFENzVjelpSdVpCcVJWNmZ0RW96OHU3NHl5ZmhpN3NvSy9rQkhCY2pZMGRRbkZrTTVjbE9DTU1nZm5EQlRmNVdMVEpWNWYwYTRXM2x4ZVdWMnVhUDZ4ZWJlcVlpRk1DVHVPelVWYmIwakxiUFFscTlHODBWSzdHa0dZRk9HOTdFT1doemg1eWlibmdVS1ZRU3FCUGZuVW9BRGN6MTVNNXk4c1F2UVM2UzdTbTRDLzNuTVRiTTBsU1hZRUQzbDdEOEUrWTRsVW8yL21sVi9rL085NWx5MDhtUUd0bWVCQS9hVEpnV0VYeUtCcjBQeUFUWEIrakN1Z3JWS2Y4d21sc0hVcTcxaEowb2g1Z1BFVUZXeTlRTHU4dHJLSlV5SllVRVBVNWV5REJiMXJlQmVXcDFuTDhxbFpDZTJ6Wm5JOFhYY3NKcWMiLCJtYWMiOiI1Y2IzMzM1Zjc0NjFkMTYwZTA4NjM4MDNmMjY1YjRhMTM4YTAyYWFjODBkMGE0ZTg2ZTI0NmRlOGJkZDU2MmIzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:22 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVidUxJeHpTTXpzSmtra2dRLyt6a1E9PSIsInZhbHVlIjoiSVp2ck94R3ZGSXc2ZDVNT0JTekFvOTJTSkZxaWx5cmFqTjhRVW55cGhLKyt2S3Z2RGkzUVhwNUIvZHAyNmFseExnZWNlcC90V0FhcDNldlRINHJxMUhrSGtTeWdLZ2FFSUE5c3lQQjM0K1ZqY2hYUFpyNk5OMG9yT3Axd0VPdXhac2VDV1ZONldSUVlkRnpXR2pCNU1wTFJnczhISjZNRkdlb2NWdUxCeHROZ2VoemxId1lSQzcvS01FY2R6a3hQakpNUTF6a01oZm1JMXI5RDl0NjRWR3hVUkg5b2NaTEM5TlF4YnZCYmE2NitVZ1dKSWFsbFFoRUNMT3NhNHc0T1kvOHpvVEJwTTI1OG9JVXd0TEo5YWRsUjN1dFd2UlU3QzM4U0ZUdDdDWWN6S0dFci9YMnZSN2NNanlzQTBCZm5xOTYwVDV4RnVJWmtocEJGQkNRWHdGV05aVzdySS96QUFTOVpqNklSLzV2TUJocCtOeFlPUkRJVkcyeHNadDJPSWJabDMrT2RBeFhBK1FDeWxLS3VDYWFPbkdjTjZsMlpiMnRUTmFEL21jd3VWaHhXOTVpRXRUNnJIRmdWOGJ1MWoxRHNTN25yZWtISE9pVExYSUNUK0N2Wk82UXZUMUZCTi9Fd09DdnFMVmFFWVRaczUxY2VyRlg0VDViR2VHMloiLCJtYWMiOiI5YmZmMDBkMjU2MjZiNTYxYjkxZWIxMjY5NzAyYzQyZGNkMzFkMGRkNWFkZWI0YWU0NTMwNjlmMWQwZmQyNmUyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:22 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii85akxQQzQrallydVBRZlRrTjY0cnc9PSIsInZhbHVlIjoiZUZzMlFoSC8weXBEczI0UGgveVVxN1ZtZzEwY0NBN3BCQ1FzY0Q5Q0JBUTBadFdmUGRlaGJ4aW5HMTBTU1V0blRXQ1dlSmllNERmRS8xV3A0WDNrNm9YRWJidUdZZktNSjJmaElQMGY1ck1ZbUo0NnBiOFk0dUpoQ1FLWFQzdS9qNk1xRnBvSG13L1dOMU9BY2FZUHNEVXdYcVp1YXRRTUJlR2xCM0N3MFZkVU80MnZHTGxCL3EzY3c5ZVFENzVjelpSdVpCcVJWNmZ0RW96OHU3NHl5ZmhpN3NvSy9rQkhCY2pZMGRRbkZrTTVjbE9DTU1nZm5EQlRmNVdMVEpWNWYwYTRXM2x4ZVdWMnVhUDZ4ZWJlcVlpRk1DVHVPelVWYmIwakxiUFFscTlHODBWSzdHa0dZRk9HOTdFT1doemg1eWlibmdVS1ZRU3FCUGZuVW9BRGN6MTVNNXk4c1F2UVM2UzdTbTRDLzNuTVRiTTBsU1hZRUQzbDdEOEUrWTRsVW8yL21sVi9rL085NWx5MDhtUUd0bWVCQS9hVEpnV0VYeUtCcjBQeUFUWEIrakN1Z3JWS2Y4d21sc0hVcTcxaEowb2g1Z1BFVUZXeTlRTHU4dHJLSlV5SllVRVBVNWV5REJiMXJlQmVXcDFuTDhxbFpDZTJ6Wm5JOFhYY3NKcWMiLCJtYWMiOiI1Y2IzMzM1Zjc0NjFkMTYwZTA4NjM4MDNmMjY1YjRhMTM4YTAyYWFjODBkMGE0ZTg2ZTI0NmRlOGJkZDU2MmIzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:22 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-848389789\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1986713343 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1986713343\", {\"maxDepth\":0})</script>\n"}}