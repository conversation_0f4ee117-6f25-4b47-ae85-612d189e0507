{"__meta": {"id": "X4942e1d745df3b8cbb2d2c9c9b483eaf", "datetime": "2025-06-27 02:25:58", "utime": **********.255104, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991157.838979, "end": **********.255119, "duration": 0.416140079498291, "duration_str": "416ms", "measures": [{"label": "Booting", "start": 1750991157.838979, "relative_start": 0, "end": **********.204862, "relative_end": **********.204862, "duration": 0.36588311195373535, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.204869, "relative_start": 0.3658900260925293, "end": **********.255122, "relative_end": 2.86102294921875e-06, "duration": 0.05025291442871094, "duration_str": "50.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45735016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00216, "accumulated_duration_str": "2.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.231192, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.815}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.2409291, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.815, "width_percent": 22.222}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.246341, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.037, "width_percent": 12.963}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1449989117 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1449989117\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-49160970 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-49160970\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-412169207 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412169207\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-319323511 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991144534%7C28%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJCTEtYdzNKcTBtZUx6cG9jaUFTd3c9PSIsInZhbHVlIjoiK1c3NmRJVHBsUnpiNmhiWEpsQmZEb2FvRDlFaDFla1pPaElRdkw0TTZlZEZTV0NhSUlFNU44Y3l3eGVqZTA2cytibUxkVWlEaDBQY3VrZ1hwZDZlT1UzMHd2ZHlxVGhrWnFUODBqN215ajRDWVdwVzUrWHB3NllzZnVXN1RldnoyZDRKYjJ4Q0wvaUFseG9tdnhGeFRCUnVtcWJ0WVpibWFCZFU1V0Z4MVhla3ZEVStUdllWV2hqbHNVREl6QTYvS2liOVloUnhIcllRa1p0ZkF6Snh5eWMrd1YvNXM0SUFoTk5PdnBtNEN0RS9zejNEUzJ0RWh3alBTTmx2b3Q1SUZaQkd2RHREcmtycisxU2N5eERxOXNGMlFqaGt3TFBEMzBwUlcxZDY1STN1ZllGWE5QTHlINmtON05PalNJVWRUbDZ5azdnUHdGNXROVm4xdFBSS2tINlJHcXBFa2VENDArV0FmbnZLalpNQThrc2JOQ3JnWnN6T2lqK1hDKzE4NS9Vc2pFTkVFenlxUW1pa0p0VEJoQmJteEhKY3p3VFdSREYzYXRUU0lQbWNLaWc2T3BKNURrc29BMzdLOThKVHI1OUk0ZG5POEFudEFwdmhJN3k3Vm5UQ1ZTbE9DNVFOdldwTEZoY3hMQzFPRjNESDNuWldhN01NejhMOUp5RDUiLCJtYWMiOiJmMzE0ZGJhYjVkNzVhZmE3MDAyN2JkNzQ2NGEyZmM5OWJhZWRmMWE4MjNlY2FmMDRlNjA3ZjNlY2M0ZWY2NWRjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjkxRFB1U0RXK0UwZ2NKSERveVhpa3c9PSIsInZhbHVlIjoieHBlRXFPUTVFT0w3OW9ISlErTTFYM0FkamhialJpY212WmpWS2dSTnhVdVpKU2JBVklrakFaaXkzMzZPTjd4bTB6V1FzNWJDQzkxSHV6ZTNaNjRXblQrVjhyb29Ob2NFVVl1bWlUMjRHcE1sOVpvQi9yMEVpY3B0NlNob3BRZVJtOHRtbVBNQnNtdUd2K1paMUFYdDZkYjkyZnZBZnViZlBxenJzOFpNNTRJSzRYQlhWUFFyQk10MWdhZ3pLc2l0Vms1L25rZzVBTVFwK1UwbVRSd3pXaFQ0WVVBcVJkekR5WTVuYWk4TDU1NE1aNTdTWjdKZVJlVmVaZE9pT1hCeFJFTUZMWmo4MDBrUWhXd0ZUdE5SNy9PNXBnTlBxNUJoQmRGblhLY3RkaFRlRzBRSG1xd1pxbFNQTEszWHhUYyt1Y0thK25EbDR0aXFUNHQvZTFrQmZ3RHJwQ1I2Rkh0LzF0ZmkvMGt3eXZNOXJlUnhCRFd3eWR3elhzd1lWVEpBVkxlc3dOZWJZd0xuR1hiYVBRZERsRDNjbjZSM2k2UFlmVEh4SmdVRWZOZlNtQ1R4Q21MYjdJeHRxYlRoaGF2NjduSmRqd0F4M0I4TjNlY3lyRWxyMnNVRmpXVFg0ZUNyVkdXTUhvNU5pWHFyeUdDbTAvVi9ZWjZIb2dTd2dpSTciLCJtYWMiOiJlMjEzMGJiM2Q5NTkzMjg2MzM2NTRjMTk5NjA3YzY5NjRkNGFhNjIyOGRkNTE5NWE2MDEwZDJkODcxZjQyNzIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319323511\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1301567369 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1301567369\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1078086880 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im9QL1VFLzBHTEFvKzBtWFQyV3V2c3c9PSIsInZhbHVlIjoiL05PRUdDMThaaWR2azBJcGJjcjJWdnNPMjZoRVAwQjF6TmEvNUorZVF5VnB6czIwVDBZL3RzNXRzeVdkWUlHMVV2MUVHRWFJUE9xRGJrOXlzUTFnLzlhN0N2K2ZTQXBnc3BjM0Y3eFhKbnlyQll6NSsvMTlPYi8yaVhmMTRPbDB1ZUNrS3Z6T01jTjhmOEppcTZWQmVLYkFtME9BTlNWMGtWdU1zZDNCZGc2VUpvRXlocUhxSG8xLzJEa0tNMXlzRElGMGtvdGxXMmV3cUNkSVJjWFltV3NmVlp2dmxzbUtRUU5HNzF1RUxXMnhla0g3UTcyb2tRenJ6K3dRZFg2cUc5T3ArWkpJM2dTUWVJOVFucjZjWmtSTy8zbkJhdVI0Q0ZxaG54TmU0WVVBVUQ2aEN3SEJadHFWQXFISTdNSnRmdUNFY3FnZkVUa251QTh4U1dlaVF1N0c3R1RPTSt4VkpUWnZNc2Jlc05pcDBhQ3FxaXhRS3daSjg3SmJzTnFkSDNDempwZ1ZCd25kUzdUbkZLWUNUa214VlcxbjFkNzF2c1lvYkdhTVBmOFJKQ2FLQS9SdmV2dUVVQ2FZS214OU5jWG9haGlxUUNtcTRMaEFKZ1JFTzNScGFMVlJBNGtoZ1dubU5NSGo2RldIRzNvWFRwSTlLSE1oR24yTEZOZGkiLCJtYWMiOiJkZmYzYTM4YzM1MzYxNzFhNTAwOTA1MDU0ZGM1Y2M1ZmZjNmExZTlmMGVlY2ZkNDFmNWI2MzFhNGYxNWNhMGRmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjJzYVdzVTFQTjJPSkp2QXdJayt2SFE9PSIsInZhbHVlIjoiek1wNlJ3L0dldXNxSm5QSk13SzNoSXo0OHA5MDI0SHY0R0JuMit5MFA5aitMRWJJUUk0ZkVsN3Rpa3hZdkgwdXNUM1ZBc3NUZk9yYkhwNjJsZ0R5bWVEeUJoemZIalRQdWxQN0VsSXY4cm9DKzVhblJqc1lCcG02dS9sZUVRd01sazluR0lyeE9RelFLNTF6TGNGMGhyMTU2QjQrODYxWjVOR2JYM0lFK3cwY2JSMlhIaW1LVW1UWG5JY3NSbVRjVXRsazRTSUx5T1ZzeWwyenpyQWc3VmtuSWtCemMyS1djekFOM1NKSG9QUElUZzQ0d2c0U0RaLzhhQWkwaUlOVmlLL3Q3YXNWdFlZa21aZVkwUVprNkE0MHRzWHNQMXhMR29Mb0tKM1lvK1RsVXFlVEhySTg1WlBrd09qcjFrbTFXdDVUbmxKLzdkKzM5bU5jS2dUa2Q3TUZ2Q0VMRG0yZ2UvakhvMmJNUnlOODNRNkRNeTI4VTZTQWFCcnIvVkgzQXkxNFQvSms3MjBrclJPV1BRN2NYRXkxQXNobkZ6bW9UVGpnZ21zYVB4VEg0a05yRDZpeGZOeEFUUzEwVzE3bFN3Rk01Rk8yRmZhUVNwRWxmSXVHZ1dRbEE1Z3QrMTU2OThicndXSDF4Q2QxYk9MdTlDTW1XWjRSUFEzRlA0V1UiLCJtYWMiOiJlZjFhZjI2ZWMxNTllMGExZjkxN2EyMmMwMDY0ZDk5OWQyNDJhNzMwMTBmM2Q1MjJmYzhiY2ZmYjE2NzljZWY5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im9QL1VFLzBHTEFvKzBtWFQyV3V2c3c9PSIsInZhbHVlIjoiL05PRUdDMThaaWR2azBJcGJjcjJWdnNPMjZoRVAwQjF6TmEvNUorZVF5VnB6czIwVDBZL3RzNXRzeVdkWUlHMVV2MUVHRWFJUE9xRGJrOXlzUTFnLzlhN0N2K2ZTQXBnc3BjM0Y3eFhKbnlyQll6NSsvMTlPYi8yaVhmMTRPbDB1ZUNrS3Z6T01jTjhmOEppcTZWQmVLYkFtME9BTlNWMGtWdU1zZDNCZGc2VUpvRXlocUhxSG8xLzJEa0tNMXlzRElGMGtvdGxXMmV3cUNkSVJjWFltV3NmVlp2dmxzbUtRUU5HNzF1RUxXMnhla0g3UTcyb2tRenJ6K3dRZFg2cUc5T3ArWkpJM2dTUWVJOVFucjZjWmtSTy8zbkJhdVI0Q0ZxaG54TmU0WVVBVUQ2aEN3SEJadHFWQXFISTdNSnRmdUNFY3FnZkVUa251QTh4U1dlaVF1N0c3R1RPTSt4VkpUWnZNc2Jlc05pcDBhQ3FxaXhRS3daSjg3SmJzTnFkSDNDempwZ1ZCd25kUzdUbkZLWUNUa214VlcxbjFkNzF2c1lvYkdhTVBmOFJKQ2FLQS9SdmV2dUVVQ2FZS214OU5jWG9haGlxUUNtcTRMaEFKZ1JFTzNScGFMVlJBNGtoZ1dubU5NSGo2RldIRzNvWFRwSTlLSE1oR24yTEZOZGkiLCJtYWMiOiJkZmYzYTM4YzM1MzYxNzFhNTAwOTA1MDU0ZGM1Y2M1ZmZjNmExZTlmMGVlY2ZkNDFmNWI2MzFhNGYxNWNhMGRmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjJzYVdzVTFQTjJPSkp2QXdJayt2SFE9PSIsInZhbHVlIjoiek1wNlJ3L0dldXNxSm5QSk13SzNoSXo0OHA5MDI0SHY0R0JuMit5MFA5aitMRWJJUUk0ZkVsN3Rpa3hZdkgwdXNUM1ZBc3NUZk9yYkhwNjJsZ0R5bWVEeUJoemZIalRQdWxQN0VsSXY4cm9DKzVhblJqc1lCcG02dS9sZUVRd01sazluR0lyeE9RelFLNTF6TGNGMGhyMTU2QjQrODYxWjVOR2JYM0lFK3cwY2JSMlhIaW1LVW1UWG5JY3NSbVRjVXRsazRTSUx5T1ZzeWwyenpyQWc3VmtuSWtCemMyS1djekFOM1NKSG9QUElUZzQ0d2c0U0RaLzhhQWkwaUlOVmlLL3Q3YXNWdFlZa21aZVkwUVprNkE0MHRzWHNQMXhMR29Mb0tKM1lvK1RsVXFlVEhySTg1WlBrd09qcjFrbTFXdDVUbmxKLzdkKzM5bU5jS2dUa2Q3TUZ2Q0VMRG0yZ2UvakhvMmJNUnlOODNRNkRNeTI4VTZTQWFCcnIvVkgzQXkxNFQvSms3MjBrclJPV1BRN2NYRXkxQXNobkZ6bW9UVGpnZ21zYVB4VEg0a05yRDZpeGZOeEFUUzEwVzE3bFN3Rk01Rk8yRmZhUVNwRWxmSXVHZ1dRbEE1Z3QrMTU2OThicndXSDF4Q2QxYk9MdTlDTW1XWjRSUFEzRlA0V1UiLCJtYWMiOiJlZjFhZjI2ZWMxNTllMGExZjkxN2EyMmMwMDY0ZDk5OWQyNDJhNzMwMTBmM2Q1MjJmYzhiY2ZmYjE2NzljZWY5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078086880\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1094540112 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1094540112\", {\"maxDepth\":0})</script>\n"}}