{"__meta": {"id": "Xdc0f848c272e2f24172fec19572741e3", "datetime": "2025-06-27 00:22:57", "utime": **********.955311, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.517125, "end": **********.955326, "duration": 0.4382011890411377, "duration_str": "438ms", "measures": [{"label": "Booting", "start": **********.517125, "relative_start": 0, "end": **********.882608, "relative_end": **********.882608, "duration": 0.36548304557800293, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.882619, "relative_start": 0.3654940128326416, "end": **********.955327, "relative_end": 9.5367431640625e-07, "duration": 0.0727081298828125, "duration_str": "72.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45041792, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02376, "accumulated_duration_str": "23.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.911342, "duration": 0.02291, "duration_str": "22.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.423}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.942136, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.423, "width_percent": 1.557}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9486032, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.98, "width_percent": 2.02}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IjB3aUYxS0ZxQU1LWXVzS2R4VU1rMlE9PSIsInZhbHVlIjoiV2lsL1lPQkxnYXFIa3FvQ0ZLNUk2QT09IiwibWFjIjoiNmFhOWU4ZDM1ZmZhMDRhMmZjN2Q5ZDNiMmFiMGJkNTUyNzQwNDFmNGE0OWVhYzIxODFhZWVmYjljYWJjYWYyYiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-537480379 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-537480379\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1578063940 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1578063940\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-656376656 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656376656\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-889274934 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IjB3aUYxS0ZxQU1LWXVzS2R4VU1rMlE9PSIsInZhbHVlIjoiV2lsL1lPQkxnYXFIa3FvQ0ZLNUk2QT09IiwibWFjIjoiNmFhOWU4ZDM1ZmZhMDRhMmZjN2Q5ZDNiMmFiMGJkNTUyNzQwNDFmNGE0OWVhYzIxODFhZWVmYjljYWJjYWYyYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983511681%7C51%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJxdGdsNmZaN1ZyVE1nclYwUjlmNUE9PSIsInZhbHVlIjoiL25YS3hNZ3ZISVo3UlE1ZWVaODBtc25uSVR2OFVRdHM0Q29JUWlnaHBMTTU3YWJmdXpoMHBWd1FDRElzYVN0aEtURm1pemlYWUVvYUsrN2xlMHpqOWtLQmMxdzlxZUZqVzl3QitnRnQxK25KODNBenBVL0pDK3g0RWE4bUNGVUVXeEJNcUVmTlEyV1ZiaWd4RXZjOHdqTzFFajZuY29DM0t5SU51dVVna3g4YTNpTW1uRFRJbTRMbjA3WVhOZ0ZMeUpLM21rak96RS9EV0ZFWG44d1M5TmtlM256THdMaXpheVRXNlRjNGxDRWQ1UWhnQjVNT2FTSVBsZkt2WmZIbnErV2J0T3E1eEFMUDhuUkN0NEQ0dzBNRTljTlo0VVRiOFRScXlrYzJrTXk5QklKdExGNDRmbUJmTHpDNmp1YThuZGpTdDV3dTdXUlVxUE8wcVYzUGRCTkpUVG8zZjdqQXlaV2dIVnJhc1NjVllUQ1g5cGdxYk84LzFoMnU1YUl4RG1rOXVMbVczQndVWCthcmIvR2FyNXNwR3BtbUtDR09LME9tZ2N4aFFJNVlLeWN3MzNzOWFOYjI0bWVRZWFHbTM4SnU3YS94WEowNmFMaUNTM0J6UGFQUmZwRkViOEtFdmRZcG9xd2E4NzRaeldJMGY0RHU0RG9hQmJOZ1FrTTgiLCJtYWMiOiJmMTc3ODZhMTIzNmYzMTVjYzg0ZTE0Mjk2NmJkM2QyNTI0ZjdmZjQ0YjJiYjQxYjA3NGJlMWE0NTUyNjU5MjM1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZzMmY0eWFnSzFaeHMyQlVtd1RWZVE9PSIsInZhbHVlIjoieHpPQlM3WFFPeEN2MWd6eTVKNHQwM2NXY2dUT1J0UHkxQkhXWERrek1xblRmT0FXSlUyTFZ3dCt2YmhtZ1lJenBYSzB2Y0NJMUpqbzRmei9sVTRCNUREZWpHVUIvdkt4Mk5TTWdvaExaYWljM0c4dXFLS1NGTmYwT29CNGpMV1p5bjhRViticWNWNExQMzVFZEg0MTd6OGMxRnE2d0RJa2Y4aTlLeExSLyttdGtHMG9LWjgrYXFXeU5TTUVBMW5mRFlXd0VWMzNpejh0Qzh0dW02NFNxVXE0dkVCVFpCNUNUUmJZZWdURHh2Z0tqbUo5S3RyZy8yeGhsVWhzbWZzL3FWbHhpckU1TnB4dUFNV01rb3F0WFNkVisvMWJ2bEFKVnVCTWNOTkVqZTlteVpnQW1tWGZLVXYxNGpHa0RQUzZPcWtNRzVrcGpHZHpBMXY4Y2szM0pKUi9DclBhS2ZQQ1BWVHN1MlJDVUUyUU5mQ3FoQ1hmNUplVGRXRHFUN3ArRm5CNFM0R25yUk9yUUFJY2EvczViaFpaWCs2Mm1HL21YVHRRTVp3UFdrUVpEWVduVklQVUs3QWhJUWhjYWIxM2N2aTJGTUErTHgvMXJFQW5tUGZkaTNSalpjd05uUVFndkVqanZQT01JWUF4VGRXNVVOdk8zTERCNmdFVUE3UjYiLCJtYWMiOiI3MjBmMmQyZWUyODI2M2UwYTI0NWU2ZjlhMTU4MDY1MjI1ZGI4YWI4YTEwYmQyYWM3MTFiYzg5ODA2YmE0NmY3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-889274934\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-232016575 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-232016575\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-517794165 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:22:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InpYZTBWaFNuaitLT3hxcDV3WDZraUE9PSIsInZhbHVlIjoiaVRjci9scjJaNmgwbnBNVHp5UVVTUCtYRzFWUmMxaWVWTlpyek1GMFlrd1JteE1aTER0U1dncE12bHlFb1JMSlc4SnExcUJCbmp3UkwwZmt3RUFPYXY2OC9rUkw0ZTl4d2FvYnIzK1oybDZBTVJGaWJmM0U0RWptYkxlUjVibGF4MFFsM1pSc1hnQnhPb0FNNDVScTlhY01pS3hPK1NxaDJZdk9JSVk1Y0JnWnliK052Vzkxd3Z2bGlZNkdjcVR1eHU4dGxKL3dieGV1cmRMeUdtM2FIWjBsMDUyeTZsK1FOK1VDU1A0S0I3d2EwRGlOZGU3R2tHc1p0UzhzTmVubXU5RHdXQ08xUUtSUHNDaFVhRWxzcTR0OTZTK3liYTY5Z1UrM3RQamdQSTNmUUdIRWdyWFBtODRBeWZiV1RJM05wbG40WHJkOFk4QS9RRlpxS1UyeHlrSDUyRkdmNnIzVWNTOTdhb21zUFd1bjRFdzBxaDZSMDhnRVEwRWhhTUdrayt6VWZHZ0FqNFJDSmV5WVlQYmxtUDVaaTBQamxvQ0E5anF2U3ZlaHo2dEZ5bmhNd1ZMMUxEWWJwaXo0MlBoTjNQaCtHWlkzZ2NQT2JNRDZPMmlKVU9HRlVERStycmhEdnNDbndVUWtTS1NCbDVQSnpiOFRQZ3NWSitsRTQ3N2QiLCJtYWMiOiI4MTc1OWQ2ZDIzNjQxYzUyMzVmYzJiYjY2Yzk3ZTgzYmIwOTU2YmQ2NDI5ODQ3MjY2YTg1NGNkOGY1ZGNiM2Q1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:22:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjZVSGhJcTNzcVNVOFZ1MngzaHJLb1E9PSIsInZhbHVlIjoiaEI3L25FZUQyT25ra1Z1R20vY29sY3IvOUxocW5CdUNTa0tBLzZGVzVDNzNCZnNpc1hsMjVlOTZNcnZNZ1JwVHFBUDhLd3NpUjd6cEtqU3BUOU5LUmJwUXo2d2ttRXd5ZVhEZ2xzZXI5WHdxQlM2SjJuT2VxWmJmQWFKZkJvTG11dTZsVkg2QzFwenZPelNUek5vZTFadmh4Mi9ieU82a3Q1SnRjVHZCQUZnUHEreG02bE44My9DVW8yRDJGR092OFRKZExwYmMzenQ2bnlDZXU2MmYycTY1RjZ0ZmZJbVUzYUp6dHExa3NxdkVkeUUxY0RxZXlsYk51SENJWGJTTnNyVlpvQnZFMDV2YkNZcVUwSTNGZER2eElJUVY3eVRhb2E4QXhjVjRaaW5Ha05tNmF6aktrdjg0QkxHSHFRUUtpbE1ucXJmZVI2UnR6NElWVjB0MTVSWkNBNTVET0JqdmRnMWVTYmtVUDZNU3dUeWFWZFNJaUJTa2dqVVNFcmx6Q2ZsbjRJTXJGV2tLb1BpeFJKRXV2OGtLRGdtUnpQSEYrN3grM3lnRStaMW1MUjZNQTIvYnRQZXN0SXRPUnBMNHAwaitwTGMzV2FsNm5heUxDc3RsSU9sMURlQWFCdkpKYk1iMW9VVjFEdFJaQnU1Ni85eDNYb0RUQmt0SW5OVW0iLCJtYWMiOiIxMjgyZWUwNTk4ODk3MDBkNTc5ODg2ZjQ2Y2VkOTVjNzYwNzJkZjg3ZDhjNjU4YzA4MjA3MmNiOGQ4Njc0NjdmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:22:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InpYZTBWaFNuaitLT3hxcDV3WDZraUE9PSIsInZhbHVlIjoiaVRjci9scjJaNmgwbnBNVHp5UVVTUCtYRzFWUmMxaWVWTlpyek1GMFlrd1JteE1aTER0U1dncE12bHlFb1JMSlc4SnExcUJCbmp3UkwwZmt3RUFPYXY2OC9rUkw0ZTl4d2FvYnIzK1oybDZBTVJGaWJmM0U0RWptYkxlUjVibGF4MFFsM1pSc1hnQnhPb0FNNDVScTlhY01pS3hPK1NxaDJZdk9JSVk1Y0JnWnliK052Vzkxd3Z2bGlZNkdjcVR1eHU4dGxKL3dieGV1cmRMeUdtM2FIWjBsMDUyeTZsK1FOK1VDU1A0S0I3d2EwRGlOZGU3R2tHc1p0UzhzTmVubXU5RHdXQ08xUUtSUHNDaFVhRWxzcTR0OTZTK3liYTY5Z1UrM3RQamdQSTNmUUdIRWdyWFBtODRBeWZiV1RJM05wbG40WHJkOFk4QS9RRlpxS1UyeHlrSDUyRkdmNnIzVWNTOTdhb21zUFd1bjRFdzBxaDZSMDhnRVEwRWhhTUdrayt6VWZHZ0FqNFJDSmV5WVlQYmxtUDVaaTBQamxvQ0E5anF2U3ZlaHo2dEZ5bmhNd1ZMMUxEWWJwaXo0MlBoTjNQaCtHWlkzZ2NQT2JNRDZPMmlKVU9HRlVERStycmhEdnNDbndVUWtTS1NCbDVQSnpiOFRQZ3NWSitsRTQ3N2QiLCJtYWMiOiI4MTc1OWQ2ZDIzNjQxYzUyMzVmYzJiYjY2Yzk3ZTgzYmIwOTU2YmQ2NDI5ODQ3MjY2YTg1NGNkOGY1ZGNiM2Q1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:22:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjZVSGhJcTNzcVNVOFZ1MngzaHJLb1E9PSIsInZhbHVlIjoiaEI3L25FZUQyT25ra1Z1R20vY29sY3IvOUxocW5CdUNTa0tBLzZGVzVDNzNCZnNpc1hsMjVlOTZNcnZNZ1JwVHFBUDhLd3NpUjd6cEtqU3BUOU5LUmJwUXo2d2ttRXd5ZVhEZ2xzZXI5WHdxQlM2SjJuT2VxWmJmQWFKZkJvTG11dTZsVkg2QzFwenZPelNUek5vZTFadmh4Mi9ieU82a3Q1SnRjVHZCQUZnUHEreG02bE44My9DVW8yRDJGR092OFRKZExwYmMzenQ2bnlDZXU2MmYycTY1RjZ0ZmZJbVUzYUp6dHExa3NxdkVkeUUxY0RxZXlsYk51SENJWGJTTnNyVlpvQnZFMDV2YkNZcVUwSTNGZER2eElJUVY3eVRhb2E4QXhjVjRaaW5Ha05tNmF6aktrdjg0QkxHSHFRUUtpbE1ucXJmZVI2UnR6NElWVjB0MTVSWkNBNTVET0JqdmRnMWVTYmtVUDZNU3dUeWFWZFNJaUJTa2dqVVNFcmx6Q2ZsbjRJTXJGV2tLb1BpeFJKRXV2OGtLRGdtUnpQSEYrN3grM3lnRStaMW1MUjZNQTIvYnRQZXN0SXRPUnBMNHAwaitwTGMzV2FsNm5heUxDc3RsSU9sMURlQWFCdkpKYk1iMW9VVjFEdFJaQnU1Ni85eDNYb0RUQmt0SW5OVW0iLCJtYWMiOiIxMjgyZWUwNTk4ODk3MDBkNTc5ODg2ZjQ2Y2VkOTVjNzYwNzJkZjg3ZDhjNjU4YzA4MjA3MmNiOGQ4Njc0NjdmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:22:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-517794165\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1140533746 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6IjB3aUYxS0ZxQU1LWXVzS2R4VU1rMlE9PSIsInZhbHVlIjoiV2lsL1lPQkxnYXFIa3FvQ0ZLNUk2QT09IiwibWFjIjoiNmFhOWU4ZDM1ZmZhMDRhMmZjN2Q5ZDNiMmFiMGJkNTUyNzQwNDFmNGE0OWVhYzIxODFhZWVmYjljYWJjYWYyYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1140533746\", {\"maxDepth\":0})</script>\n"}}