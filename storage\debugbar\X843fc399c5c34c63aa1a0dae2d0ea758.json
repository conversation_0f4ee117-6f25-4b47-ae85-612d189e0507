{"__meta": {"id": "X843fc399c5c34c63aa1a0dae2d0ea758", "datetime": "2025-06-27 02:15:40", "utime": **********.448587, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.011963, "end": **********.448603, "duration": 0.43664002418518066, "duration_str": "437ms", "measures": [{"label": "Booting", "start": **********.011963, "relative_start": 0, "end": **********.375801, "relative_end": **********.375801, "duration": 0.36383819580078125, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.375809, "relative_start": 0.3638460636138916, "end": **********.448605, "relative_end": 2.1457672119140625e-06, "duration": 0.07279610633850098, "duration_str": "72.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722120, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02543, "accumulated_duration_str": "25.43ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4005072, "duration": 0.02432, "duration_str": "24.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.635}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4335759, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.635, "width_percent": 1.652}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.439406, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.287, "width_percent": 2.713}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/20\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1303901540 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1303901540\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-104466939 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-104466939\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-502053558 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502053558\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1954313179 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990537510%7C11%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlWbVFhZlhNUWpWNWZSNk9LeEhGU1E9PSIsInZhbHVlIjoiVGhrUzkxYm5INFB1ZU1sbUd4YUFwUnFWYmp5b2VTVWpma0JJRHQveTQxcjg0SkNsTnN0Q3d2SFhzQ2ptT29vdFlsN3hQSGhxcC9BVHI2WG4yYy8yZTc3MVB5cmZFU1o5UUZvMmdWSGV2T25Ba01lV2NzbnBraUFGVDI0ME1FQkExaFRjY0dyTFFmWEltcGsreVBpbUViekZmMWdocHFlaXdnYU1Ec21rMlRXc3puMGw0a2wzaVZLem1PNkF6Vk5DWVJ1NEs2Y2Z4WGZnSUpGOWhEK1pQcnJpZFBDUEMzVENHUEprUGM5c01xZDZMbFAzcjZpKzNnVGJub2hCR2tlZFNRMU5pZEdXRTFlNldERWRudDhGa1dDRWpTOXNZSDB4Z0pBWmoyUmpVb1gwaTlIakt4dXZnQmp2SmFMcVZiYVFNeVJEVWJBT3BGdHBtRWJWU0s5WjlQWFhwb1JIa0RiSUpiZjJFczFhbFRadEZaZkM3MW1INlcvdlV3YXBJMlc5N1lkWjlSZ3ZvVUI3WTZ6N1BKNFo4NU41OW10K041NGd2THp4TG9MUm1GenRSWFIxREtiSEp6RitSYUdYTTV2VitKb1FLUEt3N3ZHNXF4d0NFMUh0cXVZRFgrWWE5Rlg3RVJQRm9kd3E4b0pnamYwMGFJaXBESUIxMXR6aHlMeXEiLCJtYWMiOiIxYzMyNjA2MmZmYmFmMjlmMDgwZTczNzg0NjVhNWUxMWI3MGUxZDNlODllMjU1M2M0NDZhYjg2YzBkZmY0MDE4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtZdG96M3cwTktHYnhyRzRsTG1XNkE9PSIsInZhbHVlIjoiUzR5WS9SNjBuTEtTU254VmNJQlNFQW9PVVVlWVg5RHZ6QUtaRG1QMWtoOUJPdVJzZVVURU5KalN5Uk0rSXFTNEpmdDRtOVhwYXBxRXpySXF5UjgyZzNER2VpUjE0djRyQzluQnVLNjAwNU5kV0xOa0VaVGczRDJyRHBsako1ekorSDNpSnFLV1FiNkpWbk1nOVlDWGdMZ1hpL1Jaem1sVUM2bzRQa0xzd2JmdzZOZ1VuaXRqblJDa3BGL1BtZVlBaVpUdmtVQ2YwU3dkT0ZrT1VyRmdYT01VRHBraGYzWExSVGRxT1JsTVlHT080QXVKaXBNRXorVlhqRlNIcFN3dHgrOGJLUE1LQUVkYXE1bm1nK0NXcGVBOUhZelhubkVZRmMyMndHZEpTZkhtU2lXYTU4T1ZGZDZnc1I2clljZ2ZjQ2k0L0ptUWQ0dFNpTWY1T2pIMW9ZamVHYWNkME9YYWRlMGtibWNNNm9ubGI4cnpKY3dpaEl4QkJlYWJVcTNOOG9MQWtqU3JQVllPUXo3Z245d1Y5ZmM5cWZhUXoyeHpMZmpacU9PbUVSSUFCZ0IwelZ6SjR3K2E3NXNnWEFOdWdtMENzSW5ROFZwa3JreUdVdWxJcjFBNnVDMHdrSjh6bi9ubURKMGV2ekpKVzUwUnp6VW1ISmFGeXBHaVM0VHUiLCJtYWMiOiIyOTRkNzNmNjUyNmRhN2MwMmMzYTUyZTkwNTg5NTI3YmM0ZDk3YjY4MDU4ODEzYWQ3Y2FiZWY0NDRlY2VlYTM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1954313179\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1182895532 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1182895532\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-713521541 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InFmT1VaVWlBZEFaZEdwQmpIVWNESEE9PSIsInZhbHVlIjoiL3hMdnBLa3FYS0V6MnkrS3ltUWxmTkZnckpPWXNwZlI4c3RXeTlFanQxb1Nndm96OW1QRFpSNGtaa2wxZlJNOE1xZUJQOVRmdERhUE82QmpqOWxMaisrM2xyU1dXcG5rSmRIWkVPK3g1NTY5M0h6Zm51QkFMRTExN0VBZUN1RG41OElLaXo4RGsxeUkvM05LUm5NVTJaT2RxMmg2V29qbXcxR2hrdDVFZlBRc0h1Y3lTL3dCTFduUkJUYndxOGxsdU53RXRjcHhIaXVwRWh4SSsvTnpvU3k5cFhsM1dMYkN6RzE2NnppMjRsZzF5VXJtdEcwc09hdVErM3QzZ1NoVFVPOGlvdmRoUHNweXpBQXBhaFB4QnYzRWZncFMvZHZzUnlTQ0ZIVGxqb2Iva0dVa0YxTjZLVUV5S1FqdnNmVkV6c2xTT0lWcm0xUnJNN2RHd1Fpck5pUHYrWm1zeXlLbmpJblpiWGNnakQzbFNjb2JENDBvbENjemJIQVVrRWF4cndBN29XSWptVVhQUmJhZExuZ3RaZGU4MURUOHlTMEJLajA1UFNndFdWTUtNWWt2cUVTNlZVbDJ3ZlBXWmVveWFzS3JzbjNwb0FQRS9OVkQ5LzQwenlyT2JqWHJLa1RIWnRYOXBhVmhTOFBVV1BjTm84eVB0Y29Ud25BTU9kdysiLCJtYWMiOiI0MzUyNTdhMDc3MWRhNmUwODYyOTYxZTlkMTJmODA5MDU2M2M0NDlmMzUzYTVhM2U5N2M3ZTQ0NTA0MjhjOTYyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImtHbXZ2U1AxSFB2cXpORkVqVGYzaXc9PSIsInZhbHVlIjoiY21RUVBWSzZ4MmY2MHdMeEFia1pYMGt5TEkxOFYvOHByNGlMWUN4R0xzRFRRNnpwbXNMM3pTdTlHUC95QXJxcmI2Qm1wYlJnV3NjdThacEdQMjlNaHJHRC9kVUp5UlF1R1lMK1hWNm5kRW1LbWRiVmpRSmpad3YxWHk4T3FGanJrK1RhWXlSVUpsZ0ROS1FwZWdUTm01NDhmWU1mbVJHNjZZS25kdjltekZqQ3VVYnNUKy91NU9SNGZURG55S1hsdUJETjBidnRiaFdHYTl1T0FadDN6NEhFSGhIenlGUUlJUmpxaHlvWmdQZ3FJNWdpZHh6SVZRNllOQW0yek54dXhwRnNSbi9vdDZJbXI2MUFidW9YNkdlZHp0RnFMYmFHTFdPeGpFRW5YSFRjanZlc0t3MXpYYTE0R1JqeU5jamJOMnN2UWh0bHV5SmJpanNsQy9zQzhWVUd3dDM5QmlvbkFsbTkyLy9vdzJLcHcrNnJubTI0a0t4TndOVU52S2pyeStvU0pGb1dVRHUvalVnMEFIckFMbWRLYis0MkVWYVRoMzF5aXdLd1dxRjdiKzFYdWN0ODZvcldVWHp5a1VDekJXZzBBNzA3TFhRY1FvUmd3VlpTeEhrb0wyZkRkQk9CZy9GVGpCRU9CN01nQWVJalJFYUE4d0djREw1bTNDd1oiLCJtYWMiOiIyZDFmMmMxZmY4M2U4YjAyNDdiNzMyNzU5MTlhYzRkZGNiMDY2YzZhOGE3MmRmY2ZkY2FhMzgxMTUzMjBkYjAyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InFmT1VaVWlBZEFaZEdwQmpIVWNESEE9PSIsInZhbHVlIjoiL3hMdnBLa3FYS0V6MnkrS3ltUWxmTkZnckpPWXNwZlI4c3RXeTlFanQxb1Nndm96OW1QRFpSNGtaa2wxZlJNOE1xZUJQOVRmdERhUE82QmpqOWxMaisrM2xyU1dXcG5rSmRIWkVPK3g1NTY5M0h6Zm51QkFMRTExN0VBZUN1RG41OElLaXo4RGsxeUkvM05LUm5NVTJaT2RxMmg2V29qbXcxR2hrdDVFZlBRc0h1Y3lTL3dCTFduUkJUYndxOGxsdU53RXRjcHhIaXVwRWh4SSsvTnpvU3k5cFhsM1dMYkN6RzE2NnppMjRsZzF5VXJtdEcwc09hdVErM3QzZ1NoVFVPOGlvdmRoUHNweXpBQXBhaFB4QnYzRWZncFMvZHZzUnlTQ0ZIVGxqb2Iva0dVa0YxTjZLVUV5S1FqdnNmVkV6c2xTT0lWcm0xUnJNN2RHd1Fpck5pUHYrWm1zeXlLbmpJblpiWGNnakQzbFNjb2JENDBvbENjemJIQVVrRWF4cndBN29XSWptVVhQUmJhZExuZ3RaZGU4MURUOHlTMEJLajA1UFNndFdWTUtNWWt2cUVTNlZVbDJ3ZlBXWmVveWFzS3JzbjNwb0FQRS9OVkQ5LzQwenlyT2JqWHJLa1RIWnRYOXBhVmhTOFBVV1BjTm84eVB0Y29Ud25BTU9kdysiLCJtYWMiOiI0MzUyNTdhMDc3MWRhNmUwODYyOTYxZTlkMTJmODA5MDU2M2M0NDlmMzUzYTVhM2U5N2M3ZTQ0NTA0MjhjOTYyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImtHbXZ2U1AxSFB2cXpORkVqVGYzaXc9PSIsInZhbHVlIjoiY21RUVBWSzZ4MmY2MHdMeEFia1pYMGt5TEkxOFYvOHByNGlMWUN4R0xzRFRRNnpwbXNMM3pTdTlHUC95QXJxcmI2Qm1wYlJnV3NjdThacEdQMjlNaHJHRC9kVUp5UlF1R1lMK1hWNm5kRW1LbWRiVmpRSmpad3YxWHk4T3FGanJrK1RhWXlSVUpsZ0ROS1FwZWdUTm01NDhmWU1mbVJHNjZZS25kdjltekZqQ3VVYnNUKy91NU9SNGZURG55S1hsdUJETjBidnRiaFdHYTl1T0FadDN6NEhFSGhIenlGUUlJUmpxaHlvWmdQZ3FJNWdpZHh6SVZRNllOQW0yek54dXhwRnNSbi9vdDZJbXI2MUFidW9YNkdlZHp0RnFMYmFHTFdPeGpFRW5YSFRjanZlc0t3MXpYYTE0R1JqeU5jamJOMnN2UWh0bHV5SmJpanNsQy9zQzhWVUd3dDM5QmlvbkFsbTkyLy9vdzJLcHcrNnJubTI0a0t4TndOVU52S2pyeStvU0pGb1dVRHUvalVnMEFIckFMbWRLYis0MkVWYVRoMzF5aXdLd1dxRjdiKzFYdWN0ODZvcldVWHp5a1VDekJXZzBBNzA3TFhRY1FvUmd3VlpTeEhrb0wyZkRkQk9CZy9GVGpCRU9CN01nQWVJalJFYUE4d0djREw1bTNDd1oiLCJtYWMiOiIyZDFmMmMxZmY4M2U4YjAyNDdiNzMyNzU5MTlhYzRkZGNiMDY2YzZhOGE3MmRmY2ZkY2FhMzgxMTUzMjBkYjAyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-713521541\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1708491107 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1708491107\", {\"maxDepth\":0})</script>\n"}}