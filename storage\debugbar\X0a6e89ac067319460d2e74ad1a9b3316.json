{"__meta": {"id": "X0a6e89ac067319460d2e74ad1a9b3316", "datetime": "2025-06-27 00:14:44", "utime": **********.74039, "method": "GET", "uri": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.31094, "end": **********.740416, "duration": 0.4294760227203369, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.31094, "relative_start": 0, "end": **********.664824, "relative_end": **********.664824, "duration": 0.3538839817047119, "duration_str": "354ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.664834, "relative_start": 0.3538939952850342, "end": **********.740418, "relative_end": 1.9073486328125e-06, "duration": 0.07558393478393555, "duration_str": "75.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47521888, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.show", "controller": "App\\Http\\Controllers\\BillController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=333\" onclick=\"\">app/Http/Controllers/BillController.php:333-393</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0043100000000000005, "accumulated_duration_str": "4.31ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.696059, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 42.227}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.7058961, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 42.227, "width_percent": 19.026}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.713934, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 61.253, "width_percent": 13.225}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.7282312, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 74.478, "width_percent": 14.617}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.73014, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 89.095, "width_percent": 10.905}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => show bill, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1377958827 data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1377958827\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.733816, "xdebug_link": null}]}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-2050460400 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2050460400\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-623642693 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-623642693\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1165392702 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1165392702\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1597357884 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983282490%7C45%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRzNTJDVzV4MTgwOWlUZG5pRU43dXc9PSIsInZhbHVlIjoid1c1NmM4SUtPOWxOWmpLQnhBQ00xZFFBc1V4ODltTDlYR3dyM01qdGFONFJxcU1kK2lSV0RGNS9JQ0pmWmZwMkZjdDVwVC9naWNKdVlqRWhaSElSL0xScVRETW5lbnJPa0RXTXhWWUVxck1tTkl3SEx6d1NFTWdJS1A2SEV0aUdyWERFQVhUS2pEZU1zanBxbHEzL1VrNTFGcEZyUE9MRWFwSjVVRlNQcWZYTEU0K1Z0S3J3NjVWSXF1MnJDUXA4Wm1KZWVMd0YrcUh5MzlueHlGWjgwVFdIODZ2U000N3ZBWkZYM0xEK0pxYUFkZll2V1Avam9tWkZQM3dKSGJGVnVGWTRmaTBuZytUSUhidkdqdW5la3lyRzBYVjE1N0FEbHNwUW02RVlmRENrejRFV3RQbDJ0STlTNzN5QXJKODVHd3FFMVpyN0FSM3RhemRDeXlSRXdFdFYzanlpUWM1dENjRlNzdjlhaXpxRTc2TXMzN2tZTzJqL3JTU3Z2c3AxVEZvc29hK1NLYzEvbjFKdVA5QnV5ckZNOUhZTEZvWDYwa3lqY2NvWmFtRUZVSHduRVNSU0RyRHluaXd4NGdTMG9yTk1KY2M1S2d0UVFXYnNoK3pCdW1uWGJtYTFGbkFyVVVWRjhXVjJjM0l1S3BFNmloek9WWncvaGdPNmRqSUEiLCJtYWMiOiJkM2E1MGQ5OGQ1MmM4YTc3ZTE5MTdhN2QwY2ZlNjI0YmJlMmViMGNkMWJkMTNhNzA4ZDU1NjNlNTZlZTVlMmQzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InBqZUczMXNBYjQzVmdiV0dOaHdQNVE9PSIsInZhbHVlIjoiSWRjMUpTUUI4d3Jkd3hNR1l4ejZWNGx2MnJEUzZLMmtHQ3Z0ajJZalpKUFVOTjg5eHl6N2FiTTNQVWdwOUF0ZUlXZHNkbGNObmluV3BKYmtaUEJtOVMrdmc5QmNiamlrN3FDZ21rUDB2TmZjTXJzKzVBVlJja0hyNUJDaklrbTJVNGlTSW82Rm14ZkUvSUZuWFlHRzEvQjE4NlkzRWoxd3RJQzBDTFRHUHFObEV5V04xRWZJYUJHYzROSlVXRElSaWxsTjJaMERvKzlOOVg2aDRPaXRpZmRHZ3RUT0NnM2tYelg3MER6WGR6ZkJWU2xkRlEySGo1RExvYU5xZkxMZTVMWEI4MmlLSHlTZmxwUllTYXpqUnJnNkRDbk9LaTZUUmlJVnVlMHB0ZEUxc3ZxYkw1aHlMRlVJK0JDeFNncDdvTXFwNUkxSDFSS1EyVHdld2krc21LR1U3ZUw2VWd5VStvaXIxNGlMczNUbXBoU2FTVVpIU3NUMDZCWW1VRUZjSnZIbGdESWcvcFFIT2JNVW5VQk43QzlTSjlFbVNsYzFwTmE1c1RhcXhFOTFjbmFxY3hwREpuWGg3RVZvMjJ6NlJBRjlWVGVxS043WW4weTFwTVhMS2tSOG5iTWVBcG1uTUdGQkdDaU9yODVlWmxZVzg3V1EzbEc5cHZDbmpJVTQiLCJtYWMiOiJmM2I5NmRhNmFlMzI0NDYzOTExMjhiZGExZTE4NWMxMzNlNDRiYjQwOTljZmMwNGE5ZjAyZTMxM2JiMjZmMDQ2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1597357884\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-548584762 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548584762\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-367237865 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpGbURnSVdBdmRqSXkrREFXYVpOYnc9PSIsInZhbHVlIjoiclF0NWVyYWhLay9QYnB5L3g3WHVPOUlHRVRuc0Q2bE9OYnJYZ0RubnEwSG5qNkdYYUJicXpFaG9PcFlBaG1vdmdhTVA0OVkzc1MzVHFnRndjeFE1cVlFbHdHVjVmQkx2UHdHMXJSWXMvSkdxdktBTm9sUk1zYXdQQ3VyUWdxN2tZNmdxYTA1K0V0a3ZYczMvcyt5Sk8rN3N6WkVVdzIyenFDcDhVcHRNQ0E1RUdMbUsraFRKUTQ3YWRYRy9tYitNNFhxTlAyb2ZsZDdISm5RWG1zVUtqekxWSXlRT0cvUnoyRmRSZjN0OFhMdlBKYmM5amxwMWsvZ1h4dWZVeERXUUtrVmhTNStCQ2xCcVhDejJiMENPWVBwWnB6Q2lnU2IvK2t1VnQ5L2ZLN01hbkpqUDNMUVVZQ2Q2U3ZSTU05Yms2dTNnc2EzdXRMa0NQdzY5MG56cThPdU9BQnVsZ1lNdUZwdDdZWHBkVDZvdUV0SHNrMW0zejFKRjBHQjFSeUZBb0tDQzFUOU9UOUtBNWhXMFBZRHdqbEwwcC9jMzlVYVNHZmFBTVBWMTVEMk40WUwzUE5LWWNQK3poNTdkUU9wd0NHK3FLSnd4TTZMSnFoTGZDSis0QmRiT0JvbnRaUFloQW81TUZlU1lsMTdCalhOY28yMkU2RU9xWUo0MnA2WEgiLCJtYWMiOiIzMDIxMjA5YzVlZjQxZDQ2NWRiYmY0NTdiYTgwNTY4ODY4NmVkZDU5OWIyZjJkODdhYmQ5ZDI0NzRjODk5YjIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlNkVnhSUFI1d3JvbjJ6V3dMUHM3VXc9PSIsInZhbHVlIjoieDArZlhDc3cxMVpTdkQxa2wzK25hUkFBNlMzbjRzRGNBak1zSDN3WXZoT3VMeS9OZlA4bkFsV3B2VHgrKzlKL1lWUWVwSlFTZHQ1NXJqY2ZQUS84RnpKVkplNCsxbVNFbEhjNUJlRTFpNUZ1VDNpc0NxTng0UmlOMWRsMk5PNFJGWHE0OVlQSjBwbnMxRlY2WStEaEliTFY5am96a2tCb0NadHFlS2IrdVA2ZWI5YjFzdmUyYWRXK3o1OWxxdWlscjhaUXc1SlpHdmJac29leE1MaXZhZlUxWWwrN29sdXRNbGZYTG5uL0JRanhsL2s2TFJrT2M1Tk8rMFl2ZkNoU0tBcGVZUGtPZy9uVlVvbVpGbU1SaXFXampDOGQ1dmxkdDZMT1pTRG83cU9vV2szUTlBN3hEYUYxZUlhNlVBWUg3UURCU29VMDhId202OEh6OVNuWllveE1xRm10OFdmMTd0WWozZ3lBK0huUjBiYW1Yd0djOE02THArT2kyVkw0eitqa0ZObFdORWNUS0lCY3ZEQkhWa0xUbHFNMWwrdTFvN2RlZmFEWDI3bGdXOTJCK0hFcnhBbVpEalZaVzZ5OU8yeTRzNDdvZnZzMFBxK0dOU2l6a0ZtbnlCZXF0cXZ4QmJKTlNLQm16WWQvUmt0NzVFdVBVZWMrUlhmMnk5OVciLCJtYWMiOiI4MTNlNmJmNzVhZjQ1NzEwMDU5OGYzMzEyZWFiYTkxMzJlNTc4Y2I4MDU1NmFlNDcwNWVjNTkxY2Y1NGQxMDhjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpGbURnSVdBdmRqSXkrREFXYVpOYnc9PSIsInZhbHVlIjoiclF0NWVyYWhLay9QYnB5L3g3WHVPOUlHRVRuc0Q2bE9OYnJYZ0RubnEwSG5qNkdYYUJicXpFaG9PcFlBaG1vdmdhTVA0OVkzc1MzVHFnRndjeFE1cVlFbHdHVjVmQkx2UHdHMXJSWXMvSkdxdktBTm9sUk1zYXdQQ3VyUWdxN2tZNmdxYTA1K0V0a3ZYczMvcyt5Sk8rN3N6WkVVdzIyenFDcDhVcHRNQ0E1RUdMbUsraFRKUTQ3YWRYRy9tYitNNFhxTlAyb2ZsZDdISm5RWG1zVUtqekxWSXlRT0cvUnoyRmRSZjN0OFhMdlBKYmM5amxwMWsvZ1h4dWZVeERXUUtrVmhTNStCQ2xCcVhDejJiMENPWVBwWnB6Q2lnU2IvK2t1VnQ5L2ZLN01hbkpqUDNMUVVZQ2Q2U3ZSTU05Yms2dTNnc2EzdXRMa0NQdzY5MG56cThPdU9BQnVsZ1lNdUZwdDdZWHBkVDZvdUV0SHNrMW0zejFKRjBHQjFSeUZBb0tDQzFUOU9UOUtBNWhXMFBZRHdqbEwwcC9jMzlVYVNHZmFBTVBWMTVEMk40WUwzUE5LWWNQK3poNTdkUU9wd0NHK3FLSnd4TTZMSnFoTGZDSis0QmRiT0JvbnRaUFloQW81TUZlU1lsMTdCalhOY28yMkU2RU9xWUo0MnA2WEgiLCJtYWMiOiIzMDIxMjA5YzVlZjQxZDQ2NWRiYmY0NTdiYTgwNTY4ODY4NmVkZDU5OWIyZjJkODdhYmQ5ZDI0NzRjODk5YjIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlNkVnhSUFI1d3JvbjJ6V3dMUHM3VXc9PSIsInZhbHVlIjoieDArZlhDc3cxMVpTdkQxa2wzK25hUkFBNlMzbjRzRGNBak1zSDN3WXZoT3VMeS9OZlA4bkFsV3B2VHgrKzlKL1lWUWVwSlFTZHQ1NXJqY2ZQUS84RnpKVkplNCsxbVNFbEhjNUJlRTFpNUZ1VDNpc0NxTng0UmlOMWRsMk5PNFJGWHE0OVlQSjBwbnMxRlY2WStEaEliTFY5am96a2tCb0NadHFlS2IrdVA2ZWI5YjFzdmUyYWRXK3o1OWxxdWlscjhaUXc1SlpHdmJac29leE1MaXZhZlUxWWwrN29sdXRNbGZYTG5uL0JRanhsL2s2TFJrT2M1Tk8rMFl2ZkNoU0tBcGVZUGtPZy9uVlVvbVpGbU1SaXFXampDOGQ1dmxkdDZMT1pTRG83cU9vV2szUTlBN3hEYUYxZUlhNlVBWUg3UURCU29VMDhId202OEh6OVNuWllveE1xRm10OFdmMTd0WWozZ3lBK0huUjBiYW1Yd0djOE02THArT2kyVkw0eitqa0ZObFdORWNUS0lCY3ZEQkhWa0xUbHFNMWwrdTFvN2RlZmFEWDI3bGdXOTJCK0hFcnhBbVpEalZaVzZ5OU8yeTRzNDdvZnZzMFBxK0dOU2l6a0ZtbnlCZXF0cXZ4QmJKTlNLQm16WWQvUmt0NzVFdVBVZWMrUlhmMnk5OVciLCJtYWMiOiI4MTNlNmJmNzVhZjQ1NzEwMDU5OGYzMzEyZWFiYTkxMzJlNTc4Y2I4MDU1NmFlNDcwNWVjNTkxY2Y1NGQxMDhjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367237865\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}