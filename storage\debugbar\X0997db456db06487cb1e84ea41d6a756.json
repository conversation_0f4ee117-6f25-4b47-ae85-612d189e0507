{"__meta": {"id": "X0997db456db06487cb1e84ea41d6a756", "datetime": "2025-06-27 01:13:40", "utime": 1750986820.021153, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.384502, "end": 1750986820.021166, "duration": 0.6366641521453857, "duration_str": "637ms", "measures": [{"label": "Booting", "start": **********.384502, "relative_start": 0, "end": **********.709552, "relative_end": **********.709552, "duration": 0.32505011558532715, "duration_str": "325ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.709561, "relative_start": 0.325059175491333, "end": 1750986820.021168, "relative_end": 1.9073486328125e-06, "duration": 0.31160688400268555, "duration_str": "312ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51496136, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent", "namespace": null, "prefix": "", "where": [], "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2370\" onclick=\"\">app/Http/Controllers/SystemController.php:2370-2422</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.013560000000000001, "accumulated_duration_str": "13.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 70}], "start": **********.7487319, "duration": 0.01324, "duration_str": "13.24ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.64}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 71}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SystemController.php", "line": 2373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.764828, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.64, "width_percent": 2.36}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "23", "_previous": "array:1 [\n  \"url\" => \"http://localhost/report/pos\"\n]"}, "request": {"path_info": "/cookie-consent", "status_code": "<pre class=sf-dump id=sf-dump-1714027460 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1714027460\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2127656022 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2127656022\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1219253868 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1219253868\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1802414624 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1945 characters\">_clck=tu6zsp%7C2%7Cfx4%7C0%7C2004; _clsk=1ggm1jz%7C1750986817753%7C3%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkcxNXpkSksrcEN6QjdnRFZzWEpiL0E9PSIsInZhbHVlIjoiTEVEYkplcEdmcUJMeCt6NzF6VEVDVmpkQkowZjNLTit6eGVHUXlsdXBHVnR3di9DSVJPOGx4bnEzbUlONVpVQ1JWcXFuSm8xa1hld0FTM0hUOFRHTVR6cEwrTFE4MmdTKzV1Qk51ZkgxOXhiMkRrYlcyQkU0TU5aWk1BdGhpckRRVzk5STU0Q3UwRGQyeXpUb1NnMUdLeG13ZUZIb08yUXdBblcwWTh0TGJ0MGxJOUs0SzQ4dU10K2JvRG5qclpWdHFGUWF0dmN1c3hXUkhUb2hUZ01HYlhMN0x3bGFHdVh5UVRQMHA2dlo3ZkpDbG16MEhlM3lDUjRHU2xkSlo0cW9LWWI2blphOXFOaWxQTkpzOUl3d0ZCbmFhemFtVk1Cb1E3OHFIQThvNmFRL2xOcjF4WkZ4Yi9yeDdoekRJOHhjOWVvYWF6a0FRSHVzOHBhN3Zua2lVdU1QdldraHMzdjhsYVlCeDNqVEE1UVRDVHM2bmhNUHNpajd0dnFBQiswU0lBNVZtQk9IUXRMNXljY21MM1M5ajh6M0g3WHBrVGdOTk0yWE1pYTF4b25TSGg4WjU2a2JpUUlHZkJ0Zmt5V1RGbnZrdHMxQitFZktZZ2JSbnl3T3YvelkyTnc4SWVkeG1ITDNNNzU0LzNlcXBDRjFRYlpqL21lUjRSZVhlaGQiLCJtYWMiOiJmMzBiNTBhNWNmNDFiY2I5YWNmZDc0N2JhOGFmMzk3MmViYjYyZGY3MjVkOWY4YWY2OGMwOWI3ZjA4MWRhYWUzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJwQzRyQWZ4djdRNWVybzhITlRUM3c9PSIsInZhbHVlIjoiUzhlMzJGQ1pDSEZoUXh5MHNTeXErYThaWXZKdDJ5UXV5NzhzWi9PZkF1RGk1K0czaGkxQmdQczQwc21OZ1NmdHVxcVFSS2EwYjgxYjZzS0JFR0M5cXMyd2pJbmU0MXlXaXd5VWl3SmxCSktpeFlCVDJiZmdjYWhNQ200Zk9paGMxVkt3eEdmUFRXajhCclkrWGJZQzdxeFhhM1Bqbk5sRnpnSGQ1UjZXSm5FOFB2bXB4MVBhZU5mVTlucEJTTXhWN2MvTWZPZ1R6V1JqdURiOXJicldQSVdLK29MWGY2Sm8rOXl0KzQ0VmpaYmdwZnZmUzU0cFV3VHlIUE1iRW1KbGhQSGJZMVdRRG1GTFg1Qjh5QXZ4dVp5cTdrSmIwaExtZ08zUGd3RkVyQzBWRE42aVBsUzg5dncwSitVU1ZaKzZBYStyN0VMb3lhUDJuR0lRdTRyYnM1QkUrdnZJTWJuNm40UEJvWEd3bEJuWGJ4b090ejhERFBWTnRpWm0rYStEUFpZMHB5R3hhMkt0ZG0yYVZqYkxKWGxaUzVCeTI3Y0tyek5EWXNOZ09USWg1WERPSExkVVhSeTJPQy9ZSnZ2TUV6eFh5cEdPTllIRVdLanYxdHcyaWN2OXhqUmVKeHFiUUxQNGZnVFp1cmdZOFltSVY2d0FUaUJSYU9nN2dTc3AiLCJtYWMiOiJlMDc2NTAyY2E0YzMzNDFmMGVkNzc2M2EwNDQ1NDg1ZGFhZTAyYzhiMmM0YTJmYzRiZGNkOWFlZTUzZTJhMGE3IiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802414624\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-647781336 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QVqhpWieKZCLT3nmskTMtatIDNwBdc2egPwBt6XG</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-647781336\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:13:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkZnWkQrUkhubUxRK1lEeXVnL0V6RlE9PSIsInZhbHVlIjoiV0tiVGd5eFR1RmxmSmlrbExvSFkyb3pRQnZzc2p3bGZ5NTFRQ3pVUkRxSktJUkdjclBjUnRMVmVzSlRZZmUyVkxLNnY0OFEyancvSnN3R3dEek56RkdidkFsU291cDNGMldkMWtCVURicTJOZFlFYnJEOEZ1MjVDb29ocmNqM21UdUVGM2wzZXBKQnRFRHBJelZBYm9aaGhwUXVoOXYyc1RNdkFCTUxMcldqTklXUTAwWlI3QldsWjZhVjlEbGNsTjQ0RDBsNS9RcW44bUtQdnVGN3hYRFB1NVdieTc4dE9kUWZUdVExZ3J0OURBa2QzWlBvZkJOa1hBVWwrTFdydjNGV1ZaVnU2TUNFZHZ5czM0K2QwaENYMnNBcUhxRkptaTZPbW1vNlQ3TmY4RzFWUEFaZU5hbXlXMUFkZVdsWnBodE9YaXcyZSsxQXVjRXFFQUlMenZBMVNxWVdEdGkvdSsveHdOemlpNDlLNExjUDRtY2RWM2FIaVpFQjhtWFA0ei9tN013T2RWVHBjdHpxSHJYZGo3eC83alpSa1JOTVpiRFhTNEljdXcrNEcwK3MyMFgyczh1RUpzWDNxTTdranNvTUM1MUNGejRsajJLTXpKTU1pdDhlUWV2UWgvZENmMjdJbGxrSHhlemxOTlBUSE1uNVNVSkJFRmwzY3ZOK2wiLCJtYWMiOiI1N2ViODFjMzdlNDYyMzlmODQyMzM3ZjZiNTRlNjAyYzNkN2VmMGNlNjY1M2Y5YzNkZDEwYzE2MWZiYjA2ZGYxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:40 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imd0ZjYzZm03Z0plMXUybUQxQnhNWXc9PSIsInZhbHVlIjoiTmhWNzYzWGRZaUtEdW5jemUwRUI1eW0yNU1sWHg1T1o5K2E1eDNRTGVnWUU3Ly9HS25EemtFVnhSRndrZmovb05QYkszczBhTFVzSnIvSzBYQjF2NDZDVWpJUmxmNGZ4NE1RTlBJcEMwV243ck0ySGtadEsySlRlczcxVThNeVRvVDhTMzlaL1RVRnFyeTNjVzlxRDRaSnNXeHlVK0gyNHhFUjlNN0FpUXlxMWRUZy8xTUhYbmtoY3ZsMjN6S3hRTFpneTlVQ08ybCtMQ09kRXBLUE9QSEtmbUZ3NFZadmlodm56MzUvVGpiM0VaSTlLTy82VG5NUmtPMXU1UUZVVWszV0FGdW5yTnN5Y3ZhNEZHM25KV0t0YUg5SHZJekg1WmxiR0FLSFI3aytLTUZLR1RZbWZvZXFaWFY2dmREUDhNSWpISGJQbUJBTGJTYnNuMDdLOXEwWjFxZzYyL0tUK0dFVnZPTThOdENnd0IxNnNHYnd2MEpLWnEvZjUrTUlCRjc5ZkViTmpMZGJQcUpRRHl4aTlrN09SMkt2SzREWjBFUFFUQWpoVGxNOEdLdTV5d3lJL2Q0RVNhbllXWTBuN0x0VkhmcFRxbEE2WFprUzlQaHpuVHA4TmlVa1BGZjdlZGZ3c3pYSXcyNEVRQmd1MTd2UWt4SVZKRjZvQlprVVgiLCJtYWMiOiIyMjNiZmYyODBiYTE5NjIyOGZmMjJkZGY0MGY3ODRmYjAyZTYwOGUxMTk3MzdiNjI1ODIzODVjYjJlZGYzOTc0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:40 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkZnWkQrUkhubUxRK1lEeXVnL0V6RlE9PSIsInZhbHVlIjoiV0tiVGd5eFR1RmxmSmlrbExvSFkyb3pRQnZzc2p3bGZ5NTFRQ3pVUkRxSktJUkdjclBjUnRMVmVzSlRZZmUyVkxLNnY0OFEyancvSnN3R3dEek56RkdidkFsU291cDNGMldkMWtCVURicTJOZFlFYnJEOEZ1MjVDb29ocmNqM21UdUVGM2wzZXBKQnRFRHBJelZBYm9aaGhwUXVoOXYyc1RNdkFCTUxMcldqTklXUTAwWlI3QldsWjZhVjlEbGNsTjQ0RDBsNS9RcW44bUtQdnVGN3hYRFB1NVdieTc4dE9kUWZUdVExZ3J0OURBa2QzWlBvZkJOa1hBVWwrTFdydjNGV1ZaVnU2TUNFZHZ5czM0K2QwaENYMnNBcUhxRkptaTZPbW1vNlQ3TmY4RzFWUEFaZU5hbXlXMUFkZVdsWnBodE9YaXcyZSsxQXVjRXFFQUlMenZBMVNxWVdEdGkvdSsveHdOemlpNDlLNExjUDRtY2RWM2FIaVpFQjhtWFA0ei9tN013T2RWVHBjdHpxSHJYZGo3eC83alpSa1JOTVpiRFhTNEljdXcrNEcwK3MyMFgyczh1RUpzWDNxTTdranNvTUM1MUNGejRsajJLTXpKTU1pdDhlUWV2UWgvZENmMjdJbGxrSHhlemxOTlBUSE1uNVNVSkJFRmwzY3ZOK2wiLCJtYWMiOiI1N2ViODFjMzdlNDYyMzlmODQyMzM3ZjZiNTRlNjAyYzNkN2VmMGNlNjY1M2Y5YzNkZDEwYzE2MWZiYjA2ZGYxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:40 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imd0ZjYzZm03Z0plMXUybUQxQnhNWXc9PSIsInZhbHVlIjoiTmhWNzYzWGRZaUtEdW5jemUwRUI1eW0yNU1sWHg1T1o5K2E1eDNRTGVnWUU3Ly9HS25EemtFVnhSRndrZmovb05QYkszczBhTFVzSnIvSzBYQjF2NDZDVWpJUmxmNGZ4NE1RTlBJcEMwV243ck0ySGtadEsySlRlczcxVThNeVRvVDhTMzlaL1RVRnFyeTNjVzlxRDRaSnNXeHlVK0gyNHhFUjlNN0FpUXlxMWRUZy8xTUhYbmtoY3ZsMjN6S3hRTFpneTlVQ08ybCtMQ09kRXBLUE9QSEtmbUZ3NFZadmlodm56MzUvVGpiM0VaSTlLTy82VG5NUmtPMXU1UUZVVWszV0FGdW5yTnN5Y3ZhNEZHM25KV0t0YUg5SHZJekg1WmxiR0FLSFI3aytLTUZLR1RZbWZvZXFaWFY2dmREUDhNSWpISGJQbUJBTGJTYnNuMDdLOXEwWjFxZzYyL0tUK0dFVnZPTThOdENnd0IxNnNHYnd2MEpLWnEvZjUrTUlCRjc5ZkViTmpMZGJQcUpRRHl4aTlrN09SMkt2SzREWjBFUFFUQWpoVGxNOEdLdTV5d3lJL2Q0RVNhbllXWTBuN0x0VkhmcFRxbEE2WFprUzlQaHpuVHA4TmlVa1BGZjdlZGZ3c3pYSXcyNEVRQmd1MTd2UWt4SVZKRjZvQlprVVgiLCJtYWMiOiIyMjNiZmYyODBiYTE5NjIyOGZmMjJkZGY0MGY3ODRmYjAyZTYwOGUxMTk3MzdiNjI1ODIzODVjYjJlZGYzOTc0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:40 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"27 characters\">http://localhost/report/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}