{"__meta": {"id": "Xe4610a2bb406725b86fbd5adc7b7268e", "datetime": "2025-06-27 02:26:08", "utime": **********.311657, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991167.917108, "end": **********.31167, "duration": 0.3945620059967041, "duration_str": "395ms", "measures": [{"label": "Booting", "start": 1750991167.917108, "relative_start": 0, "end": **********.26256, "relative_end": **********.26256, "duration": 0.34545183181762695, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.262569, "relative_start": 0.3454608917236328, "end": **********.311672, "relative_end": 1.9073486328125e-06, "duration": 0.0491030216217041, "duration_str": "49.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45720304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00233, "accumulated_duration_str": "2.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.289157, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.953}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.298371, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.953, "width_percent": 15.88}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.303421, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.833, "width_percent": 17.167}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/21\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-285672213 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-285672213\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-463088235 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-463088235\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1410305248 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1410305248\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-764177110 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991165773%7C31%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlRvTDlrRVo3djl3M3dLa3BmMzFTd2c9PSIsInZhbHVlIjoiQ1RWc3ozNS83U3k0ZHBFSnhoY0EreXUrejY4ZlJuakczWjg4aU1GK1ByUDNkS2h4WHRoeDVQMzFiZm5NTlR5OHVhTXFSUUUxY1BkanVGV3RqZExFRXZ3NUYxQ1hyZ01HTWV0dkZjR2JBVXlvcnNVRUR1dU1zVVZsTitqYnFHeks5U1dVUEI5VUxuZjRoNUFKa0VaTW1tckIyVjA1T2pUTEhCTDlxY2NSN2YzZnNlTnJVVzVPYm0vRGFhcGVzNFhxMHZMRHcwUXBNcFFXWjJjUlVNWDFGZ05zOGkxU1JwUDM3RGlmVlNHcjVaak82SVprZzBUVDBsRU5QYlNVWDN0Z3hiOWtQeVNYdml2WmFvZUFMWVhRZnowVXBWNDcySzhQdW9qN1QxWk0ycFB3VlJBTlF5VmlCZnRTNFhQM1l4RXlQSDNrS1ExdWoxUThVb0tIdHNLcWdvYW0xK2RHbklxYytQakwxRlU5RVVJSGJ1U2pDc0ZpdDIxenZrZ3Y4RkFobmZvTTQrTWhKclk3UXhucjRGeGpEMjRVQUFxRldpKzFoVlRKSTVPekIyT1NSOXpUeEFoMUpmT3RiSWNOaW44ckxYaDduUy9vRVNhV3NCem5SRFd3ZE1jZUdDd3B5RjJNcld3VVBiYVhnTmhyelpEbHNhZW0wUHlVdzJwb1ZmWWUiLCJtYWMiOiJjODJhMTMwNDlhNTA2NjhhYjEyMGEwY2JiYTIwNmJjNjBiNWVlMDM4OTVkYzkzNTU4MzliZjU5YmZjOTZlY2IzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1GUEp3cExLVENZS2ozK3ppV2RsTFE9PSIsInZhbHVlIjoic2ZBcFhSakc5emFiYWJ3RE9xSllDeUpLaDZVSkVvdHB0OHR5SS9wNENpK3UvbXovOW91K3NWVTkrNkl6VTFxdHVzWHZuS1YzQmljV2pITnNNTTNlZkJTUGc4dDJBdUQrRms0WkRmSENlWmIvK0VLTlNsNWlCNDdDSWppb3lvMXdJdHJMK1diQm9RQ282dUM4SzlzRHFKQkd3ZU5SOVRoelltUzNPL2RCODU1YW1ONU5VWW9TZmo2VENuQWVYRTBpcEdodFZyYkxsTjZYKzFqYmo1YTNCeFllRzNOVEQ2REZYWWRZamZxS0VoYzZPM1I1YUcxMUhtWkcrSmJJWHJZajF6dVlOQ2RaZ3dQbEgzaFBvUFZZOHoxbHViaTdMalRqbjVpaEcrbjcyNjd4dFhFMjhMdG0rWTVKdjc2OUJJUlRDNUlkVFpPbnFHV0lvRW1GcHp2Q09vNEthVFRSanVyYWt2UWZUY3BIbEtXdUNNTkFwanBGUFV5VFpyaDdsM1dDTkZKbkVjUDVpaW1YMG9mNlZwZWg3dDV0N1pEMHgvZmJ1cmd6QmtVbFZpY0pPcytyQXRmMkVxVkRvT2p3ajc0Q3lTeGpiaW5zTW1Yd3AzMlZuSDMyUUJMOXhpTmZ2MUZObHV4ZjNFSmRiTi9ac1lwZC9yU00rTGxLMUs4R2ZqYXEiLCJtYWMiOiI1Y2MwM2I4MDcyOWYzNTBkZWJhNmFmMTg0ZjU5ZGFkNGE5MjcxNWZhNGZmNmVlMjc3OTg4ZGFiYWY0ZTQ0ZWFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764177110\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-997988227 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-997988227\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1839671613 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:26:08 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBpaGx1Y09WVk9FeHArZmYwZFBKM3c9PSIsInZhbHVlIjoiQ05uTGhRZ1FBUjhiZGkrSHlZdXR5V0cyZFRFRUc3NXB0Y21kTmxYMGxDZW9hOW5ldlQ0dGR4b1J4SWlpc2xUd1J6ZEFBVzNlcWxqUVFzZW1uMHdyWjFWQlZEc0NCelZ2KzNXS2ZLekZuR0JIMUdDRGJYL1QrZURacWhqZ0w5ME5GN0FhS241WmFwY1hqeERNeDBYcmd4RytRSFgvckdZNTFDOFFnNDc0YUJtT0xEWnNpTG41elJHbGI3Q2R2RWpOUmowQmIzbk05Q3VXV3JjMTB3MXhPQnJUT3c0N2ZnMXE4QVJhVTlSZWNWdFU0WENwSHlNMTFoV1ZTczVqUGFYNTlxQzUwdEREQ0V4QVZ4Y2VLRXFOR0k3K2JPaFF2amREQkx4eEVYR3pHNVlRQ0FEQXJBNkcveWJVT3d0ZTBOUXRNS1JUWWNpckFReWVlK3JNUXM3NUpxblRJM3Q4cjYvWk9qM1Vuakd4cnFWY1l4bHB0NVZvOXYzeDRmZjVrRGtUS2FIaExMSXFmb01lNEZZVk5JbTI5WHNmOERrd0ZLT1B2NXlaYUN4UGhGTlZCL01nSC9KS3ZwYTUwaE5UZHI0QTg3Kyt4ZCtKT0dwRWtjTi9WZXdPRVhQY1VsU0dyVzYycnlFa2JrcEozNnJWRHRPakg1ZkhIeG5UQm9mVzB3RGkiLCJtYWMiOiJjYzRmMzliZGQ2NDk2MDUwNTc3NTQyZDc4OTgwNjhmMWNlMzJkMzBiYzBkNTdmOWJjYzA0MDE4M2QzZjE4NmVmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:08 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImhXWmNHNno3aWhDVVhyNll4QmFocGc9PSIsInZhbHVlIjoiOTFRV1BoUzJsWHlTOVlvYktPSGVvS0tDVVAxcU10ZGl0N0g1cmtJR2h5OE9qNnJKQlZUUUZsMTdaVnJuSmlDOGNRRFQzZi9kbmo4ZmFnNk04MGxqNm1TWm9EdmszeWF3SWRxd0haOHkyUDFPeGlla0JTMXFtLzJEU2UxeEZDSDNkaFZlM1NrclRXUWZSa2ZHL3BQSkMyMVhwWjMzeDk3SHI0TVRJcTlra3l5UmFsQjRJZmtxZmhTanR5ek1sZGFhVVZMZEY2Z3RubldSa09GTEdka2xETnRNQkd2UkZ5Ni9ka1MzaWdNMk1KRnFYR3NRVlFYb0dNRHFmUFdxYytJNjlFUlVxMmtOb0FCMVhza01iTEpFeWZTcU4vdFpod3I3MG1uQ20zUWUzTWtkMVZWc0tWQVVEOVpDaG9IZDVJWW9NT3JWcE5TdEFYK3VCTjI5UW5sNkVMNGRhZkRiazFnYzJueGZUL2pkbmhKNUpsay9pcW1oWFFoVEtoWTdHdlViR1cxKytVa0xoRE5uV0QzTlJ0NDdlNW9Ya2RHQUdJRDlacFYzRHl0b0ZzMXFxQndRdmlhd244dUVmcGRrNmxlYmxGMGZLTlNNbXFtT2lKeVY2QW5hWUVvVHZWQnl6OEQrRjFtSXRESStudnduanhzcForM2tvUFMrN3lobkRZbUgiLCJtYWMiOiJjNDcyZWE3OTUzNWRhYzFlNTdhZGE2Mjc1YmM3MDIwNzk0NWJmMTMzNzM1MzQ3OWI4ZjVhNGIwNzQ3MzY2MGNlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:08 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBpaGx1Y09WVk9FeHArZmYwZFBKM3c9PSIsInZhbHVlIjoiQ05uTGhRZ1FBUjhiZGkrSHlZdXR5V0cyZFRFRUc3NXB0Y21kTmxYMGxDZW9hOW5ldlQ0dGR4b1J4SWlpc2xUd1J6ZEFBVzNlcWxqUVFzZW1uMHdyWjFWQlZEc0NCelZ2KzNXS2ZLekZuR0JIMUdDRGJYL1QrZURacWhqZ0w5ME5GN0FhS241WmFwY1hqeERNeDBYcmd4RytRSFgvckdZNTFDOFFnNDc0YUJtT0xEWnNpTG41elJHbGI3Q2R2RWpOUmowQmIzbk05Q3VXV3JjMTB3MXhPQnJUT3c0N2ZnMXE4QVJhVTlSZWNWdFU0WENwSHlNMTFoV1ZTczVqUGFYNTlxQzUwdEREQ0V4QVZ4Y2VLRXFOR0k3K2JPaFF2amREQkx4eEVYR3pHNVlRQ0FEQXJBNkcveWJVT3d0ZTBOUXRNS1JUWWNpckFReWVlK3JNUXM3NUpxblRJM3Q4cjYvWk9qM1Vuakd4cnFWY1l4bHB0NVZvOXYzeDRmZjVrRGtUS2FIaExMSXFmb01lNEZZVk5JbTI5WHNmOERrd0ZLT1B2NXlaYUN4UGhGTlZCL01nSC9KS3ZwYTUwaE5UZHI0QTg3Kyt4ZCtKT0dwRWtjTi9WZXdPRVhQY1VsU0dyVzYycnlFa2JrcEozNnJWRHRPakg1ZkhIeG5UQm9mVzB3RGkiLCJtYWMiOiJjYzRmMzliZGQ2NDk2MDUwNTc3NTQyZDc4OTgwNjhmMWNlMzJkMzBiYzBkNTdmOWJjYzA0MDE4M2QzZjE4NmVmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:08 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImhXWmNHNno3aWhDVVhyNll4QmFocGc9PSIsInZhbHVlIjoiOTFRV1BoUzJsWHlTOVlvYktPSGVvS0tDVVAxcU10ZGl0N0g1cmtJR2h5OE9qNnJKQlZUUUZsMTdaVnJuSmlDOGNRRFQzZi9kbmo4ZmFnNk04MGxqNm1TWm9EdmszeWF3SWRxd0haOHkyUDFPeGlla0JTMXFtLzJEU2UxeEZDSDNkaFZlM1NrclRXUWZSa2ZHL3BQSkMyMVhwWjMzeDk3SHI0TVRJcTlra3l5UmFsQjRJZmtxZmhTanR5ek1sZGFhVVZMZEY2Z3RubldSa09GTEdka2xETnRNQkd2UkZ5Ni9ka1MzaWdNMk1KRnFYR3NRVlFYb0dNRHFmUFdxYytJNjlFUlVxMmtOb0FCMVhza01iTEpFeWZTcU4vdFpod3I3MG1uQ20zUWUzTWtkMVZWc0tWQVVEOVpDaG9IZDVJWW9NT3JWcE5TdEFYK3VCTjI5UW5sNkVMNGRhZkRiazFnYzJueGZUL2pkbmhKNUpsay9pcW1oWFFoVEtoWTdHdlViR1cxKytVa0xoRE5uV0QzTlJ0NDdlNW9Ya2RHQUdJRDlacFYzRHl0b0ZzMXFxQndRdmlhd244dUVmcGRrNmxlYmxGMGZLTlNNbXFtT2lKeVY2QW5hWUVvVHZWQnl6OEQrRjFtSXRESStudnduanhzcForM2tvUFMrN3lobkRZbUgiLCJtYWMiOiJjNDcyZWE3OTUzNWRhYzFlNTdhZGE2Mjc1YmM3MDIwNzk0NWJmMTMzNzM1MzQ3OWI4ZjVhNGIwNzQ3MzY2MGNlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:08 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1839671613\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2051821408 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/21</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051821408\", {\"maxDepth\":0})</script>\n"}}