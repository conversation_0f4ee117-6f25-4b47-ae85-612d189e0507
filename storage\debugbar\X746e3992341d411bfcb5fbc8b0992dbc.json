{"__meta": {"id": "X746e3992341d411bfcb5fbc8b0992dbc", "datetime": "2025-06-27 01:14:39", "utime": **********.114, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750986878.681128, "end": **********.114015, "duration": 0.43288707733154297, "duration_str": "433ms", "measures": [{"label": "Booting", "start": 1750986878.681128, "relative_start": 0, "end": **********.042383, "relative_end": **********.042383, "duration": 0.3612549304962158, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.042392, "relative_start": 0.3612639904022217, "end": **********.114016, "relative_end": 9.5367431640625e-07, "duration": 0.0716240406036377, "duration_str": "71.62ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020640000000000002, "accumulated_duration_str": "20.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.071929, "duration": 0.019690000000000003, "duration_str": "19.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.397}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.100291, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.397, "width_percent": 1.696}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.106293, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.093, "width_percent": 2.907}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender/eyJpdiI6IjQxcTllMmFMZ1BDZEJMN3ljeTRtZVE9PSIsInZhbHVlIjoiZUlNelRveUhLTnZHUmtGVG1CSEp5dz09IiwibWFjIjoiMWJkNTkwYTdmMjEwMDEwMDc0OGY3NmM0ODY2ODI3YTk2ZmY3OTIyZjJiM2I0ZDEzMDBiODUzNWUwNTllZGVhZiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1165532999 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1165532999\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-198310454 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-198310454\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-68918910 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-68918910\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1095967671 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/vender/eyJpdiI6IjQxcTllMmFMZ1BDZEJMN3ljeTRtZVE9PSIsInZhbHVlIjoiZUlNelRveUhLTnZHUmtGVG1CSEp5dz09IiwibWFjIjoiMWJkNTkwYTdmMjEwMDEwMDc0OGY3NmM0ODY2ODI3YTk2ZmY3OTIyZjJiM2I0ZDEzMDBiODUzNWUwNTllZGVhZiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986865483%7C87%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVMTTk1aXg4cmlPVUxmaHJVYVlIVVE9PSIsInZhbHVlIjoiaVVsczJEQldab3Rld1gwYmJkN1R6eERYQStqOE9sTDJLbzFYQkZLWWVoZEVGUmRlZTFDVW0zcXRtVXYrckt5cGZ5UGdHTmhXRzhmQVIzSnY5MWx6M2FmM2pQVURJcTQrdW43dnZsbVEvVkRqUC91OXg2KzYxTjBkTmJnOEJ3cGZ0M25CSFlTejhsWElhU1llcVdHeC92ZHFwclhaajE1RGxBTFFHbzc4V0FENU04NTZMbW4vQVZ3QkdUbTlwK1k3alZUZW1NZzJUN1hEOWNDd25hY0MvdldldDNHTGpjeFNaNlNPMDlDcnd6Y3pUZnhFS1k5eFpIeUFVNEw5aWhxRHhpZTB0TmVyNHVxb0ppTGFpTUtxdU9uZ0plVUpHcUp1aGtXOW1PY2YxWVpLL2IyV1B5Z04vUGFIZGprREtEWTlKQ3ZhdGlMNjIwMGhvRkIvRGJOaUdWM1dLdzM2emNTbEVaTWRzTXdYdWl4ZjVjaHJHUk95ZmZ0Q1FhZTBxNTlKZ3pIVUk2YzQ4THhKVDN0NUlma1l5d3BPT3I4cFc2bHZ4bHQxemg3a29YTm9HaGs1aGI5dWw1OG53ZmJPa0dTUXlBUEJuOWhuMTZRUzk5ZjJKd2dFSVZOTEE4YkNwZjRseVpwUURUVXdza2JySWJLK2YySUtxSVlSREFneG5FM1ciLCJtYWMiOiI3YTM0YjkxNmQyZDE2ZmMwNWVmMzI4ZTUxMzkzZTdjMjNlN2Y0ZjgzZTM4YzcyOTMxZWJhYjBmN2E0NjM1OTc1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdjTHUyYWdNZUtOM0cvY2YrWmp6L1E9PSIsInZhbHVlIjoib1V2bG84elhCY2o0S0IwRUowM0dmSC9POUV4dDFGV1BvV3JWTTlabzdQSndmTmJ0Y2JIUWZWaFJ2WlZ2VEhxMjFGb1haeFAvbmZ6V2p5Rkt4NTBWZ3N6Y3N3VXd6MXhpZmxCeUZoL29ydXBIaGcvOEVBc0FUdW1VNTd3MkF6dmtSSllQdkp6RzZNNEVUNHJzQkJ3TkVYbzFWbExXREhvV3JHR2VFV3dqQUNxbjM5TEc0YXpQWXFaZncySFpYN2VzamtqSDlSZmdvek01NmpBaHlmb1VRMWs5OTdEaUlzMU95WHBBeUNudGdEZ3hzZFA4T2pnc05GYkFhM0MwY21rWmRncUNybUNnYUg0ZTdURlArUkFLK3duWjFKNEFEYzNFQTRSQTA4em1ZUTJvVERWbTJhLytvT1cwcHhJdHdCdzgrdmsxWVhIRWtlVTMvZXVOd1RMNkR0REtkakFVcnJaaWlTMmZPSk5WMlhIRmNpMG1jVkpVdzRNVEFKOFZka1NvZ3h5ZEwvZkJ0MjZNaE01Z1U4Qmx2Y1RqRlNoNDRnaE03c21hMW9laXBMdHNGL2w2ZzRLZHZGNkQ0aGg2ckZJeHVQZXBONDc5VDlJV2ZSQTNERUFCRk1HY052QnlLUlF5ZVUzUmJUcW1TZ3dpOURNa1hxMUQ5Qjk0NlM5TmhtdDQiLCJtYWMiOiIzODJkNDBhODljYWMwMDIxMDFlZTJiNjFiNjEzYmRkOTRiNDgwOGNhYTEyYjJhN2FiMDcwMjA2YTM5NGVlZDVlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1095967671\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-198882879 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198882879\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2121795172 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:14:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxzbFJuOVdLUUdvNWhsQ2VDajRKdGc9PSIsInZhbHVlIjoiS1ZYVEhVRUlzYVdvR0NQcU95OFVCVml3V2NYQnBaRDJGRUhoVDFnVnB5SVUyRUZic28wR3MrQTc5eis2OUNuZW0weTJNajFqV2tnNlRBTjdVMTdKSFZUcHFzdWtjMFZZY2l4MlpUQzR0MGcyWlcrVlhlSnhVbjJydDhIZDJyQ2k5Vy9ZWTYrU3BHdFE2Nms5eWI3ckl4dVBmTWJ3dXp4b211R1BDcEJQVUgyd1FLV1BsTm51WWlFUmNHR0ZwaGY0Qk43eFFqL0hldGFHTzlvN09iOFB5M2VrLzlsdzBtVUY2RXJ4ZGZCOVJKWktvemUvQ293NG43MURHNHdpdEFEME13dy8zVGw5ZlFGME5qOWwvNFlJS1FGUzhKV3BRYzBaSFVJalNwek11MEdpS05uTHF0eklhdGVMQ0hBQTFLTEEyWDZrV2diaHQ0aWI0NUVjWW9OTVhJNGo5K2NVd3NCeXM3WmcrTmFZdG9NSW9sSGljVWhDYnV4TWcwL2xOelRyV3FtSEZEU2lOajJTZTR0ZVdsR082Y3l2WmNvZExJdGtRZENtT2tWYVUwcTJKNUMwL0NEdXI0STQ5djZqSkVuS2hkbFJTeGtFRGQwZWFyd20yd0IzWC9rZFB4UlpxNEp1VUpIYzZyUmtkdEdSNkc3NW02OUl3R2duZVh0OWxzVm0iLCJtYWMiOiIwMzI2MTgwNTljMDIxOTcxNGMzYmI2OGNkZDZlNGJjZTAwYTJkMDFjNmY4OTk5NTcwNmFkNGRiMjVkNTY0OTc3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:14:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijcwa3ppczlDeElaVUdHTm9rcmdMVWc9PSIsInZhbHVlIjoiU1VuWC9uSUQ5MFp6aTFQL3h2THhEVHlqck9SR1B5ekdmQktnRnNuMWQ1WGhoLzlGY1pNR08ra1JuNnl6cXpRYWdvM1ZDajE1SEdmNHB3dlJtM3l1aVNiOUpmeGRERjM5aVNJdGlDRWxnMjZPTkhHa2Q0L3FrYmNXbjBtcWNNMXNSS3J2SURKcndqcUFaK0ZOVC9qdzVPaCtsdER5S1NZM25ZN1R5aGMrMUZsR1FiamRna0xjRmF6NWkzbEVTMklQT0pMc0o1TWdOT2t3MllTZ0l0Uy9WWjE4ZFA1Kzg2ZDZQaEJoeFpJc3ExMERxSWNPWklsQ1ZoKzFxMU03RlEwRWZlMWUybGtRVjZFdVY5WTJKeFRCY2dZckNyT3FubkRGVzdxTVlPY1J6QjYyZVJQNUtvOTdGcWN4VldXVWlYRHRoUythTXQwS1FFK2U5UGJwdWY2UCsyNHUrc29GaXJHVlZ1d0ZQY2diRWYyc0hlL2ZhV3pRM3JFTmxkMlRYVmZnd3JPaG13a3lRUUovSFFLcnI3OC9hNWhwZ1ZkbzZKWjFwQjRocGMxbDRMaUg1VFVHSWVEaUVqQnFRR2gvYmMraXJYc2ZzNUowRURsTWhBaGdrbldBNiswN1huSGEwZ2c1WEw3L1ZsLzF3ZHh1cEJ4cWpkakIzYkNFZlUwVzhJbngiLCJtYWMiOiJhMDAwZmNiYWE1OWI5NTNhNzg0YTJjZTNlNzllY2E1NThjZDVjN2ZiZmQ4ZTNmYjE2ZDQ4ODEwOGZkMmQ4ZTA5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:14:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxzbFJuOVdLUUdvNWhsQ2VDajRKdGc9PSIsInZhbHVlIjoiS1ZYVEhVRUlzYVdvR0NQcU95OFVCVml3V2NYQnBaRDJGRUhoVDFnVnB5SVUyRUZic28wR3MrQTc5eis2OUNuZW0weTJNajFqV2tnNlRBTjdVMTdKSFZUcHFzdWtjMFZZY2l4MlpUQzR0MGcyWlcrVlhlSnhVbjJydDhIZDJyQ2k5Vy9ZWTYrU3BHdFE2Nms5eWI3ckl4dVBmTWJ3dXp4b211R1BDcEJQVUgyd1FLV1BsTm51WWlFUmNHR0ZwaGY0Qk43eFFqL0hldGFHTzlvN09iOFB5M2VrLzlsdzBtVUY2RXJ4ZGZCOVJKWktvemUvQ293NG43MURHNHdpdEFEME13dy8zVGw5ZlFGME5qOWwvNFlJS1FGUzhKV3BRYzBaSFVJalNwek11MEdpS05uTHF0eklhdGVMQ0hBQTFLTEEyWDZrV2diaHQ0aWI0NUVjWW9OTVhJNGo5K2NVd3NCeXM3WmcrTmFZdG9NSW9sSGljVWhDYnV4TWcwL2xOelRyV3FtSEZEU2lOajJTZTR0ZVdsR082Y3l2WmNvZExJdGtRZENtT2tWYVUwcTJKNUMwL0NEdXI0STQ5djZqSkVuS2hkbFJTeGtFRGQwZWFyd20yd0IzWC9rZFB4UlpxNEp1VUpIYzZyUmtkdEdSNkc3NW02OUl3R2duZVh0OWxzVm0iLCJtYWMiOiIwMzI2MTgwNTljMDIxOTcxNGMzYmI2OGNkZDZlNGJjZTAwYTJkMDFjNmY4OTk5NTcwNmFkNGRiMjVkNTY0OTc3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:14:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijcwa3ppczlDeElaVUdHTm9rcmdMVWc9PSIsInZhbHVlIjoiU1VuWC9uSUQ5MFp6aTFQL3h2THhEVHlqck9SR1B5ekdmQktnRnNuMWQ1WGhoLzlGY1pNR08ra1JuNnl6cXpRYWdvM1ZDajE1SEdmNHB3dlJtM3l1aVNiOUpmeGRERjM5aVNJdGlDRWxnMjZPTkhHa2Q0L3FrYmNXbjBtcWNNMXNSS3J2SURKcndqcUFaK0ZOVC9qdzVPaCtsdER5S1NZM25ZN1R5aGMrMUZsR1FiamRna0xjRmF6NWkzbEVTMklQT0pMc0o1TWdOT2t3MllTZ0l0Uy9WWjE4ZFA1Kzg2ZDZQaEJoeFpJc3ExMERxSWNPWklsQ1ZoKzFxMU03RlEwRWZlMWUybGtRVjZFdVY5WTJKeFRCY2dZckNyT3FubkRGVzdxTVlPY1J6QjYyZVJQNUtvOTdGcWN4VldXVWlYRHRoUythTXQwS1FFK2U5UGJwdWY2UCsyNHUrc29GaXJHVlZ1d0ZQY2diRWYyc0hlL2ZhV3pRM3JFTmxkMlRYVmZnd3JPaG13a3lRUUovSFFLcnI3OC9hNWhwZ1ZkbzZKWjFwQjRocGMxbDRMaUg1VFVHSWVEaUVqQnFRR2gvYmMraXJYc2ZzNUowRURsTWhBaGdrbldBNiswN1huSGEwZ2c1WEw3L1ZsLzF3ZHh1cEJ4cWpkakIzYkNFZlUwVzhJbngiLCJtYWMiOiJhMDAwZmNiYWE1OWI5NTNhNzg0YTJjZTNlNzllY2E1NThjZDVjN2ZiZmQ4ZTNmYjE2ZDQ4ODEwOGZkMmQ4ZTA5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:14:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2121795172\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-612053595 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/vender/eyJpdiI6IjQxcTllMmFMZ1BDZEJMN3ljeTRtZVE9PSIsInZhbHVlIjoiZUlNelRveUhLTnZHUmtGVG1CSEp5dz09IiwibWFjIjoiMWJkNTkwYTdmMjEwMDEwMDc0OGY3NmM0ODY2ODI3YTk2ZmY3OTIyZjJiM2I0ZDEzMDBiODUzNWUwNTllZGVhZiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612053595\", {\"maxDepth\":0})</script>\n"}}