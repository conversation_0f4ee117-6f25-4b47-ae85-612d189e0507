{"__meta": {"id": "Xfcc6d68aead4e026510f16a4720e4e09", "datetime": "2025-06-27 02:27:21", "utime": **********.32303, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991240.827577, "end": **********.323046, "duration": 0.4954688549041748, "duration_str": "495ms", "measures": [{"label": "Booting", "start": 1750991240.827577, "relative_start": 0, "end": **********.230603, "relative_end": **********.230603, "duration": 0.40302586555480957, "duration_str": "403ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.230611, "relative_start": 0.403033971786499, "end": **********.323048, "relative_end": 2.1457672119140625e-06, "duration": 0.0924370288848877, "duration_str": "92.44ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722208, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025039999999999996, "accumulated_duration_str": "25.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.26689, "duration": 0.023809999999999998, "duration_str": "23.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.088}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.303644, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.088, "width_percent": 2.157}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.312234, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.244, "width_percent": 2.756}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1867403398 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991174265%7C33%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldBQUh1VEtjS2djck9RUzk3RUFDT1E9PSIsInZhbHVlIjoiNG55Um9NSjNiR2RDTy9SUkFBbTl5Ym5DVm9UdWJ4dTdxQ1NmaUJHY21paFB1MFdBYStPL1dSVmhtalZrY0RFRzg3ZnhFOWFoaEk4QkFsSzk0N0czWXVUYU1tS0tISGhIMGFJTCtoT012T2xNK1JzYVNMb0QyMFlEdUZCbGlla3VqaDJoYk9RNHI4eGZUc2lpZ2hVRTcvTVJ6UVpUTUphczIwYUVCRkJFRWJXdnFNYlExK0IrOFBielowUUJCdUppbWNKdkhUblM3bEhScHIvWklJTFBIbmgzZUVYSzFXTUlud3QwNnJ6ZmdiWXdYY1pqeFE3K1QxYkNNZURWdVpGMWx6NGtCM051L0pBZ3hRZ2hIOCtIdTJIZVFsOHlSWjlJSWJVQ2xMWHh4L3ZOTHpSTElxbnJkSUNBT1pMMENBZDkwb1VHOFkzMm9yYnpNM2wreXhCbE5YWVc5SVJia0VaRkN1UzY3dmVsWTJHbm84K2w1K2dTb2pJeFdHWk0wcG1lUEwxUWZjbkFEUndFRFd0SnVHanZKRXJBSGp3VFlhdi9oTmhGaW1kMnVoU3B2VUFzYXY0elBzYVpDNm9sMmpQY21sdE9XKzN3YjVDZHRUd09VN3A1VGJYZHBsWG80dkdFaktwdXphaFBDQmNYZHg2dlgzZnNvNUo1dFozQ1grS3QiLCJtYWMiOiI5Yjg2ZjlmM2IzOGEzMWJlYjg4YzkwMjkzM2U2YmIxYmNhYjcxYmU3YTFiNDFjYWZjZTM4OTdlYzUwZmYxMDY2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Img0cHB0cEVLeVM1YjRyMkhSYXNnSHc9PSIsInZhbHVlIjoidjRkNVRCWFdxVWJqVlZTT0kxbVFPVmVTSFZoS0RQOE56RTlmK2tFZmltbGNHTWIydldiWFRFeVhGaUlFRFhvZGpNMVpqTDQ0VTdFR3gxTndxdjNZNEh5S0FxV3Fma0RzdXpleDJ5K3VYcTAyWEN6NlJOQlhHVnhDSlREZWJLaVpqQ0tNb0tSSEZWWklERG5BNENBVUY2WGNYTjI4aVcwYzMwNVBZbEM5QUhqaExRUWhyUTZ3U2ttMjZxYU42c3RoaGFjSmRzbU9lWG5ZTHFQSkVVcTRtVGs3TXRrZlkxZUxMZjdYdHN3cXQ4aXlndFhCanI4MW95bzdVdG9MQ1RQTnAvNDk0Q3ZtNmhPcTlNNkRsUXR2VmVCTVBqSUVLTThrYkNkMGxMUkRLQUx2eDNBdG45NUdFdUg0aHp5RjAyV0ZDOEtqSklJYUc4eVdxN2J2MmsyM3JvR0dQUXFreElISExqLzU2UFk4NVhxTUV2aDM2Y0JtZUNUa1BiWk1NTE1LVldsbmdqanFDOExWOVVuNWxITTZBNVJHbWJhd3FNTUtseStTdDBWU2N0b25nTGpiRGxnaUtoaXhmVGN0THJ1Rm1OMGExamN5R1ladDVoWFNEaTc3TC9OV1lHeDNiNjcvMDd4K3d3S00zNjJaQTdxRlFncUViamZ2eHhxbjNiSEIiLCJtYWMiOiI2MmE3OTRhYzBkNDRmZmVjYWVkOTU3YzVlN2MyNmE1OGI2OWZlZTU0NWFkMWZlNDkwZTQ2MWUzMmM2OGRmNDg4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867403398\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-623904540 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623904540\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1891481380 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ino5Qml3ZmhDV1N3TjZBL3lXZzU0RXc9PSIsInZhbHVlIjoiYjdFS29KdlBSS2N4cWNTM2JYb3lzb0JzSkZTeENGNXVyQk5OekVBZDlQQzF0M1hIU3BZd0M5MkZ4MEhBUWdFeVBOTnhMRFg2eVJjcFhSL3ZTWlQyN3JTZEs1WDIrSjN5UDNPY0NxcG43YWczZkk3QTlMNHJ1dVg3SFlGSHFyOWRjd1VyQXlNWnVqT3VEZTFwNkdIa0xqUkdwUUxEdlEwUHBjR1B2K1AwSXpSSWVJUHgrekdsK1JvUGpraHVtN3lZTXV6bDBYYmNtYnlOdGpZa3JQMmpMMWs4Mkh3ZGhzUENSMjdkTHY1eGZWdDloUUhoazVEMDUwZ3dzZThaSU44QTROcGhveVR6SjFGamZZaUthdXVoQXY5ZDdCTEpzZ0ZmYjZBMUFEb3k0RisxZ0dNeGYwaXlmN25ocnlwcHpLQ2VmZGViZURCcWJ3NUttTDZQVkNtcFFtSXluUVJLNDFKMlM0eGpvRTlyNzc3TjQwMzlWZVM1Vkhla0c2VDBZYWMxNkRFQVE2U0RHNFJCYWQ5a01xejdnV2hHMFM0OE9xOXhKZjdDaEVWVFcvOEJjY1NnVVZrWXIzdnMvbjl2UGFFNndhTCtPU0JoWkVQT3ZYakxtNlFUMTR6TmR3YXZ0Yzc4U3J6dkJhdSs0WUx2TWxjcWhHSXkxb0VCMDJEYVpubGMiLCJtYWMiOiI4OWQ5OGU4YjQ2YzQzYzE5YzExZThiZDEwMmI5MGUwNzEyOGMwMWFiYTM4MGQ1ODMwMmIyYmEzZjZlOGYyMjgxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxXL1pnTHFFbmU0dlU2SVVvUWl1enc9PSIsInZhbHVlIjoiR0hzbU1yUmxOS1ZpTHlacitFK3l6U1Vyak1QM25CNDZFY1ZVaURSdzRCR2kxSWhaeUwzMWwveFRpM1BXV0MxY1U5MFZVenlTbVBtR3ZHSVlwTFVmWUVtV1hickJCUHRNTlc4T2YrK0ZwWnU3a1RLUXVIaXhkTjFQbllBR2s5YnNtd1dzVGFObTh1YnE2c09HRFVwUHRLYlNGZzBJZ05FQkdtazJSMWN6OWQrdndPRUZUVWY1bVZYdm40MURBNnlwN0VTVmEyOTRpc0hxYlI2MDV6QU1uZ0tscWQ5WDJPVWhJY3F6VG9HUWhuTldwRlNJUmlValpkQjdybnhDQ2hsQjJlSkdwUGNvUnZmV01VT0hpbXM5M2tyZU9ySGJVY0xGdWYxd25RLytTSTlwVUNMUThmSmptb3Q0UllDVm93M01hNGxLM1RGWlQyazJvYWdjTU43WStZdUlJMVdpeUpWQkViWGFuUDNKcisyQm5nNGcxUUdHRE9xNkhlVFp5b2dIZnpjMEFEK2dwK0ZTUGRQNUo2S3k4bzBpRFdnWGR3MlllZ3JoNG9kZkplbUVyWHpIRUFUZXNZSmFPcjdpdUROS21zcXNxcHZGK25oeG55RkkySmszY0lBZWtOUGNVODg1cjg5eU1ZTEhRT2tzb0ZNYUJEWjJ0NGFUcDQ4V3pMaVUiLCJtYWMiOiI0YjAxNmM3YjNjMDY2M2MzNjA0OGEyMWZmZGY2ZmM5NWMxZTgwMmJjYzY2MjMwMGE5YWJjMzdjYmQwMDg1NTljIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ino5Qml3ZmhDV1N3TjZBL3lXZzU0RXc9PSIsInZhbHVlIjoiYjdFS29KdlBSS2N4cWNTM2JYb3lzb0JzSkZTeENGNXVyQk5OekVBZDlQQzF0M1hIU3BZd0M5MkZ4MEhBUWdFeVBOTnhMRFg2eVJjcFhSL3ZTWlQyN3JTZEs1WDIrSjN5UDNPY0NxcG43YWczZkk3QTlMNHJ1dVg3SFlGSHFyOWRjd1VyQXlNWnVqT3VEZTFwNkdIa0xqUkdwUUxEdlEwUHBjR1B2K1AwSXpSSWVJUHgrekdsK1JvUGpraHVtN3lZTXV6bDBYYmNtYnlOdGpZa3JQMmpMMWs4Mkh3ZGhzUENSMjdkTHY1eGZWdDloUUhoazVEMDUwZ3dzZThaSU44QTROcGhveVR6SjFGamZZaUthdXVoQXY5ZDdCTEpzZ0ZmYjZBMUFEb3k0RisxZ0dNeGYwaXlmN25ocnlwcHpLQ2VmZGViZURCcWJ3NUttTDZQVkNtcFFtSXluUVJLNDFKMlM0eGpvRTlyNzc3TjQwMzlWZVM1Vkhla0c2VDBZYWMxNkRFQVE2U0RHNFJCYWQ5a01xejdnV2hHMFM0OE9xOXhKZjdDaEVWVFcvOEJjY1NnVVZrWXIzdnMvbjl2UGFFNndhTCtPU0JoWkVQT3ZYakxtNlFUMTR6TmR3YXZ0Yzc4U3J6dkJhdSs0WUx2TWxjcWhHSXkxb0VCMDJEYVpubGMiLCJtYWMiOiI4OWQ5OGU4YjQ2YzQzYzE5YzExZThiZDEwMmI5MGUwNzEyOGMwMWFiYTM4MGQ1ODMwMmIyYmEzZjZlOGYyMjgxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxXL1pnTHFFbmU0dlU2SVVvUWl1enc9PSIsInZhbHVlIjoiR0hzbU1yUmxOS1ZpTHlacitFK3l6U1Vyak1QM25CNDZFY1ZVaURSdzRCR2kxSWhaeUwzMWwveFRpM1BXV0MxY1U5MFZVenlTbVBtR3ZHSVlwTFVmWUVtV1hickJCUHRNTlc4T2YrK0ZwWnU3a1RLUXVIaXhkTjFQbllBR2s5YnNtd1dzVGFObTh1YnE2c09HRFVwUHRLYlNGZzBJZ05FQkdtazJSMWN6OWQrdndPRUZUVWY1bVZYdm40MURBNnlwN0VTVmEyOTRpc0hxYlI2MDV6QU1uZ0tscWQ5WDJPVWhJY3F6VG9HUWhuTldwRlNJUmlValpkQjdybnhDQ2hsQjJlSkdwUGNvUnZmV01VT0hpbXM5M2tyZU9ySGJVY0xGdWYxd25RLytTSTlwVUNMUThmSmptb3Q0UllDVm93M01hNGxLM1RGWlQyazJvYWdjTU43WStZdUlJMVdpeUpWQkViWGFuUDNKcisyQm5nNGcxUUdHRE9xNkhlVFp5b2dIZnpjMEFEK2dwK0ZTUGRQNUo2S3k4bzBpRFdnWGR3MlllZ3JoNG9kZkplbUVyWHpIRUFUZXNZSmFPcjdpdUROS21zcXNxcHZGK25oeG55RkkySmszY0lBZWtOUGNVODg1cjg5eU1ZTEhRT2tzb0ZNYUJEWjJ0NGFUcDQ4V3pMaVUiLCJtYWMiOiI0YjAxNmM3YjNjMDY2M2MzNjA0OGEyMWZmZGY2ZmM5NWMxZTgwMmJjYzY2MjMwMGE5YWJjMzdjYmQwMDg1NTljIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891481380\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}