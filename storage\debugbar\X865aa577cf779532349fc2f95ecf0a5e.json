{"__meta": {"id": "X865aa577cf779532349fc2f95ecf0a5e", "datetime": "2025-06-27 00:25:42", "utime": **********.756586, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.241434, "end": **********.756602, "duration": 0.5151679515838623, "duration_str": "515ms", "measures": [{"label": "Booting", "start": **********.241434, "relative_start": 0, "end": **********.679515, "relative_end": **********.679515, "duration": 0.4380807876586914, "duration_str": "438ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.679525, "relative_start": 0.43809080123901367, "end": **********.756603, "relative_end": 9.5367431640625e-07, "duration": 0.07707810401916504, "duration_str": "77.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02349, "accumulated_duration_str": "23.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.711399, "duration": 0.02246, "duration_str": "22.46ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.615}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.742746, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.615, "width_percent": 2.214}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.74875, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.829, "width_percent": 2.171}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-237285940 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-237285940\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-342977847 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-342977847\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1942484147 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1942484147\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1683687232 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983781068%7C53%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii9YR3J4bFkrbTVCTVFBam0xd05FT2c9PSIsInZhbHVlIjoid09remR3M2RjdFVXUEVUQlF1aG5jR0tVSTdKRkJGYldzSGhQY0VnV3M2NzFkR0s0UFErZXJNSlRKSFZJUTAyRlZVTjFlVjcrRHMvL1BWaWhRRHlDNFRlcnJCNElwcWxnaEUvdGNqNkMwblloWWorMGVqN3RtMlRoQ2ZZaElFTkFodzM5SVhpYkEzMXdNbWpxYkFIUDJ0VExWZGlLYlNvSHdRamZ0WlJMNnZWRWUvWFZUWCtKeVIraWRKcW1vZXhMeE00akp3UkgwWTlVMkRjT0l2MFhJK3RiZXVSSXRlN2hDQ3R4dis1MCtSL2puS3A4MTkwQXh5ck5UdGZWVW9ZZEprYWVQQWZXQ1kvRk5PZUtMZ3RiTFlnQWdKLzZOQTlIZWE2Umo3OFcwQzZ0OFBoSjRSZGRHb3IvTlV6ZVl1ek9JK09wRmJkdTQ3TlJ3VmM5V3FaaTZBVWI2cXo0Sjk3TFN4N2JmU1FBdkc1SEt4SkJrWFZpa2tUTmd3UXFkbjFvWXFpSFZYQVVjT2tVdHBvZUs4NlZMbDFRekk3eE1WMDFpMzVhR08zR1BCQzYzT1A5TW1oMzRnNFlsUU1ueUNWMEFiZ2s0bnUxbWN3OG9taWVTcTNhSVRld3RuSVJtU1lpZWs0Nnljc1lvbkI2OEh6MnJMZXR2S0R6eTBORzRkL0EiLCJtYWMiOiI2MTBiNDdhNDZkNWNiNDRkMmZlNjlhM2VkZmFjM2ZkZjA3ZmIzYmNmMzFjNjFlNjY0ZmY3MTE1YWI2OTRhMWUwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilo0YTlJOWJMYytoNTB2QnVMYnBYV2c9PSIsInZhbHVlIjoiMTlyK3h2eTh4dHpJQ1V2cURDN0MySzFESmVqZW1lcDFuRnRRdUNuZHRSNEJ3aURxZWV6U2htYWhCdyswaVJrSW92d2lKTTJwbUlTZzM1dS91SGZrNzZVRGZRanBHdVcvc0phN2s3OVhHWWU4bHdaWU81dHdCR1JNbzFNYkcrS3M2MHc5TXVMbkNGbWV4WHd3RTRNS25IbzNOZTh0anlpdzFpL1QzR2NNZjVEbXI4dGl4SlpvTkZiZERGcG8vT2dPdUl2bHVHZ25NYndHaC9yWVNaVEIxR2d5Qk9PZFlFTHZYNTJ0Sng4bU5EMlllYjUxUFRwL1o4T2dsUmEveTI0ckZYQVZrNkZMQXhnYjRmdzVaTzhRRXRhbWpZNGh2dW1oemFWZ2lUMHBwa1NFT1ZNSzdVa3IrY05jdjJUd3JuYXlnU0tJU08yNFVHTnN3Wm1qNTE0c29XYTBVSmpXVldyVWU1ek4rcEtCNFpkR1Y1T0prRlhiZTlxRVE4MVR1Q1g3TTh5all0emVmTjZJdUk1eW9FYzRoSnFQZEo5eDlGeG95SXVRaGNxaEFldW1PRlVIVWhra1lOU0JDV0Yxc1hVNWJ6N2s3OUc4Qml5eVluclpndDFWSXpyeVp2SVBYV0FYaERRVENySGt1Wk8xQTJETUhJdmlyOHBoaExYQ2JCdisiLCJtYWMiOiIyMDE3NGE3MjRmYjI3ZmVmYTIyZjQ5NjU0ZjM2MTVhNTkwMTQ0NWY0ZjY5NjQ2OWY3ZmVhMTYxYjY5NWQyN2UxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1683687232\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-857271566 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-857271566\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-759270119 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:25:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlhnWFFwK25BSEoxREdnZndjMXZwb0E9PSIsInZhbHVlIjoieXFxOHo3MUdwMkZXbGtZdTlmclJhRGU1Qk9aVXpvS0h5ZnRJR0RXMlJ1UTVEbkpmMmtYRll3K3BDaUdNbi82SXlDbTVmWElFdDRDc0h2RjNGVm9maDVGQ1RIWWpXRUd3Y0ZJMm5jM1p2OWtKT2N0Wm9odzRWSVBSSWxDVHdxSVMxR2M4MTBUY1lDejBaMTB2QUtMTFhzc0dRN1BnQkRTcER5UTlEbnVZcFRYNWlTNUlaUFZwZW4zaFFTWWNLTjAwb0Y5V0MrTXoxaVVLcFNybmZxc3dud09HaGdlTTFHR2l4V1ZMMlRrZjZEUjBIalF3cHAxVUN4SFBwYUl1ei9LS2hTWU1uMmg1OTI3dFdBTFczUS94bndwTjhycEUwWVU3dS9OVzlHVk1tdXdkVUQ0STBOT1hSbFNLdUsxbG4wMlliMysrUzBFNWdpT1JQQS9LZ2ZVZnZGSVdiOWFWb0ZhbzA4TUxFaUloQ1EzcHFjTXozOTljQytRbXdyRmR4TEQxUVlGWXFuNWc4SlJDNkMvdTJKZ2RDd0dOWkRhTjMyVzV3d1JkdlJKM0VZNUlRZngraE5ILzhKSCtuUHRpa29XUSsyZnB2bmxXM244K2tkWXpheklyMjJJQXJWUmlPN0lOWGJMM3NmSHpKeVpEYXRKaDJzQ0wwU3J3a2VDUksrMHUiLCJtYWMiOiJjNzA2NjM0ZWVmNWI5NzliYzc1OTlmNTFkMWZjM2NhYmIzOGYzZDJhZDdjODA1NTNjMmIxNWIwYzA1YzQ4ZjA4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:25:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkNaR0FCcCt4TUtaaDBXbHRMb1l2OFE9PSIsInZhbHVlIjoiNzBNNDA5MDJoZ0NVVXNzSFE2azNXMEdqU05NNFUrZ0RoUm9XZmt0NUZpc2tVVUFMRVZId0hwaXU0aEo0ZTlUTUloUGUrL0pVYUhNb3RWMk9DbVZBbHYrU3BWaEIzb1Q1SzNnSTJEOFVINEFVM3FhMVZ4SHlMZDNkdXFoa1Q1UDlGTzRuRVo3UXIzd29RekRGS2kzY2pBZjBLblJrdVZnVmY0NGQ1bFhMeEx3aFBUMUNka05Tak15dGhYTUJ0aWpwTml6bk80c3pNekQzNmlLOVlLTkN6TVJ2VXFiSFFjRTk1U2d6bzVTQlFKV3dPdThqSXdCWGRDcUFONVlTR2xuREkzbjRFTmJEL3FsRG9uVjR2QjJmeUZ2VTVRa1daMXJqOXZ3L0VqdENzcGd2SE92V1JYdXczSTdlckZiWkY3MklKSlZtUG9xS3N6RmVuMVdocWhweDhKalZlWmVUZ3daWVFjaUd2VzBpWGw1VzFiUXRuTXRNNExzTk9SdytLRk5QT25hcERzZnZkc21PTHVyS0E0cXc5ZllnV1B4NkdBUkpicUk3TjFrYzVENVlUR3MvQm5NNHo3M3pUeklTa2o1QS8rZVBQWGg0SW13TVo4NkJOeHdFY3FEaCt2Mm9mVGtZSnF6QU56L2REdGFDdnhwR0xuNG1xamllL25PcEdrZE4iLCJtYWMiOiI5N2E5NDQ5ZDg5YjFjYzI3MWYzYzlmMjFhZGNjMGEyYTdkMGY0ZmU4ODlkMWRiMGU1OWFkODk2YjM3NWY0YTU0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:25:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlhnWFFwK25BSEoxREdnZndjMXZwb0E9PSIsInZhbHVlIjoieXFxOHo3MUdwMkZXbGtZdTlmclJhRGU1Qk9aVXpvS0h5ZnRJR0RXMlJ1UTVEbkpmMmtYRll3K3BDaUdNbi82SXlDbTVmWElFdDRDc0h2RjNGVm9maDVGQ1RIWWpXRUd3Y0ZJMm5jM1p2OWtKT2N0Wm9odzRWSVBSSWxDVHdxSVMxR2M4MTBUY1lDejBaMTB2QUtMTFhzc0dRN1BnQkRTcER5UTlEbnVZcFRYNWlTNUlaUFZwZW4zaFFTWWNLTjAwb0Y5V0MrTXoxaVVLcFNybmZxc3dud09HaGdlTTFHR2l4V1ZMMlRrZjZEUjBIalF3cHAxVUN4SFBwYUl1ei9LS2hTWU1uMmg1OTI3dFdBTFczUS94bndwTjhycEUwWVU3dS9OVzlHVk1tdXdkVUQ0STBOT1hSbFNLdUsxbG4wMlliMysrUzBFNWdpT1JQQS9LZ2ZVZnZGSVdiOWFWb0ZhbzA4TUxFaUloQ1EzcHFjTXozOTljQytRbXdyRmR4TEQxUVlGWXFuNWc4SlJDNkMvdTJKZ2RDd0dOWkRhTjMyVzV3d1JkdlJKM0VZNUlRZngraE5ILzhKSCtuUHRpa29XUSsyZnB2bmxXM244K2tkWXpheklyMjJJQXJWUmlPN0lOWGJMM3NmSHpKeVpEYXRKaDJzQ0wwU3J3a2VDUksrMHUiLCJtYWMiOiJjNzA2NjM0ZWVmNWI5NzliYzc1OTlmNTFkMWZjM2NhYmIzOGYzZDJhZDdjODA1NTNjMmIxNWIwYzA1YzQ4ZjA4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:25:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkNaR0FCcCt4TUtaaDBXbHRMb1l2OFE9PSIsInZhbHVlIjoiNzBNNDA5MDJoZ0NVVXNzSFE2azNXMEdqU05NNFUrZ0RoUm9XZmt0NUZpc2tVVUFMRVZId0hwaXU0aEo0ZTlUTUloUGUrL0pVYUhNb3RWMk9DbVZBbHYrU3BWaEIzb1Q1SzNnSTJEOFVINEFVM3FhMVZ4SHlMZDNkdXFoa1Q1UDlGTzRuRVo3UXIzd29RekRGS2kzY2pBZjBLblJrdVZnVmY0NGQ1bFhMeEx3aFBUMUNka05Tak15dGhYTUJ0aWpwTml6bk80c3pNekQzNmlLOVlLTkN6TVJ2VXFiSFFjRTk1U2d6bzVTQlFKV3dPdThqSXdCWGRDcUFONVlTR2xuREkzbjRFTmJEL3FsRG9uVjR2QjJmeUZ2VTVRa1daMXJqOXZ3L0VqdENzcGd2SE92V1JYdXczSTdlckZiWkY3MklKSlZtUG9xS3N6RmVuMVdocWhweDhKalZlWmVUZ3daWVFjaUd2VzBpWGw1VzFiUXRuTXRNNExzTk9SdytLRk5QT25hcERzZnZkc21PTHVyS0E0cXc5ZllnV1B4NkdBUkpicUk3TjFrYzVENVlUR3MvQm5NNHo3M3pUeklTa2o1QS8rZVBQWGg0SW13TVo4NkJOeHdFY3FEaCt2Mm9mVGtZSnF6QU56L2REdGFDdnhwR0xuNG1xamllL25PcEdrZE4iLCJtYWMiOiI5N2E5NDQ5ZDg5YjFjYzI3MWYzYzlmMjFhZGNjMGEyYTdkMGY0ZmU4ODlkMWRiMGU1OWFkODk2YjM3NWY0YTU0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:25:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-759270119\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-460582343 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IitYN0dwbmp4OVllNzZ6b1ZaQzlWMFE9PSIsInZhbHVlIjoiSmg3WU5xVU9GZEpGaG5hVUhqcnRsdz09IiwibWFjIjoiMjJlNDQ4NjMwNjIxYjhmZGU0ZTJjODZhZDVlNjRjYzQ1OTAzZDg5ZjkzYmU5ODI3MGVjNTZjZDJkZDgzYzVkYiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-460582343\", {\"maxDepth\":0})</script>\n"}}