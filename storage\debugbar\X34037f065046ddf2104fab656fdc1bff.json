{"__meta": {"id": "X34037f065046ddf2104fab656fdc1bff", "datetime": "2025-06-27 02:23:45", "utime": **********.022181, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.578482, "end": **********.022195, "duration": 0.4437131881713867, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.578482, "relative_start": 0, "end": **********.957942, "relative_end": **********.957942, "duration": 0.37946009635925293, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.957952, "relative_start": 0.3794701099395752, "end": **********.022197, "relative_end": 1.9073486328125e-06, "duration": 0.06424498558044434, "duration_str": "64.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45409464, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.017689999999999997, "accumulated_duration_str": "17.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.986553, "duration": 0.01647, "duration_str": "16.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.103}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.011753, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.103, "width_percent": 3.787}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.015717, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 96.891, "width_percent": 3.109}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1688631818 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1688631818\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-341526239 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-341526239\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-632002377 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-632002377\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-729334540 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991019246%7C19%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpSVWRRSjZuUStWVkEzaUxTakFPNmc9PSIsInZhbHVlIjoiNzVNb0tSOXBsZHN1c0JrR2FibktFU3NOSDZwVkV4ZWFBTklzZnVWcmxhaXo3V1BpU0NGdUV6cXpaMEpUYUY0L0YxL2ZNVzBZVTBoQll2TnpUbzZtRk5odzJwdFF6WSs5eDZGVm9ETDVmWTArUzJGK0l4VWZ5OW9ucnMrbE9UbVBCZFBTTzcwdVNUTDJ6M25TYXM3akFCNzJBQUFTUVJKK0NCV3lhbGZVemdTNCtvZXBRand2WkdGQnVST1NSelVsTlBmWjBCSm45a3Rubkc3VWxuN25PenQ2U2Ewck9EaXVFQXpjOGVIVUpsSitvdzFzTXlnSmZrQUtXa0UrM3BxeC9sZVplMENYbVRlSld3bkl0YkNqcEJTQ3JWenZWbnJSTHQ4YVc2MGlDbUFiZGdrTHYrb2dQcXRsOEUvK1BvWUdXTGIvSmdpZ2R1NlZETzJBS0plNW9hdHZpWDJ1TzZsa0FzOG1mY1lQUlJrd0pZRTNlMTRVOStxOHdmdkgvYzVZVUpSN1NqeE9pZms1WWxLbzhxd011ZTA5dnVuaHZjeFVOQ0JiTkd0M0pYWFZBOUQ2RW1xNzdydFl5amdEeWJnM1RwRG5EWTRESGRCRTgzbGJZWk51dldrY2kxVFVrdVltZzJWanpvb24ySmg4cU5XVkxUaVZLTEFkdmlLRjNmMGUiLCJtYWMiOiJkYWI3MzQ5MDI1ZDUyN2NkOTQyMDU2YTEwZjFkNWZiZWI3NzA4MzFiZmZkNGUwY2I5Zjc3MTJmNjI3MGRhYTEwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhQL3FQNXZsSWFad05VdWJXVHFXV0E9PSIsInZhbHVlIjoiMDArU0ZRU2U4d1RKa1I3V0ZweHZhOHRSNXFEUHdONmg1RTR2WFo5bW5GVmRraUJxQ1Y3TDJEckYxUW5BTjlpalVwd09NcEdPZlExbUVDZmlUb1MweE5STzNTeDV6MTNSbnFNR0JZc1habk85elNFZnE4WkN2djdXSFZUWTdkNHBuc3hDUVl1TzRlVXFsSDVLeWdNS3Rnb1hWMWV1MkdxcnNGb240a3FBRkJDc3pUdWloOXl1Y3dTZUtHWEM4eWJWUElsR0ZDUFRYZVYxSWNqaFBMOStuaWxzdXhMYWlDZmk1ZlB3U092RUJzVGJ6aWVlK20yVk9nL2lISlBHbTFIemRUdDN1Rk9LMm5hOEVGZnVPcEdCNFJSY0RodE90Ly9qbHEyZ0hOVzRsU2NJam50NW1YMWJHc3UrN3VVb1MreHhQc25WbU9QZm5aK09ic3QwTzc3cTJzeVhJemZKTzdCWXp0Mm45MXovd091bjlSb3h3U0NJSWM4T0FKaldzV0wvRWJpVkJhbzZ4RHpqZE9CMys1b3dRWk5FVHdCZWtOelN2ZlkvVWN3eDhEK0YvRDlnYk90YVRKK0ljNWNsaEFrVjdycmRSU0E3RDFZV25YcjhZaTNNVUpDREw3S054QXZuemc0b0lUZHJrTHBYZU9ZT1pZZGlDL0JSKy9sR01BV0EiLCJtYWMiOiJhMzA0YmZjMGYxMDBjOGE4MGNjZDYwNmJkOWExYmViMjg5MjI5ZTg4NDBlNjY5ZDgxZmU0MGU2MmQxMmY2M2Q4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729334540\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1024308584 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1024308584\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1604070659 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNDeW5vZWpkSGdSY1gvNnN5QS82ZVE9PSIsInZhbHVlIjoiYThxMVJmMkkyNWQ3TjgrbTByK2M1eDlDcFVKWVNjK280eFBUU1JTZG83VlpzMUhPL0FkVDV5MU95UXp3eEZINWN6M3Z5Mndtc3lMbjlZaG1VRSsyVWhEc0dEREtJRGpkL2kzM2lKdE1ZRzltK3YwOG5sVndrcUdRMzNwMmdOQnVBQ0FvR0pnaWZ5cCtTRVQ1THBrd08zTkk5ZCsrUmo0c0RwNUlSb3A1a3liU2Uxak5CeVp3eWVLSEVLWEM1clRjQTU2eWRNaXNWb1FNUGJHcjlzN0Fuc0x1bWZzaHlndmFJNDJ1MmtHRkJpdHhZRjJpamg2MXZoL25yUUQwSzZoT216ZXZIRCt2eXJ0SW9QWHo3OERaUXJoVE5pNVA0L0JGZ2xKS3FOeFFGMy9UL0Z1VXlmbHJzNUpUeWhjOGZGTTJNZzZWVlgzdUZZODZUeXVxR3lyVXprTi93MStGM0VWMzI0NlhzVWFLZ05SOUhDUWozcm8vckgybnVJa0RmU0dibFd0QXV4ajZvbGhBaCtsQk9vMU5GV09OdGpFeDZJZ0tub0RicENBQnh0ZW42RFcvOXRqd0JmeVFadmlGK21iL0JCQ2R0M0xIWTJvVkZGQXdoaHp3aU1NRVdjREs5V1Y0Wm9JaUEvS0FPeTlMQXZ1RUd2cGlVcVVJOC96R1d4RUgiLCJtYWMiOiJjYjE1OTI0ZTc2ZDVmMGE2ZjQwZmM4NjkwMDM1NDRlNjkwYjE0NDA3MzE2ZDkwZGZhMmIzNjY2MjlmMzA2NjhjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ijk3ekkxSjZCN2l6WkFFYnJqM2Z1eWc9PSIsInZhbHVlIjoiL1BDb2QyQlhwOEFCVjM3Y204Z1FNNUJvU3dMVXJiVVlXc2lzNmtVdVhFM0h5dUQzZUdtZ0M5aVNneXBDTVNTOXNWejVzVHJIWGRYVU0yZGZ0S09ZcFlPdU5WMmpFbGtIRjBIT2tPTksweUZscmxHVkFsS1I4ZTR5WEw2QUl4cnJ2eVpJOWJpTk5icWt6UjAvcWJsV08xZlBTaVhLZERROXM1SHU0YUN6YnNGdTY2RkF4NVI2d2RnYkxqaDZyWGs4R0ZVOGhxT1FCV01Ua2czblpzbFM1VUIwVis4azRZbkd1UkhLWk9sZlJGcitFakxqd2grRGdKNkpPdE5OZHRwdGdLN3lQYlBoTzR6U3ZTWTkvNVk5RytmUE1RL2xFcDZwZ0FBb2oyazhyTnBTbUVJbmE0Q1dOWWtwN1ExQ0JzSysxcmRNZWRvN0xmRmVQSjFpYmIxaWYrL0U0TGc3RDRTa1VKbjkrVUUrd0haaTJGL3pMYzAzZmV2OUN6a3FaK1dTRkRSVjNzRkNUNVQ4aEF2VE1SaGx1R2xEajM2NnRhYjlIbGJPUjVoTlJIYUtTWTVoeGdlSFV5RTFnNG85dEduM0R2QkNYUHVta0hpdXRCd1pJOXBYUGxXOGEvK2lUcTNReldLSmNBVmM1a2xMZUp0dVdIb2wvQW1TSlEvaklHTlkiLCJtYWMiOiJiMGE4M2U5NDFiODI3MTNhNmQ5ZmMxMzQ0OTYzZjA2ZjUwZDBiNjlmOWYyNmZlNDJhZjEyNTY2Y2M4YThjODllIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNDeW5vZWpkSGdSY1gvNnN5QS82ZVE9PSIsInZhbHVlIjoiYThxMVJmMkkyNWQ3TjgrbTByK2M1eDlDcFVKWVNjK280eFBUU1JTZG83VlpzMUhPL0FkVDV5MU95UXp3eEZINWN6M3Z5Mndtc3lMbjlZaG1VRSsyVWhEc0dEREtJRGpkL2kzM2lKdE1ZRzltK3YwOG5sVndrcUdRMzNwMmdOQnVBQ0FvR0pnaWZ5cCtTRVQ1THBrd08zTkk5ZCsrUmo0c0RwNUlSb3A1a3liU2Uxak5CeVp3eWVLSEVLWEM1clRjQTU2eWRNaXNWb1FNUGJHcjlzN0Fuc0x1bWZzaHlndmFJNDJ1MmtHRkJpdHhZRjJpamg2MXZoL25yUUQwSzZoT216ZXZIRCt2eXJ0SW9QWHo3OERaUXJoVE5pNVA0L0JGZ2xKS3FOeFFGMy9UL0Z1VXlmbHJzNUpUeWhjOGZGTTJNZzZWVlgzdUZZODZUeXVxR3lyVXprTi93MStGM0VWMzI0NlhzVWFLZ05SOUhDUWozcm8vckgybnVJa0RmU0dibFd0QXV4ajZvbGhBaCtsQk9vMU5GV09OdGpFeDZJZ0tub0RicENBQnh0ZW42RFcvOXRqd0JmeVFadmlGK21iL0JCQ2R0M0xIWTJvVkZGQXdoaHp3aU1NRVdjREs5V1Y0Wm9JaUEvS0FPeTlMQXZ1RUd2cGlVcVVJOC96R1d4RUgiLCJtYWMiOiJjYjE1OTI0ZTc2ZDVmMGE2ZjQwZmM4NjkwMDM1NDRlNjkwYjE0NDA3MzE2ZDkwZGZhMmIzNjY2MjlmMzA2NjhjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ijk3ekkxSjZCN2l6WkFFYnJqM2Z1eWc9PSIsInZhbHVlIjoiL1BDb2QyQlhwOEFCVjM3Y204Z1FNNUJvU3dMVXJiVVlXc2lzNmtVdVhFM0h5dUQzZUdtZ0M5aVNneXBDTVNTOXNWejVzVHJIWGRYVU0yZGZ0S09ZcFlPdU5WMmpFbGtIRjBIT2tPTksweUZscmxHVkFsS1I4ZTR5WEw2QUl4cnJ2eVpJOWJpTk5icWt6UjAvcWJsV08xZlBTaVhLZERROXM1SHU0YUN6YnNGdTY2RkF4NVI2d2RnYkxqaDZyWGs4R0ZVOGhxT1FCV01Ua2czblpzbFM1VUIwVis4azRZbkd1UkhLWk9sZlJGcitFakxqd2grRGdKNkpPdE5OZHRwdGdLN3lQYlBoTzR6U3ZTWTkvNVk5RytmUE1RL2xFcDZwZ0FBb2oyazhyTnBTbUVJbmE0Q1dOWWtwN1ExQ0JzSysxcmRNZWRvN0xmRmVQSjFpYmIxaWYrL0U0TGc3RDRTa1VKbjkrVUUrd0haaTJGL3pMYzAzZmV2OUN6a3FaK1dTRkRSVjNzRkNUNVQ4aEF2VE1SaGx1R2xEajM2NnRhYjlIbGJPUjVoTlJIYUtTWTVoeGdlSFV5RTFnNG85dEduM0R2QkNYUHVta0hpdXRCd1pJOXBYUGxXOGEvK2lUcTNReldLSmNBVmM1a2xMZUp0dVdIb2wvQW1TSlEvaklHTlkiLCJtYWMiOiJiMGE4M2U5NDFiODI3MTNhNmQ5ZmMxMzQ0OTYzZjA2ZjUwZDBiNjlmOWYyNmZlNDJhZjEyNTY2Y2M4YThjODllIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1604070659\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1750020336 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1750020336\", {\"maxDepth\":0})</script>\n"}}