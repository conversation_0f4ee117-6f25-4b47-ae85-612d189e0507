{"__meta": {"id": "X28948e57abffa5961bed4f86a9c4ad61", "datetime": "2025-06-27 00:14:54", "utime": **********.450815, "method": "GET", "uri": "/users/15/login-with-company", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.043208, "end": **********.450836, "duration": 0.40762805938720703, "duration_str": "408ms", "measures": [{"label": "Booting", "start": **********.043208, "relative_start": 0, "end": **********.401071, "relative_end": **********.401071, "duration": 0.357863187789917, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.401081, "relative_start": 0.35787320137023926, "end": **********.450839, "relative_end": 3.0994415283203125e-06, "duration": 0.049757957458496094, "duration_str": "49.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43763240, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET users/{id}/login-with-company", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\UserController@LoginWithCompany", "namespace": null, "prefix": "", "where": [], "as": "login.with.company", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=660\" onclick=\"\">app/Http/Controllers/UserController.php:660-667</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0018599999999999999, "accumulated_duration_str": "1.86ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.431456, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.871}, {"sql": "select * from `users` where `users`.`id` = '15' limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/UserController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\UserController.php", "line": 662}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4358761, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "UserController.php:662", "source": "app/Http/Controllers/UserController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FUserController.php&line=662", "ajax": false, "filename": "UserController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 83.871, "width_percent": 16.129}]}, "models": {"data": {"App\\Models\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users/15/login-with-company\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/users/15/login-with-company", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1666154711 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1666154711\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1765290831 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983290489%7C46%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImQvcVNLMEJVMG1ndSs5M2NXT0x4Tnc9PSIsInZhbHVlIjoiU1pmSnRKOWxwVkx2dmRYWUFtYnRsZTd1YmIvRi8wL0QvT1llYzNUdUFQemk2T2ZsZ2Nyamd1MEpEd1I5RW03R200WDNQa2JyKzBEYkg5Zy9ucXVZcjB5SVZvZlpCbnorbTRDRDZNWk5iUWk0azVSWHZpUnJZT0VMb0dTZElCZU9VaFpMaGVGOWlWQSs4cmZKM1JtYUNaM2l5M2l6MHhaTzExWTBaSjJIKzJ5ZzRsbFk3MncxK3RuZkJKeHlTaUJiSVBhTHh4UHQxZlhQY3hiSjZFYWVGbFo4WkEyOTNLM1VKSmZVcEI2M0tpRjk0SlFuRkU0SzhxREdXTjdwQU9oL2FtcThDbmZxMjBiR1gvYjBtRG94MTh1K1dJTys2Z0x6TUprMzVYQTNBV2FGUktuaUZjS2MyU2o3MTc2STJ0aTNEc0xPWHlMMjdnRFJ6SmgwSTRKSzhMREMrdlYwQnRCUGVldTFFaUxGTGFXYU5XRDJKdlB3cS9qWGc5V2RCZlEwS1pHSDlJYVBJb1MweUQ4QmN6MEZMWWZoZjh0TXJUNHpCVkl4SktsTmk1UWU5d0RQVVFMOVphK2tQcE0xNXBRRElGc2tJZUE2ZDlNQzF5YVJMb2MyZTYycURWTmhxcnhva3Zyb1kzOURuYkZKZ01VVnBGSDZFaUJEdk52MGw5MmgiLCJtYWMiOiJjMDg1YmFhZjNiZWVlYjljMTQ3YTVjNDA5MDZmNGFmNzlmNjY0NThlNDFkM2M3NTYwM2MxOGFlMjlhNjNmZjMyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImM1cTVvUi94YUdUaG13S1p5QUFvS1E9PSIsInZhbHVlIjoieUdIVnRVSHhmZUlNWmVkZE9Db05DdXlBc3duaEhmT2NKd1V0TXUwMVAxcHpNa1c2clJWU1lFRi9KbjduYnNmY2c5emg4VFJlemh0UWRlRnJBcHM4RHBuaVNieXgrOFlSVFJ5VzVPZkFwaHVKUzdCOU1HSFlldXZuL2JTbitMM2h1YjJwcy8yV1R6ZjM4emFuWlNzamtBbHBuRXVtbmhqOGJ1WDNjaE5xSy93anBaVjRVN3Q4OVcvRlo4MzM0TmVQa3laRU4zK09RMndGY0ZaVUUremg0SU85YWwvd0toTk9aUzdIaERVZjN2LzJBU3YwMG1SK3NXa0FsZGR1Tk9pMDNKZk5UREpIaFh6aXJCYkhJQmR3QTU2NUdhb0pWOTFuM1dkdCt6RzVreTVxYmdDUG4vSm5tdHE4K2JXdjd5OVZWY2JFOFBtdm5HcDhTZmNacDVpcEpaOEtDL21yZ3FsZTNMRXdBR29tYzQyZ0JzZlhXRWtubVAxMnZaYXh5aXdNbFQ3bms4UEQwMENWL0JlVWljK3NVaHBVUFlrMXVmM3N1K2EvQUswZVNhemxHdFhyRVV1MGE2OWFlT01rMmpGbzRVRlVTS0ZPWkgrWjNIWFd4enpXZXdHOGh3QkpEY0JHY2t3TDFYR1VjSkpYOGRocDN5NHZzdmI1NHJ3R1BlNWsiLCJtYWMiOiJkYTVjMWMwZTNhZWE1Zjg2NTcwMWIyZWEwNGNkNDBkZmQ4OWYyOWQ3ZTU2MmVhMTcwNjE4NGE1YTQ5YzZkNTNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1765290831\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-109604496 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-109604496\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2022208604 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZSbGh2L28rU0RqRWtCam9od2NtTWc9PSIsInZhbHVlIjoiN1QxK3Jrbkc3cTBDRmxKWnVPLzVlU3BCTGMyUmhPWk15eXBUQlJZeERGRHdsYjUrb2VkV0xGcFV1OHp1Z05HMUk4Misrd2YyRThhUUZHWVVjODRCVGRjY1RrckFhRTJWMS9jNGRQa2hoSlN4RWs5V3k3dVVSVURvV1VJa1VUakJRUUJwVHRYUUtveis5RTNIYzdIcXZ0QXpIanZ3dXIvajhrbkUvZ0Y3UEdrUmVFZFhhTDhYSlRjWHdHYnFDcEN3VVEvRnlCZ0NibEdJL1NFVEdmaVZXcG1EU1JIa3c5M2VkZmV6Mk1jVkZSL3NYZUtieWpINTM5amNIamRVWUQ4QXZnMm1keDZTeFVqMnJyVDNUN1pra0swZVlva205NjNzYzlwSHNOdXdkWVJjY0U3TGFrbnFTRVY2Y2lXVjBxRlZudjEwSmlPSkNqOStxT3NlN3MzT3dmSlhudzlYVHhIOTNyNTAveUNHSzBCc2xOZHFEZXJzQ1FMYUorcWtwZU1jWkF0cHpiYit0N0wxS1gydGgvWnlQWTJZUHhDamdVTkg2Tmhwamx0SWVXNStTVEpjM2l3eWRVYXBBODlXZ0Evb2FDWGtGdlYvemh3cGdXL3VQc1JrMkJhZ2JvRnE4N3BBZEw5dFdzdnpMalFOMmJNdU4zYzNySkVQT0VNc1RhVGsiLCJtYWMiOiJjN2U3ZjIzMmUwOWNlMzBjMTU5ZDU2Mzg5YzJhZmZkYzBlNjNjOGIwZmI1NGM0MThjNzVkZGFmNGJjMTk1ZDhlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktNVWY5eC84MkJPdjQweUpUWEgzaFE9PSIsInZhbHVlIjoiYWFEc1NZZUUvNmJKM2JHSVRSMERSM3VreUFQZHpsSUdxb2llZlpvRmdaQTV6bnZ6dk9tL3lBNzhWOGUzUUtYN1F1QVN2WFRSUzNKMGsrN0N2WnppWXJiems5dnRvVGtIdFRyNmRoL0V6NmVOdUVhTmZsN1ZjMDBZZlpnQTRTYXd2Q2wwVVhaTk5EUUZNTHA4U0VIS2hJcTlTbmtoOFdaMjB1UmIxbE82OHFmN1NJeUFNWFdNdm8rS0R3UE5GcHA4cjIvSUgrbW00SHRDOEs5UG5XTXhZOHNWYXpUNHRkcUsxZm83RkRTNkpuZEIzaVEyamhPRGRZbU42bDdONW9FOTlCTU14ZTE1cDlMcXhETEQxamJKQWpsYVJmdFpzVTU4ZjFPaXhuL3VhQ3dRSzZZV043SVFKRnpoRXVmYVBlL1IzdmZybk1vNzloSzd4SFora29TTEVCcmhNN2l2ZmZuSVRiL1BEZnFDdlBmZjQ4blk1NFFFS245MDUrV3RoN2FHOWhIV1I4d2orVi9pTk9tUlQ2bUFlU2pVZ2orazBMdWlDSXRJM2tJZ2lNbFdBZkFVN2l0WnB4TFdidURDS2IwVkhiOUJJZ0E1SGlLV3RkTUI4NWhldG02dGp2eHBreVNOWTZiMCtRTGtJMkRpTmZ0NFB2enpodTYwRUF4UmhZTDIiLCJtYWMiOiJhZTZlMGZhMGNlNzcxMTQ3N2Q3NmUwMTEzZDFkNjE0MTA4MThkMDQyOGRjYjJkZDQxZWQ3MzIxZDRlYTkzZWUyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZSbGh2L28rU0RqRWtCam9od2NtTWc9PSIsInZhbHVlIjoiN1QxK3Jrbkc3cTBDRmxKWnVPLzVlU3BCTGMyUmhPWk15eXBUQlJZeERGRHdsYjUrb2VkV0xGcFV1OHp1Z05HMUk4Misrd2YyRThhUUZHWVVjODRCVGRjY1RrckFhRTJWMS9jNGRQa2hoSlN4RWs5V3k3dVVSVURvV1VJa1VUakJRUUJwVHRYUUtveis5RTNIYzdIcXZ0QXpIanZ3dXIvajhrbkUvZ0Y3UEdrUmVFZFhhTDhYSlRjWHdHYnFDcEN3VVEvRnlCZ0NibEdJL1NFVEdmaVZXcG1EU1JIa3c5M2VkZmV6Mk1jVkZSL3NYZUtieWpINTM5amNIamRVWUQ4QXZnMm1keDZTeFVqMnJyVDNUN1pra0swZVlva205NjNzYzlwSHNOdXdkWVJjY0U3TGFrbnFTRVY2Y2lXVjBxRlZudjEwSmlPSkNqOStxT3NlN3MzT3dmSlhudzlYVHhIOTNyNTAveUNHSzBCc2xOZHFEZXJzQ1FMYUorcWtwZU1jWkF0cHpiYit0N0wxS1gydGgvWnlQWTJZUHhDamdVTkg2Tmhwamx0SWVXNStTVEpjM2l3eWRVYXBBODlXZ0Evb2FDWGtGdlYvemh3cGdXL3VQc1JrMkJhZ2JvRnE4N3BBZEw5dFdzdnpMalFOMmJNdU4zYzNySkVQT0VNc1RhVGsiLCJtYWMiOiJjN2U3ZjIzMmUwOWNlMzBjMTU5ZDU2Mzg5YzJhZmZkYzBlNjNjOGIwZmI1NGM0MThjNzVkZGFmNGJjMTk1ZDhlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktNVWY5eC84MkJPdjQweUpUWEgzaFE9PSIsInZhbHVlIjoiYWFEc1NZZUUvNmJKM2JHSVRSMERSM3VreUFQZHpsSUdxb2llZlpvRmdaQTV6bnZ6dk9tL3lBNzhWOGUzUUtYN1F1QVN2WFRSUzNKMGsrN0N2WnppWXJiems5dnRvVGtIdFRyNmRoL0V6NmVOdUVhTmZsN1ZjMDBZZlpnQTRTYXd2Q2wwVVhaTk5EUUZNTHA4U0VIS2hJcTlTbmtoOFdaMjB1UmIxbE82OHFmN1NJeUFNWFdNdm8rS0R3UE5GcHA4cjIvSUgrbW00SHRDOEs5UG5XTXhZOHNWYXpUNHRkcUsxZm83RkRTNkpuZEIzaVEyamhPRGRZbU42bDdONW9FOTlCTU14ZTE1cDlMcXhETEQxamJKQWpsYVJmdFpzVTU4ZjFPaXhuL3VhQ3dRSzZZV043SVFKRnpoRXVmYVBlL1IzdmZybk1vNzloSzd4SFora29TTEVCcmhNN2l2ZmZuSVRiL1BEZnFDdlBmZjQ4blk1NFFFS245MDUrV3RoN2FHOWhIV1I4d2orVi9pTk9tUlQ2bUFlU2pVZ2orazBMdWlDSXRJM2tJZ2lNbFdBZkFVN2l0WnB4TFdidURDS2IwVkhiOUJJZ0E1SGlLV3RkTUI4NWhldG02dGp2eHBreVNOWTZiMCtRTGtJMkRpTmZ0NFB2enpodTYwRUF4UmhZTDIiLCJtYWMiOiJhZTZlMGZhMGNlNzcxMTQ3N2Q3NmUwMTEzZDFkNjE0MTA4MThkMDQyOGRjYjJkZDQxZWQ3MzIxZDRlYTkzZWUyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2022208604\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://localhost/users/15/login-with-company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}