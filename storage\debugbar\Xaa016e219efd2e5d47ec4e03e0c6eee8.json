{"__meta": {"id": "Xaa016e219efd2e5d47ec4e03e0c6eee8", "datetime": "2025-06-27 01:26:43", "utime": **********.330936, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750987602.871801, "end": **********.33095, "duration": 0.4591491222381592, "duration_str": "459ms", "measures": [{"label": "Booting", "start": 1750987602.871801, "relative_start": 0, "end": **********.258616, "relative_end": **********.258616, "duration": 0.38681507110595703, "duration_str": "387ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.258626, "relative_start": 0.3868250846862793, "end": **********.330952, "relative_end": 1.9073486328125e-06, "duration": 0.0723259449005127, "duration_str": "72.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029216, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02362, "accumulated_duration_str": "23.62ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.287018, "duration": 0.0225, "duration_str": "22.5ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.258}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.317871, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.258, "width_percent": 2.075}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.324273, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.333, "width_percent": 2.667}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/roles\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2145744146 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2145744146\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1500944461 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1500944461\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1595806384 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1595806384\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750987585656%7C95%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhldDlvRkh6M3dUUVhnaVI3UXdUMmc9PSIsInZhbHVlIjoiRWJDTUxWQmIrVnZRYlQwVGY2MWEwbWZkNzl0Y1pXM3BsQnRXK3J5QkxwY3dTRGE3Y2tuTExnWnE4M0hhalU0YitVZ0R2YldBNnk3SzV3WW5Mb1lNRTdrRVVVb0tNZDNsZ3hlK2plc3hQdWhhYWxQdHZRSFFOZEo2ajRBSUp2cDBxV0NKbjhiM0lNbVBqWEtlNzBhZ3dOT0RKZlNhREF2VUd2U1UwK2JOYnNlY05OclNjaDBVeGRiQjNuMTQ3SFh6TjBRQSsyV3MvV2FmYkRPTkpORmMvNmxsRE00K0I2d3Q3dU9QczVFR2ZxTE01c3ZWc29aNG5FNHVROVdXdUpkV3I1OXQ2c2dMOG1VTTlFVGtoWjd4Z3M5UUFSSXZLWWQySTlVSWN1VlFLNWg3SjZQZmJPVEE4TnlYQ3lFNVJ0djVCWWhyME9SdVpuSUhsejR6Z1lIWjJUWTUxbUI4c0doRFpiTnBzNnREWlQxUmdyczY0TmozYktNanN6MUdQN2tLcG9VWDRhZXNSUG5POXI2NDhRU2Z5SWMvU0c3ZERURmR2WU5uOXF4OG5JS05nTDJOenFkY1pYTXFkMlpQODZ0Y0dSNFcrVXVXL2lvVzlGcDg4ekozejRXRmIrbXQ2em1MaDFFYmtQQWR6c2c0RHRlN0E0bXlOZmtTaXl1bWx4MysiLCJtYWMiOiJhMzdjOTdhZDZkNjE3YjBhMTgwYWNkMTM0MDJhZWE0ZDZiMGRjYjRlM2I0NmZkNTcxNjA5NGZiOTU1OWUwNjdkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlJCUTlJMjVWTmNQaVJyZEdmVW9KQnc9PSIsInZhbHVlIjoiWWJpRGswanNSenlHcWtjTy9FaTFvNllyWW5hODc1Z2JwMTVRWUVZVnZnaTNQdlhHcWYvSmRVVEYxNmhRdkNaSVVrTjZhblRBQ0ovaWp4dFlpUWROVGdqb1c5SEV4YXJmaEM5QVMwT24wRitFNUxiNUlWT1hsbWJFR0EwWUY0N1ZTbzVtMmc2ZjFPalY2QjBOYzlLbVhKRjhrMDh2eWNpMnVnMHJtYlRFcVJaUHp2Z3hGU3NZcERCOFpsb0hZZ0VtS1lBTHRvMXN5SEE2dEFVRXZGZ0hWdkxJUE1mME10NnY0ZjlzcE5XMHBMWU1RMmVmcVhxeVBjRGhvd1I3MGhxZE41SG9vTXovVDByUC84L2JkMlhRR0FHYUFJM0UyNmNocVE4ZFp5MVdXZlU1Uk9hTW9vVHNUdnZqTzJ4dW5QNVl6eG0vcVJZTENIRnVRMURONTRHRHljZXdCSkpsQTBkVU1VS1kzSHg5L0pKYVd1V3RaRTIyQ080RTV0UjNqUUpMU2UxdGk5TFg3L1I1SndQS2xRem1ob014L1lZUmxvMFl4bFZNL0g5QkJueHRvdkJGY25mT0lEU3JoSGpadk5FQWYrajJHTElPZ0NEZXNlWFpmZ0lFSmZzczN6VVZ0SVlQa3F0aFZmd2J1QThLMWdXLzZwOWlNNDhvS0ZoSndRb2siLCJtYWMiOiIzNmY1ZWJiMTNiODM3MTExMDA4N2ViMGI1ZmQxMDJkYzdiZTk4NTJkMjA2ZmIzNzFlOTI5NjM1ZTU4MzAwZWJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1199590926 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:26:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJWNzVVcmJVU01zdlFocWlzRld1dkE9PSIsInZhbHVlIjoiTERkVkhBcnFXU2JGTnFsazNYM2I4YnlQOGtvQ0NkUTM0SWZHM2JjemEvU2c4NXRsN3c4bEV0bEc1dGFyU09obytQQjNQUzhJbGJodW9LUlF6RnYyNGdsd1lBUkVuWHoyZEZSSGw0eDFUbTlxZnN4UU9SdWdUcmIzekRBSjQ1dkxFYm85Mm9taHNjZmlxanBSQzR0bFplMWRTejR5ZElVOTl5aERpdldjWjcyN1g4OGZDVlBKcWU0aDEreDN3dXlQcGFKQ2U1MVB0QjAxTStENGIyYjBvdVRqZncrcGtrVExrUnd0ZzVjWnpud1g2NUl3MHZiQythdld0YW95cHBmVnlUOEo1dEhPbmF0Vkp4Z1ZiYVZVbEVnRzk1amIzUytZR1dWUGJZMytEWGt5WHZ3UlZ0dDgzczZpa2ZoMXNqNGNZcDREVDFqNzJ2UWVZN1drenlvdVhmU2lOanIydWlmVTQ5dHNsZnFCNUR1TWtZVUVlRS9JcWo2WDZvM0c4b0JEYUM0RE1GQUVGVTZKM2tPZzA1bnpMTGlzU2xOdHo2em1rbjNGWmIrNi92N1JTV3BpbnlRVW5HeDU5alNyQTUyK3V1aHpPREUvMXprQUduWjZPTUNIQTNlaWNPVDQyd0daaDdUUmhCU0ZWYU1uYzVVcTFRK3NIUWlOd1RFdXJydC8iLCJtYWMiOiJhZjczZjkzZWU5NzYzN2NlMTUzMGJhOGY5ZTcyYThlNDc5NzNiMGE3NTQ3NmU2OTAxYTQ0YTFiYzBhMWIyZTIwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:26:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InEvcitSTTJhQ012Tk9Cc2I2ZWowVnc9PSIsInZhbHVlIjoiSVIyVVhUcGFTYkpWZ0NiV1Y5N2JTTWhPc0xOVC8vNERWOTJpTzFaeTUrRFkvV1p2a0RvVUhFVUUvYzNib2FRUHg4emMyc0M4WnNpUGZzang1N2gxVERYYWFHVlNQdDJEcUMyTkZqeHpsNzBNTUlZVmYxbU1UZzlLN3Y4MWJ2UkVJVEszQi9ZZkxGVkdnakxQZlBiMFgzWEVDMjNWUWhBYmI5Vzc4aWVpN0VVTGxzTjlBWEJTSjlFak5YaXlKM2d4a0sxM0lMa09pYmV2YURlbWpQVEVMMzl0Q3pNdm0yWTBVZXVwbzVQMXF3amZZRmdJSEVyZHJLZG1yRTJxbmZHYnRxTnVTdFIzbFBaUVVTWWxPMGl1bWxtTmwzTFNoYmxKSFdlV2grTHZneElJUXNEeEsvWkpHV2RuSFJzYVFzZng4UHVOVEtPblF4aVNtelhmUGIrMCt6NWZBM3N2bzh2RmtBbVlYMjNlM3pTbmRZZllPZWprTXJTT28vU1BWRFpGY09RSkpKL0R3VkcyUmRTUzloMmQ1RFZLVVFLNUZmR3p1eEZKTGdoTlNpVWR1cS9naUFESDJoaFZ0dXZna3NMUVkyWGR4UFVlMUlYazNoTFZnemwraUliYy9WY0Qvckx0enZrZVJ0SXVYOXp6aFUxYlVXTS91YUEyVkJFeFN5WHkiLCJtYWMiOiJiZDJmMTI4MjQyZjIxOTU3ZDJkOTEyMzNmYzU3M2IxZmEwMWEwMDZmMTE2NDU5NmRmOGFlZjhhZGFhOTAwOWFmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:26:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJWNzVVcmJVU01zdlFocWlzRld1dkE9PSIsInZhbHVlIjoiTERkVkhBcnFXU2JGTnFsazNYM2I4YnlQOGtvQ0NkUTM0SWZHM2JjemEvU2c4NXRsN3c4bEV0bEc1dGFyU09obytQQjNQUzhJbGJodW9LUlF6RnYyNGdsd1lBUkVuWHoyZEZSSGw0eDFUbTlxZnN4UU9SdWdUcmIzekRBSjQ1dkxFYm85Mm9taHNjZmlxanBSQzR0bFplMWRTejR5ZElVOTl5aERpdldjWjcyN1g4OGZDVlBKcWU0aDEreDN3dXlQcGFKQ2U1MVB0QjAxTStENGIyYjBvdVRqZncrcGtrVExrUnd0ZzVjWnpud1g2NUl3MHZiQythdld0YW95cHBmVnlUOEo1dEhPbmF0Vkp4Z1ZiYVZVbEVnRzk1amIzUytZR1dWUGJZMytEWGt5WHZ3UlZ0dDgzczZpa2ZoMXNqNGNZcDREVDFqNzJ2UWVZN1drenlvdVhmU2lOanIydWlmVTQ5dHNsZnFCNUR1TWtZVUVlRS9JcWo2WDZvM0c4b0JEYUM0RE1GQUVGVTZKM2tPZzA1bnpMTGlzU2xOdHo2em1rbjNGWmIrNi92N1JTV3BpbnlRVW5HeDU5alNyQTUyK3V1aHpPREUvMXprQUduWjZPTUNIQTNlaWNPVDQyd0daaDdUUmhCU0ZWYU1uYzVVcTFRK3NIUWlOd1RFdXJydC8iLCJtYWMiOiJhZjczZjkzZWU5NzYzN2NlMTUzMGJhOGY5ZTcyYThlNDc5NzNiMGE3NTQ3NmU2OTAxYTQ0YTFiYzBhMWIyZTIwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:26:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InEvcitSTTJhQ012Tk9Cc2I2ZWowVnc9PSIsInZhbHVlIjoiSVIyVVhUcGFTYkpWZ0NiV1Y5N2JTTWhPc0xOVC8vNERWOTJpTzFaeTUrRFkvV1p2a0RvVUhFVUUvYzNib2FRUHg4emMyc0M4WnNpUGZzang1N2gxVERYYWFHVlNQdDJEcUMyTkZqeHpsNzBNTUlZVmYxbU1UZzlLN3Y4MWJ2UkVJVEszQi9ZZkxGVkdnakxQZlBiMFgzWEVDMjNWUWhBYmI5Vzc4aWVpN0VVTGxzTjlBWEJTSjlFak5YaXlKM2d4a0sxM0lMa09pYmV2YURlbWpQVEVMMzl0Q3pNdm0yWTBVZXVwbzVQMXF3amZZRmdJSEVyZHJLZG1yRTJxbmZHYnRxTnVTdFIzbFBaUVVTWWxPMGl1bWxtTmwzTFNoYmxKSFdlV2grTHZneElJUXNEeEsvWkpHV2RuSFJzYVFzZng4UHVOVEtPblF4aVNtelhmUGIrMCt6NWZBM3N2bzh2RmtBbVlYMjNlM3pTbmRZZllPZWprTXJTT28vU1BWRFpGY09RSkpKL0R3VkcyUmRTUzloMmQ1RFZLVVFLNUZmR3p1eEZKTGdoTlNpVWR1cS9naUFESDJoaFZ0dXZna3NMUVkyWGR4UFVlMUlYazNoTFZnemwraUliYy9WY0Qvckx0enZrZVJ0SXVYOXp6aFUxYlVXTS91YUEyVkJFeFN5WHkiLCJtYWMiOiJiZDJmMTI4MjQyZjIxOTU3ZDJkOTEyMzNmYzU3M2IxZmEwMWEwMDZmMTE2NDU5NmRmOGFlZjhhZGFhOTAwOWFmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:26:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1199590926\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1729307798 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/roles</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1729307798\", {\"maxDepth\":0})</script>\n"}}