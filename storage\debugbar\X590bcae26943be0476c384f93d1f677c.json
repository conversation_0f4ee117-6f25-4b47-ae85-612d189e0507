{"__meta": {"id": "X590bcae26943be0476c384f93d1f677c", "datetime": "2025-06-27 02:25:42", "utime": 1750991142.043314, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.629986, "end": 1750991142.043329, "duration": 0.4133429527282715, "duration_str": "413ms", "measures": [{"label": "Booting", "start": **********.629986, "relative_start": 0, "end": **********.972455, "relative_end": **********.972455, "duration": 0.3424689769744873, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.972464, "relative_start": 0.34247803688049316, "end": 1750991142.04333, "relative_end": 9.5367431640625e-07, "duration": 0.07086586952209473, "duration_str": "70.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.020480000000000005, "accumulated_duration_str": "20.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.999083, "duration": 0.019600000000000003, "duration_str": "19.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.703}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750991142.028004, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.703, "width_percent": 2.783}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750991142.034255, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.486, "width_percent": 1.514}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-252367422 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-252367422\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1828437036 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1828437036\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1436398791 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1436398791\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-42736564 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991139555%7C26%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJQRG5DWlc3clRMUEdqU2doT3U4NkE9PSIsInZhbHVlIjoiUXlqRGIwTjdVUjhEeW83NW5ETzdyT2RQNUROd1l5S2dOalF4SXE1U3NtM1ViZk1Ua0VkQjdaaE9PUTBsMHRrNk1WSXdoQ1JFMlNsR0Ryd201NExrakRhd0xXa3VUVXFMSHkxQzlHVnQ1eVo4ekY5M1EvQTVMWmM1TjZ1Nnl4dDhRbjN3b1VLUjBtVzFYS2kzUThZTzlGalpOdk1uZndSaktxR3g3SVZrOHE0bC9qWUdaUkRKMVZoODhvVS9GWUQwWWtOeWplcVdjelZNK3c4UEdYVWtYNUpSOFFpOFcrRE1iVFdGM3FLVWxlaExqQVNxaENXOXdpUTlCYTl2bTM3WWYxWUY5WGxkcVAzQ2RXTHN4VXN5djBlZjRBUkk5aHE4UjFER0tkT29BZFRyZW5ZcEZ1ZnA0bDUwVTIwV2Q2U01XSlRDWkE2cWtYR0dBTzNwUG5pbVJDNmV0dGh3cWk4V0hQSTQyM0FkVFNqdHBnN1dtU3l0RUNhWDB0RXhmN1F5OUZOdkczMWMySWVISURnTFB5ZWFSaENPMjJkK0N4WUFoeG1UQ255MXpVNkhSNC95VUUwalE1RzFhRWRuZ2E2U1R2U0RqdjdSalVTZ2t5eVlTajdnMC9VQ09VNTFiL29XbjJRWW9tRnlMY2w4SFpFQzdFd3VVdm9Db01BbVJwZUoiLCJtYWMiOiJiYmQwYTBiMTE2ZDIzMmI4OTRiYmVmNzRmOGQ1ZmE0MjFiYzE2NzNkMjY3MWFlZjQzODQ2OTg3ZDA4NmUyYzRiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNvb1FUT1FnN3FBVnFEOXF5d0ZYNWc9PSIsInZhbHVlIjoiOS9TTjF0OUpsQnR1RlZRM2IxSmEzdVpmcWs0aG52V2lYQ2prekxqYm9aZngxTndYUnh2Mjg1VVY4UXoyU2xjcTV1RlRXdCs0SDRob1FJVks2c0NFbGMrRXRlb3Bqb3lKZUU3UW9wa0Jkc0l6dWJXMm5yazFXU1BrcXBDbXF4OGdYMUpNNVB2aDd1bXpPczB5TThlaWU1UG5SOHhlTGFWK0hWRXFIeGp4RGQ0ZlFIMWkwVFM5OHhsL0hjRHB2RjZ0NldZNlhoN3hMcHJOcGExWkxJSCtiTWtoTm1DMjRuQmQ2R3lXb01jaXZJdzNBaURYSy9DampQYkFYVjhRQTR1MXg5bHZURis4WUFwck9odDJtRGYwb2lpOUVCYVU3ZlZYRXpYWXF1NkxUamIxek9SYUltN3I2cUdtdXl5dUt5THdsWXNiSzI5YUVaQVRFanI2ZUJWOEJnUzN1T0lxRjA5MGhTd1RRQ2NsQjJNbVF1YXhqdzh2RFJyaVdSUUpPaWFNdTZKaEw4WGRnRUhMMHNQSkt3Tm9mK0xsRmdVU3JiRzNib1VHZmpIbTQ2L1RuL2FkY3hnV1FxYTVtTFFHalFpRVM3ZHJPTVJIMmRSbzAxT0FLbWk5ZzdmSDIxZ003cW5iM1U2ZVpPdFpFaXBVS2dSL2dRcUkvajZsWWxQNjFBUHYiLCJtYWMiOiI1MWEwZjQyMDcxYjYzY2ZlMDg3NjlhYzJlNGZkZTI4OTJhODk0MWFlZjhiM2Y3NTczNzJmZDZkZjBmZTdjOGQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-42736564\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1946277023 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1946277023\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2119270340 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBwRUU1UzdSclQrZmY4UnhMNTRCZHc9PSIsInZhbHVlIjoiUzA1cjVQZE9kcEprb250ckFmTFVYOGc0SGdIMFBDMVJBbjNoTjhRZXJsY0pjV0ZXS0VJTFo2N1BuLzFDei83d2dqdThoUURzLy9uZjkwcitqbkpIemNxS2pvaEE4RWZ4TXA0ZlQ5U1ZsZWYveXVtTmNhMk5saHhCeFViRlRsYTExTVJtUHBsY3hYSGNHd1dvSmgybi95Wmtta2pzZVV4SDZ0eVBrZ2ZsRm1jY3JnTVdJTmpTWGRuSmJ0MjBuVlhjaU5zWDhCNnlPRm1YOTZaUlJqSFlONFBISkZIL1llVHEwbVpLd3V4a25FVnUyZG10NU1vWW1pSGRjUmVFVXpBMFp6WHFyU0ZmY01meElQZEpSRWxYZlFpNGJ2T0pLNkQ0dG8xSGpmQm95VFo1WjJrZzdSNlllQzBZSElZbzhCNWxqc1BNV0F1QTZESEJrM2Y5Qm5wUjZXRE1ZQk1wRThtTVc2R3VKSmFaOW83Sm5aMlo2UDQxTVVMOUNsT3ZJMGdJWW95TG5FbExJdC8rd1ZweFhwZ29mbk9JeU9YOEM2SXZFYWE1R3VWeTdKY21neHJBV2RtVWczZDBFRm9yV0tCVUdFL1BhUStTR3hIakN4cDV3Qy9wamZkWHJ4REoxOUNibVVXaEtwZ2IwODVzVnZmb2ZVMThHV2FEeXhXQURVTGEiLCJtYWMiOiI1Mzk4MDM0NGY0ZmZmZTM3ZDBhNmM0MzFhNmM0NjI2NGUwZTJmMGMzMWVhOGRjZjM1N2M1YTA2NmI1MzVhOTljIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdVeFBpNXJQWCtueWxCL3oxeVFvc2c9PSIsInZhbHVlIjoibG1ycjlGWWlYUmIvY09pdm4zbXEzL2M0S3VZRm1Va3JsNGo2U3IrRmlGYzU5dzEwOFozdTk2Nm1KL2xySURsOHVpalJNZVRzQmMwd1l2RmVLT0FMaTFDT1dvNlVBOWNUK1RORERkd2VPSXFhb3RqWEQ3YzVqOG5LaFhIbEJKTStIQmtPQ0xXNCtGN3d1dnM4QStuWHk1by9yQ0xQdlEzbEp4Uk93YSswaytnK0JvNTRZMVdkTUVPUUhHMkdudEtveEhHb1ljczhPLzF6My9lWHRUaVpMYXY5cCtWOE1kcmpiTEtWTE5RU3ZiSjZKaVYxQnkwSHJ4czNoV0lHWklNWVNlZWFlVVR5Z0ZVWVJlSWd6eXFpUXdYdlcrOW5OZGFsSVl3Q2dCZi9WeWhIdnlxSVl3UkFUb0E5TDBIL3RZbE5yWVJSOHp5M2czUmY1Zi9WVGx4a0ZCT3RaY3BBbzE5VGdTQVgza0pvekErZGxaRTBrNm1uTWVrOVJPNjJ5eUxDeEdUa3RVdi9xblQ4MFAvTDlRSGx0Sno5ZnZYcDNkTG1hcDJ5bjlVcGl4b3p6ZWNxOXlBUkF2d051TDVvbmVoeExiUzFJaDk2T2M0aWUvV0hjSzJQSExhNnowWStWbnZJY3lpeHJJRFBlS3JrbGNXQUtHL3pKbHYwamFPY2c4MGgiLCJtYWMiOiJhNjZkNDk0MDI3NTBkMmZlMTZhMWMwZTIyODBhOTg5NDkwNzA1OTY3NzY4OWJhN2I5MzE3OTUzY2UzMmYwNTk1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBwRUU1UzdSclQrZmY4UnhMNTRCZHc9PSIsInZhbHVlIjoiUzA1cjVQZE9kcEprb250ckFmTFVYOGc0SGdIMFBDMVJBbjNoTjhRZXJsY0pjV0ZXS0VJTFo2N1BuLzFDei83d2dqdThoUURzLy9uZjkwcitqbkpIemNxS2pvaEE4RWZ4TXA0ZlQ5U1ZsZWYveXVtTmNhMk5saHhCeFViRlRsYTExTVJtUHBsY3hYSGNHd1dvSmgybi95Wmtta2pzZVV4SDZ0eVBrZ2ZsRm1jY3JnTVdJTmpTWGRuSmJ0MjBuVlhjaU5zWDhCNnlPRm1YOTZaUlJqSFlONFBISkZIL1llVHEwbVpLd3V4a25FVnUyZG10NU1vWW1pSGRjUmVFVXpBMFp6WHFyU0ZmY01meElQZEpSRWxYZlFpNGJ2T0pLNkQ0dG8xSGpmQm95VFo1WjJrZzdSNlllQzBZSElZbzhCNWxqc1BNV0F1QTZESEJrM2Y5Qm5wUjZXRE1ZQk1wRThtTVc2R3VKSmFaOW83Sm5aMlo2UDQxTVVMOUNsT3ZJMGdJWW95TG5FbExJdC8rd1ZweFhwZ29mbk9JeU9YOEM2SXZFYWE1R3VWeTdKY21neHJBV2RtVWczZDBFRm9yV0tCVUdFL1BhUStTR3hIakN4cDV3Qy9wamZkWHJ4REoxOUNibVVXaEtwZ2IwODVzVnZmb2ZVMThHV2FEeXhXQURVTGEiLCJtYWMiOiI1Mzk4MDM0NGY0ZmZmZTM3ZDBhNmM0MzFhNmM0NjI2NGUwZTJmMGMzMWVhOGRjZjM1N2M1YTA2NmI1MzVhOTljIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdVeFBpNXJQWCtueWxCL3oxeVFvc2c9PSIsInZhbHVlIjoibG1ycjlGWWlYUmIvY09pdm4zbXEzL2M0S3VZRm1Va3JsNGo2U3IrRmlGYzU5dzEwOFozdTk2Nm1KL2xySURsOHVpalJNZVRzQmMwd1l2RmVLT0FMaTFDT1dvNlVBOWNUK1RORERkd2VPSXFhb3RqWEQ3YzVqOG5LaFhIbEJKTStIQmtPQ0xXNCtGN3d1dnM4QStuWHk1by9yQ0xQdlEzbEp4Uk93YSswaytnK0JvNTRZMVdkTUVPUUhHMkdudEtveEhHb1ljczhPLzF6My9lWHRUaVpMYXY5cCtWOE1kcmpiTEtWTE5RU3ZiSjZKaVYxQnkwSHJ4czNoV0lHWklNWVNlZWFlVVR5Z0ZVWVJlSWd6eXFpUXdYdlcrOW5OZGFsSVl3Q2dCZi9WeWhIdnlxSVl3UkFUb0E5TDBIL3RZbE5yWVJSOHp5M2czUmY1Zi9WVGx4a0ZCT3RaY3BBbzE5VGdTQVgza0pvekErZGxaRTBrNm1uTWVrOVJPNjJ5eUxDeEdUa3RVdi9xblQ4MFAvTDlRSGx0Sno5ZnZYcDNkTG1hcDJ5bjlVcGl4b3p6ZWNxOXlBUkF2d051TDVvbmVoeExiUzFJaDk2T2M0aWUvV0hjSzJQSExhNnowWStWbnZJY3lpeHJJRFBlS3JrbGNXQUtHL3pKbHYwamFPY2c4MGgiLCJtYWMiOiJhNjZkNDk0MDI3NTBkMmZlMTZhMWMwZTIyODBhOTg5NDkwNzA1OTY3NzY4OWJhN2I5MzE3OTUzY2UzMmYwNTk1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2119270340\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1680504767 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680504767\", {\"maxDepth\":0})</script>\n"}}