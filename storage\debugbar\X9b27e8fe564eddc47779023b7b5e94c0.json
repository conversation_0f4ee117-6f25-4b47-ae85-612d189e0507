{"__meta": {"id": "X9b27e8fe564eddc47779023b7b5e94c0", "datetime": "2025-06-27 01:04:59", "utime": **********.922785, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.461422, "end": **********.922801, "duration": 0.4613790512084961, "duration_str": "461ms", "measures": [{"label": "Booting", "start": **********.461422, "relative_start": 0, "end": **********.856698, "relative_end": **********.856698, "duration": 0.3952760696411133, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.856708, "relative_start": 0.39528608322143555, "end": **********.922803, "relative_end": 1.9073486328125e-06, "duration": 0.06609487533569336, "duration_str": "66.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043976, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016630000000000002, "accumulated_duration_str": "16.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.883375, "duration": 0.01582, "duration_str": "15.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.129}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.908084, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.129, "width_percent": 2.465}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.914531, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.595, "width_percent": 2.405}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/chart-of-account\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1184160408 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986296729%7C78%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5JTTlNMWZtdzhBdTB5MGMxWXF6UkE9PSIsInZhbHVlIjoiYUVUTkF2NS81RnNITHgvSUxiOUhSUlduZ3dNS0gwaEEraDhWby9EcmNCUkVxUS91RHRaOEQ1QWZ0ZGY0bEluWC92ME4vc3IrbzhZc2JzdzNieE9pWEpMa1gvblNrZ1IyUTZtRzBzd1QyQ1RzZ3Zta2ZEQnVJZ2hTYWxXRVNJWkNjdHJrN3N1dmxuVTR1eFFWcHdGR1FmUm1BdENpRGY0VnpNZzV1ZUIwTlBEZFc4Znl1WTdTblkrdjkxd1R4aEF6UzF1MFVXQ25NaUhLc1J1aUtSeFFyQ05aNlN0QStVazlnTEVtdzFUdUt5NU9xbVMyNk1XcTFqb0loYUYrTkFGVFBaVWQ4YTJndkRjZG00MktCRjRVeldyWGNTSjk2bzRBNEgzdTNGQ0RpVnQ1VzlPNEJxUnNXdjB6L3M0MHg4NjRNOVFhNmVmZk9PcUlJT3FLb2REczZON3J0Y0ZTMzBHTGEweGJXa2cxOU1SaXIxeHNENnZwQkVJQXdsUDcyWFhlc0JCaXB0cG5tZXdZK0pYZHJBcFF0QjJUU1plclNzZnFxd3BXUlVhRXI5VmtHMmIveUlDMkdCVG15ZmxnMjA3WUtTb1lxRmdlYTIza1RvSnpvYWxYVzFPcTNKTU9SVlRmWVRYVnVHN0d4MnRRSGY0WW1NQlhYd3FHVy9kT0JnL20iLCJtYWMiOiJhYWZjM2QwZDUwYzRhZTMzNWMxZmE2NDg3YmM5YTRhMWM1YzRiNmUwM2Y3NDQyNWNhZTBmMGQyMjU5YjA3Y2MzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlHcHpJUXE4WWxyYmQ0TmJUeC80Q0E9PSIsInZhbHVlIjoid2ZqU3NzTkRycU5qUlg4WHlZcjFsd2xLeHd2Z1hMM1NvM29Mc255aEEveHkzcUpLTU5SUFZNbnozbm1rMTN5bC9oUWFHdUJHeEhwU1c5dEhUcDRGSmNabFBCd0pKdi9wOFZxdkhJZW1pY0hiRzNpaHkxSW1QV2UrbzMrZEV2S216dTQ1QWorVXRVTzlWUXYwdWRsRTJmekhoNno0WHVhVnk0ZnBkV0ZLUEtNblJ0YmVocVFBVkxDd0hnSXlMcGlna2xFbm1aUk51Z2djYU1OZlkxRG51eHR6OE9YT3QyWjNLTWdCalZIYllWVkU1V3RlT1lSeTJyWDFLT1UwQm1STHlPUEtHdGhVZi9pc2VtNTUzTWUxMXVETDZWZTNISHkxSXV5MkpPZzUxV2w2RjRtQ09sZWJYRzNmbFhrTHVMNjJFQWhaQkNuNFVMUXV2MVhKbTF0WDlwQW8xVXlremt3bHE3SUU3SWZ0NEpaZ1JvL2FOQUwyNEhBM1h1MzRycUwyaDRyd2ZGMlZaUXV4YzFkRG1LNDdmWWFvQjJlWUpMYVZxRlJBR29oaWRlWTNmQWxURHl0bnhwWGZkRlRGVnBtRFlwdm15enBTWmZkb3NqdGFENmZMTlR2Z1JuSFA5cnlqdlVQR3FoVGlvaWtVUklLQ3hpKzZVK3pDWHEwc1J1ZmUiLCJtYWMiOiJmNzY5NjM0ZGMyM2E1ZDI0NzFkZmI0MTE1NDY2NzE4OWU5Nzk5ZDc4MWE5NjNjM2U1YmFhYzJmY2IyNGFmMTQxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184160408\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-180479078 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-180479078\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1161714723 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:04:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJCQmZua1NiL3RVQVNXeDR5QnJOaEE9PSIsInZhbHVlIjoiYk1iK1dYZkdQdkgvbnZWMzJ5NXh6OHA5d0dBVWtERGY0NEhGWTV5WFh2WC9EcDRiTzRFVVN0SDJFUENIM3Ntb2psSHhrQ2UyeEIrcFZ5UXAwR0Y2YVpHcVhmRHl6Z3NpUXVPaHpqSmUxaGcyaTdybkoyVjc1TUhyOHo5NXZEcTNzOURlZkpaRHpQUjNra1owV0lFWVZDV2VtWXRqSkVuaDRKZE5IR3A5RjFhV2JPYTY1aVBPdGZZQWJYdkRQQXlBTUVlNGNQMjBnbE50eDdtRzBhTGQ0U21sYWdXckZ5a0VudGlGMzBGQWVMQ2dTK083a1pJS2tnNm5FREs1ZnA2M1E1bnZ3bFNZY3FMRGpYYTVoWlhQaXBNc1duUklQUytuUkkrY3hMV2pxRWVqTE16NFpwSnVVQmZ4cHlwQWRmWUFzSXhudFNhQjhWcDBtSmJtNXVoTFdZczljNlgwd2tXOEVVRUtKRjBSVUoxdkYrQWp0RDkvT29sZGJLODUrUDZjb3hNWHhBUFo3bU02YzI2a1BXbFVnenNTOU8wYzUvT1lqbk10NmdpTit1QnVxNGd4engrM2ExV0RJR2RVQmh1SWt2NzJqMjVrR3VWSDJCelhTcUNEbWR6OXlTSkV4QXQyTUJwcDF1NUNGOGxyYnJNbkhSNURQdG1uNmdCLy9XYjIiLCJtYWMiOiJlYmVkODVmNDBjNWEzYmUwYzdjZDMyNzA0NGIzYzhhMjY1MjFhYzlkYzc4ZTFkOWYyYWYwNzRmNDIzNWM4MDYwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:04:59 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjRIb0hVdTZZTXRWU1gwQk1qYXE1WFE9PSIsInZhbHVlIjoiNGJGdmQ0QlJDZ1ZGak5xUWtGTkZkOWxQRkdpaVpyWU9iSWJlWEdwc3VZTzJTRjBZcEk3alNZdENQTEVPeVQzY2N6aGdudk5VbHBLeGtIN0tRanlLT29PbERtaTNmMXVHaHZqTml0ZXVuZlNaYVRwWkROMDVBSndNdEUxOGM3WmtKbUZ2dTQ4eDVWNmFreXZ3VWN1SVZPcFlsa3JNTFFrY1NaandvS1ozdDQ1RExySWtta285eFRKcy9YbkVhMlV4Ynl4ajQwOExpc2g5eVRiNWlBOHlGbitCSTFkMUw1ZW5yMkI4c3Z2NjRwN0FGaHZlTHZxcEUwdUhsai9sYzF0a25tbkN0c2hkSnVNbGpwUWcwTlFiV2lsdHZ0bWNyb3hhVFFMNFZmelZid2wwR25lMDM0RWN2cE5mc2UxWlliZ3hDclI4eTlCTWc3MklSczdncWtXNzJVa0FyTFY2QTEvbldrcTAyMHlGTVQ2anhWbnpaeXNvS0c4WkUxbnR4bW95dE5TZTFQVHVLcnRMKzloem5wWmdvVTZzSE16VkVmcy9IUlU0UVF0b2xhUFRpdkIxNXk2K3p2VllCWFFud3gyY20yUVgwZWNsVlVkWVA4YTlOV3RGdDIvZGRnc3BzdHpmOXhWZGE4QjhQMFYyOUFZK1pvRkcyTkRIeVd6VmV3STAiLCJtYWMiOiI0OGM0MmQ5MzMzNDM4ZDA0NDg5NTE4NzhhNGI5YTEyMWMwMTYzYjEzNjVhMGM4MTYyYjQyZWE1YWExNzQyYWY4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:04:59 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJCQmZua1NiL3RVQVNXeDR5QnJOaEE9PSIsInZhbHVlIjoiYk1iK1dYZkdQdkgvbnZWMzJ5NXh6OHA5d0dBVWtERGY0NEhGWTV5WFh2WC9EcDRiTzRFVVN0SDJFUENIM3Ntb2psSHhrQ2UyeEIrcFZ5UXAwR0Y2YVpHcVhmRHl6Z3NpUXVPaHpqSmUxaGcyaTdybkoyVjc1TUhyOHo5NXZEcTNzOURlZkpaRHpQUjNra1owV0lFWVZDV2VtWXRqSkVuaDRKZE5IR3A5RjFhV2JPYTY1aVBPdGZZQWJYdkRQQXlBTUVlNGNQMjBnbE50eDdtRzBhTGQ0U21sYWdXckZ5a0VudGlGMzBGQWVMQ2dTK083a1pJS2tnNm5FREs1ZnA2M1E1bnZ3bFNZY3FMRGpYYTVoWlhQaXBNc1duUklQUytuUkkrY3hMV2pxRWVqTE16NFpwSnVVQmZ4cHlwQWRmWUFzSXhudFNhQjhWcDBtSmJtNXVoTFdZczljNlgwd2tXOEVVRUtKRjBSVUoxdkYrQWp0RDkvT29sZGJLODUrUDZjb3hNWHhBUFo3bU02YzI2a1BXbFVnenNTOU8wYzUvT1lqbk10NmdpTit1QnVxNGd4engrM2ExV0RJR2RVQmh1SWt2NzJqMjVrR3VWSDJCelhTcUNEbWR6OXlTSkV4QXQyTUJwcDF1NUNGOGxyYnJNbkhSNURQdG1uNmdCLy9XYjIiLCJtYWMiOiJlYmVkODVmNDBjNWEzYmUwYzdjZDMyNzA0NGIzYzhhMjY1MjFhYzlkYzc4ZTFkOWYyYWYwNzRmNDIzNWM4MDYwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:04:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjRIb0hVdTZZTXRWU1gwQk1qYXE1WFE9PSIsInZhbHVlIjoiNGJGdmQ0QlJDZ1ZGak5xUWtGTkZkOWxQRkdpaVpyWU9iSWJlWEdwc3VZTzJTRjBZcEk3alNZdENQTEVPeVQzY2N6aGdudk5VbHBLeGtIN0tRanlLT29PbERtaTNmMXVHaHZqTml0ZXVuZlNaYVRwWkROMDVBSndNdEUxOGM3WmtKbUZ2dTQ4eDVWNmFreXZ3VWN1SVZPcFlsa3JNTFFrY1NaandvS1ozdDQ1RExySWtta285eFRKcy9YbkVhMlV4Ynl4ajQwOExpc2g5eVRiNWlBOHlGbitCSTFkMUw1ZW5yMkI4c3Z2NjRwN0FGaHZlTHZxcEUwdUhsai9sYzF0a25tbkN0c2hkSnVNbGpwUWcwTlFiV2lsdHZ0bWNyb3hhVFFMNFZmelZid2wwR25lMDM0RWN2cE5mc2UxWlliZ3hDclI4eTlCTWc3MklSczdncWtXNzJVa0FyTFY2QTEvbldrcTAyMHlGTVQ2anhWbnpaeXNvS0c4WkUxbnR4bW95dE5TZTFQVHVLcnRMKzloem5wWmdvVTZzSE16VkVmcy9IUlU0UVF0b2xhUFRpdkIxNXk2K3p2VllCWFFud3gyY20yUVgwZWNsVlVkWVA4YTlOV3RGdDIvZGRnc3BzdHpmOXhWZGE4QjhQMFYyOUFZK1pvRkcyTkRIeVd6VmV3STAiLCJtYWMiOiI0OGM0MmQ5MzMzNDM4ZDA0NDg5NTE4NzhhNGI5YTEyMWMwMTYzYjEzNjVhMGM4MTYyYjQyZWE1YWExNzQyYWY4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:04:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161714723\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://localhost/chart-of-account</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}