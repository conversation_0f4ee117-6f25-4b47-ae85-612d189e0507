{"__meta": {"id": "Xa027b7977210435dccb68c1090a5c457", "datetime": "2025-06-27 02:25:27", "utime": **********.364648, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991126.94956, "end": **********.364664, "duration": 0.4151041507720947, "duration_str": "415ms", "measures": [{"label": "Booting", "start": 1750991126.94956, "relative_start": 0, "end": **********.302229, "relative_end": **********.302229, "duration": 0.35266900062561035, "duration_str": "353ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.302237, "relative_start": 0.3526771068572998, "end": **********.364665, "relative_end": 9.5367431640625e-07, "duration": 0.06242799758911133, "duration_str": "62.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45720304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01461, "accumulated_duration_str": "14.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3286672, "duration": 0.01365, "duration_str": "13.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.429}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.350218, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.429, "width_percent": 3.354}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.355739, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.783, "width_percent": 3.217}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1857963037 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1857963037\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2012324341 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2012324341\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1207271953 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207271953\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1172706947 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991124394%7C22%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZIQjlkWllkRHp6VlV5U2hMTlA3enc9PSIsInZhbHVlIjoiWE1TblZNY1k0NGxxNEQ3UlZKREFOTlRxWXQvVmdSUG8zRTZSRXVwMXJ2ZGVNRXhteXdIa0JIUE5aU2tWSloyODVqNDMwOW1hMzdYdWJFZFRjQVlDTVhXU3VKbjNUWEtWNDI5RlhaSnVIaDhpZHFKQnVSVjVWMUJLUytuR0VIN3p3UTVTejBkUTlWTzFnVzB4WDUxQ3NzL0hianVST1BmaVI4cEJBYkR1SmpjdFVnTzNSb1NjTG5ybStKVnp5VzZ5YTMrbSswTlA3L3lYalhOVVBOMzFyWUwzc0hDV1d0aEp2NjAwVUQ3d1hwcmNyMmEra21INjBBSkFoUElJUDUxTkV0d2I1Sm5DcnhwRGNZUVBOSXJmWXVoRHJkU2FGRjZHaGNXQ3NCSlA2dFhGZWJ0VGVwMzNMVTlDUW5nZlFBdGs5bGJkNE5DNHZhYXErMm5FcnRXMXdzYlV4UkZSaTZ4VGMrK2R2dHBHdDl2M3gyZ1NnSmFLMnZkUVlZQlJHQkwyMHhyWVVRcUI0NStHVDl0UDJiRkRpUWtpRi90V1dlek1OQmpYcXhFa1pKZEoxd3kzek1LdGc3T2hOWVd1NDRXbXk4bzExaTlEb2l5UUJIZ0tGRnZENFZsT0IwbC9oVGhkNzlCdEF6Z2xOcU9yQlZUWVl2TlZ0TlRSaXI4ODdjVFUiLCJtYWMiOiI0NzQ5NDg1NGE3MGYxOGUwNWY5OWE5ODM0NDMxMjBhMTk5YjYyZmFkNmNmOGRhMTA3ZGI0M2Q3ZDRlYjExNDkxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjRRTXF5NlZNR050YlpEQ2VLS2lDZVE9PSIsInZhbHVlIjoiaytob0JmdGpxTitDNmJoRDh5d0FkV1ltdU1LZ295V0lRTjJkMWZRdXlCQTFoRWFSZFo0MzQra0dtWmZBdENEZ3UxSUc2TkNCbjRnWWE5bXVOUUo0b0ZUalBJbEFwdndIZ1Y5WTU1bE5zU2tCL3ppYUtmUkRoNklySVZTRGp4c3MyVDNWYUZJM3ljMDg1c2EvSVY5dUVVc2lGdUV6SGVReU9nODQ5RC9MRXMvODNaWjhCSCtFWm1Ta1ZQWW9VRnBMVHBBekd2cVkvSDhQV3puMU8wOExFTW9TYlU0ZmswNFo1bzF2SzhHejVWaFI4TUUyb0swVmpudVA1bytzMVNUWTh3K3JFYnZKR3lDMGlSNEs4ZGxyODZpUXFwQ0M3Q0tlaCtRUWZ2bmxVRll0TGlwcHdTQUFRR2RBUENoTzFiSlZDUW1jZW9XVGx3K2RZWkdFOUlydGkzTURpeFlGQjhoTS82eXV1cnhhbTVNYkZ5RGJhVkpQTC9vTXNvc2Z5YjlpalVoY0NLTDZ4VEIzVEpzYy9jWWJINWlqREhGcVgxL00rSmtvUWVNcS9mUVJKOHJqQ2c0Z0U0NkxkZ2g2UnVIU1p5dEdrZ3VMZW5zWDZFVk0vc01pd0VvVDlPcEhwb2JvaDNaSlNsRWs2SWxPQll4UjZWYzhQaWMvZ3pPU2RCNW4iLCJtYWMiOiJlZjFkNTdhZWU2MDI5NmEyOTY1ODQ4MGQ4YzEyMDdlNzdlM2JjOWE2MDc5ZTI1NGFjMGZjMzY4ZGUyODQ1OThjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1172706947\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1907610599 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1907610599\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1741718373 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZCMG5uNDhvWVN3cTVTQzZ4T09TOVE9PSIsInZhbHVlIjoiNEtRUERJQjJOYUhPeFREYm1WY1RJdUtCRlR2TkhhQnR3QnhhWVZDMDV6ZFA0MWozQlN0UUFSZVNIdEMwdzVJOTBQSDN5dE1wODBIVlJsQ0NqMFFCb0FtTVZ4RG83UDVnV3VzdThRSlhrZzNoSXczNmtmcDl1ZVVzV0VoRThEcDBJS0Fvek84c3M0NzRKZmRxVksvSTNsT05iQzZrVWpUYjRTaWs1S2RLdmRaeG1BVVJKZ3ZuWnkwTlpoZXpUVGt3ckRHejZxUGdnOTFEeitxQnl6Qk9MVVU0djNYR3BFZS8yNFJHQ0RiQkp4bCtiblV1Z04rZkxWeHo0UXQ2VlBYTkpZUG9MSkhJd25QUnl0cUtDdi9lOHBKZk1iKzNlNnY4bmc4U0RwOTh1TnUrNk0zWVRvSkJVd0duUFFqL3FlVnNEQkwrMmthQytza25za1RWT2dQTW9FbDJlTmppTCtqQmtndkd5RDRRNFdDMGhOWEl4TEJZSHA1M3l5VThnL0x6TlZoMnMwYkduZ25BRm94VktPYUZRMHFIWHI5eGhHanQ2RzdzMVZWQ0poOE92UGRiTWJaN05WdnNBall0Mzd6cXg3MkJ1bm5Dc1lPT3dkOElxSXhXeXJQSmUzRCsyaE9KRThuRTBTWVd4UHRoYnJ3ai9JbThLUDVMNFhoK08rcEUiLCJtYWMiOiI5NGM3YzIyZDYxY2IyNzkxNTZhNWNjZmQ0NjExOTc0ZTI2YjdlOWU3Mjc2MDExN2MzZDFmMjk2NmM0MmU2ZDEyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFkbUNLRDlKVXUwZEJPSDJtTTBVVnc9PSIsInZhbHVlIjoiL1liTS92clFKc3Jtdko4YVJ6V201cUpKblpDbERnbGVlUWJsYVEzTWlDOVhoUUNRWG5SK1lwLzJQT2JEZFdjbDAxalJJOW15dGgrcDJwNFgxMm0rem91ekRzWHJXR3FrYkFVa0t1UkRrT0pDUFF1eVljeDdieHptaXJ2N1RvYXdHSy8wcS9WRG5LOVlJQU5lWnlWc0VGQXU4MTc0aUlzb0hkbThrS2dHSVVJQWNzWFUwV3RzRDdLWXB5R3NzRkgwbE9yc0hsUWZTc1pqazJqY3hQaUZUUm42TUQ0R09GcnhMQjdFbFZtc01QMDE4SHg2SSt0cXFyUFpVYW8xcXJqV0lCUjNtVGJQVnJQMXd3Q3plSmVSK3RqRkMvaHh3anRBdzRlKzhGY2F2eURoQkkxcjZSVE5ZV0dLNE8rMHZLcUozRVVldDBJT0lod3UxRE9pQzJDT2tiUit3RnZKeUlucHU1amQwaUJnRUtEYXplcGdldk9JYTk0amViZmZyekI5K1RtU1R6M0NiMlFjTWN4SnZITXJybmVpNi9QeVA2WmVtRWxiWEROVTZ4Y1lHODBPdEU5RjdnRHVSK0xkRE9lMUU5cXVkRFhQY0lOWXRydXVHY2V3NUNKVkJ5V05IeHR2MytZVmN4aTdmWHpEL3R1dHd5eWFkd2J5clREUnBjRjciLCJtYWMiOiJlNzFhOTExMWU3ODgxNzBlNzRmNGE3ZDRkYWZkYThjMWMxNzNiODRiYjAxYTBiOWExZGU5YjhhMzI5YjQ3NzI2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZCMG5uNDhvWVN3cTVTQzZ4T09TOVE9PSIsInZhbHVlIjoiNEtRUERJQjJOYUhPeFREYm1WY1RJdUtCRlR2TkhhQnR3QnhhWVZDMDV6ZFA0MWozQlN0UUFSZVNIdEMwdzVJOTBQSDN5dE1wODBIVlJsQ0NqMFFCb0FtTVZ4RG83UDVnV3VzdThRSlhrZzNoSXczNmtmcDl1ZVVzV0VoRThEcDBJS0Fvek84c3M0NzRKZmRxVksvSTNsT05iQzZrVWpUYjRTaWs1S2RLdmRaeG1BVVJKZ3ZuWnkwTlpoZXpUVGt3ckRHejZxUGdnOTFEeitxQnl6Qk9MVVU0djNYR3BFZS8yNFJHQ0RiQkp4bCtiblV1Z04rZkxWeHo0UXQ2VlBYTkpZUG9MSkhJd25QUnl0cUtDdi9lOHBKZk1iKzNlNnY4bmc4U0RwOTh1TnUrNk0zWVRvSkJVd0duUFFqL3FlVnNEQkwrMmthQytza25za1RWT2dQTW9FbDJlTmppTCtqQmtndkd5RDRRNFdDMGhOWEl4TEJZSHA1M3l5VThnL0x6TlZoMnMwYkduZ25BRm94VktPYUZRMHFIWHI5eGhHanQ2RzdzMVZWQ0poOE92UGRiTWJaN05WdnNBall0Mzd6cXg3MkJ1bm5Dc1lPT3dkOElxSXhXeXJQSmUzRCsyaE9KRThuRTBTWVd4UHRoYnJ3ai9JbThLUDVMNFhoK08rcEUiLCJtYWMiOiI5NGM3YzIyZDYxY2IyNzkxNTZhNWNjZmQ0NjExOTc0ZTI2YjdlOWU3Mjc2MDExN2MzZDFmMjk2NmM0MmU2ZDEyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFkbUNLRDlKVXUwZEJPSDJtTTBVVnc9PSIsInZhbHVlIjoiL1liTS92clFKc3Jtdko4YVJ6V201cUpKblpDbERnbGVlUWJsYVEzTWlDOVhoUUNRWG5SK1lwLzJQT2JEZFdjbDAxalJJOW15dGgrcDJwNFgxMm0rem91ekRzWHJXR3FrYkFVa0t1UkRrT0pDUFF1eVljeDdieHptaXJ2N1RvYXdHSy8wcS9WRG5LOVlJQU5lWnlWc0VGQXU4MTc0aUlzb0hkbThrS2dHSVVJQWNzWFUwV3RzRDdLWXB5R3NzRkgwbE9yc0hsUWZTc1pqazJqY3hQaUZUUm42TUQ0R09GcnhMQjdFbFZtc01QMDE4SHg2SSt0cXFyUFpVYW8xcXJqV0lCUjNtVGJQVnJQMXd3Q3plSmVSK3RqRkMvaHh3anRBdzRlKzhGY2F2eURoQkkxcjZSVE5ZV0dLNE8rMHZLcUozRVVldDBJT0lod3UxRE9pQzJDT2tiUit3RnZKeUlucHU1amQwaUJnRUtEYXplcGdldk9JYTk0amViZmZyekI5K1RtU1R6M0NiMlFjTWN4SnZITXJybmVpNi9QeVA2WmVtRWxiWEROVTZ4Y1lHODBPdEU5RjdnRHVSK0xkRE9lMUU5cXVkRFhQY0lOWXRydXVHY2V3NUNKVkJ5V05IeHR2MytZVmN4aTdmWHpEL3R1dHd5eWFkd2J5clREUnBjRjciLCJtYWMiOiJlNzFhOTExMWU3ODgxNzBlNzRmNGE3ZDRkYWZkYThjMWMxNzNiODRiYjAxYTBiOWExZGU5YjhhMzI5YjQ3NzI2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741718373\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1950641905 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950641905\", {\"maxDepth\":0})</script>\n"}}