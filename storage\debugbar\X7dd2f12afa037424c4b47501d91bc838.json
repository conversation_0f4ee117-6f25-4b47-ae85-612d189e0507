{"__meta": {"id": "X7dd2f12afa037424c4b47501d91bc838", "datetime": "2025-06-27 01:14:47", "utime": **********.076119, "method": "GET", "uri": "/vender/eyJpdiI6IkQvaEpGMWNpQkNiSC93UFNEZ1gxK2c9PSIsInZhbHVlIjoiMEJtZ0l2Y1gvZjlnVEFiMjFJMEgvQT09IiwibWFjIjoiMzU4YjAxZjJlZmRjODhlMjMwOGE4MzQzZmEyZjFkMTYyMTNmYjI2MzJlNzMyMTM5MDM0NjYwZjc0MjI0ZTFhMCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.569702, "end": **********.076136, "duration": 0.5064342021942139, "duration_str": "506ms", "measures": [{"label": "Booting", "start": **********.569702, "relative_start": 0, "end": **********.901116, "relative_end": **********.901116, "duration": 0.33141398429870605, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.901125, "relative_start": 0.3314230442047119, "end": **********.076137, "relative_end": 9.5367431640625e-07, "duration": 0.17501211166381836, "duration_str": "175ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52731448, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 7, "templates": [{"name": "1x vender.show", "param_count": null, "params": [], "start": **********.951131, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.phpvender.show", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvender%2Fshow.blade.php&line=1", "ajax": false, "filename": "show.blade.php", "line": "?"}, "render_count": 1, "name_original": "vender.show"}, {"name": "1x layouts.admin", "param_count": null, "params": [], "start": **********.011905, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.phplayouts.admin", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fadmin.blade.php&line=1", "ajax": false, "filename": "admin.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.admin"}, {"name": "1x partials.admin.menu", "param_count": null, "params": [], "start": **********.015497, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.phppartials.admin.menu", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fmenu.blade.php&line=1", "ajax": false, "filename": "menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.menu"}, {"name": "1x partials.admin.header", "param_count": null, "params": [], "start": **********.055147, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.phppartials.admin.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.header"}, {"name": "1x partials.admin.footer", "param_count": null, "params": [], "start": **********.065625, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/partials/admin/footer.blade.phppartials.admin.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "partials.admin.footer"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.067847, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}, {"name": "1x Chatify::layouts.footerLinks", "param_count": null, "params": [], "start": **********.068278, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/vendor/Chatify/layouts/footerLinks.blade.phpChatify::layouts.footerLinks", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvendor%2FChatify%2Flayouts%2FfooterLinks.blade.php&line=1", "ajax": false, "filename": "footerLinks.blade.php", "line": "?"}, "render_count": 1, "name_original": "Chatify::layouts.footerLinks"}]}, "route": {"uri": "GET vender/{vender}", "middleware": "web, verified, auth, XSS, revalidate", "as": "vender.show", "controller": "App\\Http\\Controllers\\VenderController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=152\" onclick=\"\">app/Http/Controllers/VenderController.php:152-165</a>"}, "queries": {"nb_statements": 39, "nb_failed_statements": 0, "accumulated_duration": 0.015609999999999995, "accumulated_duration_str": "15.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.9290042, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 9.737}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9393442, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 9.737, "width_percent": 3.139}, {"sql": "select * from `venders` where `venders`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\VenderController.php", "line": 161}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.942297, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "VenderController.php:161", "source": "app/Http/Controllers/VenderController.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=161", "ajax": false, "filename": "VenderController.php", "line": "161"}, "connection": "kdmkjkqknb", "start_percent": 12.876, "width_percent": 2.947}, {"sql": "select * from `warehouses` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/VenderController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\VenderController.php", "line": 162}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.944172, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "VenderController.php:162", "source": "app/Http/Controllers/VenderController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FVenderController.php&line=162", "ajax": false, "filename": "VenderController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 15.823, "width_percent": 2.178}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.965534, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 18.001, "width_percent": 4.1}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.967488, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 22.101, "width_percent": 1.794}, {"sql": "select * from `bills` where `bills`.`vender_id` = 6 and `bills`.`vender_id` is not null", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 249}, {"index": 17, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 98}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.974066, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Vender.php:249", "source": "app/Models/Vender.php:249", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=249", "ajax": false, "filename": "Vender.php", "line": "249"}, "connection": "kdmkjkqknb", "start_percent": 23.895, "width_percent": 3.203}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` in (2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 249}, {"index": 22, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 98}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.976065, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Vender.php:249", "source": "app/Models/Vender.php:249", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=249", "ajax": false, "filename": "Vender.php", "line": "249"}, "connection": "kdmkjkqknb", "start_percent": 27.098, "width_percent": 2.114}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` in (2, 3)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 249}, {"index": 22, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 98}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.977607, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Vender.php:249", "source": "app/Models/Vender.php:249", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=249", "ajax": false, "filename": "Vender.php", "line": "249"}, "connection": "kdmkjkqknb", "start_percent": 29.212, "width_percent": 1.73}, {"sql": "select * from `taxes`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 790}, {"index": 19, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 102}, {"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 26, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 250}, {"index": 27, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 98}], "start": **********.979182, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "Utility.php:790", "source": "app/Models/Utility.php:790", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=790", "ajax": false, "filename": "Utility.php", "line": "790"}, "connection": "kdmkjkqknb", "start_percent": 30.942, "width_percent": 1.73}, {"sql": "select count(*) as aggregate from `bills` where `vender_id` = 6", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 267}, {"index": 17, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 99}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.980486, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Vender.php:267", "source": "app/Models/Vender.php:267", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=267", "ajax": false, "filename": "Vender.php", "line": "267"}, "connection": "kdmkjkqknb", "start_percent": 32.671, "width_percent": 1.409}, {"sql": "select * from `bills` where `vender_id` = 6 and `status` not in ('0', '4') and `due_date` < '2025-06-27'", "type": "query", "params": [], "bindings": ["6", "0", "4", "2025-06-27"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 230}, {"index": 16, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 129}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.982393, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Vender.php:230", "source": "app/Models/Vender.php:230", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=230", "ajax": false, "filename": "Vender.php", "line": "230"}, "connection": "kdmkjkqknb", "start_percent": 34.081, "width_percent": 1.537}, {"sql": "select * from `bill_payments` where `bill_payments`.`bill_id` = 2 and `bill_payments`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 158}, {"index": 21, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 234}, {"index": 22, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.984264, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Bill.php:158", "source": "app/Models/Bill.php:158", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=158", "ajax": false, "filename": "Bill.php", "line": "158"}, "connection": "kdmkjkqknb", "start_percent": 35.618, "width_percent": 1.666}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 23, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 234}, {"index": 24, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 129}], "start": **********.985475, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 37.284, "width_percent": 1.281}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 23, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 234}, {"index": 24, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 129}], "start": **********.9874, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 38.565, "width_percent": 2.37}, {"sql": "select * from `debit_notes` where `debit_notes`.`bill` = 2 and `debit_notes`.`bill` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 178}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 22, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 234}, {"index": 23, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 129}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.989098, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Bill.php:178", "source": "app/Models/Bill.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=178", "ajax": false, "filename": "Bill.php", "line": "178"}, "connection": "kdmkjkqknb", "start_percent": 40.935, "width_percent": 1.922}, {"sql": "select * from `bill_payments` where `bill_payments`.`bill_id` = 3 and `bill_payments`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 158}, {"index": 21, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 234}, {"index": 22, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.9903479, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "Bill.php:158", "source": "app/Models/Bill.php:158", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=158", "ajax": false, "filename": "Bill.php", "line": "158"}, "connection": "kdmkjkqknb", "start_percent": 42.857, "width_percent": 1.345}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 23, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 234}, {"index": 24, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 129}], "start": **********.991497, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 44.202, "width_percent": 1.281}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 23, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 234}, {"index": 24, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 129}], "start": **********.992614, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 45.484, "width_percent": 1.281}, {"sql": "select * from `debit_notes` where `debit_notes`.`bill` = 3 and `debit_notes`.`bill` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 178}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 22, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 234}, {"index": 23, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 129}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.993735, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Bill.php:178", "source": "app/Models/Bill.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=178", "ajax": false, "filename": "Bill.php", "line": "178"}, "connection": "kdmkjkqknb", "start_percent": 46.765, "width_percent": 1.089}, {"sql": "select * from `bills` where `vender_id` = 6 order by `bill_date` desc", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Vender.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Vender.php", "line": 222}, {"index": 16, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.995541, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Vender.php:222", "source": "app/Models/Vender.php:222", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=222", "ajax": false, "filename": "Vender.php", "line": "222"}, "connection": "kdmkjkqknb", "start_percent": 47.854, "width_percent": 1.794}, {"sql": "select * from `bill_payments` where `bill_payments`.`bill_id` = 2 and `bill_payments`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 158}, {"index": 21, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 175}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.997201, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "Bill.php:158", "source": "app/Models/Bill.php:158", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=158", "ajax": false, "filename": "Bill.php", "line": "158"}, "connection": "kdmkjkqknb", "start_percent": 49.648, "width_percent": 1.473}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 2 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 23, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 175}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.998395, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 51.121, "width_percent": 2.498}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 2 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 23, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 175}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.9997559, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 53.619, "width_percent": 1.602}, {"sql": "select * from `debit_notes` where `debit_notes`.`bill` = 2 and `debit_notes`.`bill` is not null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 178}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 22, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 175}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.000951, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Bill.php:178", "source": "app/Models/Bill.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=178", "ajax": false, "filename": "Bill.php", "line": "178"}, "connection": "kdmkjkqknb", "start_percent": 55.221, "width_percent": 1.089}, {"sql": "select * from `bill_payments` where `bill_payments`.`bill_id` = 3 and `bill_payments`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 158}, {"index": 21, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 175}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.004933, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Bill.php:158", "source": "app/Models/Bill.php:158", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=158", "ajax": false, "filename": "Bill.php", "line": "158"}, "connection": "kdmkjkqknb", "start_percent": 56.31, "width_percent": 2.178}, {"sql": "select * from `bill_products` where `bill_products`.`bill_id` = 3 and `bill_products`.`bill_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 67}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 23, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 175}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.0063028, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Bill.php:67", "source": "app/Models/Bill.php:67", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=67", "ajax": false, "filename": "Bill.php", "line": "67"}, "connection": "kdmkjkqknb", "start_percent": 58.488, "width_percent": 1.409}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`ref_id` = 3 and `bill_accounts`.`ref_id` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 73}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 152}, {"index": 22, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 23, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 175}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.007488, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Bill.php:73", "source": "app/Models/Bill.php:73", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=73", "ajax": false, "filename": "Bill.php", "line": "73"}, "connection": "kdmkjkqknb", "start_percent": 59.898, "width_percent": 1.089}, {"sql": "select * from `debit_notes` where `debit_notes`.`bill` = 3 and `debit_notes`.`bill` is not null", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 178}, {"index": 21, "namespace": null, "name": "app/Models/Bill.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Bill.php", "line": 163}, {"index": 22, "namespace": "view", "name": "vender.show", "file": "C:\\laragon\\www\\erpq24\\resources\\views/vender/show.blade.php", "line": 175}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.008619, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "Bill.php:178", "source": "app/Models/Bill.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=178", "ajax": false, "filename": "Bill.php", "line": "178"}, "connection": "kdmkjkqknb", "start_percent": 60.987, "width_percent": 1.153}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 5}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.012433, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 62.14, "width_percent": 1.858}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.admin", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/admin.blade.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.01385, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 63.997, "width_percent": 2.306}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0180361, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 66.304, "width_percent": 1.858}, {"sql": "select * from `email_templates` limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/EmailTemplate.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\EmailTemplate.php", "line": 27}, {"index": 20, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 10}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.0198178, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "EmailTemplate.php:27", "source": "app/Models/EmailTemplate.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=27", "ajax": false, "filename": "EmailTemplate.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 68.161, "width_percent": 2.562}, {"sql": "select * from `plans` where `plans`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Plan.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Plan.php", "line": 65}, {"index": 21, "namespace": "view", "name": "partials.admin.menu", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/menu.blade.php", "line": 13}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.02157, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Plan.php:65", "source": "app/Models/Plan.php:65", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=65", "ajax": false, "filename": "Plan.php", "line": "65"}, "connection": "kdmkjkqknb", "start_percent": 70.724, "width_percent": 2.178}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 3}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0556118, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 72.902, "width_percent": 2.755}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.057339, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 75.657, "width_percent": 19.154}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 4}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.061849, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 94.811, "width_percent": 1.858}, {"sql": "select count(*) as aggregate from `ch_messages` where `to_id` = 15 and `seen` = 0", "type": "query", "params": [], "bindings": ["15", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "partials.admin.header", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/header.blade.php", "line": 18}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.0637681, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "partials.admin.header:18", "source": "view::partials.admin.header:18", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpartials%2Fadmin%2Fheader.blade.php&line=18", "ajax": false, "filename": "header.blade.php", "line": "18"}, "connection": "kdmkjkqknb", "start_percent": 96.669, "width_percent": 1.73}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 6223}, {"index": 15, "namespace": "view", "name": "partials.admin.footer", "file": "C:\\laragon\\www\\erpq24\\resources\\views/partials/admin/footer.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.0660138, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 98.398, "width_percent": 1.602}]}, "models": {"data": {"App\\Models\\Bill": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=1", "ajax": false, "filename": "Bill.php", "line": "?"}}, "App\\Models\\BillProduct": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillProduct.php&line=1", "ajax": false, "filename": "BillProduct.php", "line": "?"}}, "App\\Models\\BillAccount": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillAccount.php&line=1", "ajax": false, "filename": "BillAccount.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Vender": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FVender.php&line=1", "ajax": false, "filename": "Vender.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\EmailTemplate": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FEmailTemplate.php&line=1", "ajax": false, "filename": "EmailTemplate.php", "line": "?"}}, "App\\Models\\Plan": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPlan.php&line=1", "ajax": false, "filename": "Plan.php", "line": "?"}}}, "count": 27, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 83, "messages": [{"message": "[ability => create bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-755676528 data-indent-pad=\"  \"><span class=sf-dump-note>create bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755676528\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.971422, "xdebug_link": null}, {"message": "[ability => edit vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1038884391 data-indent-pad=\"  \"><span class=sf-dump-note>edit vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">edit vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1038884391\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.972123, "xdebug_link": null}, {"message": "[ability => delete vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1293287726 data-indent-pad=\"  \"><span class=sf-dump-note>delete vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">delete vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1293287726\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.972411, "xdebug_link": null}, {"message": "[ability => edit bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1665314822 data-indent-pad=\"  \"><span class=sf-dump-note>edit bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665314822\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.995411, "xdebug_link": null}, {"message": "[ability => edit bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1226236077 data-indent-pad=\"  \"><span class=sf-dump-note>edit bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226236077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.003024, "xdebug_link": null}, {"message": "[ability => duplicate bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1855627461 data-indent-pad=\"  \"><span class=sf-dump-note>duplicate bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">duplicate bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1855627461\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.00348, "xdebug_link": null}, {"message": "[ability => show bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1053313780 data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1053313780\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.003942, "xdebug_link": null}, {"message": "[ability => edit bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1395058138 data-indent-pad=\"  \"><span class=sf-dump-note>edit bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395058138\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.00429, "xdebug_link": null}, {"message": "[ability => delete bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2126783317 data-indent-pad=\"  \"><span class=sf-dump-note>delete bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2126783317\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.004587, "xdebug_link": null}, {"message": "[ability => edit bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1263444704 data-indent-pad=\"  \"><span class=sf-dump-note>edit bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1263444704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.01012, "xdebug_link": null}, {"message": "[ability => duplicate bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1337300202 data-indent-pad=\"  \"><span class=sf-dump-note>duplicate bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">duplicate bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337300202\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.010415, "xdebug_link": null}, {"message": "[ability => show bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-961642999 data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-961642999\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.010728, "xdebug_link": null}, {"message": "[ability => edit bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-391661318 data-indent-pad=\"  \"><span class=sf-dump-note>edit bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-391661318\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.011031, "xdebug_link": null}, {"message": "[ability => delete bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-966517527 data-indent-pad=\"  \"><span class=sf-dump-note>delete bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">delete bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-966517527\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.011311, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2073329998 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2073329998\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.023647, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.024003, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.024217, "xdebug_link": null}, {"message": "[\n  ability => show account dashboard,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>show account dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">show account dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.024383, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.024684, "xdebug_link": null}, {"message": "[ability => statement report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1822200017 data-indent-pad=\"  \"><span class=sf-dump-note>statement report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">statement report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1822200017\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.024988, "xdebug_link": null}, {"message": "[ability => invoice report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-729901458 data-indent-pad=\"  \"><span class=sf-dump-note>invoice report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">invoice report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-729901458\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.025257, "xdebug_link": null}, {"message": "[ability => bill report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1389062114 data-indent-pad=\"  \"><span class=sf-dump-note>bill report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">bill report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1389062114\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.02556, "xdebug_link": null}, {"message": "[ability => stock report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2086113405 data-indent-pad=\"  \"><span class=sf-dump-note>stock report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086113405\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.025824, "xdebug_link": null}, {"message": "[ability => loss & profit report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-494494501 data-indent-pad=\"  \"><span class=sf-dump-note>loss & profit report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loss &amp; profit report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494494501\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.026089, "xdebug_link": null}, {"message": "[ability => manage transaction, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2014239947 data-indent-pad=\"  \"><span class=sf-dump-note>manage transaction</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage transaction</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014239947\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.026337, "xdebug_link": null}, {"message": "[ability => income report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1090831485 data-indent-pad=\"  \"><span class=sf-dump-note>income report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">income report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090831485\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.026596, "xdebug_link": null}, {"message": "[ability => expense report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-502548221 data-indent-pad=\"  \"><span class=sf-dump-note>expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-502548221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.02685, "xdebug_link": null}, {"message": "[\n  ability => income vs expense report,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1130899935 data-indent-pad=\"  \"><span class=sf-dump-note>income vs expense report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">income vs expense report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130899935\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.027109, "xdebug_link": null}, {"message": "[ability => tax report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>tax report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">tax report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.027376, "xdebug_link": null}, {"message": "[ability => show hrm dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1801251629 data-indent-pad=\"  \"><span class=sf-dump-note>show hrm dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show hrm dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1801251629\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.027575, "xdebug_link": null}, {"message": "[ability => manage report, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1128978020 data-indent-pad=\"  \"><span class=sf-dump-note>manage report</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage report</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1128978020\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.028233, "xdebug_link": null}, {"message": "[ability => show pos dashboard, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1781153261 data-indent-pad=\"  \"><span class=sf-dump-note>show pos dashboard</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">show pos dashboard</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781153261\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.028461, "xdebug_link": null}, {"message": "[ability => manage employee, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1660512449 data-indent-pad=\"  \"><span class=sf-dump-note>manage employee</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage employee</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1660512449\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.029376, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-683581403 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-683581403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.02992, "xdebug_link": null}, {"message": "[ability => manage set salary, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-292930271 data-indent-pad=\"  \"><span class=sf-dump-note>manage set salary</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage set salary</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-292930271\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.030484, "xdebug_link": null}, {"message": "[ability => manage pay slip, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1114964349 data-indent-pad=\"  \"><span class=sf-dump-note>manage pay slip</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage pay slip</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114964349\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.03101, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1155450680 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1155450680\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.031651, "xdebug_link": null}, {"message": "[ability => manage leave, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1851053572 data-indent-pad=\"  \"><span class=sf-dump-note>manage leave</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage leave</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1851053572\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.032245, "xdebug_link": null}, {"message": "[ability => manage attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-505818887 data-indent-pad=\"  \"><span class=sf-dump-note>manage attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">manage attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-505818887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.032866, "xdebug_link": null}, {"message": "[ability => create attendance, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1614122795 data-indent-pad=\"  \"><span class=sf-dump-note>create attendance</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"17 characters\">create attendance</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1614122795\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.033501, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-30095167 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-30095167\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.03401, "xdebug_link": null}, {"message": "[ability => manage award, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1206153049 data-indent-pad=\"  \"><span class=sf-dump-note>manage award</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage award</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206153049\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.034521, "xdebug_link": null}, {"message": "[ability => manage transfer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-739497581 data-indent-pad=\"  \"><span class=sf-dump-note>manage transfer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage transfer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739497581\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.035133, "xdebug_link": null}, {"message": "[ability => manage resignation, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1495638848 data-indent-pad=\"  \"><span class=sf-dump-note>manage resignation</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage resignation</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1495638848\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.035677, "xdebug_link": null}, {"message": "[ability => manage travel, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1156620007 data-indent-pad=\"  \"><span class=sf-dump-note>manage travel</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage travel</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1156620007\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.036451, "xdebug_link": null}, {"message": "[ability => manage promotion, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1582406552 data-indent-pad=\"  \"><span class=sf-dump-note>manage promotion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage promotion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1582406552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.037139, "xdebug_link": null}, {"message": "[ability => manage complaint, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-94205728 data-indent-pad=\"  \"><span class=sf-dump-note>manage complaint</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage complaint</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-94205728\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.037825, "xdebug_link": null}, {"message": "[ability => manage warning, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-35978924 data-indent-pad=\"  \"><span class=sf-dump-note>manage warning</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage warning</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35978924\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.038415, "xdebug_link": null}, {"message": "[ability => manage termination, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1110836087 data-indent-pad=\"  \"><span class=sf-dump-note>manage termination</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">manage termination</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1110836087\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.038975, "xdebug_link": null}, {"message": "[ability => manage announcement, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-793030018 data-indent-pad=\"  \"><span class=sf-dump-note>manage announcement</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage announcement</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-793030018\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.039604, "xdebug_link": null}, {"message": "[ability => manage holiday, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-78158349 data-indent-pad=\"  \"><span class=sf-dump-note>manage holiday</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">manage holiday</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-78158349\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.040222, "xdebug_link": null}, {"message": "[ability => manage document, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage document</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage document</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.040656, "xdebug_link": null}, {"message": "[ability => manage company policy, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1767510859 data-indent-pad=\"  \"><span class=sf-dump-note>manage company policy</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage company policy</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1767510859\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.041145, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.041426, "xdebug_link": null}, {"message": "[ability => manage bank account, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage bank account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage bank account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.041653, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.041888, "xdebug_link": null}, {"message": "[ability => manage customer, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1325926740 data-indent-pad=\"  \"><span class=sf-dump-note>manage customer</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage customer</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1325926740\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.042097, "xdebug_link": null}, {"message": "[ability => manage proposal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-611190101 data-indent-pad=\"  \"><span class=sf-dump-note>manage proposal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage proposal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-611190101\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.042387, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-924978847 data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-924978847\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.04265, "xdebug_link": null}, {"message": "[ability => manage vender, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage vender</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage vender</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.042861, "xdebug_link": null}, {"message": "[\n  ability => manage chart of account,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage chart of account</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage chart of account</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.043222, "xdebug_link": null}, {"message": "[ability => manage goal, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>manage goal</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage goal</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.043661, "xdebug_link": null}, {"message": "[ability => manage constant tax, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1202812641 data-indent-pad=\"  \"><span class=sf-dump-note>manage constant tax</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage constant tax</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1202812641\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.04387, "xdebug_link": null}, {"message": "[ability => manage print settings, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage print settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">manage print settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.04405, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1272860597 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1272860597\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.044216, "xdebug_link": null}, {"message": "[ability => manage user, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-194322683 data-indent-pad=\"  \"><span class=sf-dump-note>manage user</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage user</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194322683\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.044367, "xdebug_link": null}, {"message": "[ability => manage role, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1427597124 data-indent-pad=\"  \"><span class=sf-dump-note>manage role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">manage role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1427597124\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.044529, "xdebug_link": null}, {"message": "[ability => manage client, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1751794316 data-indent-pad=\"  \"><span class=sf-dump-note>manage client</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"13 characters\">manage client</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751794316\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.044985, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-259735796 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-259735796\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.045261, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-935560000 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-935560000\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.045491, "xdebug_link": null}, {"message": "[\n  ability => manage product & service,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1284386239 data-indent-pad=\"  \"><span class=sf-dump-note>manage product & service</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"24 characters\">manage product &amp; service</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1284386239\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.045869, "xdebug_link": null}, {"message": "[ability => show warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1817279357 data-indent-pad=\"  \"><span class=sf-dump-note>show warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">show warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817279357\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.046672, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-381560502 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-381560502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.047459, "xdebug_link": null}, {"message": "[ability => show pos, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1780033045 data-indent-pad=\"  \"><span class=sf-dump-note>show pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"8 characters\">show pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1780033045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.04827, "xdebug_link": null}, {"message": "[ability => create barcode, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1977004756 data-indent-pad=\"  \"><span class=sf-dump-note>create barcode</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create barcode</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977004756\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.049044, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2114294281 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2114294281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.049858, "xdebug_link": null}, {"message": "[ability => manage warehouse, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-332557370 data-indent-pad=\"  \"><span class=sf-dump-note>manage warehouse</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">manage warehouse</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-332557370\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.050622, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1380418953 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1380418953\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.051501, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1377112866 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1377112866\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.052292, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.053283, "xdebug_link": null}, {"message": "[\n  ability => manage company settings,\n  result => true,\n  user => 15,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"23 characters\">manage company settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.053742, "xdebug_link": null}, {"message": "[ability => manage company plan, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>manage company plan</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">manage company plan</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.054571, "xdebug_link": null}, {"message": "[ability => manage order, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1411041590 data-indent-pad=\"  \"><span class=sf-dump-note>manage order</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">manage order</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1411041590\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.054893, "xdebug_link": null}]}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender/eyJpdiI6IkQvaEpGMWNpQkNiSC93UFNEZ1gxK2c9PSIsInZhbHVlIjoiMEJtZ0l2Y1gvZjlnVEFiMjFJMEgvQT09IiwibWFjIjoiMzU4YjAxZjJlZmRjODhlMjMwOGE4MzQzZmEyZjFkMTYyMTNmYjI2MzJlNzMyMTM5MDM0NjYwZjc0MjI0ZTFhMCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/vender/eyJpdiI6IkQvaEpGMWNpQkNiSC93UFNEZ1gxK2c9PSIsInZhbHVlIjoiMEJtZ0l2Y1gvZjlnVEFiMjFJMEgvQT09IiwibWFjIjoiMzU4YjAxZjJlZmRjODhlMjMwOGE4MzQzZmEyZjFkMTYyMTNmYjI2MzJlNzMyMTM5MDM0NjYwZjc0MjI0ZTFhMCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-120746364 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-120746364\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-663348338 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-663348338\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-799343366 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-799343366\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1213434392 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986879027%7C88%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilk2V2wvMjErdDZxVEE1cG9Ba1VvZlE9PSIsInZhbHVlIjoiYjU0YUlUSjUwcnhybGpFNHJGbzYwOFJsa0pJaytWdU1YODFIV2IwZ2UxRjF6SUhmKzFBQzI5a2oyU292T1Y2cktYZ0p1cmNLbHRXMzQ4WTZnam43YmgzbXRnZTVyRkkvb240dVMrMWJURVNtc0hRaitFL2xyTG9vVnBCTmQyM0NNVjh5bWF1Ti9RMkNjdi9vQWFCd2FSQ1laTHBpYnZ1OFl0dkg0VlR0RnJOVG9oNzVqRG9PaUR6SEMxZnJmWkhmNkVwUlQ0a0pTRTBIQkY1REI5VVV0SkhxdlQxRHZzSnNHS2g4NXV2N29ic0xZVmdBS0ZKblJrdzFLSTMyNlVCNVlVcVhiTUxTUnN1d0lOTzVpbWhhWFdWU01yMWZoR2ZaZWdCdm9LTUxjNHdCK3JrWXg4OWlJZWdwQ0xaeGJma0FJK2d0ZHJaU3BONC93SWM0d1UzaFplNU5oOEVtZy9TY2UxcU9YV0RiRFJLS2pjeDlTWnlrYVV3VkhpWTlMSDlqMjJFNkpXWHFHdVFtR0syZlViMHNKOHNZWTVKeUxmaytFQnRhaUo1WDN3VFAyNWRpb3BKNEpwZ2FwUUdhTFo3djJ0WS9JL1NrSEV3L1RldTZ0ZjdrcGx1NlNRNFBXSVdpaWkxQ0lyNmthNWJMVXNucTQwV21Zb3dlb25MSUIycGciLCJtYWMiOiI0YmQ5OGQ4Y2JhYWM0N2E0NjRmYmEzYzI1YTUyMDdlODYxNDA2ZTU1M2U4NWRmYzE4NTA4MjRmYjFjMmVlY2E2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilp5UXFremRyNVUyL1dEMWtiUjlQTXc9PSIsInZhbHVlIjoiT0Nja3I4UFdLRWhJVzk4RWRrRGdQZndtSGdJdHEvalBoQ291MzE2dlpFaytIaC8rck9XTkNPYTFxamNxOWtBYUU0TFhWSVBOSG1tK3dDRHBWMVlZODIvM2NBUmZYUHk1SnA2MnM0WFVxbjR0cnVRVEVPMk84TDV1ZjVWZU45Q0JzRFhMYmJTWDJXQXhFQXVIeXluQ2FvOHFBT0ppQjdOb3Q3SUtXZlFmZU1PRTNkcUZUZkc5c2ZxV0Yvb00yNHB5MVFFdE1ja3UzMnJqTXlpQy9aa2hnUHdHaUdLZWliTlZhdnI3ZVMrcXlQWmRzUlpEK2t0Zk5kRitzSVhBVXh6akxwanlLNUR2OXEvNHFWZFR5SndaQlF2UUNxRXFNaHllOGhVbXlINndyciswb0FNdGx6YS80eGxYQTUzcTBmNUZ5M01pNXJFcWpNbHpFS1gzZk81MjRxZDlic1ZuZ3RTa1JsOWlGY3AvYVBvS0lpamRhU3ZvTTQyUXJaUldUMEZpWkVwQTA5aW95d044cWJFRHQrMVBSTE43bENqU3RXK042YWdsbVdVTFRqWG1OR3ExVmE1eFBvbzJLckw0bW1ZTVF5L1h3L1RaYmphL1hWazlKcGNqYnZUWWdyT1dQUkM4UUlBMzVTUlNDSDZKUWd2M093UmtWcS9kdGM0QzNkMEUiLCJtYWMiOiJlOGYxNWY3NGIzYWY5YWQyYTU5M2MxODVmMGE1YzgwYjFiMzBkZjBlODkyZGRkOTEzMDEyODgyOGQ0MzdiNWFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1213434392\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1114816111 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114816111\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-264731203 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:14:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IndqVkpvd0xIUk5YLzNrdHZaUVZLTVE9PSIsInZhbHVlIjoidURSMjA0c0ErY0liaSs1SEEybHpCZWZhb1pjVE1pdHdwRXhVeDc1RHk2TGJmczhjditYRjVaWkFMaWs2T2JrOEZSMElab3JXRTB6di9Ob29rTzQ1d1kzTVhWaVIwdzdQWkVMeHZLTGovVmwwKzJ0MWZVWUNiWDhEMzVHSTJuSVZJd3AyZ0orMFZTcmNHdnc0QXgrbkRDRDR2S1IwNW9YUU5tREJNSHc0VzBwMHMwUnZBaU9KVTY5cCtpWGk4bUdwZXRYWTMwTTN2TUl1dFlYemtSTlloVFFyUWlNVHlNZkRRb2pCNmpsZ0RFdTQ3YWNZSHdTaVdkek5wNjdnL0Z4UlpHVEk2cE9LZHJJWkl1NUxGZXhscDU5NnhMdmdmY29DNFZpcThmdU5pcElMSVNHMWFqYk9kRW1rZGdKOWtINksxT2hTYWR6Nnp4ZERFZC9xejFhbWFibURETk84NFBqWkRndkxVQ0luYm5YYUlxU01EYVRHd0tPQ1F5REZPTjhjU284TGMyUHJQYWpNNWs1b1RxQXIyajU0WEtOL2FuRHlRckhHSWNxUndSTHFKSWVFbnFvQSt1V1EvNmpPR0FBYWE0OHNSNWZjN1ZPQjhaOVVXUzNsOHlUWktIU0hiVTRVUGlUeVMrQXZpU0FwQW5ncWNXajU0ekhSejUrOE5iY00iLCJtYWMiOiI4NDY2MWNkOWQ4ZDlmZDg5YWZmNjM1NTA4NzNmMTdkNTI2NDQyNGZmMmYyZGM1M2QxM2NkN2U0NGE5MmFkYWQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:14:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImxrbnlaNVA1Q3p4dzZMamZrUnZSN3c9PSIsInZhbHVlIjoiZU0wd1IrZ1dPdjg0eUhCQllBQlFieEdOQVlXdnBvZUxTM3FjaXBndVdwQm1qODBWYVBjWGFveER1TEpXM1Y1c2ZxNG1FUG81dkNFRGZuWlEralJaL1FRZGpXQVpLdjNVODRZbnJkUU1XQXYzcm1IWm83a096aGYrYmZtbzFOM3NtZFRYS1Q0Q0JseTNLUk9LcW5yYmcyRUs3QytndXB3STRCd292VW5CTm1MbldKV1JTWUluR1F1K0ZnbFNaN0NrVGZxT2duOGZUdm9mUmlpMk03Qnh2WU5vN0NKWEhJRnd1YlVUT0o4NWtSRExqNFBiajBBOTdwd2Q1YnllaGJjMVVDRW00U1VLckxwN0tobUZIa3BQeEhuQkZqa0oyQlREcHBCT1BVTkwrdHp2QUR6a1ZEM1cxdGFXSi9XbmplYS9GNmRQNDdtbDRtZWpTd0wrNXFkUDFiVmt2K0hBZkh3NzJQOFNhbXhPWHZyM0U0OTVVc3ZFaFdaK2p5bDB5RWpLOVdpRjArUUtTWmFxU0p6eERaYjVFcnBsMW8wc0labzA0WHlIOXczQ2NaNXRwMXdOU0VjSXo2SFJ6dGk2ZEFmUXpybXRkc21KM1hxaXZQR0l0SW1lSUxXY292ZjRYUVdYejhubkhaalJ4V0tmY0xaak1sTDV3Z1JSOUpJRGM3OTEiLCJtYWMiOiI2ZDJjNjljNTVkZDMzOGI3MjQxNmVkYTRlMzRmMWY2NmMwNWVhNzAyODM2MTQ1NmFmMWEwMTM5M2Q4YTEyMTMyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:14:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IndqVkpvd0xIUk5YLzNrdHZaUVZLTVE9PSIsInZhbHVlIjoidURSMjA0c0ErY0liaSs1SEEybHpCZWZhb1pjVE1pdHdwRXhVeDc1RHk2TGJmczhjditYRjVaWkFMaWs2T2JrOEZSMElab3JXRTB6di9Ob29rTzQ1d1kzTVhWaVIwdzdQWkVMeHZLTGovVmwwKzJ0MWZVWUNiWDhEMzVHSTJuSVZJd3AyZ0orMFZTcmNHdnc0QXgrbkRDRDR2S1IwNW9YUU5tREJNSHc0VzBwMHMwUnZBaU9KVTY5cCtpWGk4bUdwZXRYWTMwTTN2TUl1dFlYemtSTlloVFFyUWlNVHlNZkRRb2pCNmpsZ0RFdTQ3YWNZSHdTaVdkek5wNjdnL0Z4UlpHVEk2cE9LZHJJWkl1NUxGZXhscDU5NnhMdmdmY29DNFZpcThmdU5pcElMSVNHMWFqYk9kRW1rZGdKOWtINksxT2hTYWR6Nnp4ZERFZC9xejFhbWFibURETk84NFBqWkRndkxVQ0luYm5YYUlxU01EYVRHd0tPQ1F5REZPTjhjU284TGMyUHJQYWpNNWs1b1RxQXIyajU0WEtOL2FuRHlRckhHSWNxUndSTHFKSWVFbnFvQSt1V1EvNmpPR0FBYWE0OHNSNWZjN1ZPQjhaOVVXUzNsOHlUWktIU0hiVTRVUGlUeVMrQXZpU0FwQW5ncWNXajU0ekhSejUrOE5iY00iLCJtYWMiOiI4NDY2MWNkOWQ4ZDlmZDg5YWZmNjM1NTA4NzNmMTdkNTI2NDQyNGZmMmYyZGM1M2QxM2NkN2U0NGE5MmFkYWQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:14:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImxrbnlaNVA1Q3p4dzZMamZrUnZSN3c9PSIsInZhbHVlIjoiZU0wd1IrZ1dPdjg0eUhCQllBQlFieEdOQVlXdnBvZUxTM3FjaXBndVdwQm1qODBWYVBjWGFveER1TEpXM1Y1c2ZxNG1FUG81dkNFRGZuWlEralJaL1FRZGpXQVpLdjNVODRZbnJkUU1XQXYzcm1IWm83a096aGYrYmZtbzFOM3NtZFRYS1Q0Q0JseTNLUk9LcW5yYmcyRUs3QytndXB3STRCd292VW5CTm1MbldKV1JTWUluR1F1K0ZnbFNaN0NrVGZxT2duOGZUdm9mUmlpMk03Qnh2WU5vN0NKWEhJRnd1YlVUT0o4NWtSRExqNFBiajBBOTdwd2Q1YnllaGJjMVVDRW00U1VLckxwN0tobUZIa3BQeEhuQkZqa0oyQlREcHBCT1BVTkwrdHp2QUR6a1ZEM1cxdGFXSi9XbmplYS9GNmRQNDdtbDRtZWpTd0wrNXFkUDFiVmt2K0hBZkh3NzJQOFNhbXhPWHZyM0U0OTVVc3ZFaFdaK2p5bDB5RWpLOVdpRjArUUtTWmFxU0p6eERaYjVFcnBsMW8wc0labzA0WHlIOXczQ2NaNXRwMXdOU0VjSXo2SFJ6dGk2ZEFmUXpybXRkc21KM1hxaXZQR0l0SW1lSUxXY292ZjRYUVdYejhubkhaalJ4V0tmY0xaak1sTDV3Z1JSOUpJRGM3OTEiLCJtYWMiOiI2ZDJjNjljNTVkZDMzOGI3MjQxNmVkYTRlMzRmMWY2NmMwNWVhNzAyODM2MTQ1NmFmMWEwMTM5M2Q4YTEyMTMyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:14:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-264731203\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-644210737 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/vender/eyJpdiI6IkQvaEpGMWNpQkNiSC93UFNEZ1gxK2c9PSIsInZhbHVlIjoiMEJtZ0l2Y1gvZjlnVEFiMjFJMEgvQT09IiwibWFjIjoiMzU4YjAxZjJlZmRjODhlMjMwOGE4MzQzZmEyZjFkMTYyMTNmYjI2MzJlNzMyMTM5MDM0NjYwZjc0MjI0ZTFhMCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644210737\", {\"maxDepth\":0})</script>\n"}}