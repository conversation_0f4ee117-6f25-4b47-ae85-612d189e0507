{"__meta": {"id": "X49498905d3f2dbc916678b38edf403b5", "datetime": "2025-06-27 02:15:58", "utime": **********.58889, "method": "GET", "uri": "/cookie-consent?cookie%5B%5D=necessary", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990557.820348, "end": **********.588906, "duration": 0.7685580253601074, "duration_str": "769ms", "measures": [{"label": "Booting", "start": 1750990557.820348, "relative_start": 0, "end": **********.146665, "relative_end": **********.146665, "duration": 0.32631707191467285, "duration_str": "326ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.146674, "relative_start": 0.3263258934020996, "end": **********.588907, "relative_end": 9.5367431640625e-07, "duration": 0.4422330856323242, "duration_str": "442ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51509000, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET cookie-consent", "middleware": "web", "controller": "App\\Http\\Controllers\\SystemController@CookieConsent", "namespace": null, "prefix": "", "where": [], "as": "cookie-consent", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FSystemController.php&line=2370\" onclick=\"\">app/Http/Controllers/SystemController.php:2370-2422</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00188, "accumulated_duration_str": "1.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 70}], "start": **********.184052, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 84.043}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 71}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/SystemController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\SystemController.php", "line": 2373}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.188175, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 84.043, "width_percent": 15.957}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]"}, "request": {"path_info": "/cookie-consent", "status_code": "<pre class=sf-dump id=sf-dump-1330628206 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1330628206\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-797813588 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">necessary</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-797813588\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-102092902 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-102092902\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1212936824 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990546209%7C15%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik94NnlsT3o5R0JiZ1R1bzBYSlhpNFE9PSIsInZhbHVlIjoiQ1B6Vk1MTEZscVNzMWRJd0FsQm1BNk9aMFV1KzlUQXg3b3RiYjBNSmVGdVIzdjhxdzhnRjFuLzBtNDA2bzUvcHhkcnVkSVlBTXlmOEhrUjlMVnN0WEkyb1FMNlNRNFBuV2wvSlhkd3BZdzRQcmJkNHdPdU1YSE1Zam1Va1k5OGlOWnNjaVZwbys5U0EwNWl3ZS9OMmxteXBQNzgzZmtEMDIwdTNKRlh2aWhvWUg3WkF6RkFpK1BRT25CczhjVHIraVcrUlEyVEVkbGcwa0hDOWpHM1lPakdoYWVNR0x0U05GMlR2Rm1ROGJIOVhaSHk0UzBmM2NrTm83Y0dLQlg5TEd6eE5OV1RsM0xJRGc4YjZkN3Y0bHVXQnRJOXRQRTBiUlNNQlFERTh4TWtlYlQxZk1lSUtOcDY0eUpiZVczcThEYXNiSEk0eVo4aGRCTVNVeEZGRGc1NHhCdWNlK0ZySGhMcUloUG5DelQrV1JZUEV4c0ZsNXYvNi9KZ05HYkk4TUpvbytvdGFteEN1dkZRZkJXdStSdGt6KzZRUjNJVWVKTjZaRVNWVk5VU1BXcU5aM01NMk5tSm04eUN1SnZaY3RTc05XbGgxeWhERVVYV1BrYUR2bThZdHhvNlNWRSs4VVdsQVVFZTB0ajJYR0srSkQ3MytuSzRGUUZZSUgvQXMiLCJtYWMiOiI1MDk1ODZmNDEwYzQyNWJiYzMwYTFmNzUzNjc5ZWNiM2RhZTkzMjE5MzIwODgwZDQ0ZDZiMjNiNjVlMmU0NGVmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IktzbFNDektHZjUwRmI1dGlDRnFUL2c9PSIsInZhbHVlIjoiS3JKTkE4OGozRTkvWFBWYVVQOXV4aXUvZUF6YVhhRk83MFVFbFA2WG03ZVJtZW5WUXZKUWhDR3lsTmg2aldUNFdQTWFYWVVvZzBRSGZjeFN1dlExamVMMlJlNUxJRkVSMEdReXVZekNSTTVJdlRPSFJndEY0YVZUcERBVlNUY09hOWNDeDlyZHNUbnUydG1aMlZQajRGY0FJK1FoU1NHazNsQjM3UHFPLy8wVHBBY1pvTWp0bjFXVWE4aVFhMEN0VllUZ3BCTEJ4L0E2TkpmeUszTUhuMEg2R3IxUkVoT1pSQmJKWlU2SUVZTkdGUFNIRTJqTXpkeVVZMWtIS2s1OWk5RjM1R2poRkVnR2V2VXk5L25NanlSdllQczYyWS9qcE5iMzl4V0I3NVo5THZJMW1yTEIrajF4ZGs4dzhIdmxJSXhpN2ZpZkl3NTl4WWFYVi81TnJwc1BKTHlOczhreEF3eVdveGVhdXh4ZlhtNlhlZHZReEx6VEpNbnc2WUp0YVJDMGtoZ0srVk1wbTA0NTNSYTNpYUVrOVEvZ21Da0k4eDdFOVMvMXUrZmZjWndYbEUvd2ZhdDI4WUpTRVdvUWhjd2RaM3dFSHpHV0VQeE1pd1VQU040cllQTFo2TnBDclVKRzl1RVYzWFU0WjJleUZ6RUxVQWx6ZXJMSkFWUFEiLCJtYWMiOiIzN2FjOTc1ZDNkMjQzYTkyZjAxOTBkYmI0NjI4MWRmMDc1YjQ0NmYwN2I3MTk1ZDNhNDQ4ZTg1NTk4ODNmNzE4IiwidGFnIjoiIn0%3D; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212936824\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1585183281 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1585183281\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-214609091 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im5DSGljems0QXZLN2VmelBHckhva0E9PSIsInZhbHVlIjoiSmFuUU1IOVN4cU84SjFoNWlvQVh3QlBjbHRVOCt3NUZzRDQ2a1ZSc1o5YU9YdEVXQnRNaGw5NFV2UDI4VDJBZ0Uwd3VxNDNLZFZqVHRIYThoV294L0xWUVI4ODA3Z0RocDA3WDB2STZXWWsrcWhpUW03ZHYzZ2h0Vlg1cllqeWtVUjRtU21HRGxxTjFYa0hRVFZUZDMwaTFDTFFWN2UraG5RZFVHTlFlNUdEbUZkaWxvbXF2cURrcUxCcUFyZzVHK09DbGJ3M3J6cWpPbUVvV3VQSm5JS3pqSXJTTitlY292cHhldTJsdllhcDgrcXphRVJFNmtLUTltb2NGeGhUU0h6eW1YVER6U2ZjVUxCVjU1MkdJY2twOVpkZjFrd2R3ODBPQklFTUk3K1V3ZitJell6Zkk1Rmx0VXdpSVBPeVZJTVR3N1dZTnF6TTdlTDFCZTdVaEN4dkRuV1BUOVBxd2tDNTlHSXFEd2ZnSm1PWEVic0M1Z05lQXhXVWR0YlkvRkVSNVlqbDcyeFlyemc2MFhrRHlKSkRSUDZBZlRlNVRrdWpscjVBVExKcHdsZysxN0MySWtWWUNtWkYreTN5akUya0J2S2xuelIrM1B0YXVlUFdwdGkzNkszMEFJTHh0SGVOZGZ4dU5tNVU4NUtOamNveUpnVWh5VHNZcC9yTC8iLCJtYWMiOiIzM2RmMzNhZjNlZDY2NTYwYTdhYjI2MDg4ZDgyZGFhYmZlM2MxZjRjOTRlMTAwODA3NGZhNTM0YWE2NDkzMmM4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Imlwa3FkMmhzVFJGRUMrblpPcy9BcHc9PSIsInZhbHVlIjoieTBxVFpWVXY1ancrbWZPdDRCNW40c3FlU2xJZkt4Y0FDcWI0ZVBwWVREU2lLaHZGc0dLdzl1dzl4T3hxdWNBOEdjZEt2U3NlbENqT1o0SXcybmdpK3hzN1ZqZVRwYXBIUFJrYStMVEJBWUVlcnZoMktOY1A2S2xUQ0F2dnFDQ1o2dXQzcUUySlZZZTc1Q01pcitDbTRqVEFRMVMweGpUL2ZNK3RrTThhS0J4UTZYWWM0NWJGT0hQUTJiSUVTVnQwak1zV09SR3ZTTkdWSVk2RHBTVlk4K2dvcXVxTjZaSnV4OGxvVGJFNVh4U0pFSU9mRWp6MGgzZWNZdTkrUmdteEppd0N3dWpqL2ZxOHh5Sk1tNnk5am5tVFNPUVo4a0lyOS81Y0xkUzV4cjRJSkhVRnNNaGRvcFZSdEtTOXZBc2R2ZDlyc3p3OUgyV3Z2TVZrQ2ZJTUdzVnJHRTY2WlQ0OFNudlpRellXeEphSHkrcXptVGttR2FmYU5IYW5UalNjMVJHUllqR0pyQTZaTHE3Z3JGT01GVDVoR0g3RWpCNnFXWXBOSG8xY2duRnRpZ1FmOU5hWHFEODZwZERjS1VvTEVuKzk2YnZVK1VybU9wVUk2a29ROEZWZE1hVFI1UEVHbzVLN2tiV2tFTWdldTJvcVFxdkx6Sk1XalVKOWVmMmgiLCJtYWMiOiJkZDNjZDBhNzJhZjI2N2Q4N2U1ODJhZTEzYTExY2Q2Y2FkYThlYmJlZTIzYzI3ZGU1NmQxZjc0Y2E3NTI2NWRjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im5DSGljems0QXZLN2VmelBHckhva0E9PSIsInZhbHVlIjoiSmFuUU1IOVN4cU84SjFoNWlvQVh3QlBjbHRVOCt3NUZzRDQ2a1ZSc1o5YU9YdEVXQnRNaGw5NFV2UDI4VDJBZ0Uwd3VxNDNLZFZqVHRIYThoV294L0xWUVI4ODA3Z0RocDA3WDB2STZXWWsrcWhpUW03ZHYzZ2h0Vlg1cllqeWtVUjRtU21HRGxxTjFYa0hRVFZUZDMwaTFDTFFWN2UraG5RZFVHTlFlNUdEbUZkaWxvbXF2cURrcUxCcUFyZzVHK09DbGJ3M3J6cWpPbUVvV3VQSm5JS3pqSXJTTitlY292cHhldTJsdllhcDgrcXphRVJFNmtLUTltb2NGeGhUU0h6eW1YVER6U2ZjVUxCVjU1MkdJY2twOVpkZjFrd2R3ODBPQklFTUk3K1V3ZitJell6Zkk1Rmx0VXdpSVBPeVZJTVR3N1dZTnF6TTdlTDFCZTdVaEN4dkRuV1BUOVBxd2tDNTlHSXFEd2ZnSm1PWEVic0M1Z05lQXhXVWR0YlkvRkVSNVlqbDcyeFlyemc2MFhrRHlKSkRSUDZBZlRlNVRrdWpscjVBVExKcHdsZysxN0MySWtWWUNtWkYreTN5akUya0J2S2xuelIrM1B0YXVlUFdwdGkzNkszMEFJTHh0SGVOZGZ4dU5tNVU4NUtOamNveUpnVWh5VHNZcC9yTC8iLCJtYWMiOiIzM2RmMzNhZjNlZDY2NTYwYTdhYjI2MDg4ZDgyZGFhYmZlM2MxZjRjOTRlMTAwODA3NGZhNTM0YWE2NDkzMmM4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Imlwa3FkMmhzVFJGRUMrblpPcy9BcHc9PSIsInZhbHVlIjoieTBxVFpWVXY1ancrbWZPdDRCNW40c3FlU2xJZkt4Y0FDcWI0ZVBwWVREU2lLaHZGc0dLdzl1dzl4T3hxdWNBOEdjZEt2U3NlbENqT1o0SXcybmdpK3hzN1ZqZVRwYXBIUFJrYStMVEJBWUVlcnZoMktOY1A2S2xUQ0F2dnFDQ1o2dXQzcUUySlZZZTc1Q01pcitDbTRqVEFRMVMweGpUL2ZNK3RrTThhS0J4UTZYWWM0NWJGT0hQUTJiSUVTVnQwak1zV09SR3ZTTkdWSVk2RHBTVlk4K2dvcXVxTjZaSnV4OGxvVGJFNVh4U0pFSU9mRWp6MGgzZWNZdTkrUmdteEppd0N3dWpqL2ZxOHh5Sk1tNnk5am5tVFNPUVo4a0lyOS81Y0xkUzV4cjRJSkhVRnNNaGRvcFZSdEtTOXZBc2R2ZDlyc3p3OUgyV3Z2TVZrQ2ZJTUdzVnJHRTY2WlQ0OFNudlpRellXeEphSHkrcXptVGttR2FmYU5IYW5UalNjMVJHUllqR0pyQTZaTHE3Z3JGT01GVDVoR0g3RWpCNnFXWXBOSG8xY2duRnRpZ1FmOU5hWHFEODZwZERjS1VvTEVuKzk2YnZVK1VybU9wVUk2a29ROEZWZE1hVFI1UEVHbzVLN2tiV2tFTWdldTJvcVFxdkx6Sk1XalVKOWVmMmgiLCJtYWMiOiJkZDNjZDBhNzJhZjI2N2Q4N2U1ODJhZTEzYTExY2Q2Y2FkYThlYmJlZTIzYzI3ZGU1NmQxZjc0Y2E3NTI2NWRjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214609091\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1549477842 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1549477842\", {\"maxDepth\":0})</script>\n"}}