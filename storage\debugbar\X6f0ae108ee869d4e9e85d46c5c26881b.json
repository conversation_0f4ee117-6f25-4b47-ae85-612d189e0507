{"__meta": {"id": "X6f0ae108ee869d4e9e85d46c5c26881b", "datetime": "2025-06-27 02:24:43", "utime": **********.579233, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.118735, "end": **********.579246, "duration": 0.4605109691619873, "duration_str": "461ms", "measures": [{"label": "Booting", "start": **********.118735, "relative_start": 0, "end": **********.513171, "relative_end": **********.513171, "duration": 0.3944358825683594, "duration_str": "394ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.513181, "relative_start": 0.39444589614868164, "end": **********.579248, "relative_end": 1.9073486328125e-06, "duration": 0.06606698036193848, "duration_str": "66.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45274912, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.02226, "accumulated_duration_str": "22.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.542213, "duration": 0.02181, "duration_str": "21.81ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.978}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.572235, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.978, "width_percent": 2.022}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1560057627 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1560057627\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1105402597 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1105402597\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-963990451 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-963990451\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1571926602 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991028607%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InlQMXJNWTAzaFVUakhtUXZSTjZ1WGc9PSIsInZhbHVlIjoiemU2QXlTcS9EY2JMSHVnTkQ2UGRTOXB2cTduQWh2cHoyTSt1NUlJV3RFV1d3Q0Qwakk1Skp0OFZZYlNXeTkzQ2t2T0h6SUh2d3R4QjJQVzRuRDFWWXgvSzRZdmVlZEU5OHVLMWpUS0pFN0prY3RmVHN2ZFFtc09ERmlGb3ZIczZmbGJ0VFhrLytRRGZNQzZiYmhBVVR5UTFuODR2SDcvaG5zN0ovUmZXMXJXKzI0MjBTWEhsb1JHTlFGclRlL1FzcjFYK1R5eU9ubnAzYmlJUkswZGJNWXZLZENRaHNpS1ByOTRmc2xpTy91WURqL0JIY3Z4N2svSXZma3ZGZGdkYWo5K3RlSkhvNERiUCsvYXhqSmpHODcvN1VQb2VMOVc3ZUtoY0dNRUVHY25yeGcwTFVNbkM2KzJOMnJKRHFlaC9NNEViMHVpVFQwZjB5RVhqTzYwRFdaei90eHdnT1BqbnY5UVZQM2ZBZEZJL1hRNmp4K1pmVFhxS1VrY0lxdXl4ZWtqZ20xZHZOVUdXZ3JWZmlPVFhSR1JBWjl2clVuUVphdkc5UDZLVENLQkIzNXdyc3dwTHFBN1RCclBIRmdDM0s1aWowalR3d2QvRUMzcW9GakwxcTZueDJzS3lEeTJ2WlU5UTZTY094b0lCNGhqZy84Wm54b3crN0xMYlJ4Uy8iLCJtYWMiOiJiYjk3MmRlNDY5YmIwNjZmMDhjZDNjOTRlY2I5ZGRjMjJhNjVkYWE4NzZhMzIzOGFkMjQzOWQ5MDczN2U5MzhhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBOS09EQXhHR1NkNGhDSytDc2lTeEE9PSIsInZhbHVlIjoiWDY5RE90UjhDNEtMamo2bUYwS21STDdjRnRFYm1ndC9yWHBIV25nVUhMU2JzQ1JwL09tWDNYWEx6eFI4NTZ4S2RxSEhBU2VybHVGTTV2L0ZRSmx6dE5MbE41WVpRTXgzR2FRZUErcXFwcysyY3Y4T2xqWjMyYWxKNDUyQm0rQlVQWHFBNk1XK3JwQXVpRkh2UlVHMUFaM01NMGEzWkEyU1ZZOUtoNnA4bGdLOGFoSGtRNG94V0xvcUp1aVJnR1lGaWhYTVhJTWlBM1QvcWJ5d2xiUFFOdlpteFhDT0g1T0pmZ0VmOFFRTmQ2bnNwQkw5bnA0MThzMFhQdXNsSDB0MHUwODIvSmd1TmpsRE9JRXlEYXdSdjBZZnNDeE9hbSs1TTlPUWFRQ0ZOVWVlT2xNNkwySUo0SVhQd3pGRDRjancrbllzTk5mRXFxMU5NbXprUVJnOGEraDFqYXhWbW5WM2EwMkRlV3U4QnJpeUV6Y0dKZ0hlZlcvSy9yOHA1K3lablhXUlJvdHJwTnQxUWZrZ1VOZHJiRXV5NnhIb2NZcEFabU1ieWxPZUhpdUxLY0tlWVN0NGR6Ymh4SmRIc1o3UndGSFJIaTdSbUxiTWE2SjZXNVU3OUxsSWVKa1RNQzJjOTJlWG83VThOeW1aa2VEeGRFVmpLYzFCUkJpTW5yNUkiLCJtYWMiOiJmYmFjMGY1NDFmYmUyMjdmNjAwYWQ0YWFkODZlZGVmOWM0ODk4ZGU4ZWU4NWI5MWI5OTk2MDMwMjc4MDA4NWQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1571926602\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-65110885 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65110885\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1357117319 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:24:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImIvZ3JVNjVkUVg2NERYR1lTaS80SHc9PSIsInZhbHVlIjoiNFNXV3A2V3I4Nk5QbGxuL2hjOE54R0NONXd4dUJSRTBZZmZ0aEcxdWMvZFh4cTBrbm5mV045ZW9JWHNWeGp6S3NRY01rRmNNSHRUMkNhR1Y5aVluS3IxOWxRNFZXa1ZwTTBFQ2I4d1Y4alRzcytrTlFFdnoxL0ZJNFU0T1UxQm9wK0lKdVoxbTZaNW1XSm1MVVJTUDc3c2F6WVovNG5HdDdqR3BpQU9GdUFqdE1NQ3F3LzVrakpmVXoxK1ZRN3BWbnl0YS9mNDRuSldMb3FXbEc1WnFlS3ZtbzM1YWxvMjJ4V1BjbGZVUEJZN3pERythb3BLc2NKZU5XM3RCanZGS0VyaVAzS093NXhCYTRJT053eC90TVJxNmdTRjl4bFBaV0dLNXg0Wi9mZkQ4bFFBTlprLzhvTngxWlpxMzNMblhibkNUWFAvaVNBV05TWUc4WENQeW1uSHl4NkFQb3puMjMyWUZFU0liSmdSeWdDaGt0UnFCNkhocnhNMzVsVDV4TUJjeFAwZVhXUXRoY2VwOS9Ldy9SOE91Wk8vY3VkcE1kV3FuRVhhUE5tZWdndDBwZmpMc2g1SWZPTzdieWNoR295cS92WDVYam1NckNmSDVhL2N2MWgwQisrS2pXQ1VQaC9WZS9FQUhKTHRkNjlFVTFmdVp4MDErbW5mdmQvL2kiLCJtYWMiOiIxOWM5MGQ5OWZiZjIwZDRlYzZkNWQ5MzQwY2VkYTA5MDVmODEzYmM0MjYyYzYzYzlhOTc5ZTM1NmZiNjlmNTgzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:24:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkxPKzduRWhsd0FKZHplWktnWkFTWXc9PSIsInZhbHVlIjoiOXgzYkdHZkN6YzQwWlVaWWQ0TnZydlhPZnM3TXhwRS90bXlMaGJybGR2aFF2WHExcUVQeVhYeWV4WW1rNTQxcUtTc1g1SENpNUQ2SVBNL24wZENQb0t2YlZmLzA0USsvbG1vUXZzZ0oyeVZxOXFPUmNlQzhId0Zmb2xnS0NDR3M0Y3VYTDFSZU8zcXp0anMzUDR0WTgwVHNkd3Z0R2NuU3YwTmxZZHhCbWFpWHFzUHVkUVRkVXFPQlcySzM3MWtQbHp0WCs4dUZWajNZMno1cWRxbWRuR2ZQUmtYd3NqTTNYbldLUzdkTjhXUDgyRU0zVzBMNVJmWW42MHIyMGdORy9lRG9rdzFtYUxPWnJxb3ZodEFpckFZMndQNWw5Z1JVQVA2Snl0K1NacjhWSHVBZW9mU005R0R5Z3Q3bm5TSDRrTVJYMmVoVGtkL0JNZjRPUUI4aDM5bjFFbGpxWlFWVUpJdk1ORVBXaUR3dDJOVnJNVlpiM1I0bGl2WHV4TWVBa3FOQ0hpUVZVVFpudjU4YkpuT2hCbXlMZGJ5NyszUjNhM0owQUQyMUZ1bkM1OVpvZ3JuMnZtMUlpWE5sOE1IUkJMeEQzbmEzZ3QvM2pXc3lKZUx5dmQyd0o4V1d3cGU5d1VRSWlwSmN4SEpjQUhUMTBMWGxWbndUbTFRa3hVVXEiLCJtYWMiOiJhN2IzMjIyY2JjNzQwNDNjNjQ4MDA2ZTg4MDcwMTNlNjE5ODFkZmNjMGY1MzE1YTYyMjJkYjcxNjU4MmY0NDFhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:24:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImIvZ3JVNjVkUVg2NERYR1lTaS80SHc9PSIsInZhbHVlIjoiNFNXV3A2V3I4Nk5QbGxuL2hjOE54R0NONXd4dUJSRTBZZmZ0aEcxdWMvZFh4cTBrbm5mV045ZW9JWHNWeGp6S3NRY01rRmNNSHRUMkNhR1Y5aVluS3IxOWxRNFZXa1ZwTTBFQ2I4d1Y4alRzcytrTlFFdnoxL0ZJNFU0T1UxQm9wK0lKdVoxbTZaNW1XSm1MVVJTUDc3c2F6WVovNG5HdDdqR3BpQU9GdUFqdE1NQ3F3LzVrakpmVXoxK1ZRN3BWbnl0YS9mNDRuSldMb3FXbEc1WnFlS3ZtbzM1YWxvMjJ4V1BjbGZVUEJZN3pERythb3BLc2NKZU5XM3RCanZGS0VyaVAzS093NXhCYTRJT053eC90TVJxNmdTRjl4bFBaV0dLNXg0Wi9mZkQ4bFFBTlprLzhvTngxWlpxMzNMblhibkNUWFAvaVNBV05TWUc4WENQeW1uSHl4NkFQb3puMjMyWUZFU0liSmdSeWdDaGt0UnFCNkhocnhNMzVsVDV4TUJjeFAwZVhXUXRoY2VwOS9Ldy9SOE91Wk8vY3VkcE1kV3FuRVhhUE5tZWdndDBwZmpMc2g1SWZPTzdieWNoR295cS92WDVYam1NckNmSDVhL2N2MWgwQisrS2pXQ1VQaC9WZS9FQUhKTHRkNjlFVTFmdVp4MDErbW5mdmQvL2kiLCJtYWMiOiIxOWM5MGQ5OWZiZjIwZDRlYzZkNWQ5MzQwY2VkYTA5MDVmODEzYmM0MjYyYzYzYzlhOTc5ZTM1NmZiNjlmNTgzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:24:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkxPKzduRWhsd0FKZHplWktnWkFTWXc9PSIsInZhbHVlIjoiOXgzYkdHZkN6YzQwWlVaWWQ0TnZydlhPZnM3TXhwRS90bXlMaGJybGR2aFF2WHExcUVQeVhYeWV4WW1rNTQxcUtTc1g1SENpNUQ2SVBNL24wZENQb0t2YlZmLzA0USsvbG1vUXZzZ0oyeVZxOXFPUmNlQzhId0Zmb2xnS0NDR3M0Y3VYTDFSZU8zcXp0anMzUDR0WTgwVHNkd3Z0R2NuU3YwTmxZZHhCbWFpWHFzUHVkUVRkVXFPQlcySzM3MWtQbHp0WCs4dUZWajNZMno1cWRxbWRuR2ZQUmtYd3NqTTNYbldLUzdkTjhXUDgyRU0zVzBMNVJmWW42MHIyMGdORy9lRG9rdzFtYUxPWnJxb3ZodEFpckFZMndQNWw5Z1JVQVA2Snl0K1NacjhWSHVBZW9mU005R0R5Z3Q3bm5TSDRrTVJYMmVoVGtkL0JNZjRPUUI4aDM5bjFFbGpxWlFWVUpJdk1ORVBXaUR3dDJOVnJNVlpiM1I0bGl2WHV4TWVBa3FOQ0hpUVZVVFpudjU4YkpuT2hCbXlMZGJ5NyszUjNhM0owQUQyMUZ1bkM1OVpvZ3JuMnZtMUlpWE5sOE1IUkJMeEQzbmEzZ3QvM2pXc3lKZUx5dmQyd0o4V1d3cGU5d1VRSWlwSmN4SEpjQUhUMTBMWGxWbndUbTFRa3hVVXEiLCJtYWMiOiJhN2IzMjIyY2JjNzQwNDNjNjQ4MDA2ZTg4MDcwMTNlNjE5ODFkZmNjMGY1MzE1YTYyMjJkYjcxNjU4MmY0NDFhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:24:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1357117319\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1641546143 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1641546143\", {\"maxDepth\":0})</script>\n"}}