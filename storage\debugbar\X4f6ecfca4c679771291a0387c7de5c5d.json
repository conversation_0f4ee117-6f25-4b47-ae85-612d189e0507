{"__meta": {"id": "X4f6ecfca4c679771291a0387c7de5c5d", "datetime": "2025-06-27 02:23:43", "utime": **********.96831, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.501927, "end": **********.968326, "duration": 0.4663991928100586, "duration_str": "466ms", "measures": [{"label": "Booting", "start": **********.501927, "relative_start": 0, "end": **********.832811, "relative_end": **********.832811, "duration": 0.3308842182159424, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.83282, "relative_start": 0.33089303970336914, "end": **********.968328, "relative_end": 1.9073486328125e-06, "duration": 0.13550806045532227, "duration_str": "136ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50947520, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.07671, "accumulated_duration_str": "76.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8601182, "duration": 0.014539999999999999, "duration_str": "14.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 18.955}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.882746, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 18.955, "width_percent": 0.613}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('750', 0, 22, 8, '2025-06-27 02:23:43', '2025-06-27 02:23:43')", "type": "query", "params": [], "bindings": ["750", "0", "22", "8", "2025-06-27 02:23:43", "2025-06-27 02:23:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.893231, "duration": 0.05441, "duration_str": "54.41ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 19.567, "width_percent": 70.929}, {"sql": "select * from `financial_records` where (`shift_id` = 46) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["46"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.950269, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 90.497, "width_percent": 0.978}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (46, '750', 22, '2025-06-27 02:23:43', '2025-06-27 02:23:43')", "type": "query", "params": [], "bindings": ["46", "750", "22", "2025-06-27 02:23:43", "2025-06-27 02:23:43"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.952879, "duration": 0.00401, "duration_str": "4.01ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 91.474, "width_percent": 5.227}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-27 02:23:43' where `id` = 22", "type": "query", "params": [], "bindings": ["0", "2025-06-27 02:23:43", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.95788, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 96.702, "width_percent": 3.298}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "success": "تم تحديث الرصيد المفتوح بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-74149832 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-74149832\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1103142378 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1103142378\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-829291692 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"3 characters\">750</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-829291692\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-331995228 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">67</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6InI0REl0SHFhK1Ywc3Z4Q005eGhPUWc9PSIsInZhbHVlIjoiZjFoVUlNRElKQWltcndDVlozTXdNc3pPNDlUV1hTYm5nRklvSjZwU2VPSy8vakJQWkxGQVV5UlErakloM1RSTWtyYlFhWDlCWHNLRFFQKzQydVZwRjFtV2F2Wll0eUpYenRqbHhLUjlSR1RJUVVLaDZ3OVhPUkZZbmw0SUsyMW9JR05nSDBTQW51WWdZSlJXTklqTFcweno0dmpMVkFPYnQ1eDhQb2JqTzVTNDF5NmIrQTcyWjQ5RWRNakNZbmFHVlpNWEFGUEFKeE9MenNRRlcrcGV1Y0pBTmFyWnpTcklUbkJSZlNGaW1mQWxielFma2RaUHFPZnZZVmhJK2JzbXJiOHpCa0hnMmxpZk56ZzNiYkhlUWxuVnhvampLY1Qza1ZKK0dQWjA0UFk3UndVR2JsWURpeG9tZUxnZXIxTUN2ZUVqNkJ1dVFmV2c5Ymd3QUFuWFZaRSt4N0NIZTl6L2M4b0dod3dLWENDOVFTUHdPZkhXS2ZyOTgxaUNqU0lyT2JlaENXQWtadWZHYjA5TzBzTElJZ0xnbUdvbXVQYURqL2FCVm5OYzNrYVRwYlNOWERNYklhTTVxQ05uR2RjNE40OC9RbVNsSG1hNmd4N0ZzaGw5NGl4c1lyajZia3ZHMG9TbjB1NzFtRUQrTDVsT1VQTzFndElxMmtZYlJNOVAiLCJtYWMiOiIyMzcwZWYxYjc3YTk3ZjlhMjc1ZTBkZTc2NzIzYWQ3OTQyN2UwMzhkYmZmY2EwMTkxOGI5MzQyY2YzNzkzYTMyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjdQdk5jUjE3d2FMUlNJcllYc1Q3aEE9PSIsInZhbHVlIjoic2t0ZTdKZkVIdFNuSUdBVC96YjhqRzZPUmxZTjNDTml5TWtzeHRGT2l6Y0VRY3c3cTVyN3ZjTk03MExDVmFRd0RxRENRd2NnSnc3WVpnVmNtZ1htaSs3ODZkVlFrY3IzQS9MTUdOOXY1U1FQVi9FaE9xTFhWWDdpT2RuaURWVDIrSGhBa0R4MHJJSitOb3doWGNoQ2F5MlhSZGtaNVZlSDVFaU1odlFTTzFpWHpaTlJ1NTBJV2l4YWtSc2xDMHZFeksvZDkraTh2YUpKYk83L25rMXJiQ2lxSDNRY21IWXJlY2VSU0E4QmVkdXE4ZEc4VnMrVi80bWFmNDZwNVdDcEJvOU5ZU2pEcE5pbUZyUWtkWUZwYU41KzF0ZThGOXlJc3ZTNERKRXJNNmtQVWYzVTV3bUswdldpMmw0eWpOK0VsUXNDNm9PSlpFbFVvalJGaWlPelRzOERVcU10M3BGV3Jkem55SlRqYVk2aFRVcEhuNmVqK0ZEK2dMNWYxQ3ZEWlh3Z0FVUzU5UkRQM1RYNk82eHd5elJ5ZEpYellLRzlNSERBVjlzalNMV3Myc2Q5TEJYMzBiS29lSkUxRDczbUY4SjVIOUxITzZnUE9ZV0pqdDVkOTBIN2RveHd1WG0yQ0RUNGpWOURUNzdHbnZvV1U1WS9YbHVYcmNHOUFBSloiLCJtYWMiOiJhN2I4NjhkNWYxZTY4OWUxY2Y2NmMxYzQyYzMxY2NjNDNmMTFkNGFkYzMyOWM5ODkwMDNiOGY1NDczZDY0NTQ3IiwidGFnIjoiIn0%3D; _clsk=1wa64lz%7C1750991019246%7C19%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331995228\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1509901979 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1509901979\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-644249096 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImVJNDhkRk1OQnljNUtuWnl2Z21LQ1E9PSIsInZhbHVlIjoiTmprVW0veE1Gc0xsSzRHV1kyaENKSWg2S0xsVnlaOFRwUUpIT1E5RW5tWE5xNTJrdkV6VnZyRjRFbEdXTXA5NjN4bzBDMFAyMmJkQnVHbFVhanF3MDIwK0pBdEMvWW1FUEZWaHBWZWhYdkM0bDRRUmcrNE4vNHRESXpCaUdCc0ZlM3BTRWt5bUVlSmtrTUc1SXphRnhRYkw2cXVadVF2Z0ZxR28rZGNEZkFlVGhSNkVlVUZkL1BiOC95WkhVZGNGUXkxTkJ6MWVzSkQ4TU9mMi9QTGVyM2VzY3FBbWZsZ2tDKzJ3ZDh4Q2dQMlJQRnBDOElaZ2V6ei9RWU5vVGpKdklZWUsyNFZOVGtNVCsvRklvUnArZGdoQzVtMWhuem5mUjVsZHVuZHlyOHBvd3JiNHFKK2xzTHZLOEg4b0FpL0wwbjJPMUhrVmJMcWQ5U2JiSXZpb3Viek9vYUJ2ZmZpcFdVZ0RnWUlhakxQZit1NmIzdWtZTjQwbStNTnhIMVozNStxRjVGNGZNaUkrODR6ckZ1MTZTWkNnK3RRaU85U0s0Y0M5MkZ5T002KzBPWk52Vm9BWVFBc2pXQ1hTRUsvSlBLbjBJS3dhZVdJWm5RaTlVWXNNa1Iyc1doS1N1dUhLeHpsM2p6Z3FsV2RWeTFUUmFYYXhEQ2tuN011MTlIVnMiLCJtYWMiOiI3OGUyMTI0ZjcxNWFhNGI5YmQ0YWVmYWQ4YWQwMzg5ZjBmYjdmMWMzMmFlZjhkYzE4MjkzYjA4M2NjNzQ0NDdhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilkvc2RwK2UzblR0MVh6amNLQUtWZ2c9PSIsInZhbHVlIjoiWnlibnVnSXo2bEovYmdhQkZ0SExUTFptanI1bCsvT3VPNUJsVENYTEVIKzBNQWwvNDg2c2JLVjVkL1FzaDhZUXcydDUzdWR2Q2JtRlJhOTRESDUvUWovcWNicTFlY1RGMmJ5Q1FHbGZDL1luU2FoZTllNittMXExMFEvck84SlN1dDdzQU5EY3k1NWx3M01nbDhnZEozWWxHd1JEUnJYS3hvT3pxSi9rYUNlbFVRckoyeXNnSW4zRjhFb1A2U2FxSWVyc2U3d1VTRWxhTDBDYVZXTjJMMHFzTEEzZXRkZXB0NTN5b1NBVVFwOHZGUStyQjN5MUhwMlBWQUNRR0JtUHBIU0RsWVQrcDVOVkN3Q3VNeG4rSXdXS2pBOTFrVCtaUnNmbmRrOVprbTRwVjdlMkZ6Mll6MzlPQ0dSM0RHQWxkRnZ0bE1FSmtGS2Y3N1VPWDFnMnBkVDQrQi90cndiWWlFL0V4bVJ4OHpUajI4cWxHNUVJVlYvYTZCSFFoMW9uMk9UM1VrSUpIVTRvNVJpWkkzWmZWL2NDaVBGd2VyMTcwQkJqYloxSDdSdXd6YksyVHVBYVQxK3hmT3lkcERuR0tMS3krZE03cDYvWFlNQUZSMmxKUGl4Um1IazRoREEwK2pIZ2VwVUdBaExoMWxpMlpOMmJpQ3BIMFBYQTI3TlEiLCJtYWMiOiJmMDFiNDY0MjUwNzFmODEzYWU5Nzk0N2RlMTY3NDc5OGFkYmE1MjE3NWQ0NmY1Y2FlZTBhNGZlMWNjYjY2MDVjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImVJNDhkRk1OQnljNUtuWnl2Z21LQ1E9PSIsInZhbHVlIjoiTmprVW0veE1Gc0xsSzRHV1kyaENKSWg2S0xsVnlaOFRwUUpIT1E5RW5tWE5xNTJrdkV6VnZyRjRFbEdXTXA5NjN4bzBDMFAyMmJkQnVHbFVhanF3MDIwK0pBdEMvWW1FUEZWaHBWZWhYdkM0bDRRUmcrNE4vNHRESXpCaUdCc0ZlM3BTRWt5bUVlSmtrTUc1SXphRnhRYkw2cXVadVF2Z0ZxR28rZGNEZkFlVGhSNkVlVUZkL1BiOC95WkhVZGNGUXkxTkJ6MWVzSkQ4TU9mMi9QTGVyM2VzY3FBbWZsZ2tDKzJ3ZDh4Q2dQMlJQRnBDOElaZ2V6ei9RWU5vVGpKdklZWUsyNFZOVGtNVCsvRklvUnArZGdoQzVtMWhuem5mUjVsZHVuZHlyOHBvd3JiNHFKK2xzTHZLOEg4b0FpL0wwbjJPMUhrVmJMcWQ5U2JiSXZpb3Viek9vYUJ2ZmZpcFdVZ0RnWUlhakxQZit1NmIzdWtZTjQwbStNTnhIMVozNStxRjVGNGZNaUkrODR6ckZ1MTZTWkNnK3RRaU85U0s0Y0M5MkZ5T002KzBPWk52Vm9BWVFBc2pXQ1hTRUsvSlBLbjBJS3dhZVdJWm5RaTlVWXNNa1Iyc1doS1N1dUhLeHpsM2p6Z3FsV2RWeTFUUmFYYXhEQ2tuN011MTlIVnMiLCJtYWMiOiI3OGUyMTI0ZjcxNWFhNGI5YmQ0YWVmYWQ4YWQwMzg5ZjBmYjdmMWMzMmFlZjhkYzE4MjkzYjA4M2NjNzQ0NDdhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilkvc2RwK2UzblR0MVh6amNLQUtWZ2c9PSIsInZhbHVlIjoiWnlibnVnSXo2bEovYmdhQkZ0SExUTFptanI1bCsvT3VPNUJsVENYTEVIKzBNQWwvNDg2c2JLVjVkL1FzaDhZUXcydDUzdWR2Q2JtRlJhOTRESDUvUWovcWNicTFlY1RGMmJ5Q1FHbGZDL1luU2FoZTllNittMXExMFEvck84SlN1dDdzQU5EY3k1NWx3M01nbDhnZEozWWxHd1JEUnJYS3hvT3pxSi9rYUNlbFVRckoyeXNnSW4zRjhFb1A2U2FxSWVyc2U3d1VTRWxhTDBDYVZXTjJMMHFzTEEzZXRkZXB0NTN5b1NBVVFwOHZGUStyQjN5MUhwMlBWQUNRR0JtUHBIU0RsWVQrcDVOVkN3Q3VNeG4rSXdXS2pBOTFrVCtaUnNmbmRrOVprbTRwVjdlMkZ6Mll6MzlPQ0dSM0RHQWxkRnZ0bE1FSmtGS2Y3N1VPWDFnMnBkVDQrQi90cndiWWlFL0V4bVJ4OHpUajI4cWxHNUVJVlYvYTZCSFFoMW9uMk9UM1VrSUpIVTRvNVJpWkkzWmZWL2NDaVBGd2VyMTcwQkJqYloxSDdSdXd6YksyVHVBYVQxK3hmT3lkcERuR0tMS3krZE03cDYvWFlNQUZSMmxKUGl4Um1IazRoREEwK2pIZ2VwVUdBaExoMWxpMlpOMmJpQ3BIMFBYQTI3TlEiLCJtYWMiOiJmMDFiNDY0MjUwNzFmODEzYWU5Nzk0N2RlMTY3NDc5OGFkYmE1MjE3NWQ0NmY1Y2FlZTBhNGZlMWNjYjY2MDVjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644249096\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-783027663 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1575;&#1604;&#1585;&#1589;&#1610;&#1583; &#1575;&#1604;&#1605;&#1601;&#1578;&#1608;&#1581; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-783027663\", {\"maxDepth\":0})</script>\n"}}