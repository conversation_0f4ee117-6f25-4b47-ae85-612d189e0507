{"__meta": {"id": "X77f71ce625182e5f1f407c793419a940", "datetime": "2025-06-27 02:12:46", "utime": **********.008745, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.57856, "end": **********.008761, "duration": 0.43020081520080566, "duration_str": "430ms", "measures": [{"label": "Booting", "start": **********.57856, "relative_start": 0, "end": **********.94811, "relative_end": **********.94811, "duration": 0.3695499897003174, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.948118, "relative_start": 0.36955785751342773, "end": **********.008763, "relative_end": 2.1457672119140625e-06, "duration": 0.060645103454589844, "duration_str": "60.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45183640, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.016950000000000003, "accumulated_duration_str": "16.95ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.975208, "duration": 0.01626, "duration_str": "16.26ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.929}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.999814, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.929, "width_percent": 1.77}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.002601, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 97.699, "width_percent": 2.301}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-489100324 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-489100324\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-229479402 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-229479402\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-315326778 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-315326778\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-979833467 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990356916%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjdaejBsOVZzODY0MDFhRk0vaWt6Vmc9PSIsInZhbHVlIjoiSTl1WVRGMlI0dUJnbkh6bDN5Z29ocFRiOTUxaGhFSWNvWm5UVXlkanhMRlRaMUdaZ1pLNSt2K0JnYWxKdVhvL01uSHFuME9rbVhGRktXU0tIWVplQ3ZaSkRsM3ZYT25aSENJOWorWS9iclNyVkdJcERRNU96QTMzc0oxU0QxK252YUFyWkNIRGhaaVFWZW1xTGNHcWV6YjVlV1JnZWFyWlV5VEFhN2NpY2xzd05kNlk1VWtrYVhwYUxoYWd3UEd3V1o0Tm1vWmdnUDZHb2dIR0xFNzJTS0U3bzFYV3prR3dCVEJpaitFVkRkSDV0MnNDeW5DQlZiQ3dkWlUra1pmM0NkY3FqN1EweXgyTFU4QkFPN0Ruc1R4VFFxNjU3bS9tdHZVazR5OEIxYVl1TVdBQUsvamtyZFB2Qk5aMXhhRlNzQXZ6VnZwZ2U1Mm0wODM4L2MvdlFGcmFVVzJJTUYzV1JWNnhBSjFuaWRYVGJJUWNtZTBhOHVsTUt1bFdGWE1LWUlJbHFab3pHRVJFRnlRWjhRUjl0SHVHZGZ2MkRtK3JkUDJ5dXM2MEpiSEVhUnVqQ0RRQjR0UjNFQ2EyTWtmT0Uxc0tSTVB0eTVYbjNiSmdMQU10UGp6ZjFGa2VaYktwVTZuWlBTSGFlZkhrSk5WWEtIRnZqOWV3eExzNitvZjAiLCJtYWMiOiJiNWYxOGRhNDEyOWMxMTMxMzdkMDJkZTU0NzAyZThmZDU5M2RkZDg4OTAzN2RjNDBkNDNiYTI0ODE1ZjZhMWRjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlhHRi9FZmRBZlJzc3Y5K000UkR6b2c9PSIsInZhbHVlIjoidHlvaWgwVEhQR2lMbjR0YXJ4Q2VFaXQ1NkdUaEJxcS9kZm1LZnNBeEhYeDVjNGg1VFVuNitRYng3MDFQQ2F4bzRLbUx3RFZhckZLemJ4T1V4S3I5YVJMVENzazRQditNSVYzNmdvejZwT0lVK3FpdHBuM3k3eTJWRHVEM1ZpTzdEdnNOcFNVSnZ4ZEtVcU9zWjh5OXlsdXZodVNIRFNzWkttYUR2cGpaRXlGeGowRTc3cXdiTS9YMHU0TmFjc1h3WDNjSjFxak1XcTdFOU5mb1N3bFhraWZIT1l2Qk9HbFBYdjBReXB1RWVYSlQxZzhCNnJHV3FnanBGcXhxazMrNnpycCs0cGUzcVM2L1lRcnFqUDRQWkZuN0RhcE9SMzZkNjl5TTZFMnZZbHZGSDBtWHRYZUQwU2FLYUFLd2JmSU1KNW0xUVhPT2JERGRES0x2bVljbjZsSVJ1VTJMVHBTVzdVMGxpeGw4dUN6MkllLzhnUUtZcEFoNmxlZWRXdVNUK3hLcGZ3bXBXWndCRnpiakhmZEVXOWUxTlFWV0xqWTV1K2NwNG5QRHpTT1Y4ZVkzWmhlWUEvbkVJMjNIMjVndGRnMXM4b3NiNEcydllsTWVQaFRuT2JtdkFHNkNGOWEwWHg0STV2cyt6ZitEVDMrbWE4a1BkUFAwVElURE40NEUiLCJtYWMiOiJhMWU1YTVmZGQ5YWE2MWY2MTdmZjcwODI5ODAwN2I3NzRjMmM4NmNhY2E1MjliOGI0ZTEzMzIxMGJjZDhhYjE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-979833467\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklVQTEwY0VtOTJuT29QS1NiTW5wSUE9PSIsInZhbHVlIjoiMDNjVGIzb3NmU2FhaWZVVHVIYVBvSkNQR2NFK1FQR2NyT1VtTTFsejNPZFBRZEk3Z1NPTGM1aC9aeEFCRjNGSnRYWHpBRHQxSHJtZkI1SGdJZ1NQRlNCUFVPVHpJNWs3VFFLU0dJeWI1NHVIenFneVVhV3JhbFNBQTZsd0pRaVVtOWFIeFBpTCsxVTR6R25xdTBvdUpaVUFJR3ovdktmekVJbmxuY1I4SkNYNW9HdjQ5M0VSdWlDVnp1NVFZWFl5M1RiVGFKTW0zVFM4VTVOZk9yVWJUT01HeFU3cXNvdGFIT1BSdjF1RDBnaTMvZElHampOMEJ6VUZROVZUTGt0ZkgwRHUzK2JiY3hDU1JCOHN1a0MrMlFPM3AwUHNBeE9Jdll4azl2K21jcG5LREJnYUVLUzN6d2FMS3RaVlRIMHBUZ2lPNWFySStTWm9XZ09yd1g3S3JjeHl0R3ovUVFOOUJyTk5DVk02NTVGZ3V5R2creEhnOWRSWFJDUkNTRDFpeUtqNlRsUnNlUTVkYkxaUEc1MHpHTGtCd3ROWnNCOXhEZWNxZGYrYW43U01lM0p4bXJpRXdncUlydEprQ25hMEFyY3JUTVhVMXBIRStxS25hSkhndjMvMHlENXkxV3BPY1RISFlxQ01YbmhWdks4VTFoN2U1WFVoWmlUOXBONlciLCJtYWMiOiJiMWY0NzkzODYzYmQwNWJkYjQ0N2ViNjY2N2E2NjdkYWI0ZjE1MWM4NzExNmU4ZDY1MzgzZjgxM2NlZWM0MzcyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImIxaHBSaGhVVTFHbDFvSHZHZy9yakE9PSIsInZhbHVlIjoiTGpBdm1janlMa0ppTk1VQ3VMSEtaSjRldDk2TGpKTjJ4NDY5NSsrQ3VDbDZSeDQ1aGNBQkI1MkhSaHNIK05kZDI5WlY3SmtraFExckllODAyZE1TQ1ZrRi80QWRQcXl2bklJUzhJb3dKQ0JqcVAyVjY1S1JySVRHUWhWUXNwdXQvUE5EaXZOT2pGQVZXblJjcEd1b1J4by9rTzFlYndoaFRqYXBmQXVwTzR2RnA0eG9qMCtpWEtTa1lwTnc4RHJaUUtEakJZcWlDcFFqdWJDYVo1QTR1WHM1dlhrd3AyTldjR2c2S1dHbWFpUExqNjNDK3N2a1BpR0hVNjREN3ZTdWN6WlQvTzVqa0xCV0Rnd1IzNldjTzI0QlczdlRjeWZYVVY1KzdHTlhJT3RXdXl3eDBLV09wVU1FTXpkcDVRcmxjTFc5RTRyMzA2TFRHdGhHejJ2UHFtSU9ZUUJYaDZvR0xub1VMLzhIWkl0d29Lb2pnR1RVWVlhcmlaNWhnYzUrYVJDcy9XQWtRK3NJWjVYdnR4SkEvL3hZZk5icWFxaXZJUzZYa0UwQVlZbC85amZhZXUyV0l1RWJiZDEzQ1djMFhlSVUrQ2JSdUg0bWZUQ2ZhdktpVU40WHRHYTBSWGxGVUpTeVQwa0dTa05QSXkzRmRqL2JPYUkrbjdYZnNsSWQiLCJtYWMiOiJhZTkyNmNjYjY3ZGE1NTA3Y2JmNjkxNjg0NzlkNmZmZDg0OTQwY2MwNDI2MzBlMjQ3MmM0Y2ZlYjUxZDY3YzExIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklVQTEwY0VtOTJuT29QS1NiTW5wSUE9PSIsInZhbHVlIjoiMDNjVGIzb3NmU2FhaWZVVHVIYVBvSkNQR2NFK1FQR2NyT1VtTTFsejNPZFBRZEk3Z1NPTGM1aC9aeEFCRjNGSnRYWHpBRHQxSHJtZkI1SGdJZ1NQRlNCUFVPVHpJNWs3VFFLU0dJeWI1NHVIenFneVVhV3JhbFNBQTZsd0pRaVVtOWFIeFBpTCsxVTR6R25xdTBvdUpaVUFJR3ovdktmekVJbmxuY1I4SkNYNW9HdjQ5M0VSdWlDVnp1NVFZWFl5M1RiVGFKTW0zVFM4VTVOZk9yVWJUT01HeFU3cXNvdGFIT1BSdjF1RDBnaTMvZElHampOMEJ6VUZROVZUTGt0ZkgwRHUzK2JiY3hDU1JCOHN1a0MrMlFPM3AwUHNBeE9Jdll4azl2K21jcG5LREJnYUVLUzN6d2FMS3RaVlRIMHBUZ2lPNWFySStTWm9XZ09yd1g3S3JjeHl0R3ovUVFOOUJyTk5DVk02NTVGZ3V5R2creEhnOWRSWFJDUkNTRDFpeUtqNlRsUnNlUTVkYkxaUEc1MHpHTGtCd3ROWnNCOXhEZWNxZGYrYW43U01lM0p4bXJpRXdncUlydEprQ25hMEFyY3JUTVhVMXBIRStxS25hSkhndjMvMHlENXkxV3BPY1RISFlxQ01YbmhWdks4VTFoN2U1WFVoWmlUOXBONlciLCJtYWMiOiJiMWY0NzkzODYzYmQwNWJkYjQ0N2ViNjY2N2E2NjdkYWI0ZjE1MWM4NzExNmU4ZDY1MzgzZjgxM2NlZWM0MzcyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImIxaHBSaGhVVTFHbDFvSHZHZy9yakE9PSIsInZhbHVlIjoiTGpBdm1janlMa0ppTk1VQ3VMSEtaSjRldDk2TGpKTjJ4NDY5NSsrQ3VDbDZSeDQ1aGNBQkI1MkhSaHNIK05kZDI5WlY3SmtraFExckllODAyZE1TQ1ZrRi80QWRQcXl2bklJUzhJb3dKQ0JqcVAyVjY1S1JySVRHUWhWUXNwdXQvUE5EaXZOT2pGQVZXblJjcEd1b1J4by9rTzFlYndoaFRqYXBmQXVwTzR2RnA0eG9qMCtpWEtTa1lwTnc4RHJaUUtEakJZcWlDcFFqdWJDYVo1QTR1WHM1dlhrd3AyTldjR2c2S1dHbWFpUExqNjNDK3N2a1BpR0hVNjREN3ZTdWN6WlQvTzVqa0xCV0Rnd1IzNldjTzI0QlczdlRjeWZYVVY1KzdHTlhJT3RXdXl3eDBLV09wVU1FTXpkcDVRcmxjTFc5RTRyMzA2TFRHdGhHejJ2UHFtSU9ZUUJYaDZvR0xub1VMLzhIWkl0d29Lb2pnR1RVWVlhcmlaNWhnYzUrYVJDcy9XQWtRK3NJWjVYdnR4SkEvL3hZZk5icWFxaXZJUzZYa0UwQVlZbC85amZhZXUyV0l1RWJiZDEzQ1djMFhlSVUrQ2JSdUg0bWZUQ2ZhdktpVU40WHRHYTBSWGxGVUpTeVQwa0dTa05QSXkzRmRqL2JPYUkrbjdYZnNsSWQiLCJtYWMiOiJhZTkyNmNjYjY3ZGE1NTA3Y2JmNjkxNjg0NzlkNmZmZDg0OTQwY2MwNDI2MzBlMjQ3MmM0Y2ZlYjUxZDY3YzExIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-147351940 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-147351940\", {\"maxDepth\":0})</script>\n"}}