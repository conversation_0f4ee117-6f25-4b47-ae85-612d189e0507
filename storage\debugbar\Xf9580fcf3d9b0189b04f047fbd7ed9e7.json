{"__meta": {"id": "Xf9580fcf3d9b0189b04f047fbd7ed9e7", "datetime": "2025-06-27 02:12:18", "utime": **********.607715, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.10234, "end": **********.60773, "duration": 0.505389928817749, "duration_str": "505ms", "measures": [{"label": "Booting", "start": **********.10234, "relative_start": 0, "end": **********.540608, "relative_end": **********.540608, "duration": 0.43826794624328613, "duration_str": "438ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.540617, "relative_start": 0.438277006149292, "end": **********.607731, "relative_end": 1.1920928955078125e-06, "duration": 0.06711411476135254, "duration_str": "67.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45394912, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00364, "accumulated_duration_str": "3.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5846171, "duration": 0.00202, "duration_str": "2.02ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 55.495}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5966809, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 55.495, "width_percent": 21.154}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6011121, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 76.648, "width_percent": 23.352}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-118048793 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-118048793\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2022812792 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2022812792\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1198430681 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1198430681\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-795454262 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990332098%7C1%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNUMUxHQmJhMFAvdlFmck5UNmZFd0E9PSIsInZhbHVlIjoiWThERkJaRkpZc21Ib3hmb1ZBbmJQMnZTOTcwSytYZkc0MERqb3puVGlzWkdYM0hxNnk1SXRQMm93V1YzNWZaU3J6WitJc0FjZHFBYmg1czlLWktBakpJc01MeHhUMTNHYWg4ZkZnSjBINWhBb1BJc25LVk8vS3Y1N1QzVHU1TGx1cWsrYUZibmd6cTluMkJubTI1V3JpOU5oL1VJTTMzNytLQnkxZVZOV3pSMytXNkh6RGRWVElRaDZKbi9EUW9mdFpJd0hwQkZVME5kbU4vcEViNzNhYkJleFdZQlVZT1hkdW5YZ3ZtaWxEb0xsNDlxUWU4ZlBQK0g4WTBoaWFUK2FFZ2c4Wm5ldlJ3VSs2R0d0d2Q4aDlvVzZXNkorMDRPK1pKZXh2OFhKWW1pWm0yUWdjWlN5dmFMLzBZSnZaYXhmVDJQYnZrZUg3bXpFcFEvcm44MlJRUGRSTW42TVcxczlpYWJlUkc0VnQ0YXNXVXlsSGZVU3ZvdXRuRWNUNlEvbEJPcHQ3UEh2WHEvMk9EczQ2bHB3dXc2VEtreGYvUzVwNnUraE5xdzJLS2l4d0EvSEd2eVBOdG5qMGN1VHk2UVB5OUduMlBkMGxNTEN2UmdsSW9hMUJoYkc5V2Z6RkZBT3FxbGt5WWFDNjlLV0lTcGd4eVJxSkF4MmhrbzVqWGwiLCJtYWMiOiIzZmVkM2U0YjgxOTYxYTYwNDBmMDAzMjcwOTY0MmYxOWY5OTEzYmY2YjI2ZTczYzAyZGZkNjg4MjJjZTBjMWE3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJIYjdKSHh1dkdObjJvMkxVQ1diUnc9PSIsInZhbHVlIjoieHBKT2ZwVHZvdVhNRlVSWkJGS2RxaE0xUzZvSEIzdkg5aTVaZmdRQzk5ZkNlV3ZyZEsxR1Q5RndZYWlsa3JqeTBoYTgxeDJ4SWkrd3R2VUZwWW40bTBnZmgrNWRoVWlmUVdTb21uSThwT0xnOTB1MUFrRjdKMHkzeFFZOUVJZS9WS1RJRG55V28xc3krTFRRMXNtNmhvTWZMckVJakpvMmxOMDhrWC90aVp2UDJxYXFxZ2MrZnFPckFLWThVZ0FhRVAvYzl3L1BkbTBHK1BvQUU5cjFCNG1wQjN1WmsxUDZIeDd1YUV2WE8zME5Qa0I3ZEdGRmNzWVlGc0drb21oejB1NEh6K0FlR0Vrd1FPTUE0RWtqbTI3VEpFRU9lTllwZTlLWFFZdk4wNGF3RTlGZElVdHgyejlRVkRGYlJpNlJSbDZuQ3l2QzA2Q2U0STh0RU9pTGloSnVSdVhINXp1Q1J5VUVJcWRIU1dZcHhqZEsxdmF6Ymhob3RPUktjcm13OC94UHFKdHdOQjhxYVRxMW8zekNDUnRyRnMxQjdaZGpnd0U1TkRtYTVnV1NRUUtjaWk4MmdsUHdqeVVzdTk1SGVmMFNMNXM5a0p4RStUWXhwV0QwWGpoK3JsMFhMRjJxbk8vZnR3aDhRWGJaMVRYbnk2SWRudXI3WkxvSEEyeFciLCJtYWMiOiI4NWYxZWFhYTg0ODJlMmZlODdmOTUzYjVjNDg0ODJiNWJkZWQyNTMzZDlkN2U4YjA2OGY1YTU3N2Y4ZmJhOTRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795454262\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1692764690 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1692764690\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-252825082 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjhNVzBqQWxLb1lWanhjYzdIVEM0Mmc9PSIsInZhbHVlIjoiRU9ZQWF0RTVGUU03N2hRa0c4MDMzRS8rTnBDZXJCUHFXVkVJYkNvalZOWGxiUHdVZFQvWVFyOXNybk8xWDJQb1h0NWNaOWh6UVA4NWRkT0tvdW9OMnpXRmk0VzcvRkZkSTk4YjU2c3VTTS9yTjR1MlQycjIycjhwU0VzaGQzZGJFNXBGeG0xV0xtZEk3bG9EeDN1N1NRRGMyaHd5d1VsTHkvdS9ueTBtL1VCSyttN1IzYTlSWEpBRktXcDY4ejh1Ulg4T0M0ZkR3V0hKMW1Fby9FL3NHTHpKRHQvQnJqK0V4ak5qQk1JKzkreC9PQ0V5RmFPNEFnNzBHRUZxWC92YkhFRE05NHQ0T2E0ZnFLNytQSlViZ0pjcURmY3dFdXN0SzQyN1BCNkdMT3JEclZYRkg2a3NVWHNnZVZya3haeVQ4cm9IQ2loZXRXOVhrMHFDTGU1bEZzVFdTcFZvNzFNME5ZeFZFaW8wdEdvZHVrZm1JdklRWnJQTkZJUDJBalNiR1RmaGdocDlBU3pDVW0xUmI1WVFBWHpRQUFrcHdhUEpKc0V4QnQ1UHVkTEhpS1k4WVZKTWhLeHE1V3UxUzM1Y2d2dFhmdFUzeFFHSFpqQXBXakVaK2MxTXpwSStoOFdHZkc5b2oxTWc0TmN5YldpOEhoSHM0Q0hoa2x6SEIxaWgiLCJtYWMiOiIxNzNkOTFiN2QxM2YyODk5MjZmOWYwYzY5NzZhOWE5Y2E1MTFmNjU4MzU3Yjc0MTM1ZTAxMzZiYzE0NjdkNTI4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImFYV2RmUTFOQU9MZDFONnZDdnd3YXc9PSIsInZhbHVlIjoiZ3I3N2dpMGZiQVA3ZWJIc2RTNTh1TEx4WkxKOE42WU9GVitDZjJMcStUUmVtYkFTVGRDeURUbzlEZ1YzOUp2MERmTEFtaWhDQ3lxRy95cXc0R3VkeWtNUHZOSzhkRW1yK3g0T0FIMXZyVk5IdE14T2ExaFF3WlB4OXpBUTBxTExSRC9tN3k3QVM0YkVhZ1J6Z1JydlRwOGFtQnpFWVlnSnlMcDlDTWk4NTlCOElVeUNtM2RnODdaSnRnMWF5MVp1d2U4bm1LOUpEMHlLM0pBUlJNK2NZalBNbXB0MW9rekViazBnVzhZUkN3VGs0M01tU0xTSFR2dGFuYVVHVnpmMi9SZXRGV040dS92V05DbEZ4ZVg3REpqOXF2OHdsNjVjZmJVUzVXcTVsaVFLclRlWThLOFAzWnVmVVBqYVJzb2c0ZytaMGZrYnUycG94Q0NNUGkzdTBqVGpmWVZSd1YwajJOS0M5VWd2cjY4Nkptd1lyU0ZBYUZpU3pGUlRncHE2c3RseHR1Y29FdXJaRXhwYzAzYlRVQUxOVEVBNEc4U0o0OUg5QkRMaDJKTHQrV3c4U2g1aXNqcXlOZ2MrU1ByUm1NRlRodVRPbzBpZDV4WHNZWkFDN0JPMERUZmN3cFUyZnVUMHB3NmNOM2dTSHhPWDlRR1AwVVRmcWhCdm9CYkIiLCJtYWMiOiIyMGNkMTNmZWJmYzNlMTUxZDFhNDUwM2E5NzkyZmYzZjkwOTI4MWJlOWI4MDc2NWE4ZDY4MWRlNDZiYjQ5NmVjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjhNVzBqQWxLb1lWanhjYzdIVEM0Mmc9PSIsInZhbHVlIjoiRU9ZQWF0RTVGUU03N2hRa0c4MDMzRS8rTnBDZXJCUHFXVkVJYkNvalZOWGxiUHdVZFQvWVFyOXNybk8xWDJQb1h0NWNaOWh6UVA4NWRkT0tvdW9OMnpXRmk0VzcvRkZkSTk4YjU2c3VTTS9yTjR1MlQycjIycjhwU0VzaGQzZGJFNXBGeG0xV0xtZEk3bG9EeDN1N1NRRGMyaHd5d1VsTHkvdS9ueTBtL1VCSyttN1IzYTlSWEpBRktXcDY4ejh1Ulg4T0M0ZkR3V0hKMW1Fby9FL3NHTHpKRHQvQnJqK0V4ak5qQk1JKzkreC9PQ0V5RmFPNEFnNzBHRUZxWC92YkhFRE05NHQ0T2E0ZnFLNytQSlViZ0pjcURmY3dFdXN0SzQyN1BCNkdMT3JEclZYRkg2a3NVWHNnZVZya3haeVQ4cm9IQ2loZXRXOVhrMHFDTGU1bEZzVFdTcFZvNzFNME5ZeFZFaW8wdEdvZHVrZm1JdklRWnJQTkZJUDJBalNiR1RmaGdocDlBU3pDVW0xUmI1WVFBWHpRQUFrcHdhUEpKc0V4QnQ1UHVkTEhpS1k4WVZKTWhLeHE1V3UxUzM1Y2d2dFhmdFUzeFFHSFpqQXBXakVaK2MxTXpwSStoOFdHZkc5b2oxTWc0TmN5YldpOEhoSHM0Q0hoa2x6SEIxaWgiLCJtYWMiOiIxNzNkOTFiN2QxM2YyODk5MjZmOWYwYzY5NzZhOWE5Y2E1MTFmNjU4MzU3Yjc0MTM1ZTAxMzZiYzE0NjdkNTI4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImFYV2RmUTFOQU9MZDFONnZDdnd3YXc9PSIsInZhbHVlIjoiZ3I3N2dpMGZiQVA3ZWJIc2RTNTh1TEx4WkxKOE42WU9GVitDZjJMcStUUmVtYkFTVGRDeURUbzlEZ1YzOUp2MERmTEFtaWhDQ3lxRy95cXc0R3VkeWtNUHZOSzhkRW1yK3g0T0FIMXZyVk5IdE14T2ExaFF3WlB4OXpBUTBxTExSRC9tN3k3QVM0YkVhZ1J6Z1JydlRwOGFtQnpFWVlnSnlMcDlDTWk4NTlCOElVeUNtM2RnODdaSnRnMWF5MVp1d2U4bm1LOUpEMHlLM0pBUlJNK2NZalBNbXB0MW9rekViazBnVzhZUkN3VGs0M01tU0xTSFR2dGFuYVVHVnpmMi9SZXRGV040dS92V05DbEZ4ZVg3REpqOXF2OHdsNjVjZmJVUzVXcTVsaVFLclRlWThLOFAzWnVmVVBqYVJzb2c0ZytaMGZrYnUycG94Q0NNUGkzdTBqVGpmWVZSd1YwajJOS0M5VWd2cjY4Nkptd1lyU0ZBYUZpU3pGUlRncHE2c3RseHR1Y29FdXJaRXhwYzAzYlRVQUxOVEVBNEc4U0o0OUg5QkRMaDJKTHQrV3c4U2g1aXNqcXlOZ2MrU1ByUm1NRlRodVRPbzBpZDV4WHNZWkFDN0JPMERUZmN3cFUyZnVUMHB3NmNOM2dTSHhPWDlRR1AwVVRmcWhCdm9CYkIiLCJtYWMiOiIyMGNkMTNmZWJmYzNlMTUxZDFhNDUwM2E5NzkyZmYzZjkwOTI4MWJlOWI4MDc2NWE4ZDY4MWRlNDZiYjQ5NmVjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-252825082\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1184113060 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1184113060\", {\"maxDepth\":0})</script>\n"}}