{"__meta": {"id": "X8a493ab077caf2aee06c2012d9757d3c", "datetime": "2025-06-27 02:27:48", "utime": **********.3601, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991267.94825, "end": **********.360118, "duration": 0.4118678569793701, "duration_str": "412ms", "measures": [{"label": "Booting", "start": 1750991267.94825, "relative_start": 0, "end": **********.312108, "relative_end": **********.312108, "duration": 0.3638579845428467, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.312116, "relative_start": 0.36386585235595703, "end": **********.36012, "relative_end": 2.1457672119140625e-06, "duration": 0.048004150390625, "duration_str": "48ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45394752, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0024000000000000002, "accumulated_duration_str": "2.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.342887, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.417}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.352148, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.417, "width_percent": 19.167}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.354636, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 84.583, "width_percent": 15.417}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-466877764 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-466877764\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1565744477 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1565744477\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1817962721 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817962721\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1388710377 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991263527%7C37%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFtclcrRUZZMk9nWlZ1UGFjb0pnd2c9PSIsInZhbHVlIjoieW4xZ1F0cGJnOE1SUldxTlhPT3FGZEQ1YkZrYmU2cjNSYVNjYUMxY2Eyc0U3T0xFeDdzT08yaHVwSTc3aytzZGIzN3Qxd1dIci9yaGFWSXduYjdMaHR3RFU0QWZVNUhHZnpJME4xSTA3a1Bra1ZncFJLSlRtZngybmorMFdXbFFUVG5zUUxJcTYzYmNFUU9YRDNqS3NOQWVrQ1dBRlozcm9ZcTh4Wnh6ODQwSy9GMkNJbHNWaWhPV1JVRGgxR1BUNlc4ZExIQ0JNMm5iSkRIaDZ2dDJ1bHc5aXZYQjBTT2lzd2xBT205VEkrUkFzTGo0S3E4NTQ4YUlEMlhNd1ZIWHlJdGg3NkNNeVJtWGJWMUt4bXRmQ1VHclFMWk53WkZoTFdLSFRuckFmMG9Xc3UxZ0RLWHZMSFZzR2IzdlJmbFZLMERGWE04WnQyenNZTVB3R0YyMjdKck0vb0NUZWZnQTZadm9CTzVVL0dQSlk5bnArczVVT3g1T1FZZFE1NFVkYkZ5cVgzTEd6S2ZleEZBNFI5OFNiWk00eGpnUHo2TDFCZm1kTTg4d2UxQXM1a3RiUHBXRWE5L3MyUWI1NVRKOGFud1czOXc3RDY4RTgrQ1BhK2ZvclI4Uk5aOU8xRFdnRGZoZmU0MVduWDVGL1U1emZFL0NYeDUxaHN0Wk5xVEkiLCJtYWMiOiI2MDhjMDU1YzZlNWU1MzVkY2E1OTZkZjIzNDU5YjI4ZjI4OTMwZmQ3MGJkNDhjZjU1NDhjNDRmOTJiNWZjOTI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjB2eXFRUkdZVXgva09ySGdnbnl0MkE9PSIsInZhbHVlIjoiUFBXbHlIMnlreTZJc3gzckY4b0ljQ081UmlsZDg0anlvVHI1MDFNYTh4Sjg1TmxCZEZkN241UUk5Q3lnZEtaWE4xMWNhQnpPMmRoRURvWVYyeFU2bnErN0t0ejBuZUFqdHV4OFh5SXRWOHoyNkovSGNjVWVQYktEdkdodUJFSG1vTDREdkdyRmNqNEJVN1JPcWVMTlBseWhudmc2N2NnZkFTQXVUc0hsdmFOZWdMNll0V2hyUjNrM3dibUtJWThtUEh0N2JtemFWSjdlaVNmbDQ2NE0wWWlULzJKTytPRW84cHIvSUlQYjBEYllGRnRmRnlURHNLQm1YeUFtVis4MGM4R3hveFEzZ3JhanVNNDdBUUhadkRRa1BjUXh6aW1EMzltUW9nUlN0R3hVdTlLek4wYkd4bmNTL3hkOEc3ZDR3ZEZ4eTlMcWcweHE2eVcyYmgrU0NmTG5YY0d4MVlxUjJvbFZVRjJpTFNCbVUzOVJyT2FHMFdJTVp3NjdwU3JZNjZsQWpLUmtFQmVVZU14OThXWkpEd1NqMnN6b1RPZFhad2wrcHkxbEFtZ2NxK2RONUNDSEVmZktQMVB4eW1xVXVCa1ltZnBnZGVpWllCSTg1dzVJeU9BN244MGdJSXZLOXk1c3JpR2FtMFhyOWRPZ0J6eEhuWkRaTVZWQU1kNG4iLCJtYWMiOiI0ZWQxNjg1NzRlN2U5OGY0MjY5YzQzYjcxZTU5ZTBlYmM3ZDkzNmJhMDQ2NGJjOTg1OGNkNDkxNTBlYjc1OGZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1388710377\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1525040483 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdzNU1EY3Q1REpNZE52M3VSRjlVQ2c9PSIsInZhbHVlIjoiNkpXbnc4Sm5ROWNJOWNabStUQkF6ZG9iTGVyTVp6Z3BXS09zVUtIdjJGaWlQaHpMRk0zVlZNUFRKWE81Mm94LzdIcnozRDc1ZEtjbWNlZzRtWlVVTndUc3BEZTMxYWk3S01ZWDRuMGhPY3FHVVRWMGp1L1NZTFhJc3AvNU9tMnp6ekZIdldWR3o1eW1kL1QvMVhIcUJQVlVxL1JyU3hsYlFpTEVvUCtobjgxR3l4N0VySGEyUUhHUWl2T0ZLZkVLQURJdS9rajQzeVk2dWhnaXM4NkY3dWhKb0RObUJHQWlIc2xIQ3c1MEpaaytOSk45Z3UrSFdVelBrNkh5NTBNdFVvOGc3cUVEWmNCSk5yaUhCNEYzOXU4enZEaFMrbHUvZHdmUldmcGlIZlFIK25pSlJ6WUVwN3BYTEl0RUtobHYwa3JndWdTNUlYYnBjMWNJTzRKek5vZ0c5MlZkVEo5YVJpRjd0NCsxUnAzR09EbXZBSXNYZUQ4QjkzUkV3MnZjYkhMaVlMNkFIb1VDK1lxblV1RytGcENzY1ArVyt6YlFJLy91Qk14TUkyRUxpZzM2MTl1NHhIc0tNYXJxdFpxSGVsVXFHYnlNbWp1aXdHN056alo3WDdWZjY2OGphbkFLZTRqcW5hVHBVN2U5QXErb01Db1ZYbzdIRzZOejZSejYiLCJtYWMiOiI4MzVkMTE2YWVkZjM5NGEyNGYyMGQ3NzY0YWZmMmI1ZDY2MzAwZDIzYTlkMWYyZjBmYjRiYmY0ZDFiMTI1MjM1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im51RnMvU2llZzROSktZYXR6LzV6cXc9PSIsInZhbHVlIjoiRVkwODJHMVhjYVFhZDhiOHgveTdDQXhsUDluMDlkblNQblVXbVo2NHMzSC9RUThFSVNLVGpJRUhTVWg1dmc0MFBJb1Y4YnpnS2REMi9tRXZGR0ZzRkFoOFhWQlZmMUpmSHd6Wld3UW5tQTBPdk0rUW40QWtscGlrUHUyR3B6VDRSc3FjcHhTQXhMZ09LdDY5dWF1K2x5YTcvYlZobTBPSTduWEFOaGxTTmR5clBHM2NVU0pyeVFObGtUZFAySXk5ZW5aVWNkZC81UHRXaUt6V29vaXRPbkR0emIrSkZiRXNXRGFpYVhnVDI1UjZORFdqdkpoWXNEQjNxT0huY2NLeUVVT3c2eVA1REs0NUthbWJhSTVlSHlyZmY2aVpDdVlyVWtMNEJqLzVuZ2c1VUdlallUemZJdmtEbjFlR1pBdExpc0luSDAzN3ZJN21GR3RTNHc3SzBFc1NoUEJyK2Y3TVhuTHpEVzdtYnRiRmlXYkIxSWhMVXJ1bndSNGpOdVJxN3R2TWdwcWNMc2JwTVo2ZXJxVnNEVTZWSnFpNmtDalRiSUcyeW1ENUtRV0E0Y2RMSjN1L2Q2SXNFKzAwci90N0ZFSWFVWGhLOE52czlVTkZBblRUby9iVzJseWlITUtOcWN0REVVYnlhVG5CcXU0V3FqK1FGRXhDaDlzbHZJTXgiLCJtYWMiOiJmMTFmOGNkYTc2MDljZDE5NjQxODFhMDQ5Njc2NDhiN2U1MzliMDQ2NjQ2MTgwZjcxNzk2ZmJjMmM1YWE2ODQzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdzNU1EY3Q1REpNZE52M3VSRjlVQ2c9PSIsInZhbHVlIjoiNkpXbnc4Sm5ROWNJOWNabStUQkF6ZG9iTGVyTVp6Z3BXS09zVUtIdjJGaWlQaHpMRk0zVlZNUFRKWE81Mm94LzdIcnozRDc1ZEtjbWNlZzRtWlVVTndUc3BEZTMxYWk3S01ZWDRuMGhPY3FHVVRWMGp1L1NZTFhJc3AvNU9tMnp6ekZIdldWR3o1eW1kL1QvMVhIcUJQVlVxL1JyU3hsYlFpTEVvUCtobjgxR3l4N0VySGEyUUhHUWl2T0ZLZkVLQURJdS9rajQzeVk2dWhnaXM4NkY3dWhKb0RObUJHQWlIc2xIQ3c1MEpaaytOSk45Z3UrSFdVelBrNkh5NTBNdFVvOGc3cUVEWmNCSk5yaUhCNEYzOXU4enZEaFMrbHUvZHdmUldmcGlIZlFIK25pSlJ6WUVwN3BYTEl0RUtobHYwa3JndWdTNUlYYnBjMWNJTzRKek5vZ0c5MlZkVEo5YVJpRjd0NCsxUnAzR09EbXZBSXNYZUQ4QjkzUkV3MnZjYkhMaVlMNkFIb1VDK1lxblV1RytGcENzY1ArVyt6YlFJLy91Qk14TUkyRUxpZzM2MTl1NHhIc0tNYXJxdFpxSGVsVXFHYnlNbWp1aXdHN056alo3WDdWZjY2OGphbkFLZTRqcW5hVHBVN2U5QXErb01Db1ZYbzdIRzZOejZSejYiLCJtYWMiOiI4MzVkMTE2YWVkZjM5NGEyNGYyMGQ3NzY0YWZmMmI1ZDY2MzAwZDIzYTlkMWYyZjBmYjRiYmY0ZDFiMTI1MjM1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im51RnMvU2llZzROSktZYXR6LzV6cXc9PSIsInZhbHVlIjoiRVkwODJHMVhjYVFhZDhiOHgveTdDQXhsUDluMDlkblNQblVXbVo2NHMzSC9RUThFSVNLVGpJRUhTVWg1dmc0MFBJb1Y4YnpnS2REMi9tRXZGR0ZzRkFoOFhWQlZmMUpmSHd6Wld3UW5tQTBPdk0rUW40QWtscGlrUHUyR3B6VDRSc3FjcHhTQXhMZ09LdDY5dWF1K2x5YTcvYlZobTBPSTduWEFOaGxTTmR5clBHM2NVU0pyeVFObGtUZFAySXk5ZW5aVWNkZC81UHRXaUt6V29vaXRPbkR0emIrSkZiRXNXRGFpYVhnVDI1UjZORFdqdkpoWXNEQjNxT0huY2NLeUVVT3c2eVA1REs0NUthbWJhSTVlSHlyZmY2aVpDdVlyVWtMNEJqLzVuZ2c1VUdlallUemZJdmtEbjFlR1pBdExpc0luSDAzN3ZJN21GR3RTNHc3SzBFc1NoUEJyK2Y3TVhuTHpEVzdtYnRiRmlXYkIxSWhMVXJ1bndSNGpOdVJxN3R2TWdwcWNMc2JwTVo2ZXJxVnNEVTZWSnFpNmtDalRiSUcyeW1ENUtRV0E0Y2RMSjN1L2Q2SXNFKzAwci90N0ZFSWFVWGhLOE52czlVTkZBblRUby9iVzJseWlITUtOcWN0REVVYnlhVG5CcXU0V3FqK1FGRXhDaDlzbHZJTXgiLCJtYWMiOiJmMTFmOGNkYTc2MDljZDE5NjQxODFhMDQ5Njc2NDhiN2U1MzliMDQ2NjQ2MTgwZjcxNzk2ZmJjMmM1YWE2ODQzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1525040483\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-417997626 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-417997626\", {\"maxDepth\":0})</script>\n"}}