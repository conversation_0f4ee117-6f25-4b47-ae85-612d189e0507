{"__meta": {"id": "X0777a9624288774f1763133f1caee6a4", "datetime": "2025-06-27 00:46:53", "utime": **********.049692, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750985212.591256, "end": **********.049707, "duration": 0.4584510326385498, "duration_str": "458ms", "measures": [{"label": "Booting", "start": 1750985212.591256, "relative_start": 0, "end": 1750985212.99682, "relative_end": 1750985212.99682, "duration": 0.4055640697479248, "duration_str": "406ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750985212.996829, "relative_start": 0.40557312965393066, "end": **********.049709, "relative_end": 2.1457672119140625e-06, "duration": 0.052880048751831055, "duration_str": "52.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026816, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00308, "accumulated_duration_str": "3.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0257032, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.909}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.036384, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.909, "width_percent": 15.909}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.042278, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.818, "width_percent": 18.182}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1538721070 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1538721070\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-969610808 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-969610808\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-673099058 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-673099058\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1747604672 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/bill/create?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750985210604%7C67%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhGVUowdGlYczEyY0tIYzdxSmxPdlE9PSIsInZhbHVlIjoiYnk0TVltSHM3RkhBUlBWNnJvczhyQjdEOW9nNzZQM0lUMWVJNGluK25GWGsxZk5pcHhremVhSklyUzh4Nk0zeGdOV0ZaNUx0Z2RWeU42dTF2NmFPaUk1OUN0a1BvelhOZ0krVWd3bG9ocUF1dGRrcGFxd1hmNjhrWWU1KzJlTkl0TTJFdndLbGJDc0pFc1BDVTdtZkJZbk9NNFZVbkVMTGtjRjBaeUxGNjV2a004UjJibDE0UWVKZ0JTY3BCTmhwVHYrNmdqSWZQczlFdHhKK1pwc1RXMXdzQzN3TDFDTERRdHVEQ2VPS1JJd095QkJBN0tMWWZjdVhPZzhBaFIxU0ZlVXFPSjFrRVZDYjgwcC9STlNkSUF3WEJBbS9PWHVSbExUaE5lNUlkWGRZODJxWWFEc1dnZXZMRVE5OXJrOWJ5a0gydFhqQWVyRW1XRDdrV0ovYUsrWmdXU1JLNmhvN1JHTHk1NU1YdlQxMklkcGUxNDNPTGNSRzhRNmp3Z1NraHViU0xlcG5CZE55TzUySitCQXl0K0Q2MVRzRTBPaFJpWTZjamxnZWFmOFNyOEVxbm4xcjlYeDMvR0t2bjZ2RHZFLy90TGJwNTlVTzRGTUg5OXY1czlYR1Z1L2Iyb1AzMExQRWpnaVlWMzBvWEdPM3NBWjZ2ajVQVExEN3djWXEiLCJtYWMiOiI4ZmIyOTQ5NzQ2Zjg1OTI3NzUxZmI4ZjhkNjQxMmYxM2QzMTg3YTNhZmE1YTgzMmRlMzI2M2NmZGVkZGM5MGQ3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjlZTktUSnMwbXVRbGJwektFSG5JdEE9PSIsInZhbHVlIjoiSWQwR3d0SWROaE5BZVZ3bWxabTJiVTY1WmNoRXR5TzZpV2xibk44QTBnZjdsdCsxc1NVMlZhS21QdExJOENYVVpSOVdwaU1KWWptZEUySGl3ZDdKenQ4QjVLN3lBL3ZvbCtYcVBlUmZSYUdaRWZhOVBnbVJ2UEpGQWY0UWJ4TGRRdXJkT3k4bDNMbW5CUzVhUzVGM0Zlc1hIZThWOTFlQnhuM2ZvaXUwUGgvTmJPTXluamVEdkgrdnFVT0RzUlhhUGhsZFdSWVlqTDJKNHJ1SUpnVlBIbEdPM2NvSXRsUjFRL09yM1JVSzhCc0hKMm9oZkUrTXN5QnhNeGZhVFN1UlJMQ3ZzVm9PR1VpanpTeHdUVXNkdXNQejBKTXRudGFLSHB4ZjZZM3ZoTTNWOWxFRnlOdHEzajQ4WEtxVjhFNTEweEcyL1pabFR3cEs0SUlOK3V2Vmpob0NoeGcxTkpqL3ZEZEk2b21vUXZ0VFFuVkkxWkc5WWNNYWxka3lpWmZ4NjhPdVdGRFluWEF6d0FXWGtFL2dGQlV5cFR1Z1ZmMWh3MkQrR3BjNlM1TnBLNmdOVXp4REtnd3NFaFRNOXVZaVkrNSs0YnFVUTJWZzBobjYxRGpwaWdWRUl3KzgzbDBvUUdBVndTSC9DUkJsUE9iNm03Qk96K29EbEZRM1NsdDEiLCJtYWMiOiI4NDIwMTdmZDMyM2E1M2Q1YmFjNzM1MmVhYTY1NTgwMWFiNTc2MmRlMmQyNTEwNDUyZTQ4MjVjNDdjMzZhMTU2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1747604672\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1440609174 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1440609174\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1730098140 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:46:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlRtWmxFQThJMXZZMWNvRitmbkduV0E9PSIsInZhbHVlIjoiclc0VHZ3NDFjWjZNcXY1SnRnYnNqWWNETmx0T1EvS3c4UktZbjhIVm43OHBOcmxJODV1WURjQ3QwR1B3UCtaSkRnSGRKMk9ERmNBZG5tN2lWT0F0TDBJTGFXQW1nUDJQWjArSXpTM04vQjdjNmF2Y1NtdnhlekpYd281ZU1LaFo0dEFhcWpVZ1pWcDl3NTNrdlNzUyszblNFRGhzU1JNamVsTFVSYkU5a1ZHMkg1N3V0a1JPWlRsTDBLcW0ramxCbkdoM2poMEpqSUFGbnJRTmJuYS9FM3d1YTBlMyt3dzJWOG9jWVNmbS9sbko2cUY2V2tabjRpaGZyWGVPL2hIYk4rUVhOcitpSlhQcXRDektBcklXUklnckdGWmEyWGVuYkRkUnhKMDY4SEZYVjVTeWtSUlRicjExVGljYVp2K1JiKzhZZlB6cVpwbXVjbWwrSGtiMys3N1JKMi94TndXbS8yNkRrMWc4NFh6UEpyZnBWaFY0bkZkMUpYVktCQjZsejN2Z2lObmtVUWZlbXh4cTRMTjRINmh3WnRFNEVaL01oaTJldVFmUS8wTWRTUHRmVVZGc0NCd1ViZjV1ejVnREtnTCtySUJpeDkvVlNVVXkyaGxZcUpuWDZ1SXVSTkZCZTRUVFNRL3dlaG1HZThvM0hINUF3bmNWQUNSTzRZZmoiLCJtYWMiOiJlZjUxOTc2NjhjOTljMjdlODk0ODFkY2M3NDA4MzQ3NDgxNjVjNjQ1MjI2YzZhMjZkZjM4MTk3MDFhNzgzYmI2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:46:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlQYTVFQ1VvNzJPMWtORGtIN1RYNHc9PSIsInZhbHVlIjoiK0RqYUlaOXVDRmczdUcvbjlKZEF4aTVVY1VxZ3p6V09zSlJwb3RzVUN1a3FROWVsczRPVG93U3F3S200RUJYNlZwN05TbHFaM1YxbjdUU25URzNXQ1hVemhsY21UNlV5QmdtQzFuSi9vY1NyK1NzcVl5aURPZ0VWYjNtTTBZR2xLN1YrbnhnOUtpOXJrblNxWHVHNWdMM21YTHZwMTFlU2VpMWNvL3NnMGp4cEZkeVByVm1pTGd6V1lqTWJhbDgwb0hVVlR2YWNxVDJCNTAxSHFGVTNTV2lRazNMZERReE1vaURQSWZySXBPaDc2YTB0SXlFbGphRUVpMFYrSXFlTDlTT3UyWEtRVEwvMWgvUUNTS3dSaWUrT2VHeEFpdE1RY0g1cnJZN1I2dlVFMEhHazZIclFmeGswb2hsYVpkUWkyUFRyVFJpbHdmdkFQUVZ3dk1sYnRzTzNENFRYYStVRFRSWnYzTXN3bVEyZlk1Si9YbXBsS3pGcWJOUkhFOW9TNC94Q0tBKzArZUQzWGdVVXVSYTVieFZCUzRtYlZzcUhXeXdLaVNmUmFlWitUbGZLN0JzV1pJcnpoRDhQUUtkdUJCc3RPOVB0SDAzNnBTaXdJdVVJdXBRQ0s1QmZablhXeDFvdmpzK2Y0a0FNUVo1Z3JDNlZPdVVWQ2NmelB3Z2UiLCJtYWMiOiI5YzFhNzlmNDY5ZTE5MWRhZGJkNTViOWYxNmUzMzBiMGMxZjZhM2JhM2VjMGNkYTViZGVhMzlhNmNiODg2NWI5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:46:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlRtWmxFQThJMXZZMWNvRitmbkduV0E9PSIsInZhbHVlIjoiclc0VHZ3NDFjWjZNcXY1SnRnYnNqWWNETmx0T1EvS3c4UktZbjhIVm43OHBOcmxJODV1WURjQ3QwR1B3UCtaSkRnSGRKMk9ERmNBZG5tN2lWT0F0TDBJTGFXQW1nUDJQWjArSXpTM04vQjdjNmF2Y1NtdnhlekpYd281ZU1LaFo0dEFhcWpVZ1pWcDl3NTNrdlNzUyszblNFRGhzU1JNamVsTFVSYkU5a1ZHMkg1N3V0a1JPWlRsTDBLcW0ramxCbkdoM2poMEpqSUFGbnJRTmJuYS9FM3d1YTBlMyt3dzJWOG9jWVNmbS9sbko2cUY2V2tabjRpaGZyWGVPL2hIYk4rUVhOcitpSlhQcXRDektBcklXUklnckdGWmEyWGVuYkRkUnhKMDY4SEZYVjVTeWtSUlRicjExVGljYVp2K1JiKzhZZlB6cVpwbXVjbWwrSGtiMys3N1JKMi94TndXbS8yNkRrMWc4NFh6UEpyZnBWaFY0bkZkMUpYVktCQjZsejN2Z2lObmtVUWZlbXh4cTRMTjRINmh3WnRFNEVaL01oaTJldVFmUS8wTWRTUHRmVVZGc0NCd1ViZjV1ejVnREtnTCtySUJpeDkvVlNVVXkyaGxZcUpuWDZ1SXVSTkZCZTRUVFNRL3dlaG1HZThvM0hINUF3bmNWQUNSTzRZZmoiLCJtYWMiOiJlZjUxOTc2NjhjOTljMjdlODk0ODFkY2M3NDA4MzQ3NDgxNjVjNjQ1MjI2YzZhMjZkZjM4MTk3MDFhNzgzYmI2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:46:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlQYTVFQ1VvNzJPMWtORGtIN1RYNHc9PSIsInZhbHVlIjoiK0RqYUlaOXVDRmczdUcvbjlKZEF4aTVVY1VxZ3p6V09zSlJwb3RzVUN1a3FROWVsczRPVG93U3F3S200RUJYNlZwN05TbHFaM1YxbjdUU25URzNXQ1hVemhsY21UNlV5QmdtQzFuSi9vY1NyK1NzcVl5aURPZ0VWYjNtTTBZR2xLN1YrbnhnOUtpOXJrblNxWHVHNWdMM21YTHZwMTFlU2VpMWNvL3NnMGp4cEZkeVByVm1pTGd6V1lqTWJhbDgwb0hVVlR2YWNxVDJCNTAxSHFGVTNTV2lRazNMZERReE1vaURQSWZySXBPaDc2YTB0SXlFbGphRUVpMFYrSXFlTDlTT3UyWEtRVEwvMWgvUUNTS3dSaWUrT2VHeEFpdE1RY0g1cnJZN1I2dlVFMEhHazZIclFmeGswb2hsYVpkUWkyUFRyVFJpbHdmdkFQUVZ3dk1sYnRzTzNENFRYYStVRFRSWnYzTXN3bVEyZlk1Si9YbXBsS3pGcWJOUkhFOW9TNC94Q0tBKzArZUQzWGdVVXVSYTVieFZCUzRtYlZzcUhXeXdLaVNmUmFlWitUbGZLN0JzV1pJcnpoRDhQUUtkdUJCc3RPOVB0SDAzNnBTaXdJdVVJdXBRQ0s1QmZablhXeDFvdmpzK2Y0a0FNUVo1Z3JDNlZPdVVWQ2NmelB3Z2UiLCJtYWMiOiI5YzFhNzlmNDY5ZTE5MWRhZGJkNTViOWYxNmUzMzBiMGMxZjZhM2JhM2VjMGNkYTViZGVhMzlhNmNiODg2NWI5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:46:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730098140\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1270706542 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1270706542\", {\"maxDepth\":0})</script>\n"}}