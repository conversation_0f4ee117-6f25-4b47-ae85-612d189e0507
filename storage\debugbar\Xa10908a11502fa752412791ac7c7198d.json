{"__meta": {"id": "Xa10908a11502fa752412791ac7c7198d", "datetime": "2025-06-27 01:03:54", "utime": **********.951844, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.520016, "end": **********.951862, "duration": 0.43184614181518555, "duration_str": "432ms", "measures": [{"label": "Booting", "start": **********.520016, "relative_start": 0, "end": **********.898045, "relative_end": **********.898045, "duration": 0.37802910804748535, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.898056, "relative_start": 0.378040075302124, "end": **********.951864, "relative_end": 1.9073486328125e-06, "duration": 0.053807973861694336, "duration_str": "53.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044552, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0026599999999999996, "accumulated_duration_str": "2.66ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.927855, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.18}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.938117, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.18, "width_percent": 16.541}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.943649, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.722, "width_percent": 11.278}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1394407532 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1394407532\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-782529156 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-782529156\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-956062301 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-956062301\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986202112%7C74%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InF5Y1dRVHFLaEdpQVIxc254VjM3WVE9PSIsInZhbHVlIjoiRXpTQ25mUjBtR1k4WlYzcG9DOWVrNGFIWDdhUHEvUUltNVcyVm1HYTZMejRkdUJNMDFlVi9kMmdjbGFvSGprbkhGSVlPRGR1QkhUTFMveUZ0QSszSUI0NkM5ZTR1VlpHYjk4TXRzdTE5UGtQZnIydStTaDlWOVBjTUhEL0FsQmR2OVEyZDZHSDU2YTNWb0U4dW84akJFa09BRFdXZkEraG1seENRb3lhYlZ0TTNUbkVZYnNHR2FHZ0Q0NU9FZWhDbFFhMzczZGRRcjJsUHNNUmdNRVU5cjU1SlVHa3ZPa2pKcXp5QjFCWVNwNFRNQ2FxemxyV0xLdjBRREdxMmVQdGVXa3paWW5ZV2MvV1ZTY1lxVENua2laOWFiN3MyWlZaUWs0YUNmcHRhUU0zcDViMVhPemRlRzRZL2NocHBxdndBemZrRzlwWGF2dFR3K3pxTXN5VHAxTTBESHkxblNuZnVBb0Zaa0pxcVU5dGhBSkdVdVBrekNER0FpSVRHOG9qRS81L1JtK0VHODhZOENZL0kwZ0l5RFVSSkwwREI1VzNEQUFmbW9uUzltTVdjN0hnNjNRZEQxUERRKzcydHNrS2VVT3ZCMlhEc0FZUi9oL2wrSkFsenhpSTdqMzE4azIxR09Vc0NGQjJDbW00MXBzVEFBMFVsWldyQldnUU9rMy8iLCJtYWMiOiIyNjdjMGI5MjE3MjU5ZGIwZmUwYjE1ZGNlNmNkMDg0MWFkMjI5N2Q3MDVkZWEwYjgyMDgwMjcxNWEwNTUwYjRjIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IllwdmkvYTdjM2RSR1dmOHV2NWptOHc9PSIsInZhbHVlIjoiKzZQeXRBVzRqdWZnOGtuZGdMbG5YN1NZUjF1OTkySmUwV1NpSHVPNWFkZGlEYjB3b1FRbUxFWDNjNjlkNWNHZCtrNDFWdE1qQjJUUjZiSVRFalNhSnB4K0t4SldEK2tLb1U5a1dCMnNoT3hjenFLb3R2TlJ1OFpmd1FpVjl3ZExiN0VPeHZzR2h3WmFwSzZQbWpLSVhwak1Ed0VHbDNRNnRMSlh5U2RCdlUyekhXOHc3dTZncXd5TUc0NjB1NFdPdnNyQ0Z6bVdJUExkMnVWbTVxeUd3NTQyNTdSVXEwNmdGOTZ4MlVsMG1tQTBWUnM5M09LS1dNcEZJQVplTDAxenJRTTRtaEVvWGx1SnV6RWt1blFQajRUVUlOREJ2emZKOWl5c0t4ZHVKQUdKaE96cmxRRDNtei90K3ZUeERZT2M0V3cvYi9mR0hmRi9NTkRaUUtHWU1OTUJGaDQyRkhXWWtVa09IZUlFK1dzNCszZlJ3THpnZHZjMFJQMjRmMjFvZ2hESWhJbnBYeXVUa1Z1MUJRbzA2c2o2Z0tVYVE4dHU0aG9sZUs2c2RHWk0vczJjejROSFdhL3Q0akRCUXplcHpaeFowL3pWamxDRW94bGlIVDNhc1VSUzRTRXFVbG93Z24rd3RiZWtwNkdIUUNDcDNUVjJFelNHOVlUcUpTaVIiLCJtYWMiOiJlYmY4NGNiODZlZTU3ZjRlMmYyMmZhMGZiYzM0ODI4ZWVlODU3MzQ1YjI2NTEyYWNjZWU4MTJjMjcwYTJhNzhmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1221921216 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:03:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVZVnZmUDJlelNxUFh0VHNaNXc4Wnc9PSIsInZhbHVlIjoieWsyN2ZhSzJocC82TXRWeXIzdDRsZmhZNVJNVzhiaGZibVkwZm9RS216czQ4OHRnMjZuRmxBTEVlQzZ0SGFnZWI2U01telVVYWdvME12WGNzeXpxRytSNU5EZHBLWVplb2JVVDBNT3o1ZXl1MVlRMEdqRWNvaFcxNDRYeFlpSEJ5K0hiaXRtallIQ1Y2QmJhanlvR01hUGQ5S0N1ejVDZ2NNNVk3YnJaeUZnYy9QWHdJQk4vWXlNUWxaQ3EyQmJHUnlZNU5nWTRRUjhpZUtIS3VYMDNJb1dGWk1WRmxpZ2hFeHRzVVdVV0E0QktQWVNpZnV5cXhVQWVjMzdMNE1RSGpCMWV2Z1BlTThNcnlNOU9zY3JheWNOQnp4U3NUeHB4MytZRW43ZldxWjlSTmpSRU5sTm1YS01wVWZYTFNPL002cko2OEVhTE5GcnN1ek1FbzFEUjcvU0pjTitBTnl5Y0MvWTRRSGxtM3QyK1EyTGRzd09HcUY1ZHpUK0lCTVpWSU1EUXl1eCtHZklFOEM3SVdHcUg3N1EzbDNQVW5QUmdDMDlWc0pKdjY3cThVSm1WbzRkWVptYklOakN0VEhaclNSdlVlU3VkbU1LeUlhbno1enlWSzg4Z0RsRUl6VHFYc25jMDJVdlJOVktONERiUDdWOEVLY2RWbmhGYzNrWVAiLCJtYWMiOiJjNjYzZTY2OWE1ZDllNmFjZmE5NjNkZDEyZWQ1MmY4YjI5YTJlZmJjMThkMmFiODBlZmQyMDc0NGIyZDVlYzFmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:03:54 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImhKZE12MWRYdXFCY0xJYXVqSjJGU1E9PSIsInZhbHVlIjoiWFE5aU9nR1hiSngrcVJLUmN2N0p4M3I0SmYzTDZMc2hjTHR4ZXE3N0hQNzRwZnhzMy9sZ3czZnQ1MHNyUU5DSHFsYTUzSWF2NVpmdXcybWpQMzZGMzExRFJscVBGajcxa3RTL0ZNMkZ5YnN2SWVqOGg0bERQTmtwMmJLQlJDaTgwVTl2bVhKdDdFcmlGQW9Xd3VRSFVmbVV5bEZXK2tYS2ZOSEhPRDJXRG9wTFBMSlFaT2FwT1hNMTBxeitTc0pXRHdDcFZTQ3QycmxZcjh6T1hFZ1YzZTVZbzFaeWVHODRIT0FxQ2xrUDUyeEg2SlJFUnYyTGprK2xoN3pzc2tvY0RvSUJtZVU2QktRU3JzV2hJcUxPM2NlMlRDYlpOL3dDUTdmRzBzRTJMRFpSUGdLdHY1Y1phM0JRVmFJeEMySkhqcFI0TkZCR2xlNlJxYzE2eFROUlRXVVdTY1J3ZTIwVDJoL2tRMnRpMzJ6SEc2ZUp1dlpweDZDSzdlZ3FmZ3pqODhObzFlOGRkVFNqWlVFVFMva3FjUnh2WFlqZmc1MmdXdmRkc3BhWkRlcW50S0xSakw0OFlEbDdXMzRkay9TZDNpUlhQc1NrVk5ETUFEa3ZjRm5GV2lJeXJhWVd4cjc3N3dnSTNXZWs3eEZFUk9NMmMxeWZld0tRejlCalJzZlgiLCJtYWMiOiI1MDNiN2JhMDg1OGE1YzcwODA4M2E2YWJjZTBiMmU3MTJmNGY5ODNjZTAzNmMxZmEzZWVlOWZhOGJhN2Q2N2I2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:03:54 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVZVnZmUDJlelNxUFh0VHNaNXc4Wnc9PSIsInZhbHVlIjoieWsyN2ZhSzJocC82TXRWeXIzdDRsZmhZNVJNVzhiaGZibVkwZm9RS216czQ4OHRnMjZuRmxBTEVlQzZ0SGFnZWI2U01telVVYWdvME12WGNzeXpxRytSNU5EZHBLWVplb2JVVDBNT3o1ZXl1MVlRMEdqRWNvaFcxNDRYeFlpSEJ5K0hiaXRtallIQ1Y2QmJhanlvR01hUGQ5S0N1ejVDZ2NNNVk3YnJaeUZnYy9QWHdJQk4vWXlNUWxaQ3EyQmJHUnlZNU5nWTRRUjhpZUtIS3VYMDNJb1dGWk1WRmxpZ2hFeHRzVVdVV0E0QktQWVNpZnV5cXhVQWVjMzdMNE1RSGpCMWV2Z1BlTThNcnlNOU9zY3JheWNOQnp4U3NUeHB4MytZRW43ZldxWjlSTmpSRU5sTm1YS01wVWZYTFNPL002cko2OEVhTE5GcnN1ek1FbzFEUjcvU0pjTitBTnl5Y0MvWTRRSGxtM3QyK1EyTGRzd09HcUY1ZHpUK0lCTVpWSU1EUXl1eCtHZklFOEM3SVdHcUg3N1EzbDNQVW5QUmdDMDlWc0pKdjY3cThVSm1WbzRkWVptYklOakN0VEhaclNSdlVlU3VkbU1LeUlhbno1enlWSzg4Z0RsRUl6VHFYc25jMDJVdlJOVktONERiUDdWOEVLY2RWbmhGYzNrWVAiLCJtYWMiOiJjNjYzZTY2OWE1ZDllNmFjZmE5NjNkZDEyZWQ1MmY4YjI5YTJlZmJjMThkMmFiODBlZmQyMDc0NGIyZDVlYzFmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:03:54 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImhKZE12MWRYdXFCY0xJYXVqSjJGU1E9PSIsInZhbHVlIjoiWFE5aU9nR1hiSngrcVJLUmN2N0p4M3I0SmYzTDZMc2hjTHR4ZXE3N0hQNzRwZnhzMy9sZ3czZnQ1MHNyUU5DSHFsYTUzSWF2NVpmdXcybWpQMzZGMzExRFJscVBGajcxa3RTL0ZNMkZ5YnN2SWVqOGg0bERQTmtwMmJLQlJDaTgwVTl2bVhKdDdFcmlGQW9Xd3VRSFVmbVV5bEZXK2tYS2ZOSEhPRDJXRG9wTFBMSlFaT2FwT1hNMTBxeitTc0pXRHdDcFZTQ3QycmxZcjh6T1hFZ1YzZTVZbzFaeWVHODRIT0FxQ2xrUDUyeEg2SlJFUnYyTGprK2xoN3pzc2tvY0RvSUJtZVU2QktRU3JzV2hJcUxPM2NlMlRDYlpOL3dDUTdmRzBzRTJMRFpSUGdLdHY1Y1phM0JRVmFJeEMySkhqcFI0TkZCR2xlNlJxYzE2eFROUlRXVVdTY1J3ZTIwVDJoL2tRMnRpMzJ6SEc2ZUp1dlpweDZDSzdlZ3FmZ3pqODhObzFlOGRkVFNqWlVFVFMva3FjUnh2WFlqZmc1MmdXdmRkc3BhWkRlcW50S0xSakw0OFlEbDdXMzRkay9TZDNpUlhQc1NrVk5ETUFEa3ZjRm5GV2lJeXJhWVd4cjc3N3dnSTNXZWs3eEZFUk9NMmMxeWZld0tRejlCalJzZlgiLCJtYWMiOiI1MDNiN2JhMDg1OGE1YzcwODA4M2E2YWJjZTBiMmU3MTJmNGY5ODNjZTAzNmMxZmEzZWVlOWZhOGJhN2Q2N2I2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:03:54 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1221921216\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1533367576 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1533367576\", {\"maxDepth\":0})</script>\n"}}