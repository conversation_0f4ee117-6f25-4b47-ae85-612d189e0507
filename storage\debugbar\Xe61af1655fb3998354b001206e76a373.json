{"__meta": {"id": "Xe61af1655fb3998354b001206e76a373", "datetime": "2025-06-27 00:15:12", "utime": **********.265817, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750983311.806756, "end": **********.265833, "duration": 0.4590768814086914, "duration_str": "459ms", "measures": [{"label": "Booting", "start": 1750983311.806756, "relative_start": 0, "end": **********.208168, "relative_end": **********.208168, "duration": 0.4014120101928711, "duration_str": "401ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.208182, "relative_start": 0.4014260768890381, "end": **********.265834, "relative_end": 1.1920928955078125e-06, "duration": 0.05765199661254883, "duration_str": "57.65ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029240, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00276, "accumulated_duration_str": "2.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.241642, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.841}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.252169, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.841, "width_percent": 13.768}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2586591, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.609, "width_percent": 17.391}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IlRjTW5xSFI4U3lTMGNZTEV1TTUxTXc9PSIsInZhbHVlIjoieGhlY1BZcTBHOFVESmtlMjJQbGpvdz09IiwibWFjIjoiNTlkZTQ0ZTllYmZiNjYxNTE3ZjY4ODdiMDE5N2NlZDg5Zjk1NmI0MWJiNzg2YWUxMjlkYTAxMDU3NjUyZDI2NiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1467486990 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IlRjTW5xSFI4U3lTMGNZTEV1TTUxTXc9PSIsInZhbHVlIjoieGhlY1BZcTBHOFVESmtlMjJQbGpvdz09IiwibWFjIjoiNTlkZTQ0ZTllYmZiNjYxNTE3ZjY4ODdiMDE5N2NlZDg5Zjk1NmI0MWJiNzg2YWUxMjlkYTAxMDU3NjUyZDI2NiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983308454%7C49%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ing1bm9Ocnh1MEc4enZoRWFkRDIyeWc9PSIsInZhbHVlIjoiUTJXNWxLU0FJNGdlWklmb3hCNVUvek9BL21KaVQrTi9JTE05VG1EVENUU0Y0Y0xFQnVWT0YxcUZ0WThGbzNIUG82RnhFUFRXQzB4NGI4aW5aaEdqRW5rQkZkbWwrbGU1SXMxVG1yRGlvRU5QZ09qM2EvU0JXKytvWlVESXEvbEM5Wi84SmRhYUZiUDI1K1FudVc0OWcrQ0l4dHhBNkxPUzlidGlmblBhbE9Ua3BMK1ZLNjVrMGtHWCtYMHJJTnNnOWlCQVBGZldBOUxuaGw1cDNHUjNoRHJWZDZrcmRWUjJMdlQ3SFRiaThRVkEwRDFYcFQvaVY1UTVGUksvbi9XSHhIbkVXTTIwM0Q1aU1ySGIzK1c4SEZjd3Vxb1NkaGdmdkZUdkU3Qk1CaUUzaXB2SU9BQ2RZSERtdkRFLzh2ejlIalYwL3E4U2Q5WUFuZ2Q3VGQyUWk3cGRBRElrZ0FEcmJYTUJ3RGdOSGhteStJcm1GSTd3U08yR2Rtc3NNNnBSQWpJVC8ydW9pa0h1Z2hhaUJ1blEwVldqdUt0QlVYTHNKSW9neTVWWlZPcExMUXZVNm5SN3dnZVNnelpYRllvMGQyU1poSGVxSnhMSTl3enlpVHJZaklGbHZCVEIxWUtiYUd1UUdSWFJEK3dzelhtOFdJRWx3Y0ZRM2dVbTg4NGYiLCJtYWMiOiJjY2ZhNzIyZTM4MzllMGYzZGFhOWFmZDQwNDg5NTI3M2YwNGFiYzY2OWViMzJjOTY1Y2YxYWRiMGIzNmI4YjQ1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVROXkxbVRiakZ3ak5OZDFmczR2RWc9PSIsInZhbHVlIjoiZ1V0dXl3VEwyUnZ2ZERGbmZuclBFR25lTDhqbE5UQUZQdjRxQldUSDdiVFdTai9HL2pmZjJJV2Rwc1g0clNFNjRCT2xud1ExaHY5Yk85dmVhc1k2bllGamR6R2pYZUtudmhLTWZtSnZTWmppM0Fldi9XZVc3SzA5YndRMUNYUEpyR0d1Uy9hak96M2hWZmtlZ1gwTzVzRVZkNmNISXBoY1ZycHg2UEYxby9LSUIrMFppWDJEd2J0S0N3K0p0MDZVWG0yMDJSOGorTGkxRlJ3aWt1MnlrNkxnQlVVQ0ZKd1lETnV0bS82bnVPTzFmNmduaVN5SWFHQ0o3U3ZWNkx6T01EMlc3cTFwNjhCdkdNa01UeFJCWEgxcDU3cVhBQ0JudCtOd3pnZ2E5NXZUeGpTK0tSUHZIVjE0TjJDbUwxL1V2S2dmT3p5ZVp2U25LbXBoMVJYQVZzUmF6NnhtYTJBYkNVRGg1a2tHNzNjNU5MWkdxZHFJRmlYNVBWQ21CR0lGeTBBdStLUGI3QlFuclJoYmJXQ3VmcVhXRmNGcVVybzN0Zm5td0tqd0VnMUxsMUJ2QW1SVTFSbWdwMlJrOHhvYjVQSjNZb1RJKzFIUCtVYXRaY25nUTJZaXdGM1BpaEdKMm9NR3I0cFdHQjR6ZFpFL1lRQ05WTklUU2M1N3ovM3IiLCJtYWMiOiJmZDJmYmRjODFmMDMyOTg5MWEyMGJlZjczNmU4OTM5ZmMzMDU3MzcyNjc2NjA0ZjAwOWNhMzk3YjAzMmNiMzMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1467486990\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:15:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjJvT05WaGd3VDJjamRpUERzZ25YUnc9PSIsInZhbHVlIjoibWwzNmZMSkc2NThXQU5tT0IwRG56VmdHMmZ6UU9RdW02L0FRZ0YydXl0blFLRFhMbWVlUzB6ZlpWOGhOMDRkeVVPQ2hodlRvMGNpb1gzdDZscEVITXJrdHpxOEJNRS9sM1lnTHZxZEhvdmM3bWJqNnBEbGF2MUpvNWlsQ2hmcU1COHZLWkt1RTZLY2JuTUprSzZid3ZmRXQrSjVFc1ZURDEvd001M0JaYUdwRThWRVdEaHIyK01kNVQ1cjd2UWxpcWwwbEdoeVFCNkdBNERKUmpPSVg4WkpEQ1VUQW9iRGRuSjFNckxuUTl0WmMvVHhQYlh6eitPbVJJeW0vaXVnKzhIRUVrUlNVajZNbk5laHdKalV4aU50QmRWVDNrRjN2NXBqZmRaTXI5dHVvRFdIY2JKSFh5MXk1aWFtcTQvY2c5eW5jMzF4MmUwUFNPakh3eXFqa2x6M3c5MW94aEFMV3NMNzRtOU1ZNTUrVUpIU0VaUkx1bWRRL1JzVWsxQVZtUzVEeEJINzFySlRWQWNBS1R4RzBmY0JXdUppbVN6cjNzL3BSdEU1WE9hWWIxZHpBR3RJQWI2OCs2VW1tQ1NVMFB3cStiUzBrbEtXTEtreWx6ZUNVUEI1M0JwMmtpOWUrMXlVc0FpYnVzSWlPTmtGTDFQRSt6SEUwNDVWczNhQWwiLCJtYWMiOiIxM2NhYjQ0YzMzZDI0YmIyNTE1YTMwNjBlNWJhNzE2NmJlM2JjNWI2NGNmOWRhOThkNjg1ZjViMzM0ODg1NTA0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:15:12 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFJcXQ4SXN6MHNkeUhnUS9HYVNFbkE9PSIsInZhbHVlIjoiZkJPeUVyZXN5M01yTHRVMnNhSzVjRnZrYnMvK1NuMWxQMVd5d204U05HZ0d0Z2xUSUZQZisvdTh3RjZPcnJsWFZqci9pNmo5eThlMkdSdEd5aHdqc0plaXFkeW1PSG9IcFg2TGFPdkR3bDBsTXpkMGlZWEhjK1M4elhqaXIxcXM0R2V5RG1zQWx3TnlndDdoVFd0U29HeE9jQU5obWdoT3RBSXRLYzYxTWVPV0xvUHhBUmNOeTJCakw2RFJvaVVVUkE0cWNHdjFVdkR0YXVYcnRFUVA0SkxMVCtKWkR1aFJwY1NyZm9RTVMyWDNWYkVkWjF4Y1Z2WlYrcmtIMEQwano2ZEJlcC95ZkRFZy9MdjZLZU5GY2ZTMWNyeGJ1R1FkQ2NmVVNTcnBGRFQvRWtUSytIcUJsSE1nWXlZb3MzUlI1RWFMTmJCM0JPOEhIUnU0RnVFeVltMW9od254dUFlKy92YWpTb3l5dlZpVUppazgyMWp0b3VybTZ6NmZObVdwbkw0M0t5UXh6OXJ6OXp6WjRzdGR0a1oyVFI4VFhjOHQ4WXE2aTFPQXZLYVNib1IzNDZUOC91Rk56UjZPUE01TnhQZXZ2TWwvbTV6cHV1Y3VkeXE4TDNObFZLSXJxZjltbHU3TlFoSGpTcldIdlRHNEVHNXl4Sy8yWXFuU0M1cEkiLCJtYWMiOiIyMTllYzY1MDZjNzQxMzc1ZTNhOTQ4ZGEwMWM1MmE2ZjJmZTg0NzcxYTFjMmY4ZDM4NWVhMDlhNjU1Y2E3YWQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:15:12 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjJvT05WaGd3VDJjamRpUERzZ25YUnc9PSIsInZhbHVlIjoibWwzNmZMSkc2NThXQU5tT0IwRG56VmdHMmZ6UU9RdW02L0FRZ0YydXl0blFLRFhMbWVlUzB6ZlpWOGhOMDRkeVVPQ2hodlRvMGNpb1gzdDZscEVITXJrdHpxOEJNRS9sM1lnTHZxZEhvdmM3bWJqNnBEbGF2MUpvNWlsQ2hmcU1COHZLWkt1RTZLY2JuTUprSzZid3ZmRXQrSjVFc1ZURDEvd001M0JaYUdwRThWRVdEaHIyK01kNVQ1cjd2UWxpcWwwbEdoeVFCNkdBNERKUmpPSVg4WkpEQ1VUQW9iRGRuSjFNckxuUTl0WmMvVHhQYlh6eitPbVJJeW0vaXVnKzhIRUVrUlNVajZNbk5laHdKalV4aU50QmRWVDNrRjN2NXBqZmRaTXI5dHVvRFdIY2JKSFh5MXk1aWFtcTQvY2c5eW5jMzF4MmUwUFNPakh3eXFqa2x6M3c5MW94aEFMV3NMNzRtOU1ZNTUrVUpIU0VaUkx1bWRRL1JzVWsxQVZtUzVEeEJINzFySlRWQWNBS1R4RzBmY0JXdUppbVN6cjNzL3BSdEU1WE9hWWIxZHpBR3RJQWI2OCs2VW1tQ1NVMFB3cStiUzBrbEtXTEtreWx6ZUNVUEI1M0JwMmtpOWUrMXlVc0FpYnVzSWlPTmtGTDFQRSt6SEUwNDVWczNhQWwiLCJtYWMiOiIxM2NhYjQ0YzMzZDI0YmIyNTE1YTMwNjBlNWJhNzE2NmJlM2JjNWI2NGNmOWRhOThkNjg1ZjViMzM0ODg1NTA0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:15:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFJcXQ4SXN6MHNkeUhnUS9HYVNFbkE9PSIsInZhbHVlIjoiZkJPeUVyZXN5M01yTHRVMnNhSzVjRnZrYnMvK1NuMWxQMVd5d204U05HZ0d0Z2xUSUZQZisvdTh3RjZPcnJsWFZqci9pNmo5eThlMkdSdEd5aHdqc0plaXFkeW1PSG9IcFg2TGFPdkR3bDBsTXpkMGlZWEhjK1M4elhqaXIxcXM0R2V5RG1zQWx3TnlndDdoVFd0U29HeE9jQU5obWdoT3RBSXRLYzYxTWVPV0xvUHhBUmNOeTJCakw2RFJvaVVVUkE0cWNHdjFVdkR0YXVYcnRFUVA0SkxMVCtKWkR1aFJwY1NyZm9RTVMyWDNWYkVkWjF4Y1Z2WlYrcmtIMEQwano2ZEJlcC95ZkRFZy9MdjZLZU5GY2ZTMWNyeGJ1R1FkQ2NmVVNTcnBGRFQvRWtUSytIcUJsSE1nWXlZb3MzUlI1RWFMTmJCM0JPOEhIUnU0RnVFeVltMW9od254dUFlKy92YWpTb3l5dlZpVUppazgyMWp0b3VybTZ6NmZObVdwbkw0M0t5UXh6OXJ6OXp6WjRzdGR0a1oyVFI4VFhjOHQ4WXE2aTFPQXZLYVNib1IzNDZUOC91Rk56UjZPUE01TnhQZXZ2TWwvbTV6cHV1Y3VkeXE4TDNObFZLSXJxZjltbHU3TlFoSGpTcldIdlRHNEVHNXl4Sy8yWXFuU0M1cEkiLCJtYWMiOiIyMTllYzY1MDZjNzQxMzc1ZTNhOTQ4ZGEwMWM1MmE2ZjJmZTg0NzcxYTFjMmY4ZDM4NWVhMDlhNjU1Y2E3YWQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:15:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1444203021 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IlRjTW5xSFI4U3lTMGNZTEV1TTUxTXc9PSIsInZhbHVlIjoieGhlY1BZcTBHOFVESmtlMjJQbGpvdz09IiwibWFjIjoiNTlkZTQ0ZTllYmZiNjYxNTE3ZjY4ODdiMDE5N2NlZDg5Zjk1NmI0MWJiNzg2YWUxMjlkYTAxMDU3NjUyZDI2NiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444203021\", {\"maxDepth\":0})</script>\n"}}