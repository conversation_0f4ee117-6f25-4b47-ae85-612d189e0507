{"__meta": {"id": "Xf4d0eecf585483f5ec1d08080428743b", "datetime": "2025-06-27 02:26:14", "utime": **********.279181, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991173.880449, "end": **********.279196, "duration": 0.39874696731567383, "duration_str": "399ms", "measures": [{"label": "Booting", "start": 1750991173.880449, "relative_start": 0, "end": **********.228203, "relative_end": **********.228203, "duration": 0.34775400161743164, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.228212, "relative_start": 0.3477630615234375, "end": **********.279197, "relative_end": 9.5367431640625e-07, "duration": 0.050984859466552734, "duration_str": "50.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00271, "accumulated_duration_str": "2.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.254489, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.052}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.264369, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.052, "width_percent": 16.974}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.269686, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 83.026, "width_percent": 16.974}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2136328127 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2136328127\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1097782186 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1097782186\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1130291444 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130291444\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-485147887 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991168324%7C32%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImpDVUU3bUlWaUNqbGVKc3Y4OUE3TVE9PSIsInZhbHVlIjoicERkeE5lSDlLaWRXbmtwK3ZPOTE4b0g3dERjRTF0Yzh2UnhwMndzOXhtVG1aUFVaK3ZWNjI0SzNwb1IyYVhCTCtmWHJpUEZ5bU1OUk9tTnVDKzlKYVZheEFJTjJZNWZBVitlS2pNd0hPSWVjNWFKaXpJZmQ5RkQreldYNXY5ZitrNnlxK1VrMlVRMmg2WjVXT0pvT0JPdnJyU3l5RENpVVlnZmJJbHhYT01rclZ1TXNsSVdURmVqNUpMNVR0aE9vNkViVXd0bDJKTHV5NGtlY0VnYXltKzZPM0w1MXpJdGhPYS9pQUN2NjhkMlZwbldjL2xiaml5WGVYenljdVhXMGlHR3hMSkRtMk55b21rL054T1Rvbm9Rak5Mb2g3RS83RTc3ZXVJRjRrMWJxWG5iU05SMllvZW1CTU1sVHh2WXJ1RVJFMkpwcVFTd1Fab2tXQmNOZE9JK2VBNnlDc2EvbElTWndrbGQ1VUVGTlhRM1kvWTVXM0s2azhYSG0vdkhlTnVUNnZRT05jV3JESUFpczJXb21MWlNZdFJIM00vbDN5QlREa3I3b1E4YzRaMDA4ajVvaVpsejdNa0NkQmFwVy9zSUVhRUJ6OU13ZXpNaFlub3BlUEh2Rk9yNEIyb3Z0VlBkdDZpalA0QWRRakYra0U5bjJ6dG5Dczg1STdqbCsiLCJtYWMiOiI1NmVhZWYyYzMzMGUyOTk3MWE4MzFhNzRmZDM5Y2YxOGQzZTg0MWYyZTY2MTY3MGMwZmZhZWEwNDNmZjY1Mjc5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlBtNzExcFRtdFdyTzU4c25XYjNzOXc9PSIsInZhbHVlIjoiWlhpWTRpSzRlb2FKSmdOM292bHZaRDU4bjZlRG1Pc21UTXVKNC9ZWkVmbWttUkZQNjAyd2ltcmJodVNGZUp0NTR2ekFKNjRvL0RTNkp3citrckJlRjB2dVFxYWN2cHI1dlNFM3E1ZXVYM2RLRWxrTnRJNEpBOVU4ZWFNRUlQbHkxUkZEWExKWTZOV21FbEgrcG0rUkpjcDZhSDBOM09EQVA2MUhyZC9LTkFYdzN4THVrWStvM0duZVZhVndLRjg5cTJDQTljWkI2Z2FROHNUNzlzU0JyWWFsRDkzNWZpRHdtM0FMWEhiQlZXbHVFOC9WYzc5YlJYVTNtTElrcHd5cnVvTlFRUUhuYlJEZk4vM2VCRHhLQ1p6czZlenFteTZOaHNnRzl6dHh5ZVV5Y3M0TDFjZENjQzg5MzRHZVFXZVBFbjFOblR2NHIrRWJ5V1NqTjhEbE9uS1N1Y3pseU5pdE1ES1lSNDJrMDhBajZKbWtlUkpoL0ljcjZlL0xrRTVzbzArMFN1K243SmpZTEtnU1VXODlyYnJ3ekVUeVZ1dEg1T3FEZ2FQcStZZm5HbGZDY2ZaaVRYK3B4cWtlRjl1akVVWGZxZGxibGR3RkxDR3JscHpqT2p3QS9aRlpkV013YUxVakQ4S3dFS1R3TFBiRUJ4MlBNMS9VcjF0QzlpVVIiLCJtYWMiOiI2ZmQzNTBjMzVlODZlZTE3MzZlNzQ2MDg2NDU0M2U4ZTJmMjZmNjcxYTdjMzBkMTNjZmE2YzlhYzMzNGVhYzU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-485147887\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-526726098 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-526726098\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1963238919 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:26:14 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IngyMGVYYVMxUlNNUmdVeU1tOThWL1E9PSIsInZhbHVlIjoib0k5V25JcGlhenZxZW83czFFckk0aTllVEV5d3VXL01WaExjZlZrUmZ4VnBlajRsRjUyKzN1QUlndTJYb3NYQUU3T2ExRVFsR3QyTlR4SldHeFhTU3NZOFNvRXpBUi9YMlVzTFRycmovR2NaRDhzdlFqM09iWUtmQXh5UVhBOFoya3lvWFpESElRbnZhdUVZSWRKR3ZScDJoejlxRy9QMXlDWnhwS2V4SzBGeURadkdPRVh4dUwzZWdvTXZ4RlpUWjlIb0lUaDJQbmJVaEVoT3o0d1FOclV4VzJ4enJvSjlQSU9RamhkK0pLMHZrMXVCMDRicXBuTWFYTVpaeUJZaVpYdU1ZZEdCbCtPK2ZvR21TS1JWWlV3R3A2TEw5Mm42bk9pdkl2cEV0NVRScTVaaHd0U1MwVElWTXQ2d2lwMGRKMG9zSDBvUDZJTUlGV2RmQ20rTnl3Y2dsdTB3bzZuNGM0czRFT3I1MGZjT3A2TUR1K3pCZGpkL0xwQ3NYS3lPVTg2UGpyRDB3WTI3NXpjT01iY2pZUVBMeUJYZEwvZVo3SlBodGNVTGhJM0p3NzcvUnlqWm9WWUd3cHZXL0NlNjBiNXFla3Z4TldMdVlCYTQ4Z3d6cDQzTWdodlFldUZHSVV1cTJseUtaMzFNc0d3cjhGTzBoZDdoa3Z2dWJiekUiLCJtYWMiOiJkY2FlMTdkNjg2ZmY1ZTAyMjQyY2QxYjM5ZWJmZWVmY2MyNGNlNTZkNGIxZDBkODBiMDQyYjhlZDBhZGNlMzkxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:14 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InNNQVcraWsrN21ORy9VS0dXZGNVbFE9PSIsInZhbHVlIjoia1NqcTRhZXF0VFVkQXk5UG5qWUVrSEp0dlc3MVJzdHFrOEtTcDZFOFo1SmlMMUZ0OXNOZHRMSHdhancvTHo0amxyQW8wdXZBRm1jVFJuaUY2aWFabEcveXJjOVJDNTlBSEpqaU9NMmIwWG5qbHBpWGtHRVRjZGdQb09DZ0IzRk5WUDI3Z2h1RXNHUjd5bTUvTVJSeEJCZEhPcldZdzFCeXhwSW1CRlVkV1B4THQ5Q0QwY1RxbDZyd1p4NUZNMjEyOEFRVDM3dE1YVzVKaUpPQ21hdkpObmFXTHhZM3Q2YnlRMmgrSlArZjBhL1BScEVYYStnMEE4YVQwTGk3d1VyRzBmVlNIellZQXZEQSt6MWFMdVdDRXB0R2VNVmZLVHJ1L1ZINTRLZ1h3MkpkVSs2MU9mZzFtSGxvOTc4RXova1UrdUs5Z2pjNXNwL0VpYTBma3ByOFVrQWJXOTdOblh2UTZTMThEaFBDU0xaSmFuTzdJR0N4Y0RVM3E0M1V2TVUyQzI2czJtSmo3Tm43MG1mcUJVd1F6d2NGangrcEh0MlIyZWh4eWVnc1hUczVNdFpPSnBSZGVucU1BOWFIRHdsS0l4OXVzM1l2OWdybGFLWXJYNm82OXIwekFQNHBMNE1HaU9wN0tTK3Q3RGlLd2Z6SU9xOGRJWk8yTGRXMFEyTEciLCJtYWMiOiJmMDY1ZjU4YjBjNzk3NTMyMmU5ZTI0NzFiNzc5M2QwMzhmMjA3Y2I5YzM4YTNmN2FkY2RmNmRiMTY1NDQ1ZTk1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:26:14 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IngyMGVYYVMxUlNNUmdVeU1tOThWL1E9PSIsInZhbHVlIjoib0k5V25JcGlhenZxZW83czFFckk0aTllVEV5d3VXL01WaExjZlZrUmZ4VnBlajRsRjUyKzN1QUlndTJYb3NYQUU3T2ExRVFsR3QyTlR4SldHeFhTU3NZOFNvRXpBUi9YMlVzTFRycmovR2NaRDhzdlFqM09iWUtmQXh5UVhBOFoya3lvWFpESElRbnZhdUVZSWRKR3ZScDJoejlxRy9QMXlDWnhwS2V4SzBGeURadkdPRVh4dUwzZWdvTXZ4RlpUWjlIb0lUaDJQbmJVaEVoT3o0d1FOclV4VzJ4enJvSjlQSU9RamhkK0pLMHZrMXVCMDRicXBuTWFYTVpaeUJZaVpYdU1ZZEdCbCtPK2ZvR21TS1JWWlV3R3A2TEw5Mm42bk9pdkl2cEV0NVRScTVaaHd0U1MwVElWTXQ2d2lwMGRKMG9zSDBvUDZJTUlGV2RmQ20rTnl3Y2dsdTB3bzZuNGM0czRFT3I1MGZjT3A2TUR1K3pCZGpkL0xwQ3NYS3lPVTg2UGpyRDB3WTI3NXpjT01iY2pZUVBMeUJYZEwvZVo3SlBodGNVTGhJM0p3NzcvUnlqWm9WWUd3cHZXL0NlNjBiNXFla3Z4TldMdVlCYTQ4Z3d6cDQzTWdodlFldUZHSVV1cTJseUtaMzFNc0d3cjhGTzBoZDdoa3Z2dWJiekUiLCJtYWMiOiJkY2FlMTdkNjg2ZmY1ZTAyMjQyY2QxYjM5ZWJmZWVmY2MyNGNlNTZkNGIxZDBkODBiMDQyYjhlZDBhZGNlMzkxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:14 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InNNQVcraWsrN21ORy9VS0dXZGNVbFE9PSIsInZhbHVlIjoia1NqcTRhZXF0VFVkQXk5UG5qWUVrSEp0dlc3MVJzdHFrOEtTcDZFOFo1SmlMMUZ0OXNOZHRMSHdhancvTHo0amxyQW8wdXZBRm1jVFJuaUY2aWFabEcveXJjOVJDNTlBSEpqaU9NMmIwWG5qbHBpWGtHRVRjZGdQb09DZ0IzRk5WUDI3Z2h1RXNHUjd5bTUvTVJSeEJCZEhPcldZdzFCeXhwSW1CRlVkV1B4THQ5Q0QwY1RxbDZyd1p4NUZNMjEyOEFRVDM3dE1YVzVKaUpPQ21hdkpObmFXTHhZM3Q2YnlRMmgrSlArZjBhL1BScEVYYStnMEE4YVQwTGk3d1VyRzBmVlNIellZQXZEQSt6MWFMdVdDRXB0R2VNVmZLVHJ1L1ZINTRLZ1h3MkpkVSs2MU9mZzFtSGxvOTc4RXova1UrdUs5Z2pjNXNwL0VpYTBma3ByOFVrQWJXOTdOblh2UTZTMThEaFBDU0xaSmFuTzdJR0N4Y0RVM3E0M1V2TVUyQzI2czJtSmo3Tm43MG1mcUJVd1F6d2NGangrcEh0MlIyZWh4eWVnc1hUczVNdFpPSnBSZGVucU1BOWFIRHdsS0l4OXVzM1l2OWdybGFLWXJYNm82OXIwekFQNHBMNE1HaU9wN0tTK3Q3RGlLd2Z6SU9xOGRJWk8yTGRXMFEyTEciLCJtYWMiOiJmMDY1ZjU4YjBjNzk3NTMyMmU5ZTI0NzFiNzc5M2QwMzhmMjA3Y2I5YzM4YTNmN2FkY2RmNmRiMTY1NDQ1ZTk1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:26:14 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1963238919\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1723419208 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1723419208\", {\"maxDepth\":0})</script>\n"}}