{"__meta": {"id": "X1b025dd6d20efdd422fb04c205b82e14", "datetime": "2025-06-27 00:47:52", "utime": **********.456385, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.03777, "end": **********.456405, "duration": 0.41863489151000977, "duration_str": "419ms", "measures": [{"label": "Booting", "start": **********.03777, "relative_start": 0, "end": **********.407461, "relative_end": **********.407461, "duration": 0.3696908950805664, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.407471, "relative_start": 0.36970090866088867, "end": **********.456407, "relative_end": 2.1457672119140625e-06, "duration": 0.04893612861633301, "duration_str": "48.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45028960, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00282, "accumulated_duration_str": "2.82ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.434821, "duration": 0.00184, "duration_str": "1.84ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 65.248}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.444082, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 65.248, "width_percent": 15.603}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.449822, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.851, "width_percent": 19.149}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1216929057 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1216929057\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-303045559 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-303045559\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1928541886 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1928541886\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1350758041 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750985255040%7C71%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InVob2dJcnJNSk4wM1dnckU0RWt2UGc9PSIsInZhbHVlIjoiM0o5R2gvT0hueThJaWlqSDlFdUUwMTZxVUpKV2pQS2oxOEoycFp1SDR6b1hzSDduL0pTY0p3b0M3RVc2V1NVekluY1h1Nnpla1kzSU1QT1cxRmRvaDVCWEUzb2kyZnpVTFVqNDNkWUpwY2h2ZDZydlplMFRONHRvbjNETVBGSERSVG0zYXcyWVpWNHpTQVRld0VhemJyaFJja3QzamtEenRuRE40MWcxSzJUbXA0aUdXejJBSGt6a2tTYkMxcktUTEZmRXRVYSt4Um9tY3prOWtBVGdpZ0tqbWJhcVFkYXZodkJNTmdaSTFwNlQ1S21abFN1Q29hYlpXNVR3ODdGRFNtMmU1a0lNREdEZlRHOFduR01rSE9BaEF1V0I3SHh5a3pqQjFRWXZabTRyS09hcUgrUWkvMGFDK0tJbHRlN3dId0J4N1NSbS9Fa281RHJYSUJ1cGhCZ3FRT2kraHFSUUhKRmZLaWdFaXl4Qjlkc2YxTGVHWDZ0Tms5aUUrUGlpRlNlUXp0TjQ1RUFrZUJEcms1d3ZGRHdDL0NDaGtsQVpHeEpBcy90TjF3Y3Q5di9NaWhhVmtFdWZKR3U1cGJqNTZKT01wVlpzaWlzd01xVStSM1NyazRBSTdtVUFUVytNOW5ncjN6QkU1c2FBaWxnc1E3UC95RlRXc2ZVNUZrSWwiLCJtYWMiOiJkNmI3ZjFlZTc4ZGEwNWIyNzk4OTVhZjEwZWJkYTI4NmE3NTg3ZDhjYjRiYmU2MTEzZDI5ZWY2YzljZDM0YzMzIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFtdTZYVWgxVmM4aytlMHR5bWR2aUE9PSIsInZhbHVlIjoiSFd0VnloVjZER3QrRDRrd0lCOS9kSTIwazdPZld3dlVCOWpINWU3aUVqQTN0OWxEbTBlYWQxVWRSN2hmdXpXeGN6bkRlb0QrYnlFZnJzdmtYVnpwaUxTUmFQZXBLNkFXMmlDOGpZTzJvVDV3Z1F2dmFaUTZ4N2xzTGtLRVZlZEVNbnlPcFpyNHhURFA2czk0QW4zanhVclRZbmRPYVIwSGh2MmhjKzVuajVzZUh2ZnMrVXhCcXlnd2svN2pJa3AwRlE5WGNjZFdRRXBsTHBSdDRZdmV5MEZpaERyL0lQd0tvUWtmU3VtajBlaXJXUmEvZXhFOW05NmhpeGZoUG9jcDg3VkJzNG50eHR0NzFIZGNsZnlzTkg0YjZDMHlaenJwWHpORHRZZVk1bzE1TXFWNGkyNzJhNFYwek9lSlpNNkFsUGxQSDZlRGdoQklyUWdhUThMWUd6R1hyaEpRSkZuTG9uOE1jdHpVOUh3VmZHMzBnQ3JWRGxZZTRMVC9JY1hGOHJ6MDRxUVBqWWlTYUF4VXl4SHZsUGthc1dDQTFkeEQva2pvWU41dWlORTd2eFlwT2ZFZ2RYNTdsV0djeVMwb0FBa0RTL1ZaeVFKRnJHU1I2L2JmYXlobzlDYW1yZ2lNbXAycnMwL1ZralRnc3puQ0Q0dGtIUC9ERS8rRmVFK2wiLCJtYWMiOiJkZmZlNDRkMzUxNTY0MTQxZGUxYzI2ZDIyMjViODZmZDkwZWNiM2M0ZTE0ZmY5MWFlNTZjYmNjN2Q3NjcxNDM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1350758041\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1558802303 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1558802303\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-599123119 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:47:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNSLzZjZHYyZno2UWd1TVFGNXhoQ1E9PSIsInZhbHVlIjoiMnJjTm53ZFFrRlYramlacG9oM0JyZWVjb0E0RlhsTkpaV3VnRkpFNWRMMmNjb0RRK2xEQ05RVkRVOG9rU0JkeUpiM21zVmdsL3JVWlhDVVNUQndNME1pRE1nN1B1R2xJTEwxdmRQdlJVemRKMkJPSHNkTytUcWtaVlV3N1hFcUdFVi90eDZLSndNZmZmR29iWGJpTXVuL1JBRmJwTUd2WHFFZG55Z3ZNVy91NW8vdUc5MU1SNXR6UThmT0JLVzVhWkx5U0JSYmoyWEUvckpoZjA4U3h0eFlsd0NFV1VLbVdaWTZKOWU2MG5IaFU0SmY1Y1N3R3NqTWhVUUdIeTVQd1YveFd3cmVTWVhnWTVTMmYrSVNGbVE4dllvS0ZZcGhJRzZLN0ZBK1lvVVFUdk5GdnYyaVhrd1l5UlJnRnBiTXQzVnlveitwRy9HbTVZUHhhV2dxQlhSeFlZRXJ4ZlQrVStTM2hMNGg3RFRxaTNjNXJDTkVML1hkTFYyWXdZMTBEQVBiM290YUpoSzRFWTRxamsxZks3NloxZ2lZRjhEV2h5RWwxYk9nblJJYjBJMmowWUN1aG9qSWRRQXBKNUltaUZ0dFBMYmJoODExOWsxbzVZQkl0aU1PQ29COG1JdFNuMjJpZVV1NGZVZmNkODlJNE91OGxiQkNhQUN0U2pRd1MiLCJtYWMiOiJjODUyODk4M2NjMWNkYTYwYThmNmEwZGUwYTFjZWYzMDRjMjc0ODRjZWJhNTUxMTliMjFjZjg1YWQwYTFkOTljIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:47:52 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ii9ocnBOYkxMSFhLYlhScEU1OXYyRGc9PSIsInZhbHVlIjoiRDhuZzByL2hqZnZaRzlqSEIxVDVIT2xZUXpiSGpXaDh6aDBtaTlZRzdLMUIzNkhpa1hUby8wU3FzR2t1eUpmeWU1aXpLb25aRlAwek0zLzdxbFk5ZHZHMW9vdE1OVi9UcC9UZGRKeTJ6eGcreS83ZFM2UVhGUHd1V3FOS2p6cm5hempVdEdtOHJCVm5obHBlbDRVaVBvK01xU0piMFpLV09nLzZrOFdhNmFBWFJ3RXRBckpWOEp2dU1TMlJSd1U0all6WUhZa2N0Y1Q2ay9jOTBYQjllT042WnZLeVZDZjQ5Z1M0V0w3Y2FJakM2SldTakhnWkZ2QXBtck4vK1RzaVR3a0xac3F3SzhOTkpuUjFCMnlyRHB4RkVTYXA5T2NzNE91angyeVg0QzFqRkRHNjVaNHBzUkRudkxPZlFmelAvZkNNSnNhZmpsRlFVcEliaGEzZFdmS2FsNllEbXMrSFdJdEFSeFVPcDdKR1Qzb1hpZ3p2SFNvTmZ3M1ZPc04yejhpa3hTMmkrNlpRUVZ0aCs2ODRnUjQrbHpzSzU2SXZ4WmRwS1VrVmRnMGltUWR0TytkdVNZTlU5S2h4Q0p6SUQ0dTFKdGhSZ0RYMDBJeDNpcFgraEhpdWoxYjRWSGJWVnJLaVNIQjQ1d1FRaHl3bWJPQVd4bC9ad2ZjZkhob1AiLCJtYWMiOiJiMGU0NDJlMmE4MzI3MTBmOTVlNTlmYjhhN2I4ZWQ2MDNlM2RlYTcxMWZhODRlNTdkMDE1YWVjYjIxN2E4NDhmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:47:52 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNSLzZjZHYyZno2UWd1TVFGNXhoQ1E9PSIsInZhbHVlIjoiMnJjTm53ZFFrRlYramlacG9oM0JyZWVjb0E0RlhsTkpaV3VnRkpFNWRMMmNjb0RRK2xEQ05RVkRVOG9rU0JkeUpiM21zVmdsL3JVWlhDVVNUQndNME1pRE1nN1B1R2xJTEwxdmRQdlJVemRKMkJPSHNkTytUcWtaVlV3N1hFcUdFVi90eDZLSndNZmZmR29iWGJpTXVuL1JBRmJwTUd2WHFFZG55Z3ZNVy91NW8vdUc5MU1SNXR6UThmT0JLVzVhWkx5U0JSYmoyWEUvckpoZjA4U3h0eFlsd0NFV1VLbVdaWTZKOWU2MG5IaFU0SmY1Y1N3R3NqTWhVUUdIeTVQd1YveFd3cmVTWVhnWTVTMmYrSVNGbVE4dllvS0ZZcGhJRzZLN0ZBK1lvVVFUdk5GdnYyaVhrd1l5UlJnRnBiTXQzVnlveitwRy9HbTVZUHhhV2dxQlhSeFlZRXJ4ZlQrVStTM2hMNGg3RFRxaTNjNXJDTkVML1hkTFYyWXdZMTBEQVBiM290YUpoSzRFWTRxamsxZks3NloxZ2lZRjhEV2h5RWwxYk9nblJJYjBJMmowWUN1aG9qSWRRQXBKNUltaUZ0dFBMYmJoODExOWsxbzVZQkl0aU1PQ29COG1JdFNuMjJpZVV1NGZVZmNkODlJNE91OGxiQkNhQUN0U2pRd1MiLCJtYWMiOiJjODUyODk4M2NjMWNkYTYwYThmNmEwZGUwYTFjZWYzMDRjMjc0ODRjZWJhNTUxMTliMjFjZjg1YWQwYTFkOTljIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:47:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ii9ocnBOYkxMSFhLYlhScEU1OXYyRGc9PSIsInZhbHVlIjoiRDhuZzByL2hqZnZaRzlqSEIxVDVIT2xZUXpiSGpXaDh6aDBtaTlZRzdLMUIzNkhpa1hUby8wU3FzR2t1eUpmeWU1aXpLb25aRlAwek0zLzdxbFk5ZHZHMW9vdE1OVi9UcC9UZGRKeTJ6eGcreS83ZFM2UVhGUHd1V3FOS2p6cm5hempVdEdtOHJCVm5obHBlbDRVaVBvK01xU0piMFpLV09nLzZrOFdhNmFBWFJ3RXRBckpWOEp2dU1TMlJSd1U0all6WUhZa2N0Y1Q2ay9jOTBYQjllT042WnZLeVZDZjQ5Z1M0V0w3Y2FJakM2SldTakhnWkZ2QXBtck4vK1RzaVR3a0xac3F3SzhOTkpuUjFCMnlyRHB4RkVTYXA5T2NzNE91angyeVg0QzFqRkRHNjVaNHBzUkRudkxPZlFmelAvZkNNSnNhZmpsRlFVcEliaGEzZFdmS2FsNllEbXMrSFdJdEFSeFVPcDdKR1Qzb1hpZ3p2SFNvTmZ3M1ZPc04yejhpa3hTMmkrNlpRUVZ0aCs2ODRnUjQrbHpzSzU2SXZ4WmRwS1VrVmRnMGltUWR0TytkdVNZTlU5S2h4Q0p6SUQ0dTFKdGhSZ0RYMDBJeDNpcFgraEhpdWoxYjRWSGJWVnJLaVNIQjQ1d1FRaHl3bWJPQVd4bC9ad2ZjZkhob1AiLCJtYWMiOiJiMGU0NDJlMmE4MzI3MTBmOTVlNTlmYjhhN2I4ZWQ2MDNlM2RlYTcxMWZhODRlNTdkMDE1YWVjYjIxN2E4NDhmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:47:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-599123119\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1465683726 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1465683726\", {\"maxDepth\":0})</script>\n"}}