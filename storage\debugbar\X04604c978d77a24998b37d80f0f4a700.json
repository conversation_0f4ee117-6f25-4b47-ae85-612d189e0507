{"__meta": {"id": "X04604c978d77a24998b37d80f0f4a700", "datetime": "2025-06-27 02:25:42", "utime": **********.043341, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991141.629986, "end": **********.043355, "duration": 0.41336894035339355, "duration_str": "413ms", "measures": [{"label": "Booting", "start": 1750991141.629986, "relative_start": 0, "end": 1750991141.978435, "relative_end": 1750991141.978435, "duration": 0.3484489917755127, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750991141.978443, "relative_start": 0.34845685958862305, "end": **********.043358, "relative_end": 3.0994415283203125e-06, "duration": 0.06491518020629883, "duration_str": "64.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45735016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01592, "accumulated_duration_str": "15.92ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.003857, "duration": 0.01482, "duration_str": "14.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.09}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.027993, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.09, "width_percent": 3.832}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.03411, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.922, "width_percent": 3.078}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-519614760 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-519614760\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-793711805 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-793711805\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1889407840 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1889407840\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1610725917 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991139555%7C26%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJQRG5DWlc3clRMUEdqU2doT3U4NkE9PSIsInZhbHVlIjoiUXlqRGIwTjdVUjhEeW83NW5ETzdyT2RQNUROd1l5S2dOalF4SXE1U3NtM1ViZk1Ua0VkQjdaaE9PUTBsMHRrNk1WSXdoQ1JFMlNsR0Ryd201NExrakRhd0xXa3VUVXFMSHkxQzlHVnQ1eVo4ekY5M1EvQTVMWmM1TjZ1Nnl4dDhRbjN3b1VLUjBtVzFYS2kzUThZTzlGalpOdk1uZndSaktxR3g3SVZrOHE0bC9qWUdaUkRKMVZoODhvVS9GWUQwWWtOeWplcVdjelZNK3c4UEdYVWtYNUpSOFFpOFcrRE1iVFdGM3FLVWxlaExqQVNxaENXOXdpUTlCYTl2bTM3WWYxWUY5WGxkcVAzQ2RXTHN4VXN5djBlZjRBUkk5aHE4UjFER0tkT29BZFRyZW5ZcEZ1ZnA0bDUwVTIwV2Q2U01XSlRDWkE2cWtYR0dBTzNwUG5pbVJDNmV0dGh3cWk4V0hQSTQyM0FkVFNqdHBnN1dtU3l0RUNhWDB0RXhmN1F5OUZOdkczMWMySWVISURnTFB5ZWFSaENPMjJkK0N4WUFoeG1UQ255MXpVNkhSNC95VUUwalE1RzFhRWRuZ2E2U1R2U0RqdjdSalVTZ2t5eVlTajdnMC9VQ09VNTFiL29XbjJRWW9tRnlMY2w4SFpFQzdFd3VVdm9Db01BbVJwZUoiLCJtYWMiOiJiYmQwYTBiMTE2ZDIzMmI4OTRiYmVmNzRmOGQ1ZmE0MjFiYzE2NzNkMjY3MWFlZjQzODQ2OTg3ZDA4NmUyYzRiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNvb1FUT1FnN3FBVnFEOXF5d0ZYNWc9PSIsInZhbHVlIjoiOS9TTjF0OUpsQnR1RlZRM2IxSmEzdVpmcWs0aG52V2lYQ2prekxqYm9aZngxTndYUnh2Mjg1VVY4UXoyU2xjcTV1RlRXdCs0SDRob1FJVks2c0NFbGMrRXRlb3Bqb3lKZUU3UW9wa0Jkc0l6dWJXMm5yazFXU1BrcXBDbXF4OGdYMUpNNVB2aDd1bXpPczB5TThlaWU1UG5SOHhlTGFWK0hWRXFIeGp4RGQ0ZlFIMWkwVFM5OHhsL0hjRHB2RjZ0NldZNlhoN3hMcHJOcGExWkxJSCtiTWtoTm1DMjRuQmQ2R3lXb01jaXZJdzNBaURYSy9DampQYkFYVjhRQTR1MXg5bHZURis4WUFwck9odDJtRGYwb2lpOUVCYVU3ZlZYRXpYWXF1NkxUamIxek9SYUltN3I2cUdtdXl5dUt5THdsWXNiSzI5YUVaQVRFanI2ZUJWOEJnUzN1T0lxRjA5MGhTd1RRQ2NsQjJNbVF1YXhqdzh2RFJyaVdSUUpPaWFNdTZKaEw4WGRnRUhMMHNQSkt3Tm9mK0xsRmdVU3JiRzNib1VHZmpIbTQ2L1RuL2FkY3hnV1FxYTVtTFFHalFpRVM3ZHJPTVJIMmRSbzAxT0FLbWk5ZzdmSDIxZ003cW5iM1U2ZVpPdFpFaXBVS2dSL2dRcUkvajZsWWxQNjFBUHYiLCJtYWMiOiI1MWEwZjQyMDcxYjYzY2ZlMDg3NjlhYzJlNGZkZTI4OTJhODk0MWFlZjhiM2Y3NTczNzJmZDZkZjBmZTdjOGQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1610725917\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1422122232 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1422122232\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-590097549 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkM1K1cvcE5GTW9rbWorYStvaWVQa1E9PSIsInZhbHVlIjoieGxSaFNhWlErdW5OV2NkSlVtOXF0dnNoYnN5Y2drb1l1RkZ4ZHhndVg1RHE5OXJBK0JDNFNrSUdsTFhRVnNuUHZPQ0VPY0MwNGlCRDBoMFFaRUxNQlg3Ynoya0pNMW96WG5uNXhwOEdJSm9nSUZ0Mnd2VWpkNXNOZ0llYU5yb1N5RlAzakNNZFhZZFBDa0twNDViVHYvNEZnYTVjUk0zU3F3TFBnQmZKMnhXZWlHcldTWDBaSmtBQkFtdHpEYitWVTVVOUtOT3VJa0N2V3lDcERSTDVXYXZueVRSbnFYaUhkaTRSV0tSZGppZ0NrZGE5cFYyaElmNHBqb1YrU0l2dVVuT0xSejRvT1JRcnBZNmxqRmZ1UTJob252T0R5eWxmZ2UxOVNxMjlGT2ZGSS9rSFZCYTZjQTA0VGR1Y3FHQlpQM1JSUXllRXB5Zi9ycW1rWjc3a3puRkxjRXRvb3BQNlVyTHhSNGNnQTA1SWpaY0NVUGdOZ0o4YytsWHRiUS8yY2lab2UzSElJS3ViekZoeXlWck5tVlpneUVjVDZHSFBvS0ZiWUY5anQ1UGV6REtoZENzcHB1NnVhTnBta09PekE0ZUY2SUVmZWhGdlVoVEVjNjQ1bFZIeUYvMkhVYURLU1hjS0s3NE1nTTZzc084Mk03RjlRSUJiMlRsOGVyUDIiLCJtYWMiOiI3ZjZhOGUyNGNhNTk2NDdhN2ZjZDE5OTc4ODM3MTlmNDIyNjkzNjdlNzdjY2FlMmYyOTc4MzRiY2U3ZWQ2YzIyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:42 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJTbzMzL1NKWGQ3azA2ek0rZDVPakE9PSIsInZhbHVlIjoiMy9oeUVKRS9IWVJ3bUdMdGkrdXcyMGltSi8xNW5tRHZKVjlQTVhYSjJTa21FK0RDSGVFTTdlSFFKMjVrL1RiWE4vcTRoZzVScmZyaTlvV3ppcTFQWkRPanZCWEwvTDJVTGt5Q3JSTlBiMlpUelN5ZyttY0NsbDZta0E0cjdkV2JoVFpMeldmWiszWU1KMC9CTkRaeXI3R0xvTmhTZnA2TEMyNi9VaVZyRGl1ekhEQWNSUDYwanlsWVF0OXl4OEtwdHBuN3Z0VGRsb2dBamp0NHFmKzJRUVlEOG04YjVzMEYzZVpnU2lXTkpKNHl3NVhiNmJMUDJ5cHFwRlZlODd4cWk1ejNYRndnQVdYOG9sSmdoWk5GMy9sZytjZVEwY3poSTZqdXdTbHhsdE1FQUJ0anZLdUNkK1Z1Umt2cndML2FxYkJOanJyN3NpcTljbVY4OG9YTTRqakxWb3FJaGtOM1JzaHorUm5pMmh1L044dmFNN2NoTm9sVm9WTVZyMWhrTlUvMHBwVUVPK1IvdVlTMkxwWW9vWUF0aVJ2d0lCdUFEMmJjUVZjMVlzYjhyYkRXZUZlSUNoU3A3d2hoTHBnbkJtNVRQZHFRMDQxakRxUUdvcXAxaGdVRWZka0FDY0s5b3dyVGNqbGYvZ1A1OUNtcGtyV3RNVVU4eHpoSjNFcXYiLCJtYWMiOiI4ZDZlY2I2ZWU3YmRhODdjNWRmYWRkZjJlYWJkZDQ2YzhlZjA4YjFiYzEyOTg4NTA5ZDA2MjczNzcwMDcyMjI2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:42 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkM1K1cvcE5GTW9rbWorYStvaWVQa1E9PSIsInZhbHVlIjoieGxSaFNhWlErdW5OV2NkSlVtOXF0dnNoYnN5Y2drb1l1RkZ4ZHhndVg1RHE5OXJBK0JDNFNrSUdsTFhRVnNuUHZPQ0VPY0MwNGlCRDBoMFFaRUxNQlg3Ynoya0pNMW96WG5uNXhwOEdJSm9nSUZ0Mnd2VWpkNXNOZ0llYU5yb1N5RlAzakNNZFhZZFBDa0twNDViVHYvNEZnYTVjUk0zU3F3TFBnQmZKMnhXZWlHcldTWDBaSmtBQkFtdHpEYitWVTVVOUtOT3VJa0N2V3lDcERSTDVXYXZueVRSbnFYaUhkaTRSV0tSZGppZ0NrZGE5cFYyaElmNHBqb1YrU0l2dVVuT0xSejRvT1JRcnBZNmxqRmZ1UTJob252T0R5eWxmZ2UxOVNxMjlGT2ZGSS9rSFZCYTZjQTA0VGR1Y3FHQlpQM1JSUXllRXB5Zi9ycW1rWjc3a3puRkxjRXRvb3BQNlVyTHhSNGNnQTA1SWpaY0NVUGdOZ0o4YytsWHRiUS8yY2lab2UzSElJS3ViekZoeXlWck5tVlpneUVjVDZHSFBvS0ZiWUY5anQ1UGV6REtoZENzcHB1NnVhTnBta09PekE0ZUY2SUVmZWhGdlVoVEVjNjQ1bFZIeUYvMkhVYURLU1hjS0s3NE1nTTZzc084Mk03RjlRSUJiMlRsOGVyUDIiLCJtYWMiOiI3ZjZhOGUyNGNhNTk2NDdhN2ZjZDE5OTc4ODM3MTlmNDIyNjkzNjdlNzdjY2FlMmYyOTc4MzRiY2U3ZWQ2YzIyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:42 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJTbzMzL1NKWGQ3azA2ek0rZDVPakE9PSIsInZhbHVlIjoiMy9oeUVKRS9IWVJ3bUdMdGkrdXcyMGltSi8xNW5tRHZKVjlQTVhYSjJTa21FK0RDSGVFTTdlSFFKMjVrL1RiWE4vcTRoZzVScmZyaTlvV3ppcTFQWkRPanZCWEwvTDJVTGt5Q3JSTlBiMlpUelN5ZyttY0NsbDZta0E0cjdkV2JoVFpMeldmWiszWU1KMC9CTkRaeXI3R0xvTmhTZnA2TEMyNi9VaVZyRGl1ekhEQWNSUDYwanlsWVF0OXl4OEtwdHBuN3Z0VGRsb2dBamp0NHFmKzJRUVlEOG04YjVzMEYzZVpnU2lXTkpKNHl3NVhiNmJMUDJ5cHFwRlZlODd4cWk1ejNYRndnQVdYOG9sSmdoWk5GMy9sZytjZVEwY3poSTZqdXdTbHhsdE1FQUJ0anZLdUNkK1Z1Umt2cndML2FxYkJOanJyN3NpcTljbVY4OG9YTTRqakxWb3FJaGtOM1JzaHorUm5pMmh1L044dmFNN2NoTm9sVm9WTVZyMWhrTlUvMHBwVUVPK1IvdVlTMkxwWW9vWUF0aVJ2d0lCdUFEMmJjUVZjMVlzYjhyYkRXZUZlSUNoU3A3d2hoTHBnbkJtNVRQZHFRMDQxakRxUUdvcXAxaGdVRWZka0FDY0s5b3dyVGNqbGYvZ1A1OUNtcGtyV3RNVVU4eHpoSjNFcXYiLCJtYWMiOiI4ZDZlY2I2ZWU3YmRhODdjNWRmYWRkZjJlYWJkZDQ2YzhlZjA4YjFiYzEyOTg4NTA5ZDA2MjczNzcwMDcyMjI2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:42 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-590097549\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}