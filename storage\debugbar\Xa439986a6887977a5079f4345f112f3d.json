{"__meta": {"id": "Xa439986a6887977a5079f4345f112f3d", "datetime": "2025-06-27 02:12:36", "utime": **********.835146, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.403947, "end": **********.83516, "duration": 0.4312129020690918, "duration_str": "431ms", "measures": [{"label": "Booting", "start": **********.403947, "relative_start": 0, "end": **********.759965, "relative_end": **********.759965, "duration": 0.3560178279876709, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.759974, "relative_start": 0.35602688789367676, "end": **********.835162, "relative_end": 1.9073486328125e-06, "duration": 0.07518792152404785, "duration_str": "75.19ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45734672, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02024, "accumulated_duration_str": "20.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.791646, "duration": 0.019100000000000002, "duration_str": "19.1ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.368}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.819171, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.368, "width_percent": 2.125}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.824944, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.492, "width_percent": 3.508}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-177669194 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-177669194\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-729205737 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-729205737\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-678579647 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-678579647\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990354161%7C6%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJlWTVnZ0RGdjhvek80MkM2SlQvaWc9PSIsInZhbHVlIjoidWE5bVBxblNOcGhoQUVsb1kvbkgzbVZId1RqN3dRcWlpK3lSSnVsS25KZlhTblJSeWtxd3I3MEVUWGgyck12T3BOUGxjSVhZbHhhTG9FU3Bvck5Famo3RXZUOGlTbHh5NHFjaHpSUHZPYWx1dFhJeWxiR1J5cWpJVW02MVRpZDRPckNDYlVrbCtkK3RRN1gxZEFabU5BeXNjRjY1dTQxSytnZ2M5TitDQUUrdXJ5alpQTmVKdFlCdzBjYVBlOE0wQTNpeGVUL3VYZDBVejBtM3FtRHVtd3VDNk9iV1VkTUhlNE0ydVJOaWljTmZmOWdrREJORzFTOVpaTitUTXVLbFRSTVFSenlqa1VXaXB2OGtuMWlZU0hUYVdoUERuQm82NkFCWm45R0NiRFBLYTZnL3FyU243OFJwWGVVSjBVSUprbWM3VVlHdVFIVGJXKzRzVElCM0tFV0NWZWVWbEhkRHVGcmRZSVA2V1VCMWVlaEtsMlZBTTF1TlhIeXRWSm1QdG8xZHF1RjU3SjNORzdyUzhyT3kzSElQcjh4SlBYM0F3QWFOdkxnYk9jZXdMalhCdHdnK0RDYjA4Q1dYUmR0NlNBb040cU9obkNxdWQ4Z05sbGtzTXlZN1Q3TjY2NVA5QjBKbktyTU1MVmFWakdETWp1YUtQdDh4VkxZYjJqRjciLCJtYWMiOiJjMjcxOWFmMmZmOTA1M2ExMjQ2ZWQ2NDYyOTgzNGU3ZTA2YWY0MjJhYWRmZTFiYjY0NDNhZDIyOTcwOWIwMTg4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlV0eC9HaXpReXp1dUF2QzhJems3UHc9PSIsInZhbHVlIjoiS0JhNXFXeEFVdDc1Wk9PdFhUdVlaNUNUa3lWSW5ESWlBcnVMbFRQNUVHY2E2d3dUWDF2cnZqa0MvRWt4ZlJTc2ZKWjJzc0k5elYxS3paZnFvOTNBNVpFL3A4dU9yMkNDUnFoN2crNWJmbStMWFFsQjE5c1dwWnlmZlVFTzdIWCtHUzRqMGZkZ2dGdzdBQ2FNVE9tbENYdVNVVFN0a056NHJpaW5aMEpOSUdwWmtzS2pMYzBKWURQZkUrTUl1S1Q5MVExWUwraXBXeXlTU205SkJzYVlQU3hUQ1F5SEk0WmphMGV5aEJjdHhBeFBYenJiZXp0UUNSTW9hMmx2UDdhQkJjeG4zdE1SbG0yVnhsZkVvemZPNG5wMFlxMEFZUWJsNjVUVzltcHJGcFZNaDg5Y2pxNnNHNTJtWTBpeU9McEhCK0hjQlk4TE11bGliSHJBa1pJWEp5MnhFWlZ4czdZMlNTRlFoaEFTV0gwUnpsSWE0M29GNXN6ZEJhQ090ZFM5b3pWMXVpZDlvWnlkMVB4RThDWXZXY0pGbm1vN2w2TGtXUG92SnNwOXhnUkIzaTh2bzd4M1NlWkxscjhGU0RKdS80dHpBSkpFOTdtMmo2ZGxkZFpJUlFIcFhZaU44QUdjZFROTG5xUkFkWG9sT1liR3NMeHVEaEVpRmhSdUlIRTMiLCJtYWMiOiJiM2UxZWFlNDhmZTU1MDk2ODYyNjQ0MDJmZDYzZjkxODEwODFiNmQ0YmY3NTM5MWQwOTE1ZTdmNWEwZDkwZTFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ4ZTRpMTdIOEFTYTJDQWhhTDlsY3c9PSIsInZhbHVlIjoid25OcEM0MWx3M2lJTEpGTGNHd2FwYy8wNUZROXdjMzNNais3dTE2dUx4amZwbHpLRnJkUk5UK2VZaXBqYTh4TzJLNTNxUHFvMTNoaTVHN2tRRWd1QVhxeDJBZk4wYVFRSEY3ZlpUWjQwNldVcDNhejR6MEVYMG4zWEk1TjJmZERFZGtaZit6L014emZUT1ljUWg3ekR4emZhU1loV1lXTjE4Zk9mc3EyM05GaktYK0xsYnk1ejlRZ1JmaCt3MG1EQTgzS0xaNGJ3ZFlrTU4wZG55Wm8raXpSdC9TSmVTb2w2SUtmOCtHZFdzZGNwTDFpajlHUlRMYlh1czJhYkFDZ3VrdFAxem5LUmdrRDB5ZjV4by9NMGMwdDdHN1J6OGs1dUR0UVMySUk1VmRqSTltaWhHbE42ZkZFTXcrTmtuK0FSbm1rTm5nTTlveHhwZ2IwdVRvL0wrQjhvMndjUUhOQTh5M2drRXR1UDJrRkFuSURtM2xtREljRW9vK3YwdlhCeGNLdmhCTUNKb1BZazhqTUMxZ0oweGNidldSeGRBYVVURE56dExiYkY5Mk4xeWM0UVdKbjM1Q1c1S0orMndEWEI2WmNoTHlWblFleldHemRrWnpMSzFoWUdhN2lEUUpJbnpiR3BYb2JublhmMHN4djlDM3FyOEJhcVMrblpvRzgiLCJtYWMiOiJkMTY1NGEwNDQzYzQyNDBlNTc5ZjAyZGQwNTQ4MzQzNTgyOWNjZTFhZmZjZGRhMDMzNTc0M2U1Y2E0ZTZlYTBmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InV6aFBBdWkrN2pxenF5VFpwR0xHd2c9PSIsInZhbHVlIjoieFJ3K24rTWFUWStoR2JWa2FhVmhlZUs3MWl1ZVlKY2RmT21FaCtWSE11ZVF5VTVlSGQwc28rUDYxZkdlMjJPbmYrS2xodjgxZExsZkVDT2pYdlh0YVl3TUI5S2gzQTIyNjg4OG5FU056ZmhOUzNodzZJR1I0U3FtRVNNUW5wSHNJN3ZQN3V4aGxvWFN2T0hVK2lTa25vKzU0SmE5aGRqZVo3R1E2MkZoaGNOQ2J0SmNIcGMyRzBmamNuM2kveERZeGZwR1JLbUwrSktQaU91NzhmbG5YVnJNUlZHL1dZdkNNb3JoUjMwWFhVaEZMTDZNaThFYUFPSE84WGd2ZWxZUGdpeGVlZmZndzhaNG5CaXFpcFcxWXpuOVpnMEZEVFZsSnFxQkl1MStrWnVLTzV4OXRwK2M0MEE4K3F6Yk4zSmxoZnNHUkl2c2tlVmdaN1FtTm1MbE5semUvZ2U1blFlTlZNSVNYN0ZqUEQrRlZpbHNaRGVoazNvc1Y4eUwxTVgyVGM5UldqRXdYNWlid0ovUTR3NnZLcnV0K056aXJBUWcrQWtHa2NNZnBBS25nTkRoa2M4MVhtcFhUOXBrcFJwTTkzYTV4QVk3KzRKeVlDNjFobWo2ZTFrNCs0RzR1c1hZblgxbXVxR3I2SXlPTFNvTHV1djBsUGlDNWF4b3RpcS8iLCJtYWMiOiJmZDVkZGRlZWRiZWNmMWFkZWJmZGRmMGI0ZjFkOTVjNTA4ZmY1ODliN2UxN2I4YTMyZjc5MmVhNmNiMzc1NThlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ4ZTRpMTdIOEFTYTJDQWhhTDlsY3c9PSIsInZhbHVlIjoid25OcEM0MWx3M2lJTEpGTGNHd2FwYy8wNUZROXdjMzNNais3dTE2dUx4amZwbHpLRnJkUk5UK2VZaXBqYTh4TzJLNTNxUHFvMTNoaTVHN2tRRWd1QVhxeDJBZk4wYVFRSEY3ZlpUWjQwNldVcDNhejR6MEVYMG4zWEk1TjJmZERFZGtaZit6L014emZUT1ljUWg3ekR4emZhU1loV1lXTjE4Zk9mc3EyM05GaktYK0xsYnk1ejlRZ1JmaCt3MG1EQTgzS0xaNGJ3ZFlrTU4wZG55Wm8raXpSdC9TSmVTb2w2SUtmOCtHZFdzZGNwTDFpajlHUlRMYlh1czJhYkFDZ3VrdFAxem5LUmdrRDB5ZjV4by9NMGMwdDdHN1J6OGs1dUR0UVMySUk1VmRqSTltaWhHbE42ZkZFTXcrTmtuK0FSbm1rTm5nTTlveHhwZ2IwdVRvL0wrQjhvMndjUUhOQTh5M2drRXR1UDJrRkFuSURtM2xtREljRW9vK3YwdlhCeGNLdmhCTUNKb1BZazhqTUMxZ0oweGNidldSeGRBYVVURE56dExiYkY5Mk4xeWM0UVdKbjM1Q1c1S0orMndEWEI2WmNoTHlWblFleldHemRrWnpMSzFoWUdhN2lEUUpJbnpiR3BYb2JublhmMHN4djlDM3FyOEJhcVMrblpvRzgiLCJtYWMiOiJkMTY1NGEwNDQzYzQyNDBlNTc5ZjAyZGQwNTQ4MzQzNTgyOWNjZTFhZmZjZGRhMDMzNTc0M2U1Y2E0ZTZlYTBmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InV6aFBBdWkrN2pxenF5VFpwR0xHd2c9PSIsInZhbHVlIjoieFJ3K24rTWFUWStoR2JWa2FhVmhlZUs3MWl1ZVlKY2RmT21FaCtWSE11ZVF5VTVlSGQwc28rUDYxZkdlMjJPbmYrS2xodjgxZExsZkVDT2pYdlh0YVl3TUI5S2gzQTIyNjg4OG5FU056ZmhOUzNodzZJR1I0U3FtRVNNUW5wSHNJN3ZQN3V4aGxvWFN2T0hVK2lTa25vKzU0SmE5aGRqZVo3R1E2MkZoaGNOQ2J0SmNIcGMyRzBmamNuM2kveERZeGZwR1JLbUwrSktQaU91NzhmbG5YVnJNUlZHL1dZdkNNb3JoUjMwWFhVaEZMTDZNaThFYUFPSE84WGd2ZWxZUGdpeGVlZmZndzhaNG5CaXFpcFcxWXpuOVpnMEZEVFZsSnFxQkl1MStrWnVLTzV4OXRwK2M0MEE4K3F6Yk4zSmxoZnNHUkl2c2tlVmdaN1FtTm1MbE5semUvZ2U1blFlTlZNSVNYN0ZqUEQrRlZpbHNaRGVoazNvc1Y4eUwxTVgyVGM5UldqRXdYNWlid0ovUTR3NnZLcnV0K056aXJBUWcrQWtHa2NNZnBBS25nTkRoa2M4MVhtcFhUOXBrcFJwTTkzYTV4QVk3KzRKeVlDNjFobWo2ZTFrNCs0RzR1c1hZblgxbXVxR3I2SXlPTFNvTHV1djBsUGlDNWF4b3RpcS8iLCJtYWMiOiJmZDVkZGRlZWRiZWNmMWFkZWJmZGRmMGI0ZjFkOTVjNTA4ZmY1ODliN2UxN2I4YTMyZjc5MmVhNmNiMzc1NThlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}