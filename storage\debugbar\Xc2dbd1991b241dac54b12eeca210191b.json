{"__meta": {"id": "Xc2dbd1991b241dac54b12eeca210191b", "datetime": "2025-06-27 02:25:23", "utime": **********.580802, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.130032, "end": **********.58082, "duration": 0.4507880210876465, "duration_str": "451ms", "measures": [{"label": "Booting", "start": **********.130032, "relative_start": 0, "end": **********.507568, "relative_end": **********.507568, "duration": 0.3775358200073242, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.507577, "relative_start": 0.3775448799133301, "end": **********.580822, "relative_end": 1.9073486328125e-06, "duration": 0.07324504852294922, "duration_str": "73.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45733992, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019989999999999997, "accumulated_duration_str": "19.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.535251, "duration": 0.019039999999999998, "duration_str": "19.04ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.248}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.564302, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.248, "width_percent": 2.601}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.57018, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.849, "width_percent": 2.151}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-973197786 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991028607%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZSblBlbTFGekxYVjZpTjdJQ1U2anc9PSIsInZhbHVlIjoialdRSWpybjRkUEhOcmxLczUyQTJwKy9rOU1KNUt3SmlEMnZRK0RYOXFNeXpLOGZFSUM0bU9ZN0lJbmtSY3NCOVJkNmxoYktJemF2Nkx1QmNHYmV6bGNINUF2SkFSL3VvMzRVdU15NHB2NldjYzkzdkdDejhDMTRqRkU1Y0lOOUJWK3VRNlVSSTkvQzlBc0Z1NDBERzJZYksvS3JEd0pmR2lKZm5BcWl6U245QXQvWmN4N1JoQ3FTMG9VTXhsdk1iQWZkR1VLaTIwNjlJaEpGZ21FK29wV29JVHlXQmR6V2J6M0VhaDBuUllmMlcyajV3ZnRManVTUkpqUVYzUStOb3VLN1lTY1FEbGVwK2MxOXRNQ245VzErQ3c0RmF6amNtS1BTZnhodjMrenJqOG1Nbld1YmMzRFEyNWk0OUNYeXJPSjJFNGJYb0VPNGZJSlJqb2JSY2JSTXBiRHE3Q0hpTmtYZ3QwK2tLdjkzOHVXT1hDSWZUN2F5d2FieXNJR1ppUXFQUHV5TnFSaE5uRkY5dURzZk5MSFVrcUdmekRjQjRZdG1zTkMxWGRCak9RQXZKVFJJa1c5TGFsOFRHVkZHQUk1aUY1MFZOZThERHJqbFBjTkZVbjg1QnBjSUh5dkdyMmNHWkVSRkNoeWJ3cjZiNUFSd2NZZXpoZWR5NzNQT0oiLCJtYWMiOiI2Y2ZhZWZiMDc4NmY0YTU1OTMyODRhYTVlYjM5ZjM4OTc0NTg5MmMwMDE4MzNlOGE4NjA2NDlhYzE3YWI2NTVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjFHOElRMUhVWU5oNmE1SDhKckVZNWc9PSIsInZhbHVlIjoiUEFCMEtibWZsc3VJcU54SGN3QzROV3p6NFFGSmh4UG9aWTc3Q1RlY3hOQjFYcEdSQWE5WWw2Z1U1N0kvSVlFdDYzUFNFTFBtVHU3TVBXVTgreUNldDNnbjRkN3dxcll0RHVnV0dBUEdLNmN0ZUh2YVlyNlNMMU9ZWGhKWjFlTGxQY2hTaEQxUmI4azA0Z0hoeEFmeWVUcGNVMXlteDZNMEtpcEFoMGhPZ0JvU29uTnV5SjdqOVQrM1pDb0k5VG8wV0VSbFZwTkxoWXQzeVd2S1RhWVJQc3NNR2tHK2hCd3F3VGt0dHp1cTkvbkdOaTB5Ym5qbTFSUnNOZmJNc3RFYmJOV0w1eno3OFdlV0tUWlNMcCtuQXZaNnNuZTkza0dWUzZkL2F2T0JYTVhHUkRHNlgrTlZEQmFZUjhUOG1rL3I1cUtzbThRV3RNZzlod2VsZUlJVW0wWjU5WElmRWJyRU5UL0h1ZDRvMGlJcVZ2aGRwbnNxc21Ic0h1Nk56K0ZjNWlvRXNjUXFkWmNJUU84VEJLSDRyOTVTaFYvekxIQk9DUndjckVLUWh3Uy9pRmpXQ0RJU1RoeWYyMXkwdXhLaWlDTGI2bExtdExIWEplMnI1Vm5rd2xEZTQ5Z2pSZThYTi9DakZjdHFiOTVRaElVVFNNNThzcWoyb1g0cWpwVm4iLCJtYWMiOiJlZjM3ZGI1MDEwOGExNjBlMmViMjFiNTY3YWM1YjRkMjVhZjIxZmQxYjgxYWMxOGE1NTFjMmNkZGMzOGEzM2VhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-973197786\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1078869991 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1078869991\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-571952659 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjludHM0Y1lJYzFXU3BWSGFpY3l4UGc9PSIsInZhbHVlIjoiM0xiUjd1ZDdNSk1ibHU4ZmpLQ1pGWmhPUUFjS1hrR1h5NUlnc1ZUSjJyQmFoM09zMW5YTXhlVFVQT3VMT1cyUVdxZUt5UDBQcWt1QktmYzVra29DV2VBcWhlMEovbk1SWk5MMFIyWVNuTnh6YkpoZ0VQc2lwY0pXN1VrbCtwbk1LaHgxa3pLUUZyMzI5dDhQUjdRUzJZc29XMXo1ZklvU0lwZW5VWkRGbmVJRkhpZ3h3L0VhMFNSL0dBY0dYSXg2ZHB5Y1hHZW1QT1RpNkE2cW5lZ29xY1B2eXY0bGY3SVNlMXR3Zkl4WG8za1RYYXAwSkJQQ3dWR3YwM3NIRVdBeHRRNHdlVExadFJFMlkvdkZ4V1FXK1U3bkpoMXRGWURqUlEweTJEazZ6Mkg5bjh2aTM5cklrcThsNFJEQkFETEFBMTRTU3BpanlRMTk3Qy80Y1hXRmxwMUNxUHBMV0NyK2F1Z285SUR2M05jNHlmcUJPY0QrSFFlblpoQlREYUlYTEZNUnJLcWZoZlA3SnZQZTAxMFB4MXF4ODhjMVo1MDA5ajhVY1EvQlVhSW9KUFBoNnAwSVMySUkzd0YvbG1ZeFl6ZCtSekhRcGZ3SGFZZk5tN3l5T1BDMS9heVBBc25TYWx0S0NFU1ZzdW9XMG45aEZvMUFvcVZXdWJrT2w0WmwiLCJtYWMiOiIwYTIxY2Q3N2ZiOTFjMDhmYTgyZWFlMGE1MmZlMDRmZjA1NmViZDZjNDk5OWQxNjVlMTliYmZiMWEyMWM4ZDAzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjExVGhzTjdsTFF3b1I2SEpVWG8xYUE9PSIsInZhbHVlIjoiYVU2VjFGZERyb2oveXZ2SUtUdHlkaW4zK2xXOXF5M2txcmxRVjdzUnNySmQxaXI1dnVQLzJRcldRcmFFdE81dHZrQzhzYmlMUXhhSys4RG5VQ0IvaDZjaUpwSmxGaGlsdHhHT2poUmVpSGJ1TWpmSzdiSVpEQ3g0VUlQTWhsQnNxcFZxb0JPSGlISkR1S2lMR3dERjlrUHFJRUJtL1Y1aVYzWi9ZSGorOFdJNEFzWDkvbENUWjBHcTZ2YXhzK0xKSGVDaHM5U0dJMWkrdVlVdFNnSjNjdTFvQmxjc3RITGprMUQzeDUvV1pWSDRLUkZoSWNkM2dUc2M3RGpmV0lCeWhOUTN1ajNGenB4U2dUYmNLMFpJS1c3MThXNlNIRmM2ZXB1cnUxSDl3VlZXZ1ppd1M0cnB3Sk5pbm1XNWJxcEhSYklnUUhrVlB6MXZTL3oxcnBpUTBjYnZtZUxIM3FQazAzRnNGSmNtaWx3K3N3NnNlcWNQMFcyOUtJczhsUEZaSzE5U1BjbkpuS24wakhYT2lMUG82aGZWTUR4dFp0dUs3aHVxODV2Qm9YdWJCNzJCZkpoajRPYkJJNGxVb1E0WWpsWXBtdFFGM0dZTk51N0haNTc3eXZUdmtZdnI5RGhVRXJ4SVVLcVBDOThTUXNvSFJxWTBvT2tuM0dpUjFVQWwiLCJtYWMiOiI3MmMwNzA0NzY2MzI3MTExMTJiMWI4MzA0ZTg5YmE0ZjM3NGUxNWE5YzQ0NGVmN2UyODY1MTMxZjY0ZWRlODE3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjludHM0Y1lJYzFXU3BWSGFpY3l4UGc9PSIsInZhbHVlIjoiM0xiUjd1ZDdNSk1ibHU4ZmpLQ1pGWmhPUUFjS1hrR1h5NUlnc1ZUSjJyQmFoM09zMW5YTXhlVFVQT3VMT1cyUVdxZUt5UDBQcWt1QktmYzVra29DV2VBcWhlMEovbk1SWk5MMFIyWVNuTnh6YkpoZ0VQc2lwY0pXN1VrbCtwbk1LaHgxa3pLUUZyMzI5dDhQUjdRUzJZc29XMXo1ZklvU0lwZW5VWkRGbmVJRkhpZ3h3L0VhMFNSL0dBY0dYSXg2ZHB5Y1hHZW1QT1RpNkE2cW5lZ29xY1B2eXY0bGY3SVNlMXR3Zkl4WG8za1RYYXAwSkJQQ3dWR3YwM3NIRVdBeHRRNHdlVExadFJFMlkvdkZ4V1FXK1U3bkpoMXRGWURqUlEweTJEazZ6Mkg5bjh2aTM5cklrcThsNFJEQkFETEFBMTRTU3BpanlRMTk3Qy80Y1hXRmxwMUNxUHBMV0NyK2F1Z285SUR2M05jNHlmcUJPY0QrSFFlblpoQlREYUlYTEZNUnJLcWZoZlA3SnZQZTAxMFB4MXF4ODhjMVo1MDA5ajhVY1EvQlVhSW9KUFBoNnAwSVMySUkzd0YvbG1ZeFl6ZCtSekhRcGZ3SGFZZk5tN3l5T1BDMS9heVBBc25TYWx0S0NFU1ZzdW9XMG45aEZvMUFvcVZXdWJrT2w0WmwiLCJtYWMiOiIwYTIxY2Q3N2ZiOTFjMDhmYTgyZWFlMGE1MmZlMDRmZjA1NmViZDZjNDk5OWQxNjVlMTliYmZiMWEyMWM4ZDAzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjExVGhzTjdsTFF3b1I2SEpVWG8xYUE9PSIsInZhbHVlIjoiYVU2VjFGZERyb2oveXZ2SUtUdHlkaW4zK2xXOXF5M2txcmxRVjdzUnNySmQxaXI1dnVQLzJRcldRcmFFdE81dHZrQzhzYmlMUXhhSys4RG5VQ0IvaDZjaUpwSmxGaGlsdHhHT2poUmVpSGJ1TWpmSzdiSVpEQ3g0VUlQTWhsQnNxcFZxb0JPSGlISkR1S2lMR3dERjlrUHFJRUJtL1Y1aVYzWi9ZSGorOFdJNEFzWDkvbENUWjBHcTZ2YXhzK0xKSGVDaHM5U0dJMWkrdVlVdFNnSjNjdTFvQmxjc3RITGprMUQzeDUvV1pWSDRLUkZoSWNkM2dUc2M3RGpmV0lCeWhOUTN1ajNGenB4U2dUYmNLMFpJS1c3MThXNlNIRmM2ZXB1cnUxSDl3VlZXZ1ppd1M0cnB3Sk5pbm1XNWJxcEhSYklnUUhrVlB6MXZTL3oxcnBpUTBjYnZtZUxIM3FQazAzRnNGSmNtaWx3K3N3NnNlcWNQMFcyOUtJczhsUEZaSzE5U1BjbkpuS24wakhYT2lMUG82aGZWTUR4dFp0dUs3aHVxODV2Qm9YdWJCNzJCZkpoajRPYkJJNGxVb1E0WWpsWXBtdFFGM0dZTk51N0haNTc3eXZUdmtZdnI5RGhVRXJ4SVVLcVBDOThTUXNvSFJxWTBvT2tuM0dpUjFVQWwiLCJtYWMiOiI3MmMwNzA0NzY2MzI3MTExMTJiMWI4MzA0ZTg5YmE0ZjM3NGUxNWE5YzQ0NGVmN2UyODY1MTMxZjY0ZWRlODE3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-571952659\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}