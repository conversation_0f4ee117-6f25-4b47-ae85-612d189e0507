{"__meta": {"id": "X5766d48ade5e7836fe47aa2846e7eb72", "datetime": "2025-06-27 00:14:56", "utime": **********.857756, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.364298, "end": **********.857775, "duration": 0.49347686767578125, "duration_str": "493ms", "measures": [{"label": "Booting", "start": **********.364298, "relative_start": 0, "end": **********.800697, "relative_end": **********.800697, "duration": 0.436398983001709, "duration_str": "436ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.800707, "relative_start": 0.43640899658203125, "end": **********.857777, "relative_end": 2.1457672119140625e-06, "duration": 0.057070016860961914, "duration_str": "57.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45026248, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00259, "accumulated_duration_str": "2.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.832215, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.954}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.8437011, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.954, "width_percent": 18.533}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.849621, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.486, "width_percent": 13.514}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-180568290 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983290489%7C46%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InovQ3R1V2x5YnYySW5tK1I2YXlqM2c9PSIsInZhbHVlIjoieThvQld0R3l6QWczSzY0eHJsamV6OFZCTmlVdWhpYlk1ZVJVSmdoM2ZXaldoOVZxRzBNNStuL1VPcitjbFMva09Zam5IS0NkdGVJczV3MStzUzYwNDlxWnpORHRlUGpyMEgxYjJMNjgzMlB1REZrcXBoVi8zait5L3NBOE91TWhIQ3c0YWsveTF4RkRKb2svaEhLQThIY1ZvSWVvVjA5VVBYdGtQRFUrNVpPSTJjQXA5N0I4YXVKRkpuVU5nU2VZSVVxRGJNWjZZUUlDT0ovUWVISmZJcjB6RjZabHRKSVBqSE1zSzBYcXVwazdJSzNYSXpvdWhqcC9LOU9EbDIzU0lrdlVrbHF6L29WVFoyM285M1VJUURLZkM0cytiaVdIbDFBWDlpM0E4TXFnbTdpNzZob1kvbEp2N08wYkRWcDk4NnBTZTRKbmpscVIvR0RUZ05TVm11eGdRZlRXaFRYa3ZEeGIvMWVqejh3bHNlYkZHRHF2cGZQT2hQa3JlcGNJUGhJbEZaaFAzZUlKcHJaMEFSbzBsdHd0UTU3MWFPZ2RGU3ZRWXNQakI0Wm1DYmpQRGYzM0lYQWVHb0F0b3pTTzN2Y3ZSYXlmUlB1QTZqcWNncWlhcHdiT1RvOXlLQTh3aTRGVGJNT3VHK1o0b092VTN2d0xOcHhlMDE2ZGZJNkkiLCJtYWMiOiI3NWYxZDIyMWVmMjAwMmJiNWViNmFiY2M4YTNhMWNhYWJjMTI1NWI4MWUwZTJiNjFkMmZkYTExZTRjMWQxYzNmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik9tY2dIb0xkcDFHdEZzSHhjME5NZGc9PSIsInZhbHVlIjoiWFJ0eG0yMEdVQU5kcDRsMjVKMVh6bDVDcDFibDRFdDhScGlqZmI5eGpQZVVjS3crd1VBek9vZGtBbldqMDNVVWhKL01aako2dW02UmczOC95RlZDV3VqWjcxU2RqakJiRStUSFMweG9GdXVxNUFwUEN3dVU0N0dWTHRuYUFFL3JLd1dOL1dHdmNXZnY2NnpiNWZQRzIxWXplZGxrY3dWVzBGRjF6d1BXbE9VVU1RTW1GeTRuYXJidmJZSnRwQmJydDJTNW9UQ1o4b0pSZEVPcHk2VExuQmVQNEdJWU5nbmZldWlOc00wOHJaYUJyalV3YnhJblR1b0pmRkJPQ04zejU0SldBWGd1c3dxdjJ4M3NEaDRYU2V5dEM1dmZFTDBrZktNcTE5c2NIeWRIdDc5SDB6a1ZEMXpCSDZrSmVMKy9rYVNFMzNpMXdWTGRrSk83R2pWTFFJS2ZZOUo3Uzh2Q0NwWDEybnBKbnhtandlODhNZmdzQW0wa3RuZWFSZWpiU2VnYzBBT3VkRzlCT1hjd3Y5a2ppM3AzSktXVFZteG5lajN5eFNOblhWb3JqcDdnR1ZXR2NuZTJpczFOeDlOWnJaV2FGdm9rRmNDd0xmandTeG1OTTFwMzZZTmhvb255OTVaUGlnb2lwdnFtNEw4NHlLYXluSTVUOE9JY2FpKzciLCJtYWMiOiI5NmRiYjc2NmQxNzRkNzNmZThjYzU3OTI5YWQ1ZWRiNjQ2MDQwOTlkZjUzMTA4YTUwMTM0ZTFjODE5YWJlMGI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-180568290\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-384485558 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384485558\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-943358265 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:56 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNVVXoydzV6RVFRMXNySkdpUnJ4Rnc9PSIsInZhbHVlIjoiYjJJSStYclUvSlR0UUwvbHQwY0tyUFMxWFRUSWlQRlFla2d6S1NFK2FTUUtNV3U3Si9idThQSzJuSjAvZ1RIVVVPYkFueVgwc3p2V1V4bVpPelZiR0ErMnZsUVlDM09oODgvVDdUYXorU0lERGo2dXZKTnAvRXNoTXZ5aUZHb0xTZjViT3VJYnZuOEVucElGRjVVM05wZFhHd2hwMW1hRHYxREF1SzVya2hJTktmVFpMMlBLbnZQRVdMa1g4M2F6WTA1eDlpVVQyWUQ4ek1SMU1veDcrd3cxeDh2d3lqMklHbE0rZjJJdUtMaVp2K1RNMmV6SHY1U2ZYL0V1WFZsVDgzVURjQjJVVnQybUlPTzR3N3RQbFVvdDIwOEdveE53K3VqSlRST29veDdmdmh6TjRaWmJxZEVkWVJaQTdHR25ackVSd2FBR3FJemVqZkJ6aUxNT3NIT3pCRnl0ZW44aFRTUk9BNENoKzZYZlVkWjBSNUt3Vk1SSXB2QkE0blVoRWdndFJxOHV3aWNJUGVmbUVtbitsb0NjTStwRWJHNFE1MXJPbGdPMXFpekJER1JEZzdrQi8wc0VtQU5HQVcyNkppRVcycWpqVWtrc2NLRHlDQ3JmWFZlYUVOVzF5ZVpaRTIyZXZOclo5UFlXLzNCb2dVQVB2eFY5cm9MMURadVciLCJtYWMiOiJlYWQ0YzA5ZWZmZGY2ZDZmNmYzZTM0ZWY3OTc1YTcxZWQ3NTNhNTdkYjNlZDJmOGFkNWI1YTMwNWYxMmNhYzQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:56 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikcrd3RxaHV0N1p5VG50OVY1azVPRmc9PSIsInZhbHVlIjoidGxDMGFOTlRzdDh5VVNRQ2N4Y25tNkFkdzhXTU1tUkhNRWVCSzhtZVk5S2NhNTA2WXVWWlViL0p0d1RWcHcvbjFCa2JIMFFSVkdOWGJYYWNSREhZdUVxaVpUVUprY0hrQzlEQkhxMmNKOWZlS3FJbHUxdFlmaUx3MVFvQkQ5WmRxOFNTbW96WXNwYnlOUWg4WTdmd0JSTHh3MXdTNkFhTkc4NEtmQkd2NXZLVG4rYUI0Z1Y1dGJEbEw1c2xLaTJvanBPMGJyWEZMZFltcjJMOE5mWWFrV2RScnRnTEVqRjdEbUg2aTZvaUd6SnVLZHR5alpBanp6MHpzTlpRWlV1WXZwV3E2QXM2eTJOcHl2REJ5UDdpeTRSSTEzalZZMnpQSUQ1U3BtUlVPL0haUzFaRERUN2d3TWQ1L3BRcjk1L21CZVpMajF0UWNtVDFTN2xQcnNkb1hZWFZMZzdReWZoaFV6aWMwM1UyV0FFZE5pMHJtMWJxK2trV1EyM2hwdWVUNytqMm9aMFV4cVBhelZnMjNHNGdwd1oyWEdsQWxPSXJSRkRYSmhvVVd0Q2I2SGtoc2ZPbjhraWxGeFA2bmRKRWM5WFhIcE94NkdlQnh3c0EzNXphQzF1ZXUvUGpkaW82MFFKTGNxMFJnS1hranVvOWlhaVFvNy92cDBlQXF2dHAiLCJtYWMiOiI3ZDNhMTU1NGU3ZjRkMjI4YmJjYzllNDhkOGYzMDBkYWU3MjgzN2Q0ZTBjODk1MDllMzQ3ZmM0NGJiYzdkY2E5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:56 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNVVXoydzV6RVFRMXNySkdpUnJ4Rnc9PSIsInZhbHVlIjoiYjJJSStYclUvSlR0UUwvbHQwY0tyUFMxWFRUSWlQRlFla2d6S1NFK2FTUUtNV3U3Si9idThQSzJuSjAvZ1RIVVVPYkFueVgwc3p2V1V4bVpPelZiR0ErMnZsUVlDM09oODgvVDdUYXorU0lERGo2dXZKTnAvRXNoTXZ5aUZHb0xTZjViT3VJYnZuOEVucElGRjVVM05wZFhHd2hwMW1hRHYxREF1SzVya2hJTktmVFpMMlBLbnZQRVdMa1g4M2F6WTA1eDlpVVQyWUQ4ek1SMU1veDcrd3cxeDh2d3lqMklHbE0rZjJJdUtMaVp2K1RNMmV6SHY1U2ZYL0V1WFZsVDgzVURjQjJVVnQybUlPTzR3N3RQbFVvdDIwOEdveE53K3VqSlRST29veDdmdmh6TjRaWmJxZEVkWVJaQTdHR25ackVSd2FBR3FJemVqZkJ6aUxNT3NIT3pCRnl0ZW44aFRTUk9BNENoKzZYZlVkWjBSNUt3Vk1SSXB2QkE0blVoRWdndFJxOHV3aWNJUGVmbUVtbitsb0NjTStwRWJHNFE1MXJPbGdPMXFpekJER1JEZzdrQi8wc0VtQU5HQVcyNkppRVcycWpqVWtrc2NLRHlDQ3JmWFZlYUVOVzF5ZVpaRTIyZXZOclo5UFlXLzNCb2dVQVB2eFY5cm9MMURadVciLCJtYWMiOiJlYWQ0YzA5ZWZmZGY2ZDZmNmYzZTM0ZWY3OTc1YTcxZWQ3NTNhNTdkYjNlZDJmOGFkNWI1YTMwNWYxMmNhYzQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:56 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikcrd3RxaHV0N1p5VG50OVY1azVPRmc9PSIsInZhbHVlIjoidGxDMGFOTlRzdDh5VVNRQ2N4Y25tNkFkdzhXTU1tUkhNRWVCSzhtZVk5S2NhNTA2WXVWWlViL0p0d1RWcHcvbjFCa2JIMFFSVkdOWGJYYWNSREhZdUVxaVpUVUprY0hrQzlEQkhxMmNKOWZlS3FJbHUxdFlmaUx3MVFvQkQ5WmRxOFNTbW96WXNwYnlOUWg4WTdmd0JSTHh3MXdTNkFhTkc4NEtmQkd2NXZLVG4rYUI0Z1Y1dGJEbEw1c2xLaTJvanBPMGJyWEZMZFltcjJMOE5mWWFrV2RScnRnTEVqRjdEbUg2aTZvaUd6SnVLZHR5alpBanp6MHpzTlpRWlV1WXZwV3E2QXM2eTJOcHl2REJ5UDdpeTRSSTEzalZZMnpQSUQ1U3BtUlVPL0haUzFaRERUN2d3TWQ1L3BRcjk1L21CZVpMajF0UWNtVDFTN2xQcnNkb1hZWFZMZzdReWZoaFV6aWMwM1UyV0FFZE5pMHJtMWJxK2trV1EyM2hwdWVUNytqMm9aMFV4cVBhelZnMjNHNGdwd1oyWEdsQWxPSXJSRkRYSmhvVVd0Q2I2SGtoc2ZPbjhraWxGeFA2bmRKRWM5WFhIcE94NkdlQnh3c0EzNXphQzF1ZXUvUGpkaW82MFFKTGNxMFJnS1hranVvOWlhaVFvNy92cDBlQXF2dHAiLCJtYWMiOiI3ZDNhMTU1NGU3ZjRkMjI4YmJjYzllNDhkOGYzMDBkYWU3MjgzN2Q0ZTBjODk1MDllMzQ3ZmM0NGJiYzdkY2E5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:56 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-943358265\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}