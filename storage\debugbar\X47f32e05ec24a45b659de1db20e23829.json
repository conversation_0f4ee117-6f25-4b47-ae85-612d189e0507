{"__meta": {"id": "X47f32e05ec24a45b659de1db20e23829", "datetime": "2025-06-27 02:33:41", "utime": **********.184371, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991620.821754, "end": **********.184385, "duration": 0.36263108253479004, "duration_str": "363ms", "measures": [{"label": "Booting", "start": 1750991620.821754, "relative_start": 0, "end": **********.140501, "relative_end": **********.140501, "duration": 0.31874704360961914, "duration_str": "319ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.140509, "relative_start": 0.3187549114227295, "end": **********.184386, "relative_end": 9.5367431640625e-07, "duration": 0.04387712478637695, "duration_str": "43.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45186992, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027300000000000002, "accumulated_duration_str": "2.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.165993, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.429}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.175554, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.429, "width_percent": 14.652}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.178231, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 86.081, "width_percent": 13.919}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:3 [\n  2299 => array:9 [\n    \"name\" => \"جالكسي كيك الكراميل 30جم\"\n    \"quantity\" => 5\n    \"price\" => \"2.99\"\n    \"id\" => \"2299\"\n    \"tax\" => 0\n    \"subtotal\" => 14.95\n    \"originalquantity\" => 7\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2300 => array:8 [\n    \"name\" => \"جالكسي كيك البندق 27جرام\"\n    \"quantity\" => 2\n    \"price\" => \"3.00\"\n    \"tax\" => 0\n    \"subtotal\" => 6.0\n    \"id\" => \"2300\"\n    \"originalquantity\" => 1\n    \"product_tax\" => \"-\"\n  ]\n  2285 => array:8 [\n    \"name\" => \"مكفيتز دايجستف بسكويت كريمة شوكولاته\"\n    \"quantity\" => 1\n    \"price\" => \"18.49\"\n    \"tax\" => 0\n    \"subtotal\" => 18.49\n    \"id\" => \"2285\"\n    \"originalquantity\" => 2\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1151041999 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151041999\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1341867575 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1341867575\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-339034620 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkVkODIyYUc1QkdsdWtRUldHb0hEeGc9PSIsInZhbHVlIjoiZUNpV3ppdVRvNW5sQ1AzbFNzNlBHb0hLc1J2ZEJuWVhhaXJCd2twVi9FTlFYb3JzTXczQnNQNitmZTVTN05odURnS3BPR1dtRlRpejhpMUJ5Y3BENUVtUmxjVVVmVlY5djBOVTA3TnpKZ0xRdEdqRkRyR1VXWkZOM3I3OFRtZFdIY25vQlBOYVY5QjhNU1g2cWx1L0RMZGFCeHNHM0RxTnNyUlkxMUFKRUpNZHdaOFVPZjRvLzhMNlVMeEd5RTBqMmlTaUhvZU43VGQ4SHErQ3dZMFpqdms4VWRxcTdZMEdpQnEvNW1Eb2w3bDdjWUYxekVNejlxY3VIazl6TktBbDdCRC9sNUVyVElPdURHQzBpVUlGNnBGSCt2K2JOQ0hpQUJFcjV0YVhxd3o3cmRFV0pVNFpocVlibDFyL3VFMExyZy9rdWVieGVLTzRrYXNBNDBKa0ZjOGJ0QWRFcUtydXFDUlBsQmpTZlFrN2EyU3dpcUZjWG91NlhoM2Vpd2hvWStHVjZlZVlFUGxQZ1dmNzV3ZDRMV09DZWMwbnlUNEhYSU1NajZ1eTJTa013SUE1R0Fod0oydktWb0N2NkhHajloM1JiSmtyNVVqM1doejJQalQvSVlqZlNpNFpSU1A3dUhuSTR0ditqUFFxRFl5ZENUd2NoSmg1VjFPaHVrZzciLCJtYWMiOiJlOTQwZmQ1ODlkY2MyOWFlNGQxYjU5MzIwNGFmZGZhOTFhNzU3YmFkNDI0ODYxZGNiYzU1YzU3NDQyM2Y2YTM3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFuNXR4eldqSk12eEhuSFZrczFlWXc9PSIsInZhbHVlIjoiNmtmZ0lIcFhWWCt6ZVJ0OFl5U29pY3B5aDVJMUJCYXQ1Sjl0NSszRjRMT1Yxc2hBQnlVeExuRHlKd3dmYTBQbDlOZ1JRSi85eGxlYTY1YVlTNTkwQzRKcDYzcnBreU5velZ3YnRpYmQyUzhxQ09BTnVTU0RBU2VOaEZUL25TdFQzYWVxbSt0S2dQcXdvK1BUOUJTenNFV3ErOXdqOFFtR1Y4VldTbmEzV3BrNU9EdW9iN0dxa3ZMUHlJeEZ3M0xaM2tkQjA4NEdYK0xtSFJLYXhiK0Fmenl4WUVyWldBSjFvYXpydmF3VTQvc2RIM3hBK2tmclpJOUpBWGhMMVVGM1R5dXY4YTY0clp0aHIxWTlBbTNxc1YvQm5Kakh0ck0rajRkMmQrSEI4a0planVrRHJMQUdQU0lqVjZqMW1ncFZ1QUVXVjIzc09PcFNmb091SFJKbk5DY2FHQnIvL2t4a1dyNEEvTjU3ankxQklibGJlNHFhVEQwYitRb0I1SmRyMVBxU1lzTGVoaUF5dGVOY0VGcE84bEJtY28rdlhlS1lHTko2d2JxczNyTm0vdjIrVjBxWkpyUjZKV2tWd1BXVk0xQW9XMjB2U2EvTVBnd1NXdXlsRzdYYjY4bklEektTci9ZckdhOXc1N3g1S3RGbEVaODYyU1BLR1UrZ0ovRDIiLCJtYWMiOiJkMjU2M2IwZjgwMzhmMWI3ZmUwM2I0MDFhM2NkZmU5ZjY4OTQzYTYxNTU3YTZmMjhkZjFkMmU0OWVkZDI3MTk3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-339034620\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-452988669 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452988669\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-958227791 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:33:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkVleHZTZEpTWXJTYklVMGFGbWpaMWc9PSIsInZhbHVlIjoiL2tPVy9acml3M050TllqMkdMM3hzTUxneWc1TDAyaHBaNzRHS1IrQ0tIb2IrYmwvN2hCd1AvNG01VkVTZlhoYUp6WWNZWlNXUDRkVDVuL1ZGVDRxUk1PMGdOSkF0c3RPNERCakZXRStDOTFWdTh5TGlRTWtDNlFmYWc3eWx2VUs4S1J2OHladURva0FWQnlQaG1ocVdXWGNDMllwZEgwRktpVFFiOUExQlNkVDlHNGtaUVJXMFlTcWdSMFp2QVhNeENXbi8xNE9Qb3pKM2dyL3FMemc2dUJjNWZ2Sk5JNE5vRk1rUHRmcHB4Q0RtZ0pHTXFFUGJqSVh4cXYrWW4xUmlWMkhwTFR3aW1lbkRBTUNWOG93cGFRZEw2U1VJdW5FS1pTRW9ERnE1VER1cm5yNXVrQkFDcmx1ZmlMbkpwQitETkNnaTd6YU83N2ErbWNLYitDcFNkanlRVVBFMGUwczBSVXFiYnBJNktsNWZhTUtTTFNPTTNGaU9CaTgvRWF5NkZjZ3Exc0pRMGNHNXNZOUxCbjRTd1RSaG5RQ3RGdENsVmk2NDR3TWRuZERkQzFGblA2RnY4NzdIb1BSMXh6eW9iMGpEc1lVTkpGTWZxa20veHZiOFY0MEMrZVdaSk5WWE8wQjhXYVFLRFBHUmxDVWJPQXVVTFBKS25BTXVXMFIiLCJtYWMiOiJhZDIxY2JhZWUzODY2NDMyNzJkYTYyM2U4YTFmZGJjMWJkOTU0NzBiN2I5NzdlMzQ1ZjdjMjlkNWIyNzlkNjc5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:33:41 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik1XU2QrUGRab2NpeEpaL09RMml3TEE9PSIsInZhbHVlIjoiSnVLZGZmNll5ZUU4a0RtRFYwR21oMk5DWWFGVnJST2NVeGpMdHY4SW9GTHpRVUF3dVM0NzVBSWQyTmhsY1FNMEs1U2w4aEJZM2FadFdTNjJtMHpUWUtaQVlVZnlTdmZKTWwzVFUrRzdNMGhNRHFqeml4T2ZuZ0ZTS2s5OTdjSTRUWnBkRnljWXFvTUlHL2k5LzNrdWYvTlVScytydXJuTUs1b2lEYWhsSDEyZWQ2TGFXY0dBTUxWNFdlQy9YNXB5TG5NU2VIbUtLNnpRbXM4eUlDT1lYcExhY0ZJbk11azJpQlpUK3JTemtZd2wrMGRCNkxmcmFwTHpOWkYxN1U5MUpCRGNQSFlmTWtQK2duS0Q4QmVDT3RaWFJzSkFGMUo1N1dIT21EcWloMzV6dk5sNncwNzhjTzdhTVNPMWo5UVRMRjg2dHcxT29tSVJhN2lFQWpER0hoQndnRUJKS3c0WDhhV2FqclB0VFJMRWcyYU4zQVBPRjhHNFJRRjZQbjJRMm5JYkx4elkyaEZ4QVg5UTg1ZFFyWE1tZTg4V3RkT0xDYUUzcGJyOGJhY1d3L1lzRnBnNUh6enR4aEV1TzF4ZGd3Q1dzQmZUWHVYZ3F1YVJmRi9xMDIvcUoraGpOQjFrbFlRSmNSRGUvWGdQUTVyRXBFZUxTTHZNQVZ1MFR4eUsiLCJtYWMiOiI3Y2VhYzE2ZDA3Mjc2MjRjNTk4OTgzMmM2MmQzMTY1NmEzYjQ0M2I2ZjI5ZjNiMDRjMDM2Zjk0NmFjZWE1YmRlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:33:41 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkVleHZTZEpTWXJTYklVMGFGbWpaMWc9PSIsInZhbHVlIjoiL2tPVy9acml3M050TllqMkdMM3hzTUxneWc1TDAyaHBaNzRHS1IrQ0tIb2IrYmwvN2hCd1AvNG01VkVTZlhoYUp6WWNZWlNXUDRkVDVuL1ZGVDRxUk1PMGdOSkF0c3RPNERCakZXRStDOTFWdTh5TGlRTWtDNlFmYWc3eWx2VUs4S1J2OHladURva0FWQnlQaG1ocVdXWGNDMllwZEgwRktpVFFiOUExQlNkVDlHNGtaUVJXMFlTcWdSMFp2QVhNeENXbi8xNE9Qb3pKM2dyL3FMemc2dUJjNWZ2Sk5JNE5vRk1rUHRmcHB4Q0RtZ0pHTXFFUGJqSVh4cXYrWW4xUmlWMkhwTFR3aW1lbkRBTUNWOG93cGFRZEw2U1VJdW5FS1pTRW9ERnE1VER1cm5yNXVrQkFDcmx1ZmlMbkpwQitETkNnaTd6YU83N2ErbWNLYitDcFNkanlRVVBFMGUwczBSVXFiYnBJNktsNWZhTUtTTFNPTTNGaU9CaTgvRWF5NkZjZ3Exc0pRMGNHNXNZOUxCbjRTd1RSaG5RQ3RGdENsVmk2NDR3TWRuZERkQzFGblA2RnY4NzdIb1BSMXh6eW9iMGpEc1lVTkpGTWZxa20veHZiOFY0MEMrZVdaSk5WWE8wQjhXYVFLRFBHUmxDVWJPQXVVTFBKS25BTXVXMFIiLCJtYWMiOiJhZDIxY2JhZWUzODY2NDMyNzJkYTYyM2U4YTFmZGJjMWJkOTU0NzBiN2I5NzdlMzQ1ZjdjMjlkNWIyNzlkNjc5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:33:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik1XU2QrUGRab2NpeEpaL09RMml3TEE9PSIsInZhbHVlIjoiSnVLZGZmNll5ZUU4a0RtRFYwR21oMk5DWWFGVnJST2NVeGpMdHY4SW9GTHpRVUF3dVM0NzVBSWQyTmhsY1FNMEs1U2w4aEJZM2FadFdTNjJtMHpUWUtaQVlVZnlTdmZKTWwzVFUrRzdNMGhNRHFqeml4T2ZuZ0ZTS2s5OTdjSTRUWnBkRnljWXFvTUlHL2k5LzNrdWYvTlVScytydXJuTUs1b2lEYWhsSDEyZWQ2TGFXY0dBTUxWNFdlQy9YNXB5TG5NU2VIbUtLNnpRbXM4eUlDT1lYcExhY0ZJbk11azJpQlpUK3JTemtZd2wrMGRCNkxmcmFwTHpOWkYxN1U5MUpCRGNQSFlmTWtQK2duS0Q4QmVDT3RaWFJzSkFGMUo1N1dIT21EcWloMzV6dk5sNncwNzhjTzdhTVNPMWo5UVRMRjg2dHcxT29tSVJhN2lFQWpER0hoQndnRUJKS3c0WDhhV2FqclB0VFJMRWcyYU4zQVBPRjhHNFJRRjZQbjJRMm5JYkx4elkyaEZ4QVg5UTg1ZFFyWE1tZTg4V3RkT0xDYUUzcGJyOGJhY1d3L1lzRnBnNUh6enR4aEV1TzF4ZGd3Q1dzQmZUWHVYZ3F1YVJmRi9xMDIvcUoraGpOQjFrbFlRSmNSRGUvWGdQUTVyRXBFZUxTTHZNQVZ1MFR4eUsiLCJtYWMiOiI3Y2VhYzE2ZDA3Mjc2MjRjNTk4OTgzMmM2MmQzMTY1NmEzYjQ0M2I2ZjI5ZjNiMDRjMDM2Zjk0NmFjZWE1YmRlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:33:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-958227791\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1022821258 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2299</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1603;&#1585;&#1575;&#1605;&#1610;&#1604; 30&#1580;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>5</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2.99</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2299</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>14.95</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>7</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2300</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1580;&#1575;&#1604;&#1603;&#1587;&#1610; &#1603;&#1610;&#1603; &#1575;&#1604;&#1576;&#1606;&#1583;&#1602; 27&#1580;&#1585;&#1575;&#1605;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">3.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>6.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2300</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n    <span class=sf-dump-key>2285</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"36 characters\">&#1605;&#1603;&#1601;&#1610;&#1578;&#1586; &#1583;&#1575;&#1610;&#1580;&#1587;&#1578;&#1601; &#1576;&#1587;&#1603;&#1608;&#1610;&#1578; &#1603;&#1585;&#1610;&#1605;&#1577; &#1588;&#1608;&#1603;&#1608;&#1604;&#1575;&#1578;&#1607;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">18.49</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>18.49</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2285</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>2</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1022821258\", {\"maxDepth\":0})</script>\n"}}