{"__meta": {"id": "X112684cc15bc5125d903d3435b4f27dd", "datetime": "2025-06-27 02:23:39", "utime": 1750991019.007012, "method": "GET", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.57982, "end": 1750991019.007029, "duration": 0.42720913887023926, "duration_str": "427ms", "measures": [{"label": "Booting", "start": **********.57982, "relative_start": 0, "end": **********.926925, "relative_end": **********.926925, "duration": 0.3471050262451172, "duration_str": "347ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.926934, "relative_start": 0.34711408615112305, "end": 1750991019.007031, "relative_end": 1.9073486328125e-06, "duration": 0.08009696006774902, "duration_str": "80.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 52226376, "peak_usage_str": "50MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.financial_record.opening-balance", "param_count": null, "params": [], "start": 1750991019.001512, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/financial_record/opening-balance.blade.phppos.financial_record.opening-balance", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Ffinancial_record%2Fopening-balance.blade.php&line=1", "ajax": false, "filename": "opening-balance.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.financial_record.opening-balance"}]}, "route": {"uri": "GET pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@index", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=33\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:33-84</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.0034700000000000004, "accumulated_duration_str": "3.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.960608, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 46.11}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.9703288, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 46.11, "width_percent": 12.104}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.973475, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:37", "source": "app/Http/Controllers/FinancialRecordController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=37", "ajax": false, "filename": "FinancialRecordController.php", "line": "37"}, "connection": "kdmkjkqknb", "start_percent": 58.213, "width_percent": 10.375}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.986461, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 68.588, "width_percent": 20.173}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.988447, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.761, "width_percent": 11.239}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 3, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1469326959 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469326959\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.991811, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-122564530 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-122564530\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.992937, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-2010183996 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010183996\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.993715, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-1441311384 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1441311384\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1942467025 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1942467025\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1544375130 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1544375130\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1222403514 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991017010%7C18%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImhFZVhoS2NoY2loWmZmSE5QUXVZWnc9PSIsInZhbHVlIjoiQjJxM3lFR0lTV2daZGJZZkI2NGFERnVkQzl4YmNYT2JOR21QSnd4OTlseVZDSTdhWGJtbThoK21oM0pHTk5PUTBxd3d5R2hHL0RUYksvVTlqcmtrMnJHZlI5eEFmU3EzVGVUQzB6eEpjVVg3d0ZENmcxRVBLejNCMlZLanhmdnZwL2lZQm1KQTBvNmFrWXB3SE5lTEVjZXZVczc0bDRSY05KaEdRdnc3UEs5VEJ0eHEzNVV4R2hwYmxSTDllbWhuRTV0YzN5YlcwQ3VyeUcydDFEenNQcXdwd0IrTHQ1ZXNyYzUwSllWTmw4ZTZKWXB2aEF4NFVaS01vd1hvM2JudUExckNJbDM5Y01WcCtES2k5WGhTcXFLbGlxMTc3eXpJZVdjT3lDcXlVdG1Ham9obzF2S28rVzJhcFlCODNEYjA2VDhVY2dqR0s2ZnhlU1hmS1UxVXByNStQc01uQjU2WnAxWE82TVFmMFBjVitCRHF1RDN3dmE1TGsvWkNTU3UvSnFEanVIcEQ4N0RtZm1ibk0wTFo4aXZCVjNKN2ZSd0tHYkQ0OVJQSWtYTHpDemJzajBKWGp6TWRoYit4aC9DQWQ1ckFBYWlWeEZsR0Uxb09jYTlFY0dBSWlvc2NUZ29USUNsQ1BYbytyMXhNdFM5TmVXbGt2elE5NzgvQXFRaXYiLCJtYWMiOiIzMzI3NGRkNDUwYjQwOWJlODVlNDI5ZTY3YzlkM2E5NDQwOTFkMGYxZjQzNDFhYzQ1ZjQ0NjRhNzNlNjY0YmFlIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImZ6TTB0UVpvN1FZRmNta2ZCR00yL3c9PSIsInZhbHVlIjoiME8rZTNoRlQzZkFqNGI5cU9GWngxUGFmUVdHalBBcFF0T0d1R2YrNmNHVE5oem15Z2NHQ3UyZFI3UDZVZXNCQUYvSHNyNmlrOEk5MGlpMmt3akw1VkpXZVU4QnR3KzZDdzQ5RnM2eSttYkZRTDVhVnFGTkN5MUROTzJzYXJHTXlYeGVHUFNmM0pSOVdsR0tydnpzZEExRlN0M2x1djdaN1hScWRXR2didnRXZ2V4N3U0VzMvK0JkT1NpOG5YUEhyVWp1R0hzYzZFcTNXWFJUM3dDM1JuK1BudnVRWW9TenlxbnNtVmhmK1pORytTcTdWNjJMeEZDWm1zMUxjUHJwTEp1WXVxSks3a1dTK0c5VnNXbVgxQTB6RmVTbnkyZjJnb3M1NUFIc3JBaGdyZlY0eVZWTS9JMUF5dHFPd1ZxOVhCM291TVhGdkZHMTJWUU9Mb2llbTVKMHA4RXA3aTlZZTd3OHFNendPQ1BSVlVJeW5PazV2WWhBVHhaWUUveHV2UXh5YzVCSTh6dk5RZ0E2bEU1TG1OL0Y5WDcrdXUxcmRMcGhjd3g4QVpHQzBjZXplTmZaQUZhRFBsNHUzdXZ0aGhGb1BYSy9vcWV2dkNVWSs5T3dhUkh3Q2lKUHZDbDRCR0MrTjZvYWFMNG4xVmw2QnRsVXg2OUhnUG54TEwrNXkiLCJtYWMiOiJmOTY0ZDAyNDQwMGZmZDg2NWE2ZmRjYzBhYjFiMmY1ZDkxZGM2MTVjZjkxNWFjZDAyMjYwYzM5NWYyNTcwMmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1222403514\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-79122137 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-79122137\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-621467876 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InI0REl0SHFhK1Ywc3Z4Q005eGhPUWc9PSIsInZhbHVlIjoiZjFoVUlNRElKQWltcndDVlozTXdNc3pPNDlUV1hTYm5nRklvSjZwU2VPSy8vakJQWkxGQVV5UlErakloM1RSTWtyYlFhWDlCWHNLRFFQKzQydVZwRjFtV2F2Wll0eUpYenRqbHhLUjlSR1RJUVVLaDZ3OVhPUkZZbmw0SUsyMW9JR05nSDBTQW51WWdZSlJXTklqTFcweno0dmpMVkFPYnQ1eDhQb2JqTzVTNDF5NmIrQTcyWjQ5RWRNakNZbmFHVlpNWEFGUEFKeE9MenNRRlcrcGV1Y0pBTmFyWnpTcklUbkJSZlNGaW1mQWxielFma2RaUHFPZnZZVmhJK2JzbXJiOHpCa0hnMmxpZk56ZzNiYkhlUWxuVnhvampLY1Qza1ZKK0dQWjA0UFk3UndVR2JsWURpeG9tZUxnZXIxTUN2ZUVqNkJ1dVFmV2c5Ymd3QUFuWFZaRSt4N0NIZTl6L2M4b0dod3dLWENDOVFTUHdPZkhXS2ZyOTgxaUNqU0lyT2JlaENXQWtadWZHYjA5TzBzTElJZ0xnbUdvbXVQYURqL2FCVm5OYzNrYVRwYlNOWERNYklhTTVxQ05uR2RjNE40OC9RbVNsSG1hNmd4N0ZzaGw5NGl4c1lyajZia3ZHMG9TbjB1NzFtRUQrTDVsT1VQTzFndElxMmtZYlJNOVAiLCJtYWMiOiIyMzcwZWYxYjc3YTk3ZjlhMjc1ZTBkZTc2NzIzYWQ3OTQyN2UwMzhkYmZmY2EwMTkxOGI5MzQyY2YzNzkzYTMyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjdQdk5jUjE3d2FMUlNJcllYc1Q3aEE9PSIsInZhbHVlIjoic2t0ZTdKZkVIdFNuSUdBVC96YjhqRzZPUmxZTjNDTml5TWtzeHRGT2l6Y0VRY3c3cTVyN3ZjTk03MExDVmFRd0RxRENRd2NnSnc3WVpnVmNtZ1htaSs3ODZkVlFrY3IzQS9MTUdOOXY1U1FQVi9FaE9xTFhWWDdpT2RuaURWVDIrSGhBa0R4MHJJSitOb3doWGNoQ2F5MlhSZGtaNVZlSDVFaU1odlFTTzFpWHpaTlJ1NTBJV2l4YWtSc2xDMHZFeksvZDkraTh2YUpKYk83L25rMXJiQ2lxSDNRY21IWXJlY2VSU0E4QmVkdXE4ZEc4VnMrVi80bWFmNDZwNVdDcEJvOU5ZU2pEcE5pbUZyUWtkWUZwYU41KzF0ZThGOXlJc3ZTNERKRXJNNmtQVWYzVTV3bUswdldpMmw0eWpOK0VsUXNDNm9PSlpFbFVvalJGaWlPelRzOERVcU10M3BGV3Jkem55SlRqYVk2aFRVcEhuNmVqK0ZEK2dMNWYxQ3ZEWlh3Z0FVUzU5UkRQM1RYNk82eHd5elJ5ZEpYellLRzlNSERBVjlzalNMV3Myc2Q5TEJYMzBiS29lSkUxRDczbUY4SjVIOUxITzZnUE9ZV0pqdDVkOTBIN2RveHd1WG0yQ0RUNGpWOURUNzdHbnZvV1U1WS9YbHVYcmNHOUFBSloiLCJtYWMiOiJhN2I4NjhkNWYxZTY4OWUxY2Y2NmMxYzQyYzMxY2NjNDNmMTFkNGFkYzMyOWM5ODkwMDNiOGY1NDczZDY0NTQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InI0REl0SHFhK1Ywc3Z4Q005eGhPUWc9PSIsInZhbHVlIjoiZjFoVUlNRElKQWltcndDVlozTXdNc3pPNDlUV1hTYm5nRklvSjZwU2VPSy8vakJQWkxGQVV5UlErakloM1RSTWtyYlFhWDlCWHNLRFFQKzQydVZwRjFtV2F2Wll0eUpYenRqbHhLUjlSR1RJUVVLaDZ3OVhPUkZZbmw0SUsyMW9JR05nSDBTQW51WWdZSlJXTklqTFcweno0dmpMVkFPYnQ1eDhQb2JqTzVTNDF5NmIrQTcyWjQ5RWRNakNZbmFHVlpNWEFGUEFKeE9MenNRRlcrcGV1Y0pBTmFyWnpTcklUbkJSZlNGaW1mQWxielFma2RaUHFPZnZZVmhJK2JzbXJiOHpCa0hnMmxpZk56ZzNiYkhlUWxuVnhvampLY1Qza1ZKK0dQWjA0UFk3UndVR2JsWURpeG9tZUxnZXIxTUN2ZUVqNkJ1dVFmV2c5Ymd3QUFuWFZaRSt4N0NIZTl6L2M4b0dod3dLWENDOVFTUHdPZkhXS2ZyOTgxaUNqU0lyT2JlaENXQWtadWZHYjA5TzBzTElJZ0xnbUdvbXVQYURqL2FCVm5OYzNrYVRwYlNOWERNYklhTTVxQ05uR2RjNE40OC9RbVNsSG1hNmd4N0ZzaGw5NGl4c1lyajZia3ZHMG9TbjB1NzFtRUQrTDVsT1VQTzFndElxMmtZYlJNOVAiLCJtYWMiOiIyMzcwZWYxYjc3YTk3ZjlhMjc1ZTBkZTc2NzIzYWQ3OTQyN2UwMzhkYmZmY2EwMTkxOGI5MzQyY2YzNzkzYTMyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjdQdk5jUjE3d2FMUlNJcllYc1Q3aEE9PSIsInZhbHVlIjoic2t0ZTdKZkVIdFNuSUdBVC96YjhqRzZPUmxZTjNDTml5TWtzeHRGT2l6Y0VRY3c3cTVyN3ZjTk03MExDVmFRd0RxRENRd2NnSnc3WVpnVmNtZ1htaSs3ODZkVlFrY3IzQS9MTUdOOXY1U1FQVi9FaE9xTFhWWDdpT2RuaURWVDIrSGhBa0R4MHJJSitOb3doWGNoQ2F5MlhSZGtaNVZlSDVFaU1odlFTTzFpWHpaTlJ1NTBJV2l4YWtSc2xDMHZFeksvZDkraTh2YUpKYk83L25rMXJiQ2lxSDNRY21IWXJlY2VSU0E4QmVkdXE4ZEc4VnMrVi80bWFmNDZwNVdDcEJvOU5ZU2pEcE5pbUZyUWtkWUZwYU41KzF0ZThGOXlJc3ZTNERKRXJNNmtQVWYzVTV3bUswdldpMmw0eWpOK0VsUXNDNm9PSlpFbFVvalJGaWlPelRzOERVcU10M3BGV3Jkem55SlRqYVk2aFRVcEhuNmVqK0ZEK2dMNWYxQ3ZEWlh3Z0FVUzU5UkRQM1RYNk82eHd5elJ5ZEpYellLRzlNSERBVjlzalNMV3Myc2Q5TEJYMzBiS29lSkUxRDczbUY4SjVIOUxITzZnUE9ZV0pqdDVkOTBIN2RveHd1WG0yQ0RUNGpWOURUNzdHbnZvV1U1WS9YbHVYcmNHOUFBSloiLCJtYWMiOiJhN2I4NjhkNWYxZTY4OWUxY2Y2NmMxYzQyYzMxY2NjNDNmMTFkNGFkYzMyOWM5ODkwMDNiOGY1NDczZDY0NTQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-621467876\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-62532260 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-62532260\", {\"maxDepth\":0})</script>\n"}}