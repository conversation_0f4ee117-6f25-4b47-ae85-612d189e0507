{"__meta": {"id": "X4a27256576963d01e337c2dcaa691129", "datetime": "2025-06-27 02:23:48", "utime": **********.514409, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.093242, "end": **********.514425, "duration": 0.42118310928344727, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.093242, "relative_start": 0, "end": **********.448094, "relative_end": **********.448094, "duration": 0.35485196113586426, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.448103, "relative_start": 0.3548610210418701, "end": **********.514426, "relative_end": 9.5367431640625e-07, "duration": 0.06632304191589355, "duration_str": "66.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45735016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01454, "accumulated_duration_str": "14.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4759278, "duration": 0.013210000000000001, "duration_str": "13.21ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.853}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.497782, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.853, "width_percent": 3.508}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5037842, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 94.36, "width_percent": 5.64}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1344414839 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1344414839\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-800632239 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-800632239\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1070001975 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1070001975\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-623258488 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991024947%7C20%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InE2Mm9HRUVROGdSbGpLalBnYTE1SVE9PSIsInZhbHVlIjoiWEdKaFFjRjhsRWdtVm5UYWx0ZWtFdm1DR3VzMXpMbzNhSWI3aWsrQ1FPUktIbi92YmxpcnhpY0wxWko4dGR4dkROQmt0aUtVemtuL1pIRGIxcGs4cWNISTEwaUVDQ1laNHYxTU5qbU9ySEYyUHY4c0EyWFV6NS9wWE01TjRqM3BNT3o4OU9Td05OOE5Eei93WGpJREdzTFExWkRaNkltVUNDU2FDRVFvSDh5KzhVbHRDTzZyOFlyOGtLdGpPY1kxa1dQRnBSRHhocytXQllXR09Gd3pvZ1ZDa2UxS2syenJNTnhidGZlUGF3Z3dSeUZ0K1dZK21wTWp1UFdhNkdnQXBIbUZmQlNxTkVTdEVIN0hhUmkwNk5iS3MvV3hqQWNyZHpMREJvQVVMcUFtV0t3YkRzTjQrazFoOGk3TEpmSTZHMHZEekR1bTRSb2Q0a2xPSDJxRjJtSHF4QWNCUEZZZlJhUE55c2h2Y3BPb2lZemNCaVllSThQd25jQ3d1UlV1VlZzU0hHNXVHUWkwRzh2dmYxYXBIcjMvWVVZWDRhRUhYN0Y0N28xRFczb1ljYTN1bkw3UWpiN01yazAxNDhtUVJNY3d4QWNCYzgwQVJaN2ZlblI3SlhaajR1ZGNIcEMxWnlweGJabWZXRGpnT2FLTmF2RE13U3NSLy9CaG1tVTAiLCJtYWMiOiJiNjNkYjEyY2Q2YTkwYWI1MDFlMTFmYzM5NGVhMmI4ZWRjMWYyYzBkZjhjMTgzMTBkYTU4ZGMzYzY5YTBkMjY1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVUQ0oxcVhkRktsQlhLd0R3L0MzeGc9PSIsInZhbHVlIjoidURLb25yZGpJYURXQWNlR214VW40WnBBUmU2eWFnWTQwajRqQWxXbXdVZUc0cXpyVEphZXpXZmM3VVl2RUNPOXZacVE3VTJYY1d5YVZhaFZITzlubXc3TS9hMTh6dXBTdFVRKys4b0RqTnhibkY3STY0SS93aFVPM3FJaldyc3FVekFYOE1HZEMzdlNEREp5c3RhR01ISzN5M3RIUTFkaDcxZE5RUXdrbDBKRzJwNWVRUnRpSU5sVnJIUEFmMlR3amliRXJPb0JtbGxTY3JjTHIyZzQ5M0Nra3RUV2FQd1NiOTkzWU96eGlxUzZIaGxLeU5BN2c2ZFExdmE4RTU1SktCQWNJTm5jZlIvUjNvODlUNUFUMWZmd0hseHhnNXpWN0hZeFU2MVY0OE1wVHd2VTl6YmM5cDhSRE9Uc0hRSERHZWNaSUE2ZUVmK205MDM3UXVvaHYvcXpabU5lcEdGbXNiUVBtNzdVOEdtOEROZHh4Q1VjYjE1M2dncVdiVTJJUUg2aldMVUp0T2hnZXA4M0M5Qk82cFJ6NEpOaFR4ZEUvV2Y5L2pEKzU2aEJTTVgzYzQwRnJDeU51Nmx5QkEzVEFObmE0NzBhVXRFZUtxcWo1QTVYM25hMTlwWUdqOGs3K0Z6Y1M4dzRmUmVDRkhtMEVvdDlZMGNMakpBWjhGbVEiLCJtYWMiOiJlNTAzMjJlOWU5MTgzZmZjN2UzNjRkZWJhNTI3NTY1OTUyYjY3YmJjNmVjODA2Zjg3NzI5MjYyY2EzNmJhZGE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-623258488\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-316878877 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-316878877\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1126005739 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdsSlZNQWtlSWR2YWhUM2NKZUpYd0E9PSIsInZhbHVlIjoiNVZqa09lQkRkZytXcCt1TU5QQkpxSUV5aGNQU1VYTFdiNSszcGhER1F2TXE3Vk5NaU1rb0tNdFZyYmJ6bWtncDkvUjRCN1FQZlhJc3krbmNWZjgrdWZ5ZDQrbDhscUY1cGQxdzlyWTh2cmFFWWRGMnVlcDJCSW12M1VLTFF5Nm5HTmMyWmF6cU5PUDdZYzdHbjVLbGlnNGpwczJ3anlwZUVVQmVSNGViNUx1TFdjR2JMRXlsWlArS3FZd2t2RTVsT0ovU0RVd0pjQWFUZmFGZWpuMzdhNHJac2xpL1JIYm5qeCttd01scTN2Q2x6YnpoRmFzZ3RtdUJ2bGMxdjZXcmtOc3JITytWMFdpdWxIbTBuOUFNbm0wcTNHa2drT25mWEZLUzFCcnhqaDQ2TDhORm1TUWFObzQvUTVyQ1dJODdSQW1tOWo5Um1lTnIyK21mZTdwZTRJWnlHTDgwVUFmcEcyMnlvMUFoU0FLa2hycDk2bngwTXVvMHZHU2E0MmxYdDRSa3ZKZEdKMFZSQjRpa3ZCQURvTkJRaS9leGVib3gwZjRRanJiMnFmV1hnbWpablU0akJrRU5ya3BVWjBHcWpWZDJydUkySXdlamNybVJEVWJRZDc1L1VxbStrOGFNL2cwUGpTRktScFVDODYwc3JFazBFZnZkUkRlcGpVZDUiLCJtYWMiOiI2OTMxZGNhYWQ3OTE0NzE5ZjIwMGJiZTA4OGJjMDM4Y2I0NzIxNTJiMWY5N2Y0YmUxODJmNTUxNjAxN2Y5OTE4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InB6YzNGUmpKQWNkRFlQOEgrM1ViNHc9PSIsInZhbHVlIjoiZ3BNaUx6OTJmVk8wY1M3bzRNMklhRUxGaFZmSWZ4UHc4dmt4bUtUNHAwNWJ6eCtZUzlNakVFb0srLytrekFqbWZ1MzM4bStmbTlyQ0xIYlMwWFhCSDAxZlA1cUNzQ3A2ZmhERml4VmdYVzhlMUNEYlpmU0VUU2wzQW9yWm5EZVdPQVhjSXl0NmVlQmZOWXhCcXlIL3dxMER2aHJXVnNBTEkzemc1VTRGRGtieHh0ZWQ3ZmcxN05EUW9FSHF0Q1hQWis1dmc4WitJWmhXd3d1dW56djF0ajFoUE5EVlRIU2VGRmszSlgxWDBqbGJ4c2lVb0ZYRSsxOVFxSklJYlZJSndxeUgxWXVwYUxwQWs2L3U2Qi9OVGpObTJocXNmZlIzTkgzTi90MUhhYVQ2TElZTzd3aE1ZSmVZTlEvSE1qeVM0clErRzFKMHFBUmVUR1RIUW1NNWVVVi9sRzkyVUNmRXNWejNnRW5RSzJ2d3hSVFRha2xaakhFaXJ3bUFTeXE5cU5CdTBnUEFuVmN4Z1UyREFNV3pvMTkrVGh5dDZmZmIwNUpRalh2ZzgvRlNiaHdqMTE0TlFjWkk5SytMSjA5MmVqSHRMTHRpUnlHTnY0Yk9yNHdnS0UrU29jYVA4YTFkbll4S0cyMm9DRG5qRmhyMWNXYTdtM3gwSDVrZzF4S1oiLCJtYWMiOiI4MDcxMjBhM2UxOGVkMTdkYWUzZjU3NWZlNTBkMDk1YzcxNmQ1OWU4ODU4OWMwMWM1NWM5OGNkMmY0NTI0NGU2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdsSlZNQWtlSWR2YWhUM2NKZUpYd0E9PSIsInZhbHVlIjoiNVZqa09lQkRkZytXcCt1TU5QQkpxSUV5aGNQU1VYTFdiNSszcGhER1F2TXE3Vk5NaU1rb0tNdFZyYmJ6bWtncDkvUjRCN1FQZlhJc3krbmNWZjgrdWZ5ZDQrbDhscUY1cGQxdzlyWTh2cmFFWWRGMnVlcDJCSW12M1VLTFF5Nm5HTmMyWmF6cU5PUDdZYzdHbjVLbGlnNGpwczJ3anlwZUVVQmVSNGViNUx1TFdjR2JMRXlsWlArS3FZd2t2RTVsT0ovU0RVd0pjQWFUZmFGZWpuMzdhNHJac2xpL1JIYm5qeCttd01scTN2Q2x6YnpoRmFzZ3RtdUJ2bGMxdjZXcmtOc3JITytWMFdpdWxIbTBuOUFNbm0wcTNHa2drT25mWEZLUzFCcnhqaDQ2TDhORm1TUWFObzQvUTVyQ1dJODdSQW1tOWo5Um1lTnIyK21mZTdwZTRJWnlHTDgwVUFmcEcyMnlvMUFoU0FLa2hycDk2bngwTXVvMHZHU2E0MmxYdDRSa3ZKZEdKMFZSQjRpa3ZCQURvTkJRaS9leGVib3gwZjRRanJiMnFmV1hnbWpablU0akJrRU5ya3BVWjBHcWpWZDJydUkySXdlamNybVJEVWJRZDc1L1VxbStrOGFNL2cwUGpTRktScFVDODYwc3JFazBFZnZkUkRlcGpVZDUiLCJtYWMiOiI2OTMxZGNhYWQ3OTE0NzE5ZjIwMGJiZTA4OGJjMDM4Y2I0NzIxNTJiMWY5N2Y0YmUxODJmNTUxNjAxN2Y5OTE4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InB6YzNGUmpKQWNkRFlQOEgrM1ViNHc9PSIsInZhbHVlIjoiZ3BNaUx6OTJmVk8wY1M3bzRNMklhRUxGaFZmSWZ4UHc4dmt4bUtUNHAwNWJ6eCtZUzlNakVFb0srLytrekFqbWZ1MzM4bStmbTlyQ0xIYlMwWFhCSDAxZlA1cUNzQ3A2ZmhERml4VmdYVzhlMUNEYlpmU0VUU2wzQW9yWm5EZVdPQVhjSXl0NmVlQmZOWXhCcXlIL3dxMER2aHJXVnNBTEkzemc1VTRGRGtieHh0ZWQ3ZmcxN05EUW9FSHF0Q1hQWis1dmc4WitJWmhXd3d1dW56djF0ajFoUE5EVlRIU2VGRmszSlgxWDBqbGJ4c2lVb0ZYRSsxOVFxSklJYlZJSndxeUgxWXVwYUxwQWs2L3U2Qi9OVGpObTJocXNmZlIzTkgzTi90MUhhYVQ2TElZTzd3aE1ZSmVZTlEvSE1qeVM0clErRzFKMHFBUmVUR1RIUW1NNWVVVi9sRzkyVUNmRXNWejNnRW5RSzJ2d3hSVFRha2xaakhFaXJ3bUFTeXE5cU5CdTBnUEFuVmN4Z1UyREFNV3pvMTkrVGh5dDZmZmIwNUpRalh2ZzgvRlNiaHdqMTE0TlFjWkk5SytMSjA5MmVqSHRMTHRpUnlHTnY0Yk9yNHdnS0UrU29jYVA4YTFkbll4S0cyMm9DRG5qRmhyMWNXYTdtM3gwSDVrZzF4S1oiLCJtYWMiOiI4MDcxMjBhM2UxOGVkMTdkYWUzZjU3NWZlNTBkMDk1YzcxNmQ1OWU4ODU4OWMwMWM1NWM5OGNkMmY0NTI0NGU2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1126005739\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-73368630 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-73368630\", {\"maxDepth\":0})</script>\n"}}