{"__meta": {"id": "Xac8b87d9880e79ec47dac2f887b503a6", "datetime": "2025-06-27 01:26:23", "utime": **********.472682, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.024854, "end": **********.472696, "duration": 0.4478421211242676, "duration_str": "448ms", "measures": [{"label": "Booting", "start": **********.024854, "relative_start": 0, "end": **********.419365, "relative_end": **********.419365, "duration": 0.39451098442077637, "duration_str": "395ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.419375, "relative_start": 0.39452099800109863, "end": **********.472698, "relative_end": 1.9073486328125e-06, "duration": 0.05332303047180176, "duration_str": "53.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45140232, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00336, "accumulated_duration_str": "3.36ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 341}, {"index": 21, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 26}], "start": **********.453206, "duration": 0.0028, "duration_str": "2.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 83.333}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.4650478, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 83.333, "width_percent": 16.667}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-2135122072 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2135122072\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1315402142 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1315402142\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1263915007 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1263915007\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-936458688 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; XSRF-TOKEN=eyJpdiI6IjA5dW5UeW52aS90ZEF2Y0Y2SDgrcmc9PSIsInZhbHVlIjoiZGdQV0NsOHI1MmU4cXljMGVVM2J2aCs2eVVNUE9INnFiNndlem5JUjhiMkROaVBHNEZhSjFwZHA4YUJ3WGtRZHlQT0szT09zYllaUlRENzdYdFErQWg1aEZOVG8xY3pQSHAwSUphUGtCUmxCV1B6RE9BUDlPSWhoRUY3SEludU92RENLZm9wRzd6cXFTZ2gwYjlGOTV6MzdIaENqZ0cybGQyZDRqaWoxU0daY2ptNDlDWTdtWUtPM0pNVGlGL2N2VXlUWm5EeGxnSXgvaUR4RitubjFrdWdzU0I1NWFaaHgrc1Z5OXNtMjFpU0FQd1dqVWY4WFNHanJocDBLM0ZnTWlwTS9nOHVnd3hBQkNnQlpsY3BERDdkaXRqZHJoV1UyRGRheERCNjV1SGkvajQ1N3JlQ21Bamg0RkVzM3d2QVlwSXhtczhHQkJnSkdmOWZsV2JUSnRwSzFiQ21KdU9oRnN2TXVnNHEyV0NEdXc0VGs3eWRFYlJRRnZUYzhjc2VENG9LSUgwUUJlaUY5dzNnNXo1Q1AxMEFFcDN5dkx0MEZ5RXdTT1JWTmp1eXE5bkQzQm41Qk90NWd3MWFwdGdxQjNsa0NPUjFBUU1BMjZxYnE1R0dYWDM2OHkwQXJDRkxXaGQ0NEw3bE5BcUFyMEJrRmI1aXZWVVZ4bDlycVMyKzIiLCJtYWMiOiI1YTA3ODg1NzNkZGI0ZDY4ZTJmYjlmMDQ4MzZiMjNjNmYyZDhiZmIwZTFhNzQxZjhkOWRhZDdkYTE1YTFmNTE1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBCNVRRYytKTnZYTzJvWXZ2YXBJVnc9PSIsInZhbHVlIjoiR1RrYmh6T0l1a0hhQzUyOHpNN2xPazJTWThIdDNSY1ZPaEl4c3QvK01uZVA0elQ2WTEzdjJuTXJtQTdKQVllazI3dTUvSytDSklzUDFCejd6Nk4zT2x0MWxaSU0vY2ZEYUNLeDUrRDkvcWFRNGJJTXEwUmdsdFF3TWpsYWdDTlR1OWR3bUZQQ3N0SktwSXJMaFROUm4xL1FrOEFqeXlFWUowKzlBTUVXaytJdXNFZW1Ub0RrLzJXOXdGQnlQUmNlYXZjSVVRSkxvVCtPVGp3ajFWNU5GdjJDQVFZR05ZZXBHakppUlRxeUJTNURlbUJpNnQ0UHYzdXI2SDNYY3gzeExGMGN6SEc5OFVYTHJEZVdyYWsybEVLYVRFNi9oVGNoQzdRVmtZRWNMVUpGQ1lrbUpiam1hZDZtcHRRRkRLUXdZTFFHWVZMSFpRU012bjVkaHdXSVA4T3NZc3RlS3o4aXJzWU1TcmthQUhFSWZYT3ZUY0ZyRE5HTFZ6S0x6TGthckx6ZVpBV09JQ3NGSjE0cjZMLzAyVXJJU3cvbzhxaCt6Mm5JMjZKRkx6MWJWcEFPZSt6M0JzZVk4bzhubFl3SXBSaUJRYk5LUWlzc2RrSGJxT2hXeFlPZnIrbUZWelZ2WGNVSEdaSjdkMjRST0JwVWZ2cmdHcFhvcnozdnp0cEciLCJtYWMiOiI2OWZkYzEzYmQ1YzYzMDlhYjQxOTI2NmE5ZTc0YjZjZWVmMWY5MGE2ZmYzZWJhMDg4OTJhNmNkYTRkMjMxOWVhIiwidGFnIjoiIn0%3D; _clsk=8maz9g%7C1750987476327%7C94%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-936458688\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-568949240 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-568949240\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2111928116 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:26:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik5tcVR0NW5vTHNVRGM1QjNHdTBOQ1E9PSIsInZhbHVlIjoiaHJ4ZnQ5SEVqaXlUNStNVk1uaDJmSmoyY3NnZ1lTMCtadFl0cjByeU5ZVFJRNkxuVGY2R1dVN0c5NnlZQ3J0SDk2N0RIV0JHR08xNXlUaDVxQ2FPeC9td1dhcTVaRkhibjhxMjVsNlVTeHpWNzVibmRtYm9CbGRVT0VvOFFLdjAzbUdtWTdmM1Z2YTJsV0UwZVJZRjJuNm5PYzhDMlNzaGJYQ0V4R0N4M0hPS29DQlpnU21xeTV6NER5WHNFaFlSYXdSNDhaYzk3TzdwdlRxUjdhd1ljOFZmTG1HZWZHcmZoT1BjZDUwTS9zY05jUGQ2Q0dUb1hiWXlUQmYrZ254bmdNQUNyZGNpWG5ZZU1CSGVOckhqVW1tTXhrN0NGZkZmdHdEQnZJd00xQmVOdkhFcDdLOUI2QWlONzVoNW41WHo3M2VrYkpxT2VHSExtL3pEY0sxd3NMeTJtUEVIQ0IyRVhNbytXTm5Jc3Zib2tleDI3T1kxMGl2cEMzcW4yaFVmSnhTUStoRjlFbzljbFZQZDFVcm9tWjdJbE9acHVibVJXb0w0UEdzMXFaRGhNZ0tUL1JOVTB2MjlZcjdBcy8xKzFnT0Y0bmI4NkEwcGowajNJaTAvT3lpR0JmY1N6UGovM09FaURIZlZsM0FwSzY3NStpNHArZDhCb1hiWnBobGciLCJtYWMiOiIyYmNhODdjMzIwZGVjMWZiMTYxYmVlNDU1ZDUxZThjOGQxNmFjMTZjZTA0MWQ3MjQ3MjY5NjFmYzg3NzY2MzkzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:26:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D; expires=Fri, 27 Jun 2025 03:26:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik5tcVR0NW5vTHNVRGM1QjNHdTBOQ1E9PSIsInZhbHVlIjoiaHJ4ZnQ5SEVqaXlUNStNVk1uaDJmSmoyY3NnZ1lTMCtadFl0cjByeU5ZVFJRNkxuVGY2R1dVN0c5NnlZQ3J0SDk2N0RIV0JHR08xNXlUaDVxQ2FPeC9td1dhcTVaRkhibjhxMjVsNlVTeHpWNzVibmRtYm9CbGRVT0VvOFFLdjAzbUdtWTdmM1Z2YTJsV0UwZVJZRjJuNm5PYzhDMlNzaGJYQ0V4R0N4M0hPS29DQlpnU21xeTV6NER5WHNFaFlSYXdSNDhaYzk3TzdwdlRxUjdhd1ljOFZmTG1HZWZHcmZoT1BjZDUwTS9zY05jUGQ2Q0dUb1hiWXlUQmYrZ254bmdNQUNyZGNpWG5ZZU1CSGVOckhqVW1tTXhrN0NGZkZmdHdEQnZJd00xQmVOdkhFcDdLOUI2QWlONzVoNW41WHo3M2VrYkpxT2VHSExtL3pEY0sxd3NMeTJtUEVIQ0IyRVhNbytXTm5Jc3Zib2tleDI3T1kxMGl2cEMzcW4yaFVmSnhTUStoRjlFbzljbFZQZDFVcm9tWjdJbE9acHVibVJXb0w0UEdzMXFaRGhNZ0tUL1JOVTB2MjlZcjdBcy8xKzFnT0Y0bmI4NkEwcGowajNJaTAvT3lpR0JmY1N6UGovM09FaURIZlZsM0FwSzY3NStpNHArZDhCb1hiWnBobGciLCJtYWMiOiIyYmNhODdjMzIwZGVjMWZiMTYxYmVlNDU1ZDUxZThjOGQxNmFjMTZjZTA0MWQ3MjQ3MjY5NjFmYzg3NzY2MzkzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:26:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D; expires=Fri, 27-Jun-2025 03:26:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111928116\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1802556633 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802556633\", {\"maxDepth\":0})</script>\n"}}