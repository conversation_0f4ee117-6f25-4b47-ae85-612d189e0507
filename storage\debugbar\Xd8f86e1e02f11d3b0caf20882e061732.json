{"__meta": {"id": "Xd8f86e1e02f11d3b0caf20882e061732", "datetime": "2025-06-27 01:15:05", "utime": **********.801518, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.363376, "end": **********.801536, "duration": 0.4381601810455322, "duration_str": "438ms", "measures": [{"label": "Booting", "start": **********.363376, "relative_start": 0, "end": **********.733829, "relative_end": **********.733829, "duration": 0.3704531192779541, "duration_str": "370ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.733846, "relative_start": 0.3704700469970703, "end": **********.801538, "relative_end": 1.9073486328125e-06, "duration": 0.06769204139709473, "duration_str": "67.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029632, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01304, "accumulated_duration_str": "13.04ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7620392, "duration": 0.011859999999999999, "duration_str": "11.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 90.951}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.785478, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 90.951, "width_percent": 5.061}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.7931361, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.012, "width_percent": 3.988}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2031126981 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2031126981\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1293510721 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1293510721\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1740282675 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1740282675\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/vender/eyJpdiI6IlVvSkhPR0UzbVFOQlZjM3ZnU2lYNFE9PSIsInZhbHVlIjoiMnhjV1h6Q01KR2VNWFdTZmtuVzIyQT09IiwibWFjIjoiMjM2N2RiZDkwNTA1Y2FhMzkwOWZjM2QxZGRjZDdkOGU5ZmJiYjU5MGUxMTBjMjY2ODI3YzU1Mzk3OWZkMjljYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986903260%7C90%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjF3VnBrUUd0SHVYMkxDSEJ2OGhMV1E9PSIsInZhbHVlIjoiWDZnSHR6Skx0TGVDcmZCSUhaU2FYRjNUMEtZclQvWUV1Smd3S2JCeURnSjJPNW1YT283REZmdXJoSGRudGhtVWtwOHpiYTIxVmFGT2ZHaCtSWGNPeVlSa2tJVWZMWXRsejQ4c2x6aDJvYlBjeEs5K0srN2FXd2lyZFJMOXhVMk0yOHZUZXFHcFZ3Sm5VVFpLV3VDMjVNZ0t4N1Zab1FoTGhRdG9UaGwxTXNFM1lSZUFEVTBnb3FoSlVaUWxKdVhiMWVsa3NUR05PcSt6eERSSFd2aVZUS3IzcCtDblR0WVRBdzVDM29xdWVUaDlWMlB6V2NXSFNpN2lZZFNQMWRJQXAremczYzU2RkVHamZ6blBFL1NRQ0VPUUxFbEYxUHJxRTcvT3p2R1A3YWZmU0UycDFjMksyNFRJNDEvdHhSdFV2NGY5SkRZbVNycG95UnBlMnJPdVpvbG1uUkhsL3UrNUpUVDRrbnhub2ZhRDNwbm41d091TlJzSWVuQXJXOERMRFgwQzdOS3VEL0hUdEpnd0JJeEVIcWhHZDVPVElSRGZ2bnhOUmFLb0VrNTRXMzFLeTlnWjdPVldRbGJPeEpaOUdLSWFnNlhNaGQ1VncwUXBwejdSYTFiZ25saUwzYmJtR2tqemkzQmFPMnFVZGxVcDFIQ1JTYWNtd3FvZ0x3UVciLCJtYWMiOiI3NzAxMTE3NjU5MzVkOWFkMDg5Y2ExZjU5YmM1MThkZjgxNTVmMWFkMTlmNjQ1YjVlOTgxOTljNDZiZDY1MGJkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJTT2taaGZOZ29Oa1YrdmFDNUg0dVE9PSIsInZhbHVlIjoieHhIL3ZobkNGUE90dHlDdTVUcWw4U2VLZWlDWndUTE5QaWFvUTgrdlhRdGtXZ0pCQjJVUEtLcXBYQTJqb01yWjF1dWZEU3ZiMlowS0h1WmpUOGtJRVc0SkI1a3FTbUVxOVVMUGd5UjJwQ0k3MzEydmJWYW85Y3pTT1BCU3UzQWd0aVlBU0toMVlZcEpkeDNsdHVpUGJlVmp6SUxmai9VRWNtdVhydmIrQTF5ZVR0c01jNm5mUzJHU3V1NVJETk5TNldod2RMSEFwaGhxVUM1TGFBQjlkMkJ6M0wrcjNuMnEzd0Qycm1qSTE1aU9FbzhqNGFJd3dzZGtZUGJ4Ulc3ZUw5S3UxaGdWS1MyVFp3bXB6VHVKVFRxSnVkWDI2SlZOdVhtZGxaOEtWUEo5REJwN2VzMTl3bUZRNFJ4cnFXdlJVS1JPUlVsZ1htUFpDcHdZMDZkd1BFZ3NZOHE4STNQMGJMRlNkdWx1WXQ4eFFrazJyK2dmeU5sdGNkTkFMYnA2UVhPbmdwaXdlUzhXeDhSMW9vYit3RUcwQkVrMm5vV2lCRGdhV3FYM0x2blI5d0RwYklDTTg0cjN2MHlsd2lCb2ptTGhJTEUyNE4zaEhYVmVPejBPbzZPdTdaRU1GeEtQUVQrMTNvUFZmOENQb1VtUkZGd0N4WjduSTZOYTV5QXYiLCJtYWMiOiI0ODBmODM4M2I4Y2YzNzBhZTA2ZGFmYmU5YWRjNjU5MzI1YWI1MThmMGVhNDU2OGQxYmVmYTM0YjQzM2NkNTM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-921534237 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-921534237\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1923754753 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:15:05 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlgzcXpuTXhwK3ViRHNYZkx0OXl2dGc9PSIsInZhbHVlIjoid2FpTVliWTBPdUdlMUUvTzh3ektIMUI1eXNwOVMrVzZPV3lhUUdxQ1kxdVlkOWVoVTJmWWQ3ekxqVUN6c0wyVGpiWTNWWFJpN29WNUpUZTUvNVlQVy9JdloremxnRzlmVG1LaFk2cm5IWW9aa0tUREllMTByeEJlNGRpVUgrN1hqQ2Q5RFNidStaLzZoOEJQa1pwdklLZlJnZEpPeWFtZWpGUGRNNVVoWnVOMGdZaWE4d2NTU0Z0SWRUUDEwdHNXOG13dmdXRkFOaDhaQ0hRQkIxbmxlTjRVOUd5RktSSC9Pb241Nk9zU0RXSWJKWTAvaXE5WjNyZjNhRmJWTjFRc3IwWkw3V2d3OGNmcTl0TFRsVUxaUmpNanVHRjZDbmw2aU9STFY5dkZTZWV0VUZ6R0VxQ1BveGt2YmcrYkk2S1pjQk5KTHNrUEgvK3BaVDlCSlFyVTVPV3RNcWloMitWd25FUnRCUVBDYS9tL29QMFBEYVY0c3pKSGxOOXc3TTd6cU45M0hZQXErR2xjWTZEUGlySkUydkhPNWRNQ0gyZmZ5YldaaWZqaENXdGNobnVuQzEvTmdmNlBibDcyN1ZZZ1FnWjlMZXJ6NjgrMXNrcy9MSWZrQTlLTUd6eHg2eXNvb2xXbmRpUG0ycVZJWXpzT0M0dGdxNUlid3FYN0FCYkUiLCJtYWMiOiJmZTVmMWYxOWMwOTM5NjIzY2I3YTllNThjYzgxYmQzNWQxYmU4NzM3YTY4MzMyZGE4NGZiMjI5ZGJkMmNiNGIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:05 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImpYUzRvbklSL1lGQ29VSXdldU1OWHc9PSIsInZhbHVlIjoieGZLS3ZvakF6aHFNWkQ0RzBLRWFWYytZU2hYTDZOejI0NlBHSmxFVjlCNnRLMFEvWFVtTFpPUUd2UUhMRlVxRW45dHBSb2RpMU1tTWJXRm9PYTNvbWgvaXFMQWxBMzJUKzRmSzFNbzNWb2JWc3dmaUlPRjRPbExGVnozaUVaaFVPSFJzZzZ2Yk4yNEl3VWlObzhURUdjYzhpdUt6T0srYjVCR0Q2Qll2MFN3ZUFlQmxIejVyRDJoRHZPRzYwT3lYaC9QV2EyTS8yNFM1UU82WWhSeDBhbDBCdDdjeTI5d0xad2tYcC9vYldubnJTdk5Nek5uTEM4MFdyWWt3eGE4VFFuMnZYWXA2YVUycmVjK3E0T1NqdW9yWVdmYVc4dCtoTk01bjdKckZ3aUE1RE5RekhpY3p0WjcxQzE4V3hMQXhDUXp5UmFNd3ZYaFVZWDVIRGUxdDd1RG5YOXQ4c0hURnJmVWswQXhIUHQrQlNLbjBUTFZwQkd2U0JpMUtZc05zMlEyN053T05HYXNsRzJzWnUyVVc2elpaK2Q2bVZ3REVaWTFyUFI2Um1QblNVK05uV254YzFWMm1OZlk3dVgvczRmeFdUbEdpNjkrblU0amd1U25mdGh4elQ1MkRUbnFqallnMXJweVI2TnY0SlNadDVDemR2ek1lNmhvamJDOVUiLCJtYWMiOiI0Y2MzZWNhMTAxY2RkY2FjZWZjOGRkZmZiZGEzNGE2NDBmODNjNzc4MGE1YjY3NTQzZjBjOGZhZTlkNDBiYTdmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:05 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlgzcXpuTXhwK3ViRHNYZkx0OXl2dGc9PSIsInZhbHVlIjoid2FpTVliWTBPdUdlMUUvTzh3ektIMUI1eXNwOVMrVzZPV3lhUUdxQ1kxdVlkOWVoVTJmWWQ3ekxqVUN6c0wyVGpiWTNWWFJpN29WNUpUZTUvNVlQVy9JdloremxnRzlmVG1LaFk2cm5IWW9aa0tUREllMTByeEJlNGRpVUgrN1hqQ2Q5RFNidStaLzZoOEJQa1pwdklLZlJnZEpPeWFtZWpGUGRNNVVoWnVOMGdZaWE4d2NTU0Z0SWRUUDEwdHNXOG13dmdXRkFOaDhaQ0hRQkIxbmxlTjRVOUd5RktSSC9Pb241Nk9zU0RXSWJKWTAvaXE5WjNyZjNhRmJWTjFRc3IwWkw3V2d3OGNmcTl0TFRsVUxaUmpNanVHRjZDbmw2aU9STFY5dkZTZWV0VUZ6R0VxQ1BveGt2YmcrYkk2S1pjQk5KTHNrUEgvK3BaVDlCSlFyVTVPV3RNcWloMitWd25FUnRCUVBDYS9tL29QMFBEYVY0c3pKSGxOOXc3TTd6cU45M0hZQXErR2xjWTZEUGlySkUydkhPNWRNQ0gyZmZ5YldaaWZqaENXdGNobnVuQzEvTmdmNlBibDcyN1ZZZ1FnWjlMZXJ6NjgrMXNrcy9MSWZrQTlLTUd6eHg2eXNvb2xXbmRpUG0ycVZJWXpzT0M0dGdxNUlid3FYN0FCYkUiLCJtYWMiOiJmZTVmMWYxOWMwOTM5NjIzY2I3YTllNThjYzgxYmQzNWQxYmU4NzM3YTY4MzMyZGE4NGZiMjI5ZGJkMmNiNGIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:05 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImpYUzRvbklSL1lGQ29VSXdldU1OWHc9PSIsInZhbHVlIjoieGZLS3ZvakF6aHFNWkQ0RzBLRWFWYytZU2hYTDZOejI0NlBHSmxFVjlCNnRLMFEvWFVtTFpPUUd2UUhMRlVxRW45dHBSb2RpMU1tTWJXRm9PYTNvbWgvaXFMQWxBMzJUKzRmSzFNbzNWb2JWc3dmaUlPRjRPbExGVnozaUVaaFVPSFJzZzZ2Yk4yNEl3VWlObzhURUdjYzhpdUt6T0srYjVCR0Q2Qll2MFN3ZUFlQmxIejVyRDJoRHZPRzYwT3lYaC9QV2EyTS8yNFM1UU82WWhSeDBhbDBCdDdjeTI5d0xad2tYcC9vYldubnJTdk5Nek5uTEM4MFdyWWt3eGE4VFFuMnZYWXA2YVUycmVjK3E0T1NqdW9yWVdmYVc4dCtoTk01bjdKckZ3aUE1RE5RekhpY3p0WjcxQzE4V3hMQXhDUXp5UmFNd3ZYaFVZWDVIRGUxdDd1RG5YOXQ4c0hURnJmVWswQXhIUHQrQlNLbjBUTFZwQkd2U0JpMUtZc05zMlEyN053T05HYXNsRzJzWnUyVVc2elpaK2Q2bVZ3REVaWTFyUFI2Um1QblNVK05uV254YzFWMm1OZlk3dVgvczRmeFdUbEdpNjkrblU0amd1U25mdGh4elQ1MkRUbnFqallnMXJweVI2TnY0SlNadDVDemR2ek1lNmhvamJDOVUiLCJtYWMiOiI0Y2MzZWNhMTAxY2RkY2FjZWZjOGRkZmZiZGEzNGE2NDBmODNjNzc4MGE1YjY3NTQzZjBjOGZhZTlkNDBiYTdmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:05 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923754753\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}