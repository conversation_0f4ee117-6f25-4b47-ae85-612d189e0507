{"__meta": {"id": "X7315ed403be01deb391e44aac4089939", "datetime": "2025-06-27 02:34:31", "utime": **********.273834, "method": "GET", "uri": "/payment-voucher/22/confirm", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991670.877276, "end": **********.273848, "duration": 0.3965721130371094, "duration_str": "397ms", "measures": [{"label": "Booting", "start": 1750991670.877276, "relative_start": 0, "end": **********.220673, "relative_end": **********.220673, "duration": 0.3433971405029297, "duration_str": "343ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.220682, "relative_start": 0.34340596199035645, "end": **********.273849, "relative_end": 9.5367431640625e-07, "duration": 0.053167104721069336, "duration_str": "53.17ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46710608, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x voucher.payment.popup", "param_count": null, "params": [], "start": **********.269923, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/voucher/payment/popup.blade.phpvoucher.payment.popup", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvoucher%2Fpayment%2Fpopup.blade.php&line=1", "ajax": false, "filename": "popup.blade.php", "line": "?"}, "render_count": 1, "name_original": "voucher.payment.popup"}]}, "route": {"uri": "GET payment-voucher/{id}/confirm", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@showConfirmVoucher", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.confirm.show", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=146\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:146-149</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.00205, "accumulated_duration_str": "2.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.252492, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 80.488}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.262205, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 80.488, "width_percent": 19.512}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/22\"\n]"}, "request": {"path_info": "/payment-voucher/22/confirm", "status_code": "<pre class=sf-dump id=sf-dump-1581002893 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1581002893\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-245558416 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-245558416\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-858216308 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-858216308\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-350974946 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991666882%7C43%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZvU1I1K3QyOUlFTmtRMmxwZUt2OXc9PSIsInZhbHVlIjoiWWJiR1BrMTErVXBDWE9YV0U0Sm9uWTMrQ1dGTUd0RG5FTmR5ZHEvdVZ2MGtPVHY5S2FNRlNFSTUrS3gydDM0aFg1bHFyVkxKWk5sWXRpcHpmTnB1ZW1RR1JPb3VEME9abS9jY09vYzBkNHZva2N5OVpqR1ZyakREYnZ2UDNBcnpVV1c2dHRQSjRkcTFLNkswZzYwd2h4eXlsWGJodXMyemNBeWwwQnhWalExa0ZyQ3B0cEdSbnZaM3hYYU80dlplVmRZSlp0M3B1WVVlb0JoYzhRUSs0TXBsSXF2bDV6TmdqUjVCb0V4ZEFqMGR6R1ptVUs5Nm1ucUYxMHBIVHpHRHBXSURnVEhXcjYxSXk0dVh2alhjQk9xOHlJWG1sVHFZY3JRUVN4eDRqYXNYOU5NQkovVnNxMlJzMUYyNjNVSmhZSERlN1lnbHVtZHc4MUlUSW9QUzFUenZTMXJkb0ZMV3hzVXc4TllJcktnanR6UlF5bC9GNm5XNGFsdEhvekRRcGlhWndDNlZlZUJtcTFHK1JjNFU5VFZtRGM4cHZBSmNxYXorcVplUlp0b1dFSEFMWGdaVDBhdUxJUHZ3VDdhRGd4UDZ3M0EzVVBkWFpHZFptWThrV3AwQy9HV1JSRVhGN2piSGdHWGcwUEJTMDdFK0dENGN1VVIyTjNJOTlCMysiLCJtYWMiOiJhMDIwOGRiZDlkNTI4ZjgzZTEwOWE4NjQ4NzhkMmE2N2MwZWNmNjc4ZmIzM2EwMDYzZDExMTMxNmI3MGVkYzlhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFqTzBjVDY1MkErNk15RXgwMkhJVnc9PSIsInZhbHVlIjoiTDJXcTRXckNVTGhodGhnYWlLd0M1R3RhLzJrMEFINHVtYzFybCtEdHRHcGkvOEZNdEhlQTJmbXdCeVpZTHZNaFZoNnRmQi9SRVl4V2E4K3RNWE1zeVA2aG1nMzZSa29CWnNoZStMYjBrV0RtRUFjaXFuV0RyMVNTdEQwVDJiNHVMallEUkpwL2FFeFEzaldqeWV4NXFvMVhvWWJMZ1orR3orQjJWaWNpM2pHNjVsSHNHWGVERmgrSEwxUXk2ckJLSEF6YlNWV2sxa2I1MTJBd25hK2hIc0tOcHZrRGZzNHFKS09ydkZsa2JCN3RKTFlqNUVtUjZTeFV0STV5RFM0elVyZWR1SXlGK1pnQWlKbzlkbHppQVBGRmtKa0N4MU1rdnhRRkNVL0IwbkFmamQ4anVvTC9IT0RMM2t4c3lFVFVqS0Zuc05RUG45YTF4ZWNPeHA2YzhZMGNqbEVNMm01d3h2Z3U2YklNV2hQb3dzMGgxTm1ndlR0N2RjTVFldFBXVTNKQVdvM3Q1ZHJRcW94UjE4Mm5GTkxrRDVJYUhza0tCcWloOVdjdzVhamY0bThFVy9IQ0VwMEx0OW05SHRwbURxT1EyelV1aVBKRkRUZkZEMG9qeWZMN2ZyVTFYY2MvZzc4cjdRT01hdWxvOFJXT2dZV0d1WDhkSEVoanphTmciLCJtYWMiOiJjZmI5NTE0ZGI2OTQ4NDlhYzE4ZjIxZjdiODU1OTdhMzBiMzdiZDJhNGFkZTM3ZjgzN2UxZjM5YzAxODQ0NTQ3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-350974946\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-738793596 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-738793596\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1054569188 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IktGdDZneHhub1FsZ1lZR2VIWERNY2c9PSIsInZhbHVlIjoibEdKdE1ZcVZzK3JEaEpqWmtTNWpzNWsrZzcySXVvc1lkOVBHMVdidGQ5clF3em9IT0pQanRWdzFhbmYyd1hQdXZjSDVFVVI1WjFvVGhZa0g4cGRHNzdOZ3RJcENpQ09CeFhiNVNYeEV0dnZ2bTJWbXdvdlhIdUdKZmtOc3RFU0JsbVVNN3RFcGp4UjZZdlFiWm9ySytTam9KT2h1T3Ntd1pZWUs2Q2Ewelh3cmRDQnNwRWxoVkc1UzZTV3hiL3c2UWREQ1lOaEVuZlpvTmRkTVpJd25RK1g3Q1NKVEFjU1paQmZ3WTdDcWplcFdHbWNBV1hDbXpyTy9ud2lXdW1aNVhFdlE5ZDYzWU5rZ2JVMlUyTlM4VW5WN3FkVVpiT2JsalBzU2RtMXhKeSsyRmo4UDluSk5PRkx6N2s4a3QvMUVZNnZpVUxHcVhoYVV5UDBJclZGVndMcllEYjZsNWNrVWduOEZlSGNmbHpESXN6ZXBBWXEzQmkwWmdONzR3U0xncko5QmI4VTkyVHF3TjZRMGdzcUFXZS82ZmY5cldRdU5wWnQzS0xldDJZVGY1aW1tREJnQjVJRWxManU1NktORnNMTTlxdXNPazVSOVRXVmVUL1ExM2J4cGxTRFNNcnpBV1UzTWp1dW8wQXVyUDNKWVJxS1dyMnpiWENLTWwvSHciLCJtYWMiOiJkOThkOGIyNmQ5MzIxMTMzMjRhMTlkYmMzNTU5ZWI2YWEzMDM1ZWJiYzIyMjUzOTRmZTBiM2QyYmRmYjA4M2Y4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikt5dDdUUG5MaXdJaGFHb2Z5OXhhbUE9PSIsInZhbHVlIjoiVmJjVFE4MVdCaklDQkE2Qmg2S0JIUkFHVHM1VFBzQkxvcytLWkZrZ0svcWtZWkwrSi9Ta3g3MjI0YUxvYzNFdk11SjBhZmF3ZjRGTzROK1NwcE1VL04vRzZlaVltbzFWWFRZczA0dElBTDgxdGFodWZ2SUxDVndhVW9PTlV4aHJrRksyUjlPbFluVkNNd2RDWFVTK2h2U0xjK2VTdU91TEhQTEdpbTRiOU5mOEh4dEpUNlZFMUlRU2FnbUxwNlhNTDBUL0k2SnZweEtVTHUvbEw5QU1iOVRBbXNKMFl1dm9RZUdLcGFQbVp5dExzSUFmc1BIT3FZNE81TnNvRlZSZDVhaEpJSEtKOTRyRG91aTZad1YxUFpYcTd3ZWtwRjdWcTdobFpyYXdhMDEzWlFVU3psUFl3ZTE5MWFJTVdodis3ZEZOQ2ZiM2xEZ2lFN1JyNS82REV4VFRLelUrUFpDTWhDaUpyMWdObFIxK3k0MWRhUlFzbkFuTHVXcm5sSFNmeGcvVVk4SW4rQmVBSUdjWjRNaTJMMy83emM5bUlRb3M2NTZyQk1BS2V3a2MvYUlOcFBrbE9BTVlSb3lXTEhranJXZDZiYklTRHBralhpTTk2dDhybjM4emt0VkV6eStFYWxWcWVoQXBnUFE3MVRjZ1F5Y2JMaUhVZ1JrZThMUGkiLCJtYWMiOiIwZTdmN2E2OTA5MmYzMTQ1YjU4MGQ0NmI3YWI1OWE1YjU4MGNjMGNiNjkyNWM5Zjc3ODE1YzU0ODdjOTM4ZjIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IktGdDZneHhub1FsZ1lZR2VIWERNY2c9PSIsInZhbHVlIjoibEdKdE1ZcVZzK3JEaEpqWmtTNWpzNWsrZzcySXVvc1lkOVBHMVdidGQ5clF3em9IT0pQanRWdzFhbmYyd1hQdXZjSDVFVVI1WjFvVGhZa0g4cGRHNzdOZ3RJcENpQ09CeFhiNVNYeEV0dnZ2bTJWbXdvdlhIdUdKZmtOc3RFU0JsbVVNN3RFcGp4UjZZdlFiWm9ySytTam9KT2h1T3Ntd1pZWUs2Q2Ewelh3cmRDQnNwRWxoVkc1UzZTV3hiL3c2UWREQ1lOaEVuZlpvTmRkTVpJd25RK1g3Q1NKVEFjU1paQmZ3WTdDcWplcFdHbWNBV1hDbXpyTy9ud2lXdW1aNVhFdlE5ZDYzWU5rZ2JVMlUyTlM4VW5WN3FkVVpiT2JsalBzU2RtMXhKeSsyRmo4UDluSk5PRkx6N2s4a3QvMUVZNnZpVUxHcVhoYVV5UDBJclZGVndMcllEYjZsNWNrVWduOEZlSGNmbHpESXN6ZXBBWXEzQmkwWmdONzR3U0xncko5QmI4VTkyVHF3TjZRMGdzcUFXZS82ZmY5cldRdU5wWnQzS0xldDJZVGY1aW1tREJnQjVJRWxManU1NktORnNMTTlxdXNPazVSOVRXVmVUL1ExM2J4cGxTRFNNcnpBV1UzTWp1dW8wQXVyUDNKWVJxS1dyMnpiWENLTWwvSHciLCJtYWMiOiJkOThkOGIyNmQ5MzIxMTMzMjRhMTlkYmMzNTU5ZWI2YWEzMDM1ZWJiYzIyMjUzOTRmZTBiM2QyYmRmYjA4M2Y4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikt5dDdUUG5MaXdJaGFHb2Z5OXhhbUE9PSIsInZhbHVlIjoiVmJjVFE4MVdCaklDQkE2Qmg2S0JIUkFHVHM1VFBzQkxvcytLWkZrZ0svcWtZWkwrSi9Ta3g3MjI0YUxvYzNFdk11SjBhZmF3ZjRGTzROK1NwcE1VL04vRzZlaVltbzFWWFRZczA0dElBTDgxdGFodWZ2SUxDVndhVW9PTlV4aHJrRksyUjlPbFluVkNNd2RDWFVTK2h2U0xjK2VTdU91TEhQTEdpbTRiOU5mOEh4dEpUNlZFMUlRU2FnbUxwNlhNTDBUL0k2SnZweEtVTHUvbEw5QU1iOVRBbXNKMFl1dm9RZUdLcGFQbVp5dExzSUFmc1BIT3FZNE81TnNvRlZSZDVhaEpJSEtKOTRyRG91aTZad1YxUFpYcTd3ZWtwRjdWcTdobFpyYXdhMDEzWlFVU3psUFl3ZTE5MWFJTVdodis3ZEZOQ2ZiM2xEZ2lFN1JyNS82REV4VFRLelUrUFpDTWhDaUpyMWdObFIxK3k0MWRhUlFzbkFuTHVXcm5sSFNmeGcvVVk4SW4rQmVBSUdjWjRNaTJMMy83emM5bUlRb3M2NTZyQk1BS2V3a2MvYUlOcFBrbE9BTVlSb3lXTEhranJXZDZiYklTRHBralhpTTk2dDhybjM4emt0VkV6eStFYWxWcWVoQXBnUFE3MVRjZ1F5Y2JMaUhVZ1JrZThMUGkiLCJtYWMiOiIwZTdmN2E2OTA5MmYzMTQ1YjU4MGQ0NmI3YWI1OWE1YjU4MGNjMGNiNjkyNWM5Zjc3ODE1YzU0ODdjOTM4ZjIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054569188\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1977022363 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/22</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1977022363\", {\"maxDepth\":0})</script>\n"}}