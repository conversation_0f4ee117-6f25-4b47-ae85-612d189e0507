{"__meta": {"id": "X6a51e1578d2efbfeaacc81ca103524a7", "datetime": "2025-06-27 02:12:18", "utime": **********.628111, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.103342, "end": **********.628127, "duration": 0.524785041809082, "duration_str": "525ms", "measures": [{"label": "Booting", "start": **********.103342, "relative_start": 0, "end": **********.544188, "relative_end": **********.544188, "duration": 0.44084596633911133, "duration_str": "441ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.544196, "relative_start": 0.4408538341522217, "end": **********.628128, "relative_end": 9.5367431640625e-07, "duration": 0.08393216133117676, "duration_str": "83.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722600, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0036099999999999995, "accumulated_duration_str": "3.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.594172, "duration": 0.00232, "duration_str": "2.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 64.266}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.610409, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 64.266, "width_percent": 16.066}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.61809, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.332, "width_percent": 19.668}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-442830403 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-442830403\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1478867787 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1478867787\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1715533671 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1715533671\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-910858731 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990332098%7C1%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkNUMUxHQmJhMFAvdlFmck5UNmZFd0E9PSIsInZhbHVlIjoiWThERkJaRkpZc21Ib3hmb1ZBbmJQMnZTOTcwSytYZkc0MERqb3puVGlzWkdYM0hxNnk1SXRQMm93V1YzNWZaU3J6WitJc0FjZHFBYmg1czlLWktBakpJc01MeHhUMTNHYWg4ZkZnSjBINWhBb1BJc25LVk8vS3Y1N1QzVHU1TGx1cWsrYUZibmd6cTluMkJubTI1V3JpOU5oL1VJTTMzNytLQnkxZVZOV3pSMytXNkh6RGRWVElRaDZKbi9EUW9mdFpJd0hwQkZVME5kbU4vcEViNzNhYkJleFdZQlVZT1hkdW5YZ3ZtaWxEb0xsNDlxUWU4ZlBQK0g4WTBoaWFUK2FFZ2c4Wm5ldlJ3VSs2R0d0d2Q4aDlvVzZXNkorMDRPK1pKZXh2OFhKWW1pWm0yUWdjWlN5dmFMLzBZSnZaYXhmVDJQYnZrZUg3bXpFcFEvcm44MlJRUGRSTW42TVcxczlpYWJlUkc0VnQ0YXNXVXlsSGZVU3ZvdXRuRWNUNlEvbEJPcHQ3UEh2WHEvMk9EczQ2bHB3dXc2VEtreGYvUzVwNnUraE5xdzJLS2l4d0EvSEd2eVBOdG5qMGN1VHk2UVB5OUduMlBkMGxNTEN2UmdsSW9hMUJoYkc5V2Z6RkZBT3FxbGt5WWFDNjlLV0lTcGd4eVJxSkF4MmhrbzVqWGwiLCJtYWMiOiIzZmVkM2U0YjgxOTYxYTYwNDBmMDAzMjcwOTY0MmYxOWY5OTEzYmY2YjI2ZTczYzAyZGZkNjg4MjJjZTBjMWE3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJIYjdKSHh1dkdObjJvMkxVQ1diUnc9PSIsInZhbHVlIjoieHBKT2ZwVHZvdVhNRlVSWkJGS2RxaE0xUzZvSEIzdkg5aTVaZmdRQzk5ZkNlV3ZyZEsxR1Q5RndZYWlsa3JqeTBoYTgxeDJ4SWkrd3R2VUZwWW40bTBnZmgrNWRoVWlmUVdTb21uSThwT0xnOTB1MUFrRjdKMHkzeFFZOUVJZS9WS1RJRG55V28xc3krTFRRMXNtNmhvTWZMckVJakpvMmxOMDhrWC90aVp2UDJxYXFxZ2MrZnFPckFLWThVZ0FhRVAvYzl3L1BkbTBHK1BvQUU5cjFCNG1wQjN1WmsxUDZIeDd1YUV2WE8zME5Qa0I3ZEdGRmNzWVlGc0drb21oejB1NEh6K0FlR0Vrd1FPTUE0RWtqbTI3VEpFRU9lTllwZTlLWFFZdk4wNGF3RTlGZElVdHgyejlRVkRGYlJpNlJSbDZuQ3l2QzA2Q2U0STh0RU9pTGloSnVSdVhINXp1Q1J5VUVJcWRIU1dZcHhqZEsxdmF6Ymhob3RPUktjcm13OC94UHFKdHdOQjhxYVRxMW8zekNDUnRyRnMxQjdaZGpnd0U1TkRtYTVnV1NRUUtjaWk4MmdsUHdqeVVzdTk1SGVmMFNMNXM5a0p4RStUWXhwV0QwWGpoK3JsMFhMRjJxbk8vZnR3aDhRWGJaMVRYbnk2SWRudXI3WkxvSEEyeFciLCJtYWMiOiI4NWYxZWFhYTg0ODJlMmZlODdmOTUzYjVjNDg0ODJiNWJkZWQyNTMzZDlkN2U4YjA2OGY1YTU3N2Y4ZmJhOTRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-910858731\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1165283167 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1165283167\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-954504993 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1YQlJKY3R1c1JPcHhSZGwyTmtmSnc9PSIsInZhbHVlIjoic3RJZnJIVFJxVmlPdmlFcTI2RUYxZC9COUozcTk3Y2kyV1NBcmE5L2IwM0I3a0ZrK3lDVTRpdVJ6ckhVdVdZU3ZVRzdMVVRreXZTQ2IxeDlUYlJJb2NKNUVLSWlWY09GNnQ0dHZwcUxUYnVuUE1rUkFWNEVMS2Yzb2FEMk5qV1dDdHlONDRtWmF6S1Rsb1QvMy84Ym9jc1ZjaHZ1eFB4eTlLZnJRVHZMcTg1RjRJbFFpUkxSaE0yWjhycC9TcnVxNUdkUlRLUGMyaUsyeVJDczZjZUFvQnlwVUZNb0srODdkd3hHVHpkYWtCQVo0cTF2R2duVEQyRCs1dk5IWGlpcmNPK1R0Y1NBbGhNN3ZyOHhJNGxCM0tYL3RQVFV6eVZOR21xKzJ3MCt4V0Z6R1o3TGoydzBWR1gxc1BEa3V6QmlOV0ttdWNac1hRYUJLWGxRUkVmS3FJcVR0WXp1Nmg2MkNRY0Z6TFlZR1dVSEtnTVY1dTFWc1dNYUZsZUFBeWlPdnBYUFlPbnpLU3JwWElrNHRsNFRPd05wOFZ3ck1xQnBiQWN5VUZxUlpiMGV4MjZNT3NrU3doVTRtT1VtajBpdEZ6WEJpQVFnYllDcUFQTUJIUHdWRGNETS9WU0JvcWN0OXd2VHozL0hZWU5pZ0VaOC9UQjlzQkRQaFBZcGhJM0IiLCJtYWMiOiJlNTY5MmQzZGU2ZWNkYmM5OGI1ZmJjODM2ZmRlYWNjMjMyOTc2YmY4ZGU5OTliM2IxZDk2MGU2YmYzOTVhNDhjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:18 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImlqSHFoejhyc1l0K2tSOU54K1lNN2c9PSIsInZhbHVlIjoiUEt1N2w5cU5BMnNJMUJkaGFqdVlzN3dmZDIxbVZOdzFKOTk0RllqeWNKd2EvSnRORWtLTEdJelZzcU9DTWtNZnV1S3RqWTNQdzUwM0l6bmJza2hhRFZkWXJ6clFabjVMNmdxUGg2bTlGRS84Tk9KeUVROUpqaTNuRWNJdWs0U0svNEJrYTEycXV0SllSTmVTSU9PSGNlUG1XTWoyemVuWC9QTURtSEdNcjh6ZFU0RzR0ZmNuN1lnMDJVK1pRdnpaejg5dXRFc0hZQ1FLQXlpUWR3eEFRb3lMS3lscjFTd2p2VkhaTVdSeDVFYURZeWdXejVNRHJZcDk5MTB0WGtNeDhHRXM4ZDVXT1NwaC84NS9Ma1hkNUJZR2YraWtoQTkxemhvVjh5dWNuaURneHRweXl6WUd1ZkpWQmhSSW9nNllDc0tueDJVYTZRdnlLaWhsMUxsZ1FDV1lvTzl6aTNJOGlNWDNReG5MdkhnTHNiUFVtOFUvd2xock8rMkY1am1Fa1o4RVN0cnZUK1lZVHJaOVFMRHZCakkydmlsVWl5WU8zMTNoaVF5R2hvZ1ZQY2RBbno3bEtJMUR3b2o3VGlwd1hRaU5EZFdDSXdqN2JnenM2c2Z0RFRld0JpVFV3RGYxMFFHeEt5b2hUTmFCbGVLY1A1KzhhUGF6VHV0ZGRlTngiLCJtYWMiOiJjNDIyNDM5NzM4N2IzZTJjNzkzODY5YTQxZjQ3MzZmMDJkNzZhYTExMmI0ZmFhZGQ2NjhjY2Y3NDEzZTkzNTIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:18 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1YQlJKY3R1c1JPcHhSZGwyTmtmSnc9PSIsInZhbHVlIjoic3RJZnJIVFJxVmlPdmlFcTI2RUYxZC9COUozcTk3Y2kyV1NBcmE5L2IwM0I3a0ZrK3lDVTRpdVJ6ckhVdVdZU3ZVRzdMVVRreXZTQ2IxeDlUYlJJb2NKNUVLSWlWY09GNnQ0dHZwcUxUYnVuUE1rUkFWNEVMS2Yzb2FEMk5qV1dDdHlONDRtWmF6S1Rsb1QvMy84Ym9jc1ZjaHZ1eFB4eTlLZnJRVHZMcTg1RjRJbFFpUkxSaE0yWjhycC9TcnVxNUdkUlRLUGMyaUsyeVJDczZjZUFvQnlwVUZNb0srODdkd3hHVHpkYWtCQVo0cTF2R2duVEQyRCs1dk5IWGlpcmNPK1R0Y1NBbGhNN3ZyOHhJNGxCM0tYL3RQVFV6eVZOR21xKzJ3MCt4V0Z6R1o3TGoydzBWR1gxc1BEa3V6QmlOV0ttdWNac1hRYUJLWGxRUkVmS3FJcVR0WXp1Nmg2MkNRY0Z6TFlZR1dVSEtnTVY1dTFWc1dNYUZsZUFBeWlPdnBYUFlPbnpLU3JwWElrNHRsNFRPd05wOFZ3ck1xQnBiQWN5VUZxUlpiMGV4MjZNT3NrU3doVTRtT1VtajBpdEZ6WEJpQVFnYllDcUFQTUJIUHdWRGNETS9WU0JvcWN0OXd2VHozL0hZWU5pZ0VaOC9UQjlzQkRQaFBZcGhJM0IiLCJtYWMiOiJlNTY5MmQzZGU2ZWNkYmM5OGI1ZmJjODM2ZmRlYWNjMjMyOTc2YmY4ZGU5OTliM2IxZDk2MGU2YmYzOTVhNDhjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImlqSHFoejhyc1l0K2tSOU54K1lNN2c9PSIsInZhbHVlIjoiUEt1N2w5cU5BMnNJMUJkaGFqdVlzN3dmZDIxbVZOdzFKOTk0RllqeWNKd2EvSnRORWtLTEdJelZzcU9DTWtNZnV1S3RqWTNQdzUwM0l6bmJza2hhRFZkWXJ6clFabjVMNmdxUGg2bTlGRS84Tk9KeUVROUpqaTNuRWNJdWs0U0svNEJrYTEycXV0SllSTmVTSU9PSGNlUG1XTWoyemVuWC9QTURtSEdNcjh6ZFU0RzR0ZmNuN1lnMDJVK1pRdnpaejg5dXRFc0hZQ1FLQXlpUWR3eEFRb3lMS3lscjFTd2p2VkhaTVdSeDVFYURZeWdXejVNRHJZcDk5MTB0WGtNeDhHRXM4ZDVXT1NwaC84NS9Ma1hkNUJZR2YraWtoQTkxemhvVjh5dWNuaURneHRweXl6WUd1ZkpWQmhSSW9nNllDc0tueDJVYTZRdnlLaWhsMUxsZ1FDV1lvTzl6aTNJOGlNWDNReG5MdkhnTHNiUFVtOFUvd2xock8rMkY1am1Fa1o4RVN0cnZUK1lZVHJaOVFMRHZCakkydmlsVWl5WU8zMTNoaVF5R2hvZ1ZQY2RBbno3bEtJMUR3b2o3VGlwd1hRaU5EZFdDSXdqN2JnenM2c2Z0RFRld0JpVFV3RGYxMFFHeEt5b2hUTmFCbGVLY1A1KzhhUGF6VHV0ZGRlTngiLCJtYWMiOiJjNDIyNDM5NzM4N2IzZTJjNzkzODY5YTQxZjQ3MzZmMDJkNzZhYTExMmI0ZmFhZGQ2NjhjY2Y3NDEzZTkzNTIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-954504993\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1167420873 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1167420873\", {\"maxDepth\":0})</script>\n"}}