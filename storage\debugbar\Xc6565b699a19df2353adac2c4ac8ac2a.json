{"__meta": {"id": "Xc6565b699a19df2353adac2c4ac8ac2a", "datetime": "2025-06-27 02:34:06", "utime": **********.413313, "method": "GET", "uri": "/payment-voucher/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.006507, "end": **********.413325, "duration": 0.406818151473999, "duration_str": "407ms", "measures": [{"label": "Booting", "start": **********.006507, "relative_start": 0, "end": **********.335401, "relative_end": **********.335401, "duration": 0.32889413833618164, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.335409, "relative_start": 0.328902006149292, "end": **********.413327, "relative_end": 1.9073486328125e-06, "duration": 0.07791805267333984, "duration_str": "77.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46721968, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x voucher.payment.create", "param_count": null, "params": [], "start": **********.409261, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/voucher/payment/create.blade.phpvoucher.payment.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvoucher%2Fpayment%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "voucher.payment.create"}]}, "route": {"uri": "GET payment-voucher/create", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@create", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.create", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=58\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:58-63</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025830000000000002, "accumulated_duration_str": "25.83ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.366731, "duration": 0.025160000000000002, "duration_str": "25.16ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.406}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.399889, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.406, "width_percent": 1.432}, {"sql": "select * from `users` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 61}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4019852, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:61", "source": "app/Http/Controllers/PaymentVoucherController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=61", "ajax": false, "filename": "PaymentVoucherController.php", "line": "61"}, "connection": "kdmkjkqknb", "start_percent": 98.839, "width_percent": 1.161}]}, "models": {"data": {"App\\Models\\User": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]"}, "request": {"path_info": "/payment-voucher/create", "status_code": "<pre class=sf-dump id=sf-dump-1887904112 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1887904112\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1906533764 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1906533764\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-281574563 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-281574563\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1658156824 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991642374%7C40%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ild1dnFpc2V3bFIyZXdEZUU1N1V4MUE9PSIsInZhbHVlIjoidlBCclBvUFpuTVNVRmhCTnc1K3RNU3lQTjBHcE0xUlk4T3pjem5FTVZublhOQkxQRnMwbUZEcUw2MktsWDVwWWNhV3pzR05ZNldKK0lIREdBZDBXUDlMU3ZhRy81bkZTOGFWNEpCL0lNdi8rMWplSVNUNEFFRHlzQ2pycm1CMDJZS2hGZS9sekN6Y2RvQXRNSlpWMVBUeWRHQ3FrUkR0RHBJdmxZREpweTVxYkxld1piZUd0cE1TTi81SmRQdGZYcGhtRzJ3Z0svVlgreUw1dnZtd29hZWFUUkdYbVZzTTZmWHR5bmgwQ05ZV09oa3owODFRRVJrVUo0S2pWV2FvYWp1TlFsZkJPRFYvdDltMnBBeFVwMldDMFJOV3VzdzA4TUdjbld3UlIyc2s2UG00R2J6OW01ZEduOWdSOWtXbzk4ZVkrU1pYWDU4UDBCWU1FdGZsWjNBcnFmeEIxR2JNWmxjdENEakl5OVplbGExRHhRSjBDY244SFdzR3g5Z2liM0Rpb09ENExDNnJJTlc3L21XWlJMZGZhYzZTNTE0aU5qV1BxcHRpbnFyeXZKK2d6ZmEyME5VM04rU0dxU25DVzdrV001VG5DYVYrMmIvOGFqOStkOFpXSGZjMWVERmRrbGtQVTd3ZmlCWWVuTDdRQ1Z4QVhJK09ZWTEzQjdMMDEiLCJtYWMiOiI4ZDQwMmExNTgwMjFlNTgxYmE1OGNiYzUxNzM0MTE4ODM0ODgzMDFiNTk4ZmM3ZDY5MGNkZjVhNGY2NWUyYTg4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZmdXN2Z2ZGaEh1RTgwQy9WbnB3ZVE9PSIsInZhbHVlIjoiYUJkZU5LYWFjeSsxZWxtZ0FCR1B3RG1HNWhlME9xaWNkVDZoOFBjb3pGVEMwYytUbXA4VWNmaFlIMkVzVisvKzN5OGQ1SUdWZnorbm9JaFlDc2xURVVsU25zR1hPR09DY1V3dU5zcDEvM25hSGk5WG9TeGg1dGl1d1hyMFFGYmxZNVhXWnlOcEU0QWpNSkc1a0xYeFJveDlremRKeDV3VzNYeFRGTkNDeXM4c0xxQkFGdk1QMld1QXAxZTlFcWhsNm5hSGRFdGRjV0tCNGxpeTU3T0hvOEh3VW4xTFFGaGUwWUxVSUFwai9nUDZXc2I1RFpBYWFJamZxNmlvQ0pnQzk3YXVBcEZtak41UmhqRXVXNThhSFhoSDlyS2ZFcVczZE8yYzl4enNUYy9sL1lSS2JCL0JTZjcwSHhCQ3RudnAreXpzcGxhYXQyZEF6dE5tSWh4cS9SVlZDRmRRdmRoUkJ5bzkxbUE2K2ZBWmdmaDdxU0FuL2VGVWtBa3RvTEp0dUN2c1J6dHBnMG5wT0pyN2hjZEN0eWkrajJzc1lsTnBxVXdKd3QxaW1sWEwya2E3M3NiRERYbEJFa003VVR1V01VcmJzcUNwQm5nRzhjQlUra0hnQjFpMWdiK3NUN2ZKZmR2Y1gwYUR5alUzcHlkSk9SUVpEWncyUkNreWlVTWUiLCJtYWMiOiIyZmQ3OTZhY2RlNmM4NzdmMTUwYzk1YjY0YzlhNTNjMjIxN2M1MTgwYzUxMTA5Yjg0MTM2MWYyOTJhZjk2YjBlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1658156824\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1593316864 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1593316864\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1847980951 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkMrcGlkYU9pa2MvcHN5N1JMS1ZrNXc9PSIsInZhbHVlIjoiaThzWXBOY3FNYWNMbTBvdnluTEp4WXRVQzY5dlM0MVdBSVJoWERRVWhJd0ZWS3lKNFIxMjJZU2NCLzlORnFRZkhxdDAzZElLVUFCNklMZ2pUQ3pOOFVPaFUzSWhQbGtHd0dkdlBYWVAzdXdzdG5jU1hoUDNkaFAwV1hrbnB3MUdjYXVtcEpENENUaE10WnZhUlVJSUVzcGRaUnNFQnNTUmJFelFZMGpWY3B0QUZ4Z0tnc04xbkwwMTFWbGltVWUzK2JrQWtiOFpvUHRCdE9ZYlIvdndpYTVJdUJnRlRDUnJGaG9udk95ZG1uc2duT3NRbWkwM3Rob2FhOUxyY2ZBbDR2SW5XV3MxQy9Md2xKMmYvR0V3c3NRQ2pybS83dG9tZUMvOTlUTm80T1VwWnF6NkdnNDNIb01hZ2I4MklCako3dkxNMzh6VVljZWs2Nk1xeVE0azZZNmdZVkVtRFVETjZLb1ZvVk1uOWs2WmJqN1Fac3AxOXp4dWhQS0F2Nkw5VnpVZDFYb3Y4VGE3c2I0NUtOT0Q3UE44d1RoVzN6WWZja2x1aDl1VFpySGhxdmxoWjlFVGtkbERzNGFmYitrREhPeFlRcEFoWGhsWllhcEVqYzlYOG1FclZlcys1VEpwK1JhZ2lzT0wxMG50S01oTUtRTjdBTVprajlLTnpkWVkiLCJtYWMiOiJjMGRhNDA0ZWIxYTg0ZDVjYTlkYmFlYWEwNGI1ZWM3OTJlNjFjYTk5MWZlNzcyY2IwYTMyYzY5ODYxZjhmY2ZkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:06 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkF6ZzJqdXFZanZPbzBKazBJR2N3b1E9PSIsInZhbHVlIjoiRG5QWWZGWnByOUlBdDJuSEg0WXorNFNMcy9WNEhreUNUMS9kNVgrcUV3cENGbHRUSWR3UHoxdDVZQ1pDOEorOE5XUUZuOFM0RE1uZXA5aGxocWdLWlErM2gxbXB6cEppNkViUEtrVnlPZm94eWg1alduMUdtcm9JOUFKS1pNVFR2RWgyUi9xQjZWNVoyWXZ2MWFScGppRHFkdENHV2UwQWcxVHlZQ21UcnF2RlAvckNlUGY1eHNoVGxmMUYzWTFheHlNeFFKRkx1UmwwUG5MR29pMm9QRU1FSTFuTEVvZlN5V2RoVS9tRGVzQ3hvd1MxenVDVTM5cVorVVBSS1pXVW5FM2hidDNnR25CK3dMWXp3ckdOeEdWMHYrVWhac0JEbWlBcFFBSGQyS0F5Ym5qTkd6RUxYZTZyR0VEaUlBK3gyTm1GT1FGYlIwYWVNdGM1NmYwdHJqYi9HSmZwRHMzYjkxYlI1amJRNm9XVXd2NXdJVTZIdjNBK0EybFpBWHBXN1QvdUdqTkdRaHZBcmxvRy91Uk1OVzR0bnJaM1diMEtDeFVwR1VIOVlkU01NL1gyaUFMd3NTaUdiKzVkUjdOU3BtT3VVMjFIM2p1T1ZDM3NWb1pQTEJSbURCUDgwaTMyMGJUbEpxcmE5eXBJbWFSN1ZGckU4OGhZNFQ2ODBxY0ciLCJtYWMiOiIxYzdkYWQzNjJmNmIxYjA2MTlhYWYyNmE1OGMwMDZlZTlkNGE0NzdmZGNkZDhkODNhM2FlMmU0MjA1Zjg5MzM0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:06 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkMrcGlkYU9pa2MvcHN5N1JMS1ZrNXc9PSIsInZhbHVlIjoiaThzWXBOY3FNYWNMbTBvdnluTEp4WXRVQzY5dlM0MVdBSVJoWERRVWhJd0ZWS3lKNFIxMjJZU2NCLzlORnFRZkhxdDAzZElLVUFCNklMZ2pUQ3pOOFVPaFUzSWhQbGtHd0dkdlBYWVAzdXdzdG5jU1hoUDNkaFAwV1hrbnB3MUdjYXVtcEpENENUaE10WnZhUlVJSUVzcGRaUnNFQnNTUmJFelFZMGpWY3B0QUZ4Z0tnc04xbkwwMTFWbGltVWUzK2JrQWtiOFpvUHRCdE9ZYlIvdndpYTVJdUJnRlRDUnJGaG9udk95ZG1uc2duT3NRbWkwM3Rob2FhOUxyY2ZBbDR2SW5XV3MxQy9Md2xKMmYvR0V3c3NRQ2pybS83dG9tZUMvOTlUTm80T1VwWnF6NkdnNDNIb01hZ2I4MklCako3dkxNMzh6VVljZWs2Nk1xeVE0azZZNmdZVkVtRFVETjZLb1ZvVk1uOWs2WmJqN1Fac3AxOXp4dWhQS0F2Nkw5VnpVZDFYb3Y4VGE3c2I0NUtOT0Q3UE44d1RoVzN6WWZja2x1aDl1VFpySGhxdmxoWjlFVGtkbERzNGFmYitrREhPeFlRcEFoWGhsWllhcEVqYzlYOG1FclZlcys1VEpwK1JhZ2lzT0wxMG50S01oTUtRTjdBTVprajlLTnpkWVkiLCJtYWMiOiJjMGRhNDA0ZWIxYTg0ZDVjYTlkYmFlYWEwNGI1ZWM3OTJlNjFjYTk5MWZlNzcyY2IwYTMyYzY5ODYxZjhmY2ZkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkF6ZzJqdXFZanZPbzBKazBJR2N3b1E9PSIsInZhbHVlIjoiRG5QWWZGWnByOUlBdDJuSEg0WXorNFNMcy9WNEhreUNUMS9kNVgrcUV3cENGbHRUSWR3UHoxdDVZQ1pDOEorOE5XUUZuOFM0RE1uZXA5aGxocWdLWlErM2gxbXB6cEppNkViUEtrVnlPZm94eWg1alduMUdtcm9JOUFKS1pNVFR2RWgyUi9xQjZWNVoyWXZ2MWFScGppRHFkdENHV2UwQWcxVHlZQ21UcnF2RlAvckNlUGY1eHNoVGxmMUYzWTFheHlNeFFKRkx1UmwwUG5MR29pMm9QRU1FSTFuTEVvZlN5V2RoVS9tRGVzQ3hvd1MxenVDVTM5cVorVVBSS1pXVW5FM2hidDNnR25CK3dMWXp3ckdOeEdWMHYrVWhac0JEbWlBcFFBSGQyS0F5Ym5qTkd6RUxYZTZyR0VEaUlBK3gyTm1GT1FGYlIwYWVNdGM1NmYwdHJqYi9HSmZwRHMzYjkxYlI1amJRNm9XVXd2NXdJVTZIdjNBK0EybFpBWHBXN1QvdUdqTkdRaHZBcmxvRy91Uk1OVzR0bnJaM1diMEtDeFVwR1VIOVlkU01NL1gyaUFMd3NTaUdiKzVkUjdOU3BtT3VVMjFIM2p1T1ZDM3NWb1pQTEJSbURCUDgwaTMyMGJUbEpxcmE5eXBJbWFSN1ZGckU4OGhZNFQ2ODBxY0ciLCJtYWMiOiIxYzdkYWQzNjJmNmIxYjA2MTlhYWYyNmE1OGMwMDZlZTlkNGE0NzdmZGNkZDhkODNhM2FlMmU0MjA1Zjg5MzM0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1847980951\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-501330500 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-501330500\", {\"maxDepth\":0})</script>\n"}}