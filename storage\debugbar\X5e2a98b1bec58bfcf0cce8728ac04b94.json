{"__meta": {"id": "X5e2a98b1bec58bfcf0cce8728ac04b94", "datetime": "2025-06-27 02:33:46", "utime": **********.907083, "method": "POST", "uri": "/pos-payment-type", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.291265, "end": **********.907096, "duration": 0.6158308982849121, "duration_str": "616ms", "measures": [{"label": "Booting", "start": **********.291265, "relative_start": 0, "end": **********.619056, "relative_end": **********.619056, "duration": 0.3277909755706787, "duration_str": "328ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.619067, "relative_start": 0.3278019428253174, "end": **********.907097, "relative_end": 1.1920928955078125e-06, "duration": 0.28803014755249023, "duration_str": "288ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 53122312, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x pos.payment_success", "param_count": null, "params": [], "start": **********.900402, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/pos/payment_success.blade.phppos.payment_success", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fpayment_success.blade.php&line=1", "ajax": false, "filename": "payment_success.blade.php", "line": "?"}, "render_count": 1, "name_original": "pos.payment_success"}]}, "route": {"uri": "POST pos-payment-type", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@financialType", "namespace": null, "prefix": "", "where": [], "as": "pos.pos-payment-type", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=190\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:190-260</a>"}, "queries": {"nb_statements": 56, "nb_failed_statements": 0, "accumulated_duration": 0.08162999999999998, "accumulated_duration_str": "81.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.648025, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 1.899}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6579158, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 1.899, "width_percent": 0.515}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 591}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.661654, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:591", "source": "app/Services/FinancialRecordService.php:591", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=591", "ajax": false, "filename": "FinancialRecordService.php", "line": "591"}, "connection": "kdmkjkqknb", "start_percent": 2.413, "width_percent": 0}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 687}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 620}, {"index": 18, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 595}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.662747, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:687", "source": "app/Services/FinancialRecordService.php:687", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=687", "ajax": false, "filename": "FinancialRecordService.php", "line": "687"}, "connection": "kdmkjkqknb", "start_percent": 2.413, "width_percent": 0.466}, {"sql": "select * from `financial_records` where `shift_id` = 48 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["48"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 621}, {"index": 17, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 595}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.664706, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:621", "source": "app/Services/FinancialRecordService.php:621", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=621", "ajax": false, "filename": "FinancialRecordService.php", "line": "621"}, "connection": "kdmkjkqknb", "start_percent": 2.879, "width_percent": 0.49}, {"sql": "update `financial_records` set `current_cash` = 39.44, `total_cash` = 2539.44, `financial_records`.`updated_at` = '2025-06-27 02:33:46' where `id` = 48", "type": "query", "params": [], "bindings": ["39.44", "2539.44", "2025-06-27 02:33:46", "48"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 643}, {"index": 15, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 595}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.666688, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:643", "source": "app/Services/FinancialRecordService.php:643", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=643", "ajax": false, "filename": "FinancialRecordService.php", "line": "643"}, "connection": "kdmkjkqknb", "start_percent": 3.369, "width_percent": 0.588}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 610}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 200}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7265, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:610", "source": "app/Services/FinancialRecordService.php:610", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=610", "ajax": false, "filename": "FinancialRecordService.php", "line": "610"}, "connection": "kdmkjkqknb", "start_percent": 3.957, "width_percent": 0}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialTransactionService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialTransactionService.php", "line": 21}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 217}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7267568, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "FinancialTransactionService.php:21", "source": "app/Services/FinancialTransactionService.php:21", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialTransactionService.php&line=21", "ajax": false, "filename": "FinancialTransactionService.php", "line": "21"}, "connection": "kdmkjkqknb", "start_percent": 3.957, "width_percent": 0.527}, {"sql": "insert into `financial_transactions` (`shift_id`, `transaction_type`, `cash_amount`, `created_by`, `payment_method`, `updated_at`, `created_at`) values (48, 'sale', '39.44', 22, 'cash', '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["48", "sale", "39.44", "22", "cash", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialTransactionService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialTransactionService.php", "line": 27}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 217}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.728965, "duration": 0.0029500000000000004, "duration_str": "2.95ms", "memory": 0, "memory_str": null, "filename": "FinancialTransactionService.php:27", "source": "app/Services/FinancialTransactionService.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialTransactionService.php&line=27", "ajax": false, "filename": "FinancialTransactionService.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 4.484, "width_percent": 3.614}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 233}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.736331, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PosController.php:233", "source": "app/Http/Controllers/PosController.php:233", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=233", "ajax": false, "filename": "PosController.php", "line": "233"}, "connection": "kdmkjkqknb", "start_percent": 8.098, "width_percent": 0.637}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.749883, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 8.735, "width_percent": 0.858}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.7520351, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 9.592, "width_percent": 0.6}, {"sql": "select * from `customers` where `id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 249}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7570882, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "PosController.php:249", "source": "app/Http/Controllers/PosController.php:249", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=249", "ajax": false, "filename": "PosController.php", "line": "249"}, "connection": "kdmkjkqknb", "start_percent": 10.192, "width_percent": 0.355}, {"sql": "select `id` from `warehouses` where `id` = '8' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["8", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/warehouse.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\warehouse.php", "line": 28}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 250}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7587302, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "warehouse.php:28", "source": "app/Models/warehouse.php:28", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=28", "ajax": false, "filename": "warehouse.php", "line": "28"}, "connection": "kdmkjkqknb", "start_percent": 10.548, "width_percent": 0.343}, {"sql": "select * from `pos` where `created_by` = 15 order by `created_at` desc limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 517}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 251}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7614982, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "PosController.php:517", "source": "app/Http/Controllers/PosController.php:517", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=517", "ajax": false, "filename": "PosController.php", "line": "517"}, "connection": "kdmkjkqknb", "start_percent": 10.891, "width_percent": 0.539}, {"sql": "select * from `pos` where `pos_id` = 1444 and `created_by` = 15", "type": "query", "params": [], "bindings": ["1444", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 255}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.763086, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "PosController.php:255", "source": "app/Http/Controllers/PosController.php:255", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=255", "ajax": false, "filename": "PosController.php", "line": "255"}, "connection": "kdmkjkqknb", "start_percent": 11.43, "width_percent": 1.225}, {"sql": "insert into `pos` (`pos_id`, `customer_id`, `warehouse_id`, `user_id`, `pos_date`, `created_by`, `shift_id`, `updated_at`, `created_at`) values (1444, 10, '8', '', '2025-06-27', 15, 48, '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["1444", "10", "8", "", "2025-06-27", "15", "48", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 272}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7653909, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "PosController.php:272", "source": "app/Http/Controllers/PosController.php:272", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=272", "ajax": false, "filename": "PosController.php", "line": "272"}, "connection": "kdmkjkqknb", "start_percent": 12.655, "width_percent": 3.7}, {"sql": "select * from `product_services` where `id` = '2299' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["2299", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 282}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.77032, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "PosController.php:282", "source": "app/Http/Controllers/PosController.php:282", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=282", "ajax": false, "filename": "PosController.php", "line": "282"}, "connection": "kdmkjkqknb", "start_percent": 16.354, "width_percent": 0.466}, {"sql": "select `tax_id` from `product_services` where `id` = '2299' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["2299", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 286}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.771915, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:259", "source": "app/Models/ProductService.php:259", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=259", "ajax": false, "filename": "ProductService.php", "line": "259"}, "connection": "kdmkjkqknb", "start_percent": 16.82, "width_percent": 0.233}, {"sql": "insert into `pos_products` (`pos_id`, `product_id`, `price`, `quantity`, `tax`, `discount`, `updated_at`, `created_at`) values (1444, '2299', '2.99', 5, '', '', '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["1444", "2299", "2.99", "5", "", "", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 295}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.773626, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "PosController.php:295", "source": "app/Http/Controllers/PosController.php:295", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=295", "ajax": false, "filename": "PosController.php", "line": "295"}, "connection": "kdmkjkqknb", "start_percent": 17.053, "width_percent": 2.916}, {"sql": "select * from `product_services` where `product_services`.`id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 3970}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.777189, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3970", "source": "app/Models/Utility.php:3970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=3970", "ajax": false, "filename": "Utility.php", "line": "3970"}, "connection": "kdmkjkqknb", "start_percent": 19.968, "width_percent": 0.27}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["8", "2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 3984}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.778601, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3984", "source": "app/Models/Utility.php:3984", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=3984", "ajax": false, "filename": "Utility.php", "line": "3984"}, "connection": "kdmkjkqknb", "start_percent": 20.238, "width_percent": 2.328}, {"sql": "update `warehouse_products` set `quantity` = 2, `warehouse_products`.`updated_at` = '2025-06-27 02:33:46' where `id` = 8698", "type": "query", "params": [], "bindings": ["2", "2025-06-27 02:33:46", "8698"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4003}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.78174, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4003", "source": "app/Models/Utility.php:4003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4003", "ajax": false, "filename": "Utility.php", "line": "4003"}, "connection": "kdmkjkqknb", "start_percent": 22.565, "width_percent": 3.283}, {"sql": "delete from `stock_reports` where `type` = 'pos' and `type_id` = 1444", "type": "query", "params": [], "bindings": ["pos", "1444"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 302}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.786261, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "PosController.php:302", "source": "app/Http/Controllers/PosController.php:302", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=302", "ajax": false, "filename": "PosController.php", "line": "302"}, "connection": "kdmkjkqknb", "start_percent": 25.848, "width_percent": 2.364}, {"sql": "insert into `stock_reports` (`product_id`, `quantity`, `type`, `type_id`, `description`, `created_by`, `updated_at`, `created_at`) values ('2299', 5, 'pos', 1444, '5   الكمية التي تباع في نقاط البيع #POS01444', 15, '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["2299", "5", "pos", "1444", "5   الكمية التي تباع في نقاط البيع #POS01444", "15", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4047}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 304}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.789622, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4047", "source": "app/Models/Utility.php:4047", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4047", "ajax": false, "filename": "Utility.php", "line": "4047"}, "connection": "kdmkjkqknb", "start_percent": 28.213, "width_percent": 2.94}, {"sql": "select * from `product_services` where `product_services`.`id` = '2299' limit 1", "type": "query", "params": [], "bindings": ["2299"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 307}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.793234, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "PosController.php:307", "source": "app/Http/Controllers/PosController.php:307", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=307", "ajax": false, "filename": "PosController.php", "line": "307"}, "connection": "kdmkjkqknb", "start_percent": 31.153, "width_percent": 0.343}, {"sql": "select * from `transaction_lines` where `reference_id` = 1444 and `reference_sub_id` = 2299 and `reference` = 'EXP' limit 1", "type": "query", "params": [], "bindings": ["1444", "2299", "EXP"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5654}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 322}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.794813, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5654", "source": "app/Models/Utility.php:5654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5654", "ajax": false, "filename": "Utility.php", "line": "5654"}, "connection": "kdmkjkqknb", "start_percent": 31.496, "width_percent": 2.401}, {"sql": "insert into `transaction_lines` (`account_id`, `reference`, `reference_id`, `reference_sub_id`, `date`, `credit`, `debit`, `created_by`, `updated_at`, `created_at`) values (282, 'EXP', 1444, 2299, '2025-06-27 00:00:00', 0, 5, 15, '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["282", "EXP", "1444", "2299", "2025-06-27 00:00:00", "0", "5", "15", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5673}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 322}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.798056, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5673", "source": "app/Models/Utility.php:5673", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5673", "ajax": false, "filename": "Utility.php", "line": "5673"}, "connection": "kdmkjkqknb", "start_percent": 33.897, "width_percent": 2.438}, {"sql": "select * from `transaction_lines` where `reference_id` = 1444 and `reference_sub_id` = 2299 and `reference` = 'POS' limit 1", "type": "query", "params": [], "bindings": ["1444", "2299", "POS"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5654}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 336}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.801448, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5654", "source": "app/Models/Utility.php:5654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5654", "ajax": false, "filename": "Utility.php", "line": "5654"}, "connection": "kdmkjkqknb", "start_percent": 36.335, "width_percent": 2.56}, {"sql": "insert into `transaction_lines` (`account_id`, `reference`, `reference_id`, `reference_sub_id`, `date`, `credit`, `debit`, `created_by`, `updated_at`, `created_at`) values (274, 'POS', 1444, 2299, '2025-06-27 00:00:00', 14.95, 0, 15, '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["274", "POS", "1444", "2299", "2025-06-27 00:00:00", "14.95", "0", "15", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5673}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 336}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.804829, "duration": 0.00234, "duration_str": "2.34ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5673", "source": "app/Models/Utility.php:5673", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5673", "ajax": false, "filename": "Utility.php", "line": "5673"}, "connection": "kdmkjkqknb", "start_percent": 38.895, "width_percent": 2.867}, {"sql": "select * from `product_services` where `id` = '2300' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["2300", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 282}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.808328, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "PosController.php:282", "source": "app/Http/Controllers/PosController.php:282", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=282", "ajax": false, "filename": "PosController.php", "line": "282"}, "connection": "kdmkjkqknb", "start_percent": 41.762, "width_percent": 0.319}, {"sql": "select `tax_id` from `product_services` where `id` = '2300' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["2300", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 286}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8097448, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:259", "source": "app/Models/ProductService.php:259", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=259", "ajax": false, "filename": "ProductService.php", "line": "259"}, "connection": "kdmkjkqknb", "start_percent": 42.08, "width_percent": 0.257}, {"sql": "insert into `pos_products` (`pos_id`, `product_id`, `price`, `quantity`, `tax`, `discount`, `updated_at`, `created_at`) values (1444, '2300', '3.00', 2, '', '', '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["1444", "2300", "3.00", "2", "", "", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 295}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.81118, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "PosController.php:295", "source": "app/Http/Controllers/PosController.php:295", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=295", "ajax": false, "filename": "PosController.php", "line": "295"}, "connection": "kdmkjkqknb", "start_percent": 42.337, "width_percent": 2.499}, {"sql": "select * from `product_services` where `product_services`.`id` = '2300' limit 1", "type": "query", "params": [], "bindings": ["2300"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 3970}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8143969, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3970", "source": "app/Models/Utility.php:3970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=3970", "ajax": false, "filename": "Utility.php", "line": "3970"}, "connection": "kdmkjkqknb", "start_percent": 44.836, "width_percent": 0.27}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '2300' limit 1", "type": "query", "params": [], "bindings": ["8", "2300"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 3984}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8156471, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3984", "source": "app/Models/Utility.php:3984", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=3984", "ajax": false, "filename": "Utility.php", "line": "3984"}, "connection": "kdmkjkqknb", "start_percent": 45.106, "width_percent": 3.344}, {"sql": "update `warehouse_products` set `quantity` = 0, `warehouse_products`.`updated_at` = '2025-06-27 02:33:46' where `id` = 8699", "type": "query", "params": [], "bindings": ["0", "2025-06-27 02:33:46", "8699"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4003}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.820051, "duration": 0.00233, "duration_str": "2.33ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4003", "source": "app/Models/Utility.php:4003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4003", "ajax": false, "filename": "Utility.php", "line": "4003"}, "connection": "kdmkjkqknb", "start_percent": 48.45, "width_percent": 2.854}, {"sql": "delete from `stock_reports` where `type` = 'pos' and `type_id` = 1444", "type": "query", "params": [], "bindings": ["pos", "1444"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 302}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.823668, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "PosController.php:302", "source": "app/Http/Controllers/PosController.php:302", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=302", "ajax": false, "filename": "PosController.php", "line": "302"}, "connection": "kdmkjkqknb", "start_percent": 51.305, "width_percent": 4.753}, {"sql": "insert into `stock_reports` (`product_id`, `quantity`, `type`, `type_id`, `description`, `created_by`, `updated_at`, `created_at`) values ('2300', 2, 'pos', 1444, '2   الكمية التي تباع في نقاط البيع #POS01444', 15, '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["2300", "2", "pos", "1444", "2   الكمية التي تباع في نقاط البيع #POS01444", "15", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4047}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 304}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.828949, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4047", "source": "app/Models/Utility.php:4047", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4047", "ajax": false, "filename": "Utility.php", "line": "4047"}, "connection": "kdmkjkqknb", "start_percent": 56.058, "width_percent": 2.805}, {"sql": "select * from `product_services` where `product_services`.`id` = '2300' limit 1", "type": "query", "params": [], "bindings": ["2300"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 307}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.832434, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PosController.php:307", "source": "app/Http/Controllers/PosController.php:307", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=307", "ajax": false, "filename": "PosController.php", "line": "307"}, "connection": "kdmkjkqknb", "start_percent": 58.863, "width_percent": 0.49}, {"sql": "select * from `transaction_lines` where `reference_id` = 1444 and `reference_sub_id` = 2300 and `reference` = 'EXP' limit 1", "type": "query", "params": [], "bindings": ["1444", "2300", "EXP"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5654}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 322}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.834597, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5654", "source": "app/Models/Utility.php:5654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5654", "ajax": false, "filename": "Utility.php", "line": "5654"}, "connection": "kdmkjkqknb", "start_percent": 59.353, "width_percent": 2.769}, {"sql": "insert into `transaction_lines` (`account_id`, `reference`, `reference_id`, `reference_sub_id`, `date`, `credit`, `debit`, `created_by`, `updated_at`, `created_at`) values (282, 'EXP', 1444, 2300, '2025-06-27 00:00:00', 0, 2, 15, '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["282", "EXP", "1444", "2300", "2025-06-27 00:00:00", "0", "2", "15", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5673}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 322}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.838157, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5673", "source": "app/Models/Utility.php:5673", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5673", "ajax": false, "filename": "Utility.php", "line": "5673"}, "connection": "kdmkjkqknb", "start_percent": 62.122, "width_percent": 2.119}, {"sql": "select * from `transaction_lines` where `reference_id` = 1444 and `reference_sub_id` = 2300 and `reference` = 'POS' limit 1", "type": "query", "params": [], "bindings": ["1444", "2300", "POS"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5654}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 336}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.841148, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5654", "source": "app/Models/Utility.php:5654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5654", "ajax": false, "filename": "Utility.php", "line": "5654"}, "connection": "kdmkjkqknb", "start_percent": 64.241, "width_percent": 2.364}, {"sql": "insert into `transaction_lines` (`account_id`, `reference`, `reference_id`, `reference_sub_id`, `date`, `credit`, `debit`, `created_by`, `updated_at`, `created_at`) values (274, 'POS', 1444, 2300, '2025-06-27 00:00:00', 6, 0, 15, '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["274", "POS", "1444", "2300", "2025-06-27 00:00:00", "6", "0", "15", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5673}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 336}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.844348, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5673", "source": "app/Models/Utility.php:5673", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5673", "ajax": false, "filename": "Utility.php", "line": "5673"}, "connection": "kdmkjkqknb", "start_percent": 66.605, "width_percent": 2.805}, {"sql": "select * from `product_services` where `id` = '2285' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["2285", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 282}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8478742, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "PosController.php:282", "source": "app/Http/Controllers/PosController.php:282", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=282", "ajax": false, "filename": "PosController.php", "line": "282"}, "connection": "kdmkjkqknb", "start_percent": 69.411, "width_percent": 0.38}, {"sql": "select `tax_id` from `product_services` where `id` = '2285' and `created_by` = 15 limit 1", "type": "query", "params": [], "bindings": ["2285", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/ProductService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductService.php", "line": 259}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 286}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.849643, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ProductService.php:259", "source": "app/Models/ProductService.php:259", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=259", "ajax": false, "filename": "ProductService.php", "line": "259"}, "connection": "kdmkjkqknb", "start_percent": 69.791, "width_percent": 0.404}, {"sql": "insert into `pos_products` (`pos_id`, `product_id`, `price`, `quantity`, `tax`, `discount`, `updated_at`, `created_at`) values (1444, '2285', '18.49', 1, '', '', '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["1444", "2285", "18.49", "1", "", "", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 295}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.85162, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "PosController.php:295", "source": "app/Http/Controllers/PosController.php:295", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=295", "ajax": false, "filename": "PosController.php", "line": "295"}, "connection": "kdmkjkqknb", "start_percent": 70.195, "width_percent": 2.965}, {"sql": "select * from `product_services` where `product_services`.`id` = '2285' limit 1", "type": "query", "params": [], "bindings": ["2285"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 3970}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8552659, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3970", "source": "app/Models/Utility.php:3970", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=3970", "ajax": false, "filename": "Utility.php", "line": "3970"}, "connection": "kdmkjkqknb", "start_percent": 73.159, "width_percent": 0.319}, {"sql": "select * from `warehouse_products` where `warehouse_id` = '8' and `product_id` = '2285' limit 1", "type": "query", "params": [], "bindings": ["8", "2285"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 3984}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.856555, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "Utility.php:3984", "source": "app/Models/Utility.php:3984", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=3984", "ajax": false, "filename": "Utility.php", "line": "3984"}, "connection": "kdmkjkqknb", "start_percent": 73.478, "width_percent": 2.389}, {"sql": "update `warehouse_products` set `quantity` = 1, `warehouse_products`.`updated_at` = '2025-06-27 02:33:46' where `id` = 8685", "type": "query", "params": [], "bindings": ["1", "2025-06-27 02:33:46", "8685"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4003}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 297}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.859725, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4003", "source": "app/Models/Utility.php:4003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4003", "ajax": false, "filename": "Utility.php", "line": "4003"}, "connection": "kdmkjkqknb", "start_percent": 75.867, "width_percent": 1.948}, {"sql": "delete from `stock_reports` where `type` = 'pos' and `type_id` = 1444", "type": "query", "params": [], "bindings": ["pos", "1444"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 302}, {"index": 13, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.862537, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "PosController.php:302", "source": "app/Http/Controllers/PosController.php:302", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=302", "ajax": false, "filename": "PosController.php", "line": "302"}, "connection": "kdmkjkqknb", "start_percent": 77.815, "width_percent": 4.778}, {"sql": "insert into `stock_reports` (`product_id`, `quantity`, `type`, `type_id`, `description`, `created_by`, `updated_at`, `created_at`) values ('2285', 1, 'pos', 1444, '1   الكمية التي تباع في نقاط البيع #POS01444', 15, '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["2285", "1", "pos", "1444", "1   الكمية التي تباع في نقاط البيع #POS01444", "15", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4047}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 304}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.868469, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "Utility.php:4047", "source": "app/Models/Utility.php:4047", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4047", "ajax": false, "filename": "Utility.php", "line": "4047"}, "connection": "kdmkjkqknb", "start_percent": 82.592, "width_percent": 3.295}, {"sql": "select * from `product_services` where `product_services`.`id` = '2285' limit 1", "type": "query", "params": [], "bindings": ["2285"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 307}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8723829, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "PosController.php:307", "source": "app/Http/Controllers/PosController.php:307", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=307", "ajax": false, "filename": "PosController.php", "line": "307"}, "connection": "kdmkjkqknb", "start_percent": 85.888, "width_percent": 0.319}, {"sql": "select * from `transaction_lines` where `reference_id` = 1444 and `reference_sub_id` = 2285 and `reference` = 'EXP' limit 1", "type": "query", "params": [], "bindings": ["1444", "2285", "EXP"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5654}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 322}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.873808, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5654", "source": "app/Models/Utility.php:5654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5654", "ajax": false, "filename": "Utility.php", "line": "5654"}, "connection": "kdmkjkqknb", "start_percent": 86.206, "width_percent": 2.377}, {"sql": "insert into `transaction_lines` (`account_id`, `reference`, `reference_id`, `reference_sub_id`, `date`, `credit`, `debit`, `created_by`, `updated_at`, `created_at`) values (282, 'EXP', 1444, 2285, '2025-06-27 00:00:00', 0, 1, 15, '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["282", "EXP", "1444", "2285", "2025-06-27 00:00:00", "0", "1", "15", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5673}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 322}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.8769941, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5673", "source": "app/Models/Utility.php:5673", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5673", "ajax": false, "filename": "Utility.php", "line": "5673"}, "connection": "kdmkjkqknb", "start_percent": 88.583, "width_percent": 2.279}, {"sql": "select * from `transaction_lines` where `reference_id` = 1444 and `reference_sub_id` = 2285 and `reference` = 'POS' limit 1", "type": "query", "params": [], "bindings": ["1444", "2285", "POS"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5654}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 336}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.880192, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5654", "source": "app/Models/Utility.php:5654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5654", "ajax": false, "filename": "Utility.php", "line": "5654"}, "connection": "kdmkjkqknb", "start_percent": 90.861, "width_percent": 2.364}, {"sql": "insert into `transaction_lines` (`account_id`, `reference`, `reference_id`, `reference_sub_id`, `date`, `credit`, `debit`, `created_by`, `updated_at`, `created_at`) values (274, 'POS', 1444, 2285, '2025-06-27 00:00:00', 18.49, 0, 15, '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["274", "POS", "1444", "2285", "2025-06-27 00:00:00", "18.49", "0", "15", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5673}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 336}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.883652, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5673", "source": "app/Models/Utility.php:5673", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5673", "ajax": false, "filename": "Utility.php", "line": "5673"}, "connection": "kdmkjkqknb", "start_percent": 93.226, "width_percent": 2.671}, {"sql": "insert into `pos_payments` (`pos_id`, `date`, `created_by`, `amount`, `discount`, `discount_amount`, `payment_type`, `cash_amount`, `network_amount`, `updated_at`, `created_at`) values (1444, '', 15, 39.44, '', 39.44, 'cash', 39.44, 0, '2025-06-27 02:33:46', '2025-06-27 02:33:46')", "type": "query", "params": [], "bindings": ["1444", "", "15", "39.44", "", "39.44", "cash", "39.44", "0", "2025-06-27 02:33:46", "2025-06-27 02:33:46"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PosController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PosController.php", "line": 457}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 230}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.8887858, "duration": 0.00287, "duration_str": "2.87ms", "memory": 0, "memory_str": null, "filename": "PosController.php:457", "source": "app/Http/Controllers/PosController.php:457", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPosController.php&line=457", "ajax": false, "filename": "PosController.php", "line": "457"}, "connection": "kdmkjkqknb", "start_percent": 95.896, "width_percent": 3.516}, {"sql": "select * from `pos` where `pos`.`id` = 1444 limit 1", "type": "query", "params": [], "bindings": ["1444"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "pos.payment_success", "file": "C:\\laragon\\www\\erpq24\\resources\\views/pos/payment_success.blade.php", "line": 3}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 208}], "start": **********.901196, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "pos.payment_success:3", "source": "view::pos.payment_success:3", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fpos%2Fpayment_success.blade.php&line=3", "ajax": false, "filename": "payment_success.blade.php", "line": "3"}, "connection": "kdmkjkqknb", "start_percent": 99.412, "width_percent": 0.588}]}, "models": {"data": {"App\\Models\\ProductService": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductService.php&line=1", "ajax": false, "filename": "ProductService.php", "line": "?"}}, "App\\Models\\Shift": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\WarehouseProduct": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FWarehouseProduct.php&line=1", "ajax": false, "filename": "WarehouseProduct.php", "line": "?"}}, "App\\Models\\Pos": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPos.php&line=1", "ajax": false, "filename": "Pos.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 21, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-23727502 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-23727502\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.75611, "xdebug_link": null}, {"message": "[ability => manage pos, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-13468290 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13468290\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.760947, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/pos-payment-type", "status_code": "<pre class=sf-dump id=sf-dump-1150913037 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1150913037\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-572240695 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-572240695\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1751394978 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>vc_name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_name</span>\" => \"<span class=sf-dump-str>8</span>\"\n  \"<span class=sf-dump-key>quotation_id</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>payment_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cash</span>\"\n  \"<span class=sf-dump-key>total_price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">39.44</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751394978\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2144092931 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">78</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InUwdzY0bnVFcS9kWm4rN1lZT1hacnc9PSIsInZhbHVlIjoiSGYwTnN2MTRnMk4rb0FJZmliSTcxQVNucUZBZEtqbW1OSEhBSnkzcHJHRDBqaGZadllGSUtGV0tFcEVSNHVsN3dHQlM0YUt0TGg3dFBsbGhhM0xmQ2s0M0UwTmhYUTlHMTJtVmxaenZrUUFHNmVDSmJLYUpQN1NweGZ5Yk5yRk5IbDJ2dlh5alAxMnhURmoyT0dCMnlLSzBWSXROUDFyMjlPQk1ENk81RGppL1k1U0VZeWI5REphV2FyU2ZKTXRXOWZZaUNGME1IUGhkV0M2VEpiTWRJQXN1WUNXdlZLTU1ZZGpLNUt2K3pPcHNiVW10Y25nbkl3engvOWtLM1ZmZkVqT0pIaVY0QmxrYzVuVnNFWXoxOUhkWWFISk1EbXU5SVBoYWpVdlVpcjBvOE1GclVZNDdoSk1ONlAzejBwUXA3Z1lMQ3l5TXdncmpzM21RODlMWWo2N3BIZVYyamVvMVliTk5yK0ZNU0RUcks3Y1VyR09WTnRuWGJtVlF3ZjA3R2I0aHMzbkprQ0ZYT2FtZFRHV1hWLzhaT3pOWktuQU0yNjNLU1lpeU90V1B4dFZFeXp3UkFScVcxM1lyUWdQV3hNYU8zdWwrYWRISk94YUhSQ1lGWk9tZm4wRkJRVUtqaFdtUDhDS3FLaTUvZ0krU2FsVk9PSDVyclhWMEQ0YjAiLCJtYWMiOiJhMzNmNjVjZjA0Y2ZiZjU5MTRiYzZlNTE4YmU2NDIxNTI4YWZjNWU5YmRlMDA3YzY1ZDFlYjhmMGJjMmQxZjkxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhydlhVSUFzZUluL3o4bktJTFkzalE9PSIsInZhbHVlIjoibE9ONzlDZTQvUFErUXNqWFlUMHVSbGtmMUpzT3dSd3dMS2NhV0hqMjlBZUlWUkoybDRyV2xMRXVBTGc4KzFzWXFHcFc2OTBGL1hIcGgrMUxkZXo0OG50Y0FRdE9OOWFxdjlMNWVDdlBVaVVFclZnUlk1WTVjeVd2U2FUZnJleTg5R1AzRUJtWnBNdDdRMlpISksrS1RDOTh5VUs0NitYSkJvSHhsUnNvYkNTd3p5Mlo3R0J4UVVTRE5mNnpFZkNQQVVMYmJFTUFzai85OEZrSDhSeVNYUXRMTkVMRElrVEN2YVVxSTRQelpVTmxIS0d5cnhjNUV3QVJQYVFBQUowSUY4RzRCcnpzVFY3bFlrSWdOMHh3N2tSUldjTzhpUmdqblUvTm0yYTEzdytXK3N5MFhWb2piTllyRXBNTWpKOHZXYXZGell6WnhMUGF2THVBRGtlVTJqc1NvMlloSkhNYjBJb1ROcjBEVExDM1lmWHIwOXNZZWNaSHd0RUFoU3dTK2k3L1hMakJ1d2ZWVld2RjdhWkRxODZjRk5Ga3liVzdkWFJ1Wklzd2ZlSENndjF5Qkt6MmhZM0VNWE9JQ0VEMWplQWJSYlFxR3pVNnNOSmtnQU5ZOW43TXlwRUp6L1lDZzBDMmM4S3UzV3VMOEpxUEJCSnY5cUhjS3llV0Z2TlciLCJtYWMiOiI3Njk1OGMyZTgzZTM5ZGVkMDFiNzM1NzliOTVkZWZhYzFlMjViYWU0MmUxNGE4ZTA2MzkwNmJkOGUwZWQ3MTg1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2144092931\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-214563949 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-214563949\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2137991999 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:33:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Im1UVGtNbHA2UmlKT3R1KzBGSTdxUUE9PSIsInZhbHVlIjoiSHBaL0lGOHpuenUvTkdoUTdlUHQ4SS96b2dVbVRVeW1LSjg1OHd4eTh3bElnNjNUamRnUWNha0tmNHBRQlpvUVBFR3Y3cHkvdTYrOW5ORXZ3N1FoQnMrMk10VTNsZDZPSkVXMzBkaWJvcDRMbi9OdWQwdzExR3hXL1J0V0xGeDhDNDl6N2YvS1htL2txd3BEZjNvSExtRHdOVFg1Q1l5MGc5K3FYZFNFL1hORG1wQ3hMa0FrSnFJbDZtamRyakd3dU5BSXp2dDZTNHZkTURXSTBzVzd3WU13VlhJZWF0dHVGdWxkNjZNMnZjbVhiTDdtdTMzMzFFZzViNXNxRXlXZUlPbGlIdnVTTUY2TU1YaElvZzlRUEpSYyszK2trM04xVmFodC8rZ1gvYXVrZUJhanJ3QlVYbkkvdUFMTEp5Rk43d2ZyMERqVitNZ081OUdQOWxzNldVc0h1c3ViR01jeG90ZmdGdWhtaTd2dEhkdklHL0h5RDFPalNFbHJyL3FCcGhQeHNWK2ZHVkY2MExacWJXa1V3QllucVY1ODZXZUYzOEYyd2JNLy9PRkhrbXkzbGE4TDZieW8zU3JJSTNhRzF2dTUrRVp3WkhxV2hteUVHYjBNQWlDTngrZzdQMUEyTUk3ay9HK25OZVltUmIvNm1EbkQ2TTRaeFRXQWlUMmEiLCJtYWMiOiJjZWI1ZTk3M2RiMWZhNTZlMzc0ODU2MDQ5OGZjZTlmODFjMjllMjk2MDEzODM1OTUxYThlOTkzZGY3YjdhMmZlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:33:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IktpMm0vUDRpbEU4NUlsYkY3QTlJNGc9PSIsInZhbHVlIjoiTmprVzA0Y1FBbW1xSFBvbXJEMUlhQy9FTjZrSGI1RVZiWmlqY3gramQ3UThVMVZuRTc3UGxWZ20wY0tYNmxVZTE3UnNzQmo2cmJ3aXhSZ2ZxK3hzMjF4MDlYVnc2RTNkUFBTWU1KU0U0WkV1NCtKUkZVcHV5Ym9xT29kNHd6clI2Mlc5c1U1UExUaEUvZDNNalJJL1BxRXEzZTB4NjZMbzRLWWhpZ05XRzRkcHI1OVluaVdaMjRkSDJtZVFIQWVQTlpRVzFmV0tJcDJ6MEJiRFRkTjJOc1F4N2pXWkEvUW10a3RGMjNuTjdkVlU0L2xaUmFlelgvazcxQndpVVNkZ3NLMDcrUFBacE12cE44dzFXN3dkTGR4bmQwQWRKa0hHcmxiRjZxUnFuK1JzTUdvWjBvaXI1ZGY1K2NSTHdoWk9RbGY1b1FkTCtzaEZXdFJ0b2tvQnJiaG9yWFVlMUU5Z3hSUVA2bEZKeWVRaUlxd0Zrelg4bG1JSlFnNGxBY0xQWWRsbnFOSnltOVZYczJSeGNVMHA2TzZTd2V6K20rbEtCWStDd3UvNDNxandYYWVWYk9oMGNiWHAxZEx0RC83bEwvdTJwTDZ6K0Fub3F2K2RBcGMxN0NETXNDaG1aL0NtN1MvTnBqdkFYVnlVeWZqYm1sTHhUSWF0c2RjdWIwM2EiLCJtYWMiOiJlNDExNmU4Mjk3YjUzMTE2MTNmODQ4MDVlYzI3YThiNTUwZTM3ZWEyM2FiYzhjZjZkMjFmNDExM2QwYzJmNTBjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:33:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Im1UVGtNbHA2UmlKT3R1KzBGSTdxUUE9PSIsInZhbHVlIjoiSHBaL0lGOHpuenUvTkdoUTdlUHQ4SS96b2dVbVRVeW1LSjg1OHd4eTh3bElnNjNUamRnUWNha0tmNHBRQlpvUVBFR3Y3cHkvdTYrOW5ORXZ3N1FoQnMrMk10VTNsZDZPSkVXMzBkaWJvcDRMbi9OdWQwdzExR3hXL1J0V0xGeDhDNDl6N2YvS1htL2txd3BEZjNvSExtRHdOVFg1Q1l5MGc5K3FYZFNFL1hORG1wQ3hMa0FrSnFJbDZtamRyakd3dU5BSXp2dDZTNHZkTURXSTBzVzd3WU13VlhJZWF0dHVGdWxkNjZNMnZjbVhiTDdtdTMzMzFFZzViNXNxRXlXZUlPbGlIdnVTTUY2TU1YaElvZzlRUEpSYyszK2trM04xVmFodC8rZ1gvYXVrZUJhanJ3QlVYbkkvdUFMTEp5Rk43d2ZyMERqVitNZ081OUdQOWxzNldVc0h1c3ViR01jeG90ZmdGdWhtaTd2dEhkdklHL0h5RDFPalNFbHJyL3FCcGhQeHNWK2ZHVkY2MExacWJXa1V3QllucVY1ODZXZUYzOEYyd2JNLy9PRkhrbXkzbGE4TDZieW8zU3JJSTNhRzF2dTUrRVp3WkhxV2hteUVHYjBNQWlDTngrZzdQMUEyTUk3ay9HK25OZVltUmIvNm1EbkQ2TTRaeFRXQWlUMmEiLCJtYWMiOiJjZWI1ZTk3M2RiMWZhNTZlMzc0ODU2MDQ5OGZjZTlmODFjMjllMjk2MDEzODM1OTUxYThlOTkzZGY3YjdhMmZlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:33:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IktpMm0vUDRpbEU4NUlsYkY3QTlJNGc9PSIsInZhbHVlIjoiTmprVzA0Y1FBbW1xSFBvbXJEMUlhQy9FTjZrSGI1RVZiWmlqY3gramQ3UThVMVZuRTc3UGxWZ20wY0tYNmxVZTE3UnNzQmo2cmJ3aXhSZ2ZxK3hzMjF4MDlYVnc2RTNkUFBTWU1KU0U0WkV1NCtKUkZVcHV5Ym9xT29kNHd6clI2Mlc5c1U1UExUaEUvZDNNalJJL1BxRXEzZTB4NjZMbzRLWWhpZ05XRzRkcHI1OVluaVdaMjRkSDJtZVFIQWVQTlpRVzFmV0tJcDJ6MEJiRFRkTjJOc1F4N2pXWkEvUW10a3RGMjNuTjdkVlU0L2xaUmFlelgvazcxQndpVVNkZ3NLMDcrUFBacE12cE44dzFXN3dkTGR4bmQwQWRKa0hHcmxiRjZxUnFuK1JzTUdvWjBvaXI1ZGY1K2NSTHdoWk9RbGY1b1FkTCtzaEZXdFJ0b2tvQnJiaG9yWFVlMUU5Z3hSUVA2bEZKeWVRaUlxd0Zrelg4bG1JSlFnNGxBY0xQWWRsbnFOSnltOVZYczJSeGNVMHA2TzZTd2V6K20rbEtCWStDd3UvNDNxandYYWVWYk9oMGNiWHAxZEx0RC83bEwvdTJwTDZ6K0Fub3F2K2RBcGMxN0NETXNDaG1aL0NtN1MvTnBqdkFYVnlVeWZqYm1sTHhUSWF0c2RjdWIwM2EiLCJtYWMiOiJlNDExNmU4Mjk3YjUzMTE2MTNmODQ4MDVlYzI3YThiNTUwZTM3ZWEyM2FiYzhjZjZkMjFmNDExM2QwYzJmNTBjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:33:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2137991999\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1014027765 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014027765\", {\"maxDepth\":0})</script>\n"}}