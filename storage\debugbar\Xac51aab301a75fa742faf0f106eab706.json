{"__meta": {"id": "Xac51aab301a75fa742faf0f106eab706", "datetime": "2025-06-27 00:47:21", "utime": **********.411476, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750985240.997023, "end": **********.41149, "duration": 0.41446685791015625, "duration_str": "414ms", "measures": [{"label": "Booting", "start": 1750985240.997023, "relative_start": 0, "end": **********.362905, "relative_end": **********.362905, "duration": 0.36588191986083984, "duration_str": "366ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.362915, "relative_start": 0.3658919334411621, "end": **********.411491, "relative_end": 9.5367431640625e-07, "duration": 0.04857587814331055, "duration_str": "48.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45043928, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027, "accumulated_duration_str": "2.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.38982, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.593}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.399681, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.593, "width_percent": 22.963}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.405305, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 85.556, "width_percent": 14.444}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1731034417 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1731034417\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-887739703 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-887739703\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1207643325 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1207643325\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-564072176 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750985213046%7C68%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InhLQUErbkFXd2lteXhFL0tNWVVHVFE9PSIsInZhbHVlIjoiZVd4Z1h2YmJQN1hBUFVmbXh2dUIyaGUyck0zdUxXL2l3VEVtN2xxUTRMU1lsTkhnY0NKTWJVazF5bzIweTN4bDVISThVRm01TkQ3V1VVU2dVYVBVSVhRUVliS2pMdTFSMFBERDZ5MUtueWwrMGNKb2J3SGYzZnpKc3FQOW1YQ1RsTmJCZlhqZ3J4SlNyMTI2VThFMFJCTDdxZThBQ3pFUENNWnc1QUkxbWRSbnhyZmdZMU5JRFE1SkdSd1I3T2NBMzAydkFvZVNDbWVlYXgzaytIWjhzazE1STk4SVp4TnBNYVovTnF0WjYwRWtRdTkvUDN3Tk5EMWErQXduVEJaVWZGa0EyQnYxNXhudlVEenNkZHNLTHFsOEpkQnB5Y2tQTHhjWmZzQ1dURXVmZDRiVnVzajBlNjQ0eWIwYXpkQVZiSFI4RlBwMmFFOEdBN1U3THJCQ203U0l5bzVVb0RGV1hDK0FnK3R0ak5HUlM2eGxXVFNtQ1ZGZ1A0cUNOZWZmRWJvajJWNVdVdGxzNE9yM1dFcUd0VEE1Um5zSmRkNjI3UkxNTlRVMy9sNDltZkVPd205TUdEa3c5TENjN29DY1RLajhuQzArSnNkNW5HTFd6QjBGLzRwSWo1bFRmUG9mZi8yWERDMUN0WXRVWDIrZS90SXJDeW5GWDZpaFFGYnEiLCJtYWMiOiI2ZTRlNjRmYjkyNzE5YjU2ZWQ1ZTI3NGVkYzI5OThmYTcwMzAzYTYwMTczNjc5MGEzOTA2NmFlNGE4YjcxNWQ0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjJNY0JINlJ1RCs5UzlaTnJUNjBRTWc9PSIsInZhbHVlIjoiZEtFa0Z1NDVsOVJDOVBKZFh1aU5KVlFSV2syOEZ3ZSt5V0NYU3RDTnVhL3NHYVBUZGhtUkpMSWpDMnhTcVZSSTVHSTkxQmg4Zm9lS09FV3c1U0VSc280SThlVS9TV0YxZUhLcTdTanRGRFdKM2I5Nm5JSFVsVGNHZkRSdG9aYjBqaDhLdzB4ZWZSbjd6Y1NjN2JKY0hTSU4wbFdaMU1UQkZPLzY5M0hoOVc1UklVd2gwT0dkbjdRN2Fuam94Ujl4TjB6Y2YraXl3K2o5MnRkY1pIQ3F2c2ZPNEhEb3lSdFhpMmVvWUViTzZWWGFweWtta2xVd0lUVmhPVkh1TkIwdU1hbGw2ZE5XQ0lrRjZaMHpSMGxKQWM2b3phb2FiV0JGWWNnV2ZVVEl5cGRVczkyNU54ZUtZZXJvL0F4MFBNVUU4MGNGOSt3S0g2QmgyVXo1MFlvL094LzIzS3NidFlPK2VVQUg3ZkFuVTBLQ05BZ2xyV3FSWm1WYmFvOXVPWVpwaUZBbnhMMWEzY0FqSklFOTA4WG9PajZ3UVFWanRpYjFsekthbjZHTDR3cXNkdzhzT3NsTnJ6N3lwT21FekkxbGNqSXJmRG55NmZ2bG1BQmtQazEwakdxbit4ZTBMazh3L0FGbVVsNXMrR2hSaHpFL1RKbVZjS3RGYXAyRzRXY0QiLCJtYWMiOiJlYzQyOTE1MmIxNTk2MzkyOTgwMmI1MTAwZmE3YThlYmI0NDI0NTE1ZjlkZmVkMzE3YTFkMzM0NmMxYzMzZTZkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-564072176\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-417816126 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-417816126\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1996068679 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:47:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlA1RUFzUkJLRUlJQ1lGNHlHTE5QblE9PSIsInZhbHVlIjoiRU5zUXBhazFsQkIwOHgzQ2dvZTdLRXVwSkwyWE0xSFFSeUtoRjdyVE1SckRkelZybC8yUTdyU3hjVkJuN1BjMnhRcVFIUjJxWE81d1EyNGRPRG9tNEx2emxaS1g5NFgrU3FsS1JyeWlxb3pUbzNCMk1oaENwMURBRVg5VzVXQmtia3BNOXd6SFJWRDB4dEk5Zy9aREh6d2dkODE3T3lyeUdEMjFJU1BzS0gvQlkwNzMrbVl2WEhYMURmNVhnTHgzdUlqbFdMK2ZLMm1iZlRHRnh5MlpxWktQbEMwZ0ZUNitVOVRES3VHZEx1ekkwOElTQjdrZWFIRlc4T2pudjJERFlLcVcwaEhDNThRR0pVdUxJYTVRMVIvOHlqa1BwUXNsbHZjdW9qVUVNclFzZUlHYW5oWXk4Mk5rSzc5R2M3anJzT21nMGNzbkFsN1B2UE1GWVM1R2s5eWJIMnl3MGZNL0tCTHZoUk91dVFUK2JBRTdBdW85VHdpT2xXNStMUDU4alVZYzhyclFGSEpKeXppdFdyVFQxaTAwOXgyVzEvUkpJSDZwUnpidUpaMFZuVUZvQlJEM0szQk9JZzRXb0FqYVBsY3ZFNGNRRSsvZ0JQZGl0QU1OekFSMWJkU00wT1drVmdzRU03MGV3UjFieDNSbE9GM29tLzA4Vng4N2tQa3ciLCJtYWMiOiI0ZGQwNzZiYzY2NDQ4ZDFkZTFlMTg5MGZiMjg3OThmNDZiZTBjMzAxZWQzZTYyMDJmZDZmYmU4YjkxYTg2MGEzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:47:21 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IldqZmg2bGFkN2pZSElhckVyTEtrRmc9PSIsInZhbHVlIjoicEQ3VVZDVHlmaXltSVhya2VxNjJkUS9LVDVNZFZqMTlkLzEzT1M3cTVDYURkRjdWUE5uanJHM1llZXM2OVBYcWdiNGU5ZzFPRU9ESXpCT0ttNk44UTQ3SDE5d25qOFRFQVpFRGNSZVZia3VjOFZQMm9aa2I3eU9qMTFnMlp3YzViSnFPNWhMOENNR25FMS9yeElZVGh1QnV4cGRMclNkbnY3YzJTSEphSllkZHdtenZRMStmOFBiRlRjZDV2V2VQK0lpTzJFdHNNUk82Z3RzL1YyaWt4SXdLblhlWHpBcXVod21KWEVmekRCOThieGFCWVZrczVxWTNJTGZrSTkvbjlGR2g5U3JIQ3N5Y2QwdHdyVjlUUjdKOWdlMXFsa1FieXdaTGE5U3N0bFREZFAreXZhN25FOXlYWUhtTnB4c3doVURuUS94NUpqNWtBZHhMeXdOdDYrWUc0ak91Wk1iUzE0SkFHTE96ODFiWXVBQnpHek10Y0lleER4SWs3NWVYeDRoODN4ZTVVcU1rY3R4N3pQRFU4NVBwaE95Qzl3ZGx2Z1FIKzN4alYwZW9FQjNTTDd3eWo1Q3BGRXBNbkZDWHgzcWRMM1prb2RsNzZPTEN6RHRDaThUTzdpeGV2SGgyZWRXMjZnRlFBbmlVNFFwMUcyMTF1TXN0bnhTMzRQeTMiLCJtYWMiOiJkNzNhN2M1YjNlODk3MWNkNjgzYmUxYWM5MGQ5MjU3ZmM5MTIyODkxNDA0OGM2OWRkMjhmMDJiMmM2MTkyZWNhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:47:21 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlA1RUFzUkJLRUlJQ1lGNHlHTE5QblE9PSIsInZhbHVlIjoiRU5zUXBhazFsQkIwOHgzQ2dvZTdLRXVwSkwyWE0xSFFSeUtoRjdyVE1SckRkelZybC8yUTdyU3hjVkJuN1BjMnhRcVFIUjJxWE81d1EyNGRPRG9tNEx2emxaS1g5NFgrU3FsS1JyeWlxb3pUbzNCMk1oaENwMURBRVg5VzVXQmtia3BNOXd6SFJWRDB4dEk5Zy9aREh6d2dkODE3T3lyeUdEMjFJU1BzS0gvQlkwNzMrbVl2WEhYMURmNVhnTHgzdUlqbFdMK2ZLMm1iZlRHRnh5MlpxWktQbEMwZ0ZUNitVOVRES3VHZEx1ekkwOElTQjdrZWFIRlc4T2pudjJERFlLcVcwaEhDNThRR0pVdUxJYTVRMVIvOHlqa1BwUXNsbHZjdW9qVUVNclFzZUlHYW5oWXk4Mk5rSzc5R2M3anJzT21nMGNzbkFsN1B2UE1GWVM1R2s5eWJIMnl3MGZNL0tCTHZoUk91dVFUK2JBRTdBdW85VHdpT2xXNStMUDU4alVZYzhyclFGSEpKeXppdFdyVFQxaTAwOXgyVzEvUkpJSDZwUnpidUpaMFZuVUZvQlJEM0szQk9JZzRXb0FqYVBsY3ZFNGNRRSsvZ0JQZGl0QU1OekFSMWJkU00wT1drVmdzRU03MGV3UjFieDNSbE9GM29tLzA4Vng4N2tQa3ciLCJtYWMiOiI0ZGQwNzZiYzY2NDQ4ZDFkZTFlMTg5MGZiMjg3OThmNDZiZTBjMzAxZWQzZTYyMDJmZDZmYmU4YjkxYTg2MGEzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:47:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IldqZmg2bGFkN2pZSElhckVyTEtrRmc9PSIsInZhbHVlIjoicEQ3VVZDVHlmaXltSVhya2VxNjJkUS9LVDVNZFZqMTlkLzEzT1M3cTVDYURkRjdWUE5uanJHM1llZXM2OVBYcWdiNGU5ZzFPRU9ESXpCT0ttNk44UTQ3SDE5d25qOFRFQVpFRGNSZVZia3VjOFZQMm9aa2I3eU9qMTFnMlp3YzViSnFPNWhMOENNR25FMS9yeElZVGh1QnV4cGRMclNkbnY3YzJTSEphSllkZHdtenZRMStmOFBiRlRjZDV2V2VQK0lpTzJFdHNNUk82Z3RzL1YyaWt4SXdLblhlWHpBcXVod21KWEVmekRCOThieGFCWVZrczVxWTNJTGZrSTkvbjlGR2g5U3JIQ3N5Y2QwdHdyVjlUUjdKOWdlMXFsa1FieXdaTGE5U3N0bFREZFAreXZhN25FOXlYWUhtTnB4c3doVURuUS94NUpqNWtBZHhMeXdOdDYrWUc0ak91Wk1iUzE0SkFHTE96ODFiWXVBQnpHek10Y0lleER4SWs3NWVYeDRoODN4ZTVVcU1rY3R4N3pQRFU4NVBwaE95Qzl3ZGx2Z1FIKzN4alYwZW9FQjNTTDd3eWo1Q3BGRXBNbkZDWHgzcWRMM1prb2RsNzZPTEN6RHRDaThUTzdpeGV2SGgyZWRXMjZnRlFBbmlVNFFwMUcyMTF1TXN0bnhTMzRQeTMiLCJtYWMiOiJkNzNhN2M1YjNlODk3MWNkNjgzYmUxYWM5MGQ5MjU3ZmM5MTIyODkxNDA0OGM2OWRkMjhmMDJiMmM2MTkyZWNhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:47:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1996068679\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2028720642 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost/bill</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028720642\", {\"maxDepth\":0})</script>\n"}}