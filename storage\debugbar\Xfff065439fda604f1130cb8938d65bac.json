{"__meta": {"id": "Xfff065439fda604f1130cb8938d65bac", "datetime": "2025-06-27 02:15:01", "utime": **********.558716, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.151863, "end": **********.558729, "duration": 0.40686583518981934, "duration_str": "407ms", "measures": [{"label": "Booting", "start": **********.151863, "relative_start": 0, "end": **********.483718, "relative_end": **********.483718, "duration": 0.33185482025146484, "duration_str": "332ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.483726, "relative_start": 0.3318629264831543, "end": **********.558731, "relative_end": 2.1457672119140625e-06, "duration": 0.07500505447387695, "duration_str": "75.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48197696, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00758, "accumulated_duration_str": "7.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.515207, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 21.108}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.52463, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 21.108, "width_percent": 4.881}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.5375152, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 25.989, "width_percent": 6.201}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.5393, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 32.19, "width_percent": 4.749}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.543558, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 36.939, "width_percent": 34.828}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.548617, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 71.768, "width_percent": 28.232}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1686582414 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1686582414\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.542615, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-417835504 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-417835504\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1953014528 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1953014528\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-826513168 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-826513168\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1092500868 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990356916%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ii92Mm9memlVRDRZRFQ1NnoxZWFBY0E9PSIsInZhbHVlIjoia0hmQkVkckNybjNxeERIWTdtRm9mWVFzYmc1STVJVm1QS24ySGsybksra1V1enV6clJVSndnb2gwZTNBZEhJUy9pV3Q3T0tWYTRnOGZsTDdVa3lRZDVCNlcyaWc4Uml1ckNHT2l5RTRyT0tmV0lFZ1pRNC9aK1FIZkFPWFJTQ0ZtOWsvMEVOZlZUbklQR1R6V1FkV1ZHdGpsS00wK2w0SHQvYjVYMm1sK1hIL1RoclVteFFhRWJIaU50azdpc2ducVZDaEFMUi9HUUcwTVFvVENWRWF3Q1VOTTZjNmt3YmE1TngzK2V0U0NncTIzNy9SSVZkRUxHNlQzSnFXTzVkam1GK0tCZXh4QU56Q2JxVnNjVzlyNzhxSGlTRitzOGIwcWhvYkNXcHNtYVFwRG8zZ0E1QmtmUjREU3ViektCQkdHSGx1ZFVKUlhqVDEwcE1FNTg0ZVZUYllWWlhqdlF0ZnowcFFwQkJWOStlVlVnTnl0bE9wUzR3YUlDV1E2YmRNa0tZRHk1WHZNSGxLTVhlOGRlc2NiWFQ1SFE0K1ByZEsrOVNZMVhPUU91aXNsVXFpQ2tKdVQzaUJJT0Q0Ym5scEpGaElOVitUSTNDZzhQNSswZXpYMzd5Mldqais4c1k3c0J0OVBMSjVlc1Z0TUJ0SzZUSFJUZXhVaW05SGFwelAiLCJtYWMiOiIxMmVkMWRhNzQ5M2U4ODUwNzRmNmFmYjVhODZkYzFlODlhYzc3ZTE1ZWJmMGZhMjMyYjA4ZjI5YzgwYzdiOWFkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVFVlBzUnVlVzVmTGREcGJUS3ZHUXc9PSIsInZhbHVlIjoiUkZXaWJGSFV6OVdmUGJPV2RnUWVjWWdIK3NWMnJsdlR0RW1mRG9xMzNuZGQrWm5XS2cxOWozN2h2TjZaVkxLYTFBUnhFSVJYMnhJRTZpL1NzdnlTWTI4NkYrL2FOVHZwWlBydFN1MkhBenRyOFVoMGxITk1PMW82NU0wT2p4Ky9SeS9HeXNTM0tzWVZ6MXErMHFSVGg5QkF0WEhDL1hNOHVvcVpxRDdqQUwvczVFVVJRTWNBcnloaFd3bzQ2a3VYOEF5ZStTTkRmSFRXQ3NMSmsvRXo5VHFhcXFETm5FMTNpUGFMaW5mZGJZalpHaVF1UE5xL3VoTEZjRldJOWxzK2JxZTZyN1FyWkthRHVodmRqU3hnZi9PdDh3YW9HcWk5elE4UGw4QU03M255Nk9IMmVKZGptK2Y3dk9pYXJhYm9ORlBPK0dVMHp1OUZnUHhXSkFVd3JJZGhvTTdORGU2dFVESXowSU96SE9oTjQ0aWpSNUZGREZETmtyNDhMNy9aRTRIQllRYmllY1Ewam1RWFpwdG5SRVhUR1BmNkU4N1VGa3hkTTNYd29xcVQ0VnVjQWQ0VmVHY2dZY1VvNXIyRVBaN2N2cjZLZmllcDhmVjJFeWhjVlpGZ1p1U040eXpkY1VLRVRaWlgwVjhDYi92Skw5M1ZkYXZBQTdENk5BUFQiLCJtYWMiOiI1NjA1YTY2ZGU0OWFlYjc1NDg0ZTkxYjBiZDM3ZDJhZTBmYzFjNTZmYjMwZDFhYTAxN2I3ZDM5YTlmOTQ5NGJjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1092500868\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1287230830 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxDdnBpRWVnM0FNd0twVC9pWkVlVXc9PSIsInZhbHVlIjoiV0UrdTJ4MG0vNlFoR09JRThIQW54bTh3L0s5b1pjOVRLYWJVdVJnSlVwU29Na2RRZC84d2ovMWF0eVM1aUNYd0NabXBXVXdIU0k1SDVOY01leUI3MDRsWnUzbmJUWWhyUXBHZEpXZGd5YjczSEYwTXVYRGwvZ0FudnBuUzlZUjU2Yk9VYkJiY3JBUFU5cHp0TUhVdWQ5M3hFT0t5eVhGRVJSSVZHZklnRnR3c2RXMDIyanpTYW1rWjlISWlwZy9vQWF0cnJxNXBXN0VYVVlpN0JNbHM0UVhOYXFKT3IyMVd3c1pmeDlNb2g0MmQ4T2M5NnhyOVRpK3RGYm8rdXJrUDBTbFBjWkF3Y1F0Syt1eDI2M2NQNTJ3WWFVWGVZNExtZHNiQTY1cEhaOGN6dHhOL3A4QUF0bkx6RWRjVWdiYzZzYmp6akxZY0ZYRjZWTjFUSGxJVHliKzNBVXdmYlZtQWZEZkRVTm1TbFFKcU5xbmxlc0RGUFQ3UkxTNjNIS1lWOWgzeHVDc3V6aGRBd2RZcFA5WmJQcnFOTzBkVTNQMmJPS1lkUmVvZ2tNaTRaUmNuVjN2ZlB4bjhPSEF6Y2pVbGNiY1ZvTXpLcHI2b3d3bVJaVEEzQTV3ZU1DaDNHbFJBTG5teERlWHFaYm5QRk8zWmVVbFNnTGlnd0h6aEJsSzUiLCJtYWMiOiI2MThiYjc2M2EyMWExMWE0ODRmNWE2MTE5N2E0NmEwNzc1ODJlNzZiYTQ3Y2E5MjM1YjFlMmE5NTdjMjNkM2E2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:01 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlMyOFFpbmdFMURqZGRWOHhZVlpEeWc9PSIsInZhbHVlIjoiUll6czV2Njhld2k1WlhONUkwcnRSQTVpd3J2OU9EWW9OclFPN2dTVlJ2bDQvUTNIamlOeEZ4OHVFeE9JU0Vkc3V1V0wxRFNRY0x2VDVvenR6N2JNaDZxcS95dzE5L3M2NWtVRXpMQzc1eEpOVi9aVCt0ZituWG9Sdlk2V0lQU094ZnBNSlhqTDErYUdrUC8vb1dKbE0zTmlEbFNzSzlTbThBQnUxQ01qaXFEMVFmcUpwa2VtUHpmMVkwUmFhUlhhT2tMRUpHU3lDS2l6TCs5NnhLR095VkdCY3NrZXdtb1FxQmJDclFFeWRNSjBSTjE2NjdkVkVaRnR4RlZuUkhTOUhZU1JiZkJGQmlvK0pkaTdIUThuTFFrTHlaaGI4elVPeDdIZzJjbTBzTzlCbXpZZjF6MjZHUksxRkIwd3JMcE9xbGxMR2RyclpaeXFiZ3h3OUROWUwrM3F3SUt5dCt1bWtaOEorVnZ6RlBLM2tORldraWg5c1E4Z05jeHQ1Z0h6VDdwdnpnakFEOWFEdDhHcFVqcVl1ZXc5VHdzWnJ6SVdvK2dSR3QxNmZGZUw3cWZGK2JldnNGa3M4Ty8zYS9zdUd1eFJNRlJOaVpaakgzS2RLbVMrOU5FcHE2NVBhN1NUOTNnYnRoaUdmemZIa0R3aFRBZWJuMWZhT3NTWmVDOGYiLCJtYWMiOiJkNjM3ZWI2MWNiYmM5OWU4ZGEzNWU2YzFjNTg3MDg5ZTA0ODVlN2I3ZjEyN2RhMmRmNWJiY2U4ZDk1NTljOTBiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:01 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxDdnBpRWVnM0FNd0twVC9pWkVlVXc9PSIsInZhbHVlIjoiV0UrdTJ4MG0vNlFoR09JRThIQW54bTh3L0s5b1pjOVRLYWJVdVJnSlVwU29Na2RRZC84d2ovMWF0eVM1aUNYd0NabXBXVXdIU0k1SDVOY01leUI3MDRsWnUzbmJUWWhyUXBHZEpXZGd5YjczSEYwTXVYRGwvZ0FudnBuUzlZUjU2Yk9VYkJiY3JBUFU5cHp0TUhVdWQ5M3hFT0t5eVhGRVJSSVZHZklnRnR3c2RXMDIyanpTYW1rWjlISWlwZy9vQWF0cnJxNXBXN0VYVVlpN0JNbHM0UVhOYXFKT3IyMVd3c1pmeDlNb2g0MmQ4T2M5NnhyOVRpK3RGYm8rdXJrUDBTbFBjWkF3Y1F0Syt1eDI2M2NQNTJ3WWFVWGVZNExtZHNiQTY1cEhaOGN6dHhOL3A4QUF0bkx6RWRjVWdiYzZzYmp6akxZY0ZYRjZWTjFUSGxJVHliKzNBVXdmYlZtQWZEZkRVTm1TbFFKcU5xbmxlc0RGUFQ3UkxTNjNIS1lWOWgzeHVDc3V6aGRBd2RZcFA5WmJQcnFOTzBkVTNQMmJPS1lkUmVvZ2tNaTRaUmNuVjN2ZlB4bjhPSEF6Y2pVbGNiY1ZvTXpLcHI2b3d3bVJaVEEzQTV3ZU1DaDNHbFJBTG5teERlWHFaYm5QRk8zWmVVbFNnTGlnd0h6aEJsSzUiLCJtYWMiOiI2MThiYjc2M2EyMWExMWE0ODRmNWE2MTE5N2E0NmEwNzc1ODJlNzZiYTQ3Y2E5MjM1YjFlMmE5NTdjMjNkM2E2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlMyOFFpbmdFMURqZGRWOHhZVlpEeWc9PSIsInZhbHVlIjoiUll6czV2Njhld2k1WlhONUkwcnRSQTVpd3J2OU9EWW9OclFPN2dTVlJ2bDQvUTNIamlOeEZ4OHVFeE9JU0Vkc3V1V0wxRFNRY0x2VDVvenR6N2JNaDZxcS95dzE5L3M2NWtVRXpMQzc1eEpOVi9aVCt0ZituWG9Sdlk2V0lQU094ZnBNSlhqTDErYUdrUC8vb1dKbE0zTmlEbFNzSzlTbThBQnUxQ01qaXFEMVFmcUpwa2VtUHpmMVkwUmFhUlhhT2tMRUpHU3lDS2l6TCs5NnhLR095VkdCY3NrZXdtb1FxQmJDclFFeWRNSjBSTjE2NjdkVkVaRnR4RlZuUkhTOUhZU1JiZkJGQmlvK0pkaTdIUThuTFFrTHlaaGI4elVPeDdIZzJjbTBzTzlCbXpZZjF6MjZHUksxRkIwd3JMcE9xbGxMR2RyclpaeXFiZ3h3OUROWUwrM3F3SUt5dCt1bWtaOEorVnZ6RlBLM2tORldraWg5c1E4Z05jeHQ1Z0h6VDdwdnpnakFEOWFEdDhHcFVqcVl1ZXc5VHdzWnJ6SVdvK2dSR3QxNmZGZUw3cWZGK2JldnNGa3M4Ty8zYS9zdUd1eFJNRlJOaVpaakgzS2RLbVMrOU5FcHE2NVBhN1NUOTNnYnRoaUdmemZIa0R3aFRBZWJuMWZhT3NTWmVDOGYiLCJtYWMiOiJkNjM3ZWI2MWNiYmM5OWU4ZGEzNWU2YzFjNTg3MDg5ZTA0ODVlN2I3ZjEyN2RhMmRmNWJiY2U4ZDk1NTljOTBiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1287230830\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1515573510 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515573510\", {\"maxDepth\":0})</script>\n"}}