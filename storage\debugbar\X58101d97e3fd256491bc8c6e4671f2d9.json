{"__meta": {"id": "X58101d97e3fd256491bc8c6e4671f2d9", "datetime": "2025-06-27 02:15:07", "utime": 1750990507.049401, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.596399, "end": 1750990507.049415, "duration": 0.4530160427093506, "duration_str": "453ms", "measures": [{"label": "Booting", "start": **********.596399, "relative_start": 0, "end": **********.970781, "relative_end": **********.970781, "duration": 0.37438201904296875, "duration_str": "374ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.970789, "relative_start": 0.3743898868560791, "end": 1750990507.049416, "relative_end": 9.5367431640625e-07, "duration": 0.07862710952758789, "duration_str": "78.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722120, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025810000000000003, "accumulated_duration_str": "25.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.997529, "duration": 0.024820000000000002, "duration_str": "24.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.164}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750990507.034734, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.164, "width_percent": 1.937}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750990507.040585, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.102, "width_percent": 1.898}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1890586897 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1890586897\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1945797261 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1945797261\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1409777344 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409777344\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-776953368 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990505004%7C8%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InpqMkMrbXNRU2t6ZXozSURIL0tHbkE9PSIsInZhbHVlIjoiQzN1b1REa09wcHlhbmJhV25CWHp3OEc0RHVKNERXODZhSjVVdklLNkE1SExVU040YUhSTnlSWXVnNmU0RWthS1d6VnhIT3JnQTlWQTZ4NEhtR3A2bXNTbGl6QTdFaEhXUVg3NmdlQm5EU3lTN0x0RnZPWU5qZ3Rnc2RxWlA0ZVRZRW9UTnlUMDFqQU9Qbkk5RnlpMG4wMmsrazAzNkVqUHlwYm4xYWk3OFYzUkROUWR0Q3pRQ2xMUm9Ha2VQUGl1eDE4NDVDbFdvQ1V2eFlSSzJkMzhsQjdzd25lbVpOMFQ2R2JzM0tvaGpTUUJ5RC9JSEV1WlZ0b1hJd2tjRTVmQ0xSYjRBbFlnUnREbW1JQm1PUUVzTVpUUFRLMWx1QzJjVkZ3cTVVektFV0hTNnlnalpzaGl4NmZuVkRrajhaZUFwcWhKWlBFVlZEdXZ0eTZzdGFLZnBFcXY0R0JUOE5JRWlWNGV5cW8xU1B1RkZ5UXlOYjVvbUFyZEZxVFFnMFQ2OHdtOWMxbFhoUmNkMThub2NXaGdOMkYxWXFjZHZNQVBCT1U0L3E1eDlrZ2FUSmdwZ1prSFZLVGRRU29BK01YcUk0eUJyZ1gzOHgrRW9JYnhMMHhSbEQ0ZVY4SVhDRjhQWTBwWkl1bkhJYVlwbFpZR09sS25nbTRtWTgxWmZPYkQiLCJtYWMiOiI0NTRlMzJjNzQ0YmVjZWQ1YjQzNDdkZmYwZTU2ZThlMTRkMDhkNzU5NTI2Y2I1MzkzMDA0NDdkOWJhMDYwNzJmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZidGpzRWhtRjI0cDhmSlFZTDhoaUE9PSIsInZhbHVlIjoiVm5PQjBwMk5MM04zTS91VTZ3UDB4L01xWWxoQzliSHRNbjB6K0hicHZtUmJFRFJHQjZKNXVESnBtdmliZnpmNWhNME9JdDdOWEdHTHF0ekxGOWQ5R0lPalBzUlNCckJpczIrZ3JvK1pjWjMyOU9UN21uOEdsOUhBS3ZGTCtYVWZMbXFuTnRZUFBXckNzSWcwMFg0bXBPYXIzdHQ5ZnlrbTgrQXZWMjY2eHFzWVVKVGZSV0ZhMW1CMVd3NGZxUVl3aFA4OTQ4bUIwRE96SzRWR09FMm1CMVQ4MjROTFlaWGQ5QWJVbWsvSldiOGhPVG9CeGxWOG1vb21PV3RvVWk5N052djVHekQ2NVNsZk5maUdsZ3FKMzdLNkNFNTNlczlwS2E1QTByTWdQL3JBTzBzbERUV2tPMXVLTjFIRk5ZRys2aGZjMllwbHljRHQvOTFOLzVLWWFBNGt6SERScTI3YmdzcGJka0E3UDRlSUZqQm9GbUJLVDdYNVh2UmdHTXRNc2pSR1E0ZHdYNkYzckVqK3EzRDdFMEozai8vMEswQTNCS2FtOXRSODREZEdVVHpZM0tLaW41SUxUT0ttQzJLOVpyZXZEaGNkOHNJVmQvZ3pIMit3SWxHYTM3QVNLNXBGTUtjaG92UDdja0liekVET1d0ZXFsUkJnWjB0NU4zK2ciLCJtYWMiOiI4YzRkN2E5YWMzMTRkY2NjOGIyMDM4ZmE0OTBiYzk5M2M0N2RkZDU5MDc5NWMyMzA0ZDk2MGU2NTczYjc5Mzc2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-776953368\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-705818596 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-705818596\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1963199300 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ilp6aWpBMm5XZHcxQTEzdjIzWUNaTlE9PSIsInZhbHVlIjoiQ2IwbDlaUGxJT0lFWTdTQUtxVjI2aTlYdndKenBIMTczZmV1WHF4OE82Z0RmbEhFak5yNVpPQWt0WWllUmQ1eXBDV2VvUFlYOEtNVFR0V3JqTUV2TjB6OTdpR0VVSkpLc0wxQVJ4bUhYcjZBcE1OdU5yMFljTktETGhPVi83OE5PL0ZRbjhHK2xDWUVjTURKVDVYMFdaWE1nN0dEem82UUZvK2UwTTFDQ281ZjdHVlA5MFRwd0dGSkQ2NkZrOHlXYXBoMzhkSk1GNmMvN0xzZ3ovVGVJOFZDYjloWkd4bFNMQnhwaXk5YWhxdnhNTGlCY1hsakdtSmZ3N0UzNFJ5aW9QanB0MWFCQVBVcDRDZHB1aHN2bTlZdEEvejl4NnNHVmVqZzQxc0xHOG5CRmh3dDdNVUJhaDc4VkZUa0JKMUc3WnpZWkh1OUI3aGo5QVF4ZUFJTVlxcTFXT1ZwNmxtRytxNm91UmhLekVZeDF1anpKS0JUSm1YQVNUYkc0S2h3MDV5cGRjTWRrUFc4dFVkNU9OQll1alZybUlkUExjU2VTNjZ2Qm9ZQStWRnRRWlcrUEtxeVNSVHhyWDRpdkpxRTN1THY2cjFUK0VvMEdXbVQ2TFJUYTlyWWhxV1V3aFNqcWhSdkVON24wZ2hWQnBCWmo3MytldnpVTGI3dWtTL0QiLCJtYWMiOiJkMDgwNmZkNTdmZmRiMTJiOWVhNmZkOGJkYTJmNTliYWIyZjZiMGY0N2U1ZDU2NGNkZDM3NzE3ODIwMWI0M2E0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:07 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlFHWGVQRjliMzJLVlpTSDEzNm1ZbHc9PSIsInZhbHVlIjoiSmZ3WktUVnpQcDhsYmFNcHowM3pTMHRITDlwWUJTdm4yQTR5RHFKamRFN2wyZlN4a0hwZXlNcTRkYUJjamRwVTdTOVhQaGpPU25QNktsaXJuMHQvOVRDV2tmTFlrY2dOUjJodDlTdkJ4eVowbG1OcjlYOGx2anV0UVhJQW9sSDNLUkV2c0hxSVpuMFJqM0hSalg1aGE2TU1WM2xkZ1V0VDZWQnN0ZExFVWNGQjVlRC9Jc2x3b1lsVWZKaXJvVWlmaUgrVzRLUjNTMDZ1cVBNcHRxY1BzblE0cDdoQWkzdTlJRnFNSlRwcjFkSGhyaFdXS2FETVp6TWdzMVdjQXJWWGN4ZGorTGpnbnBiRmpmMzdRVjZ5Q2IxT0ZDRGdtNDI3SkNyOGdaQkp4aGZpUUxib3pjT0pwR3BxcDZZeHVkS1dCLzBpdHhEKzdQZHgyYjlQMklaQ2hHYzJMOFZoMkR2VWxkZy9EQzVWOVBZaWh6ODA4bVFSZ1I4ZnF1U2tvQjZjbzJXWFFtNXdiVmZMVjJIM1VZWUJLVjRCaUpXeWVRT1MvQlp4bzVpRnJWMTFnV3hkK2VkZXg5dXdaUDJwSFltclZoUTgvMkRXK0hsTS93aGZ2c25iQmRRb01VME96bFhEaUpnd0oyOVNMd2xmR1RGMXBkWkJ5bk1zbWtsSmF2TXMiLCJtYWMiOiIyNDRjODgwNWRiNmI0OWUwNTE1OTQ0NGYzYTg1OTgwYTU3ZjhmZTJmYjkzMGJmNGUyY2NiYTUyOGUzZmU1ZWE2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:07 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ilp6aWpBMm5XZHcxQTEzdjIzWUNaTlE9PSIsInZhbHVlIjoiQ2IwbDlaUGxJT0lFWTdTQUtxVjI2aTlYdndKenBIMTczZmV1WHF4OE82Z0RmbEhFak5yNVpPQWt0WWllUmQ1eXBDV2VvUFlYOEtNVFR0V3JqTUV2TjB6OTdpR0VVSkpLc0wxQVJ4bUhYcjZBcE1OdU5yMFljTktETGhPVi83OE5PL0ZRbjhHK2xDWUVjTURKVDVYMFdaWE1nN0dEem82UUZvK2UwTTFDQ281ZjdHVlA5MFRwd0dGSkQ2NkZrOHlXYXBoMzhkSk1GNmMvN0xzZ3ovVGVJOFZDYjloWkd4bFNMQnhwaXk5YWhxdnhNTGlCY1hsakdtSmZ3N0UzNFJ5aW9QanB0MWFCQVBVcDRDZHB1aHN2bTlZdEEvejl4NnNHVmVqZzQxc0xHOG5CRmh3dDdNVUJhaDc4VkZUa0JKMUc3WnpZWkh1OUI3aGo5QVF4ZUFJTVlxcTFXT1ZwNmxtRytxNm91UmhLekVZeDF1anpKS0JUSm1YQVNUYkc0S2h3MDV5cGRjTWRrUFc4dFVkNU9OQll1alZybUlkUExjU2VTNjZ2Qm9ZQStWRnRRWlcrUEtxeVNSVHhyWDRpdkpxRTN1THY2cjFUK0VvMEdXbVQ2TFJUYTlyWWhxV1V3aFNqcWhSdkVON24wZ2hWQnBCWmo3MytldnpVTGI3dWtTL0QiLCJtYWMiOiJkMDgwNmZkNTdmZmRiMTJiOWVhNmZkOGJkYTJmNTliYWIyZjZiMGY0N2U1ZDU2NGNkZDM3NzE3ODIwMWI0M2E0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:07 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlFHWGVQRjliMzJLVlpTSDEzNm1ZbHc9PSIsInZhbHVlIjoiSmZ3WktUVnpQcDhsYmFNcHowM3pTMHRITDlwWUJTdm4yQTR5RHFKamRFN2wyZlN4a0hwZXlNcTRkYUJjamRwVTdTOVhQaGpPU25QNktsaXJuMHQvOVRDV2tmTFlrY2dOUjJodDlTdkJ4eVowbG1OcjlYOGx2anV0UVhJQW9sSDNLUkV2c0hxSVpuMFJqM0hSalg1aGE2TU1WM2xkZ1V0VDZWQnN0ZExFVWNGQjVlRC9Jc2x3b1lsVWZKaXJvVWlmaUgrVzRLUjNTMDZ1cVBNcHRxY1BzblE0cDdoQWkzdTlJRnFNSlRwcjFkSGhyaFdXS2FETVp6TWdzMVdjQXJWWGN4ZGorTGpnbnBiRmpmMzdRVjZ5Q2IxT0ZDRGdtNDI3SkNyOGdaQkp4aGZpUUxib3pjT0pwR3BxcDZZeHVkS1dCLzBpdHhEKzdQZHgyYjlQMklaQ2hHYzJMOFZoMkR2VWxkZy9EQzVWOVBZaWh6ODA4bVFSZ1I4ZnF1U2tvQjZjbzJXWFFtNXdiVmZMVjJIM1VZWUJLVjRCaUpXeWVRT1MvQlp4bzVpRnJWMTFnV3hkK2VkZXg5dXdaUDJwSFltclZoUTgvMkRXK0hsTS93aGZ2c25iQmRRb01VME96bFhEaUpnd0oyOVNMd2xmR1RGMXBkWkJ5bk1zbWtsSmF2TXMiLCJtYWMiOiIyNDRjODgwNWRiNmI0OWUwNTE1OTQ0NGYzYTg1OTgwYTU3ZjhmZTJmYjkzMGJmNGUyY2NiYTUyOGUzZmU1ZWE2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:07 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1963199300\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1494392367 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1494392367\", {\"maxDepth\":0})</script>\n"}}