
<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('Ledger Summary')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('Ledger Summary')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startPush('head'); ?>
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <style>
        /* تحسينات CSS للأزرار والتعديل المباشر */
        .edit-btn, .save-btn, .cancel-btn {
            margin: 2px;
            padding: 4px 8px;
            font-size: 12px;
        }

        .debit-amount input, .credit-amount input {
            width: 100px;
            text-align: right;
        }

        .table td {
            vertical-align: middle;
        }

        .btn-group-sm > .btn, .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
            line-height: 1.5;
            border-radius: 0.2rem;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startPush('script-page'); ?>
    <script type="text/javascript" src="<?php echo e(asset('js/html2pdf.bundle.min.js')); ?>"></script>
    <script>
        var filename = $('#filename').val();

        function saveAsPDF() {
            var element = document.getElementById('printableArea');
            var opt = {
                margin: 0.3,
                filename: filename,
                image: {
                    type: 'jpeg',
                    quality: 1
                },
                html2canvas: {
                    scale: 4,
                    dpi: 72,
                    letterRendering: true
                },
                jsPDF: {
                    unit: 'in',
                    format: 'A2'
                }
            };
            html2pdf().set(opt).from(element).save();
        }

        // دالة جديدة لتعديل المعاملات مباشرة
        function enableEdit(transactionId) {
            // الكود الجديد - التحقق من صحة معرف المعاملة قبل التعديل
            if (!transactionId || transactionId === 0 || transactionId === '0') {
                alert('<?php echo e(__("Transaction ID is not valid for editing.")); ?>');
                return;
            }

            const row = document.querySelector(`tr[data-transaction-id="${transactionId}"]`);

            // التحقق من أن الصف غير مخصص للتعديل
            if (row && row.getAttribute('data-no-edit') === 'true') {
                alert('<?php echo e(__("This transaction cannot be edited.")); ?>');
                return;
            }
            const debitCell = row.querySelector('.debit-amount');
            const creditCell = row.querySelector('.credit-amount');
            const editBtn = row.querySelector('.edit-btn');
            const saveBtn = row.querySelector('.save-btn');
            const cancelBtn = row.querySelector('.cancel-btn');

            // حفظ القيم الأصلية
            const originalDebit = debitCell.textContent.replace(/[^\d.-]/g, '');
            const originalCredit = creditCell.textContent.replace(/[^\d.-]/g, '');

            debitCell.setAttribute('data-original', originalDebit);
            creditCell.setAttribute('data-original', originalCredit);

            // تحويل إلى حقول إدخال
            debitCell.innerHTML = `<input type="number" class="form-control form-control-sm" value="${originalDebit}" step="0.01" min="0">`;
            creditCell.innerHTML = `<input type="number" class="form-control form-control-sm" value="${originalCredit}" step="0.01" min="0">`;

            // إظهار/إخفاء الأزرار
            editBtn.style.display = 'none';
            saveBtn.style.display = 'inline-block';
            cancelBtn.style.display = 'inline-block';
        }

        function cancelEdit(transactionId) {
            const row = document.querySelector(`tr[data-transaction-id="${transactionId}"]`);
            const debitCell = row.querySelector('.debit-amount');
            const creditCell = row.querySelector('.credit-amount');
            const editBtn = row.querySelector('.edit-btn');
            const saveBtn = row.querySelector('.save-btn');
            const cancelBtn = row.querySelector('.cancel-btn');

            // استرجاع القيم الأصلية
            const originalDebit = debitCell.getAttribute('data-original');
            const originalCredit = creditCell.getAttribute('data-original');

            debitCell.innerHTML = parseFloat(originalDebit).toLocaleString('en-US', {minimumFractionDigits: 2});
            creditCell.innerHTML = parseFloat(originalCredit).toLocaleString('en-US', {minimumFractionDigits: 2});

            // إظهار/إخفاء الأزرار
            editBtn.style.display = 'inline-block';
            saveBtn.style.display = 'none';
            cancelBtn.style.display = 'none';
        }

        function saveTransaction(transactionId) {
            // الكود الجديد - التحقق من صحة معرف المعاملة قبل الحفظ
            if (!transactionId || transactionId === 0 || transactionId === '0') {
                alert('<?php echo e(__("Transaction ID is not valid for saving.")); ?>');
                return;
            }

            const row = document.querySelector(`tr[data-transaction-id="${transactionId}"]`);

            // التحقق من أن الصف غير مخصص للتعديل
            if (row && row.getAttribute('data-no-edit') === 'true') {
                alert('<?php echo e(__("This transaction cannot be saved.")); ?>');
                return;
            }
            const debitInput = row.querySelector('.debit-amount input');
            const creditInput = row.querySelector('.credit-amount input');

            const debitValue = parseFloat(debitInput.value) || 0;
            const creditValue = parseFloat(creditInput.value) || 0;

            // إرسال البيانات عبر AJAX
            // الكود الجديد - التحقق من وجود CSRF token قبل الإرسال
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (!csrfToken) {
                alert('<?php echo e(__("CSRF token not found. Please refresh the page.")); ?>');
                return;
            }

            console.log('Sending request with data:', {
                transaction_id: transactionId,
                debit: debitValue,
                credit: creditValue
            });

            fetch('<?php echo e(route("report.ledger.update")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                },
                body: JSON.stringify({
                    transaction_id: transactionId,
                    debit: debitValue,
                    credit: creditValue
                })
            })
            .then(response => {
                // الكود الجديد - تحسين معالجة الاستجابة مع تسجيل مفصل
                console.log('Response Status:', response.status);
                console.log('Response Headers:', response.headers);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                return response.json();
            })
            .then(data => {
                // الكود الجديد - تسجيل مفصل للاستجابة
                console.log('Server Response Data:', data);
                if (data.success) {
                    // تحديث العرض
                    const debitCell = row.querySelector('.debit-amount');
                    const creditCell = row.querySelector('.credit-amount');
                    const editBtn = row.querySelector('.edit-btn');
                    const saveBtn = row.querySelector('.save-btn');
                    const cancelBtn = row.querySelector('.cancel-btn');

                    debitCell.innerHTML = parseFloat(debitValue).toLocaleString('en-US', {minimumFractionDigits: 2});
                    creditCell.innerHTML = parseFloat(creditValue).toLocaleString('en-US', {minimumFractionDigits: 2});

                    // إظهار/إخفاء الأزرار
                    editBtn.style.display = 'inline-block';
                    saveBtn.style.display = 'none';
                    cancelBtn.style.display = 'none';

                    // إظهار رسالة نجاح
                    alert('<?php echo e(__("Transaction updated successfully!")); ?>');

                    // إعادة تحميل الصفحة لتحديث الأرصدة
                    location.reload();
                } else {
                    alert('<?php echo e(__("Error: ")); ?>' + data.message);
                }
            })
            .catch(error => {
                // الكود القديم - معالجة أخطاء بسيطة
                // console.error('Error:', error);
                // alert('<?php echo e(__("An error occurred while updating the transaction.")); ?>');

                // الكود الجديد - معالجة أخطاء مفصلة لتسهيل التشخيص
                console.error('Full Error Object:', error);
                console.error('Error Details:', {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                });

                // محاولة قراءة تفاصيل الخطأ من الاستجابة
                if (error.response) {
                    error.response.text().then(text => {
                        console.error('Server Response:', text);
                        try {
                            const jsonResponse = JSON.parse(text);
                            console.error('Parsed Response:', jsonResponse);
                            if (jsonResponse.debug_info) {
                                console.error('Debug Info:', jsonResponse.debug_info);
                            }
                        } catch (parseError) {
                            console.error('Could not parse server response as JSON');
                        }
                    });
                }

                alert('<?php echo e(__("An error occurred while updating the transaction. Check console for details.")); ?>');
            });
        }
    </script>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('action-btn'); ?>
    <div class="float-end">
        
        
        

        <a href="#" class="btn btn-sm btn-primary" onclick="saveAsPDF()"data-bs-toggle="tooltip"
            title="<?php echo e(__('Download')); ?>" data-original-title="<?php echo e(__('Download')); ?>">
            <span class="btn-inner--icon"><i class="ti ti-download"></i></span>
        </a>

    </div>
<?php $__env->stopSection(); ?>

<?php
        $selectAcc =     [[
    "id" => 0,
    "code" => '',
    "name" => "Select",
    "parent" => 0,
]];
       $accounts =  array_merge($selectAcc, $accounts);
?>
<?php $__env->startSection('content'); ?>
    <div class="row">
        <div class="col-sm-12">
            <div class="mt-2 " id="multiCollapseExample1">
                <div class="card">
                    <div class="card-body">
                        <?php echo e(Form::open(['route' => ['report.ledger'], 'method' => 'GET', 'id' => 'report_ledger'])); ?>


                        <div class="row align-items-center justify-content-end">
                            <div class="col-xl-10">
                                <div class="row">
                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                        </div>
                                    </div>
                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                            <?php echo e(Form::label('start_date', __('Start Date'), ['class' => 'form-label'])); ?>

                                            <?php echo e(Form::date('start_date', $filter['startDateRange'], ['class' => 'month-btn form-control'])); ?>

                                        </div>
                                    </div>
                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                            <?php echo e(Form::label('end_date', __('End Date'), ['class' => 'form-label'])); ?>

                                            <?php echo e(Form::date('end_date', $filter['endDateRange'], ['class' => 'month-btn form-control'])); ?>

                                        </div>
                                    </div>



                                    <div class="col-xl-3 col-lg-3 col-md-6 col-sm-12 col-12">
                                        <div class="btn-box">
                                            <?php echo e(Form::label('account', __('Account'), ['class' => 'form-label'])); ?>

                                            
                                            <select name="account" class="form-control" required="required">
                                                <?php $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chartAccount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($chartAccount['id']); ?>" class="subAccount" <?php echo e(isset($_GET['account']) && $chartAccount['id'] == $_GET['account'] ? 'selected' : ''); ?>><?php echo e($chartAccount['name']); ?></option>
                                                    <?php $__currentLoopData = $subAccounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subAccount): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <?php if($chartAccount['id'] == $subAccount['account']): ?>
                                                            <option value="<?php echo e($subAccount['id']); ?>" class="ms-5" <?php echo e(isset($_GET['account']) && $_GET['account'] == $subAccount['id'] ? 'selected' : ''); ?>> &nbsp; &nbsp;&nbsp; <?php echo e($subAccount['name']); ?></option>
                                                        <?php endif; ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="row">
                                    <div class="col-auto mt-4">
                                        <a href="#" class="btn btn-sm btn-primary me-1"
                                            onclick="document.getElementById('report_ledger').submit(); return false;"
                                            data-bs-toggle="tooltip" title="<?php echo e(__('Apply')); ?>"
                                            data-original-title="<?php echo e(__('apply')); ?>">
                                            <span class="btn-inner--icon"><i class="ti ti-search"></i></span>
                                        </a>
                                        <a href="<?php echo e(route('report.ledger')); ?>" class="btn btn-sm btn-danger "
                                            data-bs-toggle="tooltip" title="<?php echo e(__('Reset')); ?>"
                                            data-original-title="<?php echo e(__('Reset')); ?>">
                                            <span class="btn-inner--icon"><i
                                                    class="ti ti-refresh text-white-off "></i></span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php echo e(Form::close()); ?>

                </div>
            </div>
        </div>
    </div>



    <div id="printableArea">
        
        
        <div class="row mb-4">
            <div class="col-12 mb-4">
                <div class="card">
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th> <?php echo e(__('Account Name')); ?></th>
                                        <th> <?php echo e(__('Name')); ?></th>
                                        <th> <?php echo e(__('Transaction Type')); ?></th>
                                        <th> <?php echo e(__('Transaction Date')); ?></th>
                                        <th> <?php echo e(__('Debit')); ?></th>
                                        <th> <?php echo e(__('Credit')); ?></th>
                                        <th> <?php echo e(__('Balance')); ?></th>
                                        
                                        <th> <?php echo e(__('Actions')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                        $balance = 0;
                                        $totalDebit = 0;
                                        $totalCredit = 0;

                                        $accountArrays = [];
                                        foreach ($chart_accounts as $key => $account) {
                                            $chartDatas = App\Models\Utility::getAccountData($account['id'], $filter['startDateRange'], $filter['endDateRange']);

                                            $chartDatas = $chartDatas->toArray();
                                            $accountArrays[] = $chartDatas;
                                        }
                                    ?>

                                    <?php $__currentLoopData = $accountArrays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $accounts): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php $__currentLoopData = $accounts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $account): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php if($account->reference == 'Invoice'): ?>
                                                
                                                

                                                
                                                
                                                

                                                
                                                <tr data-transaction-id="<?php echo e($account->transaction_line_id ?? $account->id ?? 0); ?>"
                                                    <?php if(!isset($account->transaction_line_id) || empty($account->transaction_line_id)): ?>
                                                        data-no-edit="true" title="<?php echo e(__('Transaction ID not available for editing')); ?>"
                                                    <?php endif; ?>>
                                                    <td><?php echo e($account->account_name); ?></td>
                                                    <td><?php echo e($account->user_name); ?></td>
                                                    <td><?php echo e(\Auth::user()->invoiceNumberFormat($account->ids)); ?></td>
                                                    <td><?php echo e($account->date); ?></td>
                                                    <td class="debit-amount"><?php echo e(\Auth::user()->priceFormat($account->debit)); ?></td>
                                                    <?php
                                                        $total = $account->debit + $account->credit;
                                                        $balance += $total;
                                                        $totalCredit += $total;
                                                    ?>
                                                    <td class="credit-amount"><?php echo e(\Auth::user()->priceFormat($account->credit)); ?></td>
                                                    <td><?php echo e(\Auth::user()->priceFormat($balance)); ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                title="<?php echo e(__('Edit Transaction')); ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Save Changes')); ?>">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Cancel')); ?>">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>

                                            <?php if($account->reference == 'Invoice Payment'): ?>
                                                
                                                

                                                
                                                <tr data-transaction-id="<?php echo e($account->transaction_line_id ?? $account->id); ?>">
                                                    <td><?php echo e($account->account_name); ?></td>
                                                    <td><?php echo e($account->user_name); ?></td>
                                                    <td><?php echo e(\Auth::user()->invoiceNumberFormat($account->ids)); ?><?php echo e(__(' Manually Payment')); ?></td>
                                                    <td><?php echo e($account->date); ?></td>
                                                    <td class="debit-amount"><?php echo e(\Auth::user()->priceFormat($account->debit)); ?></td>
                                                    <?php
                                                        $total = $account->debit + $account->credit;
                                                        $balance -= $total;
                                                    ?>
                                                    <td class="credit-amount"><?php echo e(\Auth::user()->priceFormat($account->credit)); ?></td>
                                                    <td><?php echo e(\Auth::user()->priceFormat($balance)); ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                title="<?php echo e(__('Edit Transaction')); ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Save Changes')); ?>">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Cancel')); ?>">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>

                                            <?php if($account->reference == 'Revenue'): ?>
                                                
                                                

                                                
                                                <tr data-transaction-id="<?php echo e($account->transaction_line_id ?? $account->id); ?>">
                                                    <td><?php echo e($account->account_name); ?></td>
                                                    <td><?php echo e($account->user_name); ?></td>
                                                    <td><?php echo e(__(' Revenue')); ?></td>
                                                    <td><?php echo e($account->date); ?></td>
                                                    <td class="debit-amount"><?php echo e(\Auth::user()->priceFormat($account->debit)); ?></td>
                                                    <?php
                                                        $total = $account->debit + $account->credit;
                                                        $balance += $total;
                                                    ?>
                                                    <td class="credit-amount"><?php echo e(\Auth::user()->priceFormat($account->credit)); ?></td>
                                                    <td><?php echo e(\Auth::user()->priceFormat($balance)); ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                title="<?php echo e(__('Edit Transaction')); ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Save Changes')); ?>">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Cancel')); ?>">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>

                                             <?php if(
                                                $account->reference == 'Bill' ||
                                                    $account->reference == 'Bill Account' ||
                                                    $account->reference == 'Expense' ||
                                                    $account->reference == 'Expense Account'): ?>
                                                
                                                

                                                
                                                <tr data-transaction-id="<?php echo e($account->transaction_line_id ?? $account->id); ?>">
                                                    <td><?php echo e($account->account_name); ?></td>
                                                    <td><?php echo e($account->user_name); ?></td>
                                                    <?php if($account->reference == 'Bill' || $account->reference == 'Bill Account'): ?>
                                                        <td><?php echo e(\Auth::user()->billNumberFormat($account->ids)); ?></td>
                                                        <?php else: ?>
                                                        <td><?php echo e(\Auth::user()->expenseNumberFormat($account->ids)); ?></td>
                                                    <?php endif; ?>
                                                    <td><?php echo e($account->date); ?></td>
                                                    <td class="debit-amount"><?php echo e(\Auth::user()->priceFormat($account->debit)); ?></td>
                                                    <?php
                                                        $total = $account->debit + $account->credit;
                                                        $balance -= $total;
                                                    ?>
                                                    <td class="credit-amount"><?php echo e(\Auth::user()->priceFormat($account->credit)); ?></td>
                                                    <td><?php echo e(\Auth::user()->priceFormat($balance)); ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                title="<?php echo e(__('Edit Transaction')); ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Save Changes')); ?>">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Cancel')); ?>">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>

                                            <?php if($account->reference == 'Bill Payment' || $account->reference == 'Expense Payment'): ?>
                                                
                                                

                                                
                                                <tr data-transaction-id="<?php echo e($account->transaction_line_id ?? $account->id); ?>">
                                                    <td><?php echo e($account->account_name); ?></td>
                                                    <td><?php echo e($account->user_name); ?></td>
                                                    <?php if($account->reference == 'Bill Payment'): ?>
                                                        <td><?php echo e(\Auth::user()->billNumberFormat($account->ids)); ?><?php echo e(__(' Manually Payment')); ?></td>
                                                        <?php else: ?>
                                                        <td><?php echo e(\Auth::user()->expenseNumberFormat($account->ids)); ?><?php echo e(__(' Manually Payment')); ?></td>
                                                    <?php endif; ?>
                                                    <td><?php echo e($account->date); ?></td>
                                                    <td class="debit-amount"><?php echo e(\Auth::user()->priceFormat($account->debit)); ?></td>
                                                    <?php
                                                        $total = $account->debit + $account->credit;
                                                        $balance -= $total;
                                                    ?>
                                                    <td class="credit-amount"><?php echo e(\Auth::user()->priceFormat($account->credit)); ?></td>
                                                    <td><?php echo e(\Auth::user()->priceFormat($balance)); ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                title="<?php echo e(__('Edit Transaction')); ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Save Changes')); ?>">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Cancel')); ?>">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>

                                            <?php if($account->reference == 'Payment'): ?>
                                                
                                                

                                                
                                                <tr data-transaction-id="<?php echo e($account->transaction_line_id ?? $account->id); ?>">
                                                    <td><?php echo e($account->account_name); ?></td>
                                                    <td><?php echo e($account->user_name); ?></td>
                                                    <td><?php echo e(__('Payment')); ?></td>
                                                    <td><?php echo e($account->date); ?></td>
                                                    <td class="debit-amount"><?php echo e(\Auth::user()->priceFormat($account->debit)); ?></td>
                                                    <?php
                                                        $total = $account->debit + $account->credit;
                                                        $balance -= $total;
                                                    ?>
                                                    <td class="credit-amount"><?php echo e(\Auth::user()->priceFormat($account->credit)); ?></td>
                                                    <td><?php echo e(\Auth::user()->priceFormat($balance)); ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                title="<?php echo e(__('Edit Transaction')); ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Save Changes')); ?>">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Cancel')); ?>">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>

                                            <?php if($account->reference == 'POS'): ?>
                                                
                                                

                                                
                                                <tr data-transaction-id="<?php echo e($account->transaction_line_id ?? $account->id); ?>">
                                                    <td><?php echo e($account->account_name); ?></td>
                                                    <td><?php echo e($account->user_name); ?></td>
                                                    <td><?php echo e(\Auth::user()->posNumberFormat($account->ids)); ?></td>
                                                    <td><?php echo e($account->date); ?></td>
                                                    <td class="debit-amount"><?php echo e(\Auth::user()->priceFormat($account->debit)); ?></td>
                                                    <?php
                                                        $total = $account->debit + $account->credit;
                                                        $balance += $total;
                                                    ?>
                                                    <td class="credit-amount"><?php echo e(\Auth::user()->priceFormat($account->credit)); ?></td>
                                                    <td><?php echo e(\Auth::user()->priceFormat($balance)); ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                title="<?php echo e(__('Edit Transaction')); ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Save Changes')); ?>">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Cancel')); ?>">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>

                                            <?php if($account->reference == 'EXP'): ?>
                                                
                                                

                                                
                                                <tr data-transaction-id="<?php echo e($account->transaction_line_id ?? $account->id); ?>">
                                                    <td><?php echo e($account->account_name); ?></td>
                                                    <td><?php echo e($account->user_name); ?></td>
                                                    <td><?php echo e(\Auth::user()->expenseNumberFormat($account->ids)); ?></td>
                                                    <td><?php echo e($account->date); ?></td>
                                                    <td class="debit-amount"><?php echo e(\Auth::user()->priceFormat($account->debit)); ?></td>
                                                    <?php
                                                        $total = $account->debit + $account->credit;
                                                        $balance -= $total;
                                                    ?>
                                                    <td class="credit-amount"><?php echo e(\Auth::user()->priceFormat($account->credit)); ?></td>
                                                    <td><?php echo e(\Auth::user()->priceFormat($balance)); ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                title="<?php echo e(__('Edit Transaction')); ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Save Changes')); ?>">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Cancel')); ?>">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>

                                            <?php if($account->reference == 'Journal'): ?>
                                                
                                                

                                                
                                                <tr data-transaction-id="<?php echo e($account->transaction_line_id ?? $account->id); ?>">
                                                    <td><?php echo e($account->account_name); ?></td>
                                                    <td><?php echo e('-'); ?></td>
                                                    <td><?php echo e(AUth::user()->journalNumberFormat($account->reference_id)); ?></td>
                                                    <td><?php echo e($account->date); ?></td>
                                                    <td class="debit-amount"><?php echo e(\Auth::user()->priceFormat($account->debit)); ?></td>
                                                    <?php
                                                        $total = $account->credit - $account->debit;
                                                        $balance += $total;
                                                    ?>
                                                    <td class="credit-amount"><?php echo e(\Auth::user()->priceFormat($account->credit)); ?></td>
                                                    <td><?php echo e(\Auth::user()->priceFormat($balance)); ?></td>
                                                    <td>
                                                        <button type="button" class="btn btn-sm btn-primary edit-btn"
                                                                onclick="enableEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                title="<?php echo e(__('Edit Transaction')); ?>">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-success save-btn"
                                                                onclick="saveTransaction(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Save Changes')); ?>">
                                                            <i class="fas fa-save"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-sm btn-secondary cancel-btn"
                                                                onclick="cancelEdit(<?php echo e($account->transaction_line_id ?? $account->id); ?>)"
                                                                style="display: none;"
                                                                title="<?php echo e(__('Cancel')); ?>">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\erpq24\resources\views/report/ledger_summary.blade.php ENDPATH**/ ?>