<?php

// <PERSON>ript to setup delivery permissions and roles
require_once 'vendor/autoload.php';

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);
$kernel->bootstrap();

use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\User;

echo "=== Setting up Delivery Permissions ===\n\n";

// 1. Create delivery permissions if they don't exist
echo "1. Creating delivery permissions...\n";
$deliveryPermissions = [
    'manage delevery',
    'show delevery', 
    'create delevery',
    'edit delevery',
    'delete delevery'
];

foreach ($deliveryPermissions as $permissionName) {
    $permission = Permission::firstOrCreate([
        'name' => $permissionName,
        'guard_name' => 'web'
    ]);
    
    if ($permission->wasRecentlyCreated) {
        echo "   ✓ Created permission: $permissionName\n";
    } else {
        echo "   - Permission already exists: $permissionName\n";
    }
}

echo "\n";

// 2. Create or update Delivery role
echo "2. Setting up Delivery role...\n";
$deliveryRole = Role::firstOrCreate([
    'name' => 'Delivery',
    'guard_name' => 'web'
]);

if ($deliveryRole->wasRecentlyCreated) {
    echo "   ✓ Created Delivery role\n";
} else {
    echo "   - Delivery role already exists\n";
}

// Assign permissions to Delivery role
$deliveryRole->syncPermissions($deliveryPermissions);
echo "   ✓ Assigned all delivery permissions to Delivery role\n";

echo "\n";

// 3. Update company role with delivery permissions
echo "3. Updating company role...\n";
$companyRole = Role::firstOrCreate([
    'name' => 'company',
    'guard_name' => 'web'
]);

// Get existing permissions and add delivery permissions
$existingPermissions = $companyRole->permissions()->pluck('name')->toArray();
$allPermissions = array_unique(array_merge($existingPermissions, $deliveryPermissions));
$companyRole->syncPermissions($allPermissions);
echo "   ✓ Added delivery permissions to company role\n";

echo "\n";

// 4. Create a test delivery user if needed
echo "4. Checking for delivery users...\n";
$deliveryUsers = User::whereHas('roles', function($query) {
    $query->where('name', 'Delivery');
})->get();

if ($deliveryUsers->count() == 0) {
    echo "   No delivery users found. You may need to:\n";
    echo "   - Create a user with type 'delivery'\n";
    echo "   - Assign the 'Delivery' role to existing users\n";
    echo "   - Or assign delivery permissions directly to users\n";
} else {
    echo "   ✓ Found {$deliveryUsers->count()} delivery users\n";
    foreach ($deliveryUsers as $user) {
        echo "     - {$user->name} (ID: {$user->id})\n";
    }
}

echo "\n";

// 5. Show summary
echo "5. Summary:\n";
echo "   Permissions created: " . count($deliveryPermissions) . "\n";
echo "   Delivery role: ✓ Ready\n";
echo "   Company role: ✓ Updated\n";

echo "\n=== Setup Complete ===\n";
echo "You can now:\n";
echo "1. Assign the 'Delivery' role to users who should have delivery access\n";
echo "2. Check the delivery icon in POS interface\n";
echo "3. Test delivery functionality\n\n";
