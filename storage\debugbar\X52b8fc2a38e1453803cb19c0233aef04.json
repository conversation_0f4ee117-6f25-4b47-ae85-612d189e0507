{"__meta": {"id": "X52b8fc2a38e1453803cb19c0233aef04", "datetime": "2025-06-27 02:34:34", "utime": **********.379798, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991673.94851, "end": **********.379811, "duration": 0.4313011169433594, "duration_str": "431ms", "measures": [{"label": "Booting", "start": 1750991673.94851, "relative_start": 0, "end": **********.298642, "relative_end": **********.298642, "duration": 0.3501319885253906, "duration_str": "350ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.29865, "relative_start": 0.3501400947570801, "end": **********.379814, "relative_end": 2.86102294921875e-06, "duration": 0.08116388320922852, "duration_str": "81.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45737176, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00313, "accumulated_duration_str": "3.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.352051, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.939}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.363154, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.939, "width_percent": 18.53}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.369953, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.47, "width_percent": 18.53}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/22\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1840366971 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1840366971\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1070260157 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1070260157\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1510275179 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1510275179\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-911050524 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991671494%7C44%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNvTkE0RDgzcDB2czVBTjZDYWVscEE9PSIsInZhbHVlIjoiQ3YwanV6K3dxRnpGdjNPc3pVTVNFRE9TZWJpcU5mRVRoM1BxRUoxcFhzemwvYWxkWlRKa0FpNWtLUHpYSkh2S21yVWY3V0o1Vm9KNGlpaDl2Zmh6QWltRjl0QzdLM25MTFpsVkhyWXE3Mm1WTmQ4SGkxZDlKYnkvdllSNXhCckZFeExxaFhUdWFOOGtjamoxV3BPMXQ1eGdyTkt4cDliKzNUcFNPVmtXTUFPdTJQZjNSRno4V0VZUkhNdEZaa1ptVUlNck5BdW1paE1ZWi96NjZsdGxST0pONktWZGR6MCs4bWxQUDBsUFlldGIrWG5DRmVzMXNZb2xPNkRXbHNKMjVpbm5wKzAwdXY4SWs0WWUxRERPZ00zYjk2bnRpU2grTFViZDVTckVqWkFDYjY2MlV0aDlZK1I3c0ZoazdKYWpUdkU3azdtbStCd3EydG1RWFBKN2tTaWdFUk5QOVp3SDJMdXByUEd1ZHpLNU9hTWJQdGxhRW5hLzhDUnJHNUNZV0RrYVBjZFRielZiMjV3Sm9YbWc1VVRiU0xhQjg1dDEyZ1E4SHBuUVBKVDhMclNWZzJqN3M0Z3pScVRkSkVOUjlCUkpndnVzR3kwalJmZy9xK2ptc1N0Si9RdCt3dFdGeVFOblBRUnRtQnl0aEsvcGEwK3RsSWI3WXJzUEQ4YVkiLCJtYWMiOiJkMTljYWNiNmVlMTdkNzNhYzk3NjFmYzRhNzRlNWQ1Yzg3MDFlODA1YzY0MTg3M2VmZmUxMTIyMWUxNGMyYWEwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImkyZ0F2K21KbmsvOUQ1WG0zeWsxRFE9PSIsInZhbHVlIjoiUzNWVDRMT3RzV0dLcFlseVZNaHRVRXF2aHlmZzdMZk5HQUtXL283djEyNllsZTdGMmdRWHIyUHQ2bERtSkNGMnYyV2hHS0FPbTBIWkRvOE9ZQjIxalJBWGNUOEVDN1VLeHU3ZzIveVJJeUNoVHRpeGZUY200c3oyMHNINkZMaHREeU5vcGxpclpGalJjTmJrK0JqQmkxSzdrcVFzYU9jdktwQ25wVmNiaTZHU2NadVU3RWVhY2l3a0hnVGkvZ3lDVFVNMm1FY2pNaGU2a3o5aUdxVHNzVDB2K2VXNlc1cHpLRndsZnVtcDZJcndjTU5oRm8xa29xa09YRi9WRXdMa2NHbGZxRlRRaXViOVcxY25McVFIelBuaGdHamt5ZnM1TjdCODArQTF4Y0xSMC9jdWt1aU1RL2FNQnV0QUtyZ2p0UVh5R1FZK25GOHZjQXFoby9BMFlCS2hrM2ExVWUyMkVRSFlVUFlPeFM4dWhTZFdNWFVJWUZ6bEhKcmV4MFJ1UlpvR01qNmd2UzVrYXQrbFRuakVHZ3JaTjN0Tk5IREI0VnVQRmUzb2hLWUdGTzFvcXRRK3FPODBCMHR5U093Tk1qNXZFYVJjL1l2TWE2djdpMTNSZUFkbmg2NERZa3pQNHhKeDUzZHNnSlBJVU1mYkpUVFNHeklJYTNFZ2RXc0MiLCJtYWMiOiI4Y2MwYWE2OTU5MWZjMjg5MzdkNDEyZGI1NWE4MWM2OWM1NGE5OWRlNTRkN2Q3ZjMzZGQxN2E5MDJhNTE5MzRjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911050524\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-318350270 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-318350270\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-953805738 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImpRMm9WNGxiNzVFbnFHMk9qRUp1RUE9PSIsInZhbHVlIjoiSHVmZ2pQcFFZZFlqRVYxUFAwQ2U1UmtJYjBRb3h1cEQ2di9jSk4rTkUrQWxFaWU4L1ROOWtBSzFadjRrcGdlblJMVWFWSkFPcWQ2elpCQ0wrNkJqT2hwQU16dDRrRkJCQTRyaURnM3M3dlpuQjFDS1dIQlQvMW9mTEhKV3RPTnV0WTF5dWpvK0xyNm84cTd5VHFiR2NGRHJ4c3FoRGVEOGhsUWViQWk2YlZmd1hkTUZ3Wms4Ung0Umd3RzZXK2hFMXhWSWJkb1V5VjZyUDcrSW9TaHZxVjA2bU13NlJKZkxLK21nSUphV0xXWVdNY3QzaDZucVNqMmtFcUFFMUNqN05UcnhuUU9GU1Y0NnFQZSszYTYrVzVVUmZVekZSN1dDWmNqTmZCK3ZxVHR3TlQwb0JVRERoTFBzYlczei9rWG1VeVJ3eVRob0U5bFhKQXprT3MxSFBGTDlxdnNnUXRSb0cvbWVud1NEckc0VDJ3cnNkeVYzL0lIR0FmTVNPQkFsZmU2QkZnekI4WkRTUkN0dHhIYytLL1VDbm9YUUZtYUdTU2NQeWxlR2h3aEZhY2duK2xmVlR3Y2VvSEVIOGpKUmx6WVdEVUd0cVVRUHU4YTdJdDJRZ3BzdU9tZlVxVFBOdHFFNEpDQnBDcExoQkhWQ1hjR25ZUjFMQ0hXenRBUDIiLCJtYWMiOiIwOGMzY2IzZDYwN2E3YjY3NmNhZjRmZTA3NDcxNzZlMjBjOWE3NzY5NjRlNTc5ZDNhMjJlNDE4Y2FjMTk4MWFlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjVFWUoyTGhaQVIvL2JMNzE4bXRVMWc9PSIsInZhbHVlIjoiN0RxYTBZUldvNUZ6LzhMRVJORFVuSGlrVHNWWGdIMkcwdGh6NUVieDF1SGo3OE5sNWVLL2lVREJpeXZQdjVJaW1sU1V1WFMwOFpHc1ZkcEhaWmRyWW5idnBlbVByUXp5RmRWVHA1VmR1cjlRemtvMkkyU0x6R2pBSFpzaDJzL0JUbmtvWEZqdzJaYnFNUlhjUXFVLzVHVlJlaWFQNGdoWXlMMVo5MUg1NlkzNWNsM2F4WXJ5ZmQ3YmhpOXUxWktGVUhHaElubFhQRUllQlVFQzBtYzdvamFyMiszNFZhN0dkb3ltQlcxNlJiMVM0Q0sxZVpCdElLRURTOUNQdVl2b0EwTzdWWHp6WjhnamdsY0lUZlQ0aUV4MEcwZEVkTmtCN2ZpSk8rbGQwb241Y28vd2ltQTlCbndBSjVmMHNqWXR4Ujh3MU1sVllwbFRyQVMrUm5BUUJsdWlhRjlaa3F4U1JmSVB5ejNoSXBNWFQvSnlZNXlleWdUeWVpaWlLbzJ4aE5EMHBYeHdQNGxUSmR5c3hid3FBcTJVWENEVXZoRDN0Y1RpWExVS2FvMiswTE16ZWgvK0Npb28yTlV2MTlvenU5YzVOME9BaEwyYlY1ZmhlMCtSUW0zOFdMdkdDV2RrNThUNzBWbVhtTHZEL0hQRDdBK3NIaTZjcjUyL3NZM0ciLCJtYWMiOiIxNDk5MDQ5M2ZhNjAzNDRkZGUyNTA1OTNlNDg2MGM4MmYxODk3OGNmN2FlZjkwYjFhNWViOTEwYzJlZmRiYTViIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImpRMm9WNGxiNzVFbnFHMk9qRUp1RUE9PSIsInZhbHVlIjoiSHVmZ2pQcFFZZFlqRVYxUFAwQ2U1UmtJYjBRb3h1cEQ2di9jSk4rTkUrQWxFaWU4L1ROOWtBSzFadjRrcGdlblJMVWFWSkFPcWQ2elpCQ0wrNkJqT2hwQU16dDRrRkJCQTRyaURnM3M3dlpuQjFDS1dIQlQvMW9mTEhKV3RPTnV0WTF5dWpvK0xyNm84cTd5VHFiR2NGRHJ4c3FoRGVEOGhsUWViQWk2YlZmd1hkTUZ3Wms4Ung0Umd3RzZXK2hFMXhWSWJkb1V5VjZyUDcrSW9TaHZxVjA2bU13NlJKZkxLK21nSUphV0xXWVdNY3QzaDZucVNqMmtFcUFFMUNqN05UcnhuUU9GU1Y0NnFQZSszYTYrVzVVUmZVekZSN1dDWmNqTmZCK3ZxVHR3TlQwb0JVRERoTFBzYlczei9rWG1VeVJ3eVRob0U5bFhKQXprT3MxSFBGTDlxdnNnUXRSb0cvbWVud1NEckc0VDJ3cnNkeVYzL0lIR0FmTVNPQkFsZmU2QkZnekI4WkRTUkN0dHhIYytLL1VDbm9YUUZtYUdTU2NQeWxlR2h3aEZhY2duK2xmVlR3Y2VvSEVIOGpKUmx6WVdEVUd0cVVRUHU4YTdJdDJRZ3BzdU9tZlVxVFBOdHFFNEpDQnBDcExoQkhWQ1hjR25ZUjFMQ0hXenRBUDIiLCJtYWMiOiIwOGMzY2IzZDYwN2E3YjY3NmNhZjRmZTA3NDcxNzZlMjBjOWE3NzY5NjRlNTc5ZDNhMjJlNDE4Y2FjMTk4MWFlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjVFWUoyTGhaQVIvL2JMNzE4bXRVMWc9PSIsInZhbHVlIjoiN0RxYTBZUldvNUZ6LzhMRVJORFVuSGlrVHNWWGdIMkcwdGh6NUVieDF1SGo3OE5sNWVLL2lVREJpeXZQdjVJaW1sU1V1WFMwOFpHc1ZkcEhaWmRyWW5idnBlbVByUXp5RmRWVHA1VmR1cjlRemtvMkkyU0x6R2pBSFpzaDJzL0JUbmtvWEZqdzJaYnFNUlhjUXFVLzVHVlJlaWFQNGdoWXlMMVo5MUg1NlkzNWNsM2F4WXJ5ZmQ3YmhpOXUxWktGVUhHaElubFhQRUllQlVFQzBtYzdvamFyMiszNFZhN0dkb3ltQlcxNlJiMVM0Q0sxZVpCdElLRURTOUNQdVl2b0EwTzdWWHp6WjhnamdsY0lUZlQ0aUV4MEcwZEVkTmtCN2ZpSk8rbGQwb241Y28vd2ltQTlCbndBSjVmMHNqWXR4Ujh3MU1sVllwbFRyQVMrUm5BUUJsdWlhRjlaa3F4U1JmSVB5ejNoSXBNWFQvSnlZNXlleWdUeWVpaWlLbzJ4aE5EMHBYeHdQNGxUSmR5c3hid3FBcTJVWENEVXZoRDN0Y1RpWExVS2FvMiswTE16ZWgvK0Npb28yTlV2MTlvenU5YzVOME9BaEwyYlY1ZmhlMCtSUW0zOFdMdkdDV2RrNThUNzBWbVhtTHZEL0hQRDdBK3NIaTZjcjUyL3NZM0ciLCJtYWMiOiIxNDk5MDQ5M2ZhNjAzNDRkZGUyNTA1OTNlNDg2MGM4MmYxODk3OGNmN2FlZjkwYjFhNWViOTEwYzJlZmRiYTViIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953805738\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-528276969 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528276969\", {\"maxDepth\":0})</script>\n"}}