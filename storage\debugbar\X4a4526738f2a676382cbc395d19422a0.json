{"__meta": {"id": "X4a4526738f2a676382cbc395d19422a0", "datetime": "2025-06-27 00:40:49", "utime": **********.645438, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.232024, "end": **********.645452, "duration": 0.41342806816101074, "duration_str": "413ms", "measures": [{"label": "Booting", "start": **********.232024, "relative_start": 0, "end": **********.596098, "relative_end": **********.596098, "duration": 0.3640739917755127, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.596109, "relative_start": 0.36408495903015137, "end": **********.645454, "relative_end": 1.9073486328125e-06, "duration": 0.04934501647949219, "duration_str": "49.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00264, "accumulated_duration_str": "2.64ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.622448, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.121}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.633375, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.121, "width_percent": 19.697}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.6387072, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.818, "width_percent": 18.182}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IldRRC81RUVsZzFVOWxWVUpqcFdBYnc9PSIsInZhbHVlIjoieFl3WWZYQlVoeldJekJZL04xdThzdz09IiwibWFjIjoiYjU3MmM5ZTA0OGIwYzNmMDVlMTJjZTM0MGUxNTJjYjllNTJiYjNlZmMzZWU4ZWFlYWIyMTMwYTFlNGE0ZmE5ZCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-684488188 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-684488188\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-972694905 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-972694905\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-769877079 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-769877079\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1691196688 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IldRRC81RUVsZzFVOWxWVUpqcFdBYnc9PSIsInZhbHVlIjoieFl3WWZYQlVoeldJekJZL04xdThzdz09IiwibWFjIjoiYjU3MmM5ZTA0OGIwYzNmMDVlMTJjZTM0MGUxNTJjYjllNTJiYjNlZmMzZWU4ZWFlYWIyMTMwYTFlNGE0ZmE5ZCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750984845353%7C61%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im9FT2JqT09QbFpKL2xwMUp4NmZZYXc9PSIsInZhbHVlIjoidDVKbm8rVU90TWR0Ukx2QzhCYk1Zd1diZjhSQmNVN1RRenltK0w0SkFjcytER2xtVjM0L1E0Mm56RVJ2TjdQaVMzTmVtL0htZ0RZQUNqOW56MGJUQ0VXcnd4b3JQanpzU0ZzVlR3a0pqTVg3VHdYbzdjSGlpL210bWJOdkVIck9TVUU3YzRXNnM5TmJ6WGM1TkV5ekx0S0RXYUw5VFZ2a1loZlh3VFkwYTZuQ0xIRzJ5eEozcXlyUktEWGxZeUloT1JrUWJpREM3YnZ5TFVtaUJHZk9QOTBxQ2Z6Ulc5U3YyQXQxcVI1OHY2SUw3TVRPV1pUdkJRNnZCdEF3Vkg1cnFwQ3M0a3NpejArbDJGM2hRRWtCaEhNalFWUXArNnFJVHkwUitRMU1rVlNNblNsS2FVNmprZ0NaNk9PVUI1NHZsSkhFS2kveFc0SHlSMEhORURJb3lldWpjbzduSjNxdlNlbUU1VElKVVNhZCtzWXF0SS92U1diUDNsMzlsV2I4UFNnbnhtSjFTT2VsazB0QmpXQWtXd0pTQzlCeFBmUmlOdEl1WFFsd2ozZC9VbHFha05IQUY5OVIvcUpSeEdZVHdSekUyNnRoU3pibGdhQ3V2Wkt0OXhVU2FrOUsvWVJtZUxtQzNydUE2MnVQbDZDVVF5MWJqcmFCc3VKNytmdmciLCJtYWMiOiIzNjhkYjdhODU2NmM0ZjU3YTYwNTkxMzRmN2YxNDFhNTA4YmQ4MjZkZDBmMDg1ZDEzYWNhMTQ4ODdhYjRjYzA3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilcxc0tjSFdMNmpBdlpDenMrREhtN1E9PSIsInZhbHVlIjoidGVyTkVpMC9qT1pFRnZ6ZVZzNGZXaXB2R2RKZHMxU0w5WmVLY1RtYmtvMmRadXFHcnJPUW9VTGwwU01Ka0VJOFJ2SmtYQ2JPU2JGNTZDQS8wMjlFaUYzV0s5b1J3UzhSaVFRdEtJaXV2MHNLemlQUW1NdnFubXVHblVwdTJBS0pZMzV1dmFVYlFNOWdXa0pGb1pYME1UQnF2a0o4ZXZDNWFqbjJxdVBJK3NXRkpaaGdwZkliTDN0RjNPeXNvMHRRTU1hYzZ4NDB6M1ZwT3JPemhoUk1sTVA2RFpta2J3QWZnRkwrK1lCeEFnSmViWXNOdE4vQlY3QVVzdmtFcDBUVllKZzVBKzA3OEM3akVqbVR1dHFyaUlHUU1wVmszRTdFZndTNjlWNWxBMXI1Qk1KNTRYV2ZGUGxqcTFES1JGU1NxWDBUdVpPSHFnU1VveEEzRjB1YUd0cldZTy9QdmdsdXM1cldYZDI4Rk1VbW04T2VJTU9ianNSdEdQbzVRQ2hiK2tTMThnZUp3RWpiQUxTOHh3R3JQc3hXK3NFWEMxZHFPcnFUbG9kdnRJWGRINDMza08rb1BwUjFJYkxaZWFhMDFTL05SQXRkWHJSUFpWSzN2S2ZMN3pOb2NDekRYUzdncUhnS2VJN3Q1ellwQWtFWkU0U3J5N3AwekFyTFJ0OVkiLCJtYWMiOiJkYTE0MjVjMDIxZTFkZDc3MzI4NGUyZmNkMWNhNjVjZGI4NzYyNjg3ZTk4MGRhZjJiNjZmODMzODg1NGZhMTk2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1691196688\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-261770631 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-261770631\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1741403650 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:40:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImxUL0VTakc2QW0veStUTG4wN0FuNEE9PSIsInZhbHVlIjoiUXJDcmRsN1V5d1ZJa0RyTHB6RUl0RnZEYkZaU3BRb0RlL25TNmFSVEkzNyt4cy9TTG1Nd1VWclVCeDhwQURnd3ZrNC9hdVZabnRTMDZVQ0d5akhQRXFNeTlkR29HMHA5WnBIQ3hXQVpXTGp4Nng0TlV2TzlIaE13dFVHd2diZ1RDZHVMVU9PNXVTSTgrVjBrRE9LRSt6ZWUvaGt4UkJCSlppcEJwTk5hNWRsVE44NklIcitvclJveXlDZjdLeFRWUHQvRG1KRGZvSktBMndPOFZWM0FxZllST2FwdUNvNWdtSjIrNWhRbHdtdHk2K1llaHJhNDFBUStINHVzUS9TOXhDZjBRVGtnYTY0ZTBaUHdFNkgxMmlkd2JGNGtKckJ4NzdUdmFIWnR2alF1Mmk2ZFhFcURCRWFZWSt5Y3MyRm41RGUrODBUbDlsZmJIcG5YcE1qVjgrSHZRdEpOSy9mcmwwd1RSU01MNkRTZVNtMHNBWG9zZXZTem5pRzZYNjlJTCtOamJNOGw4THV3MHdyaXozaGYyWnFYZW9oRjlidWU5MVRlY0hPRGY0eHR1SWl1eGRxYlFTZHdJMW41TEM2aWNVOWhmYlBpRitjS25zbkJ2NTlmKzRzTDZ2TXFjUVQ1TURPUkFwbVY2MENITnhnWmZJY3ppeG1sWFNJaE11a28iLCJtYWMiOiIxZjM5NjRkNzJmOWI5Mjg3MDBhNmE4ZjIxOTljYjQxYzgwZDQ0Njc4NTRkN2ZjZmVkOWYyZTFiM2U2OTcwOTgxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:40:49 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlYweGlTZm1DM2MwOFRhWTY1RXlKV2c9PSIsInZhbHVlIjoiZU9NbzN1enNoR1E3blZ6KzV0L1FkTWd4L1VuN3ZXdjNBWVB2MHhXNUova3BRcGIwd1o1enB6bCtERTl6WTY5cHcyNVVCbStTMnJCS2lQSFJxSHlwbnJmSjRaQUg4Tmx6L0xxNll3alBSSVB3NEhiOGY3VVhpVEFRWUwyeUVqczAwMlp6SnFqSjlpTTl0K0d2ODg0ZS90VEF2TXp1REdJZ1pCb0ZmTFZvOTVHa0s1cWx5RW1wZzdZcWFmQVNDK3lvcWprWFphUGNlbEJPc1ZpdStITUJUNkZqektXREhramdqOFhhL3YrL1A3RmtkcEQ2NVNYVjNraFpEVTJtWjEzK3I5anNzR3ZtNEFaM1pnMTRTeEJIcUNtRkpTT3d6TXVxS09hTHA4TVU1VkhpSEdwWHQwcGNsYS8wU2EyejBGa1A2akZRT2VTQkZuZjlVL3VNN2pnUmVhcFd4MTZrbXkzM3dKZ0I2UWN4T3BwaTNFcFo5cDN2ZE5qNjJzQ2lkRUs3dkI5NCtRVkJqUldkL2wwa3FsZEFxdFZQc3VPemFYVmlMWVVrUVhKVWVsSzNiaDZQazh2MGhEYmkyS0Y1WCtKVk9vN2MzSHVqRHc2akRNd1lUdEVUWnpScmswSzkrb3U2dGJKdCtYcHMyeDRsQnNIOEFWSmE3SGV0UXB3SDd6QWYiLCJtYWMiOiJkMzA3MmI4YTE1NTExN2ZkMTNhNTAzZWJjZDBmNjQ3YjliMTY0YTlhZTE5YjE2NzUzZTU0MThmMWFmODIxOTZlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:40:49 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImxUL0VTakc2QW0veStUTG4wN0FuNEE9PSIsInZhbHVlIjoiUXJDcmRsN1V5d1ZJa0RyTHB6RUl0RnZEYkZaU3BRb0RlL25TNmFSVEkzNyt4cy9TTG1Nd1VWclVCeDhwQURnd3ZrNC9hdVZabnRTMDZVQ0d5akhQRXFNeTlkR29HMHA5WnBIQ3hXQVpXTGp4Nng0TlV2TzlIaE13dFVHd2diZ1RDZHVMVU9PNXVTSTgrVjBrRE9LRSt6ZWUvaGt4UkJCSlppcEJwTk5hNWRsVE44NklIcitvclJveXlDZjdLeFRWUHQvRG1KRGZvSktBMndPOFZWM0FxZllST2FwdUNvNWdtSjIrNWhRbHdtdHk2K1llaHJhNDFBUStINHVzUS9TOXhDZjBRVGtnYTY0ZTBaUHdFNkgxMmlkd2JGNGtKckJ4NzdUdmFIWnR2alF1Mmk2ZFhFcURCRWFZWSt5Y3MyRm41RGUrODBUbDlsZmJIcG5YcE1qVjgrSHZRdEpOSy9mcmwwd1RSU01MNkRTZVNtMHNBWG9zZXZTem5pRzZYNjlJTCtOamJNOGw4THV3MHdyaXozaGYyWnFYZW9oRjlidWU5MVRlY0hPRGY0eHR1SWl1eGRxYlFTZHdJMW41TEM2aWNVOWhmYlBpRitjS25zbkJ2NTlmKzRzTDZ2TXFjUVQ1TURPUkFwbVY2MENITnhnWmZJY3ppeG1sWFNJaE11a28iLCJtYWMiOiIxZjM5NjRkNzJmOWI5Mjg3MDBhNmE4ZjIxOTljYjQxYzgwZDQ0Njc4NTRkN2ZjZmVkOWYyZTFiM2U2OTcwOTgxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:40:49 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlYweGlTZm1DM2MwOFRhWTY1RXlKV2c9PSIsInZhbHVlIjoiZU9NbzN1enNoR1E3blZ6KzV0L1FkTWd4L1VuN3ZXdjNBWVB2MHhXNUova3BRcGIwd1o1enB6bCtERTl6WTY5cHcyNVVCbStTMnJCS2lQSFJxSHlwbnJmSjRaQUg4Tmx6L0xxNll3alBSSVB3NEhiOGY3VVhpVEFRWUwyeUVqczAwMlp6SnFqSjlpTTl0K0d2ODg0ZS90VEF2TXp1REdJZ1pCb0ZmTFZvOTVHa0s1cWx5RW1wZzdZcWFmQVNDK3lvcWprWFphUGNlbEJPc1ZpdStITUJUNkZqektXREhramdqOFhhL3YrL1A3RmtkcEQ2NVNYVjNraFpEVTJtWjEzK3I5anNzR3ZtNEFaM1pnMTRTeEJIcUNtRkpTT3d6TXVxS09hTHA4TVU1VkhpSEdwWHQwcGNsYS8wU2EyejBGa1A2akZRT2VTQkZuZjlVL3VNN2pnUmVhcFd4MTZrbXkzM3dKZ0I2UWN4T3BwaTNFcFo5cDN2ZE5qNjJzQ2lkRUs3dkI5NCtRVkJqUldkL2wwa3FsZEFxdFZQc3VPemFYVmlMWVVrUVhKVWVsSzNiaDZQazh2MGhEYmkyS0Y1WCtKVk9vN2MzSHVqRHc2akRNd1lUdEVUWnpScmswSzkrb3U2dGJKdCtYcHMyeDRsQnNIOEFWSmE3SGV0UXB3SDd6QWYiLCJtYWMiOiJkMzA3MmI4YTE1NTExN2ZkMTNhNTAzZWJjZDBmNjQ3YjliMTY0YTlhZTE5YjE2NzUzZTU0MThmMWFmODIxOTZlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:40:49 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741403650\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-242141162 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IldRRC81RUVsZzFVOWxWVUpqcFdBYnc9PSIsInZhbHVlIjoieFl3WWZYQlVoeldJekJZL04xdThzdz09IiwibWFjIjoiYjU3MmM5ZTA0OGIwYzNmMDVlMTJjZTM0MGUxNTJjYjllNTJiYjNlZmMzZWU4ZWFlYWIyMTMwYTFlNGE0ZmE5ZCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-242141162\", {\"maxDepth\":0})</script>\n"}}