{"__meta": {"id": "X0721c1c25e45c5b62a435e7ca8a5b5b3", "datetime": "2025-06-27 02:12:10", "utime": **********.273354, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990329.82751, "end": **********.273366, "duration": 0.44585585594177246, "duration_str": "446ms", "measures": [{"label": "Booting", "start": 1750990329.82751, "relative_start": 0, "end": **********.19928, "relative_end": **********.19928, "duration": 0.37176990509033203, "duration_str": "372ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.199289, "relative_start": 0.3717789649963379, "end": **********.273368, "relative_end": 1.9073486328125e-06, "duration": 0.07407879829406738, "duration_str": "74.08ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45951360, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "1x auth.login", "param_count": null, "params": [], "start": **********.243124, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.phpauth.login", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}, "render_count": 1, "name_original": "auth.login"}, {"name": "1x layouts.auth", "param_count": null, "params": [], "start": **********.248407, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.phplayouts.auth", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fauth.blade.php&line=1", "ajax": false, "filename": "auth.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.auth"}, {"name": "1x landingpage::layouts.buttons", "param_count": null, "params": [], "start": **********.26529, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.phplandingpage::layouts.buttons", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FResources%2Fviews%2Flayouts%2Fbuttons.blade.php&line=1", "ajax": false, "filename": "buttons.blade.php", "line": "?"}, "render_count": 1, "name_original": "landingpage::layouts.buttons"}, {"name": "1x layouts.cookie_consent", "param_count": null, "params": [], "start": **********.267914, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/layouts/cookie_consent.blade.phplayouts.cookie_consent", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Flayouts%2Fcookie_consent.blade.php&line=1", "ajax": false, "filename": "cookie_consent.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.cookie_consent"}]}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 9, "nb_failed_statements": 0, "accumulated_duration": 0.0074, "accumulated_duration_str": "7.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 348}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.226458, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.703}, {"sql": "select table_name as `name`, (data_length + index_length) as `size`, table_comment as `comment`, engine as `engine`, table_collation as `collation` from information_schema.tables where table_schema = 'kdmkjkqknb' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED') order by table_name", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 537}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2302861, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "Utility.php:537", "source": "app/Models/Utility.php:537", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=537", "ajax": false, "filename": "Utility.php", "line": "537"}, "connection": "kdmkjkqknb", "start_percent": 22.703, "width_percent": 37.568}, {"sql": "select `full_name`, `code` from `languages`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 543}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Auth/AuthenticatedSessionController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\Auth\\AuthenticatedSessionController.php", "line": 351}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.2349539, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Utility.php:543", "source": "app/Models/Utility.php:543", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=543", "ajax": false, "filename": "Utility.php", "line": "543"}, "connection": "kdmkjkqknb", "start_percent": 60.27, "width_percent": 2.568}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "auth.login", "file": "C:\\laragon\\www\\erpq24\\resources\\views/auth/login.blade.php", "line": 4}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2436092, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 62.838, "width_percent": 3.919}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 10}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.249039, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 66.757, "width_percent": 7.703}, {"sql": "select * from `users` where `type` = 'super admin' limit 1", "type": "query", "params": [], "bindings": ["super admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4090}, {"index": 17, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 18, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.257985, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4090", "source": "app/Models/Utility.php:4090", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4090", "ajax": false, "filename": "Utility.php", "line": "4090"}, "connection": "kdmkjkqknb", "start_percent": 74.459, "width_percent": 8.378}, {"sql": "select `value`, `name` from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4091}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4132}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 22}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.260729, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4091", "source": "app/Models/Utility.php:4091", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4091", "ajax": false, "filename": "Utility.php", "line": "4091"}, "connection": "kdmkjkqknb", "start_percent": 82.838, "width_percent": 4.865}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4725}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 4659}, {"index": 15, "namespace": "view", "name": "layouts.auth", "file": "C:\\laragon\\www\\erpq24\\resources\\views/layouts/auth.blade.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.2622988, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:4725", "source": "app/Models/Utility.php:4725", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=4725", "ajax": false, "filename": "Utility.php", "line": "4725"}, "connection": "kdmkjkqknb", "start_percent": 87.703, "width_percent": 5.405}, {"sql": "select * from `landing_page_settings`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "Modules/LandingPage/Entities/LandingPageSetting.php", "file": "C:\\laragon\\www\\erpq24\\Modules\\LandingPage\\Entities\\LandingPageSetting.php", "line": 27}, {"index": 19, "namespace": "view", "name": "landingpage::layouts.buttons", "file": "C:\\laragon\\www\\erpq24\\Modules/LandingPage\\Resources/views/layouts/buttons.blade.php", "line": 2}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 73}], "start": **********.266092, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "LandingPageSetting.php:27", "source": "Modules/LandingPage/Entities/LandingPageSetting.php:27", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2FModules%2FLandingPage%2FEntities%2FLandingPageSetting.php&line=27", "ajax": false, "filename": "LandingPageSetting.php", "line": "27"}, "connection": "kdmkjkqknb", "start_percent": 93.108, "width_percent": 6.892}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "yL6RI9Dzm7wWSneQaLee8nwOi8fTcp9yg3tQI2BA", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-1764356006 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1764356006\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1423431131 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1423431131\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-303990258 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-303990258\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-35065841 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1742 characters\">XSRF-TOKEN=eyJpdiI6InBOWWJDM20yTVUyVnhMWkxpN1Bjb0E9PSIsInZhbHVlIjoiR3ZjUlVXWkdBQ3M1QUwrcjg4L29FZHlUdUlUNSt6Yk13c2cvS2RWbW1KTzVPTUpEYjY2SEptU2l0NG1rVHA3T3ZnbldhMVNRekRQdVBsUHlOdmpBMjRBYWUvQnFGYnpFWmFFYjBIZ0drRS9aWm1xbWRuWklUZEdxeGhGeVlBbnF3dy9kK25tWE1OeXh3WWpwZGV2YStwWVhxaVJPWmxZdjZNNXlhc1lzVUNDenBXSlB5cDJVSGI3KzJkTjUzMHBYWlAybzdpT0xTWk5ZdmRDVEZHUVhYS1dvNG4wcisvSFFvUUUxdGJLUEppelNub0plbDVnR3JIQVZleFVYMW5GM3A5S0FDZFFIWVVrbWY2YkVqNFRtT0VsUEhUS2pLMDhLeUE3VWU1bTFqOWVpcUh5Tk1veCtlRG4zenAwa0x0eTNaYi9wRjZ2c2cvWFBDMVpYNlhLS0lDR1FyeXlaNWhCTzYzOVphVlZGd3dqRXRkaUF0QzUrcExUaHhodlBEZm11WEpFcVFwd1ZRcEJ6YlhNUjNOYm5TTkJvejVHNklrWEZHb0JNLzZYNWZhbUh5Uit4STRjc1BqRzlPVUJKL2FKL1N2b3dBcTJPWElXR3dZNUJDdkpUTEhYOFJWRHhhQzVwUlczOVlEZ3hwQkU1a3lEWEsrb1FjL3VHVnhDMjEwRzQiLCJtYWMiOiJhNTcxYWJhMzgyMjU5MGU4ODViODAwZWFkNjUyZDFjMzUwOTQzNzYyM2I5Y2ZmYTI3MjExZDk0NjQ1MDA3NjY2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJYSlZEZHQ4bjZSb0l0eGMwaHIrZWc9PSIsInZhbHVlIjoiU2Y3M2xMYjJkM3Q0c2Y2eVdBVWxxa0gvYk8wM0FBc2N1bFlyTm13MWhGY0NabEpkM2ZkYmpvbEkyOUVhcmE5YjdnZnhHMGJNSFZqbmR3eHhpWDRYR1p2QkE3WHl4WHpMdUZ3b0JDdDBIRlBoSmovNU1KVnhpNGpRWmpBeExYRXRRZ2xRWU1FZWU1QmZpN0dFd1dPL0g4ODJjRFpGZWQ2NGZvZ0wzZVppUmxKUG9zcUE5R2lmMEN4S1lKNmxINDhYbEZRMlN4TCtQRGs2YmpZV3BJeUNNckxZay80OXdCVlVqT0QwZXBKdERycVA5cy82eWRROEFTRFFCQUhMVUZ1SnZIMXJwb1NZcXF2WXk2dFQ1dUd1eWlpMXhWNUI3eUdzOG93UjhibExkWGtOTUc5NXZESm0xVXc1NXJUWEs1MmQweWdvYXZQYWFoOUZMcTJlVzN1SVd1ZkUxeEk3WHQ1NTNHajBFSTFlRVpzMi9rRldXckJlbmwzMGZNSldxb2tNM0diN2xNaXMxNTI1Y2tsMkFIalBBOGRYVFdBRjZSZlVCc0g4OXllT0RSa0xwRjgxSjI5cnB1UFFxNktNZGdQMVJ2VUc1N2ZNS0tkKzBDczl1TXAwWlVvbFFuMlNYYlo5cUNxL2EyR2x2dGVPbCtWMmVVdkhTYyt2YlRpOGdEZzEiLCJtYWMiOiI1NWZkODc2M2FlMTI1NDI3MjRhM2M1OWU3ODUwZDY0OTFkNmUxMTIxNGZjNjEwZjk0YTcxYmUzZjE2MDFlOGIxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35065841\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1042753378 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yL6RI9Dzm7wWSneQaLee8nwOi8fTcp9yg3tQI2BA</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ELyjGBtdh3JiyItc0ptzvJGtB0BpIVKMWe9y1doK</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1042753378\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdtL1U2YWUvdTIvMGZ2VDhUa1JqcGc9PSIsInZhbHVlIjoiMStHSmI0VTg2QWhacXAyWFl1ZHpyVEowZW03WnZ5VnVPcjFkcnlSNDltQ0hJZGpTZ0pabVhjM1VKT3p6bkJSaXE0RFR6R3pjSjBLV1dMcjJvdmVKRjdkZk9XUmxFN3pzSmdpSGdPSEtEeFlDNWJiWVRJNzF2Y1hTaUJUa1Z1QlVBNVhmcjZod1ptNG5wOU1iN2NlRG5FK0pTaTEzL0Z0VmsvdkJrMWJqSVVrY1dVWldNc1I2bUVibGRzM3BBNGhyTWlPTkRsS21VNjNkdEo3L3pOWCs0SlFIMVJ5L3ozZERzdVVhMkFCMDlTenlVMGdJSkdUVTBwbk9iVFR6MlNaSyt2aERlVFBUT3IwUlRNRFZsT1U3M09ENml3eXk1ajdaUDVreVVXOGtUWGN0WEtSQnhSY3AveHA0Q2MzWnZBR0p6anAyVnR4THRkcGVTVlExSk5mQnBYOGJMYldXQWVVaVgxZHJhM2FLYW1hSnkxRXBQRFdTL0wxcmFxbXZXMFhoTHV3bEFwVFZ6dlpwMVYwaGc4U3ZTcklSS1FzbEhYYU1obnJPTXdtZW4ydkpUbFBTa0NVbjU4UEV2VmRXTFBDVDJaWGQ2bzlHNHBycFcramtyc1dwcE9oMDJhSGdkRjFzYUNCL0hEbWdNRzZWMG9aOEZibHBxT0dpcEJrSGU4RCsiLCJtYWMiOiIxYTdkMWE0MGY3YmQ3MDZmMmQ1Zjk5MTRkNjc0NTkzNjliN2M0NzBiODYzNzY5MzRiMzE3MTZkYjdkM2Q3OWFiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:10 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InpaaDFBaG42dUh2dEVTeWJZQkZzSGc9PSIsInZhbHVlIjoiWlg2Vy9WcHZ2TW9lUUZBRE5OcnZVcmg0N3F3Mmd5UWxaTzlvT3lVSmNEVEE2MTludkgyYnROY1JoV1p0YlBmYXdDU1FYMzBMYWpkamFHVE16RTdYL0ZWbThWN1hPTCtuNjBsZ2NNR0xIUXhTT1dzV1hqQjdQMUhCZGZ5RHFyM1IrWkN2L04vT2xJYldWTnFweU9YL25MaDZtY0h6Z3FuQjRsaGFBSU1Bb3ViNCtROWZ6T0FGQlZiRzIvdHRhM285YTNsS0FPak5DckVuZGNFeWVEYWxBSDlBaFR1VnhSa2xCRC9JenA1RDVxdzkrbTMwNFlzZ1lVMVQxYUlYZUJsODc4ZXRZN2IvVGNBbGRETGR6WEpQZm9JTmVkeURIcTRncXlvMkxOSTBMOFkvdE81bTdnWGQvdkdEc0dETXUvMzhRaENsZU4zRlgzOEFKcjltc0gvcWk1WmptSVlVYytvOE9pRmkyYm5IM3ZRQXJqNHNoalNIU2h4U29McldBVXlndDhaUXB2cFJOTmVROEFMaXN4ZDA3ZkpuRytOUU1tbUxMS1VxT3NPcHpxVmtidmtBRkp0Rzk4VENHMkduYmk4a3U2dmRsbHJRWUFWeENkUjBQUnBYRFdGNU10alc0bVM5OEoxSGRCbGxGeXBxNHhpY0hPaFg4ODMwS0dwMlp6OVEiLCJtYWMiOiIxOWFjYzA2ZGVmZTdlZDJkODQzNjhhMzk3NTEyZDM3OTMyMTg1NWNmYmZiZDk4YzlhNjU5NzYyYWU0YzRhNTBkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:10 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdtL1U2YWUvdTIvMGZ2VDhUa1JqcGc9PSIsInZhbHVlIjoiMStHSmI0VTg2QWhacXAyWFl1ZHpyVEowZW03WnZ5VnVPcjFkcnlSNDltQ0hJZGpTZ0pabVhjM1VKT3p6bkJSaXE0RFR6R3pjSjBLV1dMcjJvdmVKRjdkZk9XUmxFN3pzSmdpSGdPSEtEeFlDNWJiWVRJNzF2Y1hTaUJUa1Z1QlVBNVhmcjZod1ptNG5wOU1iN2NlRG5FK0pTaTEzL0Z0VmsvdkJrMWJqSVVrY1dVWldNc1I2bUVibGRzM3BBNGhyTWlPTkRsS21VNjNkdEo3L3pOWCs0SlFIMVJ5L3ozZERzdVVhMkFCMDlTenlVMGdJSkdUVTBwbk9iVFR6MlNaSyt2aERlVFBUT3IwUlRNRFZsT1U3M09ENml3eXk1ajdaUDVreVVXOGtUWGN0WEtSQnhSY3AveHA0Q2MzWnZBR0p6anAyVnR4THRkcGVTVlExSk5mQnBYOGJMYldXQWVVaVgxZHJhM2FLYW1hSnkxRXBQRFdTL0wxcmFxbXZXMFhoTHV3bEFwVFZ6dlpwMVYwaGc4U3ZTcklSS1FzbEhYYU1obnJPTXdtZW4ydkpUbFBTa0NVbjU4UEV2VmRXTFBDVDJaWGQ2bzlHNHBycFcramtyc1dwcE9oMDJhSGdkRjFzYUNCL0hEbWdNRzZWMG9aOEZibHBxT0dpcEJrSGU4RCsiLCJtYWMiOiIxYTdkMWE0MGY3YmQ3MDZmMmQ1Zjk5MTRkNjc0NTkzNjliN2M0NzBiODYzNzY5MzRiMzE3MTZkYjdkM2Q3OWFiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:10 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InpaaDFBaG42dUh2dEVTeWJZQkZzSGc9PSIsInZhbHVlIjoiWlg2Vy9WcHZ2TW9lUUZBRE5OcnZVcmg0N3F3Mmd5UWxaTzlvT3lVSmNEVEE2MTludkgyYnROY1JoV1p0YlBmYXdDU1FYMzBMYWpkamFHVE16RTdYL0ZWbThWN1hPTCtuNjBsZ2NNR0xIUXhTT1dzV1hqQjdQMUhCZGZ5RHFyM1IrWkN2L04vT2xJYldWTnFweU9YL25MaDZtY0h6Z3FuQjRsaGFBSU1Bb3ViNCtROWZ6T0FGQlZiRzIvdHRhM285YTNsS0FPak5DckVuZGNFeWVEYWxBSDlBaFR1VnhSa2xCRC9JenA1RDVxdzkrbTMwNFlzZ1lVMVQxYUlYZUJsODc4ZXRZN2IvVGNBbGRETGR6WEpQZm9JTmVkeURIcTRncXlvMkxOSTBMOFkvdE81bTdnWGQvdkdEc0dETXUvMzhRaENsZU4zRlgzOEFKcjltc0gvcWk1WmptSVlVYytvOE9pRmkyYm5IM3ZRQXJqNHNoalNIU2h4U29McldBVXlndDhaUXB2cFJOTmVROEFMaXN4ZDA3ZkpuRytOUU1tbUxMS1VxT3NPcHpxVmtidmtBRkp0Rzk4VENHMkduYmk4a3U2dmRsbHJRWUFWeENkUjBQUnBYRFdGNU10alc0bVM5OEoxSGRCbGxGeXBxNHhpY0hPaFg4ODMwS0dwMlp6OVEiLCJtYWMiOiIxOWFjYzA2ZGVmZTdlZDJkODQzNjhhMzk3NTEyZDM3OTMyMTg1NWNmYmZiZDk4YzlhNjU5NzYyYWU0YzRhNTBkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:10 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-976938257 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yL6RI9Dzm7wWSneQaLee8nwOi8fTcp9yg3tQI2BA</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-976938257\", {\"maxDepth\":0})</script>\n"}}