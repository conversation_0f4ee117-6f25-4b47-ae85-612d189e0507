{"__meta": {"id": "X9505d10274b5a4f2809e4d106ba3abae", "datetime": "2025-06-27 02:32:38", "utime": **********.850794, "method": "POST", "uri": "/warehouse-empty-cart", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.392498, "end": **********.850807, "duration": 0.4583089351654053, "duration_str": "458ms", "measures": [{"label": "Booting", "start": **********.392498, "relative_start": 0, "end": **********.794998, "relative_end": **********.794998, "duration": 0.4024999141693115, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.795006, "relative_start": 0.402508020401001, "end": **********.850809, "relative_end": 2.1457672119140625e-06, "duration": 0.05580306053161621, "duration_str": "55.8ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45289544, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST warehouse-empty-cart", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\ProductServiceController@warehouseemptyCart", "namespace": null, "prefix": "", "where": [], "as": "warehouse-empty-cart", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceController.php&line=1627\" onclick=\"\">app/Http/Controllers/ProductServiceController.php:1627-1637</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0026999999999999997, "accumulated_duration_str": "2.7ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.829381, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 82.593}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8424861, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 82.593, "width_percent": 17.407}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/warehouse-empty-cart", "status_code": "<pre class=sf-dump id=sf-dump-1437260024 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1437260024\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1870410641 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1870410641\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1514231038 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>session_key</span>\" => \"<span class=sf-dump-str title=\"3 characters\">pos</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1514231038\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1629853997 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">15</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImZNYnY3T2VqN1ZPZjRVQkJOb1VmV1E9PSIsInZhbHVlIjoiaUw5ajl6aEpzYnNleGg5TGg5UVlzREZCRngwdk9NZU9zWldxdkJIOEFsaWgrV3NyK1BsMlpVWUtLNTlTN2RTeTBkNnZ3TDMrVUJ3NFJGa2txMFV0YUNyVlBQV01uUGh1YXZjaHVYQ2doRCtEdGwrdmxGK1lRM2M0VlI2WDN4TURLSHMvQjlMeDVLeTIvSjR2MUlweFV4cjI1Zmc3c2VkSXB4VmZjM1hZSmNzeGlCRGx0TGdIenhGQ21iTXU2bXdqM2hYM3FBbU5wWlYvaU9OQkd0dDZGdDdNNWJQSkYwS2ZJMUdiajB4MmRmVHVINHJkWkxpd3EzTVFRSmNuQ1FQb3JUVGFUdzIzZkFucjJyMzlQOEM1VnBSdDNTd2R5UGkyaS9XV0phRWZ4djJqNE1MU2xzTGtpYlJDaytaV3lZT2F0TzdSZTF4UGY4c0JwYnZQcDRMNGNQVlMwclZHWFppbmdRU3RuZWwwY1gzR0ZIenluZGUzbHlYYmhjUXhtOFJWKzRvSHU1SXpBYkVrdjcxajNHL3VFQXhDNEgzcFZ4Zzg5N3FETkFGaFVhK0lDM3p3ZDBFQjAyY3dPaDV1c1F0c0U3VEJjZFcxSWpERldWQXhiVGxmWjVCbDZHSHpPQzYvVUVmSVd0dkNDZlpoYlh0Tko4SWlLTXlYWitYR3BTSEMiLCJtYWMiOiJhZjMxODFlNzA0ZjIyNzkzZTUyMWQwOGMyMjIxNjYzMTc1NGJkMWFjNDkxN2RlYjRjZjM0M2E4YjViOTlmYTEyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlFLRHRIeUpCWkQvcG1nMkFWUklJMnc9PSIsInZhbHVlIjoibm1raEFNNUFROFN0ZzF2YlduR0tyb3pOV29BVlpoeHJOL1NMQTRnM1VOYi9Xdy9rVkUydktTKzNNb29KS1dxcHdlQUY4NzB0b3FrRGw0U2xXV0RFWWJZSFhYRnVWRjE3U0RYOVNWaldyVGNMbTQ5d1doZEpqZnpQRWxlVnRBN1g3R3dCQ2txZFNUeXlZczhRenJ2Sm1kT1JPV1BQTmtFZGdXRWxuZStpbCtyYzlic2h5S21ZWVhhZjF6V2VURGtmRXROYU00NkJhaUZqVWFCVGhzU1dPcDgwUHdOYklKNWFVSnVJWmNBWVhHeEg1NWpId2ppdS9WVDFJYXR2T2w2bTdHdDFZRTZtVnIzWlRhenpFK04rb3puREY2RnFFbnUxWnIvcThMR1VQbHJPWlJTbDhrQmZTdHF0NWhVOEdTejJnQXVUeTNRbUYzRDE4Y1A0S1BvcytoMGlySVJDRDZDWGFxQUZKWlpmSWNSemRtc3VYTk9sdXEzRVdEZFk4eCtZbVpwVno4OGx0WVhJK2wxL3dFMGwzYWZWREM0TTBYb2NkM2pSeW1oNTFUdXBKaWIwYkNxS1lwZERtSEwvazkzYmV6Kyswb1FBZ05iUDR4Q0tjOENPSnBhdDlMOGNPZGZqSGtXb1BhM2Y3K0ZLNXZwdU1CaVZ4K0JGWC9wUHlTODUiLCJtYWMiOiJlZTNmNDFmMDMyN2YyMTJiNmFiYTUzMjI4NjZhOGZhYmVmYWRlN2M3YTcwZjI4N2I1NDM5MmY4ZTg3NzJlNjNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1629853997\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-128330151 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-128330151\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-548264204 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:32:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImtKa2laTFNFa2llc3pkZXQvUXZQcUE9PSIsInZhbHVlIjoic0o1NFJva0puck45QTNNQ21qY2s1N3JlSmtBOHlOMnM4VnYrR2lVMWJxRklBUmpuMjdoaUtWSFFHckVBczBpNFhaZUQrTlEwMTlVK08rcUZxejkweWNlYUIzOS9Gb05EY2VRUDkzdnBsQTQ4NS9KNDlQVCtKK2hheWFKcFJFb1RqeWpaQ216bGV0TmxLN2pCTTNDc0N3dGdjZXNZemNOOVFuaHZMS09RZ1dZSGZneEdMQnh0NlVlUVpZUXBXdlRzL2p5UUluT1Q1YlZEWXBuWGswbDJxTjdHSjg1RjdnWHZNRFdaalFTM0tTVTlGandVKzJDZkNGbXBXWFdzMVUyNVkvNHZsLzZ2ekViZGFBN2J3ZEtXTUFvK1FMYXJEQVpOeVluZVNRWVRJci9yVDhIZGlsVVVLaHF6SnBxTCtpWldMc05tZVFXMFFFMEhRcUEvQVVCMzZYbEJEcHRRRG95T2ZWeHNvaXViMlRSb2VSNGViSEZHaDBnc0lmRWFUYnU3ZlhWRU9XbmJnd3BuckVMRlpTN0xDUXhTNUxna1BnQ0ZxWG9XeWtQNitaR1gyK3FOTjBpbjBDdW4raUZLcEZneTZnR3pPUFlYS08xNnArOU5zKzRrNVdYb3kvNE9TeTZKS2g1V0RsZFdiREdmSHM2WWk1ZEoyV1RFN01INHJSaVQiLCJtYWMiOiJlNmYxNDc0YTM1NzcxM2Q5MDc0ZDllNWIxZTMxNWZmYzYxMDUxYWExMmMzMmE0NzZhZTJmYTRmOTNkMDk5MTQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:32:38 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InJ5NXdNdTZKYkRjc2xwcTJLV1dSRHc9PSIsInZhbHVlIjoiWDdaSjA0NWtZamJ0VFRhUGI4aEJmUmsrVFZPUC9uQWVBZ2ZCNGRXWmRCenVTVkpXOWdFTDVOSDU4QzFaMUtXSVlINytJTXNydERUclZlTWZKQVZPUVE5VHVjWHhoUXJVUUFtVThIajJHMUhnNURqbHY3S24rSW9yR25PQi8ySFBFbUh0Y0w3TGhtdnpyMERjY1MzR0kxanFPYjZKbHMvMk0xSTBQaHkzTXVtbzNGazdEbUJjUTZUbno2Z3dyTURjamVrOWZia1Noc1N5dVIxUCtDcDFqK0ZZT2JsT2wvUW1adUdLRmVQVU93SFlhMW1uL1V0WVl5S2xNNEM1NkVMdHRtUXVkNGlIVG5tVEpEekpXbnJMSEdxbEFTTENPRTZjdHVTcGFlM2pkWENwVnFTNkQ3SUlaY1dEV044SnJwNUF5OE4zeW1yMWpGbVhKMkZoelZLSURpM3Z0L2lPZ1prUHl3MEF5VStsdW5UYloyOFlUcVROOHFHM0JrNm1SKzFadERIVmJIY2ZvYjg3SUI0M0N5a1k3VWcxdnJGOEFDcXFvRU83NmZLc0M0ZGpzSGFYUnBlRFF1MTFQcWY0d3JsREtNdG5SaVRjSy9Wc2xTTW9HOEdvTEhrR1EzK0lHUDJMc3ZoRExMdUFRQWhYWHpWOUk0OVpEUHdtSk8vcmhIRk4iLCJtYWMiOiI2M2E0YjRkYzNhODQ0M2NmYmFhMGQwMzQ0NTdkOWZiMTk3MTk0ZjY1MjU1MGRjZmRiOTQ2ZTE5N2Q5M2ZhYzBjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:32:38 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImtKa2laTFNFa2llc3pkZXQvUXZQcUE9PSIsInZhbHVlIjoic0o1NFJva0puck45QTNNQ21qY2s1N3JlSmtBOHlOMnM4VnYrR2lVMWJxRklBUmpuMjdoaUtWSFFHckVBczBpNFhaZUQrTlEwMTlVK08rcUZxejkweWNlYUIzOS9Gb05EY2VRUDkzdnBsQTQ4NS9KNDlQVCtKK2hheWFKcFJFb1RqeWpaQ216bGV0TmxLN2pCTTNDc0N3dGdjZXNZemNOOVFuaHZMS09RZ1dZSGZneEdMQnh0NlVlUVpZUXBXdlRzL2p5UUluT1Q1YlZEWXBuWGswbDJxTjdHSjg1RjdnWHZNRFdaalFTM0tTVTlGandVKzJDZkNGbXBXWFdzMVUyNVkvNHZsLzZ2ekViZGFBN2J3ZEtXTUFvK1FMYXJEQVpOeVluZVNRWVRJci9yVDhIZGlsVVVLaHF6SnBxTCtpWldMc05tZVFXMFFFMEhRcUEvQVVCMzZYbEJEcHRRRG95T2ZWeHNvaXViMlRSb2VSNGViSEZHaDBnc0lmRWFUYnU3ZlhWRU9XbmJnd3BuckVMRlpTN0xDUXhTNUxna1BnQ0ZxWG9XeWtQNitaR1gyK3FOTjBpbjBDdW4raUZLcEZneTZnR3pPUFlYS08xNnArOU5zKzRrNVdYb3kvNE9TeTZKS2g1V0RsZFdiREdmSHM2WWk1ZEoyV1RFN01INHJSaVQiLCJtYWMiOiJlNmYxNDc0YTM1NzcxM2Q5MDc0ZDllNWIxZTMxNWZmYzYxMDUxYWExMmMzMmE0NzZhZTJmYTRmOTNkMDk5MTQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:32:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InJ5NXdNdTZKYkRjc2xwcTJLV1dSRHc9PSIsInZhbHVlIjoiWDdaSjA0NWtZamJ0VFRhUGI4aEJmUmsrVFZPUC9uQWVBZ2ZCNGRXWmRCenVTVkpXOWdFTDVOSDU4QzFaMUtXSVlINytJTXNydERUclZlTWZKQVZPUVE5VHVjWHhoUXJVUUFtVThIajJHMUhnNURqbHY3S24rSW9yR25PQi8ySFBFbUh0Y0w3TGhtdnpyMERjY1MzR0kxanFPYjZKbHMvMk0xSTBQaHkzTXVtbzNGazdEbUJjUTZUbno2Z3dyTURjamVrOWZia1Noc1N5dVIxUCtDcDFqK0ZZT2JsT2wvUW1adUdLRmVQVU93SFlhMW1uL1V0WVl5S2xNNEM1NkVMdHRtUXVkNGlIVG5tVEpEekpXbnJMSEdxbEFTTENPRTZjdHVTcGFlM2pkWENwVnFTNkQ3SUlaY1dEV044SnJwNUF5OE4zeW1yMWpGbVhKMkZoelZLSURpM3Z0L2lPZ1prUHl3MEF5VStsdW5UYloyOFlUcVROOHFHM0JrNm1SKzFadERIVmJIY2ZvYjg3SUI0M0N5a1k3VWcxdnJGOEFDcXFvRU83NmZLc0M0ZGpzSGFYUnBlRFF1MTFQcWY0d3JsREtNdG5SaVRjSy9Wc2xTTW9HOEdvTEhrR1EzK0lHUDJMc3ZoRExMdUFRQWhYWHpWOUk0OVpEUHdtSk8vcmhIRk4iLCJtYWMiOiI2M2E0YjRkYzNhODQ0M2NmYmFhMGQwMzQ0NTdkOWZiMTk3MTk0ZjY1MjU1MGRjZmRiOTQ2ZTE5N2Q5M2ZhYzBjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:32:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548264204\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-137872522 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-137872522\", {\"maxDepth\":0})</script>\n"}}