{"__meta": {"id": "Xe36b679d161c5c2a2d1a6de57b6d764b", "datetime": "2025-06-27 02:27:48", "utime": **********.391501, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991267.94825, "end": **********.391521, "duration": 0.4432709217071533, "duration_str": "443ms", "measures": [{"label": "Booting", "start": 1750991267.94825, "relative_start": 0, "end": **********.306166, "relative_end": **********.306166, "duration": 0.35791587829589844, "duration_str": "358ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.306174, "relative_start": 0.3579239845275879, "end": **********.391523, "relative_end": 1.9073486328125e-06, "duration": 0.08534884452819824, "duration_str": "85.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45737152, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.025580000000000002, "accumulated_duration_str": "25.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.336965, "duration": 0.02419, "duration_str": "24.19ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.566}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.372795, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.566, "width_percent": 2.502}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.380663, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.068, "width_percent": 2.932}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2010611363 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2010611363\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-647582819 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-647582819\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-80421563 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-80421563\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1592958288 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991263527%7C37%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlFtclcrRUZZMk9nWlZ1UGFjb0pnd2c9PSIsInZhbHVlIjoieW4xZ1F0cGJnOE1SUldxTlhPT3FGZEQ1YkZrYmU2cjNSYVNjYUMxY2Eyc0U3T0xFeDdzT08yaHVwSTc3aytzZGIzN3Qxd1dIci9yaGFWSXduYjdMaHR3RFU0QWZVNUhHZnpJME4xSTA3a1Bra1ZncFJLSlRtZngybmorMFdXbFFUVG5zUUxJcTYzYmNFUU9YRDNqS3NOQWVrQ1dBRlozcm9ZcTh4Wnh6ODQwSy9GMkNJbHNWaWhPV1JVRGgxR1BUNlc4ZExIQ0JNMm5iSkRIaDZ2dDJ1bHc5aXZYQjBTT2lzd2xBT205VEkrUkFzTGo0S3E4NTQ4YUlEMlhNd1ZIWHlJdGg3NkNNeVJtWGJWMUt4bXRmQ1VHclFMWk53WkZoTFdLSFRuckFmMG9Xc3UxZ0RLWHZMSFZzR2IzdlJmbFZLMERGWE04WnQyenNZTVB3R0YyMjdKck0vb0NUZWZnQTZadm9CTzVVL0dQSlk5bnArczVVT3g1T1FZZFE1NFVkYkZ5cVgzTEd6S2ZleEZBNFI5OFNiWk00eGpnUHo2TDFCZm1kTTg4d2UxQXM1a3RiUHBXRWE5L3MyUWI1NVRKOGFud1czOXc3RDY4RTgrQ1BhK2ZvclI4Uk5aOU8xRFdnRGZoZmU0MVduWDVGL1U1emZFL0NYeDUxaHN0Wk5xVEkiLCJtYWMiOiI2MDhjMDU1YzZlNWU1MzVkY2E1OTZkZjIzNDU5YjI4ZjI4OTMwZmQ3MGJkNDhjZjU1NDhjNDRmOTJiNWZjOTI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjB2eXFRUkdZVXgva09ySGdnbnl0MkE9PSIsInZhbHVlIjoiUFBXbHlIMnlreTZJc3gzckY4b0ljQ081UmlsZDg0anlvVHI1MDFNYTh4Sjg1TmxCZEZkN241UUk5Q3lnZEtaWE4xMWNhQnpPMmRoRURvWVYyeFU2bnErN0t0ejBuZUFqdHV4OFh5SXRWOHoyNkovSGNjVWVQYktEdkdodUJFSG1vTDREdkdyRmNqNEJVN1JPcWVMTlBseWhudmc2N2NnZkFTQXVUc0hsdmFOZWdMNll0V2hyUjNrM3dibUtJWThtUEh0N2JtemFWSjdlaVNmbDQ2NE0wWWlULzJKTytPRW84cHIvSUlQYjBEYllGRnRmRnlURHNLQm1YeUFtVis4MGM4R3hveFEzZ3JhanVNNDdBUUhadkRRa1BjUXh6aW1EMzltUW9nUlN0R3hVdTlLek4wYkd4bmNTL3hkOEc3ZDR3ZEZ4eTlMcWcweHE2eVcyYmgrU0NmTG5YY0d4MVlxUjJvbFZVRjJpTFNCbVUzOVJyT2FHMFdJTVp3NjdwU3JZNjZsQWpLUmtFQmVVZU14OThXWkpEd1NqMnN6b1RPZFhad2wrcHkxbEFtZ2NxK2RONUNDSEVmZktQMVB4eW1xVXVCa1ltZnBnZGVpWllCSTg1dzVJeU9BN244MGdJSXZLOXk1c3JpR2FtMFhyOWRPZ0J6eEhuWkRaTVZWQU1kNG4iLCJtYWMiOiI0ZWQxNjg1NzRlN2U5OGY0MjY5YzQzYjcxZTU5ZTBlYmM3ZDkzNmJhMDQ2NGJjOTg1OGNkNDkxNTBlYjc1OGZlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1592958288\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1783016208 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1783016208\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1991553069 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZuMTBERlhxUGc0Vmx2RjJQOURNOEE9PSIsInZhbHVlIjoiamYrcUE4cVZyWEhyTFArOC9ZeEpZTnlHL09vRVViOWkvVXppL1VrTmVMSWFXbUk3QjdIa09FZEo4UVNIZkR1TnA4TFBKbmtCQVkyU05kZHlxeUorWTRDNEhySm9pSnlMdTZoSkZHUDNBRWRwcTAyZDQ3YjRsSXROQ1M3U21uMnBxVlBmUnFGRDFZcDhvSGtYcVhHem81akY2NTNFMFJyU3dTOUw3ZGNMYUZOVGd4WWJrVVd1d2pLZE9ZZEZyb3pUbm1qdFBSYnMzVjFseW9qTDVMTDlXSDZGaUVWS1dEWGZZM0N4Z0RLTDA1UVVaOWtBQmNYOHdGalRMV1hYSEZnS3lkWHFRODFLZHdoM2ZjRlZwY284TFQyd0RKZk9vZGtVcW1hcWxEYTk0WGl4WVJaQjlhblROMjZKL1JHR2hsbjZKS0FSNExFZk05VHRRQzduc1lxT01YaG5ubXo2QWpwSXlqeG53TW8xUWFOMXpua2pSRzdsVE5TOWFBRHhlMDJWdFc2U0c1MW5MeTJGOVBubzVEY3B1bHdKcElLWmlEU3hMN1ZZL25YK0tMREJGRENBT2pJUUR6QWVKUU56TFJYUk54SkpWWVZsZVdWdzVaMGp0SXRpZnMyYlJ4TXVPdlZ6ck1TczhLd1RHaEd3YkNqR0p6cXJUSHIwMlZDamc4Q0kiLCJtYWMiOiJiMGY5NDZiMGM0YTMyYzcxMGE2ZGYwOTA3ZWI5Njg2YTliYzE4NTgxNmUzMjk1NGQ0NDU0MzYwNTVmZmMwNzQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InBPT1hWRWJRTkNONnNtL3ZlTlJtZ1E9PSIsInZhbHVlIjoieGp4RWdMNGw3VW9zUlJ0Y2FlNGdWYUNhN1lPRk4wL0RLTUk3MDV5OFVuK2xsQ3hUV3ljeHJDV0Q1Ris2LzF6cEM5dnZJcWs5alJOM2prekMxZWNYazlxNGVPa0pwTFhvNEJkdElXUW8xUGlzOUdkMmRGNU9pNVB1SU1naSs2SFEvRlpzbHdjbXpiZVRHUDZoS3RSZnlCNmxKai9aclUrL1NhUDVxU3FsTzZndkErditRdFg0K3l5Y2g1K3dzbmk0L25RVFZ0SDlVK0pObmllVjJ1MDVmcDFqekwvbVh3QW52Y1dGV0R1NW02dGhrK29LY250QjZjUHpybjBaZGlaOThZZ1BoVFBMZkxneHhLWlVvQXZrWi9DcWhQSS9tcUYrZWZtZlhZSjNFYmRhcWZOc0pnam5iaUdrUnJrVHMxYnFob1JLeE11SHBOMStCek5RK0hvUnFQQXFLYURWU1pFdHRKd0pWMEhVVVFXUnRVNjY4Nko4cDQ3dnBVT0FHQkYyTkpIRlFEVnMxUHFpODRZTkVhY0x5dU03Tkd5RnlVSGNIanNBWnJiMmc4Tk43Q2lLMVVPcW5uRWRrTHBmSTk4ZktvTGRUUkEreGJsenIvZzcxTysza0cyTXNYYVZ6bmNwSWJQWkk0TXk5emt2bHJ2VW9DMUNSTmdmUlpzdklYNkEiLCJtYWMiOiJlODhmMmU5MjRhOTY1MGRhNjNhOWY5NmUzZWM0ODVjZGU3YTFkMjU1NjgyOTkwYzcxNGRhOTk2Y2RlYjc5Y2I1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZuMTBERlhxUGc0Vmx2RjJQOURNOEE9PSIsInZhbHVlIjoiamYrcUE4cVZyWEhyTFArOC9ZeEpZTnlHL09vRVViOWkvVXppL1VrTmVMSWFXbUk3QjdIa09FZEo4UVNIZkR1TnA4TFBKbmtCQVkyU05kZHlxeUorWTRDNEhySm9pSnlMdTZoSkZHUDNBRWRwcTAyZDQ3YjRsSXROQ1M3U21uMnBxVlBmUnFGRDFZcDhvSGtYcVhHem81akY2NTNFMFJyU3dTOUw3ZGNMYUZOVGd4WWJrVVd1d2pLZE9ZZEZyb3pUbm1qdFBSYnMzVjFseW9qTDVMTDlXSDZGaUVWS1dEWGZZM0N4Z0RLTDA1UVVaOWtBQmNYOHdGalRMV1hYSEZnS3lkWHFRODFLZHdoM2ZjRlZwY284TFQyd0RKZk9vZGtVcW1hcWxEYTk0WGl4WVJaQjlhblROMjZKL1JHR2hsbjZKS0FSNExFZk05VHRRQzduc1lxT01YaG5ubXo2QWpwSXlqeG53TW8xUWFOMXpua2pSRzdsVE5TOWFBRHhlMDJWdFc2U0c1MW5MeTJGOVBubzVEY3B1bHdKcElLWmlEU3hMN1ZZL25YK0tMREJGRENBT2pJUUR6QWVKUU56TFJYUk54SkpWWVZsZVdWdzVaMGp0SXRpZnMyYlJ4TXVPdlZ6ck1TczhLd1RHaEd3YkNqR0p6cXJUSHIwMlZDamc4Q0kiLCJtYWMiOiJiMGY5NDZiMGM0YTMyYzcxMGE2ZGYwOTA3ZWI5Njg2YTliYzE4NTgxNmUzMjk1NGQ0NDU0MzYwNTVmZmMwNzQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InBPT1hWRWJRTkNONnNtL3ZlTlJtZ1E9PSIsInZhbHVlIjoieGp4RWdMNGw3VW9zUlJ0Y2FlNGdWYUNhN1lPRk4wL0RLTUk3MDV5OFVuK2xsQ3hUV3ljeHJDV0Q1Ris2LzF6cEM5dnZJcWs5alJOM2prekMxZWNYazlxNGVPa0pwTFhvNEJkdElXUW8xUGlzOUdkMmRGNU9pNVB1SU1naSs2SFEvRlpzbHdjbXpiZVRHUDZoS3RSZnlCNmxKai9aclUrL1NhUDVxU3FsTzZndkErditRdFg0K3l5Y2g1K3dzbmk0L25RVFZ0SDlVK0pObmllVjJ1MDVmcDFqekwvbVh3QW52Y1dGV0R1NW02dGhrK29LY250QjZjUHpybjBaZGlaOThZZ1BoVFBMZkxneHhLWlVvQXZrWi9DcWhQSS9tcUYrZWZtZlhZSjNFYmRhcWZOc0pnam5iaUdrUnJrVHMxYnFob1JLeE11SHBOMStCek5RK0hvUnFQQXFLYURWU1pFdHRKd0pWMEhVVVFXUnRVNjY4Nko4cDQ3dnBVT0FHQkYyTkpIRlFEVnMxUHFpODRZTkVhY0x5dU03Tkd5RnlVSGNIanNBWnJiMmc4Tk43Q2lLMVVPcW5uRWRrTHBmSTk4ZktvTGRUUkEreGJsenIvZzcxTysza0cyTXNYYVZ6bmNwSWJQWkk0TXk5emt2bHJ2VW9DMUNSTmdmUlpzdklYNkEiLCJtYWMiOiJlODhmMmU5MjRhOTY1MGRhNjNhOWY5NmUzZWM0ODVjZGU3YTFkMjU1NjgyOTkwYzcxNGRhOTk2Y2RlYjc5Y2I1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1991553069\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1874635526 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1874635526\", {\"maxDepth\":0})</script>\n"}}