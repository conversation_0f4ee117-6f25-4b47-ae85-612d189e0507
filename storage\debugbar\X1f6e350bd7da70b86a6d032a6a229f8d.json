{"__meta": {"id": "X1f6e350bd7da70b86a6d032a6a229f8d", "datetime": "2025-06-27 00:28:43", "utime": **********.057265, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750984122.526779, "end": **********.057279, "duration": 0.5305001735687256, "duration_str": "531ms", "measures": [{"label": "Booting", "start": 1750984122.526779, "relative_start": 0, "end": 1750984122.978994, "relative_end": 1750984122.978994, "duration": 0.45221495628356934, "duration_str": "452ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750984122.979004, "relative_start": 0.4522249698638916, "end": **********.05728, "relative_end": 9.5367431640625e-07, "duration": 0.07827615737915039, "duration_str": "78.28ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029216, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013730000000000001, "accumulated_duration_str": "13.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.020378, "duration": 0.0128, "duration_str": "12.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.227}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.043961, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.227, "width_percent": 3.496}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.049925, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.723, "width_percent": 3.277}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/users\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-726548500 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-726548500\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1812957151 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1812957151\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1261449734 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1261449734\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983962085%7C58%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InB4UGxCNmxlRy85eFZYREttYWxIS2c9PSIsInZhbHVlIjoiZnNUWFV2d3ptRE9CNlRBY0czUkVLa1ZFNS8ySlBJWkZ3RWQ1R2dwbkVuUi85bWlBcXlRb1VuV285VFdRVEIraktWT0VoRlhLTFJuem5SOFdPZTRoYnlYS0Z6emlCWXdOYis0cTQ2RjRLV0VkWWxmRVhEQmJpQ3lKWXZSWURFWjBKZWQvV0p2ZldxbTVuVlIzWERqcTZXVlF2bE01QjNlaXhkTHc1M1hvb3JyemhyL0tHSFByOVJHL05Yc3lWSy9JSTd1TElLZ2xma1Qzcmd3TkJNNWliSlZhRDVEUWQzOTdPTitHZnBOYnl0ZTBkSmNvZDUxUXFkYkk2TVVSUVRhaTNwdGZuT20rbzhjM3hOZTd0L3huOEx5VUZhcFg1UWhKdVozeFhjVUxPWEU3VGM3dFVINzVMWFVCVWFZUkV5NU1iYkNURXQvSEhxMC9vNFpEVXAwRTN5NXZtTjJQZXduZXQ5Ukd2dzNaT1hsRU1XT1dONk1ycGF3T2o4Q2lCRzUxVjBIcmUrYTFDaFdSTVJCUUNhR1VCMnBBMFBmalREa01vTkk2bkpVclBKTGhWSElHVDErNDJzWU0zdDZ5dHNLb3ErNkVYSCtSek9iN0F0MStaSzNaTHVwd01hcnFvb1Jxa2NUdCs1NjZGTTlKSmxUdlNkNWVCcjd6bllLaE0vR3QiLCJtYWMiOiIzNDZkNTM0NWFhYTE4ZjI2YTliY2Q1OTdkZWVlNTg0YmVlZTQ2YmJjNzhlNTkzOGQ1OWJmZTM5NDIwNGNlMzRkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVUTnMzMWFoM3JmRDJuR1FSQ0dkS3c9PSIsInZhbHVlIjoiZUpPN0pJYUxUU2ZhN0gyVWZSUFRlTGErTDdhYzRFNDhiRFVoWlZMcTFvenhldlRiYzFsYjMzbVZLWUZQRzduSmkrbU5iNm1SUHNXS2NJMis1SDNFN1UzLy9NUnhXYmQ0RXRndWhKR3M1UEFCMXJ5b1FieVJSazVjODl3L2V4aW5Gbm5ibk91WUV0MFJxYnhSNXpFMUM2dWYyOXdhLzJRU3pFS1lQTGtOYm5KSHdwMndUYVZVNHAvOWtGa3ozN3dNSXJSSExMV0NocDNVSlVNRFQ4OUZudE5MRlFuNzFUcWZQWHBVT0RwdFcxTUlJMkh1VXNFWDFhdzVuV0FzU2ZqZ3NTTjM2S2JIZy9YNVNTM3hMemw3alRZVjJ3SWlBZTl4TVVNWitLcG9KR1h0Wi9ucjhaTmtZOGFXQWhZV3pjWkZSQlBUYnVJejhJUjBwNHl2TzVVTjhEWWdPWDAwd3BGTEkydTJNaUNMU0RMNHFZdFRzOVZrSWFoS2MzZUUwcVYrU2lJaUlPdjdJOWphbSt3NUFsQmwxeGpha0pUQ2ZweDhJRjZLRXQ5a0hva1JZTWpyYmt5OVYzOXFtZGt5dXBaZ2pNVytPNGdmWERxVmUralIxSlc2RkxCVkFrbU5zd0I3T3hsOTRXcTVXa0dRTGkvU3ZkVTF1ZXhwcEE5VENqZnYiLCJtYWMiOiIyMjM0MmI5MzNiNGVkNTU1YWZhYzZjZjI1Yjg1MzNjZDA1ZWMzN2RiNTY1ZDQ1ODkyNWRlOTUxMWM0MTdlMmM1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-987367829 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-987367829\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1673189543 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:28:43 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNCLzNwMkYwS2V4eENpd1h6QXVmWWc9PSIsInZhbHVlIjoiem8xZzJ3VElqS09BTmhWRHRkejQweWlkd25LSENXbUkxZWs0cEc2ZWFiTGtpYWNTdDd4dFZRVEc4OVhkK2ZwbzBiNGtpRmY4Sy9ObSt0WFA5cklOT2ZKbDY3ckJlb3Nwa3BxRmpVcXFUM05xUGRnclpJcEUzN2NjaWpBS1IwaGFSRWxNVzF5WWpJcjJ0cCtOdzlkNWZocUtnR2FWQVBram9EQksrakRoTmkyMlJTanM4WXZFLzFNRXJlcTFTY2Frbyt0TDU2eTR6a1JkQzhMVnhoWUxZby8zNUZ0QTR3SVB3cDV2MS9wbzE5N2JvTmpQNDg3YWZxSUN5WjczVWd6R29aT2NEWUEzVVlLS0g5N0J1aXpMeUFCM3BwcFhaZzJ1dXhWSS9NaFYzL3pGSml1d0V4SkZydE5XOXE5OURWSjZHQkszMnJmTWpGK1docVVOaXBQUHRyd3l0VG5OWDVkUlM4RitiU1hYRFExM3dBQmdhZUxwUWVrdW41Z2ZscUk5elcyNHowdG9BcjFOY0lFM2FuRjd0dkE0VXljODFWOGpucWl0OGxkR2IxWUZEZjN5enJTZmJvQ09yWFRxZ2x0WkgxSUVDYjhYbmwyQi8vR21aMWRDMW4rL1JOZ2c3R3dsaEFoZEU3dk5ya2VYTEo0SlEwUzhnNGtFTUVzcmdVR2giLCJtYWMiOiJhNDUwZTA5NmI0MjgyZDk0YjAxNGZlNTczNWU0MDQ3NjdjNGU5M2I4MzdkZmY5MmMxNmMxY2EwYWI3YzhmMjk0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:28:43 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im1sekE0VGVSMjBjcm9pdVNaOUlzbmc9PSIsInZhbHVlIjoiS20xTEpldnJaL3JIT0FqMGR1UlV0OWsvQ0MrUmdiMk5jMk1CdUltUW1IWCtXV3pBK3pabGMvcVlrWHYrMFo3akE4eFlWWGY0YjRPNnM1TkFFSjltQUtLMnBTRGllK0ZQaU02SktEUlc0dmx5aUNTYUhuVDA2WGJIRnc0MjE5bi85VzdaQlhpWmd5UDl3aElESWlwei9CSFdmMjRobFdiK1dlcWRWaWJMY1hJMTB1eE9ZN1R2c3JGQS82RjF5WnRHV0xFMURvUytnZ0xacThPR2VpRmNvMVF1R08wR281dXEwK2Y4bFFXUzdOVWFjdFBmemZLMG1ldmUwVXI1eVgrazUxNUZLZVBWSGRvdnBzSHEyZHBBaW1jWHhjbjBHQ3NmQU1tMWtSK08wM0tLQ1RKRmswZnVLSWdDZnA1d0JId3hnQVZmVWh0Y21iN3dHUENDaGt0emRmZzhwWDIxSUJUVXA1NUNuaGtRUWNMTUR1bDQ1ZzBqMjJiRTlmV2VIRy9kSldoMVpSVVBSdHhqdnlBQjFSU1ZzRkd4am15c2gyYlZ5MVIwTC9EcWs2WFQvWlFFeEtzUmorclhCRDR4bVllR21Ya3VQYlYwWUxqc3ZkVm90c21hRFIyZzl3SFRjVStYdEFUM3RzSmdETkxYSGlIUFl6SXZ6YTlPU29VKzAyMVQiLCJtYWMiOiJjY2FlZjdkZDM1N2EwMjViNjE1Y2MyZjU2MTc0MjYxNWNmZDlkNGZhYjIyMDJmMGUwOWM2OTk2OGZmMGJmNjBkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:28:43 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNCLzNwMkYwS2V4eENpd1h6QXVmWWc9PSIsInZhbHVlIjoiem8xZzJ3VElqS09BTmhWRHRkejQweWlkd25LSENXbUkxZWs0cEc2ZWFiTGtpYWNTdDd4dFZRVEc4OVhkK2ZwbzBiNGtpRmY4Sy9ObSt0WFA5cklOT2ZKbDY3ckJlb3Nwa3BxRmpVcXFUM05xUGRnclpJcEUzN2NjaWpBS1IwaGFSRWxNVzF5WWpJcjJ0cCtOdzlkNWZocUtnR2FWQVBram9EQksrakRoTmkyMlJTanM4WXZFLzFNRXJlcTFTY2Frbyt0TDU2eTR6a1JkQzhMVnhoWUxZby8zNUZ0QTR3SVB3cDV2MS9wbzE5N2JvTmpQNDg3YWZxSUN5WjczVWd6R29aT2NEWUEzVVlLS0g5N0J1aXpMeUFCM3BwcFhaZzJ1dXhWSS9NaFYzL3pGSml1d0V4SkZydE5XOXE5OURWSjZHQkszMnJmTWpGK1docVVOaXBQUHRyd3l0VG5OWDVkUlM4RitiU1hYRFExM3dBQmdhZUxwUWVrdW41Z2ZscUk5elcyNHowdG9BcjFOY0lFM2FuRjd0dkE0VXljODFWOGpucWl0OGxkR2IxWUZEZjN5enJTZmJvQ09yWFRxZ2x0WkgxSUVDYjhYbmwyQi8vR21aMWRDMW4rL1JOZ2c3R3dsaEFoZEU3dk5ya2VYTEo0SlEwUzhnNGtFTUVzcmdVR2giLCJtYWMiOiJhNDUwZTA5NmI0MjgyZDk0YjAxNGZlNTczNWU0MDQ3NjdjNGU5M2I4MzdkZmY5MmMxNmMxY2EwYWI3YzhmMjk0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:28:43 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im1sekE0VGVSMjBjcm9pdVNaOUlzbmc9PSIsInZhbHVlIjoiS20xTEpldnJaL3JIT0FqMGR1UlV0OWsvQ0MrUmdiMk5jMk1CdUltUW1IWCtXV3pBK3pabGMvcVlrWHYrMFo3akE4eFlWWGY0YjRPNnM1TkFFSjltQUtLMnBTRGllK0ZQaU02SktEUlc0dmx5aUNTYUhuVDA2WGJIRnc0MjE5bi85VzdaQlhpWmd5UDl3aElESWlwei9CSFdmMjRobFdiK1dlcWRWaWJMY1hJMTB1eE9ZN1R2c3JGQS82RjF5WnRHV0xFMURvUytnZ0xacThPR2VpRmNvMVF1R08wR281dXEwK2Y4bFFXUzdOVWFjdFBmemZLMG1ldmUwVXI1eVgrazUxNUZLZVBWSGRvdnBzSHEyZHBBaW1jWHhjbjBHQ3NmQU1tMWtSK08wM0tLQ1RKRmswZnVLSWdDZnA1d0JId3hnQVZmVWh0Y21iN3dHUENDaGt0emRmZzhwWDIxSUJUVXA1NUNuaGtRUWNMTUR1bDQ1ZzBqMjJiRTlmV2VIRy9kSldoMVpSVVBSdHhqdnlBQjFSU1ZzRkd4am15c2gyYlZ5MVIwTC9EcWs2WFQvWlFFeEtzUmorclhCRDR4bVllR21Ya3VQYlYwWUxqc3ZkVm90c21hRFIyZzl3SFRjVStYdEFUM3RzSmdETkxYSGlIUFl6SXZ6YTlPU29VKzAyMVQiLCJtYWMiOiJjY2FlZjdkZDM1N2EwMjViNjE1Y2MyZjU2MTc0MjYxNWNmZDlkNGZhYjIyMDJmMGUwOWM2OTk2OGZmMGJmNjBkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:28:43 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673189543\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-810921442 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810921442\", {\"maxDepth\":0})</script>\n"}}