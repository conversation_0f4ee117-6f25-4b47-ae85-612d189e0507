{"__meta": {"id": "Xbbc83842273f2ca6680d49897c592c93", "datetime": "2025-06-27 00:14:48", "utime": **********.824771, "method": "GET", "uri": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.357344, "end": **********.824787, "duration": 0.46744298934936523, "duration_str": "467ms", "measures": [{"label": "Booting", "start": **********.357344, "relative_start": 0, "end": **********.730203, "relative_end": **********.730203, "duration": 0.37285900115966797, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.730212, "relative_start": 0.37286806106567383, "end": **********.824789, "relative_end": 2.1457672119140625e-06, "duration": 0.09457707405090332, "duration_str": "94.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47521888, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.show", "controller": "App\\Http\\Controllers\\BillController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=333\" onclick=\"\">app/Http/Controllers/BillController.php:333-393</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.020739999999999995, "accumulated_duration_str": "20.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.763227, "duration": 0.019219999999999998, "duration_str": "19.22ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 92.671}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.791302, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 92.671, "width_percent": 1.398}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.79873, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 94.069, "width_percent": 2.845}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.812883, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 96.914, "width_percent": 1.784}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.814661, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.698, "width_percent": 1.302}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => show bill, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-676132606 data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-676132606\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.818063, "xdebug_link": null}]}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-1840630621 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1840630621\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-378547152 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-378547152\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1450554133 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1450554133\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1572474036 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983282490%7C45%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InV3V3d1Zlh4NkoxQXZYZFZudzlPOGc9PSIsInZhbHVlIjoiTVI5bXlZa2ZiR1ltMHJNS1JsNFNLMWZ5UlBXaTI4N2dpZVhkWkdDM1A4bXNnZUUvS0t5eXZGMmg4cm1Tb0FCeGcrb2R3OWEwcE56dGtCUU81bXUvTUJMeDVNQ2lpZ2o3VjV1VllLK1FsNHEydDNrb1I3YUtSU0hnV3l3Unc2TlFDWXFsVHowSzFEWFo4a1FmSjJlWEFQR3pCY2ZoSGROTUpKSFg4VVR3alNzbmh5akZGNzVSNndUSHdhMTJsWElIOEdIMEhCbWIyL2N0UnFQcGdudXZiR2U5YkQ1bTBYUFZiY2RYb0RKRTYxcmJWclJyY0oza3VWNmVBWEc0VGYxZU8zeExKUnVjY0x5ZEFMSWxyM1BsSU9SK3FQd3BRWTk3dFBIRG10ZTBHWW0zNHNidDJxRjZ1UUFVLzVCaGxjdS9TMFZhQnI2b05hTk9UbThSREw1SFhMMGRaUXdhOFk5SC95bG9uOXFYOHZ0TU1LdGxHVnNFTmY0V3JjdFk0RHpKTzFXclF2UHdLdytlbC9Tcnl3azlyajg4ajVQSXBVZVB4dGIrRlhjSGlDMFF1Mm54bmRCRUdIOVh4alo1a2NZeWhrS1dKUk1lS2lXMjdTbWtGNDhQd1FOT1FZTFBGZHU2VHkyS2g1Y1hVMEFhVkRDL2hLZWhxdHJSVlp6VmxxOW4iLCJtYWMiOiIwMGI1NzE0NDM3MzkyNjg3ZmEwODdjNTEzZDZmY2EwNDYxMTUxMjJjYjQxMWE1M2JhN2YxZDY0OGIzZWQ1MDcwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImJqbzVUSm5Nc2VYQlhHWFdpaDdqQUE9PSIsInZhbHVlIjoiQTR0K2tselpVWW1mY1o2R1IwdjhPRFd6MVhOaTMwaHc2Q0U1ajViK3FYTUcvL09ybEFBREJLTk51N2MvMEZGdi94N05mUlJMRDZkbWdwK2NTejM2Sk94LzVRUmJtZG40MFRTMkQyelhrcDl0U1F3cnYzQ1R0RkorcTgzeVorbFhTQ2ZRTG5zVUxaYzhsMktZUTBvb2JPK2JSd2tTQ3lxOWFWblRpQ2Y0dXpCd2NoTkZOZUtBc3Y5M0ZUNkRqY1FmNXZ5TE9wL3ZlRzdKTmZUWkRUK09uN1QxQ2g2ME96cXZ1MGY0aERKd055Q3I0dFBpM3dOb3RqNVNHdEEwSzB3cllvNm5KdHZmeTcybzRDNkljVVIrSk8zeGF6YUVScUlxbFVOOWFBMGg3MGFrZUtCLys4QzdjaDlHZVdVWFpCNG5xTlJrQklhSXpDMG5SSUZnWjNZWU5Nc3BicXN3UGRHQVA0U2VabnlTZzhlYk44aVpTRTkzOG5oRkI1WUpwU2ppZExxaDhrRk8wOElja1E0U3ZtSmQ5UFRYbmZMNUtYNm9PcUlBM3NGeUZsc29yUGFKZTErcWc4M3JFd3paT2pLRkNzVHRxWFRpeVhWc3R1eTduR0k0VFNvYXFTeWFoeXNJUXB2Zll2NkpCK0dyeDNBOWtQRDVscWtkMzVzUnJYWFIiLCJtYWMiOiJhYTkxZDQyNmI3ODI4YzdjY2JhMjdkODE2YzdlNTRiOTQwZGUxODRjY2JhZGZiMDAwYTFlMDEzNDZjNmMwNDZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1572474036\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1274259478 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274259478\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-934333720 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkU3a292QTdDYVBLTWVrZGFrQk9vVXc9PSIsInZhbHVlIjoibDJ4VUo2OXZ2YUNqd1NFY3JKT243Tk8wMmdiSnljQTlKNDBVazZLd3ZqMDZueXFJZU9XZWdOVkY4bHFVY1pSdm00TDB6bElwc05sS2d1VHljN25vakZZb1RyMi9aa2Z0VS8wYTFTcXNEQjBnTHBLL1RYQWpvQzV5OVpqWVZXV1VpWnI4WHR0K3FSNWpnZXhZSXl0K2FPblpMOStrdjd3aGM3bnZkS1ZvQi9LSEx2WlpqU2NxYnVVcEt0ME15N0xTdkIydVNBQy8wYTZGbjdQblFlTjhsK0ZzRG5TYUVSOTFFK2Vkc0pYZTQvQSswWFJlSERUTkVxSXMvczVpOG1sQ3dVQmdBTk1hYXozNC94VWZJNFA2WUs3d2xtR2laZXdZVTZUdnV2N3RpcGtBZFFKY1NPcDlWaHRjSWxsekI5cTNYQ2pVQ3ZYbk1EZnd1b2tUUkg2SFI2R052TE41M2s0RlJjekd2dnV2L01INmdGKzR2Q0RHV3djTmtaeUxwZkJocnlWTiswSEhvdk1IQUNEZzFsdVhrREVFTS91cXJJN0dpdDZaaDAwZ3ZhNjl4SnVGMVRMUmFpRThwcklUVTRwYTJnQWExaU95bzQrWWNpZFZHSjlkSUNaUEJCOWJDRUR1Mm5JMDUyVVZwWVlXZzRISVRYUjhlVEdXQ2xCRUNGTG4iLCJtYWMiOiJiYTBjNGVlNzc1OWExZGMzY2FkYWFiOGVjNmUzYWMyMmRjMjFjNzlhM2JhY2NlYjliOGEzNTNlMjgxMzJhZDJiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlBmZEFtSzFQYTRjaEhVWEc2UjgxQVE9PSIsInZhbHVlIjoiNW14Z1dYRXMxb2d2QW1MZERnRUVYUXJnWUcwNkhNVXNqM1ZaOHMvOFRLTUhKWlNrc2ZlbWxQa29OYkJZaDlQV0RWY2JtdnNhU3QzZWVJTXhlSmlhK0JzMzgvNy80NzBkKzVJZ3NETklreHp5L25QMUd3Yk1MdFpLWERTM2ZyZ1FneFgxNEVzUHJ1VmlYNkhVZkhSWEdGU2RRSTlIVE5qaGQrNEZxU2dqQVFBUVpMajd2cVRZTVgveCtUUTlWaFZNMm5udFNYaDE1Z1JQUmJMb1RsOGJjQ0xhLytpbjRVaE1tZEVYcU5ReXJNTjhCZWhBbWl6bFFPRFFtMi9aSXd3MmdpTVhOOXZxWmk3MWF4RWpMbUNXSFdoSnlYV1hSYWpiM1NONExtN0lhT25Na0ZkRlUvTnhhR3B0bEI3S3RsSGFkSEl4YlBMVjd0RHJOUnBTZFM5SEtZV2RJZkxRMmRqVFJYdmdPazdhaGtSbWVLNnhLN1JnaWRvL3JndUczSGhXeXRTZmdxSkJBUWt4OCs4UnZKMWYrMzd0b04vc3VYYjJVU3RwcUFzNGFtTzFPd01VelZZc3JGWGJtME1mWFVNc0xWc1RKOGtpTDZvYWd3SXdGNEtJUUNpU1NDWmpZN1RQazJCOVN4UlBWUDFEM1ZqNVMrVTVWYU1rUE9Tb2E0ajUiLCJtYWMiOiJiOWI4ZWMxN2Y4OWI2N2I0ZjkzOGYxZTBjZTViNjcxOTUzZjcwNzM3MTM0MTY5Yjc3NGE2YTkzY2FiY2NjNzAyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkU3a292QTdDYVBLTWVrZGFrQk9vVXc9PSIsInZhbHVlIjoibDJ4VUo2OXZ2YUNqd1NFY3JKT243Tk8wMmdiSnljQTlKNDBVazZLd3ZqMDZueXFJZU9XZWdOVkY4bHFVY1pSdm00TDB6bElwc05sS2d1VHljN25vakZZb1RyMi9aa2Z0VS8wYTFTcXNEQjBnTHBLL1RYQWpvQzV5OVpqWVZXV1VpWnI4WHR0K3FSNWpnZXhZSXl0K2FPblpMOStrdjd3aGM3bnZkS1ZvQi9LSEx2WlpqU2NxYnVVcEt0ME15N0xTdkIydVNBQy8wYTZGbjdQblFlTjhsK0ZzRG5TYUVSOTFFK2Vkc0pYZTQvQSswWFJlSERUTkVxSXMvczVpOG1sQ3dVQmdBTk1hYXozNC94VWZJNFA2WUs3d2xtR2laZXdZVTZUdnV2N3RpcGtBZFFKY1NPcDlWaHRjSWxsekI5cTNYQ2pVQ3ZYbk1EZnd1b2tUUkg2SFI2R052TE41M2s0RlJjekd2dnV2L01INmdGKzR2Q0RHV3djTmtaeUxwZkJocnlWTiswSEhvdk1IQUNEZzFsdVhrREVFTS91cXJJN0dpdDZaaDAwZ3ZhNjl4SnVGMVRMUmFpRThwcklUVTRwYTJnQWExaU95bzQrWWNpZFZHSjlkSUNaUEJCOWJDRUR1Mm5JMDUyVVZwWVlXZzRISVRYUjhlVEdXQ2xCRUNGTG4iLCJtYWMiOiJiYTBjNGVlNzc1OWExZGMzY2FkYWFiOGVjNmUzYWMyMmRjMjFjNzlhM2JhY2NlYjliOGEzNTNlMjgxMzJhZDJiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlBmZEFtSzFQYTRjaEhVWEc2UjgxQVE9PSIsInZhbHVlIjoiNW14Z1dYRXMxb2d2QW1MZERnRUVYUXJnWUcwNkhNVXNqM1ZaOHMvOFRLTUhKWlNrc2ZlbWxQa29OYkJZaDlQV0RWY2JtdnNhU3QzZWVJTXhlSmlhK0JzMzgvNy80NzBkKzVJZ3NETklreHp5L25QMUd3Yk1MdFpLWERTM2ZyZ1FneFgxNEVzUHJ1VmlYNkhVZkhSWEdGU2RRSTlIVE5qaGQrNEZxU2dqQVFBUVpMajd2cVRZTVgveCtUUTlWaFZNMm5udFNYaDE1Z1JQUmJMb1RsOGJjQ0xhLytpbjRVaE1tZEVYcU5ReXJNTjhCZWhBbWl6bFFPRFFtMi9aSXd3MmdpTVhOOXZxWmk3MWF4RWpMbUNXSFdoSnlYV1hSYWpiM1NONExtN0lhT25Na0ZkRlUvTnhhR3B0bEI3S3RsSGFkSEl4YlBMVjd0RHJOUnBTZFM5SEtZV2RJZkxRMmRqVFJYdmdPazdhaGtSbWVLNnhLN1JnaWRvL3JndUczSGhXeXRTZmdxSkJBUWt4OCs4UnZKMWYrMzd0b04vc3VYYjJVU3RwcUFzNGFtTzFPd01VelZZc3JGWGJtME1mWFVNc0xWc1RKOGtpTDZvYWd3SXdGNEtJUUNpU1NDWmpZN1RQazJCOVN4UlBWUDFEM1ZqNVMrVTVWYU1rUE9Tb2E0ajUiLCJtYWMiOiJiOWI4ZWMxN2Y4OWI2N2I0ZjkzOGYxZTBjZTViNjcxOTUzZjcwNzM3MTM0MTY5Yjc3NGE2YTkzY2FiY2NjNzAyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-934333720\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-594919061 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594919061\", {\"maxDepth\":0})</script>\n"}}