{"__meta": {"id": "X9eecc0a2e7a2e436c739b8cd21a2c111", "datetime": "2025-06-27 02:25:31", "utime": **********.134054, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991130.714528, "end": **********.134069, "duration": 0.4195408821105957, "duration_str": "420ms", "measures": [{"label": "Booting", "start": 1750991130.714528, "relative_start": 0, "end": **********.081661, "relative_end": **********.081661, "duration": 0.36713290214538574, "duration_str": "367ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.081669, "relative_start": 0.3671410083770752, "end": **********.134071, "relative_end": 2.1457672119140625e-06, "duration": 0.05240201950073242, "duration_str": "52.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45409464, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00241, "accumulated_duration_str": "2.41ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.115637, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 67.22}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.125226, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 67.22, "width_percent": 15.768}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.127934, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 82.988, "width_percent": 17.012}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1680514889 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1680514889\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2106618967 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2106618967\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1432621245 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1432621245\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-967107942 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991127468%7C23%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkIySUMzNEdab0VRVW9zc3VUQUw2QVE9PSIsInZhbHVlIjoiN0lnVzV0eWF6cURja0xMYkxpbzBGcnoveGtIeWh2MU8wZ3JlODR3dlo3RE1pSEZUWk9XQTR2cTIxejRPVWIvcTlzMzN5R1BGeCs1NG5nUTdHRnFBSTVZQUR1ZnNHRzlVQm43VFIrNnRLOStMaGhpRUtPMVBnRi9VRFdQWWNQVGZ6T3lUbjEvcEJkVDVzOGovQSsrN1h1QWVCTHYrTk1ENkU2K0YzR2JUQ0lqNTQ2cWtIZW9JSG4zVE8rMW0xNGxKTW93UVhkRXB4VGJnb0NwYTh5cUExT2VIS3RUOE0vb1NHT2FnMTU2ZDZneWZ1OW13K24xV1ZNVHV2dUQ2aEdlbkpZZUlBZkhXWndYRSt0SU5PeXdiemlzVkg5b1duSWk5aUNPcEphS1FMTUJidHdyV0FVVnRkL01EZGhVUkw3OHVFdVZaTTdWTVh3YURDNWt2T0k3bFUzMHN1OTRVTGYweFpaRWt2cWhIT2dVRldSeUNhQm1LaUlsU2hoL29CRHczNzJGL2I2MEZucmtRSjlvSS9LYkZWWWp6OExRekJkUUlpVlI5bmZNbFZlcmFZVkRWdElwZWRid3ZKcS9mcVdaMU10UFR5OWcyU1Mvcjcra2JWOTZFK1pZZ3BtUTd3dXFtbkpyTkRhUnZFRFphdkxnN3BSNDNvREpuOE81REFUWmQiLCJtYWMiOiIyMTY4ZjA1MTBkNTgwNTAyOTNhZWJiNzc1Y2I2YTA3NzE4NmZkYmMzYTVlN2JiMGM3ZGUwNDEwMmFmMmRhYTNkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNiakp0eWxIRUkvMVlDZFRDL3ZRN3c9PSIsInZhbHVlIjoiMXBOZWM5SWlSdnA0TTZIR1A3TkJFYWJYUFY1Z0I3TGdIejdoTjAyK2FVUzRweFRGS2Q2T2xTQXJhQUcxUjZJajlDdWNEb1NXSVJwZi9XSTdxMnFLMGYwQy9vVTZ2a01sR3dBY2tSbnQxVGdSSzZMRW93WG9NUVVrVTdqNnY5TElxbS9xcUxTeUZFOVRFT2NveEJ3blpwVzZjMFpScllKUjRQbnpuVmc5Ny9OaXB0a2JUODVVa2N4amtJMWFxOGlPd3dHWGFEbjVhaG1rQ0Jwa2MrMm5EdXR3SXFZWHJyL2VKSitxOFRXR29VQzF5UFpFbXpLc0xGZVVQeEU4S2RoZkhaWmZyQVU5SGxEa1JOanIxRmpuMVZ4T3pVMFNES1c2RHA3WFFLeFE5WEhUQWI2dlNYb3l6dEhwSFJlNnBualZCV1VkVEN3QlozdnRlVDRrZmNlalpJd0VDMkNQb3NKcDBWbTZyZFpuK3F0c1Jvb2dpZTRyTUN6U1N2M2hWVWVlWW85bFplN2xiWCsrTGhtRmg1RUttbDBKUHlkV2pHZlByZWRuaXJ0SWdNZXJRdlNicEVJd3haMUxlNFVQQmIyVXFEM1ZZNVZvN1Z1bjI3RUp6MmdzVmV3Z3VReXdFVS9BVVBCdUk0K3Y2WWxzQkZJeTl4N1ZPazE1RkwrUHlhdmQiLCJtYWMiOiIyNzE4MzFkM2IyY2MxZGY4YzY1NzNhZTlhNzc4NzgzODk5MmVjZDI1Njc0MzAyNmMwNGZjN2ViMGQ0NTUyMjc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-967107942\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1867191134 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867191134\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-777235937 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFvVEljdEhWajF3cUc2S1pac2tsdXc9PSIsInZhbHVlIjoiUXdiSmtvUWk0SlhJMm5IS08wcWsrSGxZR2hiUDV6cHlvcW04aUxqZ25YS0JHdDBGbks2VWNyREIvQ3BLd0ZZSXpDOFpydHpaZncvcFg0bTdWVGw1eHg0a1RLWGRBbHZmYmMzck56d0NzdGtVZk1wdEtjdkhPVS9QVDA4YUN4NWNTRzNyWTk5V0owTGNQT1kwWm9lb0Y5Q3FwNWlmS3E0eXhReTZ1SlpGa1R5YXkrQk1nSXZUcEJlS3prSWRKUEJrRW9oaXBqZklIMlFQT0MwbmV1YWZrdUVFc3p4UzM5UmdkRExxTEpCV1ludUVqNmNJanFFSVplWk1ESjlEVjJMelVXMGYwYm0wQzNEbzVGTTlQOHpBcUdWU3lQSW91SkwwQ0N4bXRvNFllbTd0RXNVcFpSek9BaDI4NkhwWkQyUmwvdDg3NnhvVFFzQXRxc3V4ZUdpOWlDOGk0V2FITTkwM0FUQzNTZnpYWUx1eEI5RzhTdmordXk0Q0MrQmdaQkYxaUZ3MzA5d1BGUWZKR3FoSGZhMVhPZ2VZbjU5eURlcEl1dklTWlVzWUo1UTNNUytQMG9qdnBHRHRJODJmcDBMdUF4U0tpekJyeDdJMFhRRWs3UjRsUW55UmF0SVk0ZzgvWmZuemxZRTVIdUc4ZTdLdEl1eUlaejY4YXllcEpmaHUiLCJtYWMiOiJlNmY4MjBhZWExNzcxMGZjNjlmZWQzYTU2ZGFjMmQ5ZjkxZjQzYjcxZTE2ZWRkMjEyY2YwOWQzYzRhZWUxNjgwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im0yM1ZpUlh1NXl3M3ZZNDlkR09naXc9PSIsInZhbHVlIjoiNi94NnRjL3JoUm9qTE14a2Flbk45ZFQ2RzF1VVIvdzRkUkpBT0owT3pkNm84V2srZ2Q0ZFZXZWtoNkNHbFNOOHNLcW1HK1c5WjFZZXcxdWU1aEloV1hqM3NSWGVtbFdMUTd1Z2ZBaXhNZnV2YUN2cE5ucnhPdkYzWnV2MHNaS2JKV2NFczFuSzlqRjhtMXhNWGZKc2VRcVJoNlhNNEJvRUV1SEdKYTA4Y0w3ZG02MXBoSkFqL0l1WnJzdGRabHZ5TlhsMFBzaE1kVW5xSlJadGQrTFUzM3ZGQXFhMjFpS2NCYk9NalQwd0d4ZEgzeDJHSWJUNnV4L01KaktmV0VadEFkMlRtTVVxejN6cFJsMUtHN2RVRGdFTUxzZ0xKNlFCa3diK2ttbW1ucThoc1AxUHRUSjhqQ0xGS0VDdXNKOUNtSFdsY01ZRXpKRUdWV0xLbmFkQ2IvY0hrOEw2WWpnUTQreUxoSGtQTy9XbGFXbHpnNGRjUjl4SGFjOHliWTNTYVFkeUdqWGpTaFFWRXRiNThpTDB0czU3Qm83ZmtkamJVZDhVWXI2RUIrQVA4bGsyTjY3TkRkOGFjSlphMWNZNldyckdSRld3UGYxYTRTNEFuN3l0QkVWUjNQVUpsLzNYbnZibTBzMW5hc0RFRFV5N1pKb3VSS3Q4K0dYeXFxSU8iLCJtYWMiOiI0MmU5ZmZiYzY3YjVkMjRjM2RmMDEwZDQzNWE1Nzc3M2Q3NmUwOGQ5NWEyMDA2MjQ4YTNjZWE2YzM0YjI0MjIwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFvVEljdEhWajF3cUc2S1pac2tsdXc9PSIsInZhbHVlIjoiUXdiSmtvUWk0SlhJMm5IS08wcWsrSGxZR2hiUDV6cHlvcW04aUxqZ25YS0JHdDBGbks2VWNyREIvQ3BLd0ZZSXpDOFpydHpaZncvcFg0bTdWVGw1eHg0a1RLWGRBbHZmYmMzck56d0NzdGtVZk1wdEtjdkhPVS9QVDA4YUN4NWNTRzNyWTk5V0owTGNQT1kwWm9lb0Y5Q3FwNWlmS3E0eXhReTZ1SlpGa1R5YXkrQk1nSXZUcEJlS3prSWRKUEJrRW9oaXBqZklIMlFQT0MwbmV1YWZrdUVFc3p4UzM5UmdkRExxTEpCV1ludUVqNmNJanFFSVplWk1ESjlEVjJMelVXMGYwYm0wQzNEbzVGTTlQOHpBcUdWU3lQSW91SkwwQ0N4bXRvNFllbTd0RXNVcFpSek9BaDI4NkhwWkQyUmwvdDg3NnhvVFFzQXRxc3V4ZUdpOWlDOGk0V2FITTkwM0FUQzNTZnpYWUx1eEI5RzhTdmordXk0Q0MrQmdaQkYxaUZ3MzA5d1BGUWZKR3FoSGZhMVhPZ2VZbjU5eURlcEl1dklTWlVzWUo1UTNNUytQMG9qdnBHRHRJODJmcDBMdUF4U0tpekJyeDdJMFhRRWs3UjRsUW55UmF0SVk0ZzgvWmZuemxZRTVIdUc4ZTdLdEl1eUlaejY4YXllcEpmaHUiLCJtYWMiOiJlNmY4MjBhZWExNzcxMGZjNjlmZWQzYTU2ZGFjMmQ5ZjkxZjQzYjcxZTE2ZWRkMjEyY2YwOWQzYzRhZWUxNjgwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im0yM1ZpUlh1NXl3M3ZZNDlkR09naXc9PSIsInZhbHVlIjoiNi94NnRjL3JoUm9qTE14a2Flbk45ZFQ2RzF1VVIvdzRkUkpBT0owT3pkNm84V2srZ2Q0ZFZXZWtoNkNHbFNOOHNLcW1HK1c5WjFZZXcxdWU1aEloV1hqM3NSWGVtbFdMUTd1Z2ZBaXhNZnV2YUN2cE5ucnhPdkYzWnV2MHNaS2JKV2NFczFuSzlqRjhtMXhNWGZKc2VRcVJoNlhNNEJvRUV1SEdKYTA4Y0w3ZG02MXBoSkFqL0l1WnJzdGRabHZ5TlhsMFBzaE1kVW5xSlJadGQrTFUzM3ZGQXFhMjFpS2NCYk9NalQwd0d4ZEgzeDJHSWJUNnV4L01KaktmV0VadEFkMlRtTVVxejN6cFJsMUtHN2RVRGdFTUxzZ0xKNlFCa3diK2ttbW1ucThoc1AxUHRUSjhqQ0xGS0VDdXNKOUNtSFdsY01ZRXpKRUdWV0xLbmFkQ2IvY0hrOEw2WWpnUTQreUxoSGtQTy9XbGFXbHpnNGRjUjl4SGFjOHliWTNTYVFkeUdqWGpTaFFWRXRiNThpTDB0czU3Qm83ZmtkamJVZDhVWXI2RUIrQVA4bGsyTjY3TkRkOGFjSlphMWNZNldyckdSRld3UGYxYTRTNEFuN3l0QkVWUjNQVUpsLzNYbnZibTBzMW5hc0RFRFV5N1pKb3VSS3Q4K0dYeXFxSU8iLCJtYWMiOiI0MmU5ZmZiYzY3YjVkMjRjM2RmMDEwZDQzNWE1Nzc3M2Q3NmUwOGQ5NWEyMDA2MjQ4YTNjZWE2YzM0YjI0MjIwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-777235937\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1612505832 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612505832\", {\"maxDepth\":0})</script>\n"}}