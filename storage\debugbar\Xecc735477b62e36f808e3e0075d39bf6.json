{"__meta": {"id": "Xecc735477b62e36f808e3e0075d39bf6", "datetime": "2025-06-27 02:15:37", "utime": **********.560513, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.113704, "end": **********.560528, "duration": 0.4468240737915039, "duration_str": "447ms", "measures": [{"label": "Booting", "start": **********.113704, "relative_start": 0, "end": **********.509734, "relative_end": **********.509734, "duration": 0.3960299491882324, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.509744, "relative_start": 0.3960399627685547, "end": **********.56053, "relative_end": 1.9073486328125e-06, "duration": 0.05078601837158203, "duration_str": "50.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45719960, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0023, "accumulated_duration_str": "2.3ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5358372, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 66.957}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.545153, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 66.957, "width_percent": 15.217}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.550643, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.174, "width_percent": 17.826}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1574220426 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1574220426\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2002826822 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2002826822\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-183136319 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183136319\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-82121811 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990513464%7C10%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im1oQkpxSWJOMGdETWFqS0grM1VOL0E9PSIsInZhbHVlIjoicENVSlBJTU8yUlcwSy9VdW1LYld4VFg0bUFkZ3BhbDd6Tjk2Zk5BL1REczlHY29EU0RsMUp1THpOMVlyV3luUkt4WU53Q1ZUR1NzRFM1QU1qTFlleWY4MkxRYy9FV3l6ZUlVOVNBUXJtT2VJby9Vc3Y5WTVjODRJczV6cnI4dWJqcnN4T1crclRQdis4VHdxN3M2UUtrZ1dla1BKc0kvN2xiRXhydlZuNm5CelRURXRpanJmSC90eVhHaXlhNUhGUEZJTkZxUGNjRTc3TXdXV3MyU0NnSzdXQ1pVWktMcXd6M2Y3MENXRjI2RzZhWExzSUdvcm00cW8wdW4wWTEwSUZlQUNLaGYwaERheVBHdzcwZEliQUpYMk9EejZVaTZid29ZbjBiUDdNU1daL2NyZDNabnFJa01BSGxtbG5PV29oOTVDN3MzRGdOTlFsOWR6QVliQnpSRGVQWHRlZUpJOGdVRVJUY0ZUemhRa2lHNk1qMXZERFJ0V0RFQ1JJSDI4RFdwSzU2Y0JoOHBZbkdCMTB4WnZrdjJKZ093eklRWXZXbU1QbG5XczllWGd2dUIwSHFTdGZWTFYyME5nWnlyd1l1cUcrMzQ3M05zTmJCbnhZS2hUREJ0VlVHYmp3ZU9CalZVK0t6T1JxNzZJNzZEWnZQQU1XMTNGdjh6eHdYa1oiLCJtYWMiOiIyM2I3YjI2ZjRlMDdiZTI5Mzg2YWNhZmU0MzY3NjE1MmRiNjViMTg1YjFkYTdmMThhOGYwMjhlNzIzMTkzNmJiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Imk2cUhTZ0pPWWNHRThNMnVudlpqd2c9PSIsInZhbHVlIjoiNmJiOWxUREdDV3RkaXBNODR2a3I4a3FLdWk4bENkVWdhVHJZVy9LSGFwYTR3SHMrNWsyNGo5M0ZKUlNUcVdxdlRHU0c3bTNRSm5qZ01tUnB4ZmIyS1NScmJURjNoUWJIS1pCZjdiRkpFWTVLZXJRbDUveFJSRWpSdG93ay9vUDM2aU5mWHNFUWNSSWc5cGMzL0JYa2o1QUQ5am5XSkhaaHlqVXVJaTZFcjNiMXRmWFhLT0ZPMThibHFialUzRlJjZEMwaW9OSmR4aGIyR0FqNDB1Y1NLVHBnZ0JXcG03bjN5SmFIejdOTlRJU1B6NHRCY3VFdGVSRkZ2R2VMc2lwb0RHNHBOdlJLQnV1Q2tzMXNuQnIzdmxJNTJwY3JrWEFYZGpCdkppVjhlQWp4WXluTkVWR2dXSlFuamFPMVhBb3RKVEpZSGdMQnJkMml0TE41bXg4VmRpTit4Y05TY0t1eGUwdW8xYVBuQkQvdko4TUJjeHoyV0x6SllDb0o2dVVEQWJWZHVlRFgzc3hWU1I1SEhZUGNGQmVDTHlQQWxoMEJuQVVUYWpwa1BlSW5qejNMMXFsN25lNXRMRnpRR3cramNGaUxHVUxFYjVWWE80VDZaWkF3QnhPN1VqZ1FqOG9SLzV2dXgzQnFoUEpuWk5YN3JyS3g3TlYzRWF1QUU0S0UiLCJtYWMiOiI2NjM5MzU2MjcyM2Y4ODMzZGZlMDIwMzkyNDk4Nzc1Y2RiYTk2YzhmZGYwOGFlNzM3YjdlZTIxM2Y2ZGI5MWUwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-82121811\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-660060571 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660060571\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjFHcExFSjVWSEd6WHhsYVk2M2RPVUE9PSIsInZhbHVlIjoidzQxNWJOVkZLTTJGL1N0c0p6WFJOUWNKWkxaalF1QitBSk01R3VKTXJGamtJVm5JWWJRUUIzR1hJcHpSSkJ6eW53L2ZPREJOaWZIMDlmL0NhdUM4aG52MkN4V3hXWGxCL0trajRqbTR4Q3ZRQlhsb0tCdisra0dpMmg1VXNDQmErVlQ0dDg4NkFYNVNCOVdBN0RQWmhkZEZ4aTBmZ0V4Sy9rUFVzekEyM3FDY0hjMlF5NkRzNlFUVHY0TmpaU1FnUk1GdDlXbnNvS3plRVVlSHczdUtJbUwzM3ZTamFMUGtjVWp3dWVERys0V3JxSm5kTXRxUGJxcFp0TTNNR2hjS0owSndhN05JMlh3Zy91ZzNROW5NWWk1WGVvdmE1K1I3OGJLcGZ0WUxOMVhCdkE2Y0Zkb2pIb2RvRzdPT20yRlhiVEdaQXVFL25WZTZ3VEJHek8xTURTK3lwUm4yK0tzL0VQRzB6eTluSHhiSlFObEVhbDVmZWRaLzdXbi9PK0lSd2VDQTZaUDJBMHZlMmsxWnRjMHI5eWUxaHZydkVpbE5hc3F1VTdRRURCT3FFRXRISGpTRnYvS0t2aWpkUXBSWGRRazNCbDVXZzk0VDBKSjVscm9oUUNzTGU4VjJZalhpcXFkZ1hiV3BUNklVWFZldUlqQldOdndpeWxqMHhERHMiLCJtYWMiOiI1ZjE2YmQzZTg4ZTk5Y2NlYmUyNjI4NjJiYTg5ZjE0MWMyMGU5OGZlYTQzMzdmZjNhMWFmZTIwMDY3MGIxYzVmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:37 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ims4RHdscTllSlEzWTdLZVM5eit6VXc9PSIsInZhbHVlIjoiWmcvbk5CUHRSREhPbFc1RFo4eFpDbE9nM3d2WlB0MmNNdm5acm1TcFF6NWZjSkcvWkpRaTFiMzEzR1o2Q2NYZjZKbHlvK3RHNXBCYjVOdVgrTzV2SERvZ21NVHFMVDh0cWxwU3BoZUlHVFd4WTNMaVh5RFZLYnFWcVlaU1pWRGMwRk83ck1nVHBIMStJYVJaM1FqVHlSMXpJT2JEbGY3SFNXRE9QNVVYdzQ0eHFsN0ZMYXJnL0M2cmxUK0hJWkJydEZuMWJxdzdiSXN5SkdrL1Rxdkk4ZHcyYkRTSENMMkhkdGhiTkpKc1E2SjA4ZjdYYUYyNkE3N1E2amN6NEJWTzFnWnN0ZkV4dUdiVEkxZlJlUmdVTUhKY2h5MDI3dDk2MUZ0Y2g2L0Mwd0RONERNSlJBWE8vMW9lV3JIeGlldXBMNFo4VEQ4ZjFhcmdQci9vMVk4WEIrNFF2VVdVKzl3N2loU2YwS0RwOElLNVdkSGozdmQ2YXFiMVZNR2o0RXM0MVlhcVA5L2dMWSszcktDTksrTll1MDE2amtPMDlKZWNFMUExWkQySnhmQnZZVmNNV1FkSU9yYWNHaEZqZ3IxeXBkTWNUeFNBMndwRnIrdTRyeXRrbW9WQXhjdi9hdVBxZFN5aXQvaHpYRHc2OEhBY3RBTmNZTFk4V20wRWN3UTYiLCJtYWMiOiI5MDk1YmVjNzI2NTkzMDJjMGUwMDA4NTFkOWRiNzA0MTg3N2ExNDcwOWQ4ODEyOGI1ZjVhMTUzMmJhMmJmYTQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:37 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjFHcExFSjVWSEd6WHhsYVk2M2RPVUE9PSIsInZhbHVlIjoidzQxNWJOVkZLTTJGL1N0c0p6WFJOUWNKWkxaalF1QitBSk01R3VKTXJGamtJVm5JWWJRUUIzR1hJcHpSSkJ6eW53L2ZPREJOaWZIMDlmL0NhdUM4aG52MkN4V3hXWGxCL0trajRqbTR4Q3ZRQlhsb0tCdisra0dpMmg1VXNDQmErVlQ0dDg4NkFYNVNCOVdBN0RQWmhkZEZ4aTBmZ0V4Sy9rUFVzekEyM3FDY0hjMlF5NkRzNlFUVHY0TmpaU1FnUk1GdDlXbnNvS3plRVVlSHczdUtJbUwzM3ZTamFMUGtjVWp3dWVERys0V3JxSm5kTXRxUGJxcFp0TTNNR2hjS0owSndhN05JMlh3Zy91ZzNROW5NWWk1WGVvdmE1K1I3OGJLcGZ0WUxOMVhCdkE2Y0Zkb2pIb2RvRzdPT20yRlhiVEdaQXVFL25WZTZ3VEJHek8xTURTK3lwUm4yK0tzL0VQRzB6eTluSHhiSlFObEVhbDVmZWRaLzdXbi9PK0lSd2VDQTZaUDJBMHZlMmsxWnRjMHI5eWUxaHZydkVpbE5hc3F1VTdRRURCT3FFRXRISGpTRnYvS0t2aWpkUXBSWGRRazNCbDVXZzk0VDBKSjVscm9oUUNzTGU4VjJZalhpcXFkZ1hiV3BUNklVWFZldUlqQldOdndpeWxqMHhERHMiLCJtYWMiOiI1ZjE2YmQzZTg4ZTk5Y2NlYmUyNjI4NjJiYTg5ZjE0MWMyMGU5OGZlYTQzMzdmZjNhMWFmZTIwMDY3MGIxYzVmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ims4RHdscTllSlEzWTdLZVM5eit6VXc9PSIsInZhbHVlIjoiWmcvbk5CUHRSREhPbFc1RFo4eFpDbE9nM3d2WlB0MmNNdm5acm1TcFF6NWZjSkcvWkpRaTFiMzEzR1o2Q2NYZjZKbHlvK3RHNXBCYjVOdVgrTzV2SERvZ21NVHFMVDh0cWxwU3BoZUlHVFd4WTNMaVh5RFZLYnFWcVlaU1pWRGMwRk83ck1nVHBIMStJYVJaM1FqVHlSMXpJT2JEbGY3SFNXRE9QNVVYdzQ0eHFsN0ZMYXJnL0M2cmxUK0hJWkJydEZuMWJxdzdiSXN5SkdrL1Rxdkk4ZHcyYkRTSENMMkhkdGhiTkpKc1E2SjA4ZjdYYUYyNkE3N1E2amN6NEJWTzFnWnN0ZkV4dUdiVEkxZlJlUmdVTUhKY2h5MDI3dDk2MUZ0Y2g2L0Mwd0RONERNSlJBWE8vMW9lV3JIeGlldXBMNFo4VEQ4ZjFhcmdQci9vMVk4WEIrNFF2VVdVKzl3N2loU2YwS0RwOElLNVdkSGozdmQ2YXFiMVZNR2o0RXM0MVlhcVA5L2dMWSszcktDTksrTll1MDE2amtPMDlKZWNFMUExWkQySnhmQnZZVmNNV1FkSU9yYWNHaEZqZ3IxeXBkTWNUeFNBMndwRnIrdTRyeXRrbW9WQXhjdi9hdVBxZFN5aXQvaHpYRHc2OEhBY3RBTmNZTFk4V20wRWN3UTYiLCJtYWMiOiI5MDk1YmVjNzI2NTkzMDJjMGUwMDA4NTFkOWRiNzA0MTg3N2ExNDcwOWQ4ODEyOGI1ZjVhMTUzMmJhMmJmYTQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1532019420 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532019420\", {\"maxDepth\":0})</script>\n"}}