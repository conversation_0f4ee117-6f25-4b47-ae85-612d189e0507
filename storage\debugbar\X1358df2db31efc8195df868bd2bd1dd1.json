{"__meta": {"id": "X1358df2db31efc8195df868bd2bd1dd1", "datetime": "2025-06-27 02:25:27", "utime": **********.351129, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991126.950561, "end": **********.351144, "duration": 0.40058302879333496, "duration_str": "401ms", "measures": [{"label": "Booting", "start": 1750991126.950561, "relative_start": 0, "end": **********.30212, "relative_end": **********.30212, "duration": 0.3515589237213135, "duration_str": "352ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.302128, "relative_start": 0.35156702995300293, "end": **********.351146, "relative_end": 1.9073486328125e-06, "duration": 0.049017906188964844, "duration_str": "49.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45737176, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00225, "accumulated_duration_str": "2.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.328218, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.444}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.337594, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.444, "width_percent": 14.667}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.342842, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 87.111, "width_percent": 12.889}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-793231415 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-793231415\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1003351066 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1003351066\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1578881073 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578881073\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-961555369 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991124394%7C22%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkZIQjlkWllkRHp6VlV5U2hMTlA3enc9PSIsInZhbHVlIjoiWE1TblZNY1k0NGxxNEQ3UlZKREFOTlRxWXQvVmdSUG8zRTZSRXVwMXJ2ZGVNRXhteXdIa0JIUE5aU2tWSloyODVqNDMwOW1hMzdYdWJFZFRjQVlDTVhXU3VKbjNUWEtWNDI5RlhaSnVIaDhpZHFKQnVSVjVWMUJLUytuR0VIN3p3UTVTejBkUTlWTzFnVzB4WDUxQ3NzL0hianVST1BmaVI4cEJBYkR1SmpjdFVnTzNSb1NjTG5ybStKVnp5VzZ5YTMrbSswTlA3L3lYalhOVVBOMzFyWUwzc0hDV1d0aEp2NjAwVUQ3d1hwcmNyMmEra21INjBBSkFoUElJUDUxTkV0d2I1Sm5DcnhwRGNZUVBOSXJmWXVoRHJkU2FGRjZHaGNXQ3NCSlA2dFhGZWJ0VGVwMzNMVTlDUW5nZlFBdGs5bGJkNE5DNHZhYXErMm5FcnRXMXdzYlV4UkZSaTZ4VGMrK2R2dHBHdDl2M3gyZ1NnSmFLMnZkUVlZQlJHQkwyMHhyWVVRcUI0NStHVDl0UDJiRkRpUWtpRi90V1dlek1OQmpYcXhFa1pKZEoxd3kzek1LdGc3T2hOWVd1NDRXbXk4bzExaTlEb2l5UUJIZ0tGRnZENFZsT0IwbC9oVGhkNzlCdEF6Z2xOcU9yQlZUWVl2TlZ0TlRSaXI4ODdjVFUiLCJtYWMiOiI0NzQ5NDg1NGE3MGYxOGUwNWY5OWE5ODM0NDMxMjBhMTk5YjYyZmFkNmNmOGRhMTA3ZGI0M2Q3ZDRlYjExNDkxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjRRTXF5NlZNR050YlpEQ2VLS2lDZVE9PSIsInZhbHVlIjoiaytob0JmdGpxTitDNmJoRDh5d0FkV1ltdU1LZ295V0lRTjJkMWZRdXlCQTFoRWFSZFo0MzQra0dtWmZBdENEZ3UxSUc2TkNCbjRnWWE5bXVOUUo0b0ZUalBJbEFwdndIZ1Y5WTU1bE5zU2tCL3ppYUtmUkRoNklySVZTRGp4c3MyVDNWYUZJM3ljMDg1c2EvSVY5dUVVc2lGdUV6SGVReU9nODQ5RC9MRXMvODNaWjhCSCtFWm1Ta1ZQWW9VRnBMVHBBekd2cVkvSDhQV3puMU8wOExFTW9TYlU0ZmswNFo1bzF2SzhHejVWaFI4TUUyb0swVmpudVA1bytzMVNUWTh3K3JFYnZKR3lDMGlSNEs4ZGxyODZpUXFwQ0M3Q0tlaCtRUWZ2bmxVRll0TGlwcHdTQUFRR2RBUENoTzFiSlZDUW1jZW9XVGx3K2RZWkdFOUlydGkzTURpeFlGQjhoTS82eXV1cnhhbTVNYkZ5RGJhVkpQTC9vTXNvc2Z5YjlpalVoY0NLTDZ4VEIzVEpzYy9jWWJINWlqREhGcVgxL00rSmtvUWVNcS9mUVJKOHJqQ2c0Z0U0NkxkZ2g2UnVIU1p5dEdrZ3VMZW5zWDZFVk0vc01pd0VvVDlPcEhwb2JvaDNaSlNsRWs2SWxPQll4UjZWYzhQaWMvZ3pPU2RCNW4iLCJtYWMiOiJlZjFkNTdhZWU2MDI5NmEyOTY1ODQ4MGQ4YzEyMDdlNzdlM2JjOWE2MDc5ZTI1NGFjMGZjMzY4ZGUyODQ1OThjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-961555369\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1226730547 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1226730547\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1881347726 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkdkTVJEemRiODFFZkxUMGZ6TFdlM3c9PSIsInZhbHVlIjoicHViM1YwUHFzeDNGeTZ6MWNwN2xMU2NLejNNSXF1ZEhYSGZuQWhKcEU1NytZWjczelhCYjVuY3d0Mm9YbFVQYSswVmFmTWh1WlM4RHRITVUzOExNa3pnQjlHZXlqUmlyL0FIbEhYOWJMWWN4YjRicWlrZnhKbm81Y0V2NEFPZUxhR2ZncXdqUEI0L0llT2RBOG1Bemw0cHBoblZsTWVoQU5NZFRzSHExQjFXdldvV1A3ckRhQWxKSUVtZXBNclFIaHJOWHVQMTlXb003OEZzZHRnUjBEM3VUN2tFcGlmVGZBRllvblg4cWlGYWpOdHk4TEFRcVJEaUFhTkczUU45dWxsMDB2dWRtbjY2MXRjTmhYQVliL0U2YnpKT2diNDd1dHFKRndQZHZIV3pwWS9IcWROVXJWVHIySWNjbi8yWTM1NG9LU1A0VTh4YW10QkVqaFRtaVJsalZVQWRWVHVPVnBRYk9RQ1dkSlcxcGtLa05SZitVYi9iOXlMTHdvTU9pcmQzckpkckdjVy9QV3QrVzYwMjlNbGFTMnB3MW1FVER1WkJPSzRvUmxIMU5zRjVnWnJaZTZwWFdrbHlrWGRrTnpPeFpaQ3dVVENiWXNKcmUrbnNubHI5QVBzQlFGdUFEUHJiSWFJanJQeW1WdUhMWmpiSnRNOHBsUS8ydnB1REsiLCJtYWMiOiI5NjJiNDBjZTRjNDNmZmNjMDhjOTcwNjY1OWFiMTY2ZTE1YmY4ZDc0YmEzOWM2NmIwN2E1ZmM3M2FlM2NkZjBkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:27 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdLdDh4ZSs2NXR5VEhqak9CV3VaVmc9PSIsInZhbHVlIjoidHRmdFk2eEVaZHhreDRPVTBDOEdIR3p2Q1dLZGdvUDRUY0p5RUNNRXJEdVYyQkorM1l1blpCY2JYSlR1MEkxYVQ3c0VKcFpsYU1aR0thOUNLTDJaamh2b0lrRE1wTkM3WXJPcVZieE1lVFg5UjBuRzFTZnBmanltRGtaYTE5Vm5KYUc2K3dQeW04bFU1c3o1c1pQbXNRcnpxVkpTdDZENk5tQXhWYnA0VkRaZ0VpMlh2UUlSQTVuVUh0SVJDYWFmZjZFNzdxaW9sMGRqN2NmQ1hkY0V1UW50cHpnUmlia2RrMi94ck01RTFKUVBmbkFYYjE5dm5NZ3h1K2NZSXdSRnJUNWV1czB1VmVHaFN2OUdxV0ZSK3RvU0ZXM0FQVmxqbXZQYjJPZmVuWUdwcUMrVzA4L3lmVExzTlVobFRoTElGMnlrWGNsWWtoczlwVDNuMEVNNVZabnRqNk1Bb0JVWlZ5Z01JQnY0ellJMmxXQVVRS09jaFp0cUx0TmhxZE9tblBVeWpjV01uL0FNdHZVMU03d056QlJRS3BTTzVld0ZlSUlqVmRxYWFIL3Vaa3ZsU0VsK1U5TEhFL2ZEdW1WR0FTTktZdEhrMFFma0NleGswejBDenh5YzR6cGJzR2orT0kxdkdmbmZlYmdFejR5aTRaN1E1NXZLRWUvRSt1ZE8iLCJtYWMiOiI3ZGQ4ZDJmZWMxNzgxYTNhOTE3Zjk2Y2YxMzY1NjRjMzQyNzExYWZjNjgzZGJkMDlmOTIxNzg1ZDUwN2IwNzE4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:27 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkdkTVJEemRiODFFZkxUMGZ6TFdlM3c9PSIsInZhbHVlIjoicHViM1YwUHFzeDNGeTZ6MWNwN2xMU2NLejNNSXF1ZEhYSGZuQWhKcEU1NytZWjczelhCYjVuY3d0Mm9YbFVQYSswVmFmTWh1WlM4RHRITVUzOExNa3pnQjlHZXlqUmlyL0FIbEhYOWJMWWN4YjRicWlrZnhKbm81Y0V2NEFPZUxhR2ZncXdqUEI0L0llT2RBOG1Bemw0cHBoblZsTWVoQU5NZFRzSHExQjFXdldvV1A3ckRhQWxKSUVtZXBNclFIaHJOWHVQMTlXb003OEZzZHRnUjBEM3VUN2tFcGlmVGZBRllvblg4cWlGYWpOdHk4TEFRcVJEaUFhTkczUU45dWxsMDB2dWRtbjY2MXRjTmhYQVliL0U2YnpKT2diNDd1dHFKRndQZHZIV3pwWS9IcWROVXJWVHIySWNjbi8yWTM1NG9LU1A0VTh4YW10QkVqaFRtaVJsalZVQWRWVHVPVnBRYk9RQ1dkSlcxcGtLa05SZitVYi9iOXlMTHdvTU9pcmQzckpkckdjVy9QV3QrVzYwMjlNbGFTMnB3MW1FVER1WkJPSzRvUmxIMU5zRjVnWnJaZTZwWFdrbHlrWGRrTnpPeFpaQ3dVVENiWXNKcmUrbnNubHI5QVBzQlFGdUFEUHJiSWFJanJQeW1WdUhMWmpiSnRNOHBsUS8ydnB1REsiLCJtYWMiOiI5NjJiNDBjZTRjNDNmZmNjMDhjOTcwNjY1OWFiMTY2ZTE1YmY4ZDc0YmEzOWM2NmIwN2E1ZmM3M2FlM2NkZjBkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:27 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdLdDh4ZSs2NXR5VEhqak9CV3VaVmc9PSIsInZhbHVlIjoidHRmdFk2eEVaZHhreDRPVTBDOEdIR3p2Q1dLZGdvUDRUY0p5RUNNRXJEdVYyQkorM1l1blpCY2JYSlR1MEkxYVQ3c0VKcFpsYU1aR0thOUNLTDJaamh2b0lrRE1wTkM3WXJPcVZieE1lVFg5UjBuRzFTZnBmanltRGtaYTE5Vm5KYUc2K3dQeW04bFU1c3o1c1pQbXNRcnpxVkpTdDZENk5tQXhWYnA0VkRaZ0VpMlh2UUlSQTVuVUh0SVJDYWFmZjZFNzdxaW9sMGRqN2NmQ1hkY0V1UW50cHpnUmlia2RrMi94ck01RTFKUVBmbkFYYjE5dm5NZ3h1K2NZSXdSRnJUNWV1czB1VmVHaFN2OUdxV0ZSK3RvU0ZXM0FQVmxqbXZQYjJPZmVuWUdwcUMrVzA4L3lmVExzTlVobFRoTElGMnlrWGNsWWtoczlwVDNuMEVNNVZabnRqNk1Bb0JVWlZ5Z01JQnY0ellJMmxXQVVRS09jaFp0cUx0TmhxZE9tblBVeWpjV01uL0FNdHZVMU03d056QlJRS3BTTzVld0ZlSUlqVmRxYWFIL3Vaa3ZsU0VsK1U5TEhFL2ZEdW1WR0FTTktZdEhrMFFma0NleGswejBDenh5YzR6cGJzR2orT0kxdkdmbmZlYmdFejR5aTRaN1E1NXZLRWUvRSt1ZE8iLCJtYWMiOiI3ZGQ4ZDJmZWMxNzgxYTNhOTE3Zjk2Y2YxMzY1NjRjMzQyNzExYWZjNjgzZGJkMDlmOTIxNzg1ZDUwN2IwNzE4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:27 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1881347726\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-913660425 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-913660425\", {\"maxDepth\":0})</script>\n"}}