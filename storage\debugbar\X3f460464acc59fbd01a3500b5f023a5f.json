{"__meta": {"id": "X3f460464acc59fbd01a3500b5f023a5f", "datetime": "2025-06-27 01:05:50", "utime": **********.870916, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.426699, "end": **********.870937, "duration": 0.44423818588256836, "duration_str": "444ms", "measures": [{"label": "Booting", "start": **********.426699, "relative_start": 0, "end": **********.819145, "relative_end": **********.819145, "duration": 0.39244604110717773, "duration_str": "392ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.819154, "relative_start": 0.3924551010131836, "end": **********.870939, "relative_end": 1.9073486328125e-06, "duration": 0.05178499221801758, "duration_str": "51.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45044024, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00248, "accumulated_duration_str": "2.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.844816, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.29}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.855594, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.29, "width_percent": 16.935}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.863084, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 78.226, "width_percent": 21.774}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/ledger-report/274?account=274\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-******** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-703320436 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/ledger-report/274?account=274</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986325258%7C83%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImQyS0lRYjRoazZ0MWtWMWFQNVo0cmc9PSIsInZhbHVlIjoiWXZUaTJRSExrSlFQdk53bCtzZDV1MG45RG5xc29JT09OUW5ydGppNDY2Z2hLU1Z0MlVTK1lUd1NlUENMUjNzbG1xMm5iRm1kaFRwdmFRNGxkZ1BJWm1LRlZaSVdFS2k4MFIyNU1ZWHRoczYvV29mTThaa1QraGMzOUJqcFl3RXdMQVlCbmZWbzNyVFp0dkVZaU9sTUN4aUlySmk5eXN1WEM3T0NBSWxiU1d1YUd0Y1BFRHViZ0JaenArdjVYVHUyaWp4c2pROUduMnZGVkY0ditmTjlWMXhDUndOZWF6LzJkQjd2NnFOVFRPdWpBa3VxTlRuZnFFNVFXWVFpODZNQTd1dzVwWFdaK0lMM21kTHVPWkpqa29RMjhoZW9zYW1HR2o3MkU4TDlKVHpPeWpuWGpPeUR3ODRGTWpwbmRzMjdEYzdvcGRldURGS1MrK3duVmJNRFB5Uk8wS1pBd2ZEZFRDNC9LcDJOcmFxaDhJSVozWmt0aVIrL3dlSmNwUXpkUXlleVdOemk2alhYbHVWblRHSjFYQnFEalUwem05RU1QSXkrMnNLTU1KSGlSRkQ4eXRqSytmeE9LY3RjbEYzU24xa000U1FxcDlKT1RnbGUrNUZIdWRrNFNHZ2Mwb0dsSUpac1ZDSW1yTWd1YjNxK2V5UjI2VmgvbkdQQk5BQVgiLCJtYWMiOiIxMGFiY2RiYTQyNjhiMjNmNGRlY2ZhZmE1OGQ1NWQ2ZGUyYWRlNDhlOTExOWE1MzU1M2YzZjNiNWVkNDE3MmIyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVUZGwrSEozTVNPZHhJS3VLdXd1ZkE9PSIsInZhbHVlIjoiMjNKaTlhUDRjcW1vazVjVEJ0TGZWODlTQ056Ny9OL1JSOUpEZ2xpRThGOWlSUCtydUp2S0RmWlNWbUlqRGRhNWJmeVlDZ3QyTXhRZm16OUlzNW5oT3NpcEhaQkZhLzdXd0g0TU9Cak9TYmYya2NxaDQyR3pUbGNZWW1qUHFWNUc3UStSSWY3akt2bFVtRUJSM3kyVGxLNkVZWDBFbTZRMXUvdFNMYjhDVjU3b2Jnc1JJeU5DVXV5b1RsL0d5V243ZVBIK1UvbHVXQytLUEZoQXRxYTJiL0psUnhscllJSDNNemd0bVZ3QTJIdEROSHY5WHYzb1AzZ3hqaTc5b25TMWxlRjZFN3hzaTduaVFhNUlDMkYxNGNGVlRBdEpzb2ZhRVYrb2FOaU00U1g4dVhxVmVQQUxkOEUvQUw2S2tockhqNGpuV285RUxuUmVEREp0Mi9xb3RQSnVUSlRRK2V2TTM3K2xvQUJTczFhOWppdDNNeG9qNVlTVGFvQXVEVC9Yb2xRaHdqeFlMVWlWSWkzSjdLZStuNGF3aVBzQlZxQjVvc1J4NXh1bWlPSlNLYW5Sa3l6b0ZReGlKRHFyRjlNK1Rja2NrdmJCNGpNWGtKcmo1azNiNHVaRHRCYks0aitkMVlST1RQZG9VUFliRkRUcHFBZGE1Y3VoZUdndSthYjAiLCJtYWMiOiJjYzhiOTdhNWRjY2Q3MjI3YjI2M2I1MGRmZDkxZGFkODMwNzFjMDI0NDVlMzZiMmZlMDNlMzE0OTE3NDcyOGEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-703320436\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2124314643 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:05:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFuUmFQQnRQVkhhVEREaDF0UWw0Znc9PSIsInZhbHVlIjoiNXgxbkZBaWRZL3hiQWdSbVpQYXd1UW1nSjJrTm5panpMenVRdUR0aG5SeXJmb3RwK0VhL1dIWDZXSVlvM2Y4MHQ5NGNuK2l2Z0t5MkhBUWF4SVJoVkdMdzNtN1pZZ1ZWY0RDUGZHaUF2NWxrQXkzRk01amdjeTE2UW1DREZHMThnMEk4WHZCYlZ5L0E2dHJzc2RqN1ZxOUREOGZFc0JHK2dibUtER3IzdTdIVmx0bUhMOVJZNXhnSGo3VWRKTGdxaDkyRHhaaFRUbVMweW40VWhueWd6SktvTXFLWWIrTFJoLy9QYThQTGtlalBWWlZ0THVrZjdZemUvMXBHUWdYekt4Vjh2ZDh4c2dzMFM2WDFqYWc2dHJIL05aU2lFTE5EMExwTUlvVDlxOVIycU1sS2puazhOdXpmcnBKbW9vaUJaR0FzUWpwdjVaMVI3RVJ0VUdQS3czNFg5bkUxdVNaTlFOZTRrWi93Q09ZRDRFSHdEQ0o4MXNybFVDMVVlR2pjMTBiR1UwanFlNFRMSDBUMlI2OUdpYkpTRllEcXg0SUg4bmlxUVhJYkRwQk1DMGw1dGQrcnZEVkpTRHNRQjN2Q3pnUVBrS1ZXUlk3NGx5LzBUb2k0Z3Bra3pmNVlKRGs4NnJHUzVVVlh2NmZhb0xQOXhaRm94YlpPRFZOMWRsS0siLCJtYWMiOiI3Y2JlNTRmMjk0NjEyMGIyMTIzODVlZmRmZmQ1ZjJhOGY2MjczNzM4YTQxOTVhY2NmZWJmYTNkODQ1Yzc0OGUwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:05:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im91K09vYWdJU1VTdjFnc1Q1ZzNRWVE9PSIsInZhbHVlIjoiK2YwYTRYNVBOTkNLK0ljaEh0eXo4NXUydkpQWno5eVVFMFVRK3IwNnZRQmp0b2pQWGNyNFVVcjd6bUJvbCt4bXRKS2U5QnBNMk9HeEdiNkhVYVBmbGFsVGgySnFvdTVmZlZ0L0FaNkplbnlTUFVrVlpZU1NwZGlRRXNidnBIRXM2QkRyV1AzdXI0czYxTmdpOUE1eUN2a0orWXlHWVcwb2hlNHU3TXBKR1RicmZkd0xiNXFqSE0zbFduTmZ6c0ZpSHIwNnJONXE5clhQLyt4QitMRnJHM01SYzc3aEh1LzRwV3NHcjMzbGl0dkc0ZEk3NjdtdFpqUnRhUDVHQytuS1V0RGEzQVBTQ1NxSmtvR3RTU2pJQ2ducUNlTDAvZHAzRVduKzN2elpsRCs0Si9XVXFBalhQci9pNHFVN1NEdXUwKzMyU0hLRTcvVnU1Y2dmTlJ2UlRkaHp1RFF3RGwwVEpybUZKK09ZQi8yK3ZVeTYrWFlINEhiOExjQXhUQ2NEQUMzR0VVUVNBVW1xdlVyQUNCR1JLOHhJeWEvT253ZHpHUEFxUWovOVVpRjNDV1FZc1pvVUduazdXbUFPaTJaV3pWMzhrVFRyRkZhOURUMDFSelFzMllTZGRoa01yWWdvUS80bDFka1lNYTYzQ3RYbktjOFJ4c3NCT1dVTFpJUFciLCJtYWMiOiJmZGQzN2NjZjJlOGJmNGRlOTRjN2ZlODE1OTcyNWE5OGMyN2I0NmQ4ZTQxNTg4YTFjMDk5ODUwM2VlMzdlYjQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:05:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFuUmFQQnRQVkhhVEREaDF0UWw0Znc9PSIsInZhbHVlIjoiNXgxbkZBaWRZL3hiQWdSbVpQYXd1UW1nSjJrTm5panpMenVRdUR0aG5SeXJmb3RwK0VhL1dIWDZXSVlvM2Y4MHQ5NGNuK2l2Z0t5MkhBUWF4SVJoVkdMdzNtN1pZZ1ZWY0RDUGZHaUF2NWxrQXkzRk01amdjeTE2UW1DREZHMThnMEk4WHZCYlZ5L0E2dHJzc2RqN1ZxOUREOGZFc0JHK2dibUtER3IzdTdIVmx0bUhMOVJZNXhnSGo3VWRKTGdxaDkyRHhaaFRUbVMweW40VWhueWd6SktvTXFLWWIrTFJoLy9QYThQTGtlalBWWlZ0THVrZjdZemUvMXBHUWdYekt4Vjh2ZDh4c2dzMFM2WDFqYWc2dHJIL05aU2lFTE5EMExwTUlvVDlxOVIycU1sS2puazhOdXpmcnBKbW9vaUJaR0FzUWpwdjVaMVI3RVJ0VUdQS3czNFg5bkUxdVNaTlFOZTRrWi93Q09ZRDRFSHdEQ0o4MXNybFVDMVVlR2pjMTBiR1UwanFlNFRMSDBUMlI2OUdpYkpTRllEcXg0SUg4bmlxUVhJYkRwQk1DMGw1dGQrcnZEVkpTRHNRQjN2Q3pnUVBrS1ZXUlk3NGx5LzBUb2k0Z3Bra3pmNVlKRGs4NnJHUzVVVlh2NmZhb0xQOXhaRm94YlpPRFZOMWRsS0siLCJtYWMiOiI3Y2JlNTRmMjk0NjEyMGIyMTIzODVlZmRmZmQ1ZjJhOGY2MjczNzM4YTQxOTVhY2NmZWJmYTNkODQ1Yzc0OGUwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:05:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im91K09vYWdJU1VTdjFnc1Q1ZzNRWVE9PSIsInZhbHVlIjoiK2YwYTRYNVBOTkNLK0ljaEh0eXo4NXUydkpQWno5eVVFMFVRK3IwNnZRQmp0b2pQWGNyNFVVcjd6bUJvbCt4bXRKS2U5QnBNMk9HeEdiNkhVYVBmbGFsVGgySnFvdTVmZlZ0L0FaNkplbnlTUFVrVlpZU1NwZGlRRXNidnBIRXM2QkRyV1AzdXI0czYxTmdpOUE1eUN2a0orWXlHWVcwb2hlNHU3TXBKR1RicmZkd0xiNXFqSE0zbFduTmZ6c0ZpSHIwNnJONXE5clhQLyt4QitMRnJHM01SYzc3aEh1LzRwV3NHcjMzbGl0dkc0ZEk3NjdtdFpqUnRhUDVHQytuS1V0RGEzQVBTQ1NxSmtvR3RTU2pJQ2ducUNlTDAvZHAzRVduKzN2elpsRCs0Si9XVXFBalhQci9pNHFVN1NEdXUwKzMyU0hLRTcvVnU1Y2dmTlJ2UlRkaHp1RFF3RGwwVEpybUZKK09ZQi8yK3ZVeTYrWFlINEhiOExjQXhUQ2NEQUMzR0VVUVNBVW1xdlVyQUNCR1JLOHhJeWEvT253ZHpHUEFxUWovOVVpRjNDV1FZc1pvVUduazdXbUFPaTJaV3pWMzhrVFRyRkZhOURUMDFSelFzMllTZGRoa01yWWdvUS80bDFka1lNYTYzQ3RYbktjOFJ4c3NCT1dVTFpJUFciLCJtYWMiOiJmZGQzN2NjZjJlOGJmNGRlOTRjN2ZlODE1OTcyNWE5OGMyN2I0NmQ4ZTQxNTg4YTFjMDk5ODUwM2VlMzdlYjQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:05:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2124314643\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"46 characters\">http://localhost/ledger-report/274?account=274</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}