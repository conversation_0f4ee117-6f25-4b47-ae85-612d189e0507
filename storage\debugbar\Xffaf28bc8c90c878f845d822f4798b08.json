{"__meta": {"id": "Xffaf28bc8c90c878f845d822f4798b08", "datetime": "2025-06-27 01:26:23", "utime": **********.862379, "method": "GET", "uri": "/login", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.478903, "end": **********.862393, "duration": 0.38348984718322754, "duration_str": "383ms", "measures": [{"label": "Booting", "start": **********.478903, "relative_start": 0, "end": **********.827132, "relative_end": **********.827132, "duration": 0.34822893142700195, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.827142, "relative_start": 0.3482389450073242, "end": **********.862395, "relative_end": 2.1457672119140625e-06, "duration": 0.035253047943115234, "duration_str": "35.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43577064, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET login/{lang?}", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\AuthenticatedSessionController@showLoginForm", "namespace": null, "prefix": "", "where": [], "as": "login", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FAuth%2FAuthenticatedSessionController.php&line=344\" onclick=\"\">app/Http/Controllers/Auth/AuthenticatedSessionController.php:344-359</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.0019, "accumulated_duration_str": "1.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "guest", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\RedirectIfAuthenticated.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8529592, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/login", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1386406083 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1386406083\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1342588036 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1342588036\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1138090340 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750987476327%7C94%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik5tcVR0NW5vTHNVRGM1QjNHdTBOQ1E9PSIsInZhbHVlIjoiaHJ4ZnQ5SEVqaXlUNStNVk1uaDJmSmoyY3NnZ1lTMCtadFl0cjByeU5ZVFJRNkxuVGY2R1dVN0c5NnlZQ3J0SDk2N0RIV0JHR08xNXlUaDVxQ2FPeC9td1dhcTVaRkhibjhxMjVsNlVTeHpWNzVibmRtYm9CbGRVT0VvOFFLdjAzbUdtWTdmM1Z2YTJsV0UwZVJZRjJuNm5PYzhDMlNzaGJYQ0V4R0N4M0hPS29DQlpnU21xeTV6NER5WHNFaFlSYXdSNDhaYzk3TzdwdlRxUjdhd1ljOFZmTG1HZWZHcmZoT1BjZDUwTS9zY05jUGQ2Q0dUb1hiWXlUQmYrZ254bmdNQUNyZGNpWG5ZZU1CSGVOckhqVW1tTXhrN0NGZkZmdHdEQnZJd00xQmVOdkhFcDdLOUI2QWlONzVoNW41WHo3M2VrYkpxT2VHSExtL3pEY0sxd3NMeTJtUEVIQ0IyRVhNbytXTm5Jc3Zib2tleDI3T1kxMGl2cEMzcW4yaFVmSnhTUStoRjlFbzljbFZQZDFVcm9tWjdJbE9acHVibVJXb0w0UEdzMXFaRGhNZ0tUL1JOVTB2MjlZcjdBcy8xKzFnT0Y0bmI4NkEwcGowajNJaTAvT3lpR0JmY1N6UGovM09FaURIZlZsM0FwSzY3NStpNHArZDhCb1hiWnBobGciLCJtYWMiOiIyYmNhODdjMzIwZGVjMWZiMTYxYmVlNDU1ZDUxZThjOGQxNmFjMTZjZTA0MWQ3MjQ3MjY5NjFmYzg3NzY2MzkzIiwidGFnIjoiIn0%3D; quickly24erp_session=***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138090340\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-441187859 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-441187859\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-188898067 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:26:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InhpcHBIdlRzYTdQck9Bd3dBMW1HV3c9PSIsInZhbHVlIjoiZUx4aUh1bW1yZlhLYzk1aE4yZFFIM2thUWVzR1RhZWZZY3MvQnFaWDZ1U3diWUEzeDBkbHh3VzM1a2dSMWVJUWdXcGdXcWRxMUxFRjlFU2JHNzh5NU9nNGEyaVNaSFV4cWtoU25QdkZWTFNmK3ZVUDg2MGdBcjA4RjUrTnB0UlZtYUx6bkNkMlF1NkF5a1hVNXN1NUtrVkJhd25ibDFpYjdHd21RL3BNS3d2akZ3Snp2RDJ6MHYyM2pISCs1Qm5WTlk5ZkUya25NakRYT3M0alFUVWkxMGJsWHVQaGpkMmRTRXNxZnZ5K1ZEMEpjVnpYSFFjVWVCME9Kdkg2UTdDSlhHQmNMOWNrSjQ5N0poSGpLVTNLUEJMYTk2M0xOZHdkMFVVN3dRWlVSUERlWkdqVW45OGhwMTgrTmlBeDVnMGdFd2htcGNTVm1iallaV25YZk1NVXpnckplWER6UXBPVWRuUzRaaTQrQW0ybDlacmdLM3FnRFJiajBlRDR1ZWpLTCtyaW04STlNN2tGSXZ0cFYzMUozbFVEY2FHK3R5VUdOS2g4Nlk3SmxWb2JqcW95OTZzR3RlckVKR1E0ZnpHNm55RDhEdjNTWDBDeU9ZbnJMSzJ4Y29VQ1lUNjNyd0hwSzY4WmVxbjVmVnM4dmlwQUVHa1pRUHFPQTRjMmk4ZngiLCJtYWMiOiI4ZTgzMzgwOTEzZmQyYTg4ODQ4NGEzODM4ZTc1NTgwNzI5YzE3MjlmZWM2NDMwM2RlNWMwNDllMzlhYzVjY2ZmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:26:23 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ikg5YXpLNWZDN2pyNXBxU1ZQelBxU1E9PSIsInZhbHVlIjoieVhFaE1UQ1J3cVZpS0Rxc1dYZ1VBRkhmd1BCeEJ1QkVWRjNrcmQ0dmZLTmVpYVlnV29Va0duY3VNWWh2TWExZm1QRmJ5NTBhcXBqdDFuWUtwemQ2K3hsUTBYNjNEK2tlVEl2eHJpWFJSVFZLWElkbTJGQllCRFFQVFlwNmxRVFV0M25HNG5uZmdRdFY0OGdadmpqUkRZWk1QY2ZYR0RTR214OHlyVU81Q1JkQXJXQnVtWjVqU2xGa2F0Zk9VTUtLSi9FWmdlUnF2WFp5TVZCb1psZzVQZjhUalBUQ3JESkxxOEJrZWd1cE1jZmpBdHNySHhxQk83Q3l6OHloNHVsU1VBa1ZiM0hsTVI0cWlQYmE4eWFxQTF1SDBmZ2pVRW9HekZIM2xKRXJiUTVBanIzYXNVSzUydjhPdkk3amI0VDhLQ2ExdWViTlR0cHQ0TmxMMTJmMEM1eHd3TjhGVURWblFWRENjbVg2NWV0eWl1dm43VHkwNFVVQ3R4Y2VzejBETExEUzk2a0FGdVRWSkFUSVZwdDdPdjFyWFhmczRnUGNHSXRpWjVlWUwyRE9EdFFnRTZjT2xGM1dmUW5wQ2loeGxoYVpPOTF5MUFFRDV5Q0JKV0RkOGNydHM3MUpaNHdXV0Z3WldDS29Id3pjdU5RVm90L1hHZmNZNURBSWdkbXMiLCJtYWMiOiI2MzMxYmMyMTU5ZDg2YmRkYjU3NDAxZWRiYWIyM2NjODkyZmY5ODBlOTJmMDQ3Y2U5YjUzNGMzMThmNjY2ODkxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:26:23 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InhpcHBIdlRzYTdQck9Bd3dBMW1HV3c9PSIsInZhbHVlIjoiZUx4aUh1bW1yZlhLYzk1aE4yZFFIM2thUWVzR1RhZWZZY3MvQnFaWDZ1U3diWUEzeDBkbHh3VzM1a2dSMWVJUWdXcGdXcWRxMUxFRjlFU2JHNzh5NU9nNGEyaVNaSFV4cWtoU25QdkZWTFNmK3ZVUDg2MGdBcjA4RjUrTnB0UlZtYUx6bkNkMlF1NkF5a1hVNXN1NUtrVkJhd25ibDFpYjdHd21RL3BNS3d2akZ3Snp2RDJ6MHYyM2pISCs1Qm5WTlk5ZkUya25NakRYT3M0alFUVWkxMGJsWHVQaGpkMmRTRXNxZnZ5K1ZEMEpjVnpYSFFjVWVCME9Kdkg2UTdDSlhHQmNMOWNrSjQ5N0poSGpLVTNLUEJMYTk2M0xOZHdkMFVVN3dRWlVSUERlWkdqVW45OGhwMTgrTmlBeDVnMGdFd2htcGNTVm1iallaV25YZk1NVXpnckplWER6UXBPVWRuUzRaaTQrQW0ybDlacmdLM3FnRFJiajBlRDR1ZWpLTCtyaW04STlNN2tGSXZ0cFYzMUozbFVEY2FHK3R5VUdOS2g4Nlk3SmxWb2JqcW95OTZzR3RlckVKR1E0ZnpHNm55RDhEdjNTWDBDeU9ZbnJMSzJ4Y29VQ1lUNjNyd0hwSzY4WmVxbjVmVnM4dmlwQUVHa1pRUHFPQTRjMmk4ZngiLCJtYWMiOiI4ZTgzMzgwOTEzZmQyYTg4ODQ4NGEzODM4ZTc1NTgwNzI5YzE3MjlmZWM2NDMwM2RlNWMwNDllMzlhYzVjY2ZmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:26:23 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ikg5YXpLNWZDN2pyNXBxU1ZQelBxU1E9PSIsInZhbHVlIjoieVhFaE1UQ1J3cVZpS0Rxc1dYZ1VBRkhmd1BCeEJ1QkVWRjNrcmQ0dmZLTmVpYVlnV29Va0duY3VNWWh2TWExZm1QRmJ5NTBhcXBqdDFuWUtwemQ2K3hsUTBYNjNEK2tlVEl2eHJpWFJSVFZLWElkbTJGQllCRFFQVFlwNmxRVFV0M25HNG5uZmdRdFY0OGdadmpqUkRZWk1QY2ZYR0RTR214OHlyVU81Q1JkQXJXQnVtWjVqU2xGa2F0Zk9VTUtLSi9FWmdlUnF2WFp5TVZCb1psZzVQZjhUalBUQ3JESkxxOEJrZWd1cE1jZmpBdHNySHhxQk83Q3l6OHloNHVsU1VBa1ZiM0hsTVI0cWlQYmE4eWFxQTF1SDBmZ2pVRW9HekZIM2xKRXJiUTVBanIzYXNVSzUydjhPdkk3amI0VDhLQ2ExdWViTlR0cHQ0TmxMMTJmMEM1eHd3TjhGVURWblFWRENjbVg2NWV0eWl1dm43VHkwNFVVQ3R4Y2VzejBETExEUzk2a0FGdVRWSkFUSVZwdDdPdjFyWFhmczRnUGNHSXRpWjVlWUwyRE9EdFFnRTZjT2xGM1dmUW5wQ2loeGxoYVpPOTF5MUFFRDV5Q0JKV0RkOGNydHM3MUpaNHdXV0Z3WldDS29Id3pjdU5RVm90L1hHZmNZNURBSWdkbXMiLCJtYWMiOiI2MzMxYmMyMTU5ZDg2YmRkYjU3NDAxZWRiYWIyM2NjODkyZmY5ODBlOTJmMDQ3Y2U5YjUzNGMzMThmNjY2ODkxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:26:23 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-188898067\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}