{"__meta": {"id": "X3c90441b82362f2adc4f8cc0e060031c", "datetime": "2025-06-27 02:25:31", "utime": **********.144351, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991130.715529, "end": **********.144379, "duration": 0.4288499355316162, "duration_str": "429ms", "measures": [{"label": "Booting", "start": 1750991130.715529, "relative_start": 0, "end": **********.08634, "relative_end": **********.08634, "duration": 0.37081098556518555, "duration_str": "371ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.086347, "relative_start": 0.3708181381225586, "end": **********.144382, "relative_end": 3.0994415283203125e-06, "duration": 0.05803489685058594, "duration_str": "58.03ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722440, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00205, "accumulated_duration_str": "2.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1173599, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 61.951}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.126945, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 61.951, "width_percent": 18.537}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.13276, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.488, "width_percent": 19.512}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-449373525 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-449373525\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1987570364 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1987570364\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-804493382 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-804493382\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2128329929 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991127468%7C23%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkIySUMzNEdab0VRVW9zc3VUQUw2QVE9PSIsInZhbHVlIjoiN0lnVzV0eWF6cURja0xMYkxpbzBGcnoveGtIeWh2MU8wZ3JlODR3dlo3RE1pSEZUWk9XQTR2cTIxejRPVWIvcTlzMzN5R1BGeCs1NG5nUTdHRnFBSTVZQUR1ZnNHRzlVQm43VFIrNnRLOStMaGhpRUtPMVBnRi9VRFdQWWNQVGZ6T3lUbjEvcEJkVDVzOGovQSsrN1h1QWVCTHYrTk1ENkU2K0YzR2JUQ0lqNTQ2cWtIZW9JSG4zVE8rMW0xNGxKTW93UVhkRXB4VGJnb0NwYTh5cUExT2VIS3RUOE0vb1NHT2FnMTU2ZDZneWZ1OW13K24xV1ZNVHV2dUQ2aEdlbkpZZUlBZkhXWndYRSt0SU5PeXdiemlzVkg5b1duSWk5aUNPcEphS1FMTUJidHdyV0FVVnRkL01EZGhVUkw3OHVFdVZaTTdWTVh3YURDNWt2T0k3bFUzMHN1OTRVTGYweFpaRWt2cWhIT2dVRldSeUNhQm1LaUlsU2hoL29CRHczNzJGL2I2MEZucmtRSjlvSS9LYkZWWWp6OExRekJkUUlpVlI5bmZNbFZlcmFZVkRWdElwZWRid3ZKcS9mcVdaMU10UFR5OWcyU1Mvcjcra2JWOTZFK1pZZ3BtUTd3dXFtbkpyTkRhUnZFRFphdkxnN3BSNDNvREpuOE81REFUWmQiLCJtYWMiOiIyMTY4ZjA1MTBkNTgwNTAyOTNhZWJiNzc1Y2I2YTA3NzE4NmZkYmMzYTVlN2JiMGM3ZGUwNDEwMmFmMmRhYTNkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImNiakp0eWxIRUkvMVlDZFRDL3ZRN3c9PSIsInZhbHVlIjoiMXBOZWM5SWlSdnA0TTZIR1A3TkJFYWJYUFY1Z0I3TGdIejdoTjAyK2FVUzRweFRGS2Q2T2xTQXJhQUcxUjZJajlDdWNEb1NXSVJwZi9XSTdxMnFLMGYwQy9vVTZ2a01sR3dBY2tSbnQxVGdSSzZMRW93WG9NUVVrVTdqNnY5TElxbS9xcUxTeUZFOVRFT2NveEJ3blpwVzZjMFpScllKUjRQbnpuVmc5Ny9OaXB0a2JUODVVa2N4amtJMWFxOGlPd3dHWGFEbjVhaG1rQ0Jwa2MrMm5EdXR3SXFZWHJyL2VKSitxOFRXR29VQzF5UFpFbXpLc0xGZVVQeEU4S2RoZkhaWmZyQVU5SGxEa1JOanIxRmpuMVZ4T3pVMFNES1c2RHA3WFFLeFE5WEhUQWI2dlNYb3l6dEhwSFJlNnBualZCV1VkVEN3QlozdnRlVDRrZmNlalpJd0VDMkNQb3NKcDBWbTZyZFpuK3F0c1Jvb2dpZTRyTUN6U1N2M2hWVWVlWW85bFplN2xiWCsrTGhtRmg1RUttbDBKUHlkV2pHZlByZWRuaXJ0SWdNZXJRdlNicEVJd3haMUxlNFVQQmIyVXFEM1ZZNVZvN1Z1bjI3RUp6MmdzVmV3Z3VReXdFVS9BVVBCdUk0K3Y2WWxzQkZJeTl4N1ZPazE1RkwrUHlhdmQiLCJtYWMiOiIyNzE4MzFkM2IyY2MxZGY4YzY1NzNhZTlhNzc4NzgzODk5MmVjZDI1Njc0MzAyNmMwNGZjN2ViMGQ0NTUyMjc4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2128329929\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-500613631 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-500613631\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2076497817 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlJ5QkRIZFpCSVVSd2xyakx6SmVIc0E9PSIsInZhbHVlIjoib01VME1uaGQwcEp5My8vby94ZzNoaitMc1JMc1d5WWFLTis5RFpqWjFmSWxpMCtYaW0rTE9rcHA3NVFJRjdKakhIYWJHMzh4QWJ1NnZoKzczZklaeEJtWjFjMDVvekJDaWxLclZpbXc1WXZxZHhtaDZWcUttRzdseG1xTXU2eFA3Y21OeHg3QXZ0bHN2UC9mbFo4R0IwMHkrSEVuYmtUTXdTZzl2alk0VTNFcno3aHBxZFZHV2ovMlV5MWttekt5S1hqb1E4Q0xMenpLL3F1azFSdXovc1N3TlBJNW1MTjVPOFR6ek4yR01scmMySStQZUxpWmlxcExsUnk3Syt3V0I4SHJSanZkeFZxVnFoWWZHc1EzM2RhZkk0ZUFRNCtoQXRrQzR2bjcvYi9vZlhOOVMwc2JHL2RCckx0bEFSM0NKRVppTEszblRxUGQrSE5KcUVJZmFhK2hJTFQzNlNpdGU3Q3B6NmszQnVUaFBTWlIvUDJrY3FkMlRNZGZULzc1Q2xiU1R6cXYvTkVsdzgzOXIxQ3lsdTBLZXI5ZzdVQkYySDBQTUQ4WWFyOE9Ld1hxZFRQWEhrMVJDMUpRNHhETEFTUHpITndpTE0yd1pnQWRONWtRSWcwNGRDMkgvUnRjbGwyY243Z1B4ZkFyaEROUXp0MW1kdXlGVzRxQkVWa0UiLCJtYWMiOiJjYmQ1OWRjMmE4YTk3NDc3YmUwNDQzNzYyNmFhMzUwZjA0ODkxNGQzZDQ2NjFmN2ZlMTUyNzFhYzQzNzllOTEyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImcwV1lrNEtZVnRhbzdLWmJXNStpamc9PSIsInZhbHVlIjoidHBvcy92V2QyN3FzRTI2NThyalEwUWdKZFJWM004OW5GK3lsK0N6L0QwMWlOVTQrbk9USXFaV2hYMlNvc2NFU04rRFpaS0ZRd2lwOUZZM0hZb05JWGxzTlBDR1FYRmk3THE1RFNhQXJYakVQNGxTK3RidXV4Ym9LVlFXVisyenBWV05vSWxFTUI5MFhpZHdMSWtqSmFsUmloOUxrTXlvMWNqZHExckpWRDFoVmhwaFEraWlzS21rOE0vcjJicUFCNjd2RXVyYlplS1UxYVdXUzA0cVpGOFAzQlFJcGFaMlB5WkhabDVmTjN6MjRqVEhudzFRODQxdHI5TElacDAvZlo3VnZZR1ZJdkJoam9BWndRcTQ0Y0ZtQzVicXdOb0NrcHUxbkwvcjViMW9LUDBvc0VYYW80K2plZ3VXUU9sNUJ3Y1M0TXg1aDVaZHJRL1JCWk0wYWVIdHNJejg3K3NpMnhCRGhMaHVTUFo1TVFtcnozUzl5cGFDVzQ2UUg3UWVqeHZKQXoxdDNzaHpqUFBIZzF6YTRwNUVtV0psQWxoNVdocDArQXVVdWFQVlNRV3ZLeG9WQ0xnbXltUHdqZmgwZUlGK3ZUajRmbGozVTU4dkEzOEc2Z1JIclpwd2oyN1Ewdm9qYm5xeDJqTkVVY0czSVFDQldNWVFSa1ZXN3doMXIiLCJtYWMiOiI2ZTAzYTFmZTNkNzcwZGQ2Y2E4OTYxYmU3MmJjNjVlNGU4ZjliNzk3OTIyZDM5ZTE5MWM5OWM3MTU5YmYxODFhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlJ5QkRIZFpCSVVSd2xyakx6SmVIc0E9PSIsInZhbHVlIjoib01VME1uaGQwcEp5My8vby94ZzNoaitMc1JMc1d5WWFLTis5RFpqWjFmSWxpMCtYaW0rTE9rcHA3NVFJRjdKakhIYWJHMzh4QWJ1NnZoKzczZklaeEJtWjFjMDVvekJDaWxLclZpbXc1WXZxZHhtaDZWcUttRzdseG1xTXU2eFA3Y21OeHg3QXZ0bHN2UC9mbFo4R0IwMHkrSEVuYmtUTXdTZzl2alk0VTNFcno3aHBxZFZHV2ovMlV5MWttekt5S1hqb1E4Q0xMenpLL3F1azFSdXovc1N3TlBJNW1MTjVPOFR6ek4yR01scmMySStQZUxpWmlxcExsUnk3Syt3V0I4SHJSanZkeFZxVnFoWWZHc1EzM2RhZkk0ZUFRNCtoQXRrQzR2bjcvYi9vZlhOOVMwc2JHL2RCckx0bEFSM0NKRVppTEszblRxUGQrSE5KcUVJZmFhK2hJTFQzNlNpdGU3Q3B6NmszQnVUaFBTWlIvUDJrY3FkMlRNZGZULzc1Q2xiU1R6cXYvTkVsdzgzOXIxQ3lsdTBLZXI5ZzdVQkYySDBQTUQ4WWFyOE9Ld1hxZFRQWEhrMVJDMUpRNHhETEFTUHpITndpTE0yd1pnQWRONWtRSWcwNGRDMkgvUnRjbGwyY243Z1B4ZkFyaEROUXp0MW1kdXlGVzRxQkVWa0UiLCJtYWMiOiJjYmQ1OWRjMmE4YTk3NDc3YmUwNDQzNzYyNmFhMzUwZjA0ODkxNGQzZDQ2NjFmN2ZlMTUyNzFhYzQzNzllOTEyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImcwV1lrNEtZVnRhbzdLWmJXNStpamc9PSIsInZhbHVlIjoidHBvcy92V2QyN3FzRTI2NThyalEwUWdKZFJWM004OW5GK3lsK0N6L0QwMWlOVTQrbk9USXFaV2hYMlNvc2NFU04rRFpaS0ZRd2lwOUZZM0hZb05JWGxzTlBDR1FYRmk3THE1RFNhQXJYakVQNGxTK3RidXV4Ym9LVlFXVisyenBWV05vSWxFTUI5MFhpZHdMSWtqSmFsUmloOUxrTXlvMWNqZHExckpWRDFoVmhwaFEraWlzS21rOE0vcjJicUFCNjd2RXVyYlplS1UxYVdXUzA0cVpGOFAzQlFJcGFaMlB5WkhabDVmTjN6MjRqVEhudzFRODQxdHI5TElacDAvZlo3VnZZR1ZJdkJoam9BWndRcTQ0Y0ZtQzVicXdOb0NrcHUxbkwvcjViMW9LUDBvc0VYYW80K2plZ3VXUU9sNUJ3Y1M0TXg1aDVaZHJRL1JCWk0wYWVIdHNJejg3K3NpMnhCRGhMaHVTUFo1TVFtcnozUzl5cGFDVzQ2UUg3UWVqeHZKQXoxdDNzaHpqUFBIZzF6YTRwNUVtV0psQWxoNVdocDArQXVVdWFQVlNRV3ZLeG9WQ0xnbXltUHdqZmgwZUlGK3ZUajRmbGozVTU4dkEzOEc2Z1JIclpwd2oyN1Ewdm9qYm5xeDJqTkVVY0czSVFDQldNWVFSa1ZXN3doMXIiLCJtYWMiOiI2ZTAzYTFmZTNkNzcwZGQ2Y2E4OTYxYmU3MmJjNjVlNGU4ZjliNzk3OTIyZDM5ZTE5MWM5OWM3MTU5YmYxODFhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2076497817\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1914394744 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1914394744\", {\"maxDepth\":0})</script>\n"}}