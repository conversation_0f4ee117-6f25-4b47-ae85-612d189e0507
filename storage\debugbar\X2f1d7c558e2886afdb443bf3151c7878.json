{"__meta": {"id": "X2f1d7c558e2886afdb443bf3151c7878", "datetime": "2025-06-27 00:43:26", "utime": **********.956102, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.461941, "end": **********.956116, "duration": 0.4941749572753906, "duration_str": "494ms", "measures": [{"label": "Booting", "start": **********.461941, "relative_start": 0, "end": **********.878402, "relative_end": **********.878402, "duration": 0.4164609909057617, "duration_str": "416ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.878411, "relative_start": 0.4164700508117676, "end": **********.956118, "relative_end": 2.1457672119140625e-06, "duration": 0.07770705223083496, "duration_str": "77.71ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45027680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02776, "accumulated_duration_str": "27.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.909001, "duration": 0.027, "duration_str": "27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 97.262}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.944087, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 97.262, "width_percent": 1.441}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9495199, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.703, "width_percent": 1.297}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6ImxBeXpYYS8wM0hxRlh4cXk3bzIrOVE9PSIsInZhbHVlIjoic2twNWRaa1lXWkFBeWJ5SklYMXV1Zz09IiwibWFjIjoiMTYyOGE5MjcyNzY0YjU4MTU2MTcxNjEzYTBjMGViYjQ1MzUxNDVjY2JmNWFlMzE1OTM3OGRmMTE1ZDJmZDg2YSIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-882841005 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-882841005\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1997586821 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1997586821\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-747107033 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747107033\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1143352260 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6ImxBeXpYYS8wM0hxRlh4cXk3bzIrOVE9PSIsInZhbHVlIjoic2twNWRaa1lXWkFBeWJ5SklYMXV1Zz09IiwibWFjIjoiMTYyOGE5MjcyNzY0YjU4MTU2MTcxNjEzYTBjMGViYjQ1MzUxNDVjY2JmNWFlMzE1OTM3OGRmMTE1ZDJmZDg2YSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750984993197%7C65%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJlaGVWb2RhRHRac1NNQmZXWnl6UHc9PSIsInZhbHVlIjoiUitmaUxFTGlHNVdiYnRnVENXanBhYllGRUkzY3NPci9KTmhUeUczdnNBL044S0pLM3ZkOEFyZnVLN3RFdXlxUmdOdkFhMEhHZnZmQVlheUN3MU1uc1lIT2YxNWFrK0E5MklIdVdrU25rcHc0YWliUU1IZXFVM3Y4VkN0T3IyWHBQaXpROEd6ZUxPMHE5Nm56SVgvQ3lPcHA3cVpibXpaRXRkVWEwS3FkL1FDdjB2SmZBekhqR1pyNDNidC8xejc1TlMzcy81YnJGZVVUSDd4OVRMQ1VJN2lOVzRuZEtxUk0rbXA0UlMvMmxZVVdmbDh6NlM4cW9EbHczeS92Yzk0TUFZLzVMSU9ZczRsNHNrNFIrYmVEUmtiamtjV0lRNlR6dGZZcm9XQmpPWDFXOVJvakptVS9XQmtZaEdLb0U0QmVjTldpMjQ4VFJWcnBRb1JmblppbmtEMEdNTUN6UXA2dW1YYmhaNFUrMi9NMmVuTlptWUROMVcvRnh1c1ozbmEvZVh4ZmlUK29ZS05namppUzI3S3BDejBSZXBGMG1NTUtHZlA5MUcyUFcyOVkrQjZDSmN1UHY1YlBmNCtUMkxaakxaazdIdk9Bb1JubGlKRHpocUo5UWJZUnJFZVFDTjlHZU9jU0pZaTN0ZFFPaFNzNGhxdk1FaU9SS3FBakVEM04iLCJtYWMiOiIxOTY5ZTFkYzc1ZjIxZTZkZTBlYWMzOTU2NTc3YzExZGY1MjEwN2I4NTg5OTk4ZGE3ZTI4M2ZkMjk4MThhNzQ5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ii9CY3d0UkJkQjBPUjVtbWJuYTFYd3c9PSIsInZhbHVlIjoiSU81TnUxRFlYcDltTkEybkp6bnNXaXhicG00V0RQWU9oME80S0U5SjlYOWdDczdEemhwdGJGaUtHKzh6Q3BoMnR5dEdTcC9IQ1IyWTNzeWlwL2syNUtHZi9GVGgyaWNzNFJ0ZjF2YXZEVVRzSmprY3hpNHUzVkZhN3c4QVFMVURiUlhtUllTd1U3cDRyajdvcDBUaDBaRkpSSGtOajNEUTRNTXRqbE5hejlRUysyYUMvall1MkM0eFdKdm5CbnQwV3N6M0RxaVd5SmRnVkdPNnVGS0RnMDJKY2wwSGl4V0VIdzI4em9ZYXZuYTRycy9LaW5YcmZTWVd4dXNYWFF1OEhhTWtxU2FIUERXbmJESEhPeVM4cFFwejU4OC9qdnFzeVlXTXZJTHhzMUxZaFd0eUE4VUhFOWhHZWxhcGJXSFMycElqekdwRWNvNDNPZHR4WjhZb0JOckRLTHlRQjVsY3lISHRDZEpxTmFLYjJBVVFuOTFYbXRzYUFFczBOMlhPNjlib1FOeEZ4eUFpckJlSWUrTzIrd3NoM2pkN00xN0JacEhMV2dEand5ZmZwNVZjUmQwNG9wV0lRS2hFU2ttYWk0Mm5UbFlyLzFpaGxSb3NWRjlhbGc4eFpTU3VsR3JSeDNtcEJpd2w4cnJBUDZic1N0RjZXL2c0dnBsQ1NTSTUiLCJtYWMiOiIxNGMwMzFmZTVhZjJjNzM1MzliMjc2ZDU2ZDJhNWU2NzNlNTdjNjIyMzM3ZDg3YmRjZWIzOTljZTYzZTBiNWI4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143352260\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1998545814 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1998545814\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1003865559 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:43:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlZBSXlQWlVCUFhhNTNIem1TcTcvaGc9PSIsInZhbHVlIjoiK3BidjJ5LzlGcURUWEt2UFo1elZmOG51VUJWZi96eGFndjBJN0kwZ21BRTVaNGlSbm50UGNpS1l2Q2h3NnRWQVJIYVV2UEx5V2V5YmFiSmthY2l0ZHdYc0x6eUhUK3hhN3NPb3lvbXJnTzhDcEt1b0ZwRUwrQ1duVk5pUkpzOERMMWdISXZyZGtoZDhLelVLcHRsci9LSHV5TU1yT1U4UXhwYzhBOWd0eWQ2bnd5RzlPM1NXU3hEVnpHeEFZLzZVc20wOGNBS01GQ0ZxWFZWRytVNndIVDVFSnVCVjNIelNsK0J4ckpYTXZrVEhvR0Vick1meFYvTnoxUFdnZUtGVlJ0dllWc2lPekdIN2Q0UzFCTG80bEFFRUFNTnl5L3dlcDUvdFNvazdHQ2RYcWxrMkZValYwWXJJeWQ1SmlqbEJzUDhuR0REeE1GQktNWmlxWURLK21RTno5Rkc4WjhIZkRZbmFMaDZCTEpLMWNsa3NjVzJ6bjR6dkc3U3J1SEpqaHljeGI5RkpkTUxhUVg2Vm41V05kalNNSk1KeXkrN0U0SnI4dExkcTR5UWJnblBnV2creUFlanRQcUlkWUNJeE4wVi9wMC9CMXVvbEg4Vy93V1ZTMEc5VkVwc2FsK2NIbm1ZajNzYnY2Y0xaUi8zZll2d2VPSDJ4dGp5QVE5V3kiLCJtYWMiOiI0ZmU0OTk5OTZkMmI5ZmNmYTc5ZGVkZDM5N2E2ZDQ5MDNkMGZiNTUxYzQ3MzBlYTdhOTg2OTBlNzFlMjE1NmQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:43:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImE5TmF5V0Z5ZlpiVno2RElUR1BEOUE9PSIsInZhbHVlIjoiYks1cTRFWVcxbXp3MGtFVkRUVERyWXBYUEswR1h2dmYrYjhEVGE3eUw0NWR5NWNpYk9iUXB3eHc1aFcyM2ROSDdvWXhaRlRkWnl5ZnFDUjdKUEZ0eFlqeGZMVnNrUzhNOFhLaFJJc0xjbGhyZ0NkeTRDWUZYVXJ4ZGFzbm5IalBvMEptN0hmQ1Y3d3Q1VFpiMHdCbEkyYlc1cnNYemkrcVFNcnhjQzBDbGJpcjlSdXNLd0ZHL2ljcUNJM0ZML3AwT3VIbExINmJqS21MYnh2ZVZZWlo4ekZWL2RqTTlNS3c1bzN5eXMyWWRpZDdUaktYY0lMa0wrenR4ZUhCYjZueWREblo2V2VFSi9KdWFrRXBjWWxGUS8zNWJHbmFNbGozOFY0MldUMG0rVGJWRmNGME5KU21aK1loYTFBR2tTai9qdkl3a2dCKzI4M0VXNk9ZNm9XbkVtT2I5ZjhCdnAxT0dHeFpQeHduajQxOU1kWjBGaFBRdWI3RzJIQnc5eE5XVFhyVnB1aGtub0hVV0Q5WnJWRnR1VjhwMzROWlNqczJVTythdTV4ZTlZY1VWTUFkcU1RZXpabFVLUFRUekg0ZkFndXFGWTZiYUdxN2pFZnpROEdoZHF2azVOVTNtbTRIS00xc0RUanJMR0dPNU0vM0RhWTlTYldqcWlDSENPRWUiLCJtYWMiOiI0MDhhNzJkYWQ1ODdlNDYwMWQ2NDYwMmQ3NmE5ZDY2NmYyYTU2N2E0Mzc3ZjYxNzY1ZDM0Yjc4YmE4ZmY3NDk1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:43:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlZBSXlQWlVCUFhhNTNIem1TcTcvaGc9PSIsInZhbHVlIjoiK3BidjJ5LzlGcURUWEt2UFo1elZmOG51VUJWZi96eGFndjBJN0kwZ21BRTVaNGlSbm50UGNpS1l2Q2h3NnRWQVJIYVV2UEx5V2V5YmFiSmthY2l0ZHdYc0x6eUhUK3hhN3NPb3lvbXJnTzhDcEt1b0ZwRUwrQ1duVk5pUkpzOERMMWdISXZyZGtoZDhLelVLcHRsci9LSHV5TU1yT1U4UXhwYzhBOWd0eWQ2bnd5RzlPM1NXU3hEVnpHeEFZLzZVc20wOGNBS01GQ0ZxWFZWRytVNndIVDVFSnVCVjNIelNsK0J4ckpYTXZrVEhvR0Vick1meFYvTnoxUFdnZUtGVlJ0dllWc2lPekdIN2Q0UzFCTG80bEFFRUFNTnl5L3dlcDUvdFNvazdHQ2RYcWxrMkZValYwWXJJeWQ1SmlqbEJzUDhuR0REeE1GQktNWmlxWURLK21RTno5Rkc4WjhIZkRZbmFMaDZCTEpLMWNsa3NjVzJ6bjR6dkc3U3J1SEpqaHljeGI5RkpkTUxhUVg2Vm41V05kalNNSk1KeXkrN0U0SnI4dExkcTR5UWJnblBnV2creUFlanRQcUlkWUNJeE4wVi9wMC9CMXVvbEg4Vy93V1ZTMEc5VkVwc2FsK2NIbm1ZajNzYnY2Y0xaUi8zZll2d2VPSDJ4dGp5QVE5V3kiLCJtYWMiOiI0ZmU0OTk5OTZkMmI5ZmNmYTc5ZGVkZDM5N2E2ZDQ5MDNkMGZiNTUxYzQ3MzBlYTdhOTg2OTBlNzFlMjE1NmQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:43:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImE5TmF5V0Z5ZlpiVno2RElUR1BEOUE9PSIsInZhbHVlIjoiYks1cTRFWVcxbXp3MGtFVkRUVERyWXBYUEswR1h2dmYrYjhEVGE3eUw0NWR5NWNpYk9iUXB3eHc1aFcyM2ROSDdvWXhaRlRkWnl5ZnFDUjdKUEZ0eFlqeGZMVnNrUzhNOFhLaFJJc0xjbGhyZ0NkeTRDWUZYVXJ4ZGFzbm5IalBvMEptN0hmQ1Y3d3Q1VFpiMHdCbEkyYlc1cnNYemkrcVFNcnhjQzBDbGJpcjlSdXNLd0ZHL2ljcUNJM0ZML3AwT3VIbExINmJqS21MYnh2ZVZZWlo4ekZWL2RqTTlNS3c1bzN5eXMyWWRpZDdUaktYY0lMa0wrenR4ZUhCYjZueWREblo2V2VFSi9KdWFrRXBjWWxGUS8zNWJHbmFNbGozOFY0MldUMG0rVGJWRmNGME5KU21aK1loYTFBR2tTai9qdkl3a2dCKzI4M0VXNk9ZNm9XbkVtT2I5ZjhCdnAxT0dHeFpQeHduajQxOU1kWjBGaFBRdWI3RzJIQnc5eE5XVFhyVnB1aGtub0hVV0Q5WnJWRnR1VjhwMzROWlNqczJVTythdTV4ZTlZY1VWTUFkcU1RZXpabFVLUFRUekg0ZkFndXFGWTZiYUdxN2pFZnpROEdoZHF2azVOVTNtbTRIS00xc0RUanJMR0dPNU0vM0RhWTlTYldqcWlDSENPRWUiLCJtYWMiOiI0MDhhNzJkYWQ1ODdlNDYwMWQ2NDYwMmQ3NmE5ZDY2NmYyYTU2N2E0Mzc3ZjYxNzY1ZDM0Yjc4YmE4ZmY3NDk1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:43:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003865559\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1304166749 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6ImxBeXpYYS8wM0hxRlh4cXk3bzIrOVE9PSIsInZhbHVlIjoic2twNWRaa1lXWkFBeWJ5SklYMXV1Zz09IiwibWFjIjoiMTYyOGE5MjcyNzY0YjU4MTU2MTcxNjEzYTBjMGViYjQ1MzUxNDVjY2JmNWFlMzE1OTM3OGRmMTE1ZDJmZDg2YSIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1304166749\", {\"maxDepth\":0})</script>\n"}}