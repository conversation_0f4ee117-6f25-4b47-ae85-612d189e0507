{"__meta": {"id": "Xbd9e5fe6ae8db297af491202e4b0ffba", "datetime": "2025-06-27 02:27:24", "utime": **********.720001, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.291117, "end": **********.720018, "duration": 0.42890095710754395, "duration_str": "429ms", "measures": [{"label": "Booting", "start": **********.291117, "relative_start": 0, "end": **********.65277, "relative_end": **********.65277, "duration": 0.36165308952331543, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.652779, "relative_start": 0.3616621494293213, "end": **********.72002, "relative_end": 2.1457672119140625e-06, "duration": 0.06724095344543457, "duration_str": "67.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722416, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01263, "accumulated_duration_str": "12.63ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.684911, "duration": 0.01183, "duration_str": "11.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.666}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.7049289, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.666, "width_percent": 3.088}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.710486, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.754, "width_percent": 3.246}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-64207374 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-64207374\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-129176329 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-129176329\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1300782757 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300782757\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1819938957 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991242209%7C34%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxIZHNYNDZxaVN1VnNTcE9HQ2YrVHc9PSIsInZhbHVlIjoiZERLL1p4TTdMQUZ5ZDVDbWlYZWZaaEtDS0ZXUStFR0dJS1VXMVBrRXpTMlB2ZGxRRTZNSHNKQkdYS2w4VWJCaXhzV3VhZmJTdUZOMTc5VnJjWUN3WVFwK08rSVB5dEttRHhpUGNhMWVBaDRBbi9qd2VPR2hDeXZQR0RrRCtjVXB0Y1JHS0Q1eFNoWTJBRWg5WGRuWGxYYklzMUl5anhhZVJiNTlheG9La01tZ1Y4azJseE96Qm0yTVRYRnlZck44VGQ3YndhQThyZkJDaUJJSXY3UUlZQVphYjBtMVYrNlNNa2xwMTdYeUFkMVhFd1VtQ0QvdWRORmZOa2l3MzFKOGZjRlA4WmNRa2g3N1E4VTA3bis3c05DUEF2STM1V01XUXNkV1lrR0J3bGh0MGlGMTVwZHBhNmJ0REVqRkVMdllFckRkS0lJZGJUdldtbUZFTCtGR0JoWXNzRUQ4bHpkbUhwL2YwdmNDcXNKRlk1T0d6OWNvbTNsa2U2OVZDbmZCd3d1Q09OQllvazE0MUQ5aHNlMGQ3UHpOS3dnWnl5NjhNeVcxeldhdnN3Y0c3WEJRNG56aE9xOHJ4TmZVTDRYeGJRdURFalZsNDduNFNDV1JyNzFOUWh4bDJXRU9mWEwvTksrUHowK2xYVDVIcDhaQ095Q3hhN1JIQlpqeU54L00iLCJtYWMiOiI4ZmIyZjMzYzI5NTczY2JkNmFhOTZjYzkzMjA1YzllOTQ4Yzg1MjEyNjliMzk5ODJlNTI4NDU5NzBiYWM5MjdkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImVHWXZrM0NUS1VBYXFGcUM4VHp3b0E9PSIsInZhbHVlIjoiSEFrcldzZTN1NmkwT3lzMm14QWJWRFB4MVhjZjVxNmlQM0gzdnJhK2dTb01Bc0xKTjFXS2xDTG16NlZYcWNXVGVkcUZIZnd5Z2pQYXVLRkZpWENxMGtZMWdta0ZsWTd0OVVkVmYvNEVJQ3AzME10dnIzMlk4UWdiOVlvVmZGOHU5MmJCVVZtMURpMTdBRjhxYmVsc2g5dnJOZVZvYXNBOTRWbmRhNU1FU3k2dGpZMTRPK3Z1ZkhqblBkaUl2ZXlFRnJXdGp5U2xyTFpOeWdmYVRpWmlXcmpoUjl1TzdtVGVqbGJMMnUxWG9aOHo1aEtSZVgvdXBQKzh6SzRDbmhlbEJwUjhoTjdDVGhDWE8raURQanRqMGpOY2hwbnY5R1k4WDNwSEhmQ1VvOXhvOEdJdUpPQ0Z3MU1BYk5VUUpwMzJVdi9INjVLMWM0ZW9HaDJCeXJtVTBCVUorNHB0SnpDbnU2SFdaaWQ0VDVGUWNKS3BHdkVieTI1cGNEcm1pT0FJVXM5aEJZTEt2MVMyaXBxTjVXSTgvTXhtL0x3UU4za1VQdXJBWlRmNmlMOHR2L3I3dCt6QjRoNzQvdjdmVWtCUWhsWnNBVFRPRDJZS1dGbmsvK3BqYnZ6Sjd2WXFMN25yRTZwSVhQemVUSUZ6RzB6OWdDU214Uk00RTAwQWljeFIiLCJtYWMiOiJlZDk2YTQ1ZjVhMWUzNTY2ZGFmMDRlNDFkN2JjNmRhNzVkY2Y2MzBmNDI2ZDFhYjE0MDg3YzM0MzY4N2RjMWEwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819938957\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-874875053 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-874875053\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:24 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InBTOWFBREFuNzZwY0VwNVR1UVErOXc9PSIsInZhbHVlIjoiUTE1b0IxNjFWS2xwR1R2bXorRlUwblJzSzU1ZzBwWm53bzRqbXc0OTY5SFdHL09vNFVPV0dKRVZ3cnM4QlJTa3UzN3g2bHYrNmI0aEhzZENQV3VkejZweW1aU0hEM21WNC96QUUrNFZOb1R6TGJxbFRzQWx2Ulk1N1MwMWVrSWJzS3RDSkozNTdQam1VeTY2WElnTGQ1VS8vNHRGNXJhNVlFTU9Nb2ErdmR1VjdyMU1EMm5Qdk1QWVF0cTVObVpqdnE5dzZ0Q3hsQm5hZi9mcDljQTJIaGRFTmI5ak9Ib2YxaHFGOEpmV1RnTWpqMDg4ZE9BTmZFTDU4Zi9Ta2V6TDdMSDVrOWxEeWxNTHRJSkFyVkFSQ3RpdnJTU0c2bFkxUmllUkhFNTRjZEoxR3pwOTd1ZDlaVDgzS3ZuemhDSFpjYWd5ZUFxWlhlRmFlbFEvR2VLK25FS2ZBSkMyekgwaVQ1SVRSZ05xKzdGakc3b2N5aVlIcmZRenlLUFhwUStaeTBocEFSTHgwaitnWkxMbkdSNk5CbDU3VHVybkk5anR4RWQzamlFOXJKVDM0OVZvSEJQa0lEWnA0VXBzZE8yTEZLTTM2QXpRdGxoWjRLcWJpRE55OGY5eDkyRFltUjdENVdIUHhTNVFZZnYzT3lMbVNGUEhPZW5kamRzcERNUkEiLCJtYWMiOiJjNDUzNjI4YWRlZjUyNjY0NTk4YWViNGQxOTcwYTBjN2FhNmViMTE0ZWUwYjM0YzY2YTJiZjIzMTU1NzdlMmI0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:24 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlNiaHJyZEd1cU9nUnEwVkpyMTUxdlE9PSIsInZhbHVlIjoiREdMU1EwN3JJQkFnZENRS0tyMkNTQzZNZENnRzZNOG9QQjVYTXphZHF4dk5FRFo2ZTlDc0RNektiZEtkeWVHNXhsWEhhUm1xdjdiYmNnalNULzc3TWJKbFh6SnpWaVltTDAzVkhIZ0F6bkhISkdFMW12Nm9XcVgzSU90djd0cFFFSnEzMGtnSTA4YWdYTEJUS2xoWkwwblJyR05pOEdEcHovRzg1eFQ2ZFRBYStRRmFaK2pUZUhEdTBqQ25tYjFSM1NKZ2tDRzNZd0pYNXNudGkxL2tPVWVCdnZacnV2RnRHWTV3bEhpTCt4bUtDK29JN2hGbFZrY1BOQkhQSmlOOGwrTVpGbDJyaXRaS1paVkJpc0ZzekZ3WEIvYzhIZ0hxTHhDWm53SzlmUFQ1dEhDRkVTeGYxZklSZmQ2SUNXUXBOT3hCKzFJeVV6WjM3VFJLL3JPMmhaR0N4Ym5xaG9SWGN0Wks4Wm1OM0tqODN1bTN3bnYxZEU4SEN6Mi90M1Q5ZjcwOTBlTjR5dlh2VmQrcGxvLzVhQVQ4L2FVUkNmZ05jbTdDdEJUaWhOK3ptZzJXMmxENkNPSlNVMDZoZ0lGNUI1U1lnTlBlV3A0ZFdNZ2hycjZobFp1UklBMUFRK0V6SFJLQ3BocDdyVVJGNkl0dlVKMlRtWmNHbGh3UGFGeDUiLCJtYWMiOiIzZDQ1N2Y1M2JkNWMzNWE4ZmJkZDY5NzNiOTI1MDM1ODZiZGI3NDUyYjliMGRmOGMyOGI1MjY3OWM3YjcwMjQwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:24 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InBTOWFBREFuNzZwY0VwNVR1UVErOXc9PSIsInZhbHVlIjoiUTE1b0IxNjFWS2xwR1R2bXorRlUwblJzSzU1ZzBwWm53bzRqbXc0OTY5SFdHL09vNFVPV0dKRVZ3cnM4QlJTa3UzN3g2bHYrNmI0aEhzZENQV3VkejZweW1aU0hEM21WNC96QUUrNFZOb1R6TGJxbFRzQWx2Ulk1N1MwMWVrSWJzS3RDSkozNTdQam1VeTY2WElnTGQ1VS8vNHRGNXJhNVlFTU9Nb2ErdmR1VjdyMU1EMm5Qdk1QWVF0cTVObVpqdnE5dzZ0Q3hsQm5hZi9mcDljQTJIaGRFTmI5ak9Ib2YxaHFGOEpmV1RnTWpqMDg4ZE9BTmZFTDU4Zi9Ta2V6TDdMSDVrOWxEeWxNTHRJSkFyVkFSQ3RpdnJTU0c2bFkxUmllUkhFNTRjZEoxR3pwOTd1ZDlaVDgzS3ZuemhDSFpjYWd5ZUFxWlhlRmFlbFEvR2VLK25FS2ZBSkMyekgwaVQ1SVRSZ05xKzdGakc3b2N5aVlIcmZRenlLUFhwUStaeTBocEFSTHgwaitnWkxMbkdSNk5CbDU3VHVybkk5anR4RWQzamlFOXJKVDM0OVZvSEJQa0lEWnA0VXBzZE8yTEZLTTM2QXpRdGxoWjRLcWJpRE55OGY5eDkyRFltUjdENVdIUHhTNVFZZnYzT3lMbVNGUEhPZW5kamRzcERNUkEiLCJtYWMiOiJjNDUzNjI4YWRlZjUyNjY0NTk4YWViNGQxOTcwYTBjN2FhNmViMTE0ZWUwYjM0YzY2YTJiZjIzMTU1NzdlMmI0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:24 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlNiaHJyZEd1cU9nUnEwVkpyMTUxdlE9PSIsInZhbHVlIjoiREdMU1EwN3JJQkFnZENRS0tyMkNTQzZNZENnRzZNOG9QQjVYTXphZHF4dk5FRFo2ZTlDc0RNektiZEtkeWVHNXhsWEhhUm1xdjdiYmNnalNULzc3TWJKbFh6SnpWaVltTDAzVkhIZ0F6bkhISkdFMW12Nm9XcVgzSU90djd0cFFFSnEzMGtnSTA4YWdYTEJUS2xoWkwwblJyR05pOEdEcHovRzg1eFQ2ZFRBYStRRmFaK2pUZUhEdTBqQ25tYjFSM1NKZ2tDRzNZd0pYNXNudGkxL2tPVWVCdnZacnV2RnRHWTV3bEhpTCt4bUtDK29JN2hGbFZrY1BOQkhQSmlOOGwrTVpGbDJyaXRaS1paVkJpc0ZzekZ3WEIvYzhIZ0hxTHhDWm53SzlmUFQ1dEhDRkVTeGYxZklSZmQ2SUNXUXBOT3hCKzFJeVV6WjM3VFJLL3JPMmhaR0N4Ym5xaG9SWGN0Wks4Wm1OM0tqODN1bTN3bnYxZEU4SEN6Mi90M1Q5ZjcwOTBlTjR5dlh2VmQrcGxvLzVhQVQ4L2FVUkNmZ05jbTdDdEJUaWhOK3ptZzJXMmxENkNPSlNVMDZoZ0lGNUI1U1lnTlBlV3A0ZFdNZ2hycjZobFp1UklBMUFRK0V6SFJLQ3BocDdyVVJGNkl0dlVKMlRtWmNHbGh3UGFGeDUiLCJtYWMiOiIzZDQ1N2Y1M2JkNWMzNWE4ZmJkZDY5NzNiOTI1MDM1ODZiZGI3NDUyYjliMGRmOGMyOGI1MjY3OWM3YjcwMjQwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:24 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1470243813 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1470243813\", {\"maxDepth\":0})</script>\n"}}