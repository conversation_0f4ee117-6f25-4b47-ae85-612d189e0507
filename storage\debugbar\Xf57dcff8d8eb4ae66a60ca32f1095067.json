{"__meta": {"id": "Xf57dcff8d8eb4ae66a60ca32f1095067", "datetime": "2025-06-27 02:25:44", "utime": **********.281292, "method": "GET", "uri": "/payment-voucher/create", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991143.893301, "end": **********.281304, "duration": 0.388002872467041, "duration_str": "388ms", "measures": [{"label": "Booting", "start": 1750991143.893301, "relative_start": 0, "end": **********.22651, "relative_end": **********.22651, "duration": 0.3332090377807617, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.226519, "relative_start": 0.3332180976867676, "end": **********.281306, "relative_end": 2.1457672119140625e-06, "duration": 0.05478692054748535, "duration_str": "54.79ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 46721968, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "1x voucher.payment.create", "param_count": null, "params": [], "start": **********.277184, "type": "blade", "hash": "bladeC:\\laragon\\www\\erpq24\\resources\\views/voucher/payment/create.blade.phpvoucher.payment.create", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fresources%2Fviews%2Fvoucher%2Fpayment%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "voucher.payment.create"}]}, "route": {"uri": "GET payment-voucher/create", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@create", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.create", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=58\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:58-63</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027400000000000002, "accumulated_duration_str": "2.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.257633, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.044}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2673151, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.044, "width_percent": 23.358}, {"sql": "select * from `users` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 61}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2697248, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:61", "source": "app/Http/Controllers/PaymentVoucherController.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=61", "ajax": false, "filename": "PaymentVoucherController.php", "line": "61"}, "connection": "kdmkjkqknb", "start_percent": 85.401, "width_percent": 14.599}]}, "models": {"data": {"App\\Models\\User": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]"}, "request": {"path_info": "/payment-voucher/create", "status_code": "<pre class=sf-dump id=sf-dump-1456588079 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1456588079\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1378979823 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1378979823\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-175505172 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-175505172\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-927118485 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991142203%7C27%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlBFS3RHZit0cVEwZElBaU1KVUgxZmc9PSIsInZhbHVlIjoiYzNpcHg2aittU2dCT2VrSFEwV2ZNaWVDMWRtb1R3VGdhVEZ4NXdsZWZwaThVdHBTSU5XSFNQMnZVTXU0eFM3NjhkTjFONDNlSXpKeVdBb0pQbVdkU0NZeTl1d2Z1Z0ZtSlpnYTkwVnVYcUtjVWV3ell0M2FONkZMNVdVWDBoU0w1cUFZaXAvOUFrYkJuaGRnRTFqK0ZuTE95WUdNbFJIc0ZvUXY2b3dmd0FKTEtQRTNjbys0VGFGMEt6Rk1mM0taL3h4aWZPL0R2eVhia0ZqTithZDZNQXlaZDM5ZktuYXpya2pJbnFVTkJyM3NiUFdBd3U5cWtxNTVlZGR3V3o3azM3dEkxMzkrd0x4YTdKa0VBaldvMjM1SHIyL0FPQkpzM0FXY3BSVVRORG5tRk9OeW5OZEgvbkpMbllmeGgxeDJSWUJKREZVNVdubUFhaXN2QTM2WmQ1MFJvazRvWUI0WStUeXMyekRGMGpZY2FqZHYwekdVcXFlT1hQbnhLZE1JQjlpR2tmSkh3Sm9mWWRGM0M0NVlsamhMbGFOSnJkdFkvNDVZTkU0RlFza2FwTExVVlVIT2pnTU56SHJvaFJKRzl6TUxwYVU4cVRUTkVOY2loeitGS05mdTBJK0ZLc0FHRU1VTnV2cXVHVGNOWG1IZWpSdXh3MkFIaHpqdlNtZUwiLCJtYWMiOiI4MGU1ZjhlNzQxOGQ2ZTA4ZWQxZTJlN2FkMTg4ZjIzNjdlZDllMzc0OTAzNmEzZmE5OTY4YjVjZDVkZmJlY2I1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZmbzFhTC92OWxPZjBMVVp5dzdrZmc9PSIsInZhbHVlIjoiYkt1bDh2cll3NzZQNm9kK0VBOEo2QU9JWmo0WU1LZnY5TDIzVTBzV1FNL1llUmRGUEtQZlI4TFlzMC9rNjQ2cVJYMU1WMUxDUHpuWkt2eEF3ZkxqL2JZZXl3Z0R2RGtwUlh6SjBHZ0VvODE3d2lJUmw2SWdvVkdkU1hMbFJRUG5vVlBGTm0zWHdnOHk0M3U3Nk1xUGNiZGNaWkd5d3pEaWQzdVYzTUYrSUxNMld2bDFqZFU0L2dwZlorZzQ2UVIwZXY4VVhzay9LMjVSYXBBM1Jxc25XZ0pJeE41Q3BzSllhUFBIbHcvcGdLc3UydkI3c3FNWlFlemsweCtNc0N6WEUzeWRNYlVOSEpyK21HNVcvcWtQdWxLdm9Td1J3eDFxMWdVclRZeUR3MFIxZUVQd25VK2lqcFRGbGtPeUhnYWRwNGtrQ3UxQzVkcDE2elpqdVBuc2ZBWURpQXY2VW1Zc2tic0xSNTBtOGdqVW01dXJici94SldKYzZvSFlXZVc1QUNzQ3grWVpFTHZvVEpKTFhqSzBQcWEwTzUvNktjT0ZIY1ZNQ0RVOVZCdDY3RmNlNERlUlNDNS9jcHZUMTdVVkJnZmVwYkpKMUFrVFpRakFGcmw2NUZjcTFoWHRjSC9aWHhVSzY3SUdvOVpPdVpFV0FuY2pTY1lkdXlHM21rc1oiLCJtYWMiOiI2YjMzZjk5NzI5ODJkZGM4MjY0MWMwMDE2ZjQyZWNjZTRjMWRmZmY1YjAzZjhmNGVlYjE0MjhjMDc5YzZmMmI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927118485\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-825341661 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-825341661\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1235509914 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImNYWGFVVHRrZ3dSdTZDTmxETDhscVE9PSIsInZhbHVlIjoiaVNzQU5RNGYvYXdlYmVLVE5XdTl2MzRCQnEvblZrZ2RLS3hzallzN2VFcW40ZzNSUjcwV3dxcjJsV0tqdCtVdUg0bWI5UFRzRzFuYlBzNUpQUTAwUzhMaU9BRy92eEdEcGE5ek41bHMxYmxzTmVLY1BudXVkLzhsUE9nYk9rdHFVWGs5WGRqWEcxdzR1aFVQVWQ3S0R5eGV4WkFNY1gzVk5GM2VKWCtMUjM3dk5ZY2hETTByRTdkVXVUYm8vdUlLelNvdlU3cWxLWTMzWHd0UXJlTFh0WmxneEg2MUFadGo3V2hyTURoNzhqYkZGaFA3SGR2QUNTSTZWc0JnaHNEWHcrMEZNTzUraXZWYi9KLzBSU1hQTy90NG1FcEZwakl4L3pwV1VoeVhTY05LYXUrNWRKeTdqZEV0VmI0TWFyRUdrdVlLVDdQZS96U28vN0txYkd6NEhINVpPQytzbTRQR1V4NDVrOUViM2YyZ2k4TDBQS2ZncUNNaGFubTBWNVJRNUNqb2FhZnFrMlZVWVRFQnp0cWRpelF0UU82dEhnb3BpcndLVm1WYlRCMnBnRyt6dDlPd1VLdGRRMHErTUc1dVhkSTBWYVIvNitidC94YmdmVy9PSkxhUHJvYTFlbSt6TFZpU2ZYZzBtYy9tQUhYdzJVSElWZW13VlZUbnFNQU8iLCJtYWMiOiIxZjMwYzE1MTQyZDRiNTE3ZWEwN2RjZjFjMGE1ZmVhZGI0YmFhMDA4N2NkMzgzNzMzMzhiZGU3ZTJlZmY5Y2JhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdHWVlrMElseFlUMVJkRlg1aEVaQ1E9PSIsInZhbHVlIjoiYmFwN3lmT0g4VGRPUVQ3amIxSVZpV1pPV1hPRWdkSW9IRlIyNW1LRmNaaDlLNkkyalZ3RUR2QThNbExLN1RSQlZiQ3IzV3I1MDB4cFdXWisweWRtTXpJdk5SeXl3ODFSOFB4bS9EQTVwQ00xOXNyOVZTR3lSY05QU3lzUjZJMjZMY0N1aEZud2syMC9VUDhaSXM2V2krekRiYnZMWUN2eEtuSHdtSHEvT25RL0dKU0pReUlLbmh1aU9rK3FLUEhIOTJSZDBvSkhnYjJIbUJKZFhCc2hJajQyVFQzcU4rb0pGajJXemhhVG9hYXIyeGhJUWN2eG84TDQwY2h3Vm96RjVFWFU3MXVtbHlrWnc5bkh4ZG0zNVFtRzh1cmVHM0NoUHFOSVUxeS96K2VWeGhqQzRIa0d3Z3FXRDl3cXpYRUtjcXYweVh4cnViQTRTMXhLRE9icW94MHpNWjd3cmNEZmJWbmZkWk5mQzlFM0xMSXZqM3dRbjB5L3RPS0NUbXVXYzBKRUEzUG9TcHRpaldvRnRsU1lPNUdGbzBNRkZzNGlpRTNWbE11eG1vQXBWMDBpclBXUEx5WXJqZkk3bXFEOWJ0NzVZZDkxbCtYZVZOU0o1YVpoSXlDei85bitDaXQ0MldUZDA3MWd6bTdtTS9EUjgvbTdpb1FxSmJ5ZFp2NTEiLCJtYWMiOiJmNTAyMTJjMWI2ZjI1OTQ3YzRjZDUxYzBmOTljOWU1MGJiMGJmZjFhODk4YjVlNTMyOTYzYjY2ZTNjZjM5YjNlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImNYWGFVVHRrZ3dSdTZDTmxETDhscVE9PSIsInZhbHVlIjoiaVNzQU5RNGYvYXdlYmVLVE5XdTl2MzRCQnEvblZrZ2RLS3hzallzN2VFcW40ZzNSUjcwV3dxcjJsV0tqdCtVdUg0bWI5UFRzRzFuYlBzNUpQUTAwUzhMaU9BRy92eEdEcGE5ek41bHMxYmxzTmVLY1BudXVkLzhsUE9nYk9rdHFVWGs5WGRqWEcxdzR1aFVQVWQ3S0R5eGV4WkFNY1gzVk5GM2VKWCtMUjM3dk5ZY2hETTByRTdkVXVUYm8vdUlLelNvdlU3cWxLWTMzWHd0UXJlTFh0WmxneEg2MUFadGo3V2hyTURoNzhqYkZGaFA3SGR2QUNTSTZWc0JnaHNEWHcrMEZNTzUraXZWYi9KLzBSU1hQTy90NG1FcEZwakl4L3pwV1VoeVhTY05LYXUrNWRKeTdqZEV0VmI0TWFyRUdrdVlLVDdQZS96U28vN0txYkd6NEhINVpPQytzbTRQR1V4NDVrOUViM2YyZ2k4TDBQS2ZncUNNaGFubTBWNVJRNUNqb2FhZnFrMlZVWVRFQnp0cWRpelF0UU82dEhnb3BpcndLVm1WYlRCMnBnRyt6dDlPd1VLdGRRMHErTUc1dVhkSTBWYVIvNitidC94YmdmVy9PSkxhUHJvYTFlbSt6TFZpU2ZYZzBtYy9tQUhYdzJVSElWZW13VlZUbnFNQU8iLCJtYWMiOiIxZjMwYzE1MTQyZDRiNTE3ZWEwN2RjZjFjMGE1ZmVhZGI0YmFhMDA4N2NkMzgzNzMzMzhiZGU3ZTJlZmY5Y2JhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdHWVlrMElseFlUMVJkRlg1aEVaQ1E9PSIsInZhbHVlIjoiYmFwN3lmT0g4VGRPUVQ3amIxSVZpV1pPV1hPRWdkSW9IRlIyNW1LRmNaaDlLNkkyalZ3RUR2QThNbExLN1RSQlZiQ3IzV3I1MDB4cFdXWisweWRtTXpJdk5SeXl3ODFSOFB4bS9EQTVwQ00xOXNyOVZTR3lSY05QU3lzUjZJMjZMY0N1aEZud2syMC9VUDhaSXM2V2krekRiYnZMWUN2eEtuSHdtSHEvT25RL0dKU0pReUlLbmh1aU9rK3FLUEhIOTJSZDBvSkhnYjJIbUJKZFhCc2hJajQyVFQzcU4rb0pGajJXemhhVG9hYXIyeGhJUWN2eG84TDQwY2h3Vm96RjVFWFU3MXVtbHlrWnc5bkh4ZG0zNVFtRzh1cmVHM0NoUHFOSVUxeS96K2VWeGhqQzRIa0d3Z3FXRDl3cXpYRUtjcXYweVh4cnViQTRTMXhLRE9icW94MHpNWjd3cmNEZmJWbmZkWk5mQzlFM0xMSXZqM3dRbjB5L3RPS0NUbXVXYzBKRUEzUG9TcHRpaldvRnRsU1lPNUdGbzBNRkZzNGlpRTNWbE11eG1vQXBWMDBpclBXUEx5WXJqZkk3bXFEOWJ0NzVZZDkxbCtYZVZOU0o1YVpoSXlDei85bitDaXQ0MldUZDA3MWd6bTdtTS9EUjgvbTdpb1FxSmJ5ZFp2NTEiLCJtYWMiOiJmNTAyMTJjMWI2ZjI1OTQ3YzRjZDUxYzBmOTljOWU1MGJiMGJmZjFhODk4YjVlNTMyOTYzYjY2ZTNjZjM5YjNlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1235509914\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2014648246 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2014648246\", {\"maxDepth\":0})</script>\n"}}