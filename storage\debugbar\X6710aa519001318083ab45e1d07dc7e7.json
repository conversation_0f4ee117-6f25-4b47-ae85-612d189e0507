{"__meta": {"id": "X6710aa519001318083ab45e1d07dc7e7", "datetime": "2025-06-27 02:33:58", "utime": **********.561616, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.105016, "end": **********.561631, "duration": 0.45661497116088867, "duration_str": "457ms", "measures": [{"label": "Booting", "start": **********.105016, "relative_start": 0, "end": **********.507118, "relative_end": **********.507118, "duration": 0.402101993560791, "duration_str": "402ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.507128, "relative_start": 0.4021120071411133, "end": **********.561632, "relative_end": 9.5367431640625e-07, "duration": 0.0545039176940918, "duration_str": "54.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45720304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00265, "accumulated_duration_str": "2.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5367732, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 63.774}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.547177, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 63.774, "width_percent": 16.604}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.552785, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 80.377, "width_percent": 19.623}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/account-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-411121376 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im4xdld6cFZEWHNCcElXMkMwSHIvd3c9PSIsInZhbHVlIjoiQjlZKzUyOE1HQnIyQlpqSU1tRW1oTDhxazVUWjA3WVpwSVhMMitFc3VrbU95eVFkRmZrdFZLb2VkUW1hK2FNUHJCZHpBZEhtOUIrZy96V1d4dnlja3lSV2RYK3hadlNKeG96NkdPemJCRGRUTVpYaE41MVdGbXdjVDdDU1NXSVJEL0JSbEFmdFZjNXhqc2o4S3ZTbjhCeFF1OUtTaHdDbWNPeGg3eHFrbVc1eEtVZ1RuekhjeWlMaWlzVzZzKzVFQUNpMXlrVFpmZW5sRjUydUxtY3plemxYQ0NOMXBqL0RrdkpkWVd5WHFpUFRTa01LZ1JtQVFKbExramxYMGFtMk9nSWFzRDZzUnVVY2dTb1hHdWFtejV0YzRKVVZaaXlYRFNHcmRPblI2TVhmeTRydk54bXpmT1hIVGh6RDJXN0VIU2ZrdjhWNU0yVHFWeDh6RjI5eUlUUzNGcTJ2K2trWmZzb1owZlZSREtaS2svTEovcE0ySkdLaEd3SFRzQzBub3pHekthd0NHM2VVRkNqdWlVQmdKazAweW82cEx1VDBFRmMzOGFjU3JyZllqNmhkSWdKQUUwU1hFaC9vVmowTit5ejgvQlFVY0JYSkwwMUhKM1h4NEV0bmY0Rll3bWZxZC9lby84dG45U1BXNU5OWkQwaVdBWjFjUW9uUjVzYjciLCJtYWMiOiJlYWZlOGYwYWY5ZjRhYTViMTA5OThhZTg1MjRlYWY3OThiNGUxZjEwMjNkYTIzMzlkODBiMGMwNmFmZDEyODQyIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImtyaFRxSEFuUDBtWWhudUdoUFRSd0E9PSIsInZhbHVlIjoiZDVJVEx5Wi9BbmUwM1JsVko1K2xBNFM4Q1hzZjNOTjBKbnRhSWVVclF3b202end0d2hDcmErN1lQbmlCejFoQVNjWkpFZmg4R3Z6c1dpQ3ZlYUxmTit5c1pEVi9saHdTRHgxd011N3MzZmhNc1kvV0VqTXk5ZjBxaFJJL3dWSzNzdytvcldwZGx0TGRHR0c2U3BmYXFYUmRNSTZrejNEOVdiTy9BZWVFNm1CUEJaa1Y1RTJ4Q250dGhKQ1lrS2NtbUdPZUh0MFQzZkZ3YXVhUStMemRFb3hFZHB2bXQzS0FjWkg0UWJ4bmJUL2toVE52U2N6R3gwTTdOK2Y1L1BHYTNpSTd0YjNlMUJKTHEzSlNCdG4zMDBrOG9HUjMxM0t5NE84L2V2anlMRkEwQlB3Nmt0NVpzN3BRanVmTDRDZG1rK1dzVENwendMcHVuTVVMTENUeXZsMGxjMVYxOWF3YkdLZTVFVStVNGdIWlp5WDd1UlBsMUVOZVlaeFFSQWEzWnRzaVRZRWphNkQ1RjM1VzdLRWQ1WWY0Tm9Fc21TeUVFWWVXNmZ2ODVoZ0lKR3o0czJWZm9FeE5sSmZ6RUZJUUk3dk1La1diYy9XV0JhYjFMSXpFaU40Q3pCSVl6NkZzM1Iyd0prZi85bkVsWE5nRjIwM05DNjdEcVJyckxMMHYiLCJtYWMiOiJmMTEyYjBiM2E2MmEzN2YwMDNjZGI4MzhkY2I1MTA0YTA4OWVhNjJjOWM0ZWU0ZGE3MmZlZTdkZDY5N2NlNDA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411121376\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-704590778 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-704590778\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1474492599 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:33:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNLa2F2U3JrNWdkeVdqQi92V1MzY2c9PSIsInZhbHVlIjoib05UVXNhSlArMWUxaTRaN3ZnNVlLZEk2b3l5STdTNGpIQ3JrTFlOMVhGZVBjSnEvcG5QeGNMMXlEUGE4dzdFdFdobG5JamRNNFpWbFZoMGhNdkxQdk56QVkwUVpqVzl4aXd3LytjL1NLSTR1UVc5TUpnQUNKT1hPVVM5QTdkYzhQU1lBU0Z6RzdVK1BMc3YwNjd5SDhlRWZFOThGSjBRTG1lZmZtRklLTnlvRE9ZYVJzbXJ3TTZaN0g3a2RqNzNJR0ZVVERLWVVwTTllL1krbzBIQkRXSzJ5bmpxTUZnckRvRkNlbmptamQ2dDNkMVZjWjY4T1ptbDJkU0Y2eXN1ekRiOWdCQmVrNEx6NzN6VXdESlRSblV0UWcvR3dZdHlDSnhQZDRUYkVEcUdTWWpIWEVyZVJGcURydTF2Snp5MnA2YldoZXUxZitnbWJnNFYrN0J3TWcrMG5Ed3hKZXBzZGt0Z05maHNBOGdieHlDQXVyQ1E1WUM1b1RiamRFNldQaytTVzZqN0pLWWE4UkhqZVViR25KMEYxN28yOTUwTFBIcUVBQnVYcTQrVUhTRXJhYzhUbHNJZUF5cFFSbXhHeXNKS0tIWnFyaGc5bTJ3ODd5cUZBTnByY3B2Qjk0N3EvT3o2VnJaSnBFTDBjbWhVbityWUtRY1FJYWovTlNHS0YiLCJtYWMiOiJhZjZmNjVkZmFjOTcyM2I1YjBmMTZlNzI1YTRkNzVhOWQwNWVjZjNmNDIxZjdhNDM5ZGNkNTg2MWVlNDYyYjliIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:33:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNzOTZpQmI3NkxMZ2cxMWtzNk1PS0E9PSIsInZhbHVlIjoiQUxRTkdlZFNTclAvTTFjMHErVnV5eUhjTXZDaWgwdFI1L08zNUFVN3YxTHlNVGdZdUx1REExemV1RWM0d21RRXVSMmVmZTRXR240SGl6SXRkblh3N1ZOVFFzMVBrem0wMkFJUy8yU0gyVm15Z1ZlSWJkNmdTYnA3ZCt4VmZvRmNrZG5aQTBmSnZCRnpHV3Rvem9OMjBvYkxmN2hKd1M5bkFVSS9TTWUyU3Q5NUxTbkhKWGowamIyMkxObllsUWgzcndnQmRNVnNRRlhwTDhkMDIrZjA0VzZYYm5DNitZSm11cE9rR2ZaMTVOeHFSWEI3QlhsZ1hKQ0tkczg5OC84and4RkZHODBQUzZ4cWRYeFNZY2Npd2hhQ1RRM2RtUVoxbWVyNmdUcHlXQWFPQ0xpWFVCdUJDcjhxWndCcER1MFc4azNOTlBKQ0hPMG5pTm94a0VqQllzK1E1TlAzNU93L2kwZmlVMHExR01KNThrYmhXcDlnTmkyczlNSnpNRmZyOVFIRFVSZ25mZVJsT2tncWp2R2hESlBGODZNT1FBTkprdWx4dktQcktadnBvbndJQ0RmQjlvVThEL05JTHlsalVSNFo4dVZvdHF5azZlTldXaEdsVkVLTHRaQXZvR2RRTVZ5VVB4UFd0czJJQWRZWVYvQWVCSlJwM2hTQy9MdmgiLCJtYWMiOiI3YWUzZWI0YThlNjA0NjQ1NzM3YzhiYzg5N2NjN2UyMDllYzNmNWQ2N2UwOTU1ZjQ2OTQzZGFlZjQ2NTAwNDQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:33:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNLa2F2U3JrNWdkeVdqQi92V1MzY2c9PSIsInZhbHVlIjoib05UVXNhSlArMWUxaTRaN3ZnNVlLZEk2b3l5STdTNGpIQ3JrTFlOMVhGZVBjSnEvcG5QeGNMMXlEUGE4dzdFdFdobG5JamRNNFpWbFZoMGhNdkxQdk56QVkwUVpqVzl4aXd3LytjL1NLSTR1UVc5TUpnQUNKT1hPVVM5QTdkYzhQU1lBU0Z6RzdVK1BMc3YwNjd5SDhlRWZFOThGSjBRTG1lZmZtRklLTnlvRE9ZYVJzbXJ3TTZaN0g3a2RqNzNJR0ZVVERLWVVwTTllL1krbzBIQkRXSzJ5bmpxTUZnckRvRkNlbmptamQ2dDNkMVZjWjY4T1ptbDJkU0Y2eXN1ekRiOWdCQmVrNEx6NzN6VXdESlRSblV0UWcvR3dZdHlDSnhQZDRUYkVEcUdTWWpIWEVyZVJGcURydTF2Snp5MnA2YldoZXUxZitnbWJnNFYrN0J3TWcrMG5Ed3hKZXBzZGt0Z05maHNBOGdieHlDQXVyQ1E1WUM1b1RiamRFNldQaytTVzZqN0pLWWE4UkhqZVViR25KMEYxN28yOTUwTFBIcUVBQnVYcTQrVUhTRXJhYzhUbHNJZUF5cFFSbXhHeXNKS0tIWnFyaGc5bTJ3ODd5cUZBTnByY3B2Qjk0N3EvT3o2VnJaSnBFTDBjbWhVbityWUtRY1FJYWovTlNHS0YiLCJtYWMiOiJhZjZmNjVkZmFjOTcyM2I1YjBmMTZlNzI1YTRkNzVhOWQwNWVjZjNmNDIxZjdhNDM5ZGNkNTg2MWVlNDYyYjliIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:33:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNzOTZpQmI3NkxMZ2cxMWtzNk1PS0E9PSIsInZhbHVlIjoiQUxRTkdlZFNTclAvTTFjMHErVnV5eUhjTXZDaWgwdFI1L08zNUFVN3YxTHlNVGdZdUx1REExemV1RWM0d21RRXVSMmVmZTRXR240SGl6SXRkblh3N1ZOVFFzMVBrem0wMkFJUy8yU0gyVm15Z1ZlSWJkNmdTYnA3ZCt4VmZvRmNrZG5aQTBmSnZCRnpHV3Rvem9OMjBvYkxmN2hKd1M5bkFVSS9TTWUyU3Q5NUxTbkhKWGowamIyMkxObllsUWgzcndnQmRNVnNRRlhwTDhkMDIrZjA0VzZYYm5DNitZSm11cE9rR2ZaMTVOeHFSWEI3QlhsZ1hKQ0tkczg5OC84and4RkZHODBQUzZ4cWRYeFNZY2Npd2hhQ1RRM2RtUVoxbWVyNmdUcHlXQWFPQ0xpWFVCdUJDcjhxWndCcER1MFc4azNOTlBKQ0hPMG5pTm94a0VqQllzK1E1TlAzNU93L2kwZmlVMHExR01KNThrYmhXcDlnTmkyczlNSnpNRmZyOVFIRFVSZ25mZVJsT2tncWp2R2hESlBGODZNT1FBTkprdWx4dktQcktadnBvbndJQ0RmQjlvVThEL05JTHlsalVSNFo4dVZvdHF5azZlTldXaEdsVkVLTHRaQXZvR2RRTVZ5VVB4UFd0czJJQWRZWVYvQWVCSlJwM2hTQy9MdmgiLCJtYWMiOiI3YWUzZWI0YThlNjA0NjQ1NzM3YzhiYzg5N2NjN2UyMDllYzNmNWQ2N2UwOTU1ZjQ2OTQzZGFlZjQ2NTAwNDQ2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:33:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1474492599\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}