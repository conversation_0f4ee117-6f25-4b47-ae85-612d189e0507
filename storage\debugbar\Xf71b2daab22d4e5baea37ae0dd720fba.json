{"__meta": {"id": "Xf71b2daab22d4e5baea37ae0dd720fba", "datetime": "2025-06-27 00:14:50", "utime": **********.781164, "method": "GET", "uri": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.280814, "end": **********.78118, "duration": 0.5003659725189209, "duration_str": "500ms", "measures": [{"label": "Booting", "start": **********.280814, "relative_start": 0, "end": **********.700289, "relative_end": **********.700289, "duration": 0.41947507858276367, "duration_str": "419ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.7003, "relative_start": 0.41948604583740234, "end": **********.781181, "relative_end": 1.1920928955078125e-06, "duration": 0.08088111877441406, "duration_str": "80.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47506720, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.show", "controller": "App\\Http\\Controllers\\BillController@show", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=333\" onclick=\"\">app/Http/Controllers/BillController.php:333-393</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.00374, "accumulated_duration_str": "3.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.7312691, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 50.267}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.742197, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 50.267, "width_percent": 12.032}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.749506, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 62.299, "width_percent": 15.241}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["1", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.767473, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 77.54, "width_percent": 14.439}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.769383, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 91.979, "width_percent": 8.021}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => show bill, result => null, user => 1, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1671181977 data-indent-pad=\"  \"><span class=sf-dump-note>show bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">show bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1671181977\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.773402, "xdebug_link": null}]}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "error": "Permission denied.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-1776849405 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1776849405\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1263149198 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1263149198\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1813280615 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1813280615\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1884558400 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983282490%7C45%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImdTOGJCNnlQR0tNdDVDYjBXL2Q2cUE9PSIsInZhbHVlIjoiVFB0cWtFR2hmb1dQZ1YvVDlFSUt5UG9SbnhqamlicHlFZnFZaXNOc1p6Z0g5WXlYQzk0VEh4ekZJRStLVERkekFueWNyV0diYzBjbjdRSGI0U210RllFejdjVFJQeXpjUHlMaWNnUnVKZnM1S3dnZ2daRVdJWVpKejAzMlVoUTJVYkl2TXllS0R4OUdXWlRzTm9NM3pkQVB3NkIwQUVtMTV4RU1wcjRvaS9uRTJxS1YvMHBJdFU5dWJqdGdlVjg3eUYwa1B5UE9zSTFHZWtKUXFYUUFqSUpZNVJjQUMzTmRnRWdwalRCMXh6cUVTVERWeEFnaEJWMWFlcmlDT09RWGgvYnliSjR5TDZ5a3kzSktvUGsyUEY5Y1kxWkYxMmFhSVJIM1JxN0UxVlMyV1V3Wk84Z0xmOS9DQW84dGNvL29CQWp6RG5FMDNjeWk2UEk3RkowQmw4TFo1MDl3Vnl4TkllK2x0NnFZSzdLbVJYSTNGNnloaW0xWEVCemNBSitsMWFldEFaVzhFZWQ5a2NLQmxTVEwxWHJ5UzJ3cmZPdE9vNDlKelBxeEJidForTEJVbTFnMDZwK204Yk5qd3IwR3BoZ3RhbFUrL1FxblQ3TTNjeVk5Q3ZPcWNsVFR3RjM1enVkRGQ5S3V0TlUxS1hQcU9WSExZc0pvWDc4RjZGYWciLCJtYWMiOiI1NzcyYTcwMGE0YWIwMDEyMDlhYTk5ODk3NmFjOTEyNmJhNGMwNTNkMmYyN2Q3ZDQ0NzM4ZGZhNGFjMWMzMGM1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjAyaXJaMjE3UzJ2aGkxaW14QmxEZ2c9PSIsInZhbHVlIjoic3hMYlQ0L0xPWjFNWW1sTE9IWjBZQU9UUElocjZ0cEFIQVo0WTdQUTdiUXAybUJSRkpIaWhIUnVVbGh4QXFrZzAzeXdtc1NpdFlGRTVUWU9zU1NaalpqS0p2UHR3R2hEcHpXRVJWenFlRFlQNGtCUHd0cFFsY2grUklOZ0lzV2JjZmRicmF3b2d6NGxkU2prK04wV1ZHcVk4VE5BQmlFNVFBREhvRmw2cXkzT2NybzlEeVB0bmJHZ2FXc2hJMld0dEdxN082L2djdkN1QWJTOG1kaGVlbno2elJYcGFPVjFTSC9nd2djQ3pJd2dKSFlaMzVYdVVkWEFDUmRDWWEzZUdTQzNxY3FrejFka2dIMmU3Z09VclVTbjcrTUljZ2VGdVBGK01OY3huTVp4dlRlZWkxMXRWSVdGNEp6NUMrN2Y2TFU2Vml2eEE3MmxlSnF0ZWlxRG1BRWJWWEFnMGp1ZHRrRDd0QzNvRDJWNm9IWlZCQ0tReFp2R2lQNjl1U2o4UDZjYjIvM0dxOWcyTmUxV2N0N1c5bW5jM0pXTlVXLytaVnFPNHdYQldRa25rZG5MMnJjUERHUDBCQkY1WU04d1JLNGxWd2ZGblozcjlpcTlHSWN0YUhUbjVSblJYcmh4WWwxRWVGT1RIZ2V1K0RGcDFKNmVFTFBSSHRubkJjOUoiLCJtYWMiOiIwNTU1OTZhZGU5NDg5N2QxOTcwNmQxNjUwNjMyODA1NDFhMjcwZTA3MzMzMTM2NGQ1YWQ4MjhlN2FhYTk0Y2M4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1884558400\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1810082168 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1810082168\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1532878656 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkpmSFMxaFNIMnN2NE91RlRkaXI5aGc9PSIsInZhbHVlIjoiYUNmKzhBYXRQWlZISi9Vbmo0TzRjdWxIVEpWWHVBL0dwUHAvakIyZEUyVnhJNVVNU1dFYnRQNGNJWFZwbmIxaGJrUXlMS1lua2tJcFczMFRQQUFqOUZNbGJLbWFFQnBDWmpPTkhTY2M5bEkwVWQ3WlQwNHR6UTJBQi9laTRrOUo3YTdDU3pkMEtXZ0hZS2ljVngrR2g0TUxza3c0YU9OWGYyMkNRZEtKRTBJcGdpejV3UHVZbXdSQ0NTdHZEc3RrcDhwTGp6elhTYWJkdGs1WlVlcVI1M28zd2p6NnlFUW1LTmZLZkNWMVp0c2NyTk5yVysycTd4M3hDay9IcTlDeG9kOTR1aVMzTEpoZnNyZ1hLWHVvRlJadGRVR3VIdVFRRE03K3V4ejdPMlVMalkxMHVhWGViYXo5ZkNJcXgrLzJQK2FHTkgwVmNUNEdWb254MHJOMHdWUDFzenIrRkIxc1V4ZTFmYXI3WUZhTHFEVVBXVE9ZOXpmcDFCZDBTV3pEMkxQUWpUaFM1aXRKaUFkeGxJamcvWXNSZ2ZzMXFONS93bkFHNWxkR1ZxaUlqTXl1WTVtajV4UlBKVzZSaEtScjNEMXhhRWN0N29rQ0Rjc2VZQ1Fhb0RXa1o3UnNWejVvcnlTWUNZbExtSDFPOHR5S1IvTkNDejAwZ3k3ZEl1TVYiLCJtYWMiOiI0N2I3ZmU4NzQ0NDljN2Q5YjY5M2ZiMmE4ZDk5NmQ2MWVkM2ExMzU3NTA0ZjRkMTkyOWRiNjZkODFiMDRjNGFjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjloTnRLS3FlUWpNdzBJZUl4NDQxcXc9PSIsInZhbHVlIjoiZFRHVkMvbExmSTRSRFAyVE9MT1ZselpNQnJGczlvenJCcWQ1akdYWFpOUmplcHhUa3p6ck4yclphSDBFb1FUei9JZERPWisydlg0YUJpU1dXYzh5V3AzMFpQNlBNc3JRTFd3dVF6ZWxray8yaW53Q2ZvSUh1b2NGYjA0REtSdWtMb0hpZ1NaNitBdTQwMTkySkRGZ09Nb3JhTkZIQWZrSjZrMnVMVTF5Wk9MOGRlTytjalhSWWZyc25PVDE3R0tzMU9UUytFcG9wQVMxaGtTTXpZejRQRENMOVZ4ZkZ5amVqdGRtK3JFRzcwcGx2R0tjSjQvL2NkN0d1Ym92dGJXRjByenI1elBWVDR4RWI5WmgzL1hObE0yU3ZPVHZqa0YwQXRxVkVUQWFqNmg5dUV3L2ZUNXpsZHg4TGhqV003M0U2eG5RUEpyUElod1k3R2tQbWR4clVMempyZGdwUWpwN2FvVHMvNUZva29jUCt0b0RIQ1FaVmloZUlHb2R1eTl4M2V2bVpRWERPZzdlNkQrRS9ZNGozTjdRVGsxYkorNVgxSmliQklKYVBzUUdOWjNkUE45SGJ6OFA4ckQ1NmhTYlEycmJkS2ZpRlJZT3NEV0VEdmpRdW5JOG5EWnVVeEpYbC9raW01VHh3MXVwZUJVaE5lQWpLQUk0cUxkRU1raWUiLCJtYWMiOiI1ZjFlZjhjOTBkZGY0YjZmYzZlOGNhNGIwZWM0NmU1ZTk0ODEwYjkxZTBlNDRkYzgyYjk5MTA3NDEyMTM1NjMxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkpmSFMxaFNIMnN2NE91RlRkaXI5aGc9PSIsInZhbHVlIjoiYUNmKzhBYXRQWlZISi9Vbmo0TzRjdWxIVEpWWHVBL0dwUHAvakIyZEUyVnhJNVVNU1dFYnRQNGNJWFZwbmIxaGJrUXlMS1lua2tJcFczMFRQQUFqOUZNbGJLbWFFQnBDWmpPTkhTY2M5bEkwVWQ3WlQwNHR6UTJBQi9laTRrOUo3YTdDU3pkMEtXZ0hZS2ljVngrR2g0TUxza3c0YU9OWGYyMkNRZEtKRTBJcGdpejV3UHVZbXdSQ0NTdHZEc3RrcDhwTGp6elhTYWJkdGs1WlVlcVI1M28zd2p6NnlFUW1LTmZLZkNWMVp0c2NyTk5yVysycTd4M3hDay9IcTlDeG9kOTR1aVMzTEpoZnNyZ1hLWHVvRlJadGRVR3VIdVFRRE03K3V4ejdPMlVMalkxMHVhWGViYXo5ZkNJcXgrLzJQK2FHTkgwVmNUNEdWb254MHJOMHdWUDFzenIrRkIxc1V4ZTFmYXI3WUZhTHFEVVBXVE9ZOXpmcDFCZDBTV3pEMkxQUWpUaFM1aXRKaUFkeGxJamcvWXNSZ2ZzMXFONS93bkFHNWxkR1ZxaUlqTXl1WTVtajV4UlBKVzZSaEtScjNEMXhhRWN0N29rQ0Rjc2VZQ1Fhb0RXa1o3UnNWejVvcnlTWUNZbExtSDFPOHR5S1IvTkNDejAwZ3k3ZEl1TVYiLCJtYWMiOiI0N2I3ZmU4NzQ0NDljN2Q5YjY5M2ZiMmE4ZDk5NmQ2MWVkM2ExMzU3NTA0ZjRkMTkyOWRiNjZkODFiMDRjNGFjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjloTnRLS3FlUWpNdzBJZUl4NDQxcXc9PSIsInZhbHVlIjoiZFRHVkMvbExmSTRSRFAyVE9MT1ZselpNQnJGczlvenJCcWQ1akdYWFpOUmplcHhUa3p6ck4yclphSDBFb1FUei9JZERPWisydlg0YUJpU1dXYzh5V3AzMFpQNlBNc3JRTFd3dVF6ZWxray8yaW53Q2ZvSUh1b2NGYjA0REtSdWtMb0hpZ1NaNitBdTQwMTkySkRGZ09Nb3JhTkZIQWZrSjZrMnVMVTF5Wk9MOGRlTytjalhSWWZyc25PVDE3R0tzMU9UUytFcG9wQVMxaGtTTXpZejRQRENMOVZ4ZkZ5amVqdGRtK3JFRzcwcGx2R0tjSjQvL2NkN0d1Ym92dGJXRjByenI1elBWVDR4RWI5WmgzL1hObE0yU3ZPVHZqa0YwQXRxVkVUQWFqNmg5dUV3L2ZUNXpsZHg4TGhqV003M0U2eG5RUEpyUElod1k3R2tQbWR4clVMempyZGdwUWpwN2FvVHMvNUZva29jUCt0b0RIQ1FaVmloZUlHb2R1eTl4M2V2bVpRWERPZzdlNkQrRS9ZNGozTjdRVGsxYkorNVgxSmliQklKYVBzUUdOWjNkUE45SGJ6OFA4ckQ1NmhTYlEycmJkS2ZpRlJZT3NEV0VEdmpRdW5JOG5EWnVVeEpYbC9raW01VHh3MXVwZUJVaE5lQWpLQUk0cUxkRU1raWUiLCJtYWMiOiI1ZjFlZjhjOTBkZGY0YjZmYzZlOGNhNGIwZWM0NmU1ZTk0ODEwYjkxZTBlNDRkYzgyYjk5MTA3NDEyMTM1NjMxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1532878656\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2065405815 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Permission denied.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2065405815\", {\"maxDepth\":0})</script>\n"}}