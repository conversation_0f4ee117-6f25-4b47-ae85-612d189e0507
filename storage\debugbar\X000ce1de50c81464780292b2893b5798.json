{"__meta": {"id": "X000ce1de50c81464780292b2893b5798", "datetime": "2025-06-27 02:23:35", "utime": **********.184328, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991014.718188, "end": **********.184342, "duration": 0.4661538600921631, "duration_str": "466ms", "measures": [{"label": "Booting", "start": 1750991014.718188, "relative_start": 0, "end": **********.050804, "relative_end": **********.050804, "duration": 0.33261585235595703, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.050813, "relative_start": 0.3326249122619629, "end": **********.184343, "relative_end": 1.1920928955078125e-06, "duration": 0.1335301399230957, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50171880, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.07385000000000001, "accumulated_duration_str": "73.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0801399, "duration": 0.01447, "duration_str": "14.47ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 19.594}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.102761, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 19.594, "width_percent": 0.664}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-27 02:23:35', `closed_by` = 22, `shifts`.`updated_at` = '2025-06-27 02:23:35' where `id` = '45' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-27 02:23:35", "22", "2025-06-27 02:23:35", "45"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.113597, "duration": 0.054990000000000004, "duration_str": "54.99ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "kdmkjkqknb", "start_percent": 20.257, "width_percent": 74.462}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-27 02:23:35' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-27 02:23:35", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.171164, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 94.719, "width_percent": 5.281}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "success": "تم إغلاق الشيفت بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-343429139 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-343429139\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-2138319166 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2138319166\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1026344317 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">45</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026344317\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991013146%7C17%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkQ0YU15cUFzcXgrSEU5WDZ6MVZ5YUE9PSIsInZhbHVlIjoiL1RIdDJ1NmUyVktrM2xMMmR5eDJ3S1ErM0EyakJLUUF5bytmL2tDWW1BaU5vT01pRFNkblI1Z1p1NW8xWDJmYlVqSm44QUFDTkpOOEFjdXpBN0I2ZG1RY05ENGNCaWN0NWxCYXdzaWdLYU1VQVAzU2JXNHpMMnhqOGFwUEZuODVXVWZQWjFROEpiY3ZseU53QVBBWmdWaUdKQlVUUXB4YmtGNm5sNkIrL3hia1psTlRmem1PSHA3cm1Mbkt1MXZSSmd6c2pzWU1uTnNLd1hhK1dSNldPU215UDZBRFkvRk1XNVBnSmRzQURqVDE2K1lRZWZub004Wk9ZbXhmZnJiUDc2Q1NKMUQ2VUpCdnU2YzVLcFhuSkVrLzFmbUdUejZ0WDd3NVdaVlhvLzdUUlUzOXhySkpxV3NjcTY4NjdUbElOQkVxVlFBdk1EcmNHM3RFK0NUbEdJdlUrZTM2Z0FscEFIOGI4NG5Tc0kvdmNuVmlGZFpBYzVELzRVYXJJNVloMXVQbU5qWTdBbmJ4cDVaY2tMcjdiSlR3d0hQSnhGNU93bTQ3eEdUdzlFYWFWTThjcWRDV2pkaFc3MThrenRtaUZzT0h3YmxnQWV4T3R1VjVMd1FZR0dpNHIwc3RPZnI3WFpMWmcyV2dXazFXMTlEektoOVg5a05jUzdGZzJCR0ciLCJtYWMiOiI4NTlkMjllNTkwNjcxYzYyODc3NmQyNGRkNGQ2NzAzODM0ZWMwZWY3ZjE4YTMzNDg5MzI5MTEzMzEyZWNkMzRmIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ill6WElXS1lWSHpETEdXcFdSaWpxaEE9PSIsInZhbHVlIjoia0ZhOXpQYmZ5emdCdmZ1RXJDSjlXaklDeTRKUVhDLzEwaWE2NGRQSmNtUlp6RTc0YVVOM1luTU8rcW5YVTF6blcyVmNXOTc0SE5TLzJBSUEyb1U2a0RUL2ZVV3RFK0VHbmJ3ekcrdERLaUlrdnZ4T1VsQ29zQmc1MXhIUnlhcDcyUUhSbUVKcFVtWUo5YnRTSU1lZ1VDMk80cVZRR0FTOVlZdHJjeTgvY0FEMlpGNjZxTmYxRkEzMXRjeHVWOVEzaTZDT2tXM3Y4dzVpK0lLOC9sb2RSdTRIM3BNcDVUeENSRWV3Tjg0eG9TMkJ3YVU1aE1Gd1RheHV6bmhaVHlWdFZhR1VVY21LQWdqMGMxT1NXcHRFRHlJOUJUYUNka0N3alpsQjA4K2NIcGpQL3lGVzhIeWx4M21NRjdFZ1M5T1M0di9uRzlYRmlqUlUzQ1dzd0NaNllJZzlVbWNGOGt1YnZ6UlVNUDdvUko5MG5ZRkVzRGNYYTdwM3dZQ09SZytiSFA1YWpYQlRDOUFkOFpQQmN2VUJxWFlHTDhoV25sZFA1WGgxc00xZWxsVW5DZTFCYkNmMmtQZU9QK2RsdkVJM3NrSXY1NVlEVUlmWHVHRExrVkRXcVFJSWVncWJnd2NwemozNkVqSXFpWWQ4TDk2TGs5aGVCK1VRVVFkS0FXN28iLCJtYWMiOiJlMzNlMGYxZWE3YzA5N2I0ZDFiYTkxNTFiNDlkNDNhYzI5ZjI3ZGVkOTM1MDE2MjU4NDY4NDg4MzQ0MmFlODQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1490693252 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1490693252\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-351752571 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlpHWVVhbDVCNWVVVlptRGZUL1lHUnc9PSIsInZhbHVlIjoiNU5FNm5OeXBHVzlmZWdhcWRJTU1KWVJTT0c3WkJXemRDcW5BTnpVV0p1dmdmVFRZZ0tqR1ZOdzYydllvNm1HTmcwdnNXcitMVWUya2F6UG1ZbFp6aXM5dTJLdytNcHg4cEhIRklDeWVrVEtLeG9iQUZPOG5mT1NDai9PYndkOUtFNFRSblFkQXBDMEJKMGRVNW01UisyZ2x1cWIzZ2RRclZxN3VwckM1ekUyYVd2aUg2ZGt5Y3hpdk01cU1rL0Z4QXQxUEZjOWtFaXU2V2RFNFZiS2xhMWVPSDVEZ0h3NkZURnpoenJud3AvbmUvTnFZUktkNFpUT290Zlc5TkJSV0lONFlybnhUYUN3Y0lxL3B4VHY4RjFXT251L01BcWxxVTBFU1A1eHlMS2hYY1FQWG1iOVY3SWlxelNwa0hRWFltVkt5c0FCSXRmcE05YkRac1ZDdzdUWWhQSjMzSVZxTXFzM3hsRHRUTlhXWjFKZlRvTG9nLzFWcDJpSVlvYVozVlZWL282cWJBcjJZb1A5MVlzTXF2VWRTQTUzaUJWSjVFaE81b3l0b0JJaHZZZEN3L25oMkl0cHIzWjJFa2w5MFNOVi9FNVlXT3BoQTF2RFBzcjBSQnJ2Y1NxdExPVEt0bW1GdzM2MFFXMmNlTDFPamFYZlJpM2pVRE1QK2tUbnciLCJtYWMiOiIxNmU1NGQ0MzgxYTY4ZDViNjc0ZmVkN2M4YjIyMzkzYTc1ODJiN2I4MDVlYjU3NTQzZTc1ZDViNTNkNThmNzIwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:35 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InY5OGZzV1FkTVd2MEMxRTRodVl0VHc9PSIsInZhbHVlIjoiY3UyNGZUWGoxTUltUVBJUlI2WWpDRjZEZmhrT1c0RldHek9ld1BnaTN3dUpXakYrZXN2VWN3Z0R6S0tJdndNelNhU2RMS0w1b1kzZldmVXRFVXh2SzI4WWNSNGRKOFBQYmRWQ0VHWTN5a2pXV1J1LzhyNnd1VmRuY0hxT01EM0RZNVVtMWdEOE9UbmtONGZNTHZHbFFVcEN6RzlHZ2VwTkdnMG00eUUrMkxjY2NOWnpkNXRFSmdyTGxBVGRYK2Y3Q1liWHNrZ3docXJPZDVjbkRoZW4wRWdqRnRKS0xCYzBacVpGTUd1a2prODVQb0xtS1FkSml5eGJjUTNlU3BDNFdZMkc0WXMzcUpKQmVnTG1zTTJLTVY5aEMyb2ZOOXorZTg2RzZQd2c4RzFuZUlFTzYrb3lreFV0K29CY3g1WVRFeHVtTGxtVGFwaE9NbjBteS9vcS9XUnBQbTA2Sllud2ZEaE5DaloxOGNQY0FrdThJMjN6TXVBb2xpSlhjMUFDZWZpUW0zcEppMEpEWW9mVE4zUzFQNlhGWUY1bVE3MkVCcVExVEVScmF3ZnZwSVdyR2FjMEpFZFZDdFRQUURkcjdJM0c4VG1SZnVYc1gyaDdYK1o1M21lUUh6NklFdHhtaERzS0lmL3I5Ui8vaHNmallFSzJtVXM3K2luajNmZTYiLCJtYWMiOiJjNzRjNzFkMWJiYzdiMWU2MzgwNDEzMTNhODkyNzZlY2FlY2UzNDE5Yjc0M2M5ZTRjNThkNzExZTc2YzVjNzM5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:35 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlpHWVVhbDVCNWVVVlptRGZUL1lHUnc9PSIsInZhbHVlIjoiNU5FNm5OeXBHVzlmZWdhcWRJTU1KWVJTT0c3WkJXemRDcW5BTnpVV0p1dmdmVFRZZ0tqR1ZOdzYydllvNm1HTmcwdnNXcitMVWUya2F6UG1ZbFp6aXM5dTJLdytNcHg4cEhIRklDeWVrVEtLeG9iQUZPOG5mT1NDai9PYndkOUtFNFRSblFkQXBDMEJKMGRVNW01UisyZ2x1cWIzZ2RRclZxN3VwckM1ekUyYVd2aUg2ZGt5Y3hpdk01cU1rL0Z4QXQxUEZjOWtFaXU2V2RFNFZiS2xhMWVPSDVEZ0h3NkZURnpoenJud3AvbmUvTnFZUktkNFpUT290Zlc5TkJSV0lONFlybnhUYUN3Y0lxL3B4VHY4RjFXT251L01BcWxxVTBFU1A1eHlMS2hYY1FQWG1iOVY3SWlxelNwa0hRWFltVkt5c0FCSXRmcE05YkRac1ZDdzdUWWhQSjMzSVZxTXFzM3hsRHRUTlhXWjFKZlRvTG9nLzFWcDJpSVlvYVozVlZWL282cWJBcjJZb1A5MVlzTXF2VWRTQTUzaUJWSjVFaE81b3l0b0JJaHZZZEN3L25oMkl0cHIzWjJFa2w5MFNOVi9FNVlXT3BoQTF2RFBzcjBSQnJ2Y1NxdExPVEt0bW1GdzM2MFFXMmNlTDFPamFYZlJpM2pVRE1QK2tUbnciLCJtYWMiOiIxNmU1NGQ0MzgxYTY4ZDViNjc0ZmVkN2M4YjIyMzkzYTc1ODJiN2I4MDVlYjU3NTQzZTc1ZDViNTNkNThmNzIwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InY5OGZzV1FkTVd2MEMxRTRodVl0VHc9PSIsInZhbHVlIjoiY3UyNGZUWGoxTUltUVBJUlI2WWpDRjZEZmhrT1c0RldHek9ld1BnaTN3dUpXakYrZXN2VWN3Z0R6S0tJdndNelNhU2RMS0w1b1kzZldmVXRFVXh2SzI4WWNSNGRKOFBQYmRWQ0VHWTN5a2pXV1J1LzhyNnd1VmRuY0hxT01EM0RZNVVtMWdEOE9UbmtONGZNTHZHbFFVcEN6RzlHZ2VwTkdnMG00eUUrMkxjY2NOWnpkNXRFSmdyTGxBVGRYK2Y3Q1liWHNrZ3docXJPZDVjbkRoZW4wRWdqRnRKS0xCYzBacVpGTUd1a2prODVQb0xtS1FkSml5eGJjUTNlU3BDNFdZMkc0WXMzcUpKQmVnTG1zTTJLTVY5aEMyb2ZOOXorZTg2RzZQd2c4RzFuZUlFTzYrb3lreFV0K29CY3g1WVRFeHVtTGxtVGFwaE9NbjBteS9vcS9XUnBQbTA2Sllud2ZEaE5DaloxOGNQY0FrdThJMjN6TXVBb2xpSlhjMUFDZWZpUW0zcEppMEpEWW9mVE4zUzFQNlhGWUY1bVE3MkVCcVExVEVScmF3ZnZwSVdyR2FjMEpFZFZDdFRQUURkcjdJM0c4VG1SZnVYc1gyaDdYK1o1M21lUUh6NklFdHhtaERzS0lmL3I5Ui8vaHNmallFSzJtVXM3K2luajNmZTYiLCJtYWMiOiJjNzRjNzFkMWJiYzdiMWU2MzgwNDEzMTNhODkyNzZlY2FlY2UzNDE5Yjc0M2M5ZTRjNThkNzExZTc2YzVjNzM5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-351752571\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-36674289 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1578;&#1605; &#1573;&#1594;&#1604;&#1575;&#1602; &#1575;&#1604;&#1588;&#1610;&#1601;&#1578; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-36674289\", {\"maxDepth\":0})</script>\n"}}