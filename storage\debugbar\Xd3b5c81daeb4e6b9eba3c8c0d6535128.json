{"__meta": {"id": "Xd3b5c81daeb4e6b9eba3c8c0d6535128", "datetime": "2025-06-27 01:13:31", "utime": **********.538901, "method": "GET", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.104258, "end": **********.539096, "duration": 0.43483805656433105, "duration_str": "435ms", "measures": [{"label": "Booting", "start": **********.104258, "relative_start": 0, "end": **********.433467, "relative_end": **********.433467, "duration": 0.3292088508605957, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.433476, "relative_start": 0.32921791076660156, "end": **********.539099, "relative_end": 2.86102294921875e-06, "duration": 0.10562300682067871, "duration_str": "106ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 47599744, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@index", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=33\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:33-84</a>"}, "queries": {"nb_statements": 5, "nb_failed_statements": 0, "accumulated_duration": 0.02997, "accumulated_duration_str": "29.97ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 23 limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4673228, "duration": 0.0264, "duration_str": "26.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 88.088}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.501547, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 88.088, "width_percent": 1.134}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 9 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["9"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.504478, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:37", "source": "app/Http/Controllers/FinancialRecordController.php:37", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=37", "ajax": false, "filename": "FinancialRecordController.php", "line": "37"}, "connection": "kdmkjkqknb", "start_percent": 89.223, "width_percent": 6.807}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 23 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["23", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.519894, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 96.029, "width_percent": 2.336}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (23) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.521894, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.365, "width_percent": 1.635}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 5, "messages": [{"message": "[ability => manage pos, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-908855136 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-908855136\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.526154, "xdebug_link": null}, {"message": "[ability => show financial record, result => true, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1445692779 data-indent-pad=\"  \"><span class=sf-dump-note>show financial record</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"21 characters\">show financial record</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1445692779\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.527313, "xdebug_link": null}, {"message": "[ability => manage pos, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-5693894 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-5693894\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.528091, "xdebug_link": null}, {"message": "[ability => manage pos, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1444098932 data-indent-pad=\"  \"><span class=sf-dump-note>manage pos</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"10 characters\">manage pos</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1444098932\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.528841, "xdebug_link": null}, {"message": "[ability => manage delevery, result => null, user => 23, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-1853098850 data-indent-pad=\"  \"><span class=sf-dump-note>manage delevery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">manage delevery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1853098850\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "error", "time": **********.529733, "xdebug_link": null}]}, "session": {"_token": "W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "23", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-1254370032 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1254370032\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-158914102 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-158914102\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2117231135 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2117231135\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-651465026 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1841 characters\">_clck=tu6zsp%7C2%7Cfx4%7C0%7C2004; _clsk=1ggm1jz%7C1750986807338%7C2%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilg3TytrdmJKdUhLMkR2QWk3dzhsYWc9PSIsInZhbHVlIjoiSEM0T3ZYTGwrb1U2d21rOVBKYVRXSFJqNWp2ekg2OUdQRmRDWnNMaGNqYnJLc0NSaWJHTXgzL1JpUkE0clI4UCt5SDcxY0Y2eFdYTzF3cy94Ykl6bWVxOUVaRzJzZExaOXdOQnFOZFB3NzF3Q2x6MDF4aUUxaExUY2RlaGhISlJVWjM5c04zeU9BOUFqQTVoeTN3MGNEa1V6aGQ3SXBVK1UvMnpjcXpRcTEraWVqUkJXWUM2aFlJMXRsL1c2TEVzRG53bzNCTzZHM3JacEhqT00zR1ZIUnRaWWRVdTMvNXZzRUdmcHpOS2xzMi9jNzZGYUVrUllBY0FmMFVuN1RFNFZqZWtpVitWanpjNVpMb2FZb0pVWGIyOVNLQWc0UXBmcTBkcXZnRDJsUUtiR1VZRlAyWHNPQWFLdkR6TldWU2xtT2pESEJ2ZHpRVE1RNkJzT01vWFM0WjVwNXk2aHlXeDJNZVFNQ3I2eGNFNDZPZjNIVWpJbnRoMUExK3Z0eHVDWmhzeXNseUNRSmdoSEpQSytwc3JtaEdUT3FCUEFFejM2OWJ0b0dDRklySkcwcUNWMFMvTnZ2T2JGMmNWWi9JcE9JVDlnUFBSQ0t1bVgvLzgyaGRGVXovaGg5SFpQWFdZYjlaUlNiMlMyVFFYY0RRUG1zcmJSa056ZyswYk12MlciLCJtYWMiOiIxYjdhYTNkZjAzMThjMDYzMjY2MDAwYzYxYjI1NDYzODM4OTUwNGYxYjZiY2M5NDRlZjUxZGZiMzU5NjE4N2VhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVKcjRSOTRtLy93ZkVXNWN3VU8yM3c9PSIsInZhbHVlIjoia1JwaHpMWitiOHFjNG1WRURiMS9CbE9QOXhSeHR4VVVRTlBGSWFYS1lVUDRXZGdoazlNNzZnc1ZsUjNBc1lMUmdjVmJGdmN4dmowMDA3ai9GRGV4WlUyZFJvYk8zckdxUTZHb1NCb2ZadzFDRG9WT0l0SE9wNTZUZkFpRGRqWlhGWmdOdEpUYXErNTQydVpUam5nU2tlK1YrRmhlZU12dzZYQjNiVWhua2w3ZnpnMGtTbURKd01JL2tRelBYdFFoTnFlWlAxaEppZ1cvSFYxUjV6SUMvcTBVRjNvRFFQM3lySEwyUjg2eGFEL1hHVmp2OFhZaUdBbFBJTmM0ZVZySWY0aVpnVlBTdnlibFgrdlpVaUdlZDVFU0NaQzJVbzNJQStaVFRjc0svVTRTZUx4QXJTcjZ0TEFxa3FNYWw5YUVDdnBhZHhYT21EdzN2QktTSmh6Vk5zVHBNS0pxOHc3QWp5RnB6dEJua3RoZjNyWFZRSWM3Vk1lMlFrSVdRNUhoN0ZKbUY3T2cyYU9FeThxZ1Vac2tib2JSRUUrS2FyT2FlWmNjUnJudGUxS01sZm4zVHErMm9vU2tGMUFiWFFpQlUzSEFUSUVaQk9HdFdUYUUyQVJiTFhxelJsekhyRXIzU0FXUTBiNlNFRzhHc2pqYk1lYk9tY1Z5YmVoS0FxTlEiLCJtYWMiOiIxY2Y4ZTBmY2I5MDkwMDEzODBkZWI3OTgyMzNkYTU3YTczN2VhMGY5NzM3ODE4OTI3MTA2N2I0ZTcwNGY2MGYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-651465026\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1911302692 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">QVqhpWieKZCLT3nmskTMtatIDNwBdc2egPwBt6XG</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1911302692\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1905820469 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:13:31 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImJ6RTY3RGpJRzJ0dkFQS2pKK3lIOXc9PSIsInZhbHVlIjoiRkRaeVBDQ0VFSEozL2VLS1Q2SmJsNm4xZDBSbnZzOTVrWE1tcTdIdjZqUTRLNDhNai95SXlOMGI2NFFMdnp2eGduaGFMSFQzNnlIemtGbHZ4RloxT24vQ2FueEo4bW5pbFBBbi9VUHEyb1dpMWhRWitsRlBrankrQWlhS3lnK1krK1Y5U2xZdHpha3FQQi80bW9UTzBSc1ZtZmhVcHU1ZUUxNVBCaTdkTU8zWGFrN3NRcFhuVUdFSW5NNFJ2U0EvV29wdVlqNDB6S1l4MnkyRFhkU3B4WlJYdHpNY3cyK1dFajdwQXFKRjYySEhHdHNQRm11NEhsQWs4TGVmRWpGR2E5SmJNbC9SQXdYOG5iQW1CNHpXa3dTTzZqQ3dkdm52SHBhTk9vY3JlYUF6Y3hqanpObkt6RTNmK3hTMTh2TGhRRGttUlNFY0hBblE2TjQ0U1hsUm9MNjU1TnNoTEdmSHZiOEQrUmpaVUlqUWJ0cG9yMlE0amFRY01hZWtQa0R5SWV3SUdRYmdTRGJXNHdya3ZMbTB6aVBMWVpSL1JYQjVCb0Z6M1lLRFdPRFRpRDZWUTU0aWhhWXMvQmxJbkl1S0JKeGZESmJxUjA1c21iOUo2UkpFajloaERCaVBXWHhsaEZNY05GUUU3YWVSb0l2aGdibWVScThzcjFVS01WQWkiLCJtYWMiOiIyZDE4YzQ4NjI3ODZjMDU4NDJjNTA4YWYwMGUyOWJmZGVjMDA2Mzc1ZDhlMGM0ZTY0MjJjZDMxZTUzYWZmYzMxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:31 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InlDbGhhb3JsMTZmeHRKWkhjOHZXVmc9PSIsInZhbHVlIjoiR05ENkFjNmtzUmZRSnR4UFhIcVNRZnRKOFpIWFhmYnRST3RoenNVbDAzeFZ0NWZoUmY1QmFNU2hnQ2hDMU5mVDlZeE5STlJjTW9zK2ZoUnRVKy8wSHBnVU5vUW04cVR4L042dmt4QWdyczd3WTd5ZXRUSTBlbitLTSthR3Y1UTVnTkl1UVRKSk1pOXJMclVxOGxuVXpIaHVIQUZxMnpwVi9LN3VVeHMrREZFWmx3SHhQWTZIakliOTFMUHFSbnJ0MW1HaFcydUNYTzNYWUQzZFlORHFnQ245Y1NPQUJEUUorRUtQVFZYVjQzSHZzWWRoSEEzRjZxc1pjSExnZnNaWG1YOW13OWdQbjIrVDJXeG5yM25ZRmdXNHFNaDE1RytIZWp0aUlzaDV4d21pNDIzRFRVQzUxbjRsU0xudmk3RmpPVTd5cE4vVE4ydnhOTi9PY0M3WFpYQTBjTjBhVkVBaXkxM2hpQVBnK25rSE9zVkZCeXlybjZjalYvNmxXL2NQaklQVmZUUTRCbU9RKzlUVEZaV3JoVVhlc0g3MDZhTmdoMG5MZk1Oa2dEaUZHL04wMi8vVm5vNTZrKzk3dzFPQS92bWk2YzJnT2FLQjhIbjVnenFYNEpJeWc4Y0tRYnRmNVBxNjdOQ1hwNnVKbUhHWTRZUlpRZGhYeTA0b01MRC8iLCJtYWMiOiIxNWIzYjc3YjRkNjU0NjE4Nzk1ZmRlNWJiZTI0ZDllNjU3ZjM4ZjIwODc5NDdjN2FjMjU0YjlhMjkwZjgyYmUwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:31 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImJ6RTY3RGpJRzJ0dkFQS2pKK3lIOXc9PSIsInZhbHVlIjoiRkRaeVBDQ0VFSEozL2VLS1Q2SmJsNm4xZDBSbnZzOTVrWE1tcTdIdjZqUTRLNDhNai95SXlOMGI2NFFMdnp2eGduaGFMSFQzNnlIemtGbHZ4RloxT24vQ2FueEo4bW5pbFBBbi9VUHEyb1dpMWhRWitsRlBrankrQWlhS3lnK1krK1Y5U2xZdHpha3FQQi80bW9UTzBSc1ZtZmhVcHU1ZUUxNVBCaTdkTU8zWGFrN3NRcFhuVUdFSW5NNFJ2U0EvV29wdVlqNDB6S1l4MnkyRFhkU3B4WlJYdHpNY3cyK1dFajdwQXFKRjYySEhHdHNQRm11NEhsQWs4TGVmRWpGR2E5SmJNbC9SQXdYOG5iQW1CNHpXa3dTTzZqQ3dkdm52SHBhTk9vY3JlYUF6Y3hqanpObkt6RTNmK3hTMTh2TGhRRGttUlNFY0hBblE2TjQ0U1hsUm9MNjU1TnNoTEdmSHZiOEQrUmpaVUlqUWJ0cG9yMlE0amFRY01hZWtQa0R5SWV3SUdRYmdTRGJXNHdya3ZMbTB6aVBMWVpSL1JYQjVCb0Z6M1lLRFdPRFRpRDZWUTU0aWhhWXMvQmxJbkl1S0JKeGZESmJxUjA1c21iOUo2UkpFajloaERCaVBXWHhsaEZNY05GUUU3YWVSb0l2aGdibWVScThzcjFVS01WQWkiLCJtYWMiOiIyZDE4YzQ4NjI3ODZjMDU4NDJjNTA4YWYwMGUyOWJmZGVjMDA2Mzc1ZDhlMGM0ZTY0MjJjZDMxZTUzYWZmYzMxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InlDbGhhb3JsMTZmeHRKWkhjOHZXVmc9PSIsInZhbHVlIjoiR05ENkFjNmtzUmZRSnR4UFhIcVNRZnRKOFpIWFhmYnRST3RoenNVbDAzeFZ0NWZoUmY1QmFNU2hnQ2hDMU5mVDlZeE5STlJjTW9zK2ZoUnRVKy8wSHBnVU5vUW04cVR4L042dmt4QWdyczd3WTd5ZXRUSTBlbitLTSthR3Y1UTVnTkl1UVRKSk1pOXJMclVxOGxuVXpIaHVIQUZxMnpwVi9LN3VVeHMrREZFWmx3SHhQWTZIakliOTFMUHFSbnJ0MW1HaFcydUNYTzNYWUQzZFlORHFnQ245Y1NPQUJEUUorRUtQVFZYVjQzSHZzWWRoSEEzRjZxc1pjSExnZnNaWG1YOW13OWdQbjIrVDJXeG5yM25ZRmdXNHFNaDE1RytIZWp0aUlzaDV4d21pNDIzRFRVQzUxbjRsU0xudmk3RmpPVTd5cE4vVE4ydnhOTi9PY0M3WFpYQTBjTjBhVkVBaXkxM2hpQVBnK25rSE9zVkZCeXlybjZjalYvNmxXL2NQaklQVmZUUTRCbU9RKzlUVEZaV3JoVVhlc0g3MDZhTmdoMG5MZk1Oa2dEaUZHL04wMi8vVm5vNTZrKzk3dzFPQS92bWk2YzJnT2FLQjhIbjVnenFYNEpJeWc4Y0tRYnRmNVBxNjdOQ1hwNnVKbUhHWTRZUlpRZGhYeTA0b01MRC8iLCJtYWMiOiIxNWIzYjc3YjRkNjU0NjE4Nzk1ZmRlNWJiZTI0ZDllNjU3ZjM4ZjIwODc5NDdjN2FjMjU0YjlhMjkwZjgyYmUwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1905820469\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-795647111 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W7mSquJVTThw4XkfNpf0IVTgW7iBPDYpFH3oQHcY</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>23</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-795647111\", {\"maxDepth\":0})</script>\n"}}