{"__meta": {"id": "Xbe07a4c05d0acc4784c4a0f2f8db66b7", "datetime": "2025-06-27 02:34:33", "utime": **********.355588, "method": "POST", "uri": "/payment-voucher/confirm", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.529163, "end": **********.355606, "duration": 0.8264431953430176, "duration_str": "826ms", "measures": [{"label": "Booting", "start": **********.529163, "relative_start": 0, "end": **********.852917, "relative_end": **********.852917, "duration": 0.32375407218933105, "duration_str": "324ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.852926, "relative_start": 0.3237631320953369, "end": **********.355608, "relative_end": 1.9073486328125e-06, "duration": 0.5026819705963135, "duration_str": "503ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45898872, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST payment-voucher/confirm", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@confirmVoucher", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.confirm", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=151\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:151-179</a>"}, "queries": {"nb_statements": 11, "nb_failed_statements": 0, "accumulated_duration": 0.43437, "accumulated_duration_str": "434ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.880546, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 0.555}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.890494, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0.555, "width_percent": 0.101}, {"sql": "update `voucher_payments` set `status` = 'accepted', `approved_at` = '2025-06-27 02:34:32', `voucher_payments`.`updated_at` = '2025-06-27 02:34:32' where `id` = '22'", "type": "query", "params": [], "bindings": ["accepted", "2025-06-27 02:34:32", "2025-06-27 02:34:32", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 153}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.892988, "duration": 0.*****************, "duration_str": "153ms", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:153", "source": "app/Http/Controllers/PaymentVoucherController.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=153", "ajax": false, "filename": "PaymentVoucherController.php", "line": "153"}, "connection": "kdmkjkqknb", "start_percent": 0.656, "width_percent": 35.168}, {"sql": "select * from `voucher_payments` where `id` = '22' limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 159}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0483449, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:159", "source": "app/Http/Controllers/PaymentVoucherController.php:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=159", "ajax": false, "filename": "PaymentVoucherController.php", "line": "159"}, "connection": "kdmkjkqknb", "start_percent": 35.824, "width_percent": 0.129}, {"sql": "select * from `users` where `users`.`id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.052179, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:159", "source": "app/Http/Controllers/PaymentVoucherController.php:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=159", "ajax": false, "filename": "PaymentVoucherController.php", "line": "159"}, "connection": "kdmkjkqknb", "start_percent": 35.953, "width_percent": 0.062}, {"sql": "select * from `users` where `users`.`id` in (22)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 159}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0536048, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:159", "source": "app/Http/Controllers/PaymentVoucherController.php:159", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=159", "ajax": false, "filename": "PaymentVoucherController.php", "line": "159"}, "connection": "kdmkjkqknb", "start_percent": 36.015, "width_percent": 0.051}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 289}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.055748, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:289", "source": "app/Services/FinancialRecordService.php:289", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=289", "ajax": false, "filename": "FinancialRecordService.php", "line": "289"}, "connection": "kdmkjkqknb", "start_percent": 36.066, "width_percent": 0.083}, {"sql": "select * from `financial_records` where `shift_id` = 48 and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["48"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 295}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.0576081, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:295", "source": "app/Services/FinancialRecordService.php:295", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=295", "ajax": false, "filename": "FinancialRecordService.php", "line": "295"}, "connection": "kdmkjkqknb", "start_percent": 36.149, "width_percent": 0.078}, {"sql": "select * from `financial_records` where (`id` = 48) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["48"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 306}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.059224, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:306", "source": "app/Services/FinancialRecordService.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=306", "ajax": false, "filename": "FinancialRecordService.php", "line": "306"}, "connection": "kdmkjkqknb", "start_percent": 36.227, "width_percent": 0.044}, {"sql": "update `financial_records` set `total_cash` = 1039.44, `financial_records`.`updated_at` = '2025-06-27 02:34:33' where `id` = 48", "type": "query", "params": [], "bindings": ["1039.44", "2025-06-27 02:34:33", "48"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Services/FinancialRecordService.php", "file": "C:\\laragon\\www\\erpq24\\app\\Services\\FinancialRecordService.php", "line": 306}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 164}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.060673, "duration": 0.*****************, "duration_str": "182ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordService.php:306", "source": "app/Services/FinancialRecordService.php:306", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FServices%2FFinancialRecordService.php&line=306", "ajax": false, "filename": "FinancialRecordService.php", "line": "306"}, "connection": "kdmkjkqknb", "start_percent": 36.271, "width_percent": 41.801}, {"sql": "update `voucher_payments` set `status` = 'accepted', `voucher_payments`.`updated_at` = '2025-06-27 02:34:33' where `id` = '22'", "type": "query", "params": [], "bindings": ["accepted", "2025-06-27 02:34:33", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 170}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2471151, "duration": 0.09525, "duration_str": "95.25ms", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:170", "source": "app/Http/Controllers/PaymentVoucherController.php:170", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=170", "ajax": false, "filename": "PaymentVoucherController.php", "line": "170"}, "connection": "kdmkjkqknb", "start_percent": 78.072, "width_percent": 21.928}]}, "models": {"data": {"App\\Models\\User": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\FinancialRecord": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FFinancialRecord.php&line=1", "ajax": false, "filename": "FinancialRecord.php", "line": "?"}}, "App\\Models\\PaymentVoucher": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FPaymentVoucher.php&line=1", "ajax": false, "filename": "PaymentVoucher.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}}, "count": 7, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/22\"\n]", "success": "تمت الموافقه علي سند الصرف بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/payment-voucher/confirm", "status_code": "<pre class=sf-dump id=sf-dump-192110509 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-192110509\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-781935887 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-781935887\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1407432831 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1407432831\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-891123196 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">53</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IktGdDZneHhub1FsZ1lZR2VIWERNY2c9PSIsInZhbHVlIjoibEdKdE1ZcVZzK3JEaEpqWmtTNWpzNWsrZzcySXVvc1lkOVBHMVdidGQ5clF3em9IT0pQanRWdzFhbmYyd1hQdXZjSDVFVVI1WjFvVGhZa0g4cGRHNzdOZ3RJcENpQ09CeFhiNVNYeEV0dnZ2bTJWbXdvdlhIdUdKZmtOc3RFU0JsbVVNN3RFcGp4UjZZdlFiWm9ySytTam9KT2h1T3Ntd1pZWUs2Q2Ewelh3cmRDQnNwRWxoVkc1UzZTV3hiL3c2UWREQ1lOaEVuZlpvTmRkTVpJd25RK1g3Q1NKVEFjU1paQmZ3WTdDcWplcFdHbWNBV1hDbXpyTy9ud2lXdW1aNVhFdlE5ZDYzWU5rZ2JVMlUyTlM4VW5WN3FkVVpiT2JsalBzU2RtMXhKeSsyRmo4UDluSk5PRkx6N2s4a3QvMUVZNnZpVUxHcVhoYVV5UDBJclZGVndMcllEYjZsNWNrVWduOEZlSGNmbHpESXN6ZXBBWXEzQmkwWmdONzR3U0xncko5QmI4VTkyVHF3TjZRMGdzcUFXZS82ZmY5cldRdU5wWnQzS0xldDJZVGY1aW1tREJnQjVJRWxManU1NktORnNMTTlxdXNPazVSOVRXVmVUL1ExM2J4cGxTRFNNcnpBV1UzTWp1dW8wQXVyUDNKWVJxS1dyMnpiWENLTWwvSHciLCJtYWMiOiJkOThkOGIyNmQ5MzIxMTMzMjRhMTlkYmMzNTU5ZWI2YWEzMDM1ZWJiYzIyMjUzOTRmZTBiM2QyYmRmYjA4M2Y4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ikt5dDdUUG5MaXdJaGFHb2Z5OXhhbUE9PSIsInZhbHVlIjoiVmJjVFE4MVdCaklDQkE2Qmg2S0JIUkFHVHM1VFBzQkxvcytLWkZrZ0svcWtZWkwrSi9Ta3g3MjI0YUxvYzNFdk11SjBhZmF3ZjRGTzROK1NwcE1VL04vRzZlaVltbzFWWFRZczA0dElBTDgxdGFodWZ2SUxDVndhVW9PTlV4aHJrRksyUjlPbFluVkNNd2RDWFVTK2h2U0xjK2VTdU91TEhQTEdpbTRiOU5mOEh4dEpUNlZFMUlRU2FnbUxwNlhNTDBUL0k2SnZweEtVTHUvbEw5QU1iOVRBbXNKMFl1dm9RZUdLcGFQbVp5dExzSUFmc1BIT3FZNE81TnNvRlZSZDVhaEpJSEtKOTRyRG91aTZad1YxUFpYcTd3ZWtwRjdWcTdobFpyYXdhMDEzWlFVU3psUFl3ZTE5MWFJTVdodis3ZEZOQ2ZiM2xEZ2lFN1JyNS82REV4VFRLelUrUFpDTWhDaUpyMWdObFIxK3k0MWRhUlFzbkFuTHVXcm5sSFNmeGcvVVk4SW4rQmVBSUdjWjRNaTJMMy83emM5bUlRb3M2NTZyQk1BS2V3a2MvYUlOcFBrbE9BTVlSb3lXTEhranJXZDZiYklTRHBralhpTTk2dDhybjM4emt0VkV6eStFYWxWcWVoQXBnUFE3MVRjZ1F5Y2JMaUhVZ1JrZThMUGkiLCJtYWMiOiIwZTdmN2E2OTA5MmYzMTQ1YjU4MGQ0NmI3YWI1OWE1YjU4MGNjMGNiNjkyNWM5Zjc3ODE1YzU0ODdjOTM4ZjIxIiwidGFnIjoiIn0%3D; _clsk=1wa64lz%7C1750991671494%7C44%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-891123196\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-9841373 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9841373\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1893015401 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjRQMURQbG5zZUxXcHJubUQxSU1lOFE9PSIsInZhbHVlIjoiSjM1TUhra3BuYUV2SkZDZVp5R3owbG1RUDVNamo5ZnYvbmZsd0VxZnVZZGlSK21kUXBsS3NUS0dBQnhsMFhUODVWYVRvWEd5S1VHWEtDUmVkd1hybjhScWtGS2pndkpEdzJsSlpzVis0WWFyVFpuUU9WTWlJZXBSTW5IQUE1aHRzaWtwd1FhaysyNTVDNWxIUG9sRnF4TU84Qk90T3d1WGE2eXlvZW9SL094SmR1bW1NbG5HZ0dINHRaR0cvZTc5TDlDc2dEMHhpSW9xZmpnd25TVUZ5cXM5dlM0RkorWTArRFUxUVZpNlRlODZGUkZaOTY2Y3FVTWgrNVJ6RDNHMDdvSHhLQ1hhQ2ZLZ1NuTFRkOTVQRS9qRytpbHc5d1hFdmZKaE1IV2hrSTduUVp5VkF2d2RmN1haNTNzYVlJMzkyM0xiLzkzbHhZUFhBNit0dzZxVFFVa2ZCaXpETDFSaTFMQUZubVRZSUNLSThYSGxLOXdwWEN4eURjQ1FHQjQwVThTSkpydGc0NWQrYlliTk9CWjgrYXhIa0dSRWNIUWpmZjNCQm1UYmt5RXJYSHM4KzlOMUZkdWFvcGd2OHlEWm16MTdteXlNbzR0clJRWTIrcG85Y0o4dVluWjRSRFNxWlZCRjh2NTRXbGlnVjVVeUJKa25qdk4wT0toVHBQWGIiLCJtYWMiOiJmYzZkYWI0OGIyMDY0NmY3ZGU4NzU5MzE2ZTNkNjZhYTlkYjBkZTI0Zjg5MTk4ZmYxZjI0YWNhNzJiNDVjNDQyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImI4WEZSUE9yTlJtSzRIWUNqdVdKdHc9PSIsInZhbHVlIjoiZ2svVDB5Z3JVbTE5am9LRG5rYUtBMnZLR3BGK1VsN1dMdjlOaHoreXdZcnZ6Z2E4ajBuUEozazBuYWxoMFRRTEtiNEY0Z1pIai9DR0VPWjIzOVh3ekVBZ1JXd3NCdnRiM1NDQzlsMVZLaGE0Y2J0ckp6dmI5YjFaZTRiQlFMMTh0RmdLRHRkQ1A3NVd2UUhXa0M2RC9pNFlNeGRwb3cvS1hZczEvSTJtaEhuKzhUd3ZmYWJ3SjBwOHV0WlYveGFoUHh1WVhkZ1hCUWZ5Z1lQSVd5RXhxUDFYd1k0Z0hHbVp2OTZiUnJJNDQrSlBiVkY5WXVlcmN1WGdWK3VMdFJkZGhVcWRLTWcwZDFFR0VSNXA3bEJFbGdzajM5YTBDcnJiY0lpd3hXbFlUQlk0TnVvQlhnYlFndUFpd1NIUnhFRDE4QVZ3UlR0M2VEK1ZXVURNaG5SbmFmdXBNVG9GVTBaS0hLOXRvYk1Jd29FbnM2Z3FnVmJOMzJuZ3FRQ2c4QTRmaks1QXd3NlYySk80RFVtY1VBdHFTZXFJUExOb2JMMTEyVnJBZm9CaFc2eFIwZVNOcitiRnV6REtPU1krSlpQU2gxYW5iVVJYRFlxR3hUL29EVjJZN3A3ZW9HTHpQajRLa1ppVkJtQTBNQmZjQ25SQzVzWjVNdnN2cXFpUEJ0bEsiLCJtYWMiOiI1Zjk4YTRkOTMwNmU1ZDE4MzYxZjI0N2YzZmQ4NWU5NzkxZGY2MTQ3NmFmYWY0ZTE3OTA3MzdhMmViYTE3OGNhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjRQMURQbG5zZUxXcHJubUQxSU1lOFE9PSIsInZhbHVlIjoiSjM1TUhra3BuYUV2SkZDZVp5R3owbG1RUDVNamo5ZnYvbmZsd0VxZnVZZGlSK21kUXBsS3NUS0dBQnhsMFhUODVWYVRvWEd5S1VHWEtDUmVkd1hybjhScWtGS2pndkpEdzJsSlpzVis0WWFyVFpuUU9WTWlJZXBSTW5IQUE1aHRzaWtwd1FhaysyNTVDNWxIUG9sRnF4TU84Qk90T3d1WGE2eXlvZW9SL094SmR1bW1NbG5HZ0dINHRaR0cvZTc5TDlDc2dEMHhpSW9xZmpnd25TVUZ5cXM5dlM0RkorWTArRFUxUVZpNlRlODZGUkZaOTY2Y3FVTWgrNVJ6RDNHMDdvSHhLQ1hhQ2ZLZ1NuTFRkOTVQRS9qRytpbHc5d1hFdmZKaE1IV2hrSTduUVp5VkF2d2RmN1haNTNzYVlJMzkyM0xiLzkzbHhZUFhBNit0dzZxVFFVa2ZCaXpETDFSaTFMQUZubVRZSUNLSThYSGxLOXdwWEN4eURjQ1FHQjQwVThTSkpydGc0NWQrYlliTk9CWjgrYXhIa0dSRWNIUWpmZjNCQm1UYmt5RXJYSHM4KzlOMUZkdWFvcGd2OHlEWm16MTdteXlNbzR0clJRWTIrcG85Y0o4dVluWjRSRFNxWlZCRjh2NTRXbGlnVjVVeUJKa25qdk4wT0toVHBQWGIiLCJtYWMiOiJmYzZkYWI0OGIyMDY0NmY3ZGU4NzU5MzE2ZTNkNjZhYTlkYjBkZTI0Zjg5MTk4ZmYxZjI0YWNhNzJiNDVjNDQyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImI4WEZSUE9yTlJtSzRIWUNqdVdKdHc9PSIsInZhbHVlIjoiZ2svVDB5Z3JVbTE5am9LRG5rYUtBMnZLR3BGK1VsN1dMdjlOaHoreXdZcnZ6Z2E4ajBuUEozazBuYWxoMFRRTEtiNEY0Z1pIai9DR0VPWjIzOVh3ekVBZ1JXd3NCdnRiM1NDQzlsMVZLaGE0Y2J0ckp6dmI5YjFaZTRiQlFMMTh0RmdLRHRkQ1A3NVd2UUhXa0M2RC9pNFlNeGRwb3cvS1hZczEvSTJtaEhuKzhUd3ZmYWJ3SjBwOHV0WlYveGFoUHh1WVhkZ1hCUWZ5Z1lQSVd5RXhxUDFYd1k0Z0hHbVp2OTZiUnJJNDQrSlBiVkY5WXVlcmN1WGdWK3VMdFJkZGhVcWRLTWcwZDFFR0VSNXA3bEJFbGdzajM5YTBDcnJiY0lpd3hXbFlUQlk0TnVvQlhnYlFndUFpd1NIUnhFRDE4QVZ3UlR0M2VEK1ZXVURNaG5SbmFmdXBNVG9GVTBaS0hLOXRvYk1Jd29FbnM2Z3FnVmJOMzJuZ3FRQ2c4QTRmaks1QXd3NlYySk80RFVtY1VBdHFTZXFJUExOb2JMMTEyVnJBZm9CaFc2eFIwZVNOcitiRnV6REtPU1krSlpQU2gxYW5iVVJYRFlxR3hUL29EVjJZN3A3ZW9HTHpQajRLa1ppVkJtQTBNQmZjQ25SQzVzWjVNdnN2cXFpUEJ0bEsiLCJtYWMiOiI1Zjk4YTRkOTMwNmU1ZDE4MzYxZjI0N2YzZmQ4NWU5NzkxZGY2MTQ3NmFmYWY0ZTE3OTA3MzdhMmViYTE3OGNhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1893015401\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1291005488 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"32 characters\">&#1578;&#1605;&#1578; &#1575;&#1604;&#1605;&#1608;&#1575;&#1601;&#1602;&#1607; &#1593;&#1604;&#1610; &#1587;&#1606;&#1583; &#1575;&#1604;&#1589;&#1585;&#1601; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291005488\", {\"maxDepth\":0})</script>\n"}}