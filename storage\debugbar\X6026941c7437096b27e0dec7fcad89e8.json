{"__meta": {"id": "X6026941c7437096b27e0dec7fcad89e8", "datetime": "2025-06-27 02:29:29", "utime": **********.278153, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991368.867865, "end": **********.278172, "duration": 0.4103069305419922, "duration_str": "410ms", "measures": [{"label": "Booting", "start": 1750991368.867865, "relative_start": 0, "end": **********.201128, "relative_end": **********.201128, "duration": 0.33326292037963867, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.201136, "relative_start": 0.3332710266113281, "end": **********.278174, "relative_end": 1.9073486328125e-06, "duration": 0.07703781127929688, "duration_str": "77.04ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48198040, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0071200000000000005, "accumulated_duration_str": "7.12ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.2324219, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 25.281}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.241583, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 25.281, "width_percent": 6.32}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.258192, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 31.601, "width_percent": 9.831}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.260132, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 41.433, "width_percent": 6.18}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.265039, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 47.612, "width_percent": 34.691}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.2695, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 82.303, "width_percent": 17.697}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-592026975 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-592026975\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.263926, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-564758445 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-564758445\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1591297520 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1591297520\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-962095348 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-962095348\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1955781257 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlVyNHAzMzhLN3JpYXJHOXNNOVJqalE9PSIsInZhbHVlIjoiZWhYeHYvUjZraEFOVi9Wc2VKbHZGTFA2eWFiMTVLdXlXZ3kzOGFuc1ZuV05GNVg5SWo5eElOQjJjZnk5eklXMGpUVmt2WmlCenpOOXFxNnpXVGxBTDFpVldkcVdzTHpFRUsyNjg1bWkrMlhrTWJka3JhZDlNS2c4dDVYRkFBM2hKZWpHUWwvN1I5ZUx2clJWWmFyQ2hZRkJWclhlS0pNUndCRE45RENralYyMitYODRXN0lrSDN4TWtHMjMxZVBuOXBsSXlmL0ViVS9xMjlSQmZLc2cyT3g4MUhQOHZzR3NEZmdZeFZ4UGhWUm5mK2trbHBGZUErZ21iRFNnRFJ6OUtGWEg2eFBWS3pmaGxndEk0TjJNaVdwOXl2MlgxU0tpOWRMVTJ3dFVHcGtSVWRQRU1lRGdQMzIxblBxQnJuOXhUdHNrQW96cU5ZTVRnQk9lUDVqYncxWHovS1NVbVdnWUw2THY5cWx4WTJyMWttb3QzUnJrRS9oczlQczFPb0cwYTFUbHhiYThHd1Q1RW94a1lxdzJCbjRpcTlRQTlNZHQ1STh6V2JBbUcrZDFXWHhTbTc1OFVSdDY0VnhYSmN5d3BTdkg0dllIcGRTTHJ1SmRDbmdsN0RLZlc1d0JhSDJPb0N6ZGR5M3ljR2pxdXgxM2ZGZE44SG42WjdIV01XM1QiLCJtYWMiOiI5MGJmNjJkNjBmNzI3MzBiN2UzMDNjODkyZWMzNzQxMmM0YTdiOWQzZmFiNzBkNjU1MDY5YTNhMzFhNDFkYjhkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlUxc0VUOHR5ZlRDTlM2bGxyQ2dUSmc9PSIsInZhbHVlIjoiV3ErRHpSYjdNTjdBaStVVUdwYklsazQwcXI3UXhzWHhFM2pqZ1NvSERmTGNOSlpCRS80SDdqKzZvVVdNbHd6WmQyK0VneFFsdUQ1MVA1V1hKbWRmMGtIcHBsRWFHVHlFN09NN1djZk42WjdZc3VOOFJRS2NGNXErM1R0eW1KdXFLazZNc2dwTUpXelBSVFZNWVVxUzZxYURCN2ZBM3B4M2thcmRYSWpKZEJqZ3ZRbHY3ckJvRmx1YS9MWXlVZS85MEo3TnRVd1I5d2ZocWpuYUJtN1ZTekdDUlV4cXNTTmZzS01ndWsvOXphREsycDllY2tKYXVyNGlyZUVCeVpwd3BtblVVQ3JKV0VJT3praWNHTW1TejZIL1E4N1FpdU0zNFZ0eU1tQUh5WVNoeksvTUhNb3BuTmZ3OUhtaXpVNkVqOCtjbGxIZTI5M3RrNkw5QkhlMWFQQU9NK1lIN0QrYklUY0N3RWFDcGI0THluMDRqVlVOUVp3ZEZIbHFSbnIwYjlhbEx1azRpOXU1aHd3d0xsOC9QbVJ3SmhrdEU5WHV2WlVNYlBYSFpHVEFlWGt0SGN5M3VHclZnaGtCNVovRzRTcFgzSEhMdk50WEtGUEsrcmxxeFpFbms5TkJod0RkWVFmY3RIam9kV1Zrb0lDRS9JdmxEQTlJdDdIazRtZHkiLCJtYWMiOiI5YjAyNGMwNmU0MjJmODg0MDUxNDMwZjAxZWZkYmY0OTY2YmNjMTcxMjkxZjkwYjRlNjIzZDFiNjFmM2IxMjIwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1955781257\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2143898029 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2143898029\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-72541843 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:29:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImFza1htK1hsRmpQajcxaU9DY0JBTUE9PSIsInZhbHVlIjoibGY0b0xraUhOMjMzblV1dmRXcGgwUExndFUyNkFabTdPcUVJenIyM2Nwb2VnMUErMGd4d24xM211VUljQkZKUUtPcFhKeUErNy9ZdlpJUVQzQWJ2MDZHbkRhc0ZlZldlM01acFI4b3ZzK2FZdXE4d2hhYVJkc3V3ZWh2Vlp4SHhheHdCZG1pRUNHUUxQMHhkSGFyQjNRb21KZnRWUkFPY3JXZlI4Y2k4RGQyMWhBQ2tPYkVydWNLT2xMV1kwRnhSYnVObjBNVmhCYTRPOFFJV0t5R3FJMVFpTlZJMVF3MVR1b1FRMFBvUkdTVTVEb085L213SVFYb2xoYloxL1ZTdmJZQXhpSXJ2TTA2dGk0alViNXVQZ21MVjFzeStQa2J1WklnN1JuSnRFVk10RnBOMW9DTGZBYXMxQXlkQ08wb0NHQmNyKzZGeTZpQXc1dWJHbzBRVHQrcEdmbTBlMGRUVEtxL0xVek1MVmpTVjc1czVmZlNFczlSKzJJaGJoUzFhN2p3eHZhdFdvaFo0YXcvRTZOUHdVOGFJQnJxQ2VjTkxhb2ZVaGtxT21HLzFYZzFnK0krUUZISFlSZDRsYXpSRkhHYyttaWp4ZHIvQ214SVR2L1hQY05oRFJHNVQzdE5lc1hTbG94aFJDeEZPUkQ4Q2ZDc0JaOTdjUUdlazFrNDIiLCJtYWMiOiJmNzNhZTNlMjc1MzhmYTBlNmU1Mjc2YzJlNmFjZjE3ZDEwYmFjYThjMTM2NTQzMDQ1ZWE3ZGIzMjJmYjVjOTY1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:29:29 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InZVbEhmTEdXV21OQ1piWjZqZ0JLVEE9PSIsInZhbHVlIjoiU3BMZmE5d3V1V09OY1IrVG9kS0tYZk1yMm5DWXc4eVRPbWxnb2tRTC9FTk1UUTRlcGZXMGhWemlhMGlOMVNLY3NmbmZQcWdRNThzSzFHcHBXeExzQlVCSk9WMUhCd28xYVlnUUZwcWJRTm9yWklGQURFTWhaeW93L29SR0pvZ3pPbkptYTkwdWw0Y0JOdzVJaFV5NzYxaUNPWlVidHlKaUExUStteDNBa3hVYksrOEtLcHFiMmxKbXg3dzJHUE14eGpieUx3YkRaNGVKbyt0Y0JMaXRETU50RTBxdWhTbkpsOG5nWlpZNWdHNEh2UVhBaGwrTjF0bDNua0ZqTDViSEFuQi9NTzlaVk9zRjIyZDYzT1BuUmhNUVlVb1hwMDc1MG5majBXTjFNWFJEUUFJekNzRllVc056TE5oWURMNldLazRpQy9MeksrcjFMQnhKSlRlWWpaNTdJaXVDMzJmZ09FYmVaL1VxQ05PS09PaEFpdHFJUVNrUjBkUndmWmFXQXRWbmV2NnpBcUVlZ2o2WkphMmNRUVVjbTJBUHdReDEwWmw3NXZwbm9QZmU4ZWdzQ0xISnZ4RkZZOWhFdUpSS0dwS1pzOVhDR2NtdWJlellWOEdqOEZBSG1KdjI1dFZWWW5yZlBkRWxjdDhiMFoxUmx3dTk5d3RZNUdJdzRjNnAiLCJtYWMiOiI2ZGVhMDU5YmI2YjlhNzM3NDQ2MGM4ZmJlMjllNzRjMGJiODQ4YjM5YjYwNTg1N2RhYzcwM2M1MzMzNzdiZWVkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:29:29 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImFza1htK1hsRmpQajcxaU9DY0JBTUE9PSIsInZhbHVlIjoibGY0b0xraUhOMjMzblV1dmRXcGgwUExndFUyNkFabTdPcUVJenIyM2Nwb2VnMUErMGd4d24xM211VUljQkZKUUtPcFhKeUErNy9ZdlpJUVQzQWJ2MDZHbkRhc0ZlZldlM01acFI4b3ZzK2FZdXE4d2hhYVJkc3V3ZWh2Vlp4SHhheHdCZG1pRUNHUUxQMHhkSGFyQjNRb21KZnRWUkFPY3JXZlI4Y2k4RGQyMWhBQ2tPYkVydWNLT2xMV1kwRnhSYnVObjBNVmhCYTRPOFFJV0t5R3FJMVFpTlZJMVF3MVR1b1FRMFBvUkdTVTVEb085L213SVFYb2xoYloxL1ZTdmJZQXhpSXJ2TTA2dGk0alViNXVQZ21MVjFzeStQa2J1WklnN1JuSnRFVk10RnBOMW9DTGZBYXMxQXlkQ08wb0NHQmNyKzZGeTZpQXc1dWJHbzBRVHQrcEdmbTBlMGRUVEtxL0xVek1MVmpTVjc1czVmZlNFczlSKzJJaGJoUzFhN2p3eHZhdFdvaFo0YXcvRTZOUHdVOGFJQnJxQ2VjTkxhb2ZVaGtxT21HLzFYZzFnK0krUUZISFlSZDRsYXpSRkhHYyttaWp4ZHIvQ214SVR2L1hQY05oRFJHNVQzdE5lc1hTbG94aFJDeEZPUkQ4Q2ZDc0JaOTdjUUdlazFrNDIiLCJtYWMiOiJmNzNhZTNlMjc1MzhmYTBlNmU1Mjc2YzJlNmFjZjE3ZDEwYmFjYThjMTM2NTQzMDQ1ZWE3ZGIzMjJmYjVjOTY1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:29:29 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InZVbEhmTEdXV21OQ1piWjZqZ0JLVEE9PSIsInZhbHVlIjoiU3BMZmE5d3V1V09OY1IrVG9kS0tYZk1yMm5DWXc4eVRPbWxnb2tRTC9FTk1UUTRlcGZXMGhWemlhMGlOMVNLY3NmbmZQcWdRNThzSzFHcHBXeExzQlVCSk9WMUhCd28xYVlnUUZwcWJRTm9yWklGQURFTWhaeW93L29SR0pvZ3pPbkptYTkwdWw0Y0JOdzVJaFV5NzYxaUNPWlVidHlKaUExUStteDNBa3hVYksrOEtLcHFiMmxKbXg3dzJHUE14eGpieUx3YkRaNGVKbyt0Y0JMaXRETU50RTBxdWhTbkpsOG5nWlpZNWdHNEh2UVhBaGwrTjF0bDNua0ZqTDViSEFuQi9NTzlaVk9zRjIyZDYzT1BuUmhNUVlVb1hwMDc1MG5majBXTjFNWFJEUUFJekNzRllVc056TE5oWURMNldLazRpQy9MeksrcjFMQnhKSlRlWWpaNTdJaXVDMzJmZ09FYmVaL1VxQ05PS09PaEFpdHFJUVNrUjBkUndmWmFXQXRWbmV2NnpBcUVlZ2o2WkphMmNRUVVjbTJBUHdReDEwWmw3NXZwbm9QZmU4ZWdzQ0xISnZ4RkZZOWhFdUpSS0dwS1pzOVhDR2NtdWJlellWOEdqOEZBSG1KdjI1dFZWWW5yZlBkRWxjdDhiMFoxUmx3dTk5d3RZNUdJdzRjNnAiLCJtYWMiOiI2ZGVhMDU5YmI2YjlhNzM3NDQ2MGM4ZmJlMjllNzRjMGJiODQ4YjM5YjYwNTg1N2RhYzcwM2M1MzMzNzdiZWVkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:29:29 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-72541843\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-15943166 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-15943166\", {\"maxDepth\":0})</script>\n"}}