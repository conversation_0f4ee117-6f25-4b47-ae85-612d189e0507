{"__meta": {"id": "X2366b67df63977cfb49c8204a10b2c3b", "datetime": "2025-06-27 00:14:50", "utime": **********.61761, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.117817, "end": **********.617628, "duration": 0.49981117248535156, "duration_str": "500ms", "measures": [{"label": "Booting", "start": **********.117817, "relative_start": 0, "end": **********.548557, "relative_end": **********.548557, "duration": 0.4307401180267334, "duration_str": "431ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.548565, "relative_start": 0.43074798583984375, "end": **********.61763, "relative_end": 1.9073486328125e-06, "duration": 0.06906509399414062, "duration_str": "69.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45060480, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0034700000000000004, "accumulated_duration_str": "3.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.578642, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 51.297}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.592466, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 51.297, "width_percent": 13.545}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.600093, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 64.841, "width_percent": 16.427}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.605945, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.268, "width_percent": 18.732}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-691521733 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-691521733\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1433671119 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1433671119\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-300196641 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-300196641\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1004356908 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983282490%7C45%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1OS1BteUZsMno3S0NWM3laQzBLYlE9PSIsInZhbHVlIjoiand1WktFbjMwTk1TeExFdWZhVVljM0tTaDIrbkNVUU9nREtrNENsaU9HRDhoa3hubmlhcUtYU2VQOS9LNGVLaElxN0pTT2ZlMzI4Rm1KbzRDblRvU1lsL1g2L2JZc0k0aXlXV2QwNGJnRUZQOG51SjNnc3BlSGdVT055T2trUmtkUU80cFJvQmpYb1VzNjk5VzBaUE81SERDemV5K2xpa3VPZEFCK29FSm16YmtHWi8xL0c1ZWpLU2g4SUFFU2FleUkrTHFHUkRIY2JtTjZKL2ROMVNDSThCWTJhTlRnenpKd091V3RGbExDK3pJaUJSRDR0WUhUeVRSWG9hc3pTUHloUCs3bDg2RXQ0R244WlRPV21hQ2RPbHFVN2owaXZ6Vm9uNzFiWXozZDd0ZzRUcU5tNFcrVFBIZTlyc3ppalk5eVluamc4cUl0YVZDZWdCY2Z6T3VwKzk0cHJ3cXhmNVJ4TEh4b0ovcWU4bUh4cXFSbVFEM2FwLzllb0pwclVKWGpvYnVFVXRkODRiRXp3V25WeTNXTFN1enh6MmNOcHZobnlFVUVWSWEzTldDWVh6cDVwQkxScmIrKzd0bmIzcXBRcnNFVnR2UjM0ZDc3THJzdXZKN2pwc2ZYSUg5dWI5VWlRUGVuZ1FIN2w2R0pjQkhtdXhuL0lCOEZNc3RIdGgiLCJtYWMiOiIwNjM5YjFkZmRhYjE0MGYxOGUwY2ZkNTc2NjYzZjRlODZiZDY5OTk0OGFiZDQ0YTRlODQ2NWUxZDk5MWRlMTJiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlN4ZStaRkZwcFZtOUZTOXNtSFp4ZGc9PSIsInZhbHVlIjoiYmwvd2Z5UUFYZCtYZWZvSzF4YmhVU1dZMDBPVDFDZnY1MWlVQ3V3cURnQjlPWW9ncmtUajlEVXpPdU93eUY5SzRxTE11TnRnRDM0Y1BvdjVxQXVVQ2VYREs0b3FsUDhJdmUxYTU2ME9nUFlDUFM0eDJCYWZMb2Y0OE5ORHRwUEYrNzZnYVdRQ082cTRyTnZCUjJzaUdIdEdwbUxnN3dwMG5iSWRKdmtVMmdzeFN6eTAzR1VOT25vK0x6d2FYZkVMZHdiN0xYK21USTNDR0trN09ESXhRT1ZNTjZQTFEzNnRyNGhnc3lBNytOR1ZLUnYwU2paWHcyWDBzZDcrZVEzUTJnV1kxQkowZThTZHhZL1VJME9GL2NoS0hyUThxVDh3cVVjVmlJTStVOU42Z083YVgxMUllckhXU3Z1d1R0M2hOUjJ5NnRoMCtQdEV3NWVNTUVZaUJJb2tEdVJYckhIVk5PS1dIMFJubXpFb0c4T0J4MUpZeEJ3MlRkdjhrRFdnazNoNWk3Rnl1TytqVTRQMVJGcTR5ZVlheUIzTzhoZEh3WEdLUkNIaFpsOGVsREdDaGVpTnhrVm5BeGVoOVAyYXlqL3RldmN5OHhmTHUyemdaTUpCck1ldUJqSWIrRXA1Q0d3aTdFanhEZWJXSEJObXhvdThwQUdXTFhSbklYV3UiLCJtYWMiOiJlYjdiNzlmODQyZWNhZDJlZjQ0NDFiNjYwOTYxMGZiMzRlNGI1NWQ4MDlmOTYyMWY0NDU2ZDk3OWZiMjkyZTE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1004356908\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-518428542 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518428542\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-558081264 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdmSStrMnJSRXZKelZGSlo1ZEc2a0E9PSIsInZhbHVlIjoiRmQvN2c2QnBjWVBVd0M1M1E3d1ZQalc2UkwvZ2dSQ1drYzQxOTZYUWl2Z0dXaWZVaE9iZ2JLV2lYazQ2T2dEaUlVRGFpK2hGQjRKbXh2aW52YTVnNmJ3dVhGU1dDd3NBa29LVGpKblVIbE1IMW53UllFbytXTUkyd0UxWUZFWWZ6a2pSUGYrTVZobkR5N2RYcnpwQXdEa0UvZjcybTIrY0MwWUpLTGhFMG5xRzVNVjZaUmdBMGovOTNVZGFLNUx3dG1qTDVtSHhOaVhkZzhrMHBodVBuVVkrUzRUTE9QU250SWIxMGk4THBvVERmREFMQ0Irc3RYS3EzU2Uwa1Fxd2xjUW5td1oyUTJNTHBPZUw2SUdjVUxOUFkyaXp1QWxielJOeHBhM2t1SUtOZ1JhV0MxaEgwQWhGMllkN2YrQWFrQVBJRUlSSVJJbmdnWVlEdjJnamttTUxMNkIxekgzTm9WVld0cWdneUl5eE8zRmg5SzVxazd0Y3ZUZzdjOHNQR2VWbTRSS242MENwV0xFMzN5aW1saHNsZlY5WXVNaHdNZE5yQUY2ck1oNCtvNlFsWkc2WXdkbGxRZE5QdUE1WWNzRlNReUtNem5mSGdnN0tWdzgxVkQ0djdBakpzMk9HTnZsRmh3b0VzTHk2S1loSzh4YTg3OG9aUGxiNWY2TWMiLCJtYWMiOiIzMTBiYTQ0MzI4YWM0NDBiMjllNzg1ZGY3NzJjNTJjMDM2ZDU5Y2UwZDkzMTg3ZmI3ZTZlNmVlYjIxNDZjNGJlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkdJR1dncEN0OWVOMWhUbWhva2dUcnc9PSIsInZhbHVlIjoidXNjUFJxWGtuSkQzRnZCOG5wTUU2dWV5OGZ0UjVKT2ZFMVRycjZGL1htZUtGaTJrSFlGNkJ3WGlhSmhMTUFlODFoQ3lFM0NMNzYyUHBvQjBpRzJNbHBDZ204bEdBMjM0a3ovN0FrYnFyclNiSDRHa2lyL2VzcWx4a3RFYTlrRW51V21jbnEvaHBodVhsaUUyVTQvQThsaWJBeGY2dWVWNmQrNkIxS25MSFN3a202VWdyc3R2N0JJSWxWWkE4K1hpb1JmaDUxRlNPTTNhMnFMVFlCVHhLZVRXNCt5Qjk3NEN6Y2xnd25DT2NQdnlOdU1JRk5GVXVqMW1pckVCRlJ0V09WejhYZ3BYMnA3RTh1ejZqcEFzaXEveThNeFVETEdxTFFlRFFrb2VpWFZSY3JPOTNDaDRQRDB0RjZKdnJqcCtmS096N2xGenVIUy9RRnZ5cUROZEFCM09EL3d2Z0VLa0UzNEVyMGxCZEhyTFQySUZtbFg4VUpCazdYbmsxUGJnR3dGVWNXQTFvSCs1VGJyTVpudnd0UEJYOUpneEJnY1Fid2grQ21zWnFsa2dqTVZ1NFRWTFo1R3AzdndvUWZFU3ZaV3o4dFJVNnViaVNFclRGRGwvMlRnbGlXS3ZaeDBaWDV5VVVqNnVheDhzMktQY1JvOGJ1anlYUm5lZVJGQ1giLCJtYWMiOiJmN2UzMGYwOGIyNTQ1MDQ2ODUxOTUwMjJkM2JkYzNiYmM3ODU0YzY3NzJiNDc2ODE2MmI2ZmVmMGI0MjhlNGUxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdmSStrMnJSRXZKelZGSlo1ZEc2a0E9PSIsInZhbHVlIjoiRmQvN2c2QnBjWVBVd0M1M1E3d1ZQalc2UkwvZ2dSQ1drYzQxOTZYUWl2Z0dXaWZVaE9iZ2JLV2lYazQ2T2dEaUlVRGFpK2hGQjRKbXh2aW52YTVnNmJ3dVhGU1dDd3NBa29LVGpKblVIbE1IMW53UllFbytXTUkyd0UxWUZFWWZ6a2pSUGYrTVZobkR5N2RYcnpwQXdEa0UvZjcybTIrY0MwWUpLTGhFMG5xRzVNVjZaUmdBMGovOTNVZGFLNUx3dG1qTDVtSHhOaVhkZzhrMHBodVBuVVkrUzRUTE9QU250SWIxMGk4THBvVERmREFMQ0Irc3RYS3EzU2Uwa1Fxd2xjUW5td1oyUTJNTHBPZUw2SUdjVUxOUFkyaXp1QWxielJOeHBhM2t1SUtOZ1JhV0MxaEgwQWhGMllkN2YrQWFrQVBJRUlSSVJJbmdnWVlEdjJnamttTUxMNkIxekgzTm9WVld0cWdneUl5eE8zRmg5SzVxazd0Y3ZUZzdjOHNQR2VWbTRSS242MENwV0xFMzN5aW1saHNsZlY5WXVNaHdNZE5yQUY2ck1oNCtvNlFsWkc2WXdkbGxRZE5QdUE1WWNzRlNReUtNem5mSGdnN0tWdzgxVkQ0djdBakpzMk9HTnZsRmh3b0VzTHk2S1loSzh4YTg3OG9aUGxiNWY2TWMiLCJtYWMiOiIzMTBiYTQ0MzI4YWM0NDBiMjllNzg1ZGY3NzJjNTJjMDM2ZDU5Y2UwZDkzMTg3ZmI3ZTZlNmVlYjIxNDZjNGJlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkdJR1dncEN0OWVOMWhUbWhva2dUcnc9PSIsInZhbHVlIjoidXNjUFJxWGtuSkQzRnZCOG5wTUU2dWV5OGZ0UjVKT2ZFMVRycjZGL1htZUtGaTJrSFlGNkJ3WGlhSmhMTUFlODFoQ3lFM0NMNzYyUHBvQjBpRzJNbHBDZ204bEdBMjM0a3ovN0FrYnFyclNiSDRHa2lyL2VzcWx4a3RFYTlrRW51V21jbnEvaHBodVhsaUUyVTQvQThsaWJBeGY2dWVWNmQrNkIxS25MSFN3a202VWdyc3R2N0JJSWxWWkE4K1hpb1JmaDUxRlNPTTNhMnFMVFlCVHhLZVRXNCt5Qjk3NEN6Y2xnd25DT2NQdnlOdU1JRk5GVXVqMW1pckVCRlJ0V09WejhYZ3BYMnA3RTh1ejZqcEFzaXEveThNeFVETEdxTFFlRFFrb2VpWFZSY3JPOTNDaDRQRDB0RjZKdnJqcCtmS096N2xGenVIUy9RRnZ5cUROZEFCM09EL3d2Z0VLa0UzNEVyMGxCZEhyTFQySUZtbFg4VUpCazdYbmsxUGJnR3dGVWNXQTFvSCs1VGJyTVpudnd0UEJYOUpneEJnY1Fid2grQ21zWnFsa2dqTVZ1NFRWTFo1R3AzdndvUWZFU3ZaV3o4dFJVNnViaVNFclRGRGwvMlRnbGlXS3ZaeDBaWDV5VVVqNnVheDhzMktQY1JvOGJ1anlYUm5lZVJGQ1giLCJtYWMiOiJmN2UzMGYwOGIyNTQ1MDQ2ODUxOTUwMjJkM2JkYzNiYmM3ODU0YzY3NzJiNDc2ODE2MmI2ZmVmMGI0MjhlNGUxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-558081264\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1730078530 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1730078530\", {\"maxDepth\":0})</script>\n"}}