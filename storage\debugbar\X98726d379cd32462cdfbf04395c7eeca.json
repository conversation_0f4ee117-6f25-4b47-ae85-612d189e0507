{"__meta": {"id": "X98726d379cd32462cdfbf04395c7eeca", "datetime": "2025-06-27 02:25:39", "utime": **********.605426, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.180411, "end": **********.60544, "duration": 0.42502880096435547, "duration_str": "425ms", "measures": [{"label": "Booting", "start": **********.180411, "relative_start": 0, "end": **********.55361, "relative_end": **********.55361, "duration": 0.3731989860534668, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.553619, "relative_start": 0.37320780754089355, "end": **********.605442, "relative_end": 2.1457672119140625e-06, "duration": 0.05182313919067383, "duration_str": "51.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45394752, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00279, "accumulated_duration_str": "2.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.583813, "duration": 0.0019199999999999998, "duration_str": "1.92ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.817}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.594934, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.817, "width_percent": 20.072}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.597925, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 88.889, "width_percent": 11.111}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-160025102 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-160025102\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2016355436 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2016355436\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-481257874 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-481257874\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-241954106 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991135471%7C25%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IldwVisvVEZRTU1BQ0tXREVGWmNLOWc9PSIsInZhbHVlIjoiZkVxelJEaUN6T3dOM1NyUWc0YXdHUmFUdzF4NWdTNmRvck5HK29wQk52Z1grT3BXcmpaZU5ETTBDWXl4dXFOS3NhWmFtWlI5clU5ZVhMTXNqbFNQMXBPMkF3U1NyRTZHcmVlVTR5NCtYUlBjRTdpZFZISjhUalhBWktsNHIyWGN6SVArWkJOOHZnNXlsbHQ0elpDeHpNeENBTG1ZMUFWSlQ0VUpsRitnc005RWxwVVlvSEF4MDg4Y2VNTWxWMzFqZWx5WEhFVXF3eDUvV2RlNFBURmQ4dE95R2pVcjNPQnhkdnEvdEpHbHkxWjkxVE9SYlg3UmV3cGdUTkJsbzlGRWRQZHhwbzNRakE0aVhwcnVxajhpZnMvQ2V1MUllOGdiTk5xeWpjQkxQQnRzRW9hRlFlY3pNaVpYM2QzbDFFc0s1eFJzaXhQRkZPdHBmbUZRazdRQnYwd1hXVGJMS0JUWVpXMHlUMlBReUMzYWRpd1c0eW9tU1VlZ1VVZTBBOGxaZ2pTVU1LNDEzUE9CQUZVWTkxY2JxNjlNWlhBMDJiK0kwK3BxZlk5Q2NTN2VlQ0hucU15TVNwTDRRNzhTc2QzL2ptckpqUVIyQUxJNjEzMzY0UDNFelpXZnZsZ2NxanJPZStWUGNWM3JpbUgrc21KTnUyVWdwbUVWSEFoS1FyUU0iLCJtYWMiOiJjMWYyYTRhOThkMzcyNzkzMjE5ZWQ1ZGJlODhjZTkwZGQ2NmYxNDUwYzQ4MTU3YmJhYjJmNTIyYWNjMmEwODk0IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InFsQTdKdmw1UUIzL1J1YmowbzQvNFE9PSIsInZhbHVlIjoiUHVwcjIxQ3VWRTRQMlhhZDd3SXhvMTg5MTlSSFhydk9icEljM2E1MSszQ1VCaDdzRFFrdUtreUU5VFBzMkJCNnZhK2xUVjFodmo0WjhWbUxZWU9TYkJ5WWxIdDRGVmErbER5WStPLzQ4bXhwRFZpK2swaUkxVUFRSlA5Qm9xRzg0YmlYdTA0RmEzQUtyZXR1WGpTL1dEQ0lzMzlmcnlwSExHRnlQdmFXQjJvQWRYSStCczYzYUZSNjEyYzFSVWxNdVVHbkM4T2o4cFpWT0x1RE1TcVExTDJvWUxuVVEyWllKeUtpMU5EYVEySXFHdVA5alZteWM2bzQ3YUR5NVVSdVk0SEJ0SzBTR0xDeDJ3c1FXcE54NnRXY3VmVTlXZmZjck9PWS91ZktHVlc0WVVKc2J2c1dYcDZ4TjVwcUN6U3N5QVNqcStheDdQaE5yQTQyYkgxemk0RTRsVDRqdFdqTkJzWE9ITkYzQ081OWljeGFHOWFJQ1ZDQjJyZ01uMkJQZHU4YUtGSlY1SmlRMDl3bHgvenN1azVrT2huRnNlZWFKeDdIWHNpWWFiVWxJREt0TDRnWjVuU0FicWF4RGJmS0FSemIySmhUY0hOWmRsa0NDVTA1bmpEUy9zb2xIeWIrTGhLRFBzV0xVcllEdWhnbjQ5amVVTjFPZmhDQmxGd3YiLCJtYWMiOiI1YjI3YzMyZDFmOWRhMzE3OTI2NWFjNWVlNzZkNTc2NGE2NzA1YjBiZWI4ZjgwYTI3NzhmNmM5MWJlNDc4ZjMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-241954106\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1309593198 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1309593198\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImZFN3RLRWtQalU4VDdNQUJBNXgrNlE9PSIsInZhbHVlIjoieFg4b2VRRWVRclJHaGtFc0dDNFAwbHRmTVpFaGdZQUVPdzdjT0RNU08xZytUUXFjVE85S2daU2RBSXpPKzNTSlNCK3NUZmRqRGllWnBJLzI4TGN3SWJMVDBucTU3dzduTUd2TlJKYjRvRzZabDhuWUpKQWhKaWlsOWNLL1dselh4UEw0UFFYNlN0M2oySDNjV1Q1cE9tKzF0R2pLS2dNaUNIQ1Q0dC9zcUNnRlAzS0praGhRVjF5a0lrZzR0MWt2RU1ZS2V2OTJYcWJqeGl0a0YzZEFKVzVZZmMxVCsxRTM2V3JXWmoxbnpQRkZweEh0YTdMWHZjeUVrTVFVTldKYXNiMXUxMXhiSXcrWHBkeFVxZlZUMnFld3hwcjUvL0M3T1V5OEZZRWU5UnJpZEtjYTNNNjRnVDJrNzZVY3lxVitySWttNGJPdXhnOXZ6WUVMeTZiZUNRZzQ0aVJUMFdhZFZ1OVpOdVVpNkZydVhKb3hCWk5ibDJvQ2VWRkI0cVlpUWFGcWNSRDNOVFFjeUoyYTNmTE1oWStEZ1czc1dBYy9VbW9ZNmRsVXpUZmpMTzlBeno5QitLaVpPL2s0bDhKSSt4bVgvdUFLWUdicG45eWh2U0xUbjVKd0JiK1poWnNEME1zelFVK1dTVzJQbGhtUmNCd1Rhb3Fid1Y2eU02QVYiLCJtYWMiOiI2Yjg4ZmQ3MzI2YWM5MzU2MzM5ZWE1NmQ5NTdhMmMwZjc1MTU1MzI5ZGRkNjY3ZDM5ODNhMWU1ZDk2MWRlZTE1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkJZSUltcXBQSHp5enI2MTZ6bEwvQnc9PSIsInZhbHVlIjoiSUZVczlxZi9rdTZxeEtaYW9zam02VTlWb3lVdHpQQXM0MUxOT3Z3SXNSRkd1dEtzWjJpWW5QVjhqSjc2eGR4RG45MmtwVlV6Z0pPRmg4eWZKbGJYNDh6NlU4ZThQalJRQ1VlTVUyRENyWk9sUFUxeXpDdUNPVThhUjRabmRPcDRtWlhNV09CVFA0YzlKS1FBQnQrR1BTdVNXaElOa3djVEVUV1lUVG9UeWU5dEpmSEZiSVZkRi9rUENYMmNHbUE4RkZnY3p2VEhBTGlZdnJTdHFuVXNOY3JUa21UUUl3TWY0N1J6d0l3ZXQ3QndueDM1aHdETVZ6MU9ka2hISzFDd0ZDUk1keUpzbnhRdFB5dHN0eHVGQWEveFFXalkvM20rMlg4T0dQM04wZThYemdnUFNPTi9ua3NTRHVxSlBRaWVpMmFIdG9IZzVlcy9IaHoyUGJjN3FiUithL0tIZC9sbzkvcFNyWWRBeTdmTktEdHVPaW1JcHljNnBKbG8xSkM5MUh6aWpVd1Z4RGJaQkROTnBseWlkQW1aMzJObmZpaTFwdlJ3VWh1OFFtNFJhRTZxOHRFaDA0anpIZ2VCeHdrREF0clNCcXBzK0xtZ3lESS9tYzR1ZkNXMTU2Y051ZzVaOVVNV3BPeEI1ZW00ajVjOFVPUzFaSGpkaVNFVEhUUzYiLCJtYWMiOiI2ZTQxOWVmZjAxNGFlMzM5MTVkYzJjYTA0NGYwYzhlM2VmMDRiYTM1MzJmYWJmNTRmZjNkMDlkNDFmMzA5YTA5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImZFN3RLRWtQalU4VDdNQUJBNXgrNlE9PSIsInZhbHVlIjoieFg4b2VRRWVRclJHaGtFc0dDNFAwbHRmTVpFaGdZQUVPdzdjT0RNU08xZytUUXFjVE85S2daU2RBSXpPKzNTSlNCK3NUZmRqRGllWnBJLzI4TGN3SWJMVDBucTU3dzduTUd2TlJKYjRvRzZabDhuWUpKQWhKaWlsOWNLL1dselh4UEw0UFFYNlN0M2oySDNjV1Q1cE9tKzF0R2pLS2dNaUNIQ1Q0dC9zcUNnRlAzS0praGhRVjF5a0lrZzR0MWt2RU1ZS2V2OTJYcWJqeGl0a0YzZEFKVzVZZmMxVCsxRTM2V3JXWmoxbnpQRkZweEh0YTdMWHZjeUVrTVFVTldKYXNiMXUxMXhiSXcrWHBkeFVxZlZUMnFld3hwcjUvL0M3T1V5OEZZRWU5UnJpZEtjYTNNNjRnVDJrNzZVY3lxVitySWttNGJPdXhnOXZ6WUVMeTZiZUNRZzQ0aVJUMFdhZFZ1OVpOdVVpNkZydVhKb3hCWk5ibDJvQ2VWRkI0cVlpUWFGcWNSRDNOVFFjeUoyYTNmTE1oWStEZ1czc1dBYy9VbW9ZNmRsVXpUZmpMTzlBeno5QitLaVpPL2s0bDhKSSt4bVgvdUFLWUdicG45eWh2U0xUbjVKd0JiK1poWnNEME1zelFVK1dTVzJQbGhtUmNCd1Rhb3Fid1Y2eU02QVYiLCJtYWMiOiI2Yjg4ZmQ3MzI2YWM5MzU2MzM5ZWE1NmQ5NTdhMmMwZjc1MTU1MzI5ZGRkNjY3ZDM5ODNhMWU1ZDk2MWRlZTE1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkJZSUltcXBQSHp5enI2MTZ6bEwvQnc9PSIsInZhbHVlIjoiSUZVczlxZi9rdTZxeEtaYW9zam02VTlWb3lVdHpQQXM0MUxOT3Z3SXNSRkd1dEtzWjJpWW5QVjhqSjc2eGR4RG45MmtwVlV6Z0pPRmg4eWZKbGJYNDh6NlU4ZThQalJRQ1VlTVUyRENyWk9sUFUxeXpDdUNPVThhUjRabmRPcDRtWlhNV09CVFA0YzlKS1FBQnQrR1BTdVNXaElOa3djVEVUV1lUVG9UeWU5dEpmSEZiSVZkRi9rUENYMmNHbUE4RkZnY3p2VEhBTGlZdnJTdHFuVXNOY3JUa21UUUl3TWY0N1J6d0l3ZXQ3QndueDM1aHdETVZ6MU9ka2hISzFDd0ZDUk1keUpzbnhRdFB5dHN0eHVGQWEveFFXalkvM20rMlg4T0dQM04wZThYemdnUFNPTi9ua3NTRHVxSlBRaWVpMmFIdG9IZzVlcy9IaHoyUGJjN3FiUithL0tIZC9sbzkvcFNyWWRBeTdmTktEdHVPaW1JcHljNnBKbG8xSkM5MUh6aWpVd1Z4RGJaQkROTnBseWlkQW1aMzJObmZpaTFwdlJ3VWh1OFFtNFJhRTZxOHRFaDA0anpIZ2VCeHdrREF0clNCcXBzK0xtZ3lESS9tYzR1ZkNXMTU2Y051ZzVaOVVNV3BPeEI1ZW00ajVjOFVPUzFaSGpkaVNFVEhUUzYiLCJtYWMiOiI2ZTQxOWVmZjAxNGFlMzM5MTVkYzJjYTA0NGYwYzhlM2VmMDRiYTM1MzJmYWJmNTRmZjNkMDlkNDFmMzA5YTA5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}