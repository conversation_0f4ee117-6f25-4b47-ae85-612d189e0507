{"__meta": {"id": "X1714309cfea1d5696e9f7489a46808c6", "datetime": "2025-06-27 00:43:13", "utime": **********.287148, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750984992.830369, "end": **********.28718, "duration": 0.45681095123291016, "duration_str": "457ms", "measures": [{"label": "Booting", "start": 1750984992.830369, "relative_start": 0, "end": **********.208183, "relative_end": **********.208183, "duration": 0.37781405448913574, "duration_str": "378ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.208194, "relative_start": 0.3778250217437744, "end": **********.287184, "relative_end": 4.0531158447265625e-06, "duration": 0.07898998260498047, "duration_str": "78.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00439, "accumulated_duration_str": "4.39ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.238455, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 72.21}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.271348, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 72.21, "width_percent": 16.173}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.278072, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.383, "width_percent": 11.617}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InhuVzRZdXgvaTIzSXU5NzJBckw2MkE9PSIsInZhbHVlIjoiOCtWV2hJVEg1ZWJ5eXRIOEVmK0Nudz09IiwibWFjIjoiN2Q5ZmM4OTJhY2EyMDRjNTA0NDE5M2MwZTQwMThjODkwMjRlNWVjOTI4OGQwMjFiMGNkZTU4YTFmNDRhOTNjYiIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-41752304 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-41752304\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-643955581 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-643955581\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1119089462 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119089462\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1387574159 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6InhuVzRZdXgvaTIzSXU5NzJBckw2MkE9PSIsInZhbHVlIjoiOCtWV2hJVEg1ZWJ5eXRIOEVmK0Nudz09IiwibWFjIjoiN2Q5ZmM4OTJhY2EyMDRjNTA0NDE5M2MwZTQwMThjODkwMjRlNWVjOTI4OGQwMjFiMGNkZTU4YTFmNDRhOTNjYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750984990287%7C64%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImU4WTZXUHh5UWFFS2pzaEQ5eVowNFE9PSIsInZhbHVlIjoiTTdyMGlJS0tQZFI1SDVOMzJHNjhrZER1ZTB5cHZOd21YMCs0MFIyanZRcGgwUTQvOEV3SldSWTlBRGc5YXU1MnNGd3dQTGdvYXlMNWdMaG1VMlpPZWVxK1gxNXRzYnkzd0NUU0VocnUvaWFRV0g2dGl4amdGdmo1VmNqZWpZcFVySTVjUWdySG1vaExYQmtxcEIwemZCZGVRV2xlNFlWZ0g1SnI3VUNucWRrcjd0U1N5T3laQ1JnVmM1aG1HUFBaTkEreExUV3htSWJ0YTd2ZURRZEJsSzYzMGtpMnFtMVl0T200VEhjaTRxV1ZuNk85VU96ZVBWdHQrZjB5VlBwd0NGSGlWSm1UWFBEMjU1NFlyYVNpY2dDVVllL1NkaDQzNmdHNTd4Ukl1RElHMitYY0w4OXU4N0tBU1o0amJjcm4wTlV2eEYzV3lXZ2tVckIvRkQ5M2g1elRIRmtBT3JWTW15MzFrU1ZQa1RTUVZUYXlKT2JCcXIrKzVaRjBscDhXZy9qUDZDUC9tTFp2UTlaUHE3aEFKRVIrVXdZK0wzcXJmeXpqeTZjQ1hCTTJTc0F6NmVzR3l3WUk4TnNKbERpMVNid0dPSXRpWTQ0bnBlZVVSSFR3MG5kSmVFK3Rzd3hycWdvN2labXlFM0tEcGJpNGc3S0V3NzJoNXpNQlVRcGQiLCJtYWMiOiIxNzYwNzU4Yjg3ODJjYTlhMGE0MjdhZmYyNzQ2N2VmOWE1YzY0OGU5ZTFmM2JkNmZhYTJiZWY5YzQyMDIyODI2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ik1GRXFHZTc1eHcwcDhQeDhqZlh3dWc9PSIsInZhbHVlIjoiTGF2b3ErOC80ZGVYTk9VSEpYYldOeEgxanJxSFkrS01mWnJydzgyMUs1dkdlRnFhQTZCNVVPaGZZNkQ3U0tvWmlsTkl2VEM4SkdQMWc2Z2VPcjBFTnBUZ0tyUit4MjNGVndQTG5ySnhVV1ZvalE5Nm5QWG1uT29IcUUxdWtreVJvZTJaazI1bER4MFJPckY2K3RiUUtlZTF1RE1QaUk0cGNnRzQ0dk9BQ2I5bm5PL1g3YWN2cUxjMWNqckxaUkVGb2kwNXY0dGZVck1lblZZQzZ5Y1I0Y0dTdDhnczJMcS9hSFhmZXhDVUpkMDZnYTRDUytwemRuOEtFWkRObFRJQnhaMXpSeHVEeUZpaWhGdnhNMGZKTkQrdXZiQUNKMFdleHRidytEeGFXTU5yMGVPZW9KaXpUQXBneEU1dHlzbEIwQURRaEdVTzhFc1FCR2JadGh5QU01ZWRyZ2k4SmpDSVh1Z0hVUXFkZFgraUtmRjhKYUEzcFFiM0dxMllxT3ZTZ2VqdytjMllEYzlBbFZiakRTZ1FNK2JVTHhSRC9ERzVRbHhiR29FcFgxK3hTMG52NTdUS1JidW1kMXkvVnZVSVlZSWo1YUVpY1g3RkVlOGxTckhRdGxQWklLL0pJS3ZQekdxYnNrYnEwYm9HbGVrenNETDByVy81ekozeGZXL2YiLCJtYWMiOiI3NmY2YjU5ZmJiZThkZTRlMDc5ZDE3ODY5NjM4ZjFiNmU3MWM1NDhhNDhjMmVlZjA3OGU2MDhlMmI3ZjBjMzk5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387574159\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-18018068 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18018068\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:43:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklGd2d6c01kdG90MU43c2MxbW9WZUE9PSIsInZhbHVlIjoiM1k0VlNsNTFUQmYwc2JZbXR1QitreDBqa0lLckkrRGdhYVUza1kwQm9UUVNGNkZMVG5UZlJrZlhCZzhLeWdCUWpYRlN3MTdLejVyOEx3SXJQUUVHVm85NXdwMU9iMmlIeHJoSys1M212OG9qMVhQTzVPd2U4Wkp0TEg4ZFdyb0VWcGhieEEzMUZHZ1lVeWdVSThhRkxqRVFEK1ZhSVdyNUFINk1HMnl3MkNDSTYxS24vZnRNbVl5L2pPNzNoUjlVMklWK0R4MVNUOGJpZUJ3LzlzR3hJOUxCMENhWEpEem5mWUJtOXpSQmpXQkl6ODBPZjRYSDFJS2RBSUpJK2lJbGh3UWhiWENrQ09Wb3FxRzkvc1FUWEYzMy9JanVtUlUrTW9JakJmMCt4czd4UGRlckVUaUtMTFBYdm5uMml3ay9MZURGM1ZiNi9VemdkRVZtMnk2Z2lHdHhqcnpKdkUrYnJGbWR2NEJlY2M0S3ExOUhoWnBFMzkvbzVXZUtmVGJ5YU92TC9Cb0RuY1RrNGtRWTJTeENzZ05RQlNzZHBtVlpscWNoNFNSZEZRRTdVTTV1TzY5aThXUDdHSVI4SnlTV2xmRWVWaGgvZC91eU1KbS8rVjk5N0hLM0g5VlZsZUxaVTRwbUFQSkNqM2N4aStvSURqZ3IxR0tsK1pKeElacVEiLCJtYWMiOiI0NDdlZjRjMTU5NmE0M2ZlMzE4Y2JjZTdkMzc5OTU2OTkyNzI5NGM0MmU0ZDY5ODQ4YTVmNmNiZGQyZDA5MWI5IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:43:13 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InExbkJXbUVwekZmZmE4SlFQaWVvNVE9PSIsInZhbHVlIjoiNXdiOW9LWlpWYnhTNFl6WkRaTWFPbkJwcUlqV05oc0ZkbmZPdWtPSW1GTEErSGRZRXFyZG9SMmpUbURKYXJ4OTQrU0RxY21LSTRCM3l6N0ZiU2NQOEJ2Tjk0WWRVT2p4aEI1M1dldysrVkgwYkltK3I3cEE5cGFBdFNuS1dHbVBJUnVYYmcxTytVejZ3T2pJU3VwYXRvbm80TmZiRnd2bFhlcHJRbDVwYzlONjBSSm1zZm1MeDVJenRNR1VMem5rSFJxai9CckhzSE5sRTJ0cVRURDBjaW5Bc1BCdnUzcWMzczE2cnRtdjRXa1hlL0k4dGRPcU1QMmM2UDBoVTQwZnpSUXhjN0VnV0xibUI5cTE0VUJCcEsrcWNrWlhNSEdlcFV5T2Zxa1lEOHFpSXRiQ2g5L3RQSk5QNHVvOStZbGtkVUFBWlRNa09EOC9hOUgwTW1vbnpQRkUxOXhVMlVBZlFHcW5qSDRxSmxIckhCMFlaMEpxakJvVExlNTFjMnhuZTdFazB4clc2N2NuY0xuN0Q1TDVrS1J2bUlFWjkvNGdZU0RldkRZcmROb015SUtvY0E5a2Uvcmp2OHJpaFlLSE5yRll5a2tIOGthU0lFSXV6Vm5lNXpLaFp5aDF5TDJjZDc4d05WR05FUEI4Zk9BbnJlYS9uUk5VMDRYZEN3RDciLCJtYWMiOiIzOGMwNDVlMmJhMmFjNGVkZWM1YzhkNjU4ZjM2YzU4NGFjZTM5NGFjNjBlZjY2NmI2MDg5M2EwM2IxODUwY2U0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:43:13 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklGd2d6c01kdG90MU43c2MxbW9WZUE9PSIsInZhbHVlIjoiM1k0VlNsNTFUQmYwc2JZbXR1QitreDBqa0lLckkrRGdhYVUza1kwQm9UUVNGNkZMVG5UZlJrZlhCZzhLeWdCUWpYRlN3MTdLejVyOEx3SXJQUUVHVm85NXdwMU9iMmlIeHJoSys1M212OG9qMVhQTzVPd2U4Wkp0TEg4ZFdyb0VWcGhieEEzMUZHZ1lVeWdVSThhRkxqRVFEK1ZhSVdyNUFINk1HMnl3MkNDSTYxS24vZnRNbVl5L2pPNzNoUjlVMklWK0R4MVNUOGJpZUJ3LzlzR3hJOUxCMENhWEpEem5mWUJtOXpSQmpXQkl6ODBPZjRYSDFJS2RBSUpJK2lJbGh3UWhiWENrQ09Wb3FxRzkvc1FUWEYzMy9JanVtUlUrTW9JakJmMCt4czd4UGRlckVUaUtMTFBYdm5uMml3ay9MZURGM1ZiNi9VemdkRVZtMnk2Z2lHdHhqcnpKdkUrYnJGbWR2NEJlY2M0S3ExOUhoWnBFMzkvbzVXZUtmVGJ5YU92TC9Cb0RuY1RrNGtRWTJTeENzZ05RQlNzZHBtVlpscWNoNFNSZEZRRTdVTTV1TzY5aThXUDdHSVI4SnlTV2xmRWVWaGgvZC91eU1KbS8rVjk5N0hLM0g5VlZsZUxaVTRwbUFQSkNqM2N4aStvSURqZ3IxR0tsK1pKeElacVEiLCJtYWMiOiI0NDdlZjRjMTU5NmE0M2ZlMzE4Y2JjZTdkMzc5OTU2OTkyNzI5NGM0MmU0ZDY5ODQ4YTVmNmNiZGQyZDA5MWI5IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:43:13 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InExbkJXbUVwekZmZmE4SlFQaWVvNVE9PSIsInZhbHVlIjoiNXdiOW9LWlpWYnhTNFl6WkRaTWFPbkJwcUlqV05oc0ZkbmZPdWtPSW1GTEErSGRZRXFyZG9SMmpUbURKYXJ4OTQrU0RxY21LSTRCM3l6N0ZiU2NQOEJ2Tjk0WWRVT2p4aEI1M1dldysrVkgwYkltK3I3cEE5cGFBdFNuS1dHbVBJUnVYYmcxTytVejZ3T2pJU3VwYXRvbm80TmZiRnd2bFhlcHJRbDVwYzlONjBSSm1zZm1MeDVJenRNR1VMem5rSFJxai9CckhzSE5sRTJ0cVRURDBjaW5Bc1BCdnUzcWMzczE2cnRtdjRXa1hlL0k4dGRPcU1QMmM2UDBoVTQwZnpSUXhjN0VnV0xibUI5cTE0VUJCcEsrcWNrWlhNSEdlcFV5T2Zxa1lEOHFpSXRiQ2g5L3RQSk5QNHVvOStZbGtkVUFBWlRNa09EOC9hOUgwTW1vbnpQRkUxOXhVMlVBZlFHcW5qSDRxSmxIckhCMFlaMEpxakJvVExlNTFjMnhuZTdFazB4clc2N2NuY0xuN0Q1TDVrS1J2bUlFWjkvNGdZU0RldkRZcmROb015SUtvY0E5a2Uvcmp2OHJpaFlLSE5yRll5a2tIOGthU0lFSXV6Vm5lNXpLaFp5aDF5TDJjZDc4d05WR05FUEI4Zk9BbnJlYS9uUk5VMDRYZEN3RDciLCJtYWMiOiIzOGMwNDVlMmJhMmFjNGVkZWM1YzhkNjU4ZjM2YzU4NGFjZTM5NGFjNjBlZjY2NmI2MDg5M2EwM2IxODUwY2U0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:43:13 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6InhuVzRZdXgvaTIzSXU5NzJBckw2MkE9PSIsInZhbHVlIjoiOCtWV2hJVEg1ZWJ5eXRIOEVmK0Nudz09IiwibWFjIjoiN2Q5ZmM4OTJhY2EyMDRjNTA0NDE5M2MwZTQwMThjODkwMjRlNWVjOTI4OGQwMjFiMGNkZTU4YTFmNDRhOTNjYiIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}}