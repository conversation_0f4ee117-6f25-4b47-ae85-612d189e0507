{"__meta": {"id": "X5b963a4adb69bbf18f82c45d7d1dc30b", "datetime": "2025-06-27 01:15:03", "utime": **********.354404, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750986902.936365, "end": **********.35442, "duration": 0.41805505752563477, "duration_str": "418ms", "measures": [{"label": "Booting", "start": 1750986902.936365, "relative_start": 0, "end": **********.291942, "relative_end": **********.291942, "duration": 0.3555769920349121, "duration_str": "356ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.29195, "relative_start": 0.35558509826660156, "end": **********.354422, "relative_end": 2.1457672119140625e-06, "duration": 0.06247210502624512, "duration_str": "62.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45027056, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.013900000000000001, "accumulated_duration_str": "13.9ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.318342, "duration": 0.01293, "duration_str": "12.93ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.022}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.341634, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.022, "width_percent": 4.029}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.3474278, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.05, "width_percent": 2.95}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-605644491 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-605644491\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1369470210 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1369470210\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-401178990 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-401178990\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2095635812 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986887615%7C89%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjJ2RzRpeEpPL1h2K0RKUUFkei9VT2c9PSIsInZhbHVlIjoiVGtUeHk3TkMvei9XR0t2M2huV3VSckR3TGhNb1J2VldEelpYdUlFNTR6Q1dyZzFWREJQRVhnd2czNVIraEdDMVlDNUlFRC9pUUpHZjBYNk0vOVU5ZmpuZ3p0eGVNaWRDZEZwckY5SjdHMFJ4dXc5cVh2STRhWC8rRm93SnJmNFcrMEYzUXo3RDZRcUF4ZUpadmc5dS92REh2b1ZzY2V2Zm5HZWd6UHJPMUVjeEZpTjZKaE9uMGVuRzFkUlpZMTMveWpvaFhGZy8wWitPc1BKS3Y4aXR2Nm55Lzg5Y21ZcWo3b1hZSThHUXdVTFZBN2cxV2d5N2lVc3BvbWFBbGJZR3hWNHB2OEYvYTFIbzgyTXdSOHJ6UWJlU01EOEh3WjhUS3NuWENmMTZzdjR3ekMvOWlmeGUvNG9YWWxkMUFYa1RXeGlNek8wYzNIaW4yS3dxOWRmYzRkb21mUHdkRFUrLzlqSUlOdXB0bDRJMFNPdDNIbTdQeDA4enNmcVZoRGJ3T1N5OUxHS2RnSGhpU0xYSFU1TVFpSFpsUjlDa1p3bktEaW43d2Znd3FacHEzdUhDK3JzeXpFRTVONjZyZm1CREF0bkRlcWNnWVZvazJBcDY5UitnL0FUVis4TGhlZGhpa2VBdFdRaklUTDNzUHdseXRBWWtCbTJnVmQ5dUgyUkEiLCJtYWMiOiI5MmFiNTk2YzkwNjBmYTFjMTg2ZDdjZGFiMTQ1MTk0ZmEzOTc1YWM0M2U5YWI3MWMxNDIyMDU3NTMyMmRhMGNkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhId25HOGZTZ3ZkVlRMWmpvQjYrNWc9PSIsInZhbHVlIjoiTFpDNEtEbUhBTEp2M09vMmUyb3RxQi9TNWd4VEdSUkRoN1ByUzF2L3lKM3FONktCNk1HZ2Zoa1l1MEk0OXBYczNQUmZsd0NTVlNuRGljaC9iSHhyankrVFdHVGprdDA0RkNsdDlPeENaa0xZUWduREZMdDhjNW5LT2IvdXNDWDZkQVBXazJqZSt5Qk50UjMwN3pvaCt1clg2Mkgzb2ZsODBadmFtNXMzR3pDd3BKelF3ZEJraWppNENZWHVKS1JVbGJ6MC9oU0xFTnVWZXNIekFGeUdLaXV0SXM2akpVWDF2aEg5WU5nelpEb2pwbGp6NFJYb0xoMWQ1ZDA2c1o3OHhlM0ZmVU0vWFU4UWoxOVN5QXI4bHBzMTl0R0FtT1FWeWVlMWk5ME8wYk1JSHFiRmZHMzRiY2JsVTF2NDdOTmV4SWJFaVdyclFkOTR4Q2s0SEIvQkVjTUY3NG5nUXJrT2drWTRmTFE5NmZKemF2d2JkKy9RdjZtQm1BWUVhL3lIVmxvR1RacmVHR3hIdTI3SDVNVEs2dzF5d0RLdXFVVy94NmNha2NLdm1zZ04vcSsya00welltTjNBdzZZbVhPai82cExHTi9qYjhrYU44MXhrNkRXUnBCVGpIWmxtcHJLWitDcmEvVW1EeDhna0dDcGtjbFR1emJTcU9QV0x0ZUUiLCJtYWMiOiI5YTY2MDc3ZGMxZjc3YzU0NDAxYjBlYmIzZmQ4MThlNThiYWNjMDFlMzExOWZhNDMxZTc1MjZiNDQ5ZTdhZjU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095635812\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-457564460 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-457564460\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-757230684 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:15:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjlpQU5FaTRLeEdaTkpJeHowM090VGc9PSIsInZhbHVlIjoiRU1ObExMS09aQjREVG0rR2xKNURmcWJzblYzZDNHeXZKdUxlYWowQ3l2S1huVk42Y1BhdVp0NkZwcVNlNnQzbHphZXF6QXdmSC9ETVZuY2syczAyazgzc3dyTVdLOStUVlpxWk1Qb2c4SDVQNFo5SFl5NmpFdDlBZ2tHekZwQitKcmtpbHMvNjBZSzhBbE1JNFhuTU1YSXJGYkNRdTRUcHgwNlVzRHBBVDVqSzB5YmtlbU14d0FNK0hHV2Y0d0l1WnYzRkNqMEFDSTVLbm5MdzNEQWpYWDREb2p5WVh0R3dMeGNId0lvTzBWdzFoeG9tSEFVM2xlakRhQUk5N2tMelVRUkViamZiWHJDWFc2TzROWnI2NG9kMExYZ2FWbDlnMk1tb0wyU1N0NnBFNTFNaFJYRURQZzVlc3ExN2Zic2pUNHBFdllFbE02dVhmR21tK3RPZXhxemdsR21WSXNneHdERWVUOFZieUJNWkI3T1lHU2lnSFZ0aUNmUjhycTJkYzI4SFR4NnhOU1BjRzBIWWdFZWs1THBTVWZkNjF1YldXY2NlbWdkMFVXRTkvcUVhZVVUeFpGYit6VGUycEhhYnhWK3AvMS9aSnRNRkJiNzR1d1lOYkZrS1NNSWVoTnJNNDJsbTZWeGtWa3ZRbDE5V3RwSmwxR0IxV3N4V3FERWoiLCJtYWMiOiI2ZjEwZjEwOTIzNWFlOTZhM2U4ODlmM2U5MTEwNzA4ODZkMWM3ZjQ5YjM0N2QxYmViZGE4ZTQ4MDMwNzY4YjYzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:03 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ink2UGFvWHZRK2l3dE5VVmk0cnF3ZHc9PSIsInZhbHVlIjoidlNjaGs5Z1praXdKYVgzbmhXZlV1U0RpTzZCTlNFN1VtMGh0K3VPT2NVa2NsUExXQ2ZuVFlySkVteWRCcU8vVVozMnlSdWZ4djdobzhDd01PQ0JsSloxNzRid0F3SHVSdklCeWdFaGRYT2lJbU9aU01PR0hLZFdid01FdUVnTVBJR0JiajgxbHBPM1NxcXZRUHl5UjNsbkhrVXI4MEUvVFErdWVLSUJHTzhPbVdjRTVCUkpaVTViUjVxV1lFU29wcTZJTVl5Sy85b1ZBNlJGU3htU211NWl4d3EwTG5KL0tmam55L05ySGdFVjQ0eGh0KzBvU0o3bXhuZk5NNUtaRmNKVXhlOEJhUm1BZjkva1lXOTE5Yjd4YzJETU9GNEtWWWIwWUtQM1JodllTN3pCcHY3ZEFuQUlwVXFJWkhyWjIxdEhOQ1N3aEd2eVRzYm4xcHNUcWxwQkpGZXZmTzhiQzlVWjByNG5oZlhIazFwQkdHRDM1QlVDQlNyejB0elZCTTVCZ3JoU3p0dUVXTURZSExCZVZVY2hMOFFUOVZGbmpEWUlVYkQydXcrZ3B6RnlQU0piak1QQ211YUFxT3lSMExTQitzb0VKUU1CK2E4OTdZeXVVYmRnUFc0SXkvMkM5eGVvZlZYb1VZdCtYT1diM0prUk9SQmx4Qy9mMWxMR0kiLCJtYWMiOiI4MDMyYjg3NzQ3NWE5NmQ4YmViNmY1ZWQ0MTAyMTg0NmJjYjg5OTExYzJkYjNiMDBjMmEzZjFhODAyZWFjNWIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:15:03 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjlpQU5FaTRLeEdaTkpJeHowM090VGc9PSIsInZhbHVlIjoiRU1ObExMS09aQjREVG0rR2xKNURmcWJzblYzZDNHeXZKdUxlYWowQ3l2S1huVk42Y1BhdVp0NkZwcVNlNnQzbHphZXF6QXdmSC9ETVZuY2syczAyazgzc3dyTVdLOStUVlpxWk1Qb2c4SDVQNFo5SFl5NmpFdDlBZ2tHekZwQitKcmtpbHMvNjBZSzhBbE1JNFhuTU1YSXJGYkNRdTRUcHgwNlVzRHBBVDVqSzB5YmtlbU14d0FNK0hHV2Y0d0l1WnYzRkNqMEFDSTVLbm5MdzNEQWpYWDREb2p5WVh0R3dMeGNId0lvTzBWdzFoeG9tSEFVM2xlakRhQUk5N2tMelVRUkViamZiWHJDWFc2TzROWnI2NG9kMExYZ2FWbDlnMk1tb0wyU1N0NnBFNTFNaFJYRURQZzVlc3ExN2Zic2pUNHBFdllFbE02dVhmR21tK3RPZXhxemdsR21WSXNneHdERWVUOFZieUJNWkI3T1lHU2lnSFZ0aUNmUjhycTJkYzI4SFR4NnhOU1BjRzBIWWdFZWs1THBTVWZkNjF1YldXY2NlbWdkMFVXRTkvcUVhZVVUeFpGYit6VGUycEhhYnhWK3AvMS9aSnRNRkJiNzR1d1lOYkZrS1NNSWVoTnJNNDJsbTZWeGtWa3ZRbDE5V3RwSmwxR0IxV3N4V3FERWoiLCJtYWMiOiI2ZjEwZjEwOTIzNWFlOTZhM2U4ODlmM2U5MTEwNzA4ODZkMWM3ZjQ5YjM0N2QxYmViZGE4ZTQ4MDMwNzY4YjYzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ink2UGFvWHZRK2l3dE5VVmk0cnF3ZHc9PSIsInZhbHVlIjoidlNjaGs5Z1praXdKYVgzbmhXZlV1U0RpTzZCTlNFN1VtMGh0K3VPT2NVa2NsUExXQ2ZuVFlySkVteWRCcU8vVVozMnlSdWZ4djdobzhDd01PQ0JsSloxNzRid0F3SHVSdklCeWdFaGRYT2lJbU9aU01PR0hLZFdid01FdUVnTVBJR0JiajgxbHBPM1NxcXZRUHl5UjNsbkhrVXI4MEUvVFErdWVLSUJHTzhPbVdjRTVCUkpaVTViUjVxV1lFU29wcTZJTVl5Sy85b1ZBNlJGU3htU211NWl4d3EwTG5KL0tmam55L05ySGdFVjQ0eGh0KzBvU0o3bXhuZk5NNUtaRmNKVXhlOEJhUm1BZjkva1lXOTE5Yjd4YzJETU9GNEtWWWIwWUtQM1JodllTN3pCcHY3ZEFuQUlwVXFJWkhyWjIxdEhOQ1N3aEd2eVRzYm4xcHNUcWxwQkpGZXZmTzhiQzlVWjByNG5oZlhIazFwQkdHRDM1QlVDQlNyejB0elZCTTVCZ3JoU3p0dUVXTURZSExCZVZVY2hMOFFUOVZGbmpEWUlVYkQydXcrZ3B6RnlQU0piak1QQ211YUFxT3lSMExTQitzb0VKUU1CK2E4OTdZeXVVYmRnUFc0SXkvMkM5eGVvZlZYb1VZdCtYT1diM0prUk9SQmx4Qy9mMWxMR0kiLCJtYWMiOiI4MDMyYjg3NzQ3NWE5NmQ4YmViNmY1ZWQ0MTAyMTg0NmJjYjg5OTExYzJkYjNiMDBjMmEzZjFhODAyZWFjNWIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:15:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-757230684\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1022287408 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1022287408\", {\"maxDepth\":0})</script>\n"}}