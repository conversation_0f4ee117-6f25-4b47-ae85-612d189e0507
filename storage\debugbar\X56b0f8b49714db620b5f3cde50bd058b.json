{"__meta": {"id": "X56b0f8b49714db620b5f3cde50bd058b", "datetime": "2025-06-27 01:14:47", "utime": **********.731792, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.276806, "end": **********.731806, "duration": 0.4549999237060547, "duration_str": "455ms", "measures": [{"label": "Booting", "start": **********.276806, "relative_start": 0, "end": **********.656397, "relative_end": **********.656397, "duration": 0.3795909881591797, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.656407, "relative_start": 0.37960100173950195, "end": **********.731808, "relative_end": 1.9073486328125e-06, "duration": 0.07540082931518555, "duration_str": "75.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029840, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.027880000000000002, "accumulated_duration_str": "27.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.683055, "duration": 0.02677, "duration_str": "26.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.019}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.717983, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.019, "width_percent": 1.686}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.724805, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.704, "width_percent": 2.296}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender/eyJpdiI6IkQvaEpGMWNpQkNiSC93UFNEZ1gxK2c9PSIsInZhbHVlIjoiMEJtZ0l2Y1gvZjlnVEFiMjFJMEgvQT09IiwibWFjIjoiMzU4YjAxZjJlZmRjODhlMjMwOGE4MzQzZmEyZjFkMTYyMTNmYjI2MzJlNzMyMTM5MDM0NjYwZjc0MjI0ZTFhMCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1494203098 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1494203098\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1831079227 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1831079227\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-254845722 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-254845722\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1328013761 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/vender/eyJpdiI6IkQvaEpGMWNpQkNiSC93UFNEZ1gxK2c9PSIsInZhbHVlIjoiMEJtZ0l2Y1gvZjlnVEFiMjFJMEgvQT09IiwibWFjIjoiMzU4YjAxZjJlZmRjODhlMjMwOGE4MzQzZmEyZjFkMTYyMTNmYjI2MzJlNzMyMTM5MDM0NjYwZjc0MjI0ZTFhMCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986879027%7C88%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndqVkpvd0xIUk5YLzNrdHZaUVZLTVE9PSIsInZhbHVlIjoidURSMjA0c0ErY0liaSs1SEEybHpCZWZhb1pjVE1pdHdwRXhVeDc1RHk2TGJmczhjditYRjVaWkFMaWs2T2JrOEZSMElab3JXRTB6di9Ob29rTzQ1d1kzTVhWaVIwdzdQWkVMeHZLTGovVmwwKzJ0MWZVWUNiWDhEMzVHSTJuSVZJd3AyZ0orMFZTcmNHdnc0QXgrbkRDRDR2S1IwNW9YUU5tREJNSHc0VzBwMHMwUnZBaU9KVTY5cCtpWGk4bUdwZXRYWTMwTTN2TUl1dFlYemtSTlloVFFyUWlNVHlNZkRRb2pCNmpsZ0RFdTQ3YWNZSHdTaVdkek5wNjdnL0Z4UlpHVEk2cE9LZHJJWkl1NUxGZXhscDU5NnhMdmdmY29DNFZpcThmdU5pcElMSVNHMWFqYk9kRW1rZGdKOWtINksxT2hTYWR6Nnp4ZERFZC9xejFhbWFibURETk84NFBqWkRndkxVQ0luYm5YYUlxU01EYVRHd0tPQ1F5REZPTjhjU284TGMyUHJQYWpNNWs1b1RxQXIyajU0WEtOL2FuRHlRckhHSWNxUndSTHFKSWVFbnFvQSt1V1EvNmpPR0FBYWE0OHNSNWZjN1ZPQjhaOVVXUzNsOHlUWktIU0hiVTRVUGlUeVMrQXZpU0FwQW5ncWNXajU0ekhSejUrOE5iY00iLCJtYWMiOiI4NDY2MWNkOWQ4ZDlmZDg5YWZmNjM1NTA4NzNmMTdkNTI2NDQyNGZmMmYyZGM1M2QxM2NkN2U0NGE5MmFkYWQ4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImxrbnlaNVA1Q3p4dzZMamZrUnZSN3c9PSIsInZhbHVlIjoiZU0wd1IrZ1dPdjg0eUhCQllBQlFieEdOQVlXdnBvZUxTM3FjaXBndVdwQm1qODBWYVBjWGFveER1TEpXM1Y1c2ZxNG1FUG81dkNFRGZuWlEralJaL1FRZGpXQVpLdjNVODRZbnJkUU1XQXYzcm1IWm83a096aGYrYmZtbzFOM3NtZFRYS1Q0Q0JseTNLUk9LcW5yYmcyRUs3QytndXB3STRCd292VW5CTm1MbldKV1JTWUluR1F1K0ZnbFNaN0NrVGZxT2duOGZUdm9mUmlpMk03Qnh2WU5vN0NKWEhJRnd1YlVUT0o4NWtSRExqNFBiajBBOTdwd2Q1YnllaGJjMVVDRW00U1VLckxwN0tobUZIa3BQeEhuQkZqa0oyQlREcHBCT1BVTkwrdHp2QUR6a1ZEM1cxdGFXSi9XbmplYS9GNmRQNDdtbDRtZWpTd0wrNXFkUDFiVmt2K0hBZkh3NzJQOFNhbXhPWHZyM0U0OTVVc3ZFaFdaK2p5bDB5RWpLOVdpRjArUUtTWmFxU0p6eERaYjVFcnBsMW8wc0labzA0WHlIOXczQ2NaNXRwMXdOU0VjSXo2SFJ6dGk2ZEFmUXpybXRkc21KM1hxaXZQR0l0SW1lSUxXY292ZjRYUVdYejhubkhaalJ4V0tmY0xaak1sTDV3Z1JSOUpJRGM3OTEiLCJtYWMiOiI2ZDJjNjljNTVkZDMzOGI3MjQxNmVkYTRlMzRmMWY2NmMwNWVhNzAyODM2MTQ1NmFmMWEwMTM5M2Q4YTEyMTMyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328013761\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-650509747 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-650509747\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1578251243 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:14:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InRPOXEvRmRkRUk0Nkg3SUU3Q2ZoQ2c9PSIsInZhbHVlIjoiS2czU2ppeXNMMW9sTnBqWGdvdGJER2NRTGF4Q0hycVovR1o0ZFkzVjIyN3JzVUtvU2U1RVJxV0pjRG5qL2VjdHBZbU1hUk1IaFNucTBRZWJHM1RDWHk2MVQyOE4rUzBFNFBwWjNYZVdrLzIzeEZoUkxhb2dXSVFGYWxLdnFrdnl1dVBzcWwzY2xjNVVDaWZObjFCdG8rZzNBNjB2SnhnczIvV01XeUNiVXZXSkgvaDRVZDJOSXQwOUE0ZkF0bC80L0NuWXNmVzRWQUtaOE9JS2JnN0JjaEIwZ3p6NWJ1Q3V6VEVVMVc5MzJwbFdHKzhqTFJ0cjBxSjlHbmFDRkdoZEJhZ1BYamNKRVBDaGlnaU02Wm4xMnlmMXBLdEhwdmtqVzRibUI3V3JLVGpGS2d6eGR5Z1gyWXRZSnRlQkEyVUQ2NFphOTFMZTF3emxieG03RFo4Qzh3MUdlTnBaMnprRWpuek1oL3FPWjVFWlZjWHQxK0R4RlRmVlNDam5EcmFpM1BocG9NRFdhbm0zT3R5eWpRbFFtcmRPdjJOdVdaQ0dSRXA2Q21mQndzZ2NMTVc0SkJrTFRSMFpjK1hCclpJbTFYVHF2T0M5dVI0bVFkWlhJSkU4cnBvVXQ2ZDZXL1BRVEhWUE52VTAzTUV6RHRpeUlyVjR4T2x0TUp3bnREeDEiLCJtYWMiOiJhMzNjYjRlODU4Y2Q2Mjg0NjU3YTkwYWUwZTAxMWUyZWFmMGUyOTNjOGQ5ZmVkZDQ0N2QwZTUwMjU5ZmM4NTFjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:14:47 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6InFuOU9mOWpvRVc5cHVRQ2R0bWdlalE9PSIsInZhbHVlIjoienRiVW1LbnBucTFNSEhFdzh6Vy9KL0lQbEZsT0xoRXcvYVdITzBmeHc4aXFMbzg5U281bS9tMGlYTVdSRytjRFppMkF5U25ISENWSHV0Q1V6NEUvbXo3QXhxT3h6K1c1SHlIMk8ybllZTnhKNlNtLzBKZFNnYXNXakxYVFg0RFZGWHd2d3hDTTE0Rk9pLzBSSW9ENURNZWRtdDVDMGIwY1VINGs0b0pWaEREV05ZL040NW93cEFpdFRVT0xzN1h1b0pXQ080OUh0R3VjVFFUdFBzZWc5SGZSNFZVTkkxR29Kek1NbjhCQ29JWkZqUk4xWmRWaG5TUVAzRkFTbS9oMjJWelczMzIrenRGZjYrK21uU0wzSmxWQzRmZDR1Y0NjamIwZXNoUzU5aTYyNXc4bGV6c0pJN0t1ZFFzZ2c4cCtobVJIbVNqMDFVZ25STllqazlWWDZ1cWQyUFhqSDU4cTFWYVB1RXcvNkpMblI3U2Jzbzh0b2xCaTQ4U3VYcVJsNXg3d3NrQ05aMzk2Znp5NjJnYk44dFgwK05ETThKeUxhU3BBYU9wNEhBbzJFQ3ZvejVTelE3dzZRSGdDdFVwUFJQQkZia0MrUmJCQVFRcURqZ3BaMDBEL1o2TGZmK2NaYzVNQkpkSW81bndCdTFUUUpiRjF1eWNFeEowa1B3M2wiLCJtYWMiOiJlNGZkZmUyNDNiOTNiNjgxOTc0MGZkMDg1ODcwMzFhOGNlYjdiY2Q1ZDk3OWYzYjEzOTg4MGIxMDlmNjcwNjY2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:14:47 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InRPOXEvRmRkRUk0Nkg3SUU3Q2ZoQ2c9PSIsInZhbHVlIjoiS2czU2ppeXNMMW9sTnBqWGdvdGJER2NRTGF4Q0hycVovR1o0ZFkzVjIyN3JzVUtvU2U1RVJxV0pjRG5qL2VjdHBZbU1hUk1IaFNucTBRZWJHM1RDWHk2MVQyOE4rUzBFNFBwWjNYZVdrLzIzeEZoUkxhb2dXSVFGYWxLdnFrdnl1dVBzcWwzY2xjNVVDaWZObjFCdG8rZzNBNjB2SnhnczIvV01XeUNiVXZXSkgvaDRVZDJOSXQwOUE0ZkF0bC80L0NuWXNmVzRWQUtaOE9JS2JnN0JjaEIwZ3p6NWJ1Q3V6VEVVMVc5MzJwbFdHKzhqTFJ0cjBxSjlHbmFDRkdoZEJhZ1BYamNKRVBDaGlnaU02Wm4xMnlmMXBLdEhwdmtqVzRibUI3V3JLVGpGS2d6eGR5Z1gyWXRZSnRlQkEyVUQ2NFphOTFMZTF3emxieG03RFo4Qzh3MUdlTnBaMnprRWpuek1oL3FPWjVFWlZjWHQxK0R4RlRmVlNDam5EcmFpM1BocG9NRFdhbm0zT3R5eWpRbFFtcmRPdjJOdVdaQ0dSRXA2Q21mQndzZ2NMTVc0SkJrTFRSMFpjK1hCclpJbTFYVHF2T0M5dVI0bVFkWlhJSkU4cnBvVXQ2ZDZXL1BRVEhWUE52VTAzTUV6RHRpeUlyVjR4T2x0TUp3bnREeDEiLCJtYWMiOiJhMzNjYjRlODU4Y2Q2Mjg0NjU3YTkwYWUwZTAxMWUyZWFmMGUyOTNjOGQ5ZmVkZDQ0N2QwZTUwMjU5ZmM4NTFjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:14:47 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6InFuOU9mOWpvRVc5cHVRQ2R0bWdlalE9PSIsInZhbHVlIjoienRiVW1LbnBucTFNSEhFdzh6Vy9KL0lQbEZsT0xoRXcvYVdITzBmeHc4aXFMbzg5U281bS9tMGlYTVdSRytjRFppMkF5U25ISENWSHV0Q1V6NEUvbXo3QXhxT3h6K1c1SHlIMk8ybllZTnhKNlNtLzBKZFNnYXNXakxYVFg0RFZGWHd2d3hDTTE0Rk9pLzBSSW9ENURNZWRtdDVDMGIwY1VINGs0b0pWaEREV05ZL040NW93cEFpdFRVT0xzN1h1b0pXQ080OUh0R3VjVFFUdFBzZWc5SGZSNFZVTkkxR29Kek1NbjhCQ29JWkZqUk4xWmRWaG5TUVAzRkFTbS9oMjJWelczMzIrenRGZjYrK21uU0wzSmxWQzRmZDR1Y0NjamIwZXNoUzU5aTYyNXc4bGV6c0pJN0t1ZFFzZ2c4cCtobVJIbVNqMDFVZ25STllqazlWWDZ1cWQyUFhqSDU4cTFWYVB1RXcvNkpMblI3U2Jzbzh0b2xCaTQ4U3VYcVJsNXg3d3NrQ05aMzk2Znp5NjJnYk44dFgwK05ETThKeUxhU3BBYU9wNEhBbzJFQ3ZvejVTelE3dzZRSGdDdFVwUFJQQkZia0MrUmJCQVFRcURqZ3BaMDBEL1o2TGZmK2NaYzVNQkpkSW81bndCdTFUUUpiRjF1eWNFeEowa1B3M2wiLCJtYWMiOiJlNGZkZmUyNDNiOTNiNjgxOTc0MGZkMDg1ODcwMzFhOGNlYjdiY2Q1ZDk3OWYzYjEzOTg4MGIxMDlmNjcwNjY2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:14:47 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1578251243\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-774228830 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"224 characters\">http://localhost/vender/eyJpdiI6IkQvaEpGMWNpQkNiSC93UFNEZ1gxK2c9PSIsInZhbHVlIjoiMEJtZ0l2Y1gvZjlnVEFiMjFJMEgvQT09IiwibWFjIjoiMzU4YjAxZjJlZmRjODhlMjMwOGE4MzQzZmEyZjFkMTYyMTNmYjI2MzJlNzMyMTM5MDM0NjYwZjc0MjI0ZTFhMCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-774228830\", {\"maxDepth\":0})</script>\n"}}