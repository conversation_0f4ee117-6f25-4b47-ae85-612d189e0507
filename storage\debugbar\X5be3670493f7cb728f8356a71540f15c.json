{"__meta": {"id": "X5be3670493f7cb728f8356a71540f15c", "datetime": "2025-06-27 02:27:51", "utime": 1750991271.005906, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.575384, "end": 1750991271.005919, "duration": 0.43053507804870605, "duration_str": "431ms", "measures": [{"label": "Booting", "start": **********.575384, "relative_start": 0, "end": **********.91441, "relative_end": **********.91441, "duration": 0.33902621269226074, "duration_str": "339ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.914418, "relative_start": 0.3390340805053711, "end": 1750991271.005921, "relative_end": 1.9073486328125e-06, "duration": 0.09150290489196777, "duration_str": "91.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48198040, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.02473, "accumulated_duration_str": "24.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.946042, "duration": 0.01899, "duration_str": "18.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 76.789}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.972867, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 76.789, "width_percent": 2.103}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.986417, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 78.892, "width_percent": 2.75}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.988292, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 81.642, "width_percent": 1.456}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.992642, "duration": 0.00279, "duration_str": "2.79ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 83.097, "width_percent": 11.282}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.997509, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 94.379, "width_percent": 5.621}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-291224896 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-291224896\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.991549, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-182532017 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-182532017\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-432494763 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-432494763\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-450286177 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-450286177\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2029014470 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991268339%7C38%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IklpSXhjQ05FOThESVY3aVhoYVNXWXc9PSIsInZhbHVlIjoiSzhjL0M5dXZzalNqaGVybTVWN0ZTODdVeExJTmIzUmtRenRyTXg2QThySGZ6VWV3UjA5S1dxTjhBQmJIYVJKMUxUR2FUUEJYWENmVFVNUWpxSEkyTUhNYnIxcXRDSk9QelJwTWtDNmNMSjJVSTMrdzFOdEZ0ZEJMM1liN1dVZ3N0VE9aeGJtMmJ2M2RwL1JVYk9sMjMxb0JJNUcvSkhsdklrZUhXWlZYTDlJaWZwNlZQMm40RkRjQUZYeVkzeVJ0REU0cUNoWmVFUHlHTVlFcVdWTjA5bDVDTHVYTzRRTjZpYUJkemt4Q0ZXQVhBaDdpMHBORW9RRWVKNFIxbHlVclNIL1VVVDY0WE9KeW9zbGdwaG1jZVZDZ0tkZWY4L2tDZHJ5TDZhQldBV3FxQnBqOHo1YmFnS1lEa2VWSHpha1RROEZtQXQ5T0lYRXdydHFvT3ZFZndIMnlQUnpCRU9WcEpQd2JTUFNiQzFxRWtGbkUxOXBsUHBiTXdLUFM3ZlUvc2hUZXlRUmp0Z2JOVEhBQmVhN1VmUFJkL0w1b1pBZ1FrREVmamFMc2FmRHRIZy9xcDBGVThwV2lYQ1lrNm9HRytPYTE5MnBCd1B4NEt0eE4vR2w0YUNrWWYvV0I1UFBneWFTSTRHend2WXE5WFNKZ2NWVFdaWktHeUFDQUhoK2kiLCJtYWMiOiIzOTliYzViYzg4ZDZlNWFhOTNiNjc1OTI3MjhiNThkMDgyMTQwM2NjNzMyYTIyMmZhNTdkYjYwM2E3MTJkMmFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im5SalVsVmJHUDloaFRKQ2dQNE5MNkE9PSIsInZhbHVlIjoiRXJkdkQ4NEd5ejZXKzFXWncxZUdjRm9JaUF0V1J4YUhIMXdoQ3dOVm10VHpva0Z3NkdJSjh0NWhlNWRMZ0RVOG9ySFgrQ0tPTmw0MUR0VkN0eWF0enRIbUlJNjNRcTJBR0pWS1BIc0h0RUxKZ0VDZDJmT3EvNTgrMEtoME9WZGVta01sQXdhdkRPV2pKTVNuWU9aVUNDNlJNdW9tOUJMb0ZFMkdhZUppNmdTaUdCT21pQWdSQTU1MFdUejd6SG9uS0l1YmxYUklDbmFOVXRXYnNjb0txbTJPVldVSmRFMGxhajdnelRTU3NiVUEweXpXekd0eExpZGE3L3A1ZUpxV0tMNkRVOWVEdUFhRitPaEh5bTJGelh0N1FnMlVIcjBUUGNiY3pqZCtlS0gzcm1uTXg0eEJYM1Qwa3BwR2c4SG04Mi9pMkpsa2pXS1pQSjZDZlBqb2krTmkzQzZqYVlFeDZ5M3RKSE1CRG8yeFNmcW0yMUwxVUlNZkwvVjNhNnk3aGZ1WUpOenNwZUlOL3IvT0R2c0FBUmcvcXdlQmtsMXVwRXkyUk1oQWZiZ0xacWYvQ0pVcmJiUGJCV3hudW5zTFJTQlI2T0hxRktMNnJjc1Z0QVdrRTQ4dWRZbHVXTFZ5N3FWVGJyZlNJdFR4eUZrRHVoNG5GR3JXc2tqNHpQakwiLCJtYWMiOiJhMzgyZjIxM2M0YjljZTQwZGI0ODEzOTRmMGU5ZTk3NjcyMDg1ZGM0OGE4NGQ3NThiYmI4Y2NkYzg4OGIxMjgwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2029014470\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-24634102 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-24634102\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-960821618 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IklZT2Q0Z1dUcTNaQnlvUEpCNWhVL2c9PSIsInZhbHVlIjoiVnp5NzJaMnQzejJBbzRiZzR2MGlYUHdhdjk5emQ3Z1dta0g2SzQ4WVNuNUhPb0FqR0NmQiszQWdsNHQ0Uk1OQmhZRDY0bXhSeXMrQnhFbUo4a093eEVqdFM3Rm1MTC9uK0h1N2FxU2wrSjZ4cDRiQWd5ay9hNUliR0hUbVhRS2UxdHVkSWJyVFkxL2ZoSVdMY1NUR01uRi9lUVlYdFN0Z3EyTGlKaFowRWtjUjFISTFVdmpidE5qdEthTWEva1hEeTQ5WUdUWXJOZm9HWS9ZeXkzN0srdUgxVkNCUTZiWW5rTEd6VlVIMFcxYnhHNkoyckFid0tRamdYbEtzSW5BalNDNk9LQWlWSzJYSmJraFZJL1RlRUN0Tk5lb2FDa0ZhN3dQeGdUbDJwK2xkb2dBeXJ3N2d3d1ZRdzhlcWh0dnhiOWN2anNPQWdkbGlnM2xJTTRSbHJqSCtwOHFEVWJ2WjFoc1hycDhrTTZWbmFpUUc0aWIrMVJwWFBpNG9PYkNsMmk1K3FSRmY3Ymk0eFVBQ1MvMDRZZHVtRzIwVHhXU213c0MraTlqQWwwQjhib2JSTlpUQm4vcmVLV2NBMk9IaU5yWnRaUHM4Y245N1IwdWExdDVyRzRzbnZ4N2tZL1lvMFRuaFZHN1VzR0grekZXS0E4RDVrREdkV0F6MVJFdWEiLCJtYWMiOiJiY2FkYzExYzgzNzRmMzA3MzYwMTNhZWIxYzIwMGJiNjdjODYxNmUwNTU2OTkyZjM4MTdmMzViZGFhMDlhOGQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:51 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkZISjZYd24zYkdabzJXUkM1MUdvbkE9PSIsInZhbHVlIjoiNnBvbS8raTJzbkxycjhIejlEUjdKZ0JuclJRV2RlMjBqTk5zRFhsclJxaVVDZ0NBQmg5a0c3dEZaYmV1NEhhNmlOQWlqTFc2VCtkMGM3MXJYeFRJQVhVWkFTUStpTTQwbFFpMWw2U0cycVJvcEVRd0t2VTh5N252S21yVTRRdDJMQjRtQWVJQjB3cFpYYkwrRE00V3BCR1QvVHJZalVnRDNXN1ZFclJveVlSQ0JjNHZKNXFFeWFFMFJ1YmRhbkFISUNyRVlsVzRnN2lWbnZVdTJkQUZIdEM4V0hUdU8xYWl2Q0dva1VuVTJFMUJzQy9OeGY5SFRVaGJESWVZZGcwYWZXN2lxc2pZemoyY0VTd1lzSHBiTkhQT2RLZXlTR05CMVY1aXZWRUM0YUhkb2ZFMXZ3aFRmWExveXhzeVFJZUNOUS9OaFR2RFZOdVAvOTVVUGRrQzVydS9tY0pEZHltRHFLSTVEOGdiQUJRRVh6R0RsUVdsaHp2RTdOOURBbHlMdUMzWFhZTWp6L1NGRVdBL2ZNV0JtNVVITDBFZXdkb2gzMTBPOVFQMUtINE5reXBKMGVhQWJMMWl0RVZBMENzd0tBUzhEK1J2Rk0wMTFhOWV3NGtUN2JQK0RvREMweHFuZ3JNbElZcXdaazRSUmllanZsWXAvOFNzOU5zK1RrUFAiLCJtYWMiOiJjZTVlNmI1ZjAwOTEyNTIyZjU1YjBiNjE3ODlhZjg1NzJlM2U5YTIyYWQyN2M0Njg5Njk0NGI1NmRhYzIyYmZkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:51 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IklZT2Q0Z1dUcTNaQnlvUEpCNWhVL2c9PSIsInZhbHVlIjoiVnp5NzJaMnQzejJBbzRiZzR2MGlYUHdhdjk5emQ3Z1dta0g2SzQ4WVNuNUhPb0FqR0NmQiszQWdsNHQ0Uk1OQmhZRDY0bXhSeXMrQnhFbUo4a093eEVqdFM3Rm1MTC9uK0h1N2FxU2wrSjZ4cDRiQWd5ay9hNUliR0hUbVhRS2UxdHVkSWJyVFkxL2ZoSVdMY1NUR01uRi9lUVlYdFN0Z3EyTGlKaFowRWtjUjFISTFVdmpidE5qdEthTWEva1hEeTQ5WUdUWXJOZm9HWS9ZeXkzN0srdUgxVkNCUTZiWW5rTEd6VlVIMFcxYnhHNkoyckFid0tRamdYbEtzSW5BalNDNk9LQWlWSzJYSmJraFZJL1RlRUN0Tk5lb2FDa0ZhN3dQeGdUbDJwK2xkb2dBeXJ3N2d3d1ZRdzhlcWh0dnhiOWN2anNPQWdkbGlnM2xJTTRSbHJqSCtwOHFEVWJ2WjFoc1hycDhrTTZWbmFpUUc0aWIrMVJwWFBpNG9PYkNsMmk1K3FSRmY3Ymk0eFVBQ1MvMDRZZHVtRzIwVHhXU213c0MraTlqQWwwQjhib2JSTlpUQm4vcmVLV2NBMk9IaU5yWnRaUHM4Y245N1IwdWExdDVyRzRzbnZ4N2tZL1lvMFRuaFZHN1VzR0grekZXS0E4RDVrREdkV0F6MVJFdWEiLCJtYWMiOiJiY2FkYzExYzgzNzRmMzA3MzYwMTNhZWIxYzIwMGJiNjdjODYxNmUwNTU2OTkyZjM4MTdmMzViZGFhMDlhOGQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkZISjZYd24zYkdabzJXUkM1MUdvbkE9PSIsInZhbHVlIjoiNnBvbS8raTJzbkxycjhIejlEUjdKZ0JuclJRV2RlMjBqTk5zRFhsclJxaVVDZ0NBQmg5a0c3dEZaYmV1NEhhNmlOQWlqTFc2VCtkMGM3MXJYeFRJQVhVWkFTUStpTTQwbFFpMWw2U0cycVJvcEVRd0t2VTh5N252S21yVTRRdDJMQjRtQWVJQjB3cFpYYkwrRE00V3BCR1QvVHJZalVnRDNXN1ZFclJveVlSQ0JjNHZKNXFFeWFFMFJ1YmRhbkFISUNyRVlsVzRnN2lWbnZVdTJkQUZIdEM4V0hUdU8xYWl2Q0dva1VuVTJFMUJzQy9OeGY5SFRVaGJESWVZZGcwYWZXN2lxc2pZemoyY0VTd1lzSHBiTkhQT2RLZXlTR05CMVY1aXZWRUM0YUhkb2ZFMXZ3aFRmWExveXhzeVFJZUNOUS9OaFR2RFZOdVAvOTVVUGRrQzVydS9tY0pEZHltRHFLSTVEOGdiQUJRRVh6R0RsUVdsaHp2RTdOOURBbHlMdUMzWFhZTWp6L1NGRVdBL2ZNV0JtNVVITDBFZXdkb2gzMTBPOVFQMUtINE5reXBKMGVhQWJMMWl0RVZBMENzd0tBUzhEK1J2Rk0wMTFhOWV3NGtUN2JQK0RvREMweHFuZ3JNbElZcXdaazRSUmllanZsWXAvOFNzOU5zK1RrUFAiLCJtYWMiOiJjZTVlNmI1ZjAwOTEyNTIyZjU1YjBiNjE3ODlhZjg1NzJlM2U5YTIyYWQyN2M0Njg5Njk0NGI1NmRhYzIyYmZkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-960821618\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1455154148 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1455154148\", {\"maxDepth\":0})</script>\n"}}