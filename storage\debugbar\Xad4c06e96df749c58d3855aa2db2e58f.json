{"__meta": {"id": "Xad4c06e96df749c58d3855aa2db2e58f", "datetime": "2025-06-27 02:13:26", "utime": **********.417651, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990405.980056, "end": **********.417666, "duration": 0.4376099109649658, "duration_str": "438ms", "measures": [{"label": "Booting", "start": 1750990405.980056, "relative_start": 0, "end": **********.344121, "relative_end": **********.344121, "duration": 0.36406493186950684, "duration_str": "364ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.344129, "relative_start": 0.3640730381011963, "end": **********.417668, "relative_end": 2.1457672119140625e-06, "duration": 0.07353901863098145, "duration_str": "73.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48199824, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.00713, "accumulated_duration_str": "7.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.373633, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.02}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.383816, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.02, "width_percent": 6.592}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.396815, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 28.612, "width_percent": 8.976}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.3986, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 37.588, "width_percent": 6.171}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.402641, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 43.759, "width_percent": 33.52}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.4072049, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 77.279, "width_percent": 22.721}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-40484167 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-40484167\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.401774, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]", "pos": "array:2 [\n  2305 => array:9 [\n    \"name\" => \"بيبيرو أصابع بسكويت مغطاة بالشوكولاتة كرانشي - 39غ\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"id\" => \"2305\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"originalquantity\" => 4\n    \"product_tax\" => \"-\"\n    \"product_tax_id\" => 0\n  ]\n  2306 => array:8 [\n    \"name\" => \"ذرة بريتز\"\n    \"quantity\" => 1\n    \"price\" => \"5.00\"\n    \"tax\" => 0\n    \"subtotal\" => 5.0\n    \"id\" => \"2306\"\n    \"originalquantity\" => 52\n    \"product_tax\" => \"-\"\n  ]\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1709855878 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1709855878\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990356916%7C7%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjlkeDNCNHppT0VpVmhWSlBYQ0R6MHc9PSIsInZhbHVlIjoicStpVnU5RkRQTjc1Z28rbTJRYk1vcElLL2I3Yi9saFZoRTRGVXZNbDQ1YmhEWXBZL2ZkdURrYVNydkVPVTh0NW5RN1JkeDZxL0xqM3FtWGxpaTgyYXhPcVBRaFQreU41YzMxNW5heTMrVEhWMi8rdmdodkxWczBuc0VkUHg1cFRQdDVIbmQwdCs0VkwzdGh0R3NrVVZsZDh1Q0NhcUc2N2JrZlhkWmxLVDRnWEhJbkJUWThzVWNSYnJqYkRlQTdWRzVXVFJ3bE90cGJJSUxuUEtLUFZGY3RJS0cxeW9SeFNBaVlwT1R2bnR4Q1Q0Sks1bC9kSDdUOWg5ZGpsRjRkaG5ZVVV1MXJvWDlHWWpIUGhpKzZobWhvNS9FU2Z5QkJCRlp0WkRNK2hUVXpWeGQvdzkwUkRsKzkvNHk5WkZ1S20vNXJ5UzkvbWdSS2JZb3RuSWh3YnZlKzlUdEh5M2JpSW5CTmI1eWlSTG1DbnlnWVJ4K0puNE0ydmR6b2kxcWJMNU5pVTYxUEozZWlJQlhvYzNaYWV5MHpXNGd4a1lnUndUODlqYXE0U1pBSWhOWUdsck1SR3VEOWVXaGR6QnRyRUwwc1g0Y2lGWTFPbWdmQ3pzR21hVGYvb2RGMXl2QzdxSStEY1ZWcGFiMC9uUEpuZGRjckRNNkZTS3NNQ2ZWWk4iLCJtYWMiOiIzNmQ5MGJkOTI4YzMzMmFiZjYwMDU0Nzk0NTFkNzFjNzgxZmI0ZDkwZjcwY2JiNjU3NWYwNzE1YjVlY2FjZDg5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InIwSm5mM2ZJNEcxUUxmbjZRZ2tvZ0E9PSIsInZhbHVlIjoiTUFRYWw5TkFZMTdCRkRZdUVTS2h3V2YrUURocHM2djNENk5neGJDUEZoVXNTMnVCdHZZR09YYXh4M2xEbWRxcnlFTmtqWkQwdDBFM0U3TEhVMmRSWWxXRXNLSXVwZjAzNC9UZExsaitHUkx3U0dTbXhqOGxGT0c2VWJBcVRjVHZTRVNqeDF6YlE5MGE0dElFZ2VpU3VrWTVUZWtrSzMxdVJiSlRnVURvSU1xWnViRUdHMWtyQ1EzS2p5N1JSblhWeGNqM3JNcCtGclFDZG40NDRGNThWSitIc0t3OEFhTldybEVNQUtYak5nWnA3Qlc4VWk4UHlHSEQwUHdHeXk1ZVFad2pRcHY4NmxvODhiY2RJRmplT3pVNEo2amhLU2xkMGdPbkpIMHdjR3NnWlZaeTFMMzJncHBiQzFxWEtKRUpOYnhSYUJKRFM4eVJOVmtoYU9UTjdWZEc2bENBUEU2MDkxeU5TY2prQlJUNFpWMWltcXlMK0tnUkdmMU9zVDRiU1ZnY0FYRmFRSUVDdjVjL2hTSitkaEVzYldpRmtFSTNzZ2Q3RDc3cnZibk9ZMFFySENUdWZ0YnBqbDJHV0thd3o3Mk5JYlR5OXUrcGViZTZOV2xVZFhlci9GbHp3bk9TREV4Q1JWaGk3MUZ6bGFsR0pudlJxSy9hY3NBWGVMSksiLCJtYWMiOiI2ZGIzN2MzZjE0M2U1ZjdkZTc4NjUxM2M4YWZhM2YwZGYyZjY2YWIwY2JmMWZiYWMwZDkzYzU1ZmRhNmI3NjQ1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-493814678 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-493814678\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-873362043 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:13:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlIxTTVDQ25BY3BLRGsrZmtiVDBUOUE9PSIsInZhbHVlIjoiSVpldzd2TWhyL0k2dmtjSXVuQ0pBaHh3OUc5UVp6UUJtRzNaczZCOCtSSWxEL0lrQWNhSFJIUzNmQk1iTDdkZ1dYd0pYU2xNZmgxYlhSa0gxcFJxSEtRSU9yUEV1MkxIS2lqYk9DVktKT0JVcVh2Sllzcjhlc3ZRbXlMc2FrZ2swMXJJdTVZNG1hRVNJZUJwNXp1RyttY3pJSnVxV0JEcU83dUJtYTJ0OHRjdDBpU2dDaDFieSsrd3dpSGRKQUVGR0x0cTZUbzZSNUM3S1BrQjZMcGdBU2pzbkI0V3B4TENjbEFpNi8wS0lkT2I0aHYxN3JSUWxWM1MzR0tSSGtmME9wVnVFY1BlbTREOHZxTUpJcDZtOFE4T010bVhjbmdVY0RZWjVPaFZBeW41YTNlT25INXFqTlV6elkvTWd5UElldmsrLy9Dbm4vSEFqVjRmc3c3M1UxY2xEUFkyNlRIQ1JqWkRWZDdONzVnMFVnRnNIUlRSUUdXWmFpSXZvY1ZFaTYyZHQ0aDY2bGZKOGlxWEl4bzUrRCt0TndxUzhobXNJZmc4TEMyTGliTVNyb3lpNDA0eTdxZkJjWVRKc28rZExYYnRrcmxJLzg3bzJ3QkZlY09OVHArU3FLeDNPRWQ3Mktzb3IydGU1TzRQRldYckRwK0ozU0hpa1F5TWExRXIiLCJtYWMiOiI1YTNhZjRkYmU0OWM4ZWE4YzdiYWMwNzhkZmJkNzZmYjBlZmVkNGM0OTlmODgwZTg0OGQwMjdiMDFkODgzYzhjIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:13:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjFMQ29YU0pocEQ2LzI1TzJOUFUxeWc9PSIsInZhbHVlIjoidDNCYU8xMFlGaEtJdmRUK3VqSmE5RXkyN3UyVk1kWE5KQXhMUEUyZWh0TTFPQ3ZpaXd4TlBkVkFpZ1NhdFQ5dWowUExEd1hYckNFK3NXbXZBZUwrNlJxa2RlSGFiT0hWZTV5UnRlVWFXZ3ArRHlEeklabkJhbnVjSUhWbkVEaGRJRytWNGNZQmRsVUgzVzk4ZGczM0QwTm1yQnpVWjlzRHl2Vm9hM1pRYlNQOStrYlM2VzFXbWNEWm03a25hbmhXVzZjQWsxY2FENkFEa0Y3ZUpmVmxPUkp1S3RmMXduK0NHVHRuQkUxYS9uK2FPUnd3UGV0Q3hteFNNVmZFSFdlR08rK3NVd2F0MXg2U1NxUFNZY0ppMGs3WGNrNG5MMmg1dkh6MytVWmxlMi9RVUZNMXdIVHcvaDB1RUJtcTN2MTd0eHYvbTlBZzFZYzcxVlppeWhGdXk5dEpOTWNrMzlLZUNhTW02WG5LV1poL3pwTkJrejJ1NGVuTC80alQ3WDJvdVlOZnZtSWlnaW5aV08rc0x6ck1GbzF6S1pzeSs1NzV5U2VvSkpMOTlGOCt2bjZodWFpOUtuNnJBbEx3TDdtSnd5eGR4dEwyLzZDVW9tQlVDNEZSbmV0OXEzanlHSDdRbkR3bFBZek8wQ0hUYk1iU0lvRlFEM3h6bXNmQ1JIUVQiLCJtYWMiOiJkNzA0OTE2ZGNhNmZkZmIzMmNlODBhZDIyOWNiNjViMzJlNDQ3N2FjZjE5NjVhODlmM2U3YzY1YjIwMWJkMzliIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:13:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlIxTTVDQ25BY3BLRGsrZmtiVDBUOUE9PSIsInZhbHVlIjoiSVpldzd2TWhyL0k2dmtjSXVuQ0pBaHh3OUc5UVp6UUJtRzNaczZCOCtSSWxEL0lrQWNhSFJIUzNmQk1iTDdkZ1dYd0pYU2xNZmgxYlhSa0gxcFJxSEtRSU9yUEV1MkxIS2lqYk9DVktKT0JVcVh2Sllzcjhlc3ZRbXlMc2FrZ2swMXJJdTVZNG1hRVNJZUJwNXp1RyttY3pJSnVxV0JEcU83dUJtYTJ0OHRjdDBpU2dDaDFieSsrd3dpSGRKQUVGR0x0cTZUbzZSNUM3S1BrQjZMcGdBU2pzbkI0V3B4TENjbEFpNi8wS0lkT2I0aHYxN3JSUWxWM1MzR0tSSGtmME9wVnVFY1BlbTREOHZxTUpJcDZtOFE4T010bVhjbmdVY0RZWjVPaFZBeW41YTNlT25INXFqTlV6elkvTWd5UElldmsrLy9Dbm4vSEFqVjRmc3c3M1UxY2xEUFkyNlRIQ1JqWkRWZDdONzVnMFVnRnNIUlRSUUdXWmFpSXZvY1ZFaTYyZHQ0aDY2bGZKOGlxWEl4bzUrRCt0TndxUzhobXNJZmc4TEMyTGliTVNyb3lpNDA0eTdxZkJjWVRKc28rZExYYnRrcmxJLzg3bzJ3QkZlY09OVHArU3FLeDNPRWQ3Mktzb3IydGU1TzRQRldYckRwK0ozU0hpa1F5TWExRXIiLCJtYWMiOiI1YTNhZjRkYmU0OWM4ZWE4YzdiYWMwNzhkZmJkNzZmYjBlZmVkNGM0OTlmODgwZTg0OGQwMjdiMDFkODgzYzhjIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:13:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjFMQ29YU0pocEQ2LzI1TzJOUFUxeWc9PSIsInZhbHVlIjoidDNCYU8xMFlGaEtJdmRUK3VqSmE5RXkyN3UyVk1kWE5KQXhMUEUyZWh0TTFPQ3ZpaXd4TlBkVkFpZ1NhdFQ5dWowUExEd1hYckNFK3NXbXZBZUwrNlJxa2RlSGFiT0hWZTV5UnRlVWFXZ3ArRHlEeklabkJhbnVjSUhWbkVEaGRJRytWNGNZQmRsVUgzVzk4ZGczM0QwTm1yQnpVWjlzRHl2Vm9hM1pRYlNQOStrYlM2VzFXbWNEWm03a25hbmhXVzZjQWsxY2FENkFEa0Y3ZUpmVmxPUkp1S3RmMXduK0NHVHRuQkUxYS9uK2FPUnd3UGV0Q3hteFNNVmZFSFdlR08rK3NVd2F0MXg2U1NxUFNZY0ppMGs3WGNrNG5MMmg1dkh6MytVWmxlMi9RVUZNMXdIVHcvaDB1RUJtcTN2MTd0eHYvbTlBZzFZYzcxVlppeWhGdXk5dEpOTWNrMzlLZUNhTW02WG5LV1poL3pwTkJrejJ1NGVuTC80alQ3WDJvdVlOZnZtSWlnaW5aV08rc0x6ck1GbzF6S1pzeSs1NzV5U2VvSkpMOTlGOCt2bjZodWFpOUtuNnJBbEx3TDdtSnd5eGR4dEwyLzZDVW9tQlVDNEZSbmV0OXEzanlHSDdRbkR3bFBZek8wQ0hUYk1iU0lvRlFEM3h6bXNmQ1JIUVQiLCJtYWMiOiJkNzA0OTE2ZGNhNmZkZmIzMmNlODBhZDIyOWNiNjViMzJlNDQ3N2FjZjE5NjVhODlmM2U3YzY1YjIwMWJkMzliIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:13:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-873362043\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1957877011 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pos</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>2305</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"50 characters\">&#1576;&#1610;&#1576;&#1610;&#1585;&#1608; &#1571;&#1589;&#1575;&#1576;&#1593; &#1576;&#1587;&#1603;&#1608;&#1610;&#1578; &#1605;&#1594;&#1591;&#1575;&#1577; &#1576;&#1575;&#1604;&#1588;&#1608;&#1603;&#1608;&#1604;&#1575;&#1578;&#1577; &#1603;&#1585;&#1575;&#1606;&#1588;&#1610; - 39&#1594;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2305</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>4</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n      \"<span class=sf-dump-key>product_tax_id</span>\" => <span class=sf-dump-num>0</span>\n    </samp>]\n    <span class=sf-dump-key>2306</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&#1584;&#1585;&#1577; &#1576;&#1585;&#1610;&#1578;&#1586;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"4 characters\">5.00</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>subtotal</span>\" => <span class=sf-dump-num>5.0</span>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2306</span>\"\n      \"<span class=sf-dump-key>originalquantity</span>\" => <span class=sf-dump-num>52</span>\n      \"<span class=sf-dump-key>product_tax</span>\" => \"<span class=sf-dump-str>-</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1957877011\", {\"maxDepth\":0})</script>\n"}}