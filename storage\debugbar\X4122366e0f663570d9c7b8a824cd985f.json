{"__meta": {"id": "X4122366e0f663570d9c7b8a824cd985f", "datetime": "2025-06-27 02:23:58", "utime": **********.056976, "method": "GET", "uri": "/customer/check/delivery?customer_id=10", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991037.637742, "end": **********.056993, "duration": 0.4192509651184082, "duration_str": "419ms", "measures": [{"label": "Booting", "start": 1750991037.637742, "relative_start": 0, "end": **********.012385, "relative_end": **********.012385, "duration": 0.37464284896850586, "duration_str": "375ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.012393, "relative_start": 0.3746509552001953, "end": **********.056995, "relative_end": 1.9073486328125e-06, "duration": 0.0446019172668457, "duration_str": "44.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43898912, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/delivery", "middleware": "web, verified", "controller": "App\\Http\\Controllers\\CustomerController@checkDelivery", "namespace": null, "prefix": "", "where": [], "as": "customer.check.delivery", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=365\" onclick=\"\">app/Http/Controllers/CustomerController.php:365-378</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.0020900000000000003, "accumulated_duration_str": "2.09ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 33}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.046413, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 85.167}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 368}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.0510721, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:368", "source": "app/Http/Controllers/CustomerController.php:368", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=368", "ajax": false, "filename": "CustomerController.php", "line": "368"}, "connection": "kdmkjkqknb", "start_percent": 85.167, "width_percent": 14.833}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/delivery", "status_code": "<pre class=sf-dump id=sf-dump-1785701326 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1785701326\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-764053866 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-764053866\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1301690091 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1301690091\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991028607%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJYK0tqMHVhcXpaNW15U3JEUVBOeVE9PSIsInZhbHVlIjoiYmlZWG9NQUNyTVBBVk1INmY3Z1Rsbm5iaUV2ekdkZE5VV2hpV1FoQlg4bmZpZ2ZReUIwOTBUakFOU0pqMEtiYmRjMDdGYlNxQTNzRHJBSENlandzOGxZRFVPOFp3TVNQVVRSV2RoNXd0dy81VWVXLzBSMkVLWUNrcnZsejZIazFXTEVqWS9jU2w5V2szK0s3aXlRTVVuUElSaEkyeFQ3TGo0MitjRGJnUHJiVXY3M1MxZmZwU1hYb3Z0eDhpZC9meS9vSUp0dWN4SU9QeE5WRGoyeDUwcjVnMUxBK3ZzdlhHQUI4U2JYWlU4QlFQVHBBQTIzanRvYWhiOHBEYlNyN3hGaDUyZnd2dE8vKzFJRGZaeGR3dTgwM1J3VlNnOWQ2V0FlNGhhQzFvOUN1UWdldUZnc0E4NGJLWGxXeTBYTHRnOWN1T2owYmR2MWsreXpEeUNvei9mNWMxOStzR2dndzc2cm5yQkxxUzVhWlVYMTBDenlNVTV3ZTVSc0lqS3RZRk9qSkJpclZOUCt5a2taeVN3bHgxaWlHNkN3VTFmOWpLY2x6MHM4aVFnUWxvSFl3ZHNnZEdkRnZMcStOVjEvM21MYUtjU0h6UFJRejFXdmp1VjdOQWxla0owYTd3S3VLbTZ4dzg3ZGpNRzBJaXovOC9PZ2JhTE1GNGJ2eFhseTkiLCJtYWMiOiI4MjZhZDkyOWI1NjBlYjI3ZDU0NTY0YWQxZjBiMzg3ZDc0Mjc4NDFjYzgzZWUzYzBjMjE3NTg3NmI0ZDc5NjFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Ilc4YmxKdnhSczVvMmU5Sk9SWnJvckE9PSIsInZhbHVlIjoiVmx1c2tLU0x0aHIwYmUzOUhjSDZKNEM1YjQvbTF2SnczM2RaQVNMOU5HS054VEorajl0T2pKaVNqVy9MeWJDTVI3WncvV1BzMTR3NzJESHROUVhOaFFpMnF1dmp0K0dXV0lZUkxqd1M0TkZOSFRKM3ZDTSt3SFlEYy9kbUc4Q3FpbDd6VnUzSVJpZVNnVEFFT3paWWZTOEFCRFlhbkEzMjgzTFNmMldvQThzWFJWaDhLVkRMY2JOQzFKTEpHbytzQmxNK1N3U0JJcUo0cElSb2pQRU5DYzJUN1FzOUZJcXlqdWp0a0ZwQ1RHZ3dnRU1yQ3U1OHU4L0dsVm9pNHoxb2VXQnMvalArbkdhMGxaVENucDBNc2REWkR4cGJXbnR4eitSQ2ZHYTgvd3Z2Vi9tdGt2Y1VpMUF0MmFUc0R6dU92Rml5SHZuYWZxQmN6TmJUYzlEUWFBUWVBVEprQkowWVJ2ZnRYdXc2U3R3TDVkR3o5aXRQVEdrKzQxMm5zV3F6SDRYbkRuTUhWcXZ3aUFkYUNySDRMVG90anBDYkc5N1NGZlkzSEJ6dDd2OFRNR3FTbjlYQ2dvRTMxZHJKa3NreDBIWTNMMXREU2NSZFJYNG1PMjBWOGNRYmV3aCtsTU4rTjNOd2g0SVVSZ3RUUlBiUVhzbFZqYzhuZ3ZPVW5BcHYiLCJtYWMiOiI2YWZmYWQ2MTBmY2ZjMTExYzgyNzcyOTk2Yzc2MTBhZGM3Y2I2Y2Y3OWNmMWYzZmQ2YzZiZjY3NGUwNmI1ODRhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-332798414 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1VYnB2WVhwc05iYjhjSG52UVpzNnc9PSIsInZhbHVlIjoieFNwVTdnKzdlbldNNzAvSVN3djRFdXhvNjJBNXFyL2VKOUlBa2FjQ3BibDRjaHppOW1xZGh4ZitXRXpyRW1vNXE0dmJMNytQa29yU3BtUnJVa1Vaam8yQ0JpQkt0eHhWSHluNVI3Z2FPQThXR1FmcHRsSWRhcTg4amNqdFhTb0VKMjNaNi9BVTBaMWwwbzVDQWpUSEc1YUFRb0xHd0FoSHZENDVWMFpBb0RheHRHYnEyOFI4dVE5ZVpLckVhSW0wN1lQTi90czBHMENPNGJackFWNEVqQ2szZjdBbDI3TWZGakJWTENvYUlvSHdEaFpmc29Eek1qbnQ4UVlSVzFQdUl0WGdOYnBXazlQL0xrSW9OeldYMThGbjcxYnUzNmpUczB0MmJGUm9qbXBDSjdoRFBYMWxEaHdYQUtub2RjYWYzcnYrVFJvSGp6MVlkM1Yvakc5UjdqLzRRVFRoQTFjcHE2ZVM5YWYvKzZCQVBGZUpCV1JUYUJINWV2VFpNSlI4SnR3QUpDZWJoQ080SGZDbjBIQi9FKzdKamk4Sk1xWGZVWDlEZmdhQzZKMjJDRVFaMXpRNkJKLy9KSXpoNjNGeHkxWFoycldQQVBwM2I4SnlkenhJb2FlOEdKZ0c3RVZoUXBQLzJGM1RqWUlMR1RPZ2xLd3B1dzF2aXRhTUpTVDgiLCJtYWMiOiI4NTFiNzFmOTM0ZDMwZTAzOGJjMmY1MWI1YWJlMTg5MDNiZGVmYTkyNDZlNjc2MTA0YzE0M2ZiNjlmMWI5ODA2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:58 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik53aWpJWkV2clFQNmhVSzZkcWpQa0E9PSIsInZhbHVlIjoiYzZ1bzhQL08zUkViWnZ4b2hXcnd6cUw5N0pIR1Y1bEdSNUI3cDhrNDdrS1V2U1JCMTJYOXVCcUxscDRYcDZ6azhkeFJMS0Q4aXQrSnZmU0NQYkQ0SFFtNFNjWW9mVmZJQ2FFSE1KYmdSQmQ3NHpuYnh5QVdwMkFTYUlEckJmcDJJeTlYaGxPbnc0YlRReE5iL1JYOHVEL1VMNHNDWjc4aDVUQWQyUzBDNUpCalFNWlBMNjBWbzB2aWRPUE9hOFlwdE9QY3NSelFjVTlVQTdMSFk5OW9pN25IZElvcUZBSDVVZDBBRzYyTEUrdXRESUxvT05YZnhhWHFKMWh2L1NKU0hqN2sxVW1BbFBBZWRsdW41a2pIRXUzc0Vtbm9DNE45TTJ5bGh1NEZSWnd2czNrR3BFOUYva05pMENDSUk2aDJnVktQY3dnd0VOU2dIN2NxdHlQWUJpenRrOU5kL0UzMWlJVm5aOXBLRFMvVmduT0JLNVN2SzlRKys5Yjd6K2ZneEdYSlcwek5OMWEzTGpIbG1DRWpEcDFJQ0tBMVhseEg4N1FpbXpjM2s2NFJFeWQ1dVBFY1I3bCtuM253aHczTnFmRWlVdzhHRWx1clpTNkdjbzFhTGU2SU1WNXJEZmd2VURnMjV2bWlKbkN4MzFZUkFpWm5WYWM1djZhZG0vdmoiLCJtYWMiOiIzNmNkMzliMDlhZDMwNTVjZjY2ZDJhZGI3N2U0YjEzYjQwZDJhMDkwZGIxNTIwMzkzNTY0YWIwMDQ2ZjM3MWY0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:58 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1VYnB2WVhwc05iYjhjSG52UVpzNnc9PSIsInZhbHVlIjoieFNwVTdnKzdlbldNNzAvSVN3djRFdXhvNjJBNXFyL2VKOUlBa2FjQ3BibDRjaHppOW1xZGh4ZitXRXpyRW1vNXE0dmJMNytQa29yU3BtUnJVa1Vaam8yQ0JpQkt0eHhWSHluNVI3Z2FPQThXR1FmcHRsSWRhcTg4amNqdFhTb0VKMjNaNi9BVTBaMWwwbzVDQWpUSEc1YUFRb0xHd0FoSHZENDVWMFpBb0RheHRHYnEyOFI4dVE5ZVpLckVhSW0wN1lQTi90czBHMENPNGJackFWNEVqQ2szZjdBbDI3TWZGakJWTENvYUlvSHdEaFpmc29Eek1qbnQ4UVlSVzFQdUl0WGdOYnBXazlQL0xrSW9OeldYMThGbjcxYnUzNmpUczB0MmJGUm9qbXBDSjdoRFBYMWxEaHdYQUtub2RjYWYzcnYrVFJvSGp6MVlkM1Yvakc5UjdqLzRRVFRoQTFjcHE2ZVM5YWYvKzZCQVBGZUpCV1JUYUJINWV2VFpNSlI4SnR3QUpDZWJoQ080SGZDbjBIQi9FKzdKamk4Sk1xWGZVWDlEZmdhQzZKMjJDRVFaMXpRNkJKLy9KSXpoNjNGeHkxWFoycldQQVBwM2I4SnlkenhJb2FlOEdKZ0c3RVZoUXBQLzJGM1RqWUlMR1RPZ2xLd3B1dzF2aXRhTUpTVDgiLCJtYWMiOiI4NTFiNzFmOTM0ZDMwZTAzOGJjMmY1MWI1YWJlMTg5MDNiZGVmYTkyNDZlNjc2MTA0YzE0M2ZiNjlmMWI5ODA2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik53aWpJWkV2clFQNmhVSzZkcWpQa0E9PSIsInZhbHVlIjoiYzZ1bzhQL08zUkViWnZ4b2hXcnd6cUw5N0pIR1Y1bEdSNUI3cDhrNDdrS1V2U1JCMTJYOXVCcUxscDRYcDZ6azhkeFJMS0Q4aXQrSnZmU0NQYkQ0SFFtNFNjWW9mVmZJQ2FFSE1KYmdSQmQ3NHpuYnh5QVdwMkFTYUlEckJmcDJJeTlYaGxPbnc0YlRReE5iL1JYOHVEL1VMNHNDWjc4aDVUQWQyUzBDNUpCalFNWlBMNjBWbzB2aWRPUE9hOFlwdE9QY3NSelFjVTlVQTdMSFk5OW9pN25IZElvcUZBSDVVZDBBRzYyTEUrdXRESUxvT05YZnhhWHFKMWh2L1NKU0hqN2sxVW1BbFBBZWRsdW41a2pIRXUzc0Vtbm9DNE45TTJ5bGh1NEZSWnd2czNrR3BFOUYva05pMENDSUk2aDJnVktQY3dnd0VOU2dIN2NxdHlQWUJpenRrOU5kL0UzMWlJVm5aOXBLRFMvVmduT0JLNVN2SzlRKys5Yjd6K2ZneEdYSlcwek5OMWEzTGpIbG1DRWpEcDFJQ0tBMVhseEg4N1FpbXpjM2s2NFJFeWQ1dVBFY1I3bCtuM253aHczTnFmRWlVdzhHRWx1clpTNkdjbzFhTGU2SU1WNXJEZmd2VURnMjV2bWlKbkN4MzFZUkFpWm5WYWM1djZhZG0vdmoiLCJtYWMiOiIzNmNkMzliMDlhZDMwNTVjZjY2ZDJhZGI3N2U0YjEzYjQwZDJhMDkwZGIxNTIwMzkzNTY0YWIwMDQ2ZjM3MWY0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-332798414\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1662961437 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1662961437\", {\"maxDepth\":0})</script>\n"}}