{"__meta": {"id": "X260b9313766df95636c8796ceb522789", "datetime": "2025-06-27 02:27:39", "utime": **********.553684, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.082357, "end": **********.553701, "duration": 0.471343994140625, "duration_str": "471ms", "measures": [{"label": "Booting", "start": **********.082357, "relative_start": 0, "end": **********.48219, "relative_end": **********.48219, "duration": 0.39983296394348145, "duration_str": "400ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.4822, "relative_start": 0.3998429775238037, "end": **********.553703, "relative_end": 2.1457672119140625e-06, "duration": 0.0715031623840332, "duration_str": "71.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45394752, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02429, "accumulated_duration_str": "24.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.5118499, "duration": 0.023399999999999997, "duration_str": "23.4ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.336}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.5434601, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.336, "width_percent": 1.976}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.54633, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 98.312, "width_percent": 1.688}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-1720329325 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1720329325\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-2055236446 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2055236446\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-99618872 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99618872\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1145185035 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991245628%7C35%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImtIaVdNaXlKMDBoSTNLMW16NFBiV1E9PSIsInZhbHVlIjoiVGwxWFE0anpnN1c4bVBOa1UzOVY5c3JndFJkWkxmdmRLOC9qRHl2V2FHZGYyc3BlWEYxOUpUSXRhZUZMSzRFa09KS3lXc1JRYXZrTWprR2R3WWlaWlVhdGxFOTZWNHNHSytXZnpaVXhaN2psTFJsUFJxWW1CMmlvOHBLaE1JdStoREVSanlpS3VMWjkzUURjUG9waEs1YzZwRmUwUlVMUjVtVGhPSEQybVBuUUZRdHo4TmNiWWFlTVRNN1VkUG9SaU5JTG90VStXOGxsZlB4dmhOa1YxWWpmTHRaM21wNVMxYzZ0QkMrRGVQYmMrbWw4TVk0SGxDZC9kUml1a0pybWhNV3NzU1NnRUxUMWV5YTRxQ0lLaUw5amgzTlo4dE8zK1I4QUEvb2VmbG1UK1pwSERsYjMzR21IL0NhdjZjanR2U0hhZjJvY3ZBMVhHRXoyakZKcjdOM084RFF2MzdwRnMxN1Z3ODc0MU0rbVNnRUtWblpySE5JL3dvUWFmUHlrWGNBV2x3ak05eGg2YXYrSHhoQ2dGR1BkaWtMODR2MjdZMU16bllScWkxSytIaUs2dTRFWjhoSjFycGdJYVorQkJhWnZRWThRbVVYalhyUjhSUHBESFRRb29GeFZPUk5ibllXYnQraHNnNmR4Ykl4R0pwZVR4NFo5ZHl2d3J5eHAiLCJtYWMiOiJiNDkzNzk4N2FjYTBkNDcyOTYxOWFlZjRiNGRlOGI0YjBmYjFlOGRmZTJmMmZkMmY4NDYyNTQ0YmNhZDZhZjI5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6ImhFK056VGVIVDVuT1A4U1liZHJIZnc9PSIsInZhbHVlIjoibkp2dkNRbkpCcjRwMlNUcjhIV2JnU3RuY3VtMWlYV1p4RTdlZ0E4SVZCM2Yxc3A3U2t4dlpLUVFwSXFIMUFmVzJXNDNzdmMwZXNCZmhWT05XaGRYd29nb2ViWGFBaDZrcEg0cHB2bFk0VEw3VUgzRytpSGdCcGRiY0VEMis3ZnZ0NHUrRFdlbmZ5MnBGZGsySW53cmtKbDgrSUhkbTZXMU83VzJNa1FLVkxTbS9Db3FrQlVlZFVlYXltVGtFZ2lmQUt5SFI0YlJhd25qR1A3UGpZWmtnWUppT0pvRm9vMkE5Y2szVXd4NWkzRW9sYjlacS9RbmwzMkp1NWVPTWgwYk5wSmpsc01Pai9LSHd2UmMwL2VENGhOQVM1VDdITFduSklkU3NwM1ZzTSt2Mk54U3NEVGM4QXE3NlpZdGNCUC8rdzJOZ0UvSS9PZzI5Tzd5YWl3U1kzeGk0dVg4cG5pTC81eXgySERkekMzclhXT2ZsSGlaeXNJT1VNN2thYVBDU3c3amoxdUFnOGV4VE41d2RSdkNUMmdibk5ZR0w1cStkWHRMVVJDdDJydzVobDVLb0tTTDd2cmd2YU9Vem4rYVNWcEFFOVIrNlprQ241c0NRRDZGRzRmNjltWExNdk02TG1idG9RU2tGU2FQaWtvOVBIUUxBRkNkRmU5am8wYUIiLCJtYWMiOiJmZGU5YzRiOTM5NTJmY2ZhNWQ1NGFmMDJiMzc5M2U2ZmJkYjIzYWQ5ZmNkMjE3OTcyZDc0ZDgzNjQ3YzRiZGRkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1145185035\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-323050375 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323050375\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-234940280 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:27:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImdTZDRSNmRuQkdTeHFkYm03ZS9EU0E9PSIsInZhbHVlIjoiekxhT1d6QjN6T3VIaldmQ0RqdkQ4K1FDZHlQWHo4Q1NlNkMxNml1bEZIYThYaERyamN1Rm9UU1YwNFhCTzE0ZnZHazdYeVFEd0NyZ3JUdWtpNTFGQ1NjL05Id3I2SUtseU11b3BsbzVaUTNqcTl3V0hLYjZvS0lidGhseGpRNFJZaHhsbnRPWkFzQ2M5djhiOWpITWRMcEM1NXdNQTZFcUVFQzdRZ2NyMVBYTVdFOXByN1RjdEwzdVJJajkxMldSb1Z3RlNneitLR1R0bnFxb1NQSk1KZUE5ekUya0lYM0MvdlJuMjVmQTAwdDNvZUZ1QjYrSy9iK3daWHMrR0xqN2w2eFZtTHlnWjQ4aHlDcENtMCt1dnBXK3ZlTkxHcHVreU9iOWpmZGlMNDJobTJiZ1JCbWlpOEJIY3gzYzV0bnk4Q0QwS3drQnhlbXZMYmFBVEd1NDZXbDU3c0trdTRtb1hDKy9ZYU1mWmpHVUdVbUxZSHRhL2wwd3ZvSTFaNXkvRjZhVm82QjdPaHJmSEltWkJ6TUNPY05zTFZxMVlwT1QzVGlwTVFsWmNCK2pCSTlmOU84WHAweGlJZkNJTkZ2RFVVN1VvWG9NRm9wYVoyeXh6a05OQTlFNXFmUHU0Y0dXUTduMlFZMC9HKzBINmEyaDdkR0xXRU52SlZZdlhvZUoiLCJtYWMiOiI1NzJmODNlNTg4MDZkYjJhZTdjM2I3N2E1ODU0NTRiZDhjMjQ1N2UwNDdhY2VhNDM0MmE0M2E4NzJiMmVkNTU0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:39 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IitxcStsU0tEWjRxYjJqODJWeGVYTXc9PSIsInZhbHVlIjoiTXVxRzNTRnpPV0RIeStPVGFha3pMLy9naEZjUHUzaE9mWXlyL1dFcFdTYzZLSm9uRnhRWkdNaVFIcnFwbm4wd2g5Tms2aVc5OXNsQ1JiVml4NFhtd21OTjQ1SXBDb1RGK0s3UDdTaGRJeDB0STZZbTdjQW5xcmVYemRlRlRYRmZCNWFIVlZEakdjb1ptWXl6TW5pQUgvTFk4NHplN0tGaDF6Sitkb2VMV3pRbG4rZjFrbzM4djM5Z0RBYlhqTDVzTnp6TnY2SDd6M0w3VFVJaEJzdkx6UERRQkpYQ0tpVm1URnFqeFJJUmtNNkZDWDNnVW9PLy93bGY4MmtMT1JLV3lnNFBrbXpDU2wrWllOMUtyNCtWcTFkWnA0ekllWWQyMTBrd3FIRkFBeG1xbjRpV2JTK3V2ZUVyNGoxRFZiWXNMdE8wTkNVL1h4dTJQb0RRZDJ2R0UzS2xYMkNJSjAwcE5ycXpJb1BabkR4amNTZGZUZGN0WWdlUGp4WVI1U1hnQVp1TlV1akVFMkJHcHhwQ2VZT1g5cE9qTC8xcnRZMVNBSGh4am1SQTJsMHBaVjJRQ1BnSU1Uc2YyM3dJLzEwd0JmVEZrdGtmbUl2VHUvVG5rcXl5MzlhalJoTUtybVEwakJyS0tMVFJhSEszSUQzMm0xakp1THhTMjU3Vy9qS3oiLCJtYWMiOiI3OGVlZWQxYmMyNjY3OTM0YmIyYzAwNmY4ZDY2NDE4NjQwNjA0OWEwZDVmZWY5MjNhMjZhODlkMWU3NjQ0YWY2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:27:39 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImdTZDRSNmRuQkdTeHFkYm03ZS9EU0E9PSIsInZhbHVlIjoiekxhT1d6QjN6T3VIaldmQ0RqdkQ4K1FDZHlQWHo4Q1NlNkMxNml1bEZIYThYaERyamN1Rm9UU1YwNFhCTzE0ZnZHazdYeVFEd0NyZ3JUdWtpNTFGQ1NjL05Id3I2SUtseU11b3BsbzVaUTNqcTl3V0hLYjZvS0lidGhseGpRNFJZaHhsbnRPWkFzQ2M5djhiOWpITWRMcEM1NXdNQTZFcUVFQzdRZ2NyMVBYTVdFOXByN1RjdEwzdVJJajkxMldSb1Z3RlNneitLR1R0bnFxb1NQSk1KZUE5ekUya0lYM0MvdlJuMjVmQTAwdDNvZUZ1QjYrSy9iK3daWHMrR0xqN2w2eFZtTHlnWjQ4aHlDcENtMCt1dnBXK3ZlTkxHcHVreU9iOWpmZGlMNDJobTJiZ1JCbWlpOEJIY3gzYzV0bnk4Q0QwS3drQnhlbXZMYmFBVEd1NDZXbDU3c0trdTRtb1hDKy9ZYU1mWmpHVUdVbUxZSHRhL2wwd3ZvSTFaNXkvRjZhVm82QjdPaHJmSEltWkJ6TUNPY05zTFZxMVlwT1QzVGlwTVFsWmNCK2pCSTlmOU84WHAweGlJZkNJTkZ2RFVVN1VvWG9NRm9wYVoyeXh6a05OQTlFNXFmUHU0Y0dXUTduMlFZMC9HKzBINmEyaDdkR0xXRU52SlZZdlhvZUoiLCJtYWMiOiI1NzJmODNlNTg4MDZkYjJhZTdjM2I3N2E1ODU0NTRiZDhjMjQ1N2UwNDdhY2VhNDM0MmE0M2E4NzJiMmVkNTU0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:39 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IitxcStsU0tEWjRxYjJqODJWeGVYTXc9PSIsInZhbHVlIjoiTXVxRzNTRnpPV0RIeStPVGFha3pMLy9naEZjUHUzaE9mWXlyL1dFcFdTYzZLSm9uRnhRWkdNaVFIcnFwbm4wd2g5Tms2aVc5OXNsQ1JiVml4NFhtd21OTjQ1SXBDb1RGK0s3UDdTaGRJeDB0STZZbTdjQW5xcmVYemRlRlRYRmZCNWFIVlZEakdjb1ptWXl6TW5pQUgvTFk4NHplN0tGaDF6Sitkb2VMV3pRbG4rZjFrbzM4djM5Z0RBYlhqTDVzTnp6TnY2SDd6M0w3VFVJaEJzdkx6UERRQkpYQ0tpVm1URnFqeFJJUmtNNkZDWDNnVW9PLy93bGY4MmtMT1JLV3lnNFBrbXpDU2wrWllOMUtyNCtWcTFkWnA0ekllWWQyMTBrd3FIRkFBeG1xbjRpV2JTK3V2ZUVyNGoxRFZiWXNMdE8wTkNVL1h4dTJQb0RRZDJ2R0UzS2xYMkNJSjAwcE5ycXpJb1BabkR4amNTZGZUZGN0WWdlUGp4WVI1U1hnQVp1TlV1akVFMkJHcHhwQ2VZT1g5cE9qTC8xcnRZMVNBSGh4am1SQTJsMHBaVjJRQ1BnSU1Uc2YyM3dJLzEwd0JmVEZrdGtmbUl2VHUvVG5rcXl5MzlhalJoTUtybVEwakJyS0tMVFJhSEszSUQzMm0xakp1THhTMjU3Vy9qS3oiLCJtYWMiOiI3OGVlZWQxYmMyNjY3OTM0YmIyYzAwNmY4ZDY2NDE4NjQwNjA0OWEwZDVmZWY5MjNhMjZhODlkMWU3NjQ0YWY2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:27:39 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-234940280\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1131398183 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1131398183\", {\"maxDepth\":0})</script>\n"}}