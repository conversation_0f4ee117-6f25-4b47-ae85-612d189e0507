{"__meta": {"id": "X3913ea8ef31010b38d8d7d00193c339e", "datetime": "2025-06-27 02:12:36", "utime": **********.835359, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.403947, "end": **********.835373, "duration": 0.4314258098602295, "duration_str": "431ms", "measures": [{"label": "Booting", "start": **********.403947, "relative_start": 0, "end": **********.763232, "relative_end": **********.763232, "duration": 0.3592848777770996, "duration_str": "359ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.763244, "relative_start": 0.3592967987060547, "end": **********.835375, "relative_end": 2.1457672119140625e-06, "duration": 0.07213115692138672, "duration_str": "72.13ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45736192, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.019219999999999998, "accumulated_duration_str": "19.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.79246, "duration": 0.018269999999999998, "duration_str": "18.27ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.057}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.819535, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.057, "width_percent": 1.769}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.825044, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.826, "width_percent": 3.174}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1930416970 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1930416970\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1342191608 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1342191608\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-305704286 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-305704286\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-443375660 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990354161%7C6%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InJlWTVnZ0RGdjhvek80MkM2SlQvaWc9PSIsInZhbHVlIjoidWE5bVBxblNOcGhoQUVsb1kvbkgzbVZId1RqN3dRcWlpK3lSSnVsS25KZlhTblJSeWtxd3I3MEVUWGgyck12T3BOUGxjSVhZbHhhTG9FU3Bvck5Famo3RXZUOGlTbHh5NHFjaHpSUHZPYWx1dFhJeWxiR1J5cWpJVW02MVRpZDRPckNDYlVrbCtkK3RRN1gxZEFabU5BeXNjRjY1dTQxSytnZ2M5TitDQUUrdXJ5alpQTmVKdFlCdzBjYVBlOE0wQTNpeGVUL3VYZDBVejBtM3FtRHVtd3VDNk9iV1VkTUhlNE0ydVJOaWljTmZmOWdrREJORzFTOVpaTitUTXVLbFRSTVFSenlqa1VXaXB2OGtuMWlZU0hUYVdoUERuQm82NkFCWm45R0NiRFBLYTZnL3FyU243OFJwWGVVSjBVSUprbWM3VVlHdVFIVGJXKzRzVElCM0tFV0NWZWVWbEhkRHVGcmRZSVA2V1VCMWVlaEtsMlZBTTF1TlhIeXRWSm1QdG8xZHF1RjU3SjNORzdyUzhyT3kzSElQcjh4SlBYM0F3QWFOdkxnYk9jZXdMalhCdHdnK0RDYjA4Q1dYUmR0NlNBb040cU9obkNxdWQ4Z05sbGtzTXlZN1Q3TjY2NVA5QjBKbktyTU1MVmFWakdETWp1YUtQdDh4VkxZYjJqRjciLCJtYWMiOiJjMjcxOWFmMmZmOTA1M2ExMjQ2ZWQ2NDYyOTgzNGU3ZTA2YWY0MjJhYWRmZTFiYjY0NDNhZDIyOTcwOWIwMTg4IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlV0eC9HaXpReXp1dUF2QzhJems3UHc9PSIsInZhbHVlIjoiS0JhNXFXeEFVdDc1Wk9PdFhUdVlaNUNUa3lWSW5ESWlBcnVMbFRQNUVHY2E2d3dUWDF2cnZqa0MvRWt4ZlJTc2ZKWjJzc0k5elYxS3paZnFvOTNBNVpFL3A4dU9yMkNDUnFoN2crNWJmbStMWFFsQjE5c1dwWnlmZlVFTzdIWCtHUzRqMGZkZ2dGdzdBQ2FNVE9tbENYdVNVVFN0a056NHJpaW5aMEpOSUdwWmtzS2pMYzBKWURQZkUrTUl1S1Q5MVExWUwraXBXeXlTU205SkJzYVlQU3hUQ1F5SEk0WmphMGV5aEJjdHhBeFBYenJiZXp0UUNSTW9hMmx2UDdhQkJjeG4zdE1SbG0yVnhsZkVvemZPNG5wMFlxMEFZUWJsNjVUVzltcHJGcFZNaDg5Y2pxNnNHNTJtWTBpeU9McEhCK0hjQlk4TE11bGliSHJBa1pJWEp5MnhFWlZ4czdZMlNTRlFoaEFTV0gwUnpsSWE0M29GNXN6ZEJhQ090ZFM5b3pWMXVpZDlvWnlkMVB4RThDWXZXY0pGbm1vN2w2TGtXUG92SnNwOXhnUkIzaTh2bzd4M1NlWkxscjhGU0RKdS80dHpBSkpFOTdtMmo2ZGxkZFpJUlFIcFhZaU44QUdjZFROTG5xUkFkWG9sT1liR3NMeHVEaEVpRmhSdUlIRTMiLCJtYWMiOiJiM2UxZWFlNDhmZTU1MDk2ODYyNjQ0MDJmZDYzZjkxODEwODFiNmQ0YmY3NTM5MWQwOTE1ZTdmNWEwZDkwZTFkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-443375660\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-641867722 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlNmRElBLzdwNmZyR1VUT01nQ3hBc1E9PSIsInZhbHVlIjoidFdva0NGaXhiTHV3VHZCWmtwdjRsd3prY0RlQjZGWFZHeEZJRWJMamRnSWVMejR3WFBTTDBCYitDTDJyYmNzcUZRQ0RtVXlXM1p4YzJMV21xUUN6MUM4MGFIa1Rob05yTFo0aXNoT1dNVUFMN01ib1RURWt4N2FHd2FvdWNhRFhEUXVONEpSSi9FWjI4LzRrdjVWb2NEREV3YVZiMUJKdzFuMk9yajUrQ3haVnprd29TRGo1SjE4REtmbHc3RFI3NkFsSXR6ankwRU5EdVZNYkwzUXFHK3laUUdjbHhPZzhybW16MUM0dStEdC91cS9TK0VvR1Y4V1c3MmpKVGp1Tk9sZWxiK2tabCtUNGFMY3NHL1hHU1JrQmNtODlxcmx0N3lEZCtJU3I5NTlxanpVLy9RYlN1QWNUQytoanlsNWVoZUd2Y0dvbURjUjNkY3MxOXBUbkJlUVVBZXNpbWRFS0ovTkJhQVQwWXVtTnlFeEdRUCtwSk1Jb05xVmxCOW9QYzdZcUJMdzI3YmVBUFJyRExaRjQzKzRGR2c4ZDh5NzVjUVl3WkVMTVpKejQxSWxCMEhmWlB3R01ubE9XVmNRRkphWWxmMmUxOFNTRmRhRlRHb3U1eW1heVhLUVJMZEtXREpsOWQrMU0rSURscjNTTGppKzV1Zzlqelc5ZXhvYWYiLCJtYWMiOiIxMzY4MWI3MTBhNTYyOWJlMmY1Zjg4NzQxODQzNzNlNmVmMzc1MDAxYzJkZjA4OTczMzExYThkMTFlOWMyMzFlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJ1RVJrMkVSaWVIcDBFZHZWWkxKdmc9PSIsInZhbHVlIjoieWZPekZjQmZZSE82N2NEZVN1QStMRU5ZWDBUR1pkZDdKbk0rdXVUcGJpdmxCTWI1d0VnYWIxeTc1Tll0dnBZVytUMks0akgwYjdLckNqQ05iU0c1aVpWOGNhSC9qcHh1MVBNanZCeitOYnBudkRheG9NcWxmeU05REdhTVRxc0luZXd6VEdKWDlLQ1B0c211S3NYd2M5Y252YWRYMGQzN2VWTFdzZ2tMbm1Tak5YWmlEaGpZWmN4YVhWQnUrYlc4Z2c5eFpOamVpcFFDbWVDeGk1YXhjQkRNSTJXVTB4OGp5UUMyTjZ0UVVyTEdyNVNBLzM4TWRZSWsybGE3NUxWbmZEUk5ad0VwY082djRVSzNjRlFlaHM2T3dwK0ZJMkZYdXZsSWZDQjdDdm1Kem90dEI5d0t4RmlCNE40V1RnS3FHSTdZUTduMHh6WnoydlBoeU5OREs4V1F5b0NLallIN3RCazNuTW9ZRVlLN2NXa1pSTzFKaFAxU2x3cnVENWVXckJzRldGREVrVW9abFZtZThJOVF3dVVwNDlqV0o2RGlma2EvQVI1ZFBKZnFpckowZjd5aFhmemRlSVpDcjFWTUpKSUFZRWI3UmxuU0VpdHEybDhlamdoZkNWZ3M4Nm5IblEwc01JZ2FlU0MrR2NienNYQ1ZSWkUvWHdadTVZcGoiLCJtYWMiOiI4NzM5YWYwZThjNWY4ZTJjY2JhOTE0MjZjYmZmNTMwOGFmNjE2YjcwOWVjNjk0YjUzMDg2MGZmMDQxYzUyODQ0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlNmRElBLzdwNmZyR1VUT01nQ3hBc1E9PSIsInZhbHVlIjoidFdva0NGaXhiTHV3VHZCWmtwdjRsd3prY0RlQjZGWFZHeEZJRWJMamRnSWVMejR3WFBTTDBCYitDTDJyYmNzcUZRQ0RtVXlXM1p4YzJMV21xUUN6MUM4MGFIa1Rob05yTFo0aXNoT1dNVUFMN01ib1RURWt4N2FHd2FvdWNhRFhEUXVONEpSSi9FWjI4LzRrdjVWb2NEREV3YVZiMUJKdzFuMk9yajUrQ3haVnprd29TRGo1SjE4REtmbHc3RFI3NkFsSXR6ankwRU5EdVZNYkwzUXFHK3laUUdjbHhPZzhybW16MUM0dStEdC91cS9TK0VvR1Y4V1c3MmpKVGp1Tk9sZWxiK2tabCtUNGFMY3NHL1hHU1JrQmNtODlxcmx0N3lEZCtJU3I5NTlxanpVLy9RYlN1QWNUQytoanlsNWVoZUd2Y0dvbURjUjNkY3MxOXBUbkJlUVVBZXNpbWRFS0ovTkJhQVQwWXVtTnlFeEdRUCtwSk1Jb05xVmxCOW9QYzdZcUJMdzI3YmVBUFJyRExaRjQzKzRGR2c4ZDh5NzVjUVl3WkVMTVpKejQxSWxCMEhmWlB3R01ubE9XVmNRRkphWWxmMmUxOFNTRmRhRlRHb3U1eW1heVhLUVJMZEtXREpsOWQrMU0rSURscjNTTGppKzV1Zzlqelc5ZXhvYWYiLCJtYWMiOiIxMzY4MWI3MTBhNTYyOWJlMmY1Zjg4NzQxODQzNzNlNmVmMzc1MDAxYzJkZjA4OTczMzExYThkMTFlOWMyMzFlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJ1RVJrMkVSaWVIcDBFZHZWWkxKdmc9PSIsInZhbHVlIjoieWZPekZjQmZZSE82N2NEZVN1QStMRU5ZWDBUR1pkZDdKbk0rdXVUcGJpdmxCTWI1d0VnYWIxeTc1Tll0dnBZVytUMks0akgwYjdLckNqQ05iU0c1aVpWOGNhSC9qcHh1MVBNanZCeitOYnBudkRheG9NcWxmeU05REdhTVRxc0luZXd6VEdKWDlLQ1B0c211S3NYd2M5Y252YWRYMGQzN2VWTFdzZ2tMbm1Tak5YWmlEaGpZWmN4YVhWQnUrYlc4Z2c5eFpOamVpcFFDbWVDeGk1YXhjQkRNSTJXVTB4OGp5UUMyTjZ0UVVyTEdyNVNBLzM4TWRZSWsybGE3NUxWbmZEUk5ad0VwY082djRVSzNjRlFlaHM2T3dwK0ZJMkZYdXZsSWZDQjdDdm1Kem90dEI5d0t4RmlCNE40V1RnS3FHSTdZUTduMHh6WnoydlBoeU5OREs4V1F5b0NLallIN3RCazNuTW9ZRVlLN2NXa1pSTzFKaFAxU2x3cnVENWVXckJzRldGREVrVW9abFZtZThJOVF3dVVwNDlqV0o2RGlma2EvQVI1ZFBKZnFpckowZjd5aFhmemRlSVpDcjFWTUpKSUFZRWI3UmxuU0VpdHEybDhlamdoZkNWZ3M4Nm5IblEwc01JZ2FlU0MrR2NienNYQ1ZSWkUvWHdadTVZcGoiLCJtYWMiOiI4NzM5YWYwZThjNWY4ZTJjY2JhOTE0MjZjYmZmNTMwOGFmNjE2YjcwOWVjNjk0YjUzMDg2MGZmMDQxYzUyODQ0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-641867722\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-383135727 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-383135727\", {\"maxDepth\":0})</script>\n"}}