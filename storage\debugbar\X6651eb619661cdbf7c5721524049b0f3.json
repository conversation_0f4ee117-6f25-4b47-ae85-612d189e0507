{"__meta": {"id": "X6651eb619661cdbf7c5721524049b0f3", "datetime": "2025-06-27 02:23:57", "utime": **********.632214, "method": "GET", "uri": "/customer/check/warehouse?customer_id=10&warehouse_id=8", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.23616, "end": **********.632229, "duration": 0.3960690498352051, "duration_str": "396ms", "measures": [{"label": "Booting", "start": **********.23616, "relative_start": 0, "end": **********.580236, "relative_end": **********.580236, "duration": 0.34407591819763184, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.580244, "relative_start": 0.3440840244293213, "end": **********.63223, "relative_end": 9.5367431640625e-07, "duration": 0.051985979080200195, "duration_str": "51.99ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45183880, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET customer/check/warehouse", "middleware": "web, verified, auth, XSS, revalidate", "controller": "App\\Http\\Controllers\\CustomerController@checkWarehouse", "namespace": null, "prefix": "", "where": [], "as": "customer.check.warehouse", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=383\" onclick=\"\">app/Http/Controllers/CustomerController.php:383-397</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00298, "accumulated_duration_str": "2.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6120682, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.128}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.6224432, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.128, "width_percent": 14.765}, {"sql": "select * from `customers` where `customers`.`id` = '10' limit 1", "type": "query", "params": [], "bindings": ["10"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/CustomerController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\CustomerController.php", "line": 388}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6253471, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CustomerController.php:388", "source": "app/Http/Controllers/CustomerController.php:388", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FCustomerController.php&line=388", "ajax": false, "filename": "CustomerController.php", "line": "388"}, "connection": "kdmkjkqknb", "start_percent": 83.893, "width_percent": 16.107}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/customer/check/warehouse", "status_code": "<pre class=sf-dump id=sf-dump-1273639292 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1273639292\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-692937894 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>customer_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-692937894\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-861798640 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-861798640\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1014796488 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991028607%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IndVSHpBdFovd2s0bFpjeEJpU3BXdXc9PSIsInZhbHVlIjoiNmtHTC9Cc1BYelhKa21MWWZOaWpSS2J2LzJOenAzUWhxZ2ptSGNNc2l0dFRnN2w2cThJU1FuYkJjK3ROdDlISjA1VzVDeUFYMVhIcGRIZlIzeUJPVXRoMUV2YkNkZlE3UnZCQnNkcDk3cFlnUjVGMFd3SDlEMVVXWldRcEdQU1QxdHA3OTR5WUZveHBpaHk2bkJKaGpzdWlXelUrTXc1UGU1QnR1TkJvaG9OeStxbE5Jdk04cVI1bm0ya1BpenhVS2lHSUdjcE8xazRYUDd6eFczSXpNWkpHeGtCMzFKOTVZY3ptQmFCaDFSUkZyNTRQR0c2bEVSb2JnaUpOams1aVd0RGhnelU3K2tnL3RudThhN3JhR2VFVVUvdTNlWWJJb3JKNFhFL0J3MGFMVm9HVHZ1NlVUUHpiUFgrSTczd2Q1Uk5SWXlqZE9UUjhleXpsaGQwZ2svMlU3WWw3YXhGVDRZSFk1SHlRTjRNTUpGczZDZFNMaTg3SVBFYThHa0d3WUZ4R1Jyd3JMcnB0ajBIalNtdzNrR1JEU0Y1SjR3MGE0ZE8zcDhFYTZ0S3ZNbnFUYTJLc1pqbnVxM05aRUtzbUFMZXBZSkE1L1Jrb2RVZmE4RDdlc09PSGtQcERyTC9qWFVNdE5xYXF4RTEyY080cFNCN1pWV3BwUXdteG5NM3IiLCJtYWMiOiJhMTBlOTIyYmFhM2YzYTEyNDM5ZGE1NTQ4ZjI3ZTU0ZjUwNjRiN2RkZWQxOWYzZjU1MWZmNmRlOGEzZDBmNzcxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Im1zMHNiYjNDblpaMlg5M09nUEJLdnc9PSIsInZhbHVlIjoiakhvWnQzeGJ6VmtwRDk2RDczN3E1RmoybnpGcVNtenZsWW8yZ1lBUTZJM1hSTVN4MzVMM2V1czBSM2NpWHhKUTV4cjVZdVRpdlVTM3RjdndRbUVsb0x4LzdnZkVqV0lSTlJtdkE3UmtEWkRRRnZkeWgwQytvSG81UzJJeGFwaEx4VW5BMkpWd2tsSE9zaUpNTS91MXEwWXNIVlhweFRqYmpUdnZ1NkNxR1V0K3RRNnhMci9wRmsrbEJMZnRYa3U3ZXJBUFNlUWxpMmtqcllVVXFHVlpXeVNrRUdzay9pcit0QWNEamw0NjIwRS9RQW41Y3JqclREdUFsK3l4dVpHL1YyRWZQV3ZOcXJ4cmZSOXhqL0xqUjJlVVh1bnlUc3ZqRUc3WG5Obyt3TWNOWGtwdG0ybGovY3FaMTdqUTVIMmtneDdzYjYrYnlmRVh0L3Q1RWtBMlBtTkd0dVNTMU9wUG9FeXFIRkRReXlPMUFwLzd0b0o0emcxbzd3TkVYdnZ5U0w2RUx2UUFLbkM1K0diNXpZQUZ3dlBQc01EeUdvNWhBbngvTVp3MUI2VHJ6ZXJuYmc5MzlzWG4vTFJnaGN1ODBZSlNaZ3dRaTM2N2hJVkNjcForU0pCYjUyNVNaWmpRWFZ5azh4M0w3UDF4UlprN3ErN2IwV2NZTFoxOE9VY1AiLCJtYWMiOiJkODdjZmU4ODExY2FlNjJiMTZmZmNlNjg1MmYzMmJjMWIzYjk2OThhZWMwMTMzMTBlMjY5N2UzN2EyMTYzOWRiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1014796488\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1096686210 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096686210\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InJYK0tqMHVhcXpaNW15U3JEUVBOeVE9PSIsInZhbHVlIjoiYmlZWG9NQUNyTVBBVk1INmY3Z1Rsbm5iaUV2ekdkZE5VV2hpV1FoQlg4bmZpZ2ZReUIwOTBUakFOU0pqMEtiYmRjMDdGYlNxQTNzRHJBSENlandzOGxZRFVPOFp3TVNQVVRSV2RoNXd0dy81VWVXLzBSMkVLWUNrcnZsejZIazFXTEVqWS9jU2w5V2szK0s3aXlRTVVuUElSaEkyeFQ3TGo0MitjRGJnUHJiVXY3M1MxZmZwU1hYb3Z0eDhpZC9meS9vSUp0dWN4SU9QeE5WRGoyeDUwcjVnMUxBK3ZzdlhHQUI4U2JYWlU4QlFQVHBBQTIzanRvYWhiOHBEYlNyN3hGaDUyZnd2dE8vKzFJRGZaeGR3dTgwM1J3VlNnOWQ2V0FlNGhhQzFvOUN1UWdldUZnc0E4NGJLWGxXeTBYTHRnOWN1T2owYmR2MWsreXpEeUNvei9mNWMxOStzR2dndzc2cm5yQkxxUzVhWlVYMTBDenlNVTV3ZTVSc0lqS3RZRk9qSkJpclZOUCt5a2taeVN3bHgxaWlHNkN3VTFmOWpLY2x6MHM4aVFnUWxvSFl3ZHNnZEdkRnZMcStOVjEvM21MYUtjU0h6UFJRejFXdmp1VjdOQWxla0owYTd3S3VLbTZ4dzg3ZGpNRzBJaXovOC9PZ2JhTE1GNGJ2eFhseTkiLCJtYWMiOiI4MjZhZDkyOWI1NjBlYjI3ZDU0NTY0YWQxZjBiMzg3ZDc0Mjc4NDFjYzgzZWUzYzBjMjE3NTg3NmI0ZDc5NjFhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ilc4YmxKdnhSczVvMmU5Sk9SWnJvckE9PSIsInZhbHVlIjoiVmx1c2tLU0x0aHIwYmUzOUhjSDZKNEM1YjQvbTF2SnczM2RaQVNMOU5HS054VEorajl0T2pKaVNqVy9MeWJDTVI3WncvV1BzMTR3NzJESHROUVhOaFFpMnF1dmp0K0dXV0lZUkxqd1M0TkZOSFRKM3ZDTSt3SFlEYy9kbUc4Q3FpbDd6VnUzSVJpZVNnVEFFT3paWWZTOEFCRFlhbkEzMjgzTFNmMldvQThzWFJWaDhLVkRMY2JOQzFKTEpHbytzQmxNK1N3U0JJcUo0cElSb2pQRU5DYzJUN1FzOUZJcXlqdWp0a0ZwQ1RHZ3dnRU1yQ3U1OHU4L0dsVm9pNHoxb2VXQnMvalArbkdhMGxaVENucDBNc2REWkR4cGJXbnR4eitSQ2ZHYTgvd3Z2Vi9tdGt2Y1VpMUF0MmFUc0R6dU92Rml5SHZuYWZxQmN6TmJUYzlEUWFBUWVBVEprQkowWVJ2ZnRYdXc2U3R3TDVkR3o5aXRQVEdrKzQxMm5zV3F6SDRYbkRuTUhWcXZ3aUFkYUNySDRMVG90anBDYkc5N1NGZlkzSEJ6dDd2OFRNR3FTbjlYQ2dvRTMxZHJKa3NreDBIWTNMMXREU2NSZFJYNG1PMjBWOGNRYmV3aCtsTU4rTjNOd2g0SVVSZ3RUUlBiUVhzbFZqYzhuZ3ZPVW5BcHYiLCJtYWMiOiI2YWZmYWQ2MTBmY2ZjMTExYzgyNzcyOTk2Yzc2MTBhZGM3Y2I2Y2Y3OWNmMWYzZmQ2YzZiZjY3NGUwNmI1ODRhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InJYK0tqMHVhcXpaNW15U3JEUVBOeVE9PSIsInZhbHVlIjoiYmlZWG9NQUNyTVBBVk1INmY3Z1Rsbm5iaUV2ekdkZE5VV2hpV1FoQlg4bmZpZ2ZReUIwOTBUakFOU0pqMEtiYmRjMDdGYlNxQTNzRHJBSENlandzOGxZRFVPOFp3TVNQVVRSV2RoNXd0dy81VWVXLzBSMkVLWUNrcnZsejZIazFXTEVqWS9jU2w5V2szK0s3aXlRTVVuUElSaEkyeFQ3TGo0MitjRGJnUHJiVXY3M1MxZmZwU1hYb3Z0eDhpZC9meS9vSUp0dWN4SU9QeE5WRGoyeDUwcjVnMUxBK3ZzdlhHQUI4U2JYWlU4QlFQVHBBQTIzanRvYWhiOHBEYlNyN3hGaDUyZnd2dE8vKzFJRGZaeGR3dTgwM1J3VlNnOWQ2V0FlNGhhQzFvOUN1UWdldUZnc0E4NGJLWGxXeTBYTHRnOWN1T2owYmR2MWsreXpEeUNvei9mNWMxOStzR2dndzc2cm5yQkxxUzVhWlVYMTBDenlNVTV3ZTVSc0lqS3RZRk9qSkJpclZOUCt5a2taeVN3bHgxaWlHNkN3VTFmOWpLY2x6MHM4aVFnUWxvSFl3ZHNnZEdkRnZMcStOVjEvM21MYUtjU0h6UFJRejFXdmp1VjdOQWxla0owYTd3S3VLbTZ4dzg3ZGpNRzBJaXovOC9PZ2JhTE1GNGJ2eFhseTkiLCJtYWMiOiI4MjZhZDkyOWI1NjBlYjI3ZDU0NTY0YWQxZjBiMzg3ZDc0Mjc4NDFjYzgzZWUzYzBjMjE3NTg3NmI0ZDc5NjFhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ilc4YmxKdnhSczVvMmU5Sk9SWnJvckE9PSIsInZhbHVlIjoiVmx1c2tLU0x0aHIwYmUzOUhjSDZKNEM1YjQvbTF2SnczM2RaQVNMOU5HS054VEorajl0T2pKaVNqVy9MeWJDTVI3WncvV1BzMTR3NzJESHROUVhOaFFpMnF1dmp0K0dXV0lZUkxqd1M0TkZOSFRKM3ZDTSt3SFlEYy9kbUc4Q3FpbDd6VnUzSVJpZVNnVEFFT3paWWZTOEFCRFlhbkEzMjgzTFNmMldvQThzWFJWaDhLVkRMY2JOQzFKTEpHbytzQmxNK1N3U0JJcUo0cElSb2pQRU5DYzJUN1FzOUZJcXlqdWp0a0ZwQ1RHZ3dnRU1yQ3U1OHU4L0dsVm9pNHoxb2VXQnMvalArbkdhMGxaVENucDBNc2REWkR4cGJXbnR4eitSQ2ZHYTgvd3Z2Vi9tdGt2Y1VpMUF0MmFUc0R6dU92Rml5SHZuYWZxQmN6TmJUYzlEUWFBUWVBVEprQkowWVJ2ZnRYdXc2U3R3TDVkR3o5aXRQVEdrKzQxMm5zV3F6SDRYbkRuTUhWcXZ3aUFkYUNySDRMVG90anBDYkc5N1NGZlkzSEJ6dDd2OFRNR3FTbjlYQ2dvRTMxZHJKa3NreDBIWTNMMXREU2NSZFJYNG1PMjBWOGNRYmV3aCtsTU4rTjNOd2g0SVVSZ3RUUlBiUVhzbFZqYzhuZ3ZPVW5BcHYiLCJtYWMiOiI2YWZmYWQ2MTBmY2ZjMTExYzgyNzcyOTk2Yzc2MTBhZGM3Y2I2Y2Y3OWNmMWYzZmQ2YzZiZjY3NGUwNmI1ODRhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1631990169 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1631990169\", {\"maxDepth\":0})</script>\n"}}