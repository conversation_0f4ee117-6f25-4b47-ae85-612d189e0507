{"__meta": {"id": "X18f3af6e9c5f911304c10653a96b0ebe", "datetime": "2025-06-27 00:42:34", "utime": **********.749894, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.29177, "end": **********.749909, "duration": 0.45813894271850586, "duration_str": "458ms", "measures": [{"label": "Booting", "start": **********.29177, "relative_start": 0, "end": **********.684331, "relative_end": **********.684331, "duration": 0.3925609588623047, "duration_str": "393ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.68434, "relative_start": 0.39257001876831055, "end": **********.749911, "relative_end": 2.1457672119140625e-06, "duration": 0.06557106971740723, "duration_str": "65.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45042392, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.018449999999999998, "accumulated_duration_str": "18.45ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.710453, "duration": 0.01748, "duration_str": "17.48ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.743}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.737426, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.743, "width_percent": 2.493}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.743016, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.236, "width_percent": 2.764}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IldRRC81RUVsZzFVOWxWVUpqcFdBYnc9PSIsInZhbHVlIjoieFl3WWZYQlVoeldJekJZL04xdThzdz09IiwibWFjIjoiYjU3MmM5ZTA0OGIwYzNmMDVlMTJjZTM0MGUxNTJjYjllNTJiYjNlZmMzZWU4ZWFlYWIyMTMwYTFlNGE0ZmE5ZCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-2143562154 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2143562154\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1236462482 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1236462482\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1536368865 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1536368865\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-26570891 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IldRRC81RUVsZzFVOWxWVUpqcFdBYnc9PSIsInZhbHVlIjoieFl3WWZYQlVoeldJekJZL04xdThzdz09IiwibWFjIjoiYjU3MmM5ZTA0OGIwYzNmMDVlMTJjZTM0MGUxNTJjYjllNTJiYjNlZmMzZWU4ZWFlYWIyMTMwYTFlNGE0ZmE5ZCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750984849771%7C62%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InNlM1IzK3RDZzd5K1ArRHptK0dqb3c9PSIsInZhbHVlIjoiZ3R0TURwTE1WQTFvNytCL3RqU2k2Vno2bDRTamtrcWl3NzFkU1AxQzYrWFBxSklNakVwWGF4KyttVXFFV3pKSnFnTnA5TkI0SHFoNDBtSER0N2YrM3FPSHZRc0ZVQXF0RFR6cVQxQ2pidDc3c2hCTExMWVFNRUlYSERDZ2pyUFkrOUNYUmJRY1Z5ejdEMkVGaVQ1Wnd0WDRKTnl3cUlBUHFuajQ0aGphTDlSMkx3NVVmWFMwMnpxM3lESFp4eFI2cHhYSUlxeEFzTEc3dzNFekQ5NmdyZ3FhNVM3N25ocWt3bkpkV1Z5L0lUK2lxd25hNGlaRHVsaWFvbmRPZDN5cTgvQkhwOE1MUEluQzgwN012a3N4NElOQ2hxc294b3pVT1Z1YjUyTm95bGxvNUJ3RjBoQlh2bCtFWkM2Zm1wZDRka0ErNmxzNDJLYVpqL2w0VldTSzM2Skg1K2VqaVZmaXdjbU55d2V2bXpkd2kzdFV3RVgvbHhZRFVUUlFmaW5pVkRUSUtNcFBZeTBnUVRGNjJTd3pubDdtd1lzUXBLSXFpYWpsZm9nSWMvZm5TNzNwNTk5TnFoVGxIbWJ1Ujh4NE94MGhwV3RYajkrOUIyN2E1Y1pkWXZYS0hwTTBVWTNFdmJhUXlwUUw3dlNvV3pxS01NMXh6bHVVMjdodE1zbHMiLCJtYWMiOiI0ZTNjMWM4Nzc0YmNjMzM5YmJjOGJhY2ZlM2QzMzNjOTBmNGFiZWE2Y2ZmNTZjNTQyOTQ5ZjQzMmQ0M2IwZmVkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkZnWU0ycEpUaCtheTc4RjJzL3V2QUE9PSIsInZhbHVlIjoib3J1RWFYczhiVG5lc1VQVytsVTRwRUp5aXpHbXhmVFRVUGxGbHpXYXBOMDQ4M09mMXR2a3NCVTFXZ1RydHJQaDU2bW8rU1JNOStjUlBFQUgxSTZnL2c5ZmRwRFpXYzEwY0ErR3hraVBWdDlsanpEd2hjY24wQXZSMXZvYjVFNTZwRnpvOXpqQmVBQXMydEdMRzhPU2h4Y1hpanJRRk9CYmtIR0YwRnRwYjRZZHNLQVl5M2NtZHBhTXMybWx0ZndiZjB3M0FKYWN1K2w0RjFmT3lPRG0weWI2UHdCbTgySmRVdGFZaTVqbHZxOXhDVkRUR0d1UVBkVmdhdjJ2V01lRDVzTG9PZmZKVGNFWXVjbGNLa3JaSnVZeCtKeis1bFZMQ1paNE5rakVZaUpuUWZyNG5LckJ5WjFKRTJVZHhEL3ZJT29VU0wyWHUrNVgydHJJZ0psWGZPMm9aV3pNa2REdUtrRExmdUhBSEtUeC9tOHQ5eUpDWHhpNnAxRFJ5YmlDaW90RS8rVUM0R0M4ZmNjaTREbUlHVlF6dFRjVGJaRlFNSjNOdHUvUzBsNHlRT00rekQxbklIeEp5ZjJzcG83SlNSemtZMWVXRVQramdFM3M4VkpRc0U2QUh5R3psSkMya2RscVhWT1FDMy8xeFhBREtUTjU1blU1aHJtUzQ0dG8iLCJtYWMiOiIyZTZmYzE2MWY0MTFlODYyNWZjMDE5YzA3MzcwNzIzYjZhY2Y2OTFlYTk1ZWIwMTY4YjI4MjUzZDVjYzM2NzNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-26570891\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1864155665 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864155665\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-618784872 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:42:34 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjNUdVRQL3dnank0a25pb1FHZFlIdWc9PSIsInZhbHVlIjoicjNQRlNmbFpueVV1MFdQbDM2YUVDSkNnZk04NUVGb1pGMWROMVQxMldONlVTQU5Mbld5ODdKMHJJQ0pqd3NpaUxMb1Z3SHYzL0xldGZWOUlzUW8xT3BOdElJTWc5MWVlNEI4Qm43Vm1VaVd1TTBmL1RVZUdoQWh1TDNERzJBR3NCakxaYndWaFIwTjV2Ukw0VGs0R1RmenllLytHNjlkdFRkdWtteWhhalVNb2Z6cmx6anFKZVBiL1pSVU1Oanl5d2FCQk5pQXQ0WDQyTEJLWVlGdlR3aVI1eE1GUDc5OU8zVjBCTzZOa21LYWRhV3ZackszdkIzVUU3Szl6OThQTUFBd2w3cHUrOVJWYzQ1SVBpL3JZWityZklEZGJqS1AzeFYzd2UvdjFzZFVzenU0U3pFaWg4VzVtd3o5NVpMNDIyUzZFb3o5WmNNeWZmT2c1a0I4ejkvdW5zYUc4cC9YSy9sRTVOTTRzRHJtd0I5anhOQ1NRUEdBekpobzFQd2h0KzZBUmRnRlFSbWw4SCtrWktEeWlRLzdBWHNkZjlCaGRYRmoycWdHZ1BKTThlUE43Ni9RZ1VHR0Q0ZVh6anV6ZVdvR2cwL2RZZzNzT0R4MWNsendmdDhoaHB3VGJPMkRsVSs2N0RZSXZQRUlTOFQ1bm96V3k1V2l1dGxlOWk1ZWwiLCJtYWMiOiIzYjIzZjkwM2RiZmM2OWE1MTJjMGFjZjEwOWNlYTNkZTFiYmY3ZmJhZjRmZDg1ODVlM2JjOTdhOTZmMzU0Y2NlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:42:34 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlJOL1BHRWVGQTdNUHJBRVJKZGVMbEE9PSIsInZhbHVlIjoiWnRSK1JCK016bnp3MkVFL3lOZjBhNlhZL0U5aWhIVm4vVDBwSTVJamtKRzJnZzZkRkJpaU90aFFTeno1VVZHK2MyUS9QR3VRWXgrMVVUSU1DbmpNcVdsQVhjcEVnaXJqTkpZREtYbW4vbStmWGs5ZHFwVXZGQ2VMaDY5emxET05weURwZzVvd0o0bEpUdXVBWjdZNk82NlJMSnJTSlJIT3FTd2FqWndWOFZpNmwxUWNZWWtrczhaTXZVaC9oVDRldHhncTJVRGxmUElGU0tBS0N4UDdvWHd5bUZtUTBNWDg5UFQ4cGU5c3pBN3FGcVBUUnRPOTZZK2V2dnhFb09HOXIxRmg1eWxCWlJ2ZTZ5aTUwQ21NSVcrRklSOWhZaDV3K0pOc3FaWVV4NjBOOGQ2NzdEUDl3akFCWGRtTUdrK2RDYkxjWGF5OEpaenlwWGZlMElvQ2FhRjRHSFprYzZGMVdneDBINmN6NXhjNFZOUTBodEVQMjdza1laYlhpU2xTVXpSMVc1N3RnWEhpNmNBQUpUNVllYVVEVVFRNzlLR3hxbzZxaVBWNDlpYXJxSXZUTzVaQ1E4d3R4M3VzbkdnMFRtSzkwQytoYUE1SVo3Sktla2Y5b0tJQ3VDVlVzMTlOK2w0V3JTNjYzdE1ra3VRMzVQT0NFTUNBdVoyQmJ5YkwiLCJtYWMiOiIwYzg2NTgzNDcxZmJhZmM0NmQyZmQwOGFjMTAwOWNkZDk5MTUyODZjMzBkMDQ0NzY5Y2Y3ODRkYzY5NTYxZjgxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:42:34 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjNUdVRQL3dnank0a25pb1FHZFlIdWc9PSIsInZhbHVlIjoicjNQRlNmbFpueVV1MFdQbDM2YUVDSkNnZk04NUVGb1pGMWROMVQxMldONlVTQU5Mbld5ODdKMHJJQ0pqd3NpaUxMb1Z3SHYzL0xldGZWOUlzUW8xT3BOdElJTWc5MWVlNEI4Qm43Vm1VaVd1TTBmL1RVZUdoQWh1TDNERzJBR3NCakxaYndWaFIwTjV2Ukw0VGs0R1RmenllLytHNjlkdFRkdWtteWhhalVNb2Z6cmx6anFKZVBiL1pSVU1Oanl5d2FCQk5pQXQ0WDQyTEJLWVlGdlR3aVI1eE1GUDc5OU8zVjBCTzZOa21LYWRhV3ZackszdkIzVUU3Szl6OThQTUFBd2w3cHUrOVJWYzQ1SVBpL3JZWityZklEZGJqS1AzeFYzd2UvdjFzZFVzenU0U3pFaWg4VzVtd3o5NVpMNDIyUzZFb3o5WmNNeWZmT2c1a0I4ejkvdW5zYUc4cC9YSy9sRTVOTTRzRHJtd0I5anhOQ1NRUEdBekpobzFQd2h0KzZBUmRnRlFSbWw4SCtrWktEeWlRLzdBWHNkZjlCaGRYRmoycWdHZ1BKTThlUE43Ni9RZ1VHR0Q0ZVh6anV6ZVdvR2cwL2RZZzNzT0R4MWNsendmdDhoaHB3VGJPMkRsVSs2N0RZSXZQRUlTOFQ1bm96V3k1V2l1dGxlOWk1ZWwiLCJtYWMiOiIzYjIzZjkwM2RiZmM2OWE1MTJjMGFjZjEwOWNlYTNkZTFiYmY3ZmJhZjRmZDg1ODVlM2JjOTdhOTZmMzU0Y2NlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:42:34 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlJOL1BHRWVGQTdNUHJBRVJKZGVMbEE9PSIsInZhbHVlIjoiWnRSK1JCK016bnp3MkVFL3lOZjBhNlhZL0U5aWhIVm4vVDBwSTVJamtKRzJnZzZkRkJpaU90aFFTeno1VVZHK2MyUS9QR3VRWXgrMVVUSU1DbmpNcVdsQVhjcEVnaXJqTkpZREtYbW4vbStmWGs5ZHFwVXZGQ2VMaDY5emxET05weURwZzVvd0o0bEpUdXVBWjdZNk82NlJMSnJTSlJIT3FTd2FqWndWOFZpNmwxUWNZWWtrczhaTXZVaC9oVDRldHhncTJVRGxmUElGU0tBS0N4UDdvWHd5bUZtUTBNWDg5UFQ4cGU5c3pBN3FGcVBUUnRPOTZZK2V2dnhFb09HOXIxRmg1eWxCWlJ2ZTZ5aTUwQ21NSVcrRklSOWhZaDV3K0pOc3FaWVV4NjBOOGQ2NzdEUDl3akFCWGRtTUdrK2RDYkxjWGF5OEpaenlwWGZlMElvQ2FhRjRHSFprYzZGMVdneDBINmN6NXhjNFZOUTBodEVQMjdza1laYlhpU2xTVXpSMVc1N3RnWEhpNmNBQUpUNVllYVVEVVFRNzlLR3hxbzZxaVBWNDlpYXJxSXZUTzVaQ1E4d3R4M3VzbkdnMFRtSzkwQytoYUE1SVo3Sktla2Y5b0tJQ3VDVlVzMTlOK2w0V3JTNjYzdE1ra3VRMzVQT0NFTUNBdVoyQmJ5YkwiLCJtYWMiOiIwYzg2NTgzNDcxZmJhZmM0NmQyZmQwOGFjMTAwOWNkZDk5MTUyODZjMzBkMDQ0NzY5Y2Y3ODRkYzY5NTYxZjgxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:42:34 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618784872\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-655924852 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IldRRC81RUVsZzFVOWxWVUpqcFdBYnc9PSIsInZhbHVlIjoieFl3WWZYQlVoeldJekJZL04xdThzdz09IiwibWFjIjoiYjU3MmM5ZTA0OGIwYzNmMDVlMTJjZTM0MGUxNTJjYjllNTJiYjNlZmMzZWU4ZWFlYWIyMTMwYTFlNGE0ZmE5ZCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-655924852\", {\"maxDepth\":0})</script>\n"}}