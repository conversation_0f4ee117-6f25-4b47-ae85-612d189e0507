{"__meta": {"id": "Xb761527c0033a8fa79c05fd08dc25eec", "datetime": "2025-06-27 00:14:50", "utime": **********.664937, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.119817, "end": **********.664953, "duration": 0.5451359748840332, "duration_str": "545ms", "measures": [{"label": "Booting", "start": **********.119817, "relative_start": 0, "end": **********.586573, "relative_end": **********.586573, "duration": 0.46675586700439453, "duration_str": "467ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.586584, "relative_start": 0.4667670726776123, "end": **********.664955, "relative_end": 1.9073486328125e-06, "duration": 0.07837080955505371, "duration_str": "78.37ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45047928, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.0038499999999999997, "accumulated_duration_str": "3.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.630009, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.74}, {"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.643047, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.74, "width_percent": 11.948}, {"sql": "select * from `migrations`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\rachidlaasri\\laravel-installer\\src\\Helpers\\MigrationsHelper.php", "line": 29}, {"index": 14, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 40}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 16, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.650558, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MigrationsHelper.php:29", "source": "vendor/rachidlaasri/laravel-installer/src/Helpers/MigrationsHelper.php:29", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Frachidlaasri%2Flaravel-installer%2Fsrc%2FHelpers%2FMigrationsHelper.php&line=29", "ajax": false, "filename": "MigrationsHelper.php", "line": "29"}, "connection": "kdmkjkqknb", "start_percent": 71.688, "width_percent": 14.805}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.657189, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 86.494, "width_percent": 13.506}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "1", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1944160519 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1944160519\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1402048601 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1402048601\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1790847644 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1790847644\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-139105233 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/users</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983282490%7C45%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ik1OS1BteUZsMno3S0NWM3laQzBLYlE9PSIsInZhbHVlIjoiand1WktFbjMwTk1TeExFdWZhVVljM0tTaDIrbkNVUU9nREtrNENsaU9HRDhoa3hubmlhcUtYU2VQOS9LNGVLaElxN0pTT2ZlMzI4Rm1KbzRDblRvU1lsL1g2L2JZc0k0aXlXV2QwNGJnRUZQOG51SjNnc3BlSGdVT055T2trUmtkUU80cFJvQmpYb1VzNjk5VzBaUE81SERDemV5K2xpa3VPZEFCK29FSm16YmtHWi8xL0c1ZWpLU2g4SUFFU2FleUkrTHFHUkRIY2JtTjZKL2ROMVNDSThCWTJhTlRnenpKd091V3RGbExDK3pJaUJSRDR0WUhUeVRSWG9hc3pTUHloUCs3bDg2RXQ0R244WlRPV21hQ2RPbHFVN2owaXZ6Vm9uNzFiWXozZDd0ZzRUcU5tNFcrVFBIZTlyc3ppalk5eVluamc4cUl0YVZDZWdCY2Z6T3VwKzk0cHJ3cXhmNVJ4TEh4b0ovcWU4bUh4cXFSbVFEM2FwLzllb0pwclVKWGpvYnVFVXRkODRiRXp3V25WeTNXTFN1enh6MmNOcHZobnlFVUVWSWEzTldDWVh6cDVwQkxScmIrKzd0bmIzcXBRcnNFVnR2UjM0ZDc3THJzdXZKN2pwc2ZYSUg5dWI5VWlRUGVuZ1FIN2w2R0pjQkhtdXhuL0lCOEZNc3RIdGgiLCJtYWMiOiIwNjM5YjFkZmRhYjE0MGYxOGUwY2ZkNTc2NjYzZjRlODZiZDY5OTk0OGFiZDQ0YTRlODQ2NWUxZDk5MWRlMTJiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlN4ZStaRkZwcFZtOUZTOXNtSFp4ZGc9PSIsInZhbHVlIjoiYmwvd2Z5UUFYZCtYZWZvSzF4YmhVU1dZMDBPVDFDZnY1MWlVQ3V3cURnQjlPWW9ncmtUajlEVXpPdU93eUY5SzRxTE11TnRnRDM0Y1BvdjVxQXVVQ2VYREs0b3FsUDhJdmUxYTU2ME9nUFlDUFM0eDJCYWZMb2Y0OE5ORHRwUEYrNzZnYVdRQ082cTRyTnZCUjJzaUdIdEdwbUxnN3dwMG5iSWRKdmtVMmdzeFN6eTAzR1VOT25vK0x6d2FYZkVMZHdiN0xYK21USTNDR0trN09ESXhRT1ZNTjZQTFEzNnRyNGhnc3lBNytOR1ZLUnYwU2paWHcyWDBzZDcrZVEzUTJnV1kxQkowZThTZHhZL1VJME9GL2NoS0hyUThxVDh3cVVjVmlJTStVOU42Z083YVgxMUllckhXU3Z1d1R0M2hOUjJ5NnRoMCtQdEV3NWVNTUVZaUJJb2tEdVJYckhIVk5PS1dIMFJubXpFb0c4T0J4MUpZeEJ3MlRkdjhrRFdnazNoNWk3Rnl1TytqVTRQMVJGcTR5ZVlheUIzTzhoZEh3WEdLUkNIaFpsOGVsREdDaGVpTnhrVm5BeGVoOVAyYXlqL3RldmN5OHhmTHUyemdaTUpCck1ldUJqSWIrRXA1Q0d3aTdFanhEZWJXSEJObXhvdThwQUdXTFhSbklYV3UiLCJtYWMiOiJlYjdiNzlmODQyZWNhZDJlZjQ0NDFiNjYwOTYxMGZiMzRlNGI1NWQ4MDlmOTYyMWY0NDU2ZDk3OWZiMjkyZTE5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-139105233\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1010772950 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ENi0gvuqNOdQwgNCESiLXJmLDaUcW5vVM2lUnOdd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010772950\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1809497631 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:14:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InVTWHdQckg0YjhDSEJUM0prd1VJNnc9PSIsInZhbHVlIjoibkQvZVA4c0JIREFVejM3VWF1TWl3Wmo0U2JvMFFpQ1E0YXRTNi91OXpqcnUrNlhYWHNXY25IbVZRUWlhdFNoUGJjZnJLYVlUTVo5RWhBTll1L0diVDRSNXkwQnM2R3BOSmFHWnZacStBa3pDYW03VDdSQVNjeVRzak9lZ3dMTkltUnhRTlBLZmlTbjVJRDQ4TjZURDVzejljd3A4bnhveC90MTI1YkUwMHVPRnpWNk1zZ2FtbG1rQmZYeFhmc0tiK3F0NnA2TXhIM1BtQXpuUTBFczFkVmtmVHd6amJWUE1jcmdLL042MGVncTV6eTl1ZlhiMzBUbmxrOWs2UGpOV3RnNXRnSjY2S1FMTDFPWkxpdkhOY3FUZ3lpc0dTdUx2cTIvRDI0L2JpMTVKZ0k5MjJyRDhQMGJtZ1FOekI5NloyWlpxeEhSVU0vMUZtaFRJUWxQR2E3cDhGWU9SRUNPOUhEZTc5enp0Tm1lRXpReXQ2ZTdYSHRaaERoQy8zZ1NJWXAzNjUvK09qalRpcjRMT0xIbGlFNktXWU5GL0ZteUc0WEJiN0tDYXBZR2ZJTVBuTEJ1KzlTN09iZUdOYzg5UktUTG9UZXk1b25iQ2VhNTZnUnpZVHc5WlFqRDhjMEpKb3lqRFFtQnFmWE45Nk14dUQrN0hVT3BMT1pUL2pHbjAiLCJtYWMiOiI4OTdlYzBjMjFjNzAwZDdhYzZhYjMwMzViMzdhMjIyMjFjNzI1N2VhYzc2MDk3NjI1ODRlNjYxZjAwODMwMTIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:50 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImNlTkFSMTlNVVhCWmhVM0FESGZac2c9PSIsInZhbHVlIjoiZ2JkaHVwV2d5R1UzQ1BvK1JTWTlTS0djQ3hDbzRWelhVODRYTmpCc1N6QzFMZ2RmUHQ3eWZLVVpLaldTTkpvakRvU2Zrb0xJRTRyQzNDaDNWdmp4WlVweXdqNml3aHRsSldDOTVlS0xHZGJLRVd1L0FSc0hoR1NiTk4yMlg1VEVEQXFvVWFSNVhGSWdtaHk5SUt2V3I1dDdKZ1ZQdUp3M3NmUHZBRG9pVVNuWW45Ny94cVVJanprUFRlaU92a1ZSTlVUS2dHNENNcFBjM0JVTFc2ejNWcnN2RVdtY2oxUGVNUVI5R2xUYXU4WlRmZWF3ejZmQVNDN0RJZXhTK2JsWTg1dXNGY0Y5WGc4VWlNZG1SR0J6dldFazRTbm83YnpnT3pRZWFOZWhXYXdvZjZFVDRadXBSVUltdldsMkM0bHdzV2tndWczdkx2cFdlSS9jR2lyL2JUc1JNcS9xQm9tZEhvaHBuZVJ4YjFvRDBiTmNzMDcxcnFnWFY4SXVURmpmdGxLR3F1OXZPbEtudFFCVGlOY2lwYU8xVFdOZTJnTldLU2NsczBHM20xK0hJemtZUkczdWMyWWZNUExHajlKSUpyWGJKUlFMVkEvemZGaFpaY01mdVhhSlIzN0J0UXhBdVFqQnZzVysrRDlvSkdoU1dyOTFOaTF5dUp5dmNYamgiLCJtYWMiOiI1YjEwMDAxNzJkZGZhNjQxY2ViNTk4M2EzYjdkODZlNGMwOWM4MzUzZDA2NjkzODM1YjVmMzFjYWI4ZDU3MGQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:14:50 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InVTWHdQckg0YjhDSEJUM0prd1VJNnc9PSIsInZhbHVlIjoibkQvZVA4c0JIREFVejM3VWF1TWl3Wmo0U2JvMFFpQ1E0YXRTNi91OXpqcnUrNlhYWHNXY25IbVZRUWlhdFNoUGJjZnJLYVlUTVo5RWhBTll1L0diVDRSNXkwQnM2R3BOSmFHWnZacStBa3pDYW03VDdSQVNjeVRzak9lZ3dMTkltUnhRTlBLZmlTbjVJRDQ4TjZURDVzejljd3A4bnhveC90MTI1YkUwMHVPRnpWNk1zZ2FtbG1rQmZYeFhmc0tiK3F0NnA2TXhIM1BtQXpuUTBFczFkVmtmVHd6amJWUE1jcmdLL042MGVncTV6eTl1ZlhiMzBUbmxrOWs2UGpOV3RnNXRnSjY2S1FMTDFPWkxpdkhOY3FUZ3lpc0dTdUx2cTIvRDI0L2JpMTVKZ0k5MjJyRDhQMGJtZ1FOekI5NloyWlpxeEhSVU0vMUZtaFRJUWxQR2E3cDhGWU9SRUNPOUhEZTc5enp0Tm1lRXpReXQ2ZTdYSHRaaERoQy8zZ1NJWXAzNjUvK09qalRpcjRMT0xIbGlFNktXWU5GL0ZteUc0WEJiN0tDYXBZR2ZJTVBuTEJ1KzlTN09iZUdOYzg5UktUTG9UZXk1b25iQ2VhNTZnUnpZVHc5WlFqRDhjMEpKb3lqRFFtQnFmWE45Nk14dUQrN0hVT3BMT1pUL2pHbjAiLCJtYWMiOiI4OTdlYzBjMjFjNzAwZDdhYzZhYjMwMzViMzdhMjIyMjFjNzI1N2VhYzc2MDk3NjI1ODRlNjYxZjAwODMwMTIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:50 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImNlTkFSMTlNVVhCWmhVM0FESGZac2c9PSIsInZhbHVlIjoiZ2JkaHVwV2d5R1UzQ1BvK1JTWTlTS0djQ3hDbzRWelhVODRYTmpCc1N6QzFMZ2RmUHQ3eWZLVVpLaldTTkpvakRvU2Zrb0xJRTRyQzNDaDNWdmp4WlVweXdqNml3aHRsSldDOTVlS0xHZGJLRVd1L0FSc0hoR1NiTk4yMlg1VEVEQXFvVWFSNVhGSWdtaHk5SUt2V3I1dDdKZ1ZQdUp3M3NmUHZBRG9pVVNuWW45Ny94cVVJanprUFRlaU92a1ZSTlVUS2dHNENNcFBjM0JVTFc2ejNWcnN2RVdtY2oxUGVNUVI5R2xUYXU4WlRmZWF3ejZmQVNDN0RJZXhTK2JsWTg1dXNGY0Y5WGc4VWlNZG1SR0J6dldFazRTbm83YnpnT3pRZWFOZWhXYXdvZjZFVDRadXBSVUltdldsMkM0bHdzV2tndWczdkx2cFdlSS9jR2lyL2JUc1JNcS9xQm9tZEhvaHBuZVJ4YjFvRDBiTmNzMDcxcnFnWFY4SXVURmpmdGxLR3F1OXZPbEtudFFCVGlOY2lwYU8xVFdOZTJnTldLU2NsczBHM20xK0hJemtZUkczdWMyWWZNUExHajlKSUpyWGJKUlFMVkEvemZGaFpaY01mdVhhSlIzN0J0UXhBdVFqQnZzVysrRDlvSkdoU1dyOTFOaTF5dUp5dmNYamgiLCJtYWMiOiI1YjEwMDAxNzJkZGZhNjQxY2ViNTk4M2EzYjdkODZlNGMwOWM4MzUzZDA2NjkzODM1YjVmMzFjYWI4ZDU3MGQ3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:14:50 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1809497631\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1163428153 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"222 characters\">http://localhost/bill/eyJpdiI6Inh0WEphTm9EcGdyYXlaeDlOL1VYUVE9PSIsInZhbHVlIjoia3pJT0tEc1dETGRsRExhZlYzZXR2Zz09IiwibWFjIjoiNDFlNTQ3ZWYxZDMxNDNjZjk3MTU3YjAxMDczODVhMDgzMmU4ZmFkYmExZTg5MGZlZmZmZjEyNmFlZDY3MzY2MCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163428153\", {\"maxDepth\":0})</script>\n"}}