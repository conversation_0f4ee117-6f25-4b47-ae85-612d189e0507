{"__meta": {"id": "X5694ad70591cdd3acc80cd948599e895", "datetime": "2025-06-27 01:03:53", "utime": **********.810807, "method": "PUT", "uri": "/bill/5", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.299271, "end": **********.810825, "duration": 0.5115540027618408, "duration_str": "512ms", "measures": [{"label": "Booting", "start": **********.299271, "relative_start": 0, "end": **********.629966, "relative_end": **********.629966, "duration": 0.33069491386413574, "duration_str": "331ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.629977, "relative_start": 0.3307058811187744, "end": **********.810827, "relative_end": 1.9073486328125e-06, "duration": 0.18085002899169922, "duration_str": "181ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 51518672, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "PUT bill/{bill}", "middleware": "web, verified, auth, XSS, revalidate", "as": "bill.update", "controller": "App\\Http\\Controllers\\BillController@update", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=474\" onclick=\"\">app/Http/Controllers/BillController.php:474-683</a>"}, "queries": {"nb_statements": 23, "nb_failed_statements": 0, "accumulated_duration": 0.08369000000000001, "accumulated_duration_str": "83.69ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6574042, "duration": 0.0016799999999999999, "duration_str": "1.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 2.007}, {"sql": "select * from `bills` where `id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 64}], "start": **********.6619802, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php:61", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "kdmkjkqknb", "start_percent": 2.007, "width_percent": 0.382}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.668821, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 2.39, "width_percent": 0.585}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 15 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["15", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.681662, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 2.975, "width_percent": 1.016}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.683734, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 3.991, "width_percent": 0.609}, {"sql": "select * from `bill_products` where `bill_products`.`id` = '5' limit 1", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 525}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.69346, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BillController.php:525", "source": "app/Http/Controllers/BillController.php:525", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=525", "ajax": false, "filename": "BillController.php", "line": "525"}, "connection": "kdmkjkqknb", "start_percent": 4.6, "width_percent": 0.514}, {"sql": "select * from `product_services` where `product_services`.`id` = 0 limit 1", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 3931}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 544}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.695905, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3931", "source": "app/Models/Utility.php:3931", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=3931", "ajax": false, "filename": "Utility.php", "line": "3931"}, "connection": "kdmkjkqknb", "start_percent": 5.114, "width_percent": 0.657}, {"sql": "update `bill_products` set `price` = '150', `bill_products`.`updated_at` = '2025-06-27 01:03:53' where `id` = 5", "type": "query", "params": [], "bindings": ["150", "2025-06-27 01:03:53", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 555}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.6979852, "duration": 0.05367, "duration_str": "53.67ms", "memory": 0, "memory_str": null, "filename": "BillController.php:555", "source": "app/Http/Controllers/BillController.php:555", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=555", "ajax": false, "filename": "BillController.php", "line": "555"}, "connection": "kdmkjkqknb", "start_percent": 5.771, "width_percent": 64.13}, {"sql": "select * from `bill_products` where `bill_products`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 525}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7540781, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BillController.php:525", "source": "app/Http/Controllers/BillController.php:525", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=525", "ajax": false, "filename": "BillController.php", "line": "525"}, "connection": "kdmkjkqknb", "start_percent": 69.901, "width_percent": 0.55}, {"sql": "select * from `product_services` where `product_services`.`id` = 0 limit 1", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 3931}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 544}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.75623, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "Utility.php:3931", "source": "app/Models/Utility.php:3931", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=3931", "ajax": false, "filename": "Utility.php", "line": "3931"}, "connection": "kdmkjkqknb", "start_percent": 70.45, "width_percent": 0.335}, {"sql": "update `bill_products` set `price` = '0', `bill_products`.`updated_at` = '2025-06-27 01:03:53' where `id` = 6", "type": "query", "params": [], "bindings": ["0", "2025-06-27 01:03:53", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 555}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7578192, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "BillController.php:555", "source": "app/Http/Controllers/BillController.php:555", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=555", "ajax": false, "filename": "BillController.php", "line": "555"}, "connection": "kdmkjkqknb", "start_percent": 70.785, "width_percent": 2.426}, {"sql": "select * from `bill_accounts` where `bill_accounts`.`id` = '6' limit 1", "type": "query", "params": [], "bindings": ["6"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 562}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.7613919, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "BillController.php:562", "source": "app/Http/Controllers/BillController.php:562", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=562", "ajax": false, "filename": "BillController.php", "line": "562"}, "connection": "kdmkjkqknb", "start_percent": 73.211, "width_percent": 0.311}, {"sql": "update `bill_accounts` set `price` = '20', `bill_accounts`.`updated_at` = '2025-06-27 01:03:53' where `id` = 6", "type": "query", "params": [], "bindings": ["20", "2025-06-27 01:03:53", "6"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 575}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.763186, "duration": 0.00307, "duration_str": "3.07ms", "memory": 0, "memory_str": null, "filename": "BillController.php:575", "source": "app/Http/Controllers/BillController.php:575", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=575", "ajax": false, "filename": "BillController.php", "line": "575"}, "connection": "kdmkjkqknb", "start_percent": 73.521, "width_percent": 3.668}, {"sql": "delete from `transaction_lines` where `reference_id` = 5 and `reference` = 'Bill'", "type": "query", "params": [], "bindings": ["5", "Bill"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 619}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.767957, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "BillController.php:619", "source": "app/Http/Controllers/BillController.php:619", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=619", "ajax": false, "filename": "BillController.php", "line": "619"}, "connection": "kdmkjkqknb", "start_percent": 77.19, "width_percent": 4.445}, {"sql": "delete from `transaction_lines` where `reference_id` = 5 and `reference` = 'Bill Account'", "type": "query", "params": [], "bindings": ["5", "<PERSON> Account"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 620}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.772897, "duration": 0.00579, "duration_str": "5.79ms", "memory": 0, "memory_str": null, "filename": "BillController.php:620", "source": "app/Http/Controllers/BillController.php:620", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=620", "ajax": false, "filename": "BillController.php", "line": "620"}, "connection": "kdmkjkqknb", "start_percent": 81.635, "width_percent": 6.918}, {"sql": "select * from `bill_products` where `bill_id` = 5", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 622}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.780304, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BillController.php:622", "source": "app/Http/Controllers/BillController.php:622", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=622", "ajax": false, "filename": "BillController.php", "line": "622"}, "connection": "kdmkjkqknb", "start_percent": 88.553, "width_percent": 0.394}, {"sql": "select * from `product_services` where `product_services`.`id` = 0 limit 1", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 624}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.781964, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BillController.php:624", "source": "app/Http/Controllers/BillController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=624", "ajax": false, "filename": "BillController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 88.947, "width_percent": 0.466}, {"sql": "select * from `product_services` where `product_services`.`id` = 0 limit 1", "type": "query", "params": [], "bindings": ["0"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 624}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.783506, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BillController.php:624", "source": "app/Http/Controllers/BillController.php:624", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=624", "ajax": false, "filename": "BillController.php", "line": "624"}, "connection": "kdmkjkqknb", "start_percent": 89.413, "width_percent": 0.299}, {"sql": "select * from `bill_accounts` where `ref_id` = 5", "type": "query", "params": [], "bindings": ["5"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 662}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.784793, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BillController.php:662", "source": "app/Http/Controllers/BillController.php:662", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FBillController.php&line=662", "ajax": false, "filename": "BillController.php", "line": "662"}, "connection": "kdmkjkqknb", "start_percent": 89.712, "width_percent": 0.227}, {"sql": "select * from `transaction_lines` where `reference_id` = 5 and `reference_sub_id` = 5 and `reference` = 'Bill Account' limit 1", "type": "query", "params": [], "bindings": ["5", "5", "<PERSON> Account"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5654}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 673}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.7861671, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5654", "source": "app/Models/Utility.php:5654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5654", "ajax": false, "filename": "Utility.php", "line": "5654"}, "connection": "kdmkjkqknb", "start_percent": 89.939, "width_percent": 2.246}, {"sql": "insert into `transaction_lines` (`account_id`, `reference`, `reference_id`, `reference_sub_id`, `date`, `credit`, `debit`, `created_by`, `updated_at`, `created_at`) values (0, 'Bill Account', 5, 5, '2025-06-27 00:00:00', 0, '150.00', 15, '2025-06-27 01:03:53', '2025-06-27 01:03:53')", "type": "query", "params": [], "bindings": ["0", "<PERSON> Account", "5", "5", "2025-06-27 00:00:00", "0", "150.00", "15", "2025-06-27 01:03:53", "2025-06-27 01:03:53"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5673}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 673}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.789343, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5673", "source": "app/Models/Utility.php:5673", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5673", "ajax": false, "filename": "Utility.php", "line": "5673"}, "connection": "kdmkjkqknb", "start_percent": 92.185, "width_percent": 2.485}, {"sql": "select * from `transaction_lines` where `reference_id` = 5 and `reference_sub_id` = 6 and `reference` = 'Bill Account' limit 1", "type": "query", "params": [], "bindings": ["5", "6", "<PERSON> Account"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5654}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 673}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.792693, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5654", "source": "app/Models/Utility.php:5654", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5654", "ajax": false, "filename": "Utility.php", "line": "5654"}, "connection": "kdmkjkqknb", "start_percent": 94.671, "width_percent": 2.175}, {"sql": "insert into `transaction_lines` (`account_id`, `reference`, `reference_id`, `reference_sub_id`, `date`, `credit`, `debit`, `created_by`, `updated_at`, `created_at`) values (252, 'Bill Account', 5, 6, '2025-06-27 00:00:00', 0, '20.00', 15, '2025-06-27 01:03:53', '2025-06-27 01:03:53')", "type": "query", "params": [], "bindings": ["252", "<PERSON> Account", "5", "6", "2025-06-27 00:00:00", "0", "20.00", "15", "2025-06-27 01:03:53", "2025-06-27 01:03:53"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 5673}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/BillController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\BillController.php", "line": 673}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.795744, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "Utility.php:5673", "source": "app/Models/Utility.php:5673", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=5673", "ajax": false, "filename": "Utility.php", "line": "5673"}, "connection": "kdmkjkqknb", "start_percent": 96.846, "width_percent": 3.154}]}, "models": {"data": {"App\\Models\\BillProduct": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillProduct.php&line=1", "ajax": false, "filename": "BillProduct.php", "line": "?"}}, "App\\Models\\BillAccount": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBillAccount.php&line=1", "ajax": false, "filename": "BillAccount.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Bill": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FBill.php&line=1", "ajax": false, "filename": "Bill.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 10, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => edit bill, result => true, user => 15, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>edit bill</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">edit bill</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.687355, "xdebug_link": null}]}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "success": "تم تحديث بيل بنجاح.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/bill/5", "status_code": "<pre class=sf-dump id=sf-dump-687295982 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-687295982\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-749797412 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-749797412\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>vender_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>bill_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-27</span>\"\n  \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-27</span>\"\n  \"<span class=sf-dump-key>category_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">31</span>\"\n  \"<span class=sf-dump-key>order_number</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:11</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>account_id</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>item_name</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1601;&#1575;&#1578;&#1585;&#1608;&#1577;  &#1605;&#1588;&#1578;&#1585;&#1610;&#1575;&#1578; &#1575;&#1604;&#1576;&#1575;&#1606;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"3 characters\">150</span>\"\n      \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>itemTaxPrice</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n      \"<span class=sf-dump-key>itemTaxRate</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">130</span>\"\n      \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:12</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>account_id</span>\" => \"<span class=sf-dump-str>6</span>\"\n      \"<span class=sf-dump-key>item_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">&#1590;&#1585;&#1610;&#1576;&#1577; &#1605;&#1583;&#1582;&#1604;&#1575;&#1578;</span>\"\n      \"<span class=sf-dump-key>quantity</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>discount</span>\" => \"<span class=sf-dump-str>0</span>\"\n      \"<span class=sf-dump-key>tax</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>itemTaxPrice</span>\" => \"<span class=sf-dump-str title=\"4 characters\">0.00</span>\"\n      \"<span class=sf-dump-key>itemTaxRate</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>chart_account_id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">252</span>\"\n      \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"\n      \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3575</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryAYZMBFM4jVKrnNyk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750986202112%7C74%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlJGVW51L1FuVTVSUm5RcnZYUm1wV1E9PSIsInZhbHVlIjoiQzR5RTFPSzlIRzBTRyttOXZkOVZZSEpQSVR6UlYwc0x3TXVpN25MMk85bHFmdi9NV3dHTk5odk1FWXVIbzZ1Z2FpTXEzK1p0N0JuM3RVL3ZOblNnK1M2S09nNERZUzZMMkZ0NU5nRnFONHQ1QWlUNU1nQ0svS2JpdXBYU0VuUTY5VjkwQnFZbnVndVM1dnBsRnVkU1VtSll1Z3BSZzV4dFZyR203V25rODJBVHJsRkVRN2k2bERHbllMeFZod1FsZWJ0SEJMd29Ka01xTU9tUWlwRmM3eHlXbGFpTXN5QlFzeU5pZk1ka3RWaVprOGVycUxSTFpKQllXRTlRcUNxU2lwYjcxdG1PV1hHdG5HdlBPa0QyUlErT2l4Q0RSczhLWVVJaG8rTFpRK2xSQlU3WkwvdWVEd2hUQW11cTE3Qy9pWlZQcWhCNlJYNDJ3NTZiMTQ1Mks5d2RPSXo0Qjg4UjZLbHArYnlhZEhGZzdQK25nelphdDc3QUFxUEdITGcwcDNScUNrdEVla2xVL1diOGlNdHM3NFp6bDJVai9uMnVhRGc1VlpwUkxuSG1waWZQZHJHZGt0QWp5eFZKMFl0Q2kyU1pmT244ckErdGlmdjcvb0NETUdqZXh5cC9jWHJwMTQ2aHBFUFE1YlhqTHo5SzdqTzM5N0ZEQjdKNWFkMFUiLCJtYWMiOiIwNTUwZDJhOTM3ZGE5OTA2NzQ2YTQzMTkwOTQyMmViNDI1YTYwOTUzN2I4NjdmMjhiYjE5YmI4NjFhNDM0ZDZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjhZdThEaTJqZ0JCTHNudjhvSStpc1E9PSIsInZhbHVlIjoiOHdTNktxUVduTGtXUXE2OEJDSEZ5QkltMXlGQmsvVVFqRUZhY0hnZi9IRmxnc0E5SEdBdTFlR0tmaFVNanRTWG8yeW4yblpNMTNDYWRpbkwrYVJZQmtqWVV6dU11YndwZ04rcHd2b1ZuSFVqUEpYQThwVCs0TnAyWUxoaFpEUGZ6d3ZsejBnMnhRSmZyR055SUZxeGpGSHZraUFWS2kxWXhJbnM4NUY3aWw0YnMwbnp6NC82bWluV3FqT1BCb3BlWnZKdGpkZkxoQXZSaDJvY2hab2drVG5pZjlXR2EzZmdIZXRFNFBQSnBqZllzT2V1VFAwN0lBaENwaDdDTzNLUU9qbkg0dXRSR01uMW1aTHRKQ2haODB2YmtHd2o2d09LejZMaGkrUy9jU2pRYm9sMlk2K256cHoyaVZIMm83bGorUjk0VUxqTmF5eWxPeXRJcjJRQzJBTXJ2dkhXcFYwTjI3S1dsSVgrZFdrUENsd1BoWW1SRTdPNURnQjV2bWV6SU9CZTVDYVRLNjFkZWJHUEcyZFRHbG0xVDJUaitRSUZxYzR2bTV2RTFFeVY1OXluVDJPbng0dUROemdrN3ExK0pFREJ2eEQwcGNjNU5qeEd3a2piejRVa3RSL2pocUE0QmMxZGN0a2JMWUJiTDR2cDBNWnE5VCtDVzI2TGhsVmIiLCJtYWMiOiI0M2ZhZTQ3ZWRkNGY0YjZiMTc4ZTRiNDFmMjQyNTU0YjIxZjdmMDQ4NGQxYWI5YTIyYmQ5NTE0YzU1YTcyNzMxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-697403687 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-697403687\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:03:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjVKVVhVS05hTEkvb00zakl4dTFmREE9PSIsInZhbHVlIjoiRHVLcHlOdENTT0NiRFl5bWVSYXZiWTBWMjdJcHN3MGVKT2s1Y1dFazdzUEpvNjFRcGpUZUR0RUJIcXNOZ2Zvb21uVWhwQ1BoLzRXRmp2Tk1UcmxvaW9SUHE1amxNMnNyT1VSQ3VWMExtNjRNNFBicWhsU1VPbVcxb3VQcUdocUFWd1VvQUNqUExjdFpMZVByV0hDWmJFSVFoYzEzTlNoZ282THozSUFCZ0hxM1RHREd5aTd0UlpvdjlQbGJTeWkvQ2loRzNCNGppVk1JNUR6UkU5WmkyYkNCQlNlb0ZyMVgzODZhVEwrTHU1SnJUdlJFZlFZakdBUkhyc2VGeDFKODFXZld4a3pKdmZFWnRubW9qS0ZDVk5Sd1NuK3VhYitFeEVLcmFBWU5QbzcrOUFTc2FYV1dZR2xVdkJNbHd0SUowTklLcVN5QkJLUFBpRzNBbVhSeUtuVE1LbXFsNDRUSVBWR0M0SW9SOEF0MUZzY2V6THN4REwxQm5lN2RLKzBIM2ZEdlBxUkJKbDlZdnAvUlY1c3hZTHhKMjAxR2ViVVc4L0lRZ0Y0b21WRi9ac0tVUUV4bVVxYzZnNkkyWDNKQWJMK0ZYRi9nekZLaGZ2MC83czJ4RVdSZHUxNlBNMU5vUDRpdGw1U2RCR0Q5S3Q4TDRwQ1ZGeHZ2djVTRXZxcXIiLCJtYWMiOiJiODMxMGY1N2I3OGM3NGFhMDk4ZDcwYTlhMzdmN2U5ZGJjY2Y1Yzg4MjJmOWNjMWNmNzNhYTAwYWJiNDU5ODc2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:03:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRFdkFzL0RObHcwUG1sSVpiU3dvMWc9PSIsInZhbHVlIjoiQTFVclRncUZMQTc4WDhrZWxyQVNpS0dUOEg5cGc1VzUwMGhKbkVxRTFscG5sekJOOHJTZEhMZ1FoYVEvOE53TzN3blFrRXAweVZPeUdLLzZvY2JzTWlhbVVEQ0ZUYlg2TVdRQm5xQ0ZMV1ZITnJHQmJad3IwNFc2MnFoMjNBZ3NQMFlGRlhKekhHTnh0SG1jbG9ZbDZVeGVtVFVlb25lSXcvdEg4cmpuOWVKdTVFUDcvUjBjbmdMNDl2VEVVb25YcjE1T1NZaTdKdXE2UGNBWFBEdTYzVDNROENYZjEveDdwSDBvN3l4ZE55NURaUVorZjNzMkpibUdIZG1WakFCekVnL0xvK0VnV05jWllMVjh2THU1cE83YkpsSUUwK2J1cXJIeVVhWmZrc1A3TEZvV0xFbVJ2dFRzSVh2QkwwNWV4blBpNGJEV24zM2g1UTN6ay9yUlhYMjFPbHRWYW00SzhJd1FRb2YyZkZIdzJ6QXp6eTNVNDk3R0tEdlNsbVpnYnh1NGh5SzhUTTdpNUtIdDg5UnUrRG8yVm5rcFlVdm5Ub3IwZGQrcUVBcDd0NEdaVzNPWkJhdmFsR00xVzFCYngwTWJpdTZXN25DekpJTXFkRUg3cXJDNTJtU2w1c3F3KzhEZmljbE4xL2p5RjlRa2tQZUxhS3huYjZQSGJZTHEiLCJtYWMiOiJkZDIyOGI5NzgwNjU5ZTViZTE3NjBhZWI3ZTJmYjBiOWRmYjczOWFhM2JlNTBlMWFkODBmMTRmNTM2NTk5Y2MzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:03:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjVKVVhVS05hTEkvb00zakl4dTFmREE9PSIsInZhbHVlIjoiRHVLcHlOdENTT0NiRFl5bWVSYXZiWTBWMjdJcHN3MGVKT2s1Y1dFazdzUEpvNjFRcGpUZUR0RUJIcXNOZ2Zvb21uVWhwQ1BoLzRXRmp2Tk1UcmxvaW9SUHE1amxNMnNyT1VSQ3VWMExtNjRNNFBicWhsU1VPbVcxb3VQcUdocUFWd1VvQUNqUExjdFpMZVByV0hDWmJFSVFoYzEzTlNoZ282THozSUFCZ0hxM1RHREd5aTd0UlpvdjlQbGJTeWkvQ2loRzNCNGppVk1JNUR6UkU5WmkyYkNCQlNlb0ZyMVgzODZhVEwrTHU1SnJUdlJFZlFZakdBUkhyc2VGeDFKODFXZld4a3pKdmZFWnRubW9qS0ZDVk5Sd1NuK3VhYitFeEVLcmFBWU5QbzcrOUFTc2FYV1dZR2xVdkJNbHd0SUowTklLcVN5QkJLUFBpRzNBbVhSeUtuVE1LbXFsNDRUSVBWR0M0SW9SOEF0MUZzY2V6THN4REwxQm5lN2RLKzBIM2ZEdlBxUkJKbDlZdnAvUlY1c3hZTHhKMjAxR2ViVVc4L0lRZ0Y0b21WRi9ac0tVUUV4bVVxYzZnNkkyWDNKQWJMK0ZYRi9nekZLaGZ2MC83czJ4RVdSZHUxNlBNMU5vUDRpdGw1U2RCR0Q5S3Q4TDRwQ1ZGeHZ2djVTRXZxcXIiLCJtYWMiOiJiODMxMGY1N2I3OGM3NGFhMDk4ZDcwYTlhMzdmN2U5ZGJjY2Y1Yzg4MjJmOWNjMWNmNzNhYTAwYWJiNDU5ODc2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:03:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRFdkFzL0RObHcwUG1sSVpiU3dvMWc9PSIsInZhbHVlIjoiQTFVclRncUZMQTc4WDhrZWxyQVNpS0dUOEg5cGc1VzUwMGhKbkVxRTFscG5sekJOOHJTZEhMZ1FoYVEvOE53TzN3blFrRXAweVZPeUdLLzZvY2JzTWlhbVVEQ0ZUYlg2TVdRQm5xQ0ZMV1ZITnJHQmJad3IwNFc2MnFoMjNBZ3NQMFlGRlhKekhHTnh0SG1jbG9ZbDZVeGVtVFVlb25lSXcvdEg4cmpuOWVKdTVFUDcvUjBjbmdMNDl2VEVVb25YcjE1T1NZaTdKdXE2UGNBWFBEdTYzVDNROENYZjEveDdwSDBvN3l4ZE55NURaUVorZjNzMkpibUdIZG1WakFCekVnL0xvK0VnV05jWllMVjh2THU1cE83YkpsSUUwK2J1cXJIeVVhWmZrc1A3TEZvV0xFbVJ2dFRzSVh2QkwwNWV4blBpNGJEV24zM2g1UTN6ay9yUlhYMjFPbHRWYW00SzhJd1FRb2YyZkZIdzJ6QXp6eTNVNDk3R0tEdlNsbVpnYnh1NGh5SzhUTTdpNUtIdDg5UnUrRG8yVm5rcFlVdm5Ub3IwZGQrcUVBcDd0NEdaVzNPWkJhdmFsR00xVzFCYngwTWJpdTZXN25DekpJTXFkRUg3cXJDNTJtU2w1c3F3KzhEZmljbE4xL2p5RjlRa2tQZUxhS3huYjZQSGJZTHEiLCJtYWMiOiJkZDIyOGI5NzgwNjU5ZTViZTE3NjBhZWI3ZTJmYjBiOWRmYjczOWFhM2JlNTBlMWFkODBmMTRmNTM2NTk5Y2MzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:03:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-246154753 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6IjhJYmdDVUNuRWp2cTZuQlduaU5vRHc9PSIsInZhbHVlIjoiQ1NtM0VNWDYyRnEwWExCb2Vwei9Rdz09IiwibWFjIjoiNjQ2NmE5YmYzNTU1YWYyMGRkYWUxY2VkNGEyNDhmMmNhZGJiNjZmZTg1NDhjNDQ0N2Y4NGY1NTY3OTFjZjVjOCIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"19 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1576;&#1610;&#1604; &#1576;&#1606;&#1580;&#1575;&#1581;.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-246154753\", {\"maxDepth\":0})</script>\n"}}