{"__meta": {"id": "Xa00f681d42f3983c8179b791b5ea6a89", "datetime": "2025-06-27 02:34:19", "utime": **********.990784, "method": "POST", "uri": "/payment-voucher", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.473388, "end": **********.990797, "duration": 0.517409086227417, "duration_str": "517ms", "measures": [{"label": "Booting", "start": **********.473388, "relative_start": 0, "end": **********.791332, "relative_end": **********.791332, "duration": 0.3179440498352051, "duration_str": "318ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.791341, "relative_start": 0.31795310974121094, "end": **********.990799, "relative_end": 1.9073486328125e-06, "duration": 0.19945788383483887, "duration_str": "199ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45921920, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST payment-voucher", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@store", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.store", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=68\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:68-89</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.14701, "accumulated_duration_str": "147ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.8188071, "duration": 0.02556, "duration_str": "25.56ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 17.387}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.8521838, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 17.387, "width_percent": 0.292}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.855212, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:74", "source": "app/Http/Controllers/PaymentVoucherController.php:74", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=74", "ajax": false, "filename": "PaymentVoucherController.php", "line": "74"}, "connection": "kdmkjkqknb", "start_percent": 17.679, "width_percent": 0.177}, {"sql": "select * from `warehouses` where `warehouses`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.857796, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:75", "source": "app/Http/Controllers/PaymentVoucherController.php:75", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=75", "ajax": false, "filename": "PaymentVoucherController.php", "line": "75"}, "connection": "kdmkjkqknb", "start_percent": 17.856, "width_percent": 0.129}, {"sql": "select count(*) as aggregate from `voucher_payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 77}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.859348, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:77", "source": "app/Http/Controllers/PaymentVoucherController.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=77", "ajax": false, "filename": "PaymentVoucherController.php", "line": "77"}, "connection": "kdmkjkqknb", "start_percent": 17.985, "width_percent": 0.197}, {"sql": "insert into `voucher_payments` (`date`, `payment_amount`, `pay_to_user_id`, `purpose`, `payment_method`, `created_by`, `custome_id`, `warehouse_id`, `shift_id`, `updated_at`, `created_at`) values ('2025-06-27', '1500', '22', '12', 'cash', 22, 'PUR-المستودع الرئيسي-22', 8, 48, '2025-06-27 02:34:19', '2025-06-27 02:34:19')", "type": "query", "params": [], "bindings": ["2025-06-27", "1500", "22", "12", "cash", "22", "PUR-المستودع الرئيسي-22", "8", "48", "2025-06-27 02:34:19", "2025-06-27 02:34:19"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 86}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.860929, "duration": 0.12028, "duration_str": "120ms", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:86", "source": "app/Http/Controllers/PaymentVoucherController.php:86", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=86", "ajax": false, "filename": "PaymentVoucherController.php", "line": "86"}, "connection": "kdmkjkqknb", "start_percent": 18.182, "width_percent": 81.818}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "success": "تم انشاء سند الصرف بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/payment-voucher", "status_code": "<pre class=sf-dump id=sf-dump-681468842 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-681468842\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-918027531 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-918027531\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1376509827 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-27</span>\"\n  \"<span class=sf-dump-key>payment_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1500</span>\"\n  \"<span class=sf-dump-key>pay_to_user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n  \"<span class=sf-dump-key>purpose</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cash</span>\"\n  \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>custome_id</span>\" => \"<span class=sf-dump-str title=\"23 characters\">PUR-&#1575;&#1604;&#1605;&#1587;&#1578;&#1608;&#1583;&#1593; &#1575;&#1604;&#1585;&#1574;&#1610;&#1587;&#1610;-22</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>shift_id</span>\" => <span class=sf-dump-num>48</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376509827\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-895912710 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">132</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6IkMrcGlkYU9pa2MvcHN5N1JMS1ZrNXc9PSIsInZhbHVlIjoiaThzWXBOY3FNYWNMbTBvdnluTEp4WXRVQzY5dlM0MVdBSVJoWERRVWhJd0ZWS3lKNFIxMjJZU2NCLzlORnFRZkhxdDAzZElLVUFCNklMZ2pUQ3pOOFVPaFUzSWhQbGtHd0dkdlBYWVAzdXdzdG5jU1hoUDNkaFAwV1hrbnB3MUdjYXVtcEpENENUaE10WnZhUlVJSUVzcGRaUnNFQnNTUmJFelFZMGpWY3B0QUZ4Z0tnc04xbkwwMTFWbGltVWUzK2JrQWtiOFpvUHRCdE9ZYlIvdndpYTVJdUJnRlRDUnJGaG9udk95ZG1uc2duT3NRbWkwM3Rob2FhOUxyY2ZBbDR2SW5XV3MxQy9Md2xKMmYvR0V3c3NRQ2pybS83dG9tZUMvOTlUTm80T1VwWnF6NkdnNDNIb01hZ2I4MklCako3dkxNMzh6VVljZWs2Nk1xeVE0azZZNmdZVkVtRFVETjZLb1ZvVk1uOWs2WmJqN1Fac3AxOXp4dWhQS0F2Nkw5VnpVZDFYb3Y4VGE3c2I0NUtOT0Q3UE44d1RoVzN6WWZja2x1aDl1VFpySGhxdmxoWjlFVGtkbERzNGFmYitrREhPeFlRcEFoWGhsWllhcEVqYzlYOG1FclZlcys1VEpwK1JhZ2lzT0wxMG50S01oTUtRTjdBTVprajlLTnpkWVkiLCJtYWMiOiJjMGRhNDA0ZWIxYTg0ZDVjYTlkYmFlYWEwNGI1ZWM3OTJlNjFjYTk5MWZlNzcyY2IwYTMyYzY5ODYxZjhmY2ZkIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkF6ZzJqdXFZanZPbzBKazBJR2N3b1E9PSIsInZhbHVlIjoiRG5QWWZGWnByOUlBdDJuSEg0WXorNFNMcy9WNEhreUNUMS9kNVgrcUV3cENGbHRUSWR3UHoxdDVZQ1pDOEorOE5XUUZuOFM0RE1uZXA5aGxocWdLWlErM2gxbXB6cEppNkViUEtrVnlPZm94eWg1alduMUdtcm9JOUFKS1pNVFR2RWgyUi9xQjZWNVoyWXZ2MWFScGppRHFkdENHV2UwQWcxVHlZQ21UcnF2RlAvckNlUGY1eHNoVGxmMUYzWTFheHlNeFFKRkx1UmwwUG5MR29pMm9QRU1FSTFuTEVvZlN5V2RoVS9tRGVzQ3hvd1MxenVDVTM5cVorVVBSS1pXVW5FM2hidDNnR25CK3dMWXp3ckdOeEdWMHYrVWhac0JEbWlBcFFBSGQyS0F5Ym5qTkd6RUxYZTZyR0VEaUlBK3gyTm1GT1FGYlIwYWVNdGM1NmYwdHJqYi9HSmZwRHMzYjkxYlI1amJRNm9XVXd2NXdJVTZIdjNBK0EybFpBWHBXN1QvdUdqTkdRaHZBcmxvRy91Uk1OVzR0bnJaM1diMEtDeFVwR1VIOVlkU01NL1gyaUFMd3NTaUdiKzVkUjdOU3BtT3VVMjFIM2p1T1ZDM3NWb1pQTEJSbURCUDgwaTMyMGJUbEpxcmE5eXBJbWFSN1ZGckU4OGhZNFQ2ODBxY0ciLCJtYWMiOiIxYzdkYWQzNjJmNmIxYjA2MTlhYWYyNmE1OGMwMDZlZTlkNGE0NzdmZGNkZDhkODNhM2FlMmU0MjA1Zjg5MzM0IiwidGFnIjoiIn0%3D; _clsk=1wa64lz%7C1750991646682%7C41%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-895912710\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1069912671 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1069912671\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-994172454 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IlVtaHVlc0dZc3pXdlpiSDlidE1CYkE9PSIsInZhbHVlIjoiZTNucTQyT0w1V1BQa3JnTE9TUWttZTRJZURNUzlOVGFONVNER3hIUmI5UVNVQXFIeU1WeEdsQTN3bnZLTXppTGxrdWxOK3YycENsc0R5eUIxWVZ6WXc4bCt3V1pyTnhVVGxhWUJwTkgrS3R0MzZnNjBKQ0V0S2hEdlFLM3ZZNUNSSEJjbmdHdU9HWWFJWEcvTURTdm52d2Zqc0FtdzdSOU9tcXdYVE9jTU9XZFRoYnRDL1ZLdHdXT0h5b1ZWeGptU3IyNDlVMkRYR25hL1FWRm12RjNkNnBiUTFVeUdDeVZzVFRVNGoyUE1Ycy9sWlN2MFhmdDZHNk96MW9CVGZmRU5Gdy9oRjZvRGp2bGx6M1hmb1NycVphK1BTaXpKbVNiYzZaa0ZmbUZKOHgyWVNra0RYSERQN29jaHV2czdsMTVjQlZ2K2VvOWR2dTNmMFNtcStKdnBwYnVwcXlpWXJoa1NkRVBrYmMySjkxNVVrU0llVVlzWUdhSlArdDUwN1FJSDN2b3IycTd4aTV4eUtCYjlCeDl3R05ENVRpL3pnVkduNnhjckgrSTlKWnlFMVlUYkszdWxUN294Q25neUtmbWs0MmMxSlIxeUdQTXlvT3p1S2xmQUU4NHByTnNUTkpOMlRoZ2ZKMDN6S2hYK0NPTFJJQXJrbXRTUUhGZWRITjQiLCJtYWMiOiJkYzk2Njg2YWM2ODg4ZmVkMmQzZjUwZjQ2NmE5MzJkOTExZDA5NGQyMzg4ZTRlYjEyMzc2OTNiZWYzNjNmNzY0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:19 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Inc1TGxsV2VBSXgya09KUkxrK21hZmc9PSIsInZhbHVlIjoiVys2RThlVHd3RmxVeXpYYnJXYjBlREVjcnpBYUJVMG95R2JYdUMwa2JLbXNta2RFamVPWXpiSDB2YTJ2eHd3cWxkV1ZjdlYxTzVIRWJvQ213Z2hGK3JUTHArd05ZSWgvUzl3Tmw2Z2lBNVdhcktYeTRleGpiSndMcEUxL1J2U2hPVVdtYzdBUjF2RlJjOFo1QmtLZUVRYjVHOEE3TU8rQnVDU0J4ZFplb09mSWYvY0FwRE8zczVOVjN2eWF2a3UwMEtGbkk1bUdNSU1pSHdYTnhHb2ZVUnJNNlBLdCtjMnZxdmxEQXBERkdaVkovd3FGZDlxUkJwbU1yTnI1Y2p4cGNFb2xYeHk4ckFVamt3MmkrUkNLSmhKNk9lNEZrNlB6M3h0aTJINU11VmFpK29oSnA1VDBXc1djNGNqVnlNWTFkR2lpSEgzNzcvWXdLZUVlYTVJQWJwWHFCTWE2b3llbE84dHpZZ0ZsNWpwMUJKOFVUcmgrRmlXU0VQVzBVeVF3b1hNWXZUQ2dVNlA0WDl1Nlk5Vmw0L3ViZlNiQXRacWN6Y2NxN1MzeThrSDBqeFVad0pYMkxwU1hyRVMzbnlBM3JkcnpDYitJZFZHYmhFSUlEUXpFY0Q2Z25TY1phWFFDWWJlcW5MTE9LcG05YzNXUkE2dDBtc0JHNU5XdkpvSTAiLCJtYWMiOiI2ZWY3YjI3YjY3YTgwMjhiM2EwNTAyMzZjYmQ5MTRiZjgyMjM4YzYyNmEwMzhjMDc4NTE2M2FhNWRmOWY2NDkzIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:19 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IlVtaHVlc0dZc3pXdlpiSDlidE1CYkE9PSIsInZhbHVlIjoiZTNucTQyT0w1V1BQa3JnTE9TUWttZTRJZURNUzlOVGFONVNER3hIUmI5UVNVQXFIeU1WeEdsQTN3bnZLTXppTGxrdWxOK3YycENsc0R5eUIxWVZ6WXc4bCt3V1pyTnhVVGxhWUJwTkgrS3R0MzZnNjBKQ0V0S2hEdlFLM3ZZNUNSSEJjbmdHdU9HWWFJWEcvTURTdm52d2Zqc0FtdzdSOU9tcXdYVE9jTU9XZFRoYnRDL1ZLdHdXT0h5b1ZWeGptU3IyNDlVMkRYR25hL1FWRm12RjNkNnBiUTFVeUdDeVZzVFRVNGoyUE1Ycy9sWlN2MFhmdDZHNk96MW9CVGZmRU5Gdy9oRjZvRGp2bGx6M1hmb1NycVphK1BTaXpKbVNiYzZaa0ZmbUZKOHgyWVNra0RYSERQN29jaHV2czdsMTVjQlZ2K2VvOWR2dTNmMFNtcStKdnBwYnVwcXlpWXJoa1NkRVBrYmMySjkxNVVrU0llVVlzWUdhSlArdDUwN1FJSDN2b3IycTd4aTV4eUtCYjlCeDl3R05ENVRpL3pnVkduNnhjckgrSTlKWnlFMVlUYkszdWxUN294Q25neUtmbWs0MmMxSlIxeUdQTXlvT3p1S2xmQUU4NHByTnNUTkpOMlRoZ2ZKMDN6S2hYK0NPTFJJQXJrbXRTUUhGZWRITjQiLCJtYWMiOiJkYzk2Njg2YWM2ODg4ZmVkMmQzZjUwZjQ2NmE5MzJkOTExZDA5NGQyMzg4ZTRlYjEyMzc2OTNiZWYzNjNmNzY0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Inc1TGxsV2VBSXgya09KUkxrK21hZmc9PSIsInZhbHVlIjoiVys2RThlVHd3RmxVeXpYYnJXYjBlREVjcnpBYUJVMG95R2JYdUMwa2JLbXNta2RFamVPWXpiSDB2YTJ2eHd3cWxkV1ZjdlYxTzVIRWJvQ213Z2hGK3JUTHArd05ZSWgvUzl3Tmw2Z2lBNVdhcktYeTRleGpiSndMcEUxL1J2U2hPVVdtYzdBUjF2RlJjOFo1QmtLZUVRYjVHOEE3TU8rQnVDU0J4ZFplb09mSWYvY0FwRE8zczVOVjN2eWF2a3UwMEtGbkk1bUdNSU1pSHdYTnhHb2ZVUnJNNlBLdCtjMnZxdmxEQXBERkdaVkovd3FGZDlxUkJwbU1yTnI1Y2p4cGNFb2xYeHk4ckFVamt3MmkrUkNLSmhKNk9lNEZrNlB6M3h0aTJINU11VmFpK29oSnA1VDBXc1djNGNqVnlNWTFkR2lpSEgzNzcvWXdLZUVlYTVJQWJwWHFCTWE2b3llbE84dHpZZ0ZsNWpwMUJKOFVUcmgrRmlXU0VQVzBVeVF3b1hNWXZUQ2dVNlA0WDl1Nlk5Vmw0L3ViZlNiQXRacWN6Y2NxN1MzeThrSDBqeFVad0pYMkxwU1hyRVMzbnlBM3JkcnpDYitJZFZHYmhFSUlEUXpFY0Q2Z25TY1phWFFDWWJlcW5MTE9LcG05YzNXUkE2dDBtc0JHNU5XdkpvSTAiLCJtYWMiOiI2ZWY3YjI3YjY3YTgwMjhiM2EwNTAyMzZjYmQ5MTRiZjgyMjM4YzYyNmEwMzhjMDc4NTE2M2FhNWRmOWY2NDkzIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994172454\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1238574232 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1578;&#1605; &#1575;&#1606;&#1588;&#1575;&#1569; &#1587;&#1606;&#1583; &#1575;&#1604;&#1589;&#1585;&#1601; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1238574232\", {\"maxDepth\":0})</script>\n"}}