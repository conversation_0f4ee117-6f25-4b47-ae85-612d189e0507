{"__meta": {"id": "Xe03c0bb43826f06441043da1d933e709", "datetime": "2025-06-27 02:15:46", "utime": **********.187853, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990545.75767, "end": **********.187868, "duration": 0.43019819259643555, "duration_str": "430ms", "measures": [{"label": "Booting", "start": 1750990545.75767, "relative_start": 0, "end": **********.105482, "relative_end": **********.105482, "duration": 0.3478121757507324, "duration_str": "348ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.105491, "relative_start": 0.3478209972381592, "end": **********.187869, "relative_end": 9.5367431640625e-07, "duration": 0.08237814903259277, "duration_str": "82.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45719960, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02715, "accumulated_duration_str": "27.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.139877, "duration": 0.0259, "duration_str": "25.9ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 95.396}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.173874, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 95.396, "width_percent": 2.136}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.179256, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.532, "width_percent": 2.468}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-149172365 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-149172365\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-681258256 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-681258256\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-832469151 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-832469151\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1276689855 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990544006%7C14%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Im5UcHJiLzkrQjRBUkJuMlFxMmx0Y0E9PSIsInZhbHVlIjoiOGRub2s5b3VhdTN0WmR1NnYrRXJkUzlTVjYyR3lkMTdnYVJiSDNWTVJkUWVaNUlabzRRSFFaRmlETzhhNHlBK3k3Rm1FbS9jb08vK253WTRvREF4TjBpcmJZbUdVUjVRbU1BUjFXNUl0aWJUUnh5Z1ppT3Q4aXV1K1VreE52S1VkNXdHdVBOdnpLcmZCd3VXSWFFQWJWM2NTZllSS0JBRzRkN0RSUldSQzF5Q0FhWkZLM2syZzdla05kdHVaUzVzTUozVkljZEhOUWlhUnA4WEFnK3Y0UXBkQ1lzN1FBYW9GMUlxeUxySnZoZ2hKMm5XdWRsdjNZZ25XR0dCMnJSMkJWY1Q3SnN6cG9qUWhuMEJjRXg0cXVhdEp1ZklnN0I3TGxoSjZFajc5cm1LNzJmS2p6Tlg2VStOMTJ0QURrU1hnWm1vVnZ3bmxFUHVVNkN1RlphMFJVcndPaDF6eXdNQ0JWTzlmeUY2TW9qaldiMUpab2FBMXNNK3RLdXpLWDU2akYwUnF3UnREZmJaV2I4eElod1BweDJleG9sdm1XSk95NXY4bTQwTEdtSTVUdFNFcmpqcExGemVja1pUK3pZUEQyZ3g4R1V4akNTVWNCbDhJdVc5RSs0VXpTeC9iY21UOUplVURoYUJjSkQ3QzBvdGs1ZHBtY0hpNDVGWHliZkEiLCJtYWMiOiI0M2NmMGFmMGI5MjU5OGM2ZjliZWEzYmQzMmY4NmZiMjZmNWFhNGI3NWI0Y2Y2MmRkNmQ5YTY1MDNhMDZiNmJhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlAxbmdRdXJCOFErcnhML0FqeC9pdUE9PSIsInZhbHVlIjoiYUtUZ1hZbEx2ZzU2MjNFNUpIcUtaM1BOamdtTCt3SSs4bStwdmljYzJMcCszdmI0L0JhbDJjZzU0aVlWOVJUUzJySnVmeWxTaFN6cStyalNkckZ6V05CNkxhc1VjUGJGbTVoYklLSUJjdkNHZUZkdGxlMDVVNkxGcXVOQm1qb0FwZUt0VlNac0VHcDYxRHdaQmhDbEw4MzRSMkVKY0tnZXllYVhyRWRtMjh5MHI1dG5OU29aOUkvd0RpR2NGY0gzSEhhQUx3ckRlaVpWb0YxcmFZb2NUbVF3K3ZtNitibDM2YjZ6RzE0U3d6ZUNtT3kwekdLM0NnUkIyc0hjQXd2RlNsMkNLblkxMU5LNEZLbXdCNmhweWc0L2laR2I4UVVBby9WQTRkdmtvQVFPMCtVZDdya3BWbnp6RGg2ZnN3VlBqNUNQaEVMZW55d0RWSTVMdVpPZmxiWG4yTGxtcGtkdWtJZUtjN20xRG5sQ3I3em83QmJqS1Z2bm41eGxJcGFERDRYYzhqeHMwT1JCOHJ0MExoZk5DZEROK25OREdLVS9hTkxYdkhDNFhVclo1VVg4cUU1R1dRM1hTbUh2OEtjaU9zWUZQdVppUytUVVNEbDNGYlVSRUZPMHVnUmhKdkRlaWgvVVlHRlRLUGxaNThUL3ZGL1dUTVhXOXF3WmFFekoiLCJtYWMiOiIxZDY1OTg3Y2ZmMTBjMTA1MGU1YjEzODdlODU1OTg3NDYxYmFhMTk5MzBjN2I3MTQ0OWU2MDcyM2Y1N2ZhZmU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1276689855\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-371999894 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-371999894\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1733491815 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:46 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImlTdmJnR3FlQWQxa21sK1puNFNvOXc9PSIsInZhbHVlIjoiSDBqM0hjTTVaZlBoRlN1RUpSUkNXQW9STmJPMkZnakhOUWltWnVvRWszZjB6aGxxa3FOL1FSQ25OaWc2eWFJNnR3cXgraklnVXlxME1MWnVVRFZaQ1pVOURHQ1Blc1N0SlR3K1VLbGFWaDBXK2ZQMVJzTFA1R2RpQzlUNnIyeTB0Q2VHUWorblE1Z29naDJad0hiLzc3cGRiS1JVRmM1K1NQNVlTUjY4R0VGbyt2V1d6NW9sdjFtSU9zemhEcTFPUi9pYlNIMmR6T1ptTnV5a3FGL2VCNHBITG5aRlEvSVBSTUgyVlphdkQvem0wb1FrTUdteHRTMEJiTVNDaTN4bHhuQnhmNVJoZzhIWS9NRnVCcGVMbWYwZVFWZXlkYldtNjZVUFdjWTRsRWFMK0sweWIvS3RVdncvZG1RRDh2d2Jua0ZXdHZPMExXSXZXcStyc0JSMVVETkpidTZOSXpTdHQ3ZmI1T2txT0NCM3hQdjZKWW84VjdicVp3Z1llNlRBQnlxVkQ3ZzZrMUdQN2sxMHFIS3FsL2RMUVNrQ3VXRWMwTlovY0JkUkZIV3g4bVN3cmxHTzEvaURXNURiQzdRdXk1OW1UU1VCZVNuM2V3b3RrTWVvcHhZaXRIVzJLL1pHVGhFT0xKTGoyek9NUE9JcE9wUTVsaG5qOE4zNmNnUXMiLCJtYWMiOiJhZjFkODllNTM4NjJlMWU1Y2EyNmQ0YzFiYzBlN2IwNWQzZmViYzRmM2NkNzZlOWJhNWM1ZDA2Y2MxNTg5Mzc4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:46 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im56MXJDMzNIangxbUFFYlNmbldhaHc9PSIsInZhbHVlIjoiTUV0NXNkOWNBdEdFQldEMW15Z1hrdkdzWkxuTktPWWpLaTc0WDg2VFJ2RmdMM3VkRG1qenBtclVqYmdweG1idGo1SjZVRUFxMTdLeXkwU1F1MGxKbDViYWxmaU9CeWo2TkNwSlo3TEpCNk9JZkRkVzZWVXhJaDRjVnp4RWttWnljUndnbERNME9Tb3dXZXRxU0ZWZDV1ZmhUV0l2WGNEZWpqVldCOStUUTYwU25zWU1mZGcyOC9iS2tsTTRra3RDNE9ZRS9KbGtSaFY3UStuRmZaa1g0aWZ1dVVPTlFjNXRuTTg1M09QL3FGRnFLeGxwNk5Kc3dyYTdKTzhrQVJuaUs0MFVEOXlhT2hqb09pM0ZJQUF5QUs4V2E4QTArQ2UyK01PSERSek1uckR3bzQ5ajdRY2lKZlcwTklMczYrNStBd0xQT0RZYWUxdWM2QmtoOTVNU0VKNXRMWHA1dkJmbjZaNG56K2tMU2FJWnJiM2F0QVhtUHpudTgycld1V0pEQXFLVmVrb25XUVFmTVBIMXlnNDl6NTNtOFgxNElMRUtFWjdJZ09yeFQ5UjlGa1pJdmZEYmxYVUx1djRNTkRMVExtbnUveUlpaEdoNDd3bXd2dERJSnBINkFkZ3NGcFF1TFZjQ3prNlJmakNMQzZnb0VibGNkSjN0Yk5Rdk5NdlQiLCJtYWMiOiIyNzYwY2QxMjgwY2NiNDA5ZTQ1YTk1N2NkODdmYTE3N2U4ZGIxYjM5NTU5ZDhmM2UwZWYwNzRmNGRiNzhhMjcyIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:46 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImlTdmJnR3FlQWQxa21sK1puNFNvOXc9PSIsInZhbHVlIjoiSDBqM0hjTTVaZlBoRlN1RUpSUkNXQW9STmJPMkZnakhOUWltWnVvRWszZjB6aGxxa3FOL1FSQ25OaWc2eWFJNnR3cXgraklnVXlxME1MWnVVRFZaQ1pVOURHQ1Blc1N0SlR3K1VLbGFWaDBXK2ZQMVJzTFA1R2RpQzlUNnIyeTB0Q2VHUWorblE1Z29naDJad0hiLzc3cGRiS1JVRmM1K1NQNVlTUjY4R0VGbyt2V1d6NW9sdjFtSU9zemhEcTFPUi9pYlNIMmR6T1ptTnV5a3FGL2VCNHBITG5aRlEvSVBSTUgyVlphdkQvem0wb1FrTUdteHRTMEJiTVNDaTN4bHhuQnhmNVJoZzhIWS9NRnVCcGVMbWYwZVFWZXlkYldtNjZVUFdjWTRsRWFMK0sweWIvS3RVdncvZG1RRDh2d2Jua0ZXdHZPMExXSXZXcStyc0JSMVVETkpidTZOSXpTdHQ3ZmI1T2txT0NCM3hQdjZKWW84VjdicVp3Z1llNlRBQnlxVkQ3ZzZrMUdQN2sxMHFIS3FsL2RMUVNrQ3VXRWMwTlovY0JkUkZIV3g4bVN3cmxHTzEvaURXNURiQzdRdXk1OW1UU1VCZVNuM2V3b3RrTWVvcHhZaXRIVzJLL1pHVGhFT0xKTGoyek9NUE9JcE9wUTVsaG5qOE4zNmNnUXMiLCJtYWMiOiJhZjFkODllNTM4NjJlMWU1Y2EyNmQ0YzFiYzBlN2IwNWQzZmViYzRmM2NkNzZlOWJhNWM1ZDA2Y2MxNTg5Mzc4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:46 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im56MXJDMzNIangxbUFFYlNmbldhaHc9PSIsInZhbHVlIjoiTUV0NXNkOWNBdEdFQldEMW15Z1hrdkdzWkxuTktPWWpLaTc0WDg2VFJ2RmdMM3VkRG1qenBtclVqYmdweG1idGo1SjZVRUFxMTdLeXkwU1F1MGxKbDViYWxmaU9CeWo2TkNwSlo3TEpCNk9JZkRkVzZWVXhJaDRjVnp4RWttWnljUndnbERNME9Tb3dXZXRxU0ZWZDV1ZmhUV0l2WGNEZWpqVldCOStUUTYwU25zWU1mZGcyOC9iS2tsTTRra3RDNE9ZRS9KbGtSaFY3UStuRmZaa1g0aWZ1dVVPTlFjNXRuTTg1M09QL3FGRnFLeGxwNk5Kc3dyYTdKTzhrQVJuaUs0MFVEOXlhT2hqb09pM0ZJQUF5QUs4V2E4QTArQ2UyK01PSERSek1uckR3bzQ5ajdRY2lKZlcwTklMczYrNStBd0xQT0RZYWUxdWM2QmtoOTVNU0VKNXRMWHA1dkJmbjZaNG56K2tMU2FJWnJiM2F0QVhtUHpudTgycld1V0pEQXFLVmVrb25XUVFmTVBIMXlnNDl6NTNtOFgxNElMRUtFWjdJZ09yeFQ5UjlGa1pJdmZEYmxYVUx1djRNTkRMVExtbnUveUlpaEdoNDd3bXd2dERJSnBINkFkZ3NGcFF1TFZjQ3prNlJmakNMQzZnb0VibGNkSjN0Yk5Rdk5NdlQiLCJtYWMiOiIyNzYwY2QxMjgwY2NiNDA5ZTQ1YTk1N2NkODdmYTE3N2U4ZGIxYjM5NTU5ZDhmM2UwZWYwNzRmNGRiNzhhMjcyIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:46 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1733491815\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1872810934 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1872810934\", {\"maxDepth\":0})</script>\n"}}