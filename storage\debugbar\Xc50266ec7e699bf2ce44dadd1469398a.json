{"__meta": {"id": "Xc50266ec7e699bf2ce44dadd1469398a", "datetime": "2025-06-27 01:13:15", "utime": **********.541539, "method": "GET", "uri": "/", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.173018, "end": **********.541558, "duration": 0.36854004859924316, "duration_str": "369ms", "measures": [{"label": "Booting", "start": **********.173018, "relative_start": 0, "end": **********.491892, "relative_end": **********.491892, "duration": 0.3188741207122803, "duration_str": "319ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.491902, "relative_start": 0.31888413429260254, "end": **********.541561, "relative_end": 2.86102294921875e-06, "duration": 0.049658775329589844, "duration_str": "49.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43367040, "peak_usage_str": "41MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web, XSS, revalidate", "controller": "App\\Http\\Controllers\\DashboardController@landingpage", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=59\" onclick=\"\">app/Http/Controllers/DashboardController.php:59-74</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.014070000000000001, "accumulated_duration_str": "14.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `created_by` = 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 46}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 78}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\DashboardController.php", "line": 66}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.51613, "duration": 0.014070000000000001, "duration_str": "14.07ms", "memory": 0, "memory_str": null, "filename": "Utility.php:46", "source": "app/Models/Utility.php:46", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=46", "ajax": false, "filename": "Utility.php", "line": "46"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "c0QLm2IJSY55XLSie7FZIMItvVnGUAnfB3XPkDy7", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1807675783 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1807675783\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-113942170 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-113942170\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1952534748 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">prefetch;prerender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>purpose</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">prefetch</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1952534748\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-65033901 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-65033901\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2070671730 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:13:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-methods</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">POST, GET, OPTIONS, PUT, DELETE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>access-control-allow-headers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">Content-Type, Accept, Authorization, X-Requested-With, Application</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkNRVTRqaWhjakhGbnROZzFmYzExaXc9PSIsInZhbHVlIjoia1RvMU42ditCT0tiRXlHVDNaaVFubkNDT09mMHlRc0hDRGxwZFR4QUx2Z2JkcHBOTnY4d0tpc2d3OW50Q0NpYjdDdnZNTHEyT0pINnB0MjRhb1RSYXh0cUdOeStvNE0zSWhXVCtEYVhuYVIzeVh4UW4vZHhPSk5EQURyVVRCSE5xUlZMa3hJYkFub1k2RHNDbmE3VVdOUVBNbGdlR2VQTlg5YmdnZ2JXazZ6TzV6QStNZjhyelFyN2xqckhLcFgvcnBXd2xCMWNGWTZrZHdkbGg0aFdzQkg5WmJwRzg5bktVZjMvc0lvUTJTWGo3Qit4Mm1ZaG1oZVFSVWZRMnA5SWl3OGw2dU11WGE5bHk2NFQ4YTJwLzlCYllaTExuMlZmZXpMY2dDeWhMeWJ6STF4aEtiY0N6YzByekgwUnc0MGRlNTFzUjRXb0wwVXJTZlQ2RUNhRG5YZTQyT0dMaHEvb2hkR2pLWmZPUU1VdE02ejMwZm9pNmtacTdWUUp1R3F1RUFFQ3hUZG5LaWhyUmxlQTNZc1FSZ1RhVTZoOFVSanFvY0FFV2FLeXZaV0kzZDBVSllWUE9tL0g4TnpVM1ozalBmbFZreXh4SFJ6aDVwVFV5M2IzdmtjRTRZdFB4NkRsNFJucVBLbzV1T2NoUTgrUXF5dG54bXduZkF5MjZmSEciLCJtYWMiOiJjZjdlYWI1ZWIxZTQ0MzliZDJmZjY2ODE4NmJiZTVlN2JmY2IzYTc4MjhhOWViZGQxMGIyMWNmZWE4MDE4ZjNmIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:15 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik84NWFJTDJFOEZHbUNlb1VlOWhoNVE9PSIsInZhbHVlIjoiM0s3R0JUdS9vVytCK3ZlSkpZVHIxbE9QVnFyU3JKSjhWdVAwQWNvbjUrTXNzTndQNWx4NGZESEtpNTAwcjFoc0hJdkdsUHV5ZVhJWkVUZjAwZm0vd0d6bFQxelkxZlQyTmJjQ1UrVVpZemY0L2lBY2JSN2dNVWd4cWFpUzFYWE5hOE1PSGtmakFIUkZCN1lJUDdRNTBXVTBIVmtxTGY3Z0RYNndHbXdkazRWaEJFVXc2Q0wvTXF0clV4YWQ2SEJ1RDEySHRIbEw5YUo4Wnc0eE0rOHJuajY3RjR0YzIxNERKenYzREprM2xSaW5vN05tNU5QMkQrZjJRYWxuYm9yTnhwaVMycldPQTJRQmRNWTkrRHQvVHBOWGYxaGhsZ3BJZURJVXk0YmFLc1dLK0w3QWVabnlSR2haV0JuWjNKdDVZRk42ZXVTUnlkQTJHY2JwUVErWldZVVJuR2pSZ00vbjRDeG1SSTRCK24rL082WWU5OWp1Q2VtaXY0dTF1UGJsTnQ0b25FWU5DMHBxYXdGWEQxT0pOaCtKSGZpV3ZTd3NvTHEzWEFSN1AyTVdyVDl1eHRlTC9nOElZd0dGaGVQQ2hYMVgwcFVzNEJDMUloeWZNemFTc0FSZ1NlaDVGSk4yQUlTR3oxbFN3V3QwcUJOOEJPYjUyNElobGM5OUFQM28iLCJtYWMiOiIxZTg1NTMzMzg5ZGY3YmNlYzYwZTIwZWJmMGZjOGU5ZTVhODNjOGQ4ZTUwNGZjZWNhNzU0MmZlOTM1Y2MwZTI2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:13:15 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkNRVTRqaWhjakhGbnROZzFmYzExaXc9PSIsInZhbHVlIjoia1RvMU42ditCT0tiRXlHVDNaaVFubkNDT09mMHlRc0hDRGxwZFR4QUx2Z2JkcHBOTnY4d0tpc2d3OW50Q0NpYjdDdnZNTHEyT0pINnB0MjRhb1RSYXh0cUdOeStvNE0zSWhXVCtEYVhuYVIzeVh4UW4vZHhPSk5EQURyVVRCSE5xUlZMa3hJYkFub1k2RHNDbmE3VVdOUVBNbGdlR2VQTlg5YmdnZ2JXazZ6TzV6QStNZjhyelFyN2xqckhLcFgvcnBXd2xCMWNGWTZrZHdkbGg0aFdzQkg5WmJwRzg5bktVZjMvc0lvUTJTWGo3Qit4Mm1ZaG1oZVFSVWZRMnA5SWl3OGw2dU11WGE5bHk2NFQ4YTJwLzlCYllaTExuMlZmZXpMY2dDeWhMeWJ6STF4aEtiY0N6YzByekgwUnc0MGRlNTFzUjRXb0wwVXJTZlQ2RUNhRG5YZTQyT0dMaHEvb2hkR2pLWmZPUU1VdE02ejMwZm9pNmtacTdWUUp1R3F1RUFFQ3hUZG5LaWhyUmxlQTNZc1FSZ1RhVTZoOFVSanFvY0FFV2FLeXZaV0kzZDBVSllWUE9tL0g4TnpVM1ozalBmbFZreXh4SFJ6aDVwVFV5M2IzdmtjRTRZdFB4NkRsNFJucVBLbzV1T2NoUTgrUXF5dG54bXduZkF5MjZmSEciLCJtYWMiOiJjZjdlYWI1ZWIxZTQ0MzliZDJmZjY2ODE4NmJiZTVlN2JmY2IzYTc4MjhhOWViZGQxMGIyMWNmZWE4MDE4ZjNmIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik84NWFJTDJFOEZHbUNlb1VlOWhoNVE9PSIsInZhbHVlIjoiM0s3R0JUdS9vVytCK3ZlSkpZVHIxbE9QVnFyU3JKSjhWdVAwQWNvbjUrTXNzTndQNWx4NGZESEtpNTAwcjFoc0hJdkdsUHV5ZVhJWkVUZjAwZm0vd0d6bFQxelkxZlQyTmJjQ1UrVVpZemY0L2lBY2JSN2dNVWd4cWFpUzFYWE5hOE1PSGtmakFIUkZCN1lJUDdRNTBXVTBIVmtxTGY3Z0RYNndHbXdkazRWaEJFVXc2Q0wvTXF0clV4YWQ2SEJ1RDEySHRIbEw5YUo4Wnc0eE0rOHJuajY3RjR0YzIxNERKenYzREprM2xSaW5vN05tNU5QMkQrZjJRYWxuYm9yTnhwaVMycldPQTJRQmRNWTkrRHQvVHBOWGYxaGhsZ3BJZURJVXk0YmFLc1dLK0w3QWVabnlSR2haV0JuWjNKdDVZRk42ZXVTUnlkQTJHY2JwUVErWldZVVJuR2pSZ00vbjRDeG1SSTRCK24rL082WWU5OWp1Q2VtaXY0dTF1UGJsTnQ0b25FWU5DMHBxYXdGWEQxT0pOaCtKSGZpV3ZTd3NvTHEzWEFSN1AyTVdyVDl1eHRlTC9nOElZd0dGaGVQQ2hYMVgwcFVzNEJDMUloeWZNemFTc0FSZ1NlaDVGSk4yQUlTR3oxbFN3V3QwcUJOOEJPYjUyNElobGM5OUFQM28iLCJtYWMiOiIxZTg1NTMzMzg5ZGY3YmNlYzYwZTIwZWJmMGZjOGU5ZTVhODNjOGQ4ZTUwNGZjZWNhNzU0MmZlOTM1Y2MwZTI2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:13:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2070671730\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1010923010 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">c0QLm2IJSY55XLSie7FZIMItvVnGUAnfB3XPkDy7</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010923010\", {\"maxDepth\":0})</script>\n"}}