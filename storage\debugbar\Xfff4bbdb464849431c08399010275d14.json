{"__meta": {"id": "Xfff4bbdb464849431c08399010275d14", "datetime": "2025-06-27 02:23:53", "utime": **********.649955, "method": "GET", "uri": "/product-categories", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.250091, "end": **********.649967, "duration": 0.3998758792877197, "duration_str": "400ms", "measures": [{"label": "Booting", "start": **********.250091, "relative_start": 0, "end": **********.5788, "relative_end": **********.5788, "duration": 0.3287088871002197, "duration_str": "329ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.578809, "relative_start": 0.3287179470062256, "end": **********.649969, "relative_end": 2.1457672119140625e-06, "duration": 0.07116007804870605, "duration_str": "71.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 48198040, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET product-categories", "middleware": "web, verified, auth, XSS, category.access", "controller": "App\\Http\\Controllers\\ProductServiceCategoryController@getProductCategories", "namespace": null, "prefix": "", "where": [], "as": "product.categories", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=203\" onclick=\"\">app/Http/Controllers/ProductServiceCategoryController.php:203-229</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.006769999999999999, "accumulated_duration_str": "6.77ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.6089501, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 22.895}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.618594, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 22.895, "width_percent": 5.761}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 22 and `model_has_permissions`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["22", "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 326}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.6315792, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:326", "source": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php:326", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=326", "ajax": false, "filename": "HasPermissions.php", "line": "326"}, "connection": "kdmkjkqknb", "start_percent": 28.656, "width_percent": 9.897}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 312}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 215}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 261}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 129}], "start": **********.6334798, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 38.552, "width_percent": 6.056}, {"sql": "select `product_service_categories`.*, COUNT(pu.category_id) product_services from `product_service_categories` left join `product_services` as `pu` on `product_service_categories`.`id` = `pu`.`category_id` where `product_service_categories`.`created_by` = 15 and `product_service_categories`.`type` = 'product & service' group by `product_service_categories`.`id` order by `product_service_categories`.`id` desc", "type": "query", "params": [], "bindings": ["15", "product &amp; service"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/ProductServiceCategory.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\ProductServiceCategory.php", "line": 116}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 206}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}], "start": **********.637601, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategory.php:116", "source": "app/Models/ProductServiceCategory.php:116", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=116", "ajax": false, "filename": "ProductServiceCategory.php", "line": "116"}, "connection": "kdmkjkqknb", "start_percent": 44.609, "width_percent": 37.371}, {"sql": "select count(*) as aggregate from `product_services` left join `product_service_categories` as `c` on `c`.`id` = `product_services`.`category_id` where `product_services`.`type` = 'product' and `product_services`.`created_by` = 15", "type": "query", "params": [], "bindings": ["product", "15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/ProductServiceCategoryController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\ProductServiceCategoryController.php", "line": 209}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.642148, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ProductServiceCategoryController.php:209", "source": "app/Http/Controllers/ProductServiceCategoryController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FProductServiceCategoryController.php&line=209", "ajax": false, "filename": "ProductServiceCategoryController.php", "line": "209"}, "connection": "kdmkjkqknb", "start_percent": 81.979, "width_percent": 18.021}]}, "models": {"data": {"App\\Models\\ProductServiceCategory": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FProductServiceCategory.php&line=1", "ajax": false, "filename": "ProductServiceCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[ability => create purchase, result => true, user => 22, arguments => []]", "message_html": "<pre class=sf-dump id=sf-dump-348274998 data-indent-pad=\"  \"><span class=sf-dump-note>create purchase</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"15 characters\">create purchase</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-348274998\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.636711, "xdebug_link": null}]}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos\"\n]"}, "request": {"path_info": "/product-categories", "status_code": "<pre class=sf-dump id=sf-dump-1259949272 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1259949272\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-698399573 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-698399573\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-557721261 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-557721261\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-508348583 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991028607%7C21%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjRVMGNmTi8rSVBhWkk1REZhbURaMnc9PSIsInZhbHVlIjoiMU5Wd0ptUFNjUGtuZ3FTVWFhajdrSnRibnJwMW85Uk5TVTU1WWh5NkVqUFVTVmVHS0ZRNzRPeWpsRFBMQlFQaVpzQmFZaGJxend1UDA3dGtOcnU0SWk1VUhmTkhERUk0U2J0SXFIM0F1ME1zYzF3SFdTbUprZVdaL0E4T2hDRmZtU2FhVkNvc1YxRFpIK1JHaHlValU2NlFGRVBiaWJEcDBGV1dSSW5Gc3RhV043TnBBVUdrbTF2cEcydXdyQ1ZKRHFvZUZFUVgveks3L3hEMjZMSmJrV2ExMWdSQWRWSmZ0cWNjQXpiSXBXY0ZYTWNDcXRjaTc1SzRhZnM1ZVg2STJySXNYSldCTzg3dWFtYWVTUDFQVkR4M1I5eFpyblh5cGVTaHF0bG1WekxndmFKM1JBdjlvWTZLNzI5MGdpdGU2UzVKb1NwNFpIQWljOGNQRFdDSXRiYUEyaWlIanFhS1NBMFJRdkFBZEt6bnJLZXN1RFV2SEg4aFQvRS9Vd3NsZERqZ1ppbFVnZDZnWE0wYzZlMm1USndaTXpuRDlqWUZyckt5VEs3ZVVvd1JvSzFZNTFGVjhORUlQeVNhNm82UHRRckRYbVAwcUFrVHMwRnJnczF5cWNZeEVYR05mYzNVZlhidkJJN0xBd2ZVN0tQZHhXeVREYWFhZGFBOGZGN2YiLCJtYWMiOiIyZDA5MTNhOWFjYTE1ZmJkMGY4ZWZmYzJjMzFiMWIxM2VlZmUxYjk0NWRhMTlhYTc5M2M2MTI0NjFjNGYzZGFhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IitBRHJtV0FNUGNSdzNSekpmQVFaaVE9PSIsInZhbHVlIjoiamV4cmhrNWpuUTI4T0d0NGNwdHplb1NncVhtUzFTU01ieHM5aGJqNVRjY3RVQWhZcHF4T1ZaTUlkWjFWNFB1OVJ3VDJBeDZHWHhBYWJ3UHNoRHFtSWhtUUo2UFF3cFBRRUxyekFqM3VLNDRoY3pHUXd5SCtVYkVTeFdoNGUvNFBUNkVRL25Gd0VaWjJGMllZRW1iUGJEdXFZSDF3S090aXp4V0x6eWEvTllLTHNFVW50UmNxOTRESXhpYWdxOHBGOVd3Lzhadm03cmtVY2U3aXdxK2JtMzR5dGUwaWZOQWpWZUI0bFpVeEpEMnJta3dleXhSUFJVaVVnNEpKS1V6TGM5R2trNklabmwzQys5ZmZhMDY0YTNxZmp5UDVQOGNQMEw2OXoyVGJ3aUpVNy9KZzM4MUpXeFA5TC94MUI5VXdxcVVwZVY1cTlGN3oxNlhOSzJEazIzYUZhQnRiRFQza29XemtGTFVEV2lzWHg4Yk01RURFM00yZnExVm1FdzNEKzdqQVd6WWxVU0UySkhpaW8ycHZHZjFIblc1c1NnYVNJM1R0ek03U3dobXlMOFNkUzYrWEdwNjBlc3JiTEF5OVVqSktCaFZBKzJ3SUprWUtXcVM0UjNieU5hOGtReWhSbkRFYXI2QXJidDlPdmF6M3BhU0dlL0FXc25uK1pmNVAiLCJtYWMiOiI1ZDM4ZDQzOWI2MDViNWVhNjQyNWViNTFmMmE3ZTdmYWIxODhiOTEzZGNjYmJlMmMzYTk4MmVmZWM0YWFhMWYyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508348583\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1557998935 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1557998935\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1034981024 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkcrV3o2Yit3Z1V4QUlTSEMyQVZWM1E9PSIsInZhbHVlIjoiOGk5MDdaR3VGSVpGUTFoT1ozQTdGT1BhdTdhbU1UQ0FhSFRRd2VVcndkOG1QUks0cHBmbUIwOGFjWGhPM3NnUjVUdmt0K1RBTW5ZNytmMG9OeU8zU2ZBMmJadzdSQW9rZWZYTnpYdHJxTzRHVyt2ZHkzVUQwT2FKZ2lFMHExaWROT0FXeXRuM0EwL2tkZXVabVJ2MGhjckFDOUdGeFZlTy9HeStMdjRwK2FEczEySmhJT3FiZkI5SmxNSUpMVC9UeE80OS9FR1l3NUl1QWc4cTlENXI5SUdpVFQ4ck5DM0tEOTl3L3BkRCs5N1J0bGd0dFY4SHRWeTNxZ1hHaVpVVUkxWVNDNlBIVFJ6TjZGem4wblpEcnhRdjMwa2tZRXR1bStNc3lVUW1UMVZaMjVkVzh5RG5paUVHcmgzOVZIbFRva3J2a0NVUWpoSFA3V0NyVUhnNGQ3cXdEenhoeTNnL0JQQ3FyS2UvRWhmcDBGSE1qMVhwcnhFcDhYWmYxK2NnV21wbVRaaTFVeElOVnRXaGw2M0kvSnV4eURyaXBoUHZhejhTc3dpd1J3aUNQQTRyaUV5OUliZXhHNVhWWWF4bXBIUjNTZ2J3eFd2ZVdsUDJQZlhTTk41a3p2UmZ3VS91YjdEampPMFRxUmhOT3hiVTNoU3RvaHA4eE5zeDhHcFoiLCJtYWMiOiIyMzdhNGMwYmQ4ZmVhODgwMjA2ZTRiNzIwMGI5OGFkOWM5MGQyYzYwODFiYTlhZWE3OWM0MTkwNGY0ZTEyODFhIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:53 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik9MY1E0Wk53Q2tJN0JBck1iWmdxL2c9PSIsInZhbHVlIjoibTU2R0F1N1FYMFRna0haVXFsNEltYnd1cXJudEcxYzZPZWVIdWkzYUJOVitjTDZoT3RIdmVVT2Z4QUdFclErRE9aVzdXcXhLcVViRjk5RHZqcGFBN1hkOEo0QVVsTDd2bU16RVMyN3JvMnRCVlFjczZUTnhJUHRpWkpidC90S2xrVzZqaFF4dWwzbXJmYzlkeEZqcTh4RmoxMGpWUG8wMnJOQTlKSE0yb3JjWlZKNlErVEdILzVyR3AvaHUxY3FybUJFNldzS3ZBNi85cGxlK256QWFOQS9mMy9reHVZcUhjS1BHd3BOSXFhUHV4a3JrR3RtSXVvZUdkM3ZxN3lpbnZUK05waDFYTUNUS3RjbFU5S3Rsd2RrL0ZYYUhhejh2WHVUb2EwR1p4QVlTbCtSTG9NQjlkZnB0bTlYdG5wRlBPYis2VktlQU5kb1RwZGVlODlPdHJHZ2ZtTmJpQVpJUTBSNklzV2t4TTFMR2IrbmJraStrcnhtc2NiUTBuRlBSWi9ESnBrOTN6K0tHc2Y4dEQ4U2FIM2hJaE8xTXArQ1k2WEY3aVBSRGsrc1AwaUZEcDZFSjd5UFNIbXNxNjJaSnFMUTByejV4SlFQczdQNGFIKy82cE9oajYyQWxNL0Uya1VVWHhzT3YzM1ladVYxOUtKL0RoS1hWMTloYXlDVmgiLCJtYWMiOiJhNDhjMDBhMmM2MDdlOTZkZjIxZWNiOTFiZDMwNWIwZjU1OGVmMzJiNmFkZmI4NDRiZjQzYWVlYmRjZTNiYjljIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:53 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkcrV3o2Yit3Z1V4QUlTSEMyQVZWM1E9PSIsInZhbHVlIjoiOGk5MDdaR3VGSVpGUTFoT1ozQTdGT1BhdTdhbU1UQ0FhSFRRd2VVcndkOG1QUks0cHBmbUIwOGFjWGhPM3NnUjVUdmt0K1RBTW5ZNytmMG9OeU8zU2ZBMmJadzdSQW9rZWZYTnpYdHJxTzRHVyt2ZHkzVUQwT2FKZ2lFMHExaWROT0FXeXRuM0EwL2tkZXVabVJ2MGhjckFDOUdGeFZlTy9HeStMdjRwK2FEczEySmhJT3FiZkI5SmxNSUpMVC9UeE80OS9FR1l3NUl1QWc4cTlENXI5SUdpVFQ4ck5DM0tEOTl3L3BkRCs5N1J0bGd0dFY4SHRWeTNxZ1hHaVpVVUkxWVNDNlBIVFJ6TjZGem4wblpEcnhRdjMwa2tZRXR1bStNc3lVUW1UMVZaMjVkVzh5RG5paUVHcmgzOVZIbFRva3J2a0NVUWpoSFA3V0NyVUhnNGQ3cXdEenhoeTNnL0JQQ3FyS2UvRWhmcDBGSE1qMVhwcnhFcDhYWmYxK2NnV21wbVRaaTFVeElOVnRXaGw2M0kvSnV4eURyaXBoUHZhejhTc3dpd1J3aUNQQTRyaUV5OUliZXhHNVhWWWF4bXBIUjNTZ2J3eFd2ZVdsUDJQZlhTTk41a3p2UmZ3VS91YjdEampPMFRxUmhOT3hiVTNoU3RvaHA4eE5zeDhHcFoiLCJtYWMiOiIyMzdhNGMwYmQ4ZmVhODgwMjA2ZTRiNzIwMGI5OGFkOWM5MGQyYzYwODFiYTlhZWE3OWM0MTkwNGY0ZTEyODFhIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik9MY1E0Wk53Q2tJN0JBck1iWmdxL2c9PSIsInZhbHVlIjoibTU2R0F1N1FYMFRna0haVXFsNEltYnd1cXJudEcxYzZPZWVIdWkzYUJOVitjTDZoT3RIdmVVT2Z4QUdFclErRE9aVzdXcXhLcVViRjk5RHZqcGFBN1hkOEo0QVVsTDd2bU16RVMyN3JvMnRCVlFjczZUTnhJUHRpWkpidC90S2xrVzZqaFF4dWwzbXJmYzlkeEZqcTh4RmoxMGpWUG8wMnJOQTlKSE0yb3JjWlZKNlErVEdILzVyR3AvaHUxY3FybUJFNldzS3ZBNi85cGxlK256QWFOQS9mMy9reHVZcUhjS1BHd3BOSXFhUHV4a3JrR3RtSXVvZUdkM3ZxN3lpbnZUK05waDFYTUNUS3RjbFU5S3Rsd2RrL0ZYYUhhejh2WHVUb2EwR1p4QVlTbCtSTG9NQjlkZnB0bTlYdG5wRlBPYis2VktlQU5kb1RwZGVlODlPdHJHZ2ZtTmJpQVpJUTBSNklzV2t4TTFMR2IrbmJraStrcnhtc2NiUTBuRlBSWi9ESnBrOTN6K0tHc2Y4dEQ4U2FIM2hJaE8xTXArQ1k2WEY3aVBSRGsrc1AwaUZEcDZFSjd5UFNIbXNxNjJaSnFMUTByejV4SlFQczdQNGFIKy82cE9oajYyQWxNL0Uya1VVWHhzT3YzM1ladVYxOUtKL0RoS1hWMTloYXlDVmgiLCJtYWMiOiJhNDhjMDBhMmM2MDdlOTZkZjIxZWNiOTFiZDMwNWIwZjU1OGVmMzJiNmFkZmI4NDRiZjQzYWVlYmRjZTNiYjljIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1034981024\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-805108804 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">http://localhost/pos</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-805108804\", {\"maxDepth\":0})</script>\n"}}