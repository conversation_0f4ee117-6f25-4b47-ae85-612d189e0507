{"__meta": {"id": "Xded2fc263a5a42a57c43242c12dad55b", "datetime": "2025-06-27 02:23:36", "utime": **********.208411, "method": "POST", "uri": "/event/get_event_data", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991015.782943, "end": **********.208425, "duration": 0.42548203468322754, "duration_str": "425ms", "measures": [{"label": "Booting", "start": 1750991015.782943, "relative_start": 0, "end": **********.155631, "relative_end": **********.155631, "duration": 0.37268805503845215, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.155641, "relative_start": 0.3726980686187744, "end": **********.208426, "relative_end": 9.5367431640625e-07, "duration": 0.05278491973876953, "duration_str": "52.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45394752, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET event/get_event_data", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\EventController@get_event_data", "namespace": null, "prefix": "", "where": [], "as": "event.get_event_data", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=317\" onclick=\"\">app/Http/Controllers/EventController.php:317-345</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00276, "accumulated_duration_str": "2.76ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1906981, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 69.203}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.2005808, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 69.203, "width_percent": 15.58}, {"sql": "select * from `events` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/EventController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\EventController.php", "line": 327}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.202998, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "EventController.php:327", "source": "app/Http/Controllers/EventController.php:327", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FEventController.php&line=327", "ajax": false, "filename": "EventController.php", "line": "327"}, "connection": "kdmkjkqknb", "start_percent": 84.783, "width_percent": 15.217}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]"}, "request": {"path_info": "/event/get_event_data", "status_code": "<pre class=sf-dump id=sf-dump-2047972435 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-2047972435\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1080269232 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1080269232\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-603872029 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-603872029\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-770382403 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991013146%7C17%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImFVTVF6aFJpaHhBdEQrVWhkK3ZQd3c9PSIsInZhbHVlIjoiZWkyWFM1TWF5elpaYXN1SXZlSUR2cW1LbkdhUGxId1VIR1p1MGVoTlVsRUg5b2JRdnB4TE8zbC9aclpJZjlXRmlVUjdqOHh3UFdPbDBjV2ljTERVTmdJenpOSmdXbjdzRUdmaGpONTEvSmVSY0dEKzhNK2xhaDR6eVgreEk4N3JoS3gvc0xtMjN0WWdBL3pGUFk0dDV6clduNms3bk9ycDB6ZFEvQlA0am9CRjRwYVVlSUJsTGtmUFRnRnpvQW9hR2tIRlFlbjZCeEZydTY2NUNrUm1yblcrbk9zakhCd3ZFZFlESkFSLzFnTlA5NFFhZXlTNTd6SEc0SktyY296cDFZRGdNWWRicjJmYXZNZ3dSbDkxWVpQMmd6THBSTXFJNmNHdEkyczQ1TGwwcnhFdTA0NjVWUW45VTJia1J5ZHVMaklydXB2OW1CVFczL3dOZm0xMDlubjBsa0grMlIraGVYdjNrVG1VK3c3Z2hWNFRJaU50Njd6L1RxdHF4UGhvM3lYaVJ6UnRLeERmMTNBRTVyWmJKUXhzZlVnTmVHcndkbXRkNEI2aE10ZHZqZ0tzemlrUFFiWWg2bk5zbkZYOUJhbTNzUnJLeFAwejdDOW92aGY1bDM5K3puMmNsK05tSEtPbnpUanM2NjRLS05XaFFEeWsxZGEvOHhWaTJqMnIiLCJtYWMiOiIwZmYwOTZkMmU0ZThjYTU5ZWVlNWRlZGZhNzY2NWIwYzlmNjI0MWQ3NGYxZWU4MmFkMDE2NTg2Mzg2YWI3NGJiIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjBZVEs1dXRLNzlwYytxNjVaWlhHWEE9PSIsInZhbHVlIjoibmpCNVQrTHpWb2c4enBRckRYa0F3Tjd4ZWV5c1JLK1pCMUY5Q0tCSDl1UXV0bmlZeUpaN1FuTGxCbnBXUnF6cmh4SkJqU3VDWmdoYjI4QmlkNkxzeWpDVE5ldzVDOUhlQWQrQldqZGQ1WVY4aXBFeHVDcWVDTE9RV3d4MnNpLzhFMzN1dGN1NzhWNFJsN2x5OVVzNkUxNTEwMTM0RjhxWWcrbEFZVDR4UElSSS9JOUZTRWNKc1p0RjFpRXhQb25tNHFLa09pWExBYUtKd1VsT2U4SHN3QXlUbEQ5eHQvbkt3bEpTcFUvVzNTeVlkOXdWSkNYMjVkekZCR29ZVllPa093NHVNUGg0RzVzM2s5MXZxQjlmQTN2V1hJL2F2c3BzMGpMRTcrdENTK1I2ZS95bXdxZEVvbjNTbmdYTEwraGNhNzFIN1JxMDJad1pxQ004UDlxeUVETmRrWSthQThPZWQ0K1RNaHZOb0drSVZnM3o2Q0gzQjU0QTBmZ0Y3bjBSWkgvU0czWjQ4SmRrc2RCMko1QVBWWVN1VFhrSHplTnJDUk0xYXBMRHB4a2lkQ3UvVysxRzZGa0hUeVpLQnZQMnlPbW5hSHdvZlVndFB6SEYwVEh4c2FXOEovdUR4eXdsMG1JVjJ2QURGTWZkRENhazNrMlVjUGE1cXlwNFcvanUiLCJtYWMiOiI1MGFmNjljOWYzMGU0YjQ2ZjM5ZjY5NmVjNzFjZmYzZWI4NmE3MTQ5Mjk2ZjVjNThiYTBjNDEyZTc5ODMwODdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-770382403\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-568981515 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-568981515\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-442539528 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjMwbU0yZ3pMMGp3SSs1OGUvTDlmaUE9PSIsInZhbHVlIjoiTjVjT3I2ZkROTEhIcng4M0lZcVlzemlaQ01hYkJnQUh6ZS9WVWM1R2U4U1lGYWp5NUFpT00yYWxhOUg1UnlVbmliZy9QOEcvaXFSOEREMW9Ua1htRDY0cnluVHh5UWY2dkVCWXpvUFJ2V0JRMDB5K0ZqZEY1NHc4REQ0RVZlSmxVVk9GYzdVSWFJbW13MzFtejF2aVNHYXhMZlR6SlRkSkVSRlByNkwzclRoK3g5QzBOZTd3eHowakx4SjZhbVkrVFlCMVRnOTh2QjF3emwzdFJ6Y3AweWNiU28yYm1VeHg1NnBTSHJFeEdoQXNZeU0zZFRNQUlZNEl5Mm1MYnA1ZlV6LzZ0S3g2OTlWZnFTaEZmUjZtZENFbWFPUmdzRGF5VkUwbjJrUkpMTnZ4RXErZHRLeHU0eGFqa0E2QnpEYzJ0VnlSYTJRNFV1QXYrYU1uc3B6YUZ3L2ozWFVaRGdxVEZ4aG9MYi9Ta3AzdnJQcUE5RlUrMnh4OWhpd1V5WGYzWEhxaDczMitjR0ZEODFTcGZZMHQzQndKU3VhRms1aEpxQjMwVGkyYjVTcjVNc0JuZEQ0c2kyaEg3eHRoSHA5akRyN1UvdGFhSEJpTi8zRkZBZUQwNlNGc0NITnVvL0VSelNCT0lzWVh0QjQzVHpEVlFHeWlMWVNRdjBFNEp1bi8iLCJtYWMiOiI1NjVhY2E4ODBhMTUwOWYxMTlkN2JlYjc1NjExYzNiYTcwNzVmMTQyNTRhYzZkMTUwNzE5OTA0YTcwN2JmYTFkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:36 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlVuWEJBN2NvVjNHZUFvMnhKZ2VLcGc9PSIsInZhbHVlIjoiRW5DL2trQzVqaTgyeWdqd05CU2owWEZodlRld2sxK2hSdElibDM0L0VaNDlhbWxUeHVXb015ME0rLy9HaDFObUN0WUxCSUxaM0FwTmtkS1VPZzBNazlWNmxOTG5yZUE5Z2UwNUtUMEkzdnhLdm44a0pvY3dUNktOM29RWkJOUi9WaGhQY2k1ejNHdUFWa1A3R2J0c3BRN2tCOURES1pUcEFxbW9EMXE1RzJhMHk1VjRPQUsyMTZiQit6ODhqb1hJS0ZhTCtjTHE2YWR3YXpOS2w0YjBDWnV5a0lpZWhwYlQwU2szUTVrTkhRcGlLYzJ3SktITzRENk9OelBJSGt5ejZ1RjlidXZOSjhCNUV3M3ZqbU91WFJGa3RLN2d2dzdBZGJ4NlByWFdhQTJZVWNkaTI2NGNZb1p6SjJ5SHYxbnVmVzNSM0VhZnlNcDg0ZkR0MU9TT3R4ejN6Z3RGTWdCcExwOWRLTGZzZDRCRXROYzhmVGFVbXppSUhBTW8xY294dVp2V2RmY3FPUCs1MloySE9mQlkybUNVUzZ6RndPUXJONnVmUGhtb3lkeE5MbWFySC9BbmswYTV0a0kySTA1V3NpeFBoSlo0d1ErYlVFc05vMDM5dEF2VFZWd2xQeitKYUx3V3djcTdaZHhaSFpnMVdzd3FmTU1udnF6L3oyai8iLCJtYWMiOiIwNGZhMDE4MjcyNjRiMDQwODQyMGJhZmJlZjg0NDFkZTU2MDZkMTMzYjE5NDUwNmE1ZjY0OWM3M2JkZjNkOWE0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:36 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjMwbU0yZ3pMMGp3SSs1OGUvTDlmaUE9PSIsInZhbHVlIjoiTjVjT3I2ZkROTEhIcng4M0lZcVlzemlaQ01hYkJnQUh6ZS9WVWM1R2U4U1lGYWp5NUFpT00yYWxhOUg1UnlVbmliZy9QOEcvaXFSOEREMW9Ua1htRDY0cnluVHh5UWY2dkVCWXpvUFJ2V0JRMDB5K0ZqZEY1NHc4REQ0RVZlSmxVVk9GYzdVSWFJbW13MzFtejF2aVNHYXhMZlR6SlRkSkVSRlByNkwzclRoK3g5QzBOZTd3eHowakx4SjZhbVkrVFlCMVRnOTh2QjF3emwzdFJ6Y3AweWNiU28yYm1VeHg1NnBTSHJFeEdoQXNZeU0zZFRNQUlZNEl5Mm1MYnA1ZlV6LzZ0S3g2OTlWZnFTaEZmUjZtZENFbWFPUmdzRGF5VkUwbjJrUkpMTnZ4RXErZHRLeHU0eGFqa0E2QnpEYzJ0VnlSYTJRNFV1QXYrYU1uc3B6YUZ3L2ozWFVaRGdxVEZ4aG9MYi9Ta3AzdnJQcUE5RlUrMnh4OWhpd1V5WGYzWEhxaDczMitjR0ZEODFTcGZZMHQzQndKU3VhRms1aEpxQjMwVGkyYjVTcjVNc0JuZEQ0c2kyaEg3eHRoSHA5akRyN1UvdGFhSEJpTi8zRkZBZUQwNlNGc0NITnVvL0VSelNCT0lzWVh0QjQzVHpEVlFHeWlMWVNRdjBFNEp1bi8iLCJtYWMiOiI1NjVhY2E4ODBhMTUwOWYxMTlkN2JlYjc1NjExYzNiYTcwNzVmMTQyNTRhYzZkMTUwNzE5OTA0YTcwN2JmYTFkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:36 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlVuWEJBN2NvVjNHZUFvMnhKZ2VLcGc9PSIsInZhbHVlIjoiRW5DL2trQzVqaTgyeWdqd05CU2owWEZodlRld2sxK2hSdElibDM0L0VaNDlhbWxUeHVXb015ME0rLy9HaDFObUN0WUxCSUxaM0FwTmtkS1VPZzBNazlWNmxOTG5yZUE5Z2UwNUtUMEkzdnhLdm44a0pvY3dUNktOM29RWkJOUi9WaGhQY2k1ejNHdUFWa1A3R2J0c3BRN2tCOURES1pUcEFxbW9EMXE1RzJhMHk1VjRPQUsyMTZiQit6ODhqb1hJS0ZhTCtjTHE2YWR3YXpOS2w0YjBDWnV5a0lpZWhwYlQwU2szUTVrTkhRcGlLYzJ3SktITzRENk9OelBJSGt5ejZ1RjlidXZOSjhCNUV3M3ZqbU91WFJGa3RLN2d2dzdBZGJ4NlByWFdhQTJZVWNkaTI2NGNZb1p6SjJ5SHYxbnVmVzNSM0VhZnlNcDg0ZkR0MU9TT3R4ejN6Z3RGTWdCcExwOWRLTGZzZDRCRXROYzhmVGFVbXppSUhBTW8xY294dVp2V2RmY3FPUCs1MloySE9mQlkybUNVUzZ6RndPUXJONnVmUGhtb3lkeE5MbWFySC9BbmswYTV0a0kySTA1V3NpeFBoSlo0d1ErYlVFc05vMDM5dEF2VFZWd2xQeitKYUx3V3djcTdaZHhaSFpnMVdzd3FmTU1udnF6L3oyai8iLCJtYWMiOiIwNGZhMDE4MjcyNjRiMDQwODQyMGJhZmJlZjg0NDFkZTU2MDZkMTMzYjE5NDUwNmE1ZjY0OWM3M2JkZjNkOWE0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:36 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-442539528\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1860739361 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1860739361\", {\"maxDepth\":0})</script>\n"}}