{"__meta": {"id": "Xb835fb0edee190f6884c49f0cb293b6c", "datetime": "2025-06-27 02:23:45", "utime": 1750991025.009396, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.579483, "end": 1750991025.00941, "duration": 0.42992687225341797, "duration_str": "430ms", "measures": [{"label": "Booting", "start": **********.579483, "relative_start": 0, "end": **********.959024, "relative_end": **********.959024, "duration": 0.37954092025756836, "duration_str": "380ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.959032, "relative_start": 0.3795490264892578, "end": 1750991025.009412, "relative_end": 2.1457672119140625e-06, "duration": 0.05037999153137207, "duration_str": "50.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722184, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00258, "accumulated_duration_str": "2.58ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.985234, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 62.791}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.994669, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 62.791, "width_percent": 14.729}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750991025.0001318, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 77.519, "width_percent": 22.481}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-1679292075 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1679292075\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1673815969 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1673815969\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-294679632 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-294679632\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1219675519 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991019246%7C19%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlpSVWRRSjZuUStWVkEzaUxTakFPNmc9PSIsInZhbHVlIjoiNzVNb0tSOXBsZHN1c0JrR2FibktFU3NOSDZwVkV4ZWFBTklzZnVWcmxhaXo3V1BpU0NGdUV6cXpaMEpUYUY0L0YxL2ZNVzBZVTBoQll2TnpUbzZtRk5odzJwdFF6WSs5eDZGVm9ETDVmWTArUzJGK0l4VWZ5OW9ucnMrbE9UbVBCZFBTTzcwdVNUTDJ6M25TYXM3akFCNzJBQUFTUVJKK0NCV3lhbGZVemdTNCtvZXBRand2WkdGQnVST1NSelVsTlBmWjBCSm45a3Rubkc3VWxuN25PenQ2U2Ewck9EaXVFQXpjOGVIVUpsSitvdzFzTXlnSmZrQUtXa0UrM3BxeC9sZVplMENYbVRlSld3bkl0YkNqcEJTQ3JWenZWbnJSTHQ4YVc2MGlDbUFiZGdrTHYrb2dQcXRsOEUvK1BvWUdXTGIvSmdpZ2R1NlZETzJBS0plNW9hdHZpWDJ1TzZsa0FzOG1mY1lQUlJrd0pZRTNlMTRVOStxOHdmdkgvYzVZVUpSN1NqeE9pZms1WWxLbzhxd011ZTA5dnVuaHZjeFVOQ0JiTkd0M0pYWFZBOUQ2RW1xNzdydFl5amdEeWJnM1RwRG5EWTRESGRCRTgzbGJZWk51dldrY2kxVFVrdVltZzJWanpvb24ySmg4cU5XVkxUaVZLTEFkdmlLRjNmMGUiLCJtYWMiOiJkYWI3MzQ5MDI1ZDUyN2NkOTQyMDU2YTEwZjFkNWZiZWI3NzA4MzFiZmZkNGUwY2I5Zjc3MTJmNjI3MGRhYTEwIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InhQL3FQNXZsSWFad05VdWJXVHFXV0E9PSIsInZhbHVlIjoiMDArU0ZRU2U4d1RKa1I3V0ZweHZhOHRSNXFEUHdONmg1RTR2WFo5bW5GVmRraUJxQ1Y3TDJEckYxUW5BTjlpalVwd09NcEdPZlExbUVDZmlUb1MweE5STzNTeDV6MTNSbnFNR0JZc1habk85elNFZnE4WkN2djdXSFZUWTdkNHBuc3hDUVl1TzRlVXFsSDVLeWdNS3Rnb1hWMWV1MkdxcnNGb240a3FBRkJDc3pUdWloOXl1Y3dTZUtHWEM4eWJWUElsR0ZDUFRYZVYxSWNqaFBMOStuaWxzdXhMYWlDZmk1ZlB3U092RUJzVGJ6aWVlK20yVk9nL2lISlBHbTFIemRUdDN1Rk9LMm5hOEVGZnVPcEdCNFJSY0RodE90Ly9qbHEyZ0hOVzRsU2NJam50NW1YMWJHc3UrN3VVb1MreHhQc25WbU9QZm5aK09ic3QwTzc3cTJzeVhJemZKTzdCWXp0Mm45MXovd091bjlSb3h3U0NJSWM4T0FKaldzV0wvRWJpVkJhbzZ4RHpqZE9CMys1b3dRWk5FVHdCZWtOelN2ZlkvVWN3eDhEK0YvRDlnYk90YVRKK0ljNWNsaEFrVjdycmRSU0E3RDFZV25YcjhZaTNNVUpDREw3S054QXZuemc0b0lUZHJrTHBYZU9ZT1pZZGlDL0JSKy9sR01BV0EiLCJtYWMiOiJhMzA0YmZjMGYxMDBjOGE4MGNjZDYwNmJkOWExYmViMjg5MjI5ZTg4NDBlNjY5ZDgxZmU0MGU2MmQxMmY2M2Q4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1219675519\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1791978016 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1791978016\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-419022384 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:45 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjdsL2Qzb0dIT0dIRlNiMXEycXExTnc9PSIsInZhbHVlIjoiVE9SR25Ickp1OGE0cG9oaUkrR1NwVXAwTUNwUU1Xcitwb2pJNmhRK1lvMGtWMk1QaFQwWit3QkY4dS9BUjJNZmVmTXNMK3ljbGJ4aW90bFk5Z3NGVWtPN0tRd2dzRTJCZUlhM1RncittR3FMREN1MzVSUThTSEo4NXc5SEVFMjJXTFFpNTE0U09HQWlrOXdtNm5DbkNlZ29QZG95L0gxWWExYmxhZm5nQ2R2dGk3VjhGdXE2TSt1UjdvOGNmSlhkWW8xOUdiaXBtOWViV1J5dWlkVXR2RzQ3YjRhRTQzUURYeUNYZ004N216Q25jNWxGWDZzVFc0cG4xUVRiTDdGQkVMT2VhK3M0d1NTYUxzcEx0T3REZnFmTDMrN0tOWDd1cGhqQmtNZUZ5R004dFJnL2x6YjgxUzVnYW1OWGpKcndyNWwyTEt5c3NrSTlyYU1ySndJYTA0eGgwMGdCaDlJdlJaa1d0UDl2SjFuTDd1YWRlTVlpdjVDS0sxVTZYd0tvK3RFTVNjRS83LzBWRzZUSE5iOTAzOEo4L1p1TkFXa25VN041c0F5QnllcFZvVkVROUJudDV2N3FBM0tvVHRqY3lXeWxsUTdJMkhaRUpBQWh0TjFFanpBRjNXKzNtaWxCeis2M1JlcUl6VHp0YWhGNGtSK1JOemY0YmxiVS9DVHIiLCJtYWMiOiI3MWYzOWQ1ZmJkNjU2NTc1MmNkN2VkYzE3ODVhZDFhYWNlNTExNzc4YTEzNDA3NzEzMWEwNGIwNGFjYjBiMTkxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:45 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImJCdTdkMDdxalRLWjVXMzZQS1hDYVE9PSIsInZhbHVlIjoiamJwb210MmhkcXE0TUdtY2phajNsZk1CK0pmVVd1M245eG1uNFpnRWdEKyt2aVA2Qk5mU01KdmdQbFduMFB3V2RwV21qVEl0UGRtK3J2aVBMaG9xNkdoTk14M3dEaFJCcUZnK2pla2hNb1pici9FSWdDdmw2WGhieUJXQXhobVdPbURJOWgvdGFCa3JWNDZJNE0wM2R1NDlIMHlIczgwWWVQM3BZbVV1T3NsVDdweVBVQldPYVZ3aXNKZWt4NTdiTmRBei9OYmlvUGRBRkF6RGtuUGtSVSs4d2kyZ2ZaNG02R2Q5UkovbGRnTVhjeVZGUzhjVFljaVlBYXFBaythTUlsMzBQZWJJeVAxbDNPYUt4YXJmRWxhODNHa0dBekljVHVTZXZvdTd1VHRISUxHY2xSMU5KSjFxMzdHKzAwQ2x0eXluOFhOTzlsaURzUCtBTkFVWVlZWFdIK2swVWhZWGEzaHdVdldMZEZjTGJhaklUb1ZwNnFjNFZER05qellOV0xLeDVVV0FtdkFTR2l6OVFtK3FkQm5KOVBjdjBQZU05dHRMbGw4R0wrZjZKTm9JVlpnaXAzOE1rVHlRVFlYMXRkL1F6ek10QVdLK1ozalZWREZzRFVyTURQaWtaYUJldk9CbEFrUHppMXJiN0Q0SCszZFZaYVZIaUF3L3VmaUkiLCJtYWMiOiI1OTBlZmUwNzc2MDEwOGRkZDA0NTI1OTJhYWNjYmEwMDhkYzg2YjdlNTY0MDFhY2FhNGYzMmNjOGQ2ZTU1ZWE4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:45 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjdsL2Qzb0dIT0dIRlNiMXEycXExTnc9PSIsInZhbHVlIjoiVE9SR25Ickp1OGE0cG9oaUkrR1NwVXAwTUNwUU1Xcitwb2pJNmhRK1lvMGtWMk1QaFQwWit3QkY4dS9BUjJNZmVmTXNMK3ljbGJ4aW90bFk5Z3NGVWtPN0tRd2dzRTJCZUlhM1RncittR3FMREN1MzVSUThTSEo4NXc5SEVFMjJXTFFpNTE0U09HQWlrOXdtNm5DbkNlZ29QZG95L0gxWWExYmxhZm5nQ2R2dGk3VjhGdXE2TSt1UjdvOGNmSlhkWW8xOUdiaXBtOWViV1J5dWlkVXR2RzQ3YjRhRTQzUURYeUNYZ004N216Q25jNWxGWDZzVFc0cG4xUVRiTDdGQkVMT2VhK3M0d1NTYUxzcEx0T3REZnFmTDMrN0tOWDd1cGhqQmtNZUZ5R004dFJnL2x6YjgxUzVnYW1OWGpKcndyNWwyTEt5c3NrSTlyYU1ySndJYTA0eGgwMGdCaDlJdlJaa1d0UDl2SjFuTDd1YWRlTVlpdjVDS0sxVTZYd0tvK3RFTVNjRS83LzBWRzZUSE5iOTAzOEo4L1p1TkFXa25VN041c0F5QnllcFZvVkVROUJudDV2N3FBM0tvVHRqY3lXeWxsUTdJMkhaRUpBQWh0TjFFanpBRjNXKzNtaWxCeis2M1JlcUl6VHp0YWhGNGtSK1JOemY0YmxiVS9DVHIiLCJtYWMiOiI3MWYzOWQ1ZmJkNjU2NTc1MmNkN2VkYzE3ODVhZDFhYWNlNTExNzc4YTEzNDA3NzEzMWEwNGIwNGFjYjBiMTkxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:45 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImJCdTdkMDdxalRLWjVXMzZQS1hDYVE9PSIsInZhbHVlIjoiamJwb210MmhkcXE0TUdtY2phajNsZk1CK0pmVVd1M245eG1uNFpnRWdEKyt2aVA2Qk5mU01KdmdQbFduMFB3V2RwV21qVEl0UGRtK3J2aVBMaG9xNkdoTk14M3dEaFJCcUZnK2pla2hNb1pici9FSWdDdmw2WGhieUJXQXhobVdPbURJOWgvdGFCa3JWNDZJNE0wM2R1NDlIMHlIczgwWWVQM3BZbVV1T3NsVDdweVBVQldPYVZ3aXNKZWt4NTdiTmRBei9OYmlvUGRBRkF6RGtuUGtSVSs4d2kyZ2ZaNG02R2Q5UkovbGRnTVhjeVZGUzhjVFljaVlBYXFBaythTUlsMzBQZWJJeVAxbDNPYUt4YXJmRWxhODNHa0dBekljVHVTZXZvdTd1VHRISUxHY2xSMU5KSjFxMzdHKzAwQ2x0eXluOFhOTzlsaURzUCtBTkFVWVlZWFdIK2swVWhZWGEzaHdVdldMZEZjTGJhaklUb1ZwNnFjNFZER05qellOV0xLeDVVV0FtdkFTR2l6OVFtK3FkQm5KOVBjdjBQZU05dHRMbGw4R0wrZjZKTm9JVlpnaXAzOE1rVHlRVFlYMXRkL1F6ek10QVdLK1ozalZWREZzRFVyTURQaWtaYUJldk9CbEFrUHppMXJiN0Q0SCszZFZaYVZIaUF3L3VmaUkiLCJtYWMiOiI1OTBlZmUwNzc2MDEwOGRkZDA0NTI1OTJhYWNjYmEwMDhkYzg2YjdlNTY0MDFhY2FhNGYzMmNjOGQ2ZTU1ZWE4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:45 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-419022384\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-751329309 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-751329309\", {\"maxDepth\":0})</script>\n"}}