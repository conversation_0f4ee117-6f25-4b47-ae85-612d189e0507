{"__meta": {"id": "Xd17262983708f18fe36643bd82ffcaa6", "datetime": "2025-06-27 02:25:57", "utime": **********.23388, "method": "POST", "uri": "/payment-voucher", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991156.755354, "end": **********.233893, "duration": 0.47853899002075195, "duration_str": "479ms", "measures": [{"label": "Booting", "start": 1750991156.755354, "relative_start": 0, "end": **********.120384, "relative_end": **********.120384, "duration": 0.36503005027770996, "duration_str": "365ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.120393, "relative_start": 0.3650391101837158, "end": **********.233895, "relative_end": 2.1457672119140625e-06, "duration": 0.11350202560424805, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45921920, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST payment-voucher", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\PaymentVoucherController@store", "namespace": null, "prefix": "", "where": [], "as": "payment.voucher.store", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=68\" onclick=\"\">app/Http/Controllers/PaymentVoucherController.php:68-89</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.055850000000000004, "accumulated_duration_str": "55.85ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.151889, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 3.062}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.16363, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 3.062, "width_percent": 0.806}, {"sql": "select * from `shifts` where `closed_at` is null and `warehouse_id` = 8 and `shifts`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1669319, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:74", "source": "app/Http/Controllers/PaymentVoucherController.php:74", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=74", "ajax": false, "filename": "PaymentVoucherController.php", "line": "74"}, "connection": "kdmkjkqknb", "start_percent": 3.868, "width_percent": 0.698}, {"sql": "select * from `warehouses` where `warehouses`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": ["8"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 75}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1699681, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:75", "source": "app/Http/Controllers/PaymentVoucherController.php:75", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=75", "ajax": false, "filename": "PaymentVoucherController.php", "line": "75"}, "connection": "kdmkjkqknb", "start_percent": 4.566, "width_percent": 0.537}, {"sql": "select count(*) as aggregate from `voucher_payments`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 77}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.171509, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:77", "source": "app/Http/Controllers/PaymentVoucherController.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=77", "ajax": false, "filename": "PaymentVoucherController.php", "line": "77"}, "connection": "kdmkjkqknb", "start_percent": 5.103, "width_percent": 0.412}, {"sql": "insert into `voucher_payments` (`date`, `payment_amount`, `pay_to_user_id`, `purpose`, `payment_method`, `created_by`, `custome_id`, `warehouse_id`, `shift_id`, `updated_at`, `created_at`) values ('2025-06-27', '1000', '22', '0', 'cash', 22, 'PUR-المستودع الرئيسي-21', 8, 47, '2025-06-27 02:25:57', '2025-06-27 02:25:57')", "type": "query", "params": [], "bindings": ["2025-06-27", "1000", "22", "0", "cash", "22", "PUR-المستودع الرئيسي-21", "8", "47", "2025-06-27 02:25:57", "2025-06-27 02:25:57"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/PaymentVoucherController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\PaymentVoucherController.php", "line": 86}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.1734252, "duration": 0.052770000000000004, "duration_str": "52.77ms", "memory": 0, "memory_str": null, "filename": "PaymentVoucherController.php:86", "source": "app/Http/Controllers/PaymentVoucherController.php:86", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FPaymentVoucherController.php&line=86", "ajax": false, "filename": "PaymentVoucherController.php", "line": "86"}, "connection": "kdmkjkqknb", "start_percent": 5.515, "width_percent": 94.485}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Shift": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FShift.php&line=1", "ajax": false, "filename": "Shift.php", "line": "?"}}, "App\\Models\\warehouse": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2Fwarehouse.php&line=1", "ajax": false, "filename": "warehouse.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "success": "تم انشاء سند الصرف بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/payment-voucher", "status_code": "<pre class=sf-dump id=sf-dump-2062180767 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2062180767\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1157436129 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1157436129\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1119307172 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>date</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-06-27</span>\"\n  \"<span class=sf-dump-key>payment_amount</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1000</span>\"\n  \"<span class=sf-dump-key>pay_to_user_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">22</span>\"\n  \"<span class=sf-dump-key>purpose</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>payment_method</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cash</span>\"\n  \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>custome_id</span>\" => \"<span class=sf-dump-str title=\"23 characters\">PUR-&#1575;&#1604;&#1605;&#1587;&#1578;&#1608;&#1583;&#1593; &#1575;&#1604;&#1585;&#1574;&#1610;&#1587;&#1610;-21</span>\"\n  \"<span class=sf-dump-key>warehouse_id</span>\" => <span class=sf-dump-num>8</span>\n  \"<span class=sf-dump-key>shift_id</span>\" => <span class=sf-dump-num>47</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1119307172\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1220586606 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">131</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; XSRF-TOKEN=eyJpdiI6ImNYWGFVVHRrZ3dSdTZDTmxETDhscVE9PSIsInZhbHVlIjoiaVNzQU5RNGYvYXdlYmVLVE5XdTl2MzRCQnEvblZrZ2RLS3hzallzN2VFcW40ZzNSUjcwV3dxcjJsV0tqdCtVdUg0bWI5UFRzRzFuYlBzNUpQUTAwUzhMaU9BRy92eEdEcGE5ek41bHMxYmxzTmVLY1BudXVkLzhsUE9nYk9rdHFVWGs5WGRqWEcxdzR1aFVQVWQ3S0R5eGV4WkFNY1gzVk5GM2VKWCtMUjM3dk5ZY2hETTByRTdkVXVUYm8vdUlLelNvdlU3cWxLWTMzWHd0UXJlTFh0WmxneEg2MUFadGo3V2hyTURoNzhqYkZGaFA3SGR2QUNTSTZWc0JnaHNEWHcrMEZNTzUraXZWYi9KLzBSU1hQTy90NG1FcEZwakl4L3pwV1VoeVhTY05LYXUrNWRKeTdqZEV0VmI0TWFyRUdrdVlLVDdQZS96U28vN0txYkd6NEhINVpPQytzbTRQR1V4NDVrOUViM2YyZ2k4TDBQS2ZncUNNaGFubTBWNVJRNUNqb2FhZnFrMlZVWVRFQnp0cWRpelF0UU82dEhnb3BpcndLVm1WYlRCMnBnRyt6dDlPd1VLdGRRMHErTUc1dVhkSTBWYVIvNitidC94YmdmVy9PSkxhUHJvYTFlbSt6TFZpU2ZYZzBtYy9tQUhYdzJVSElWZW13VlZUbnFNQU8iLCJtYWMiOiIxZjMwYzE1MTQyZDRiNTE3ZWEwN2RjZjFjMGE1ZmVhZGI0YmFhMDA4N2NkMzgzNzMzMzhiZGU3ZTJlZmY5Y2JhIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkdHWVlrMElseFlUMVJkRlg1aEVaQ1E9PSIsInZhbHVlIjoiYmFwN3lmT0g4VGRPUVQ3amIxSVZpV1pPV1hPRWdkSW9IRlIyNW1LRmNaaDlLNkkyalZ3RUR2QThNbExLN1RSQlZiQ3IzV3I1MDB4cFdXWisweWRtTXpJdk5SeXl3ODFSOFB4bS9EQTVwQ00xOXNyOVZTR3lSY05QU3lzUjZJMjZMY0N1aEZud2syMC9VUDhaSXM2V2krekRiYnZMWUN2eEtuSHdtSHEvT25RL0dKU0pReUlLbmh1aU9rK3FLUEhIOTJSZDBvSkhnYjJIbUJKZFhCc2hJajQyVFQzcU4rb0pGajJXemhhVG9hYXIyeGhJUWN2eG84TDQwY2h3Vm96RjVFWFU3MXVtbHlrWnc5bkh4ZG0zNVFtRzh1cmVHM0NoUHFOSVUxeS96K2VWeGhqQzRIa0d3Z3FXRDl3cXpYRUtjcXYweVh4cnViQTRTMXhLRE9icW94MHpNWjd3cmNEZmJWbmZkWk5mQzlFM0xMSXZqM3dRbjB5L3RPS0NUbXVXYzBKRUEzUG9TcHRpaldvRnRsU1lPNUdGbzBNRkZzNGlpRTNWbE11eG1vQXBWMDBpclBXUEx5WXJqZkk3bXFEOWJ0NzVZZDkxbCtYZVZOU0o1YVpoSXlDei85bitDaXQ0MldUZDA3MWd6bTdtTS9EUjgvbTdpb1FxSmJ5ZFp2NTEiLCJtYWMiOiJmNTAyMTJjMWI2ZjI1OTQ3YzRjZDUxYzBmOTljOWU1MGJiMGJmZjFhODk4YjVlNTMyOTYzYjY2ZTNjZjM5YjNlIiwidGFnIjoiIn0%3D; _clsk=1wa64lz%7C1750991144534%7C28%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1220586606\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-548768264 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548768264\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1347220955 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjM5QnM4akZDZUVSQWdPanZpNTFicnc9PSIsInZhbHVlIjoiT1RydVlPaE5QdExQWEdXcjN1dDcwOERtOGZFczN1dHdiZmhMOUxkQ0xGeDRvbzFweC9iQUtENGhjNE1WR2pBYTZYQm1VU25XV0t6a2s2SDJMZzJSMHpYOWVvd2R2MkNHSDIxMlZiR1lzdXgzMGVuMzJxNlBpZGZPZ1hpd1NuWHBZM0o5TEZTejhzZkVjamtlVEJUUmxmVFoxV0d2NGdnTGJUd2VjWTRWcXdSQ0xMMXRlTlgwQWQxR2VZRjVLaTNLdEU1UVJIa0lvRHVkWGJmRnRtdFBaamF6aDg3RWVnc1N4OUNJdWpjMTJxNW1jdmFMYnY3Z0FVZGxZVWtCUEYrRU1oRkkrWkMxc3pING5tc2dDRVpUZ3NMQ1pVRFdVQ1Yzc0RLTXJ0alVWdmplYUd5bXN6L2JRL0g2ZmM4d2lWY01YbUFlQUpibmgwRzZyUXBoOVJWOXB3WW4xRktzd3U4Q3czYVdnSVpsTXdkWHlIQldKRnFrTWlHYS9CMmpxM3J1bVBQNy9xNHhnajFXTlJ2WEdqRnM3bHlhR2lMUDQ3QXpGb1ZiRXNYclNZZkhSdE9Md2EzemxyUGg3cTZyTWxjY1FrbmNON1kwMFVPYzZSNUQzdENHbnFibjZscEZDeWVSV2dLQnN4LzhWT2tlNnk0U2JPVEF2akdHS2d2ZU1iN2IiLCJtYWMiOiI1M2Q4NmU5MjZhNzQxMzhlZGVhODBlN2ZkNWVhZDVhZjQyOTk0YWRkZGE4YTkxZjQ2MTcxOWQ0MGJlYWJmNWI4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:57 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im42UG1KdlN3Y1lqNGZaSVNXTi9ldEE9PSIsInZhbHVlIjoiNVNid1NkSGNqYzJWaUJuWURpQWJyK0FKTVYwbW5ncFJ1OUNhVVdkWkxidGJ0SzRkVi9TSkRHNFgvSDlHWU5Bd1cvY0U0LzlwRSszYkY1UGlMeW5FcTZzb2dvcHVkWk1PK2NvYjVRMUdYbm9XNmNiY1NFZnhCZVBFaXlQYStHeWlscHd0bnpzVWhSNStlZU5UK0RmajQrNGtMUFR4M1pKYXJaWmRMc1lueEY0K1ZFNVk3S3VqaC8wZ2IwOEk1VlN0dUNldlZ3aVBkNTBwVkZFbzV6d0QzeC9pWVhpT1o1bmNZYzc4cjR1VXpjSjVlTXR2M2QzL1RkekVQeUZLa29yTG5oZFIreXcyYk9TcjlETlVoUzloeTRENHkrTFRlSjI3Q3pZc0t6T1VnME5lSmltT2VXYm1XcFJXZWEvbGtGMndlZW55bmFEbkNubDEycExZVzJaWHVYL0UvUFRiL3dFUStPUWxxMUp5Tjh6d2ZIcTlrdkFrUDNNalUwUGFFSm1mWG1qSHZqV0RBaVdwZGl5MGI3d3FFZ2ZwU3RZMlM4U2FFL29HdDlPZzJzUE9sVkJ0a3E2T29lTEZZUGFuMWJmVVRpRVNHc2d4OWJ5ekVHSStWMkRMaTNkMW4rZ0p1OUFreHJuaFpqUGx3SFhKUnRVOTFvR3ZoT2lsbEpERXlMdlEiLCJtYWMiOiI1YTQ4ZWZhMTg3ZmYyN2YwN2I0MjQzMTYxZjYwNGNmYzlmMmE3ZGMzMzM2OTg3MDQyYmI3NjdhNzc1YzQyYjlkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:57 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjM5QnM4akZDZUVSQWdPanZpNTFicnc9PSIsInZhbHVlIjoiT1RydVlPaE5QdExQWEdXcjN1dDcwOERtOGZFczN1dHdiZmhMOUxkQ0xGeDRvbzFweC9iQUtENGhjNE1WR2pBYTZYQm1VU25XV0t6a2s2SDJMZzJSMHpYOWVvd2R2MkNHSDIxMlZiR1lzdXgzMGVuMzJxNlBpZGZPZ1hpd1NuWHBZM0o5TEZTejhzZkVjamtlVEJUUmxmVFoxV0d2NGdnTGJUd2VjWTRWcXdSQ0xMMXRlTlgwQWQxR2VZRjVLaTNLdEU1UVJIa0lvRHVkWGJmRnRtdFBaamF6aDg3RWVnc1N4OUNJdWpjMTJxNW1jdmFMYnY3Z0FVZGxZVWtCUEYrRU1oRkkrWkMxc3pING5tc2dDRVpUZ3NMQ1pVRFdVQ1Yzc0RLTXJ0alVWdmplYUd5bXN6L2JRL0g2ZmM4d2lWY01YbUFlQUpibmgwRzZyUXBoOVJWOXB3WW4xRktzd3U4Q3czYVdnSVpsTXdkWHlIQldKRnFrTWlHYS9CMmpxM3J1bVBQNy9xNHhnajFXTlJ2WEdqRnM3bHlhR2lMUDQ3QXpGb1ZiRXNYclNZZkhSdE9Md2EzemxyUGg3cTZyTWxjY1FrbmNON1kwMFVPYzZSNUQzdENHbnFibjZscEZDeWVSV2dLQnN4LzhWT2tlNnk0U2JPVEF2akdHS2d2ZU1iN2IiLCJtYWMiOiI1M2Q4NmU5MjZhNzQxMzhlZGVhODBlN2ZkNWVhZDVhZjQyOTk0YWRkZGE4YTkxZjQ2MTcxOWQ0MGJlYWJmNWI4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im42UG1KdlN3Y1lqNGZaSVNXTi9ldEE9PSIsInZhbHVlIjoiNVNid1NkSGNqYzJWaUJuWURpQWJyK0FKTVYwbW5ncFJ1OUNhVVdkWkxidGJ0SzRkVi9TSkRHNFgvSDlHWU5Bd1cvY0U0LzlwRSszYkY1UGlMeW5FcTZzb2dvcHVkWk1PK2NvYjVRMUdYbm9XNmNiY1NFZnhCZVBFaXlQYStHeWlscHd0bnpzVWhSNStlZU5UK0RmajQrNGtMUFR4M1pKYXJaWmRMc1lueEY0K1ZFNVk3S3VqaC8wZ2IwOEk1VlN0dUNldlZ3aVBkNTBwVkZFbzV6d0QzeC9pWVhpT1o1bmNZYzc4cjR1VXpjSjVlTXR2M2QzL1RkekVQeUZLa29yTG5oZFIreXcyYk9TcjlETlVoUzloeTRENHkrTFRlSjI3Q3pZc0t6T1VnME5lSmltT2VXYm1XcFJXZWEvbGtGMndlZW55bmFEbkNubDEycExZVzJaWHVYL0UvUFRiL3dFUStPUWxxMUp5Tjh6d2ZIcTlrdkFrUDNNalUwUGFFSm1mWG1qSHZqV0RBaVdwZGl5MGI3d3FFZ2ZwU3RZMlM4U2FFL29HdDlPZzJzUE9sVkJ0a3E2T29lTEZZUGFuMWJmVVRpRVNHc2d4OWJ5ekVHSStWMkRMaTNkMW4rZ0p1OUFreHJuaFpqUGx3SFhKUnRVOTFvR3ZoT2lsbEpERXlMdlEiLCJtYWMiOiI1YTQ4ZWZhMTg3ZmYyN2YwN2I0MjQzMTYxZjYwNGNmYzlmMmE3ZGMzMzM2OTg3MDQyYmI3NjdhNzc1YzQyYjlkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1347220955\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1136542374 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"24 characters\">&#1578;&#1605; &#1575;&#1606;&#1588;&#1575;&#1569; &#1587;&#1606;&#1583; &#1575;&#1604;&#1589;&#1585;&#1601; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1136542374\", {\"maxDepth\":0})</script>\n"}}