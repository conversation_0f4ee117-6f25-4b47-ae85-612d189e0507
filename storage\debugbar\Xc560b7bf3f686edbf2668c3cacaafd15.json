{"__meta": {"id": "Xc560b7bf3f686edbf2668c3cacaafd15", "datetime": "2025-06-27 02:34:26", "utime": **********.932488, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.515666, "end": **********.932505, "duration": 0.4168388843536377, "duration_str": "417ms", "measures": [{"label": "Booting", "start": **********.515666, "relative_start": 0, "end": **********.878426, "relative_end": **********.878426, "duration": 0.362760066986084, "duration_str": "363ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.878435, "relative_start": 0.36276888847351074, "end": **********.932507, "relative_end": 2.1457672119140625e-06, "duration": 0.05407214164733887, "duration_str": "54.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45736672, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0025900000000000003, "accumulated_duration_str": "2.59ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.903673, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 68.726}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.913363, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 68.726, "width_percent": 15.83}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.918897, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 84.556, "width_percent": 15.444}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/22\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-844531600 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-844531600\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1772945536 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1772945536\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-253815816 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-253815816\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1212764049 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991660993%7C42%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpnYk9pcHB2TGszQzNudk9HTmRPWlE9PSIsInZhbHVlIjoiSGUxSml0MmN3TlZWYTg2cGhabnRIdkl3OVVrWlRhZVFhZkpmWEtuMUtUNUZ1cVZUR29zUnpMZkF5aGJjS1NSSGlYekt3QVRlMkJER0cvRVcrQ1h1U3g5T25qVTFlZHJySm8xZ2w3UTFUVWdUb3VTSGRRV2E5S0g2dExSd3Z6VVZuckZ0Y2JlTGE4ZmJab1FaamlrKzl5T1dyQ01sWEtLOENSVGs0QUJ0eFBLVGM4RmJ2M3VBWDB5RFNGZE96UHVjZE1QS3A1a0N4K2UrdjdDUDZDOHpmZlk0YUd1bWhPVXR3cFRhaGtrOXZERmgzWGR2aXJ0OVpCaVRtRVBOS0JLT2x5MFBaV2hJdVhkdWlOYVh1WEFhOXdqYzRSQnpOR1J2SXlOdnl0TUpEVWQyZDcxQU9pd2dtaVpRdHBRVzRhVFFOVXNqT3diNXRaeERDYkdrNlQ1NHF2ZFJaNTlMTU9YYkFMSzdaZXFNeE12K290Nkk3YnhTMFRvekZXUnJkZDhIZjZLN0FTY3I5dDZlZ2w1aUIrM3lxdmNQSE93VXFrY2NXY1NTR1hKRW8vZkdFNklwbjJ4RURCT2xkVGFjL0pYWHBoZE92YjRnbWsydGk4cTM0RG5BNVNhcmNLWldyVVZkNDZGRjlsNnJjdG1jdkYzRWplMis3MzZHL2owQWVSNUMiLCJtYWMiOiI5N2MyZjVmZjI2NWQxMWQwZGU2ZTdiNGQ4NDNiMGNhNmEwNzMwMmJmYmY3YzU0M2VhZTRjMGFmMzI3ODAxZDAxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZxeEFDSTdSeXRjcTB4d0djZEx0K1E9PSIsInZhbHVlIjoiT0pxVFNRWXV5MHRzUm1aMU9IbnU3aUl5R2xJRTFyRFB0VWhDNU9xVUZEMHVvcmVsQ2dLZ1FIMW4xV1h2eElVYXh2T0NISnoxMnE2MGhmQlVhTndKU2xva095TEhGRDZVZ3Fna0JaMy9laGtPL25BTnd2NUlhcDBySHFTTHdBbWNhYTBiU2Jvdlk1Y0N6aDRoS2s2bU9aTFBGU1VRSHJzc0pDQVFHUDQ3OCtJWkRGV0pMZy8yZGxkMDMyRkNYeHhCY1B6SmtBbTR3Rjh6Y01tRytUSUl4M2tyUlhqa3dPYUU2bnZQeUpKeEpoR2JQREx2YnVmMnNLaVBlcDFzNDJvWG0wS2pQZnhJRjc3WXFhdVd2eGhxMWhuYkhmWDd5ckNaa29YVlQ4VzBPSG9mTnQ4OFhSbGZpMEJwL0NPNzNXazIwS3VJakJnMjBxQm1nMHlaQzlIUHZXM1orWFUwL3l3T1hLMEo5NTR4cmRQK2ZDZHliYmFZTlRzUXZWdjRMemtUVThxeGVIRDRUdEZvTnQxd3FkQnMwZUI4NXRxUTQzdHFjWDNKQUs2eFo4WGljZXlnY0hpT2dOSlRPRkNjbXZYaVhIMFhtWlY1YnRwdEFYSVVJeW1xenlmL1RiSXQ4V3JnQS9rTGpzRDhCWHdkL2ExTlcvWUpuYVd3Ym0vVnZqMkEiLCJtYWMiOiI1M2M4MDIxMDdhZGFkMmM2ODNlMWEzODMxNzg1NjhlZWQ1NmE5MTg4OTFhMTQwMzkzNGUzY2I5YWVhN2NhMDg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1212764049\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1637330285 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1637330285\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6ImhJcXRMZ3ZVZ2wvVk5zZ29oNkFNSGc9PSIsInZhbHVlIjoidElFRlIzTDkzQVZwQkJ0dmoxb3NXanFqemcvQS92UURHSFZCNUNpbDJRYU1CQVZJZzgwU0wxL1BrMFdIeGUyRXY4b1JpdnBRN3YvUy8ya3hTYlhrOG5BeTBadFI4ZUI3RlljcWtLWndyMC8rcDVxNktLWDB3Q1psUUdrZW1oNG4zWUF1ZXdyOGovVHBiUG05TnpnU1h3K0JLQzgybGtwQStkZEZYcHdlZk9MMEx4elcxN1h4YmFsQ1FDM05PYTB4TnMvemtkbk5Mb29sVDQ1MDJEaVhTSFBjQy96SFkrL3NjN3BiQ21URmRIZUNIaU5YU0hRR3V4VE1yZ0ljQ0RNOWRkV3QyWlorenB3TXUzYmVQbWdDL1JIWHNxVlQyWDA1SzhmYk56RVp1RU43NXZTTzhqRXQvWnY3Q3hvK1Q2OGV6NnpIbjNtS1Y0dnRNTExKb3VLQ0pZL3NoYVgyRnJFN3U3R1lLUlJ1L3R1WVh4dlpCQkZnMVVvTzdzUEpVMGdRY1VwUTNYU2pVQjlxeVVXdURMemh4RHpZV094SFllczMzWkZ2M21XSXY3TUJ2ZkRHb1VxdmZjK1VLdUQvVnRURW93SEtQYTByZmsrcDBhSGdYa3B4c1o2Qmo3bjFxZ2tXQmM0ejBGWE9NWHplQ1J6VHZ0ekVzRU9qS3c4Um84UkgiLCJtYWMiOiI0ZGFkOGE1YmQxNTdjZmMwN2M5MTcxMThlMGUzMGNkNzdiMGIzZTk5OTNkZjU3ZjY0Mzc3MmViMjczMmYxNTIxIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Im8wZUJ4Wk5PaGU1R3hyc2xpUE9WbUE9PSIsInZhbHVlIjoiNFpXTlBobHM4cU5TMkUyNXhCY29mSFc3NkRJNDYvSHV1Q09LZDhCOUZJWE9URFJoRVhnelNLb3YxcFRUMjhMN0d3Sjg0TXdUK21oT0VBVXpXM1A3MjJORjVGNGM1TFc0SzZpZWZwaGRIQkNPd1NDUEJqeC95aVhuSzRrMWNhcmU4d3lNNXdXMldKdlo0WHlQWU14L1RlY0d5dkY1eGtCdDNEY2RadlltZjg5SnBxRmVhcnhrNXExQm1ZUTA2WEh2bHhNS0xuSmowNCtNcmN3c1FYYnZiWDIveWV2YU9PQ3FRL1VHUEZtellCUzdtQ25yc0VrQzFvOFcyNVBtV1FJT0txLytDbkdDaGtRZHNOc2p0UUoyRWwzemU0czVxTlcyQjRzSXNzZlY3cTBJU2NTR0FoUE91cGF0dmtXaDRjdTRWR2JyWGppbGk5MW00M1BhQk5BUGZLbVBpUlcvZTB1bW5IRlhyRU40ckR2VldLSlh4OWovaUxKWWZ4TlA4Q3dZWW4zRmV5U2NxTlFUeGh3WHZORGtlUzhDbno1MzhHZW0wYUxudmxqUFlkNVo4enF0TmRiamR2dXRGS29XUUZ0YnN6L0NpSXMxNWF1blM5TEhhTEFoT1c3TWR4ekRVUkNSOVBsOE9sbWw5VjZPWCthUHdYNXhRd0FLdmVNSU1OR2MiLCJtYWMiOiIzZjA3ODEwNzk4Mzc1MTczOTViMDFkYzNmMTAxYjlhMjBkNzJjZGE2ZTk2OWM5OGI4NTljM2U0ZjNiOTZmNzY2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6ImhJcXRMZ3ZVZ2wvVk5zZ29oNkFNSGc9PSIsInZhbHVlIjoidElFRlIzTDkzQVZwQkJ0dmoxb3NXanFqemcvQS92UURHSFZCNUNpbDJRYU1CQVZJZzgwU0wxL1BrMFdIeGUyRXY4b1JpdnBRN3YvUy8ya3hTYlhrOG5BeTBadFI4ZUI3RlljcWtLWndyMC8rcDVxNktLWDB3Q1psUUdrZW1oNG4zWUF1ZXdyOGovVHBiUG05TnpnU1h3K0JLQzgybGtwQStkZEZYcHdlZk9MMEx4elcxN1h4YmFsQ1FDM05PYTB4TnMvemtkbk5Mb29sVDQ1MDJEaVhTSFBjQy96SFkrL3NjN3BiQ21URmRIZUNIaU5YU0hRR3V4VE1yZ0ljQ0RNOWRkV3QyWlorenB3TXUzYmVQbWdDL1JIWHNxVlQyWDA1SzhmYk56RVp1RU43NXZTTzhqRXQvWnY3Q3hvK1Q2OGV6NnpIbjNtS1Y0dnRNTExKb3VLQ0pZL3NoYVgyRnJFN3U3R1lLUlJ1L3R1WVh4dlpCQkZnMVVvTzdzUEpVMGdRY1VwUTNYU2pVQjlxeVVXdURMemh4RHpZV094SFllczMzWkZ2M21XSXY3TUJ2ZkRHb1VxdmZjK1VLdUQvVnRURW93SEtQYTByZmsrcDBhSGdYa3B4c1o2Qmo3bjFxZ2tXQmM0ejBGWE9NWHplQ1J6VHZ0ekVzRU9qS3c4Um84UkgiLCJtYWMiOiI0ZGFkOGE1YmQxNTdjZmMwN2M5MTcxMThlMGUzMGNkNzdiMGIzZTk5OTNkZjU3ZjY0Mzc3MmViMjczMmYxNTIxIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Im8wZUJ4Wk5PaGU1R3hyc2xpUE9WbUE9PSIsInZhbHVlIjoiNFpXTlBobHM4cU5TMkUyNXhCY29mSFc3NkRJNDYvSHV1Q09LZDhCOUZJWE9URFJoRVhnelNLb3YxcFRUMjhMN0d3Sjg0TXdUK21oT0VBVXpXM1A3MjJORjVGNGM1TFc0SzZpZWZwaGRIQkNPd1NDUEJqeC95aVhuSzRrMWNhcmU4d3lNNXdXMldKdlo0WHlQWU14L1RlY0d5dkY1eGtCdDNEY2RadlltZjg5SnBxRmVhcnhrNXExQm1ZUTA2WEh2bHhNS0xuSmowNCtNcmN3c1FYYnZiWDIveWV2YU9PQ3FRL1VHUEZtellCUzdtQ25yc0VrQzFvOFcyNVBtV1FJT0txLytDbkdDaGtRZHNOc2p0UUoyRWwzemU0czVxTlcyQjRzSXNzZlY3cTBJU2NTR0FoUE91cGF0dmtXaDRjdTRWR2JyWGppbGk5MW00M1BhQk5BUGZLbVBpUlcvZTB1bW5IRlhyRU40ckR2VldLSlh4OWovaUxKWWZ4TlA4Q3dZWW4zRmV5U2NxTlFUeGh3WHZORGtlUzhDbno1MzhHZW0wYUxudmxqUFlkNVo4enF0TmRiamR2dXRGS29XUUZ0YnN6L0NpSXMxNWF1blM5TEhhTEFoT1c3TWR4ekRVUkNSOVBsOE9sbWw5VjZPWCthUHdYNXhRd0FLdmVNSU1OR2MiLCJtYWMiOiIzZjA3ODEwNzk4Mzc1MTczOTViMDFkYzNmMTAxYjlhMjBkNzJjZGE2ZTk2OWM5OGI4NTljM2U0ZjNiOTZmNzY2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}