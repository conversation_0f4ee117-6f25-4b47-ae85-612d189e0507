{"__meta": {"id": "X76e880202a5052214415a703f3c1c931", "datetime": "2025-06-27 02:23:48", "utime": **********.500244, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.093242, "end": **********.500259, "duration": 0.4070169925689697, "duration_str": "407ms", "measures": [{"label": "Booting", "start": **********.093242, "relative_start": 0, "end": **********.447902, "relative_end": **********.447902, "duration": 0.3546600341796875, "duration_str": "355ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.447911, "relative_start": 0.35466909408569336, "end": **********.500261, "relative_end": 2.1457672119140625e-06, "duration": 0.05235004425048828, "duration_str": "52.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45722464, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.0027099999999999997, "accumulated_duration_str": "2.71ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.474876, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 71.587}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.485316, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 71.587, "width_percent": 16.974}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.490827, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 88.561, "width_percent": 11.439}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-542544808 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-542544808\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-94274277 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-94274277\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-794064864 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-794064864\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-366847336 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991024947%7C20%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6InE2Mm9HRUVROGdSbGpLalBnYTE1SVE9PSIsInZhbHVlIjoiWEdKaFFjRjhsRWdtVm5UYWx0ZWtFdm1DR3VzMXpMbzNhSWI3aWsrQ1FPUktIbi92YmxpcnhpY0wxWko4dGR4dkROQmt0aUtVemtuL1pIRGIxcGs4cWNISTEwaUVDQ1laNHYxTU5qbU9ySEYyUHY4c0EyWFV6NS9wWE01TjRqM3BNT3o4OU9Td05OOE5Eei93WGpJREdzTFExWkRaNkltVUNDU2FDRVFvSDh5KzhVbHRDTzZyOFlyOGtLdGpPY1kxa1dQRnBSRHhocytXQllXR09Gd3pvZ1ZDa2UxS2syenJNTnhidGZlUGF3Z3dSeUZ0K1dZK21wTWp1UFdhNkdnQXBIbUZmQlNxTkVTdEVIN0hhUmkwNk5iS3MvV3hqQWNyZHpMREJvQVVMcUFtV0t3YkRzTjQrazFoOGk3TEpmSTZHMHZEekR1bTRSb2Q0a2xPSDJxRjJtSHF4QWNCUEZZZlJhUE55c2h2Y3BPb2lZemNCaVllSThQd25jQ3d1UlV1VlZzU0hHNXVHUWkwRzh2dmYxYXBIcjMvWVVZWDRhRUhYN0Y0N28xRFczb1ljYTN1bkw3UWpiN01yazAxNDhtUVJNY3d4QWNCYzgwQVJaN2ZlblI3SlhaajR1ZGNIcEMxWnlweGJabWZXRGpnT2FLTmF2RE13U3NSLy9CaG1tVTAiLCJtYWMiOiJiNjNkYjEyY2Q2YTkwYWI1MDFlMTFmYzM5NGVhMmI4ZWRjMWYyYzBkZjhjMTgzMTBkYTU4ZGMzYzY5YTBkMjY1IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlVUQ0oxcVhkRktsQlhLd0R3L0MzeGc9PSIsInZhbHVlIjoidURLb25yZGpJYURXQWNlR214VW40WnBBUmU2eWFnWTQwajRqQWxXbXdVZUc0cXpyVEphZXpXZmM3VVl2RUNPOXZacVE3VTJYY1d5YVZhaFZITzlubXc3TS9hMTh6dXBTdFVRKys4b0RqTnhibkY3STY0SS93aFVPM3FJaldyc3FVekFYOE1HZEMzdlNEREp5c3RhR01ISzN5M3RIUTFkaDcxZE5RUXdrbDBKRzJwNWVRUnRpSU5sVnJIUEFmMlR3amliRXJPb0JtbGxTY3JjTHIyZzQ5M0Nra3RUV2FQd1NiOTkzWU96eGlxUzZIaGxLeU5BN2c2ZFExdmE4RTU1SktCQWNJTm5jZlIvUjNvODlUNUFUMWZmd0hseHhnNXpWN0hZeFU2MVY0OE1wVHd2VTl6YmM5cDhSRE9Uc0hRSERHZWNaSUE2ZUVmK205MDM3UXVvaHYvcXpabU5lcEdGbXNiUVBtNzdVOEdtOEROZHh4Q1VjYjE1M2dncVdiVTJJUUg2aldMVUp0T2hnZXA4M0M5Qk82cFJ6NEpOaFR4ZEUvV2Y5L2pEKzU2aEJTTVgzYzQwRnJDeU51Nmx5QkEzVEFObmE0NzBhVXRFZUtxcWo1QTVYM25hMTlwWUdqOGs3K0Z6Y1M4dzRmUmVDRkhtMEVvdDlZMGNMakpBWjhGbVEiLCJtYWMiOiJlNTAzMjJlOWU5MTgzZmZjN2UzNjRkZWJhNTI3NTY1OTUyYjY3YmJjNmVjODA2Zjg3NzI5MjYyY2EzNmJhZGE2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-366847336\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1740070301 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1740070301\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1413507708 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:23:48 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ii9IWFlZVWx1R2ozN1ZyazhkQ2IwdHc9PSIsInZhbHVlIjoiaFNoSTVWSEtwdlYzSlBhYWdjaXBDSkwwU2UyOXp4MHU4OXd0bzJxeVZYU3VheGxwck9lTFJkUFFXbVpLWUplc0NYRGxpd1J6LzRFK1phT0hQd0ZpWW4yZkZqM0lYK2JQTWVyeDAwVDlHV2VhY0tVS2JYYnJsTWZ6eHFHeld5NWxLdzVpL1RlVUVmK1JCWlFiL2R6MnptYW43d1lvbFVSQ08vZFYybSt6U1FOYlNNNHNFcUtydXhqYjFYbEQwcW1qazViUk5SWkxQa25JS0xMdDR4NVpVam9aNUUxajVuT2tYVXJ1ZkowdFdyY05OYmRVdlovTDZveWhzSkpRaFl3RXpxRWJpZjhHQmxaZXpQRHZ5a2hqdkQwZGtEeFlMaGp5aFhuaDloeFRlbzNDQ1grd3JyUDY2WWM5Vk0yLy91RDY4QzVvN0xYOElvTTh6TmFYb2p2a2hmT2Y0UDkwblpvMDlJNGhMQUFtUTNOZ0dheTRLcjNockRZQ0RYeExoSEt2OTUzTkVZR3Y3TTV0QmtYd1pYMnhsaisraVhJM1hTQ1VCUWJ0YmdGQVcvcy9QRGVRZys0ZnRNRWJTd1hDZXpaREUrR0NEU3ZIMjhBZi9oanJDUUtxeTBUNDgzV3dITXpDd0RzVWhDZFF4WnhBWnl1R09SeUFaaDVRYmt4V2pNWVciLCJtYWMiOiI2ZjVlNWQzOGFiOGVlMzBkMTdiOWI3MTM2OTQwN2FlNDg3NDJmNWFlNmMyZWQ1N2MzOWE3Njc4MWU5ZGI1ZmQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:48 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkpPb1F5TWtsdThqdW9VT0xMRXV2aWc9PSIsInZhbHVlIjoiVEJvbkNPWGZGSEsxdEZVOEhFM1FrWFk0OGl6Q2FKOTE2VXl1d3VTeHQ1N1BXZjkyQ21aSGJiY1JNUk5qSXlIQjVqUTh0Y2V1a3ZKQmJmZXFGLzZjdXEvVTFkc0o1MlVXOCsyOGtreXlISWtycFR4eWV4eE5vRzdxOGlJbDI5QVJXWlBpdEFHRW00d2tCdmMwdUwvdjFRRm1KQWlMVFBEOXQwd1plbXNTcWp3MWo4RnJ5WjJaYmFTVnJxNXJEWG9mZzlra003UkRuWjRRNU5oRTZCMVI2dTNYNlQ4TGNaR3o3RkFGU3RXa3NDV25xSUc5U0x1bTFmcEJtemg1Z2pTQTVJdElRK0N1SXcrVWdIWG9kQWxoSnQ3Q25oT3NRUXlweUhhNE5pbGNEQlo4OGhNbndJSmg5VHhTY0RIa3NFZkxlYms2dS9DTFd6cTE1cmViSDd1VXgzU2sxQXJESXQrMVEwZjJMMUZJNkVoYlFOZm5VQ2NWbmtPc08yN3haS1Z3dGlkL1JTLzVjSEdNcjNMejloNDh5R2dsdUtiQVhPdWVjekFWS1E0SzJocE9JeEZmd04rRVFQM3pnTjA4MGRtb1dpUzFnWWlJODNvc2lWOXBvdlIraDFqeDNYTjVFVGNWT1Jpa1A4akdVU2w5LzhzU3FCeVNaWncyQkJMTkIyZGsiLCJtYWMiOiI5YWI3ZTJkMzE5MGNmNzE2NjJlMmQ4MWEzYTNjN2ZhZWZiMjM5ZGY3NTkzNTk2NjJmODE3M2YyMGYzYTFmNWY0IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:23:48 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ii9IWFlZVWx1R2ozN1ZyazhkQ2IwdHc9PSIsInZhbHVlIjoiaFNoSTVWSEtwdlYzSlBhYWdjaXBDSkwwU2UyOXp4MHU4OXd0bzJxeVZYU3VheGxwck9lTFJkUFFXbVpLWUplc0NYRGxpd1J6LzRFK1phT0hQd0ZpWW4yZkZqM0lYK2JQTWVyeDAwVDlHV2VhY0tVS2JYYnJsTWZ6eHFHeld5NWxLdzVpL1RlVUVmK1JCWlFiL2R6MnptYW43d1lvbFVSQ08vZFYybSt6U1FOYlNNNHNFcUtydXhqYjFYbEQwcW1qazViUk5SWkxQa25JS0xMdDR4NVpVam9aNUUxajVuT2tYVXJ1ZkowdFdyY05OYmRVdlovTDZveWhzSkpRaFl3RXpxRWJpZjhHQmxaZXpQRHZ5a2hqdkQwZGtEeFlMaGp5aFhuaDloeFRlbzNDQ1grd3JyUDY2WWM5Vk0yLy91RDY4QzVvN0xYOElvTTh6TmFYb2p2a2hmT2Y0UDkwblpvMDlJNGhMQUFtUTNOZ0dheTRLcjNockRZQ0RYeExoSEt2OTUzTkVZR3Y3TTV0QmtYd1pYMnhsaisraVhJM1hTQ1VCUWJ0YmdGQVcvcy9QRGVRZys0ZnRNRWJTd1hDZXpaREUrR0NEU3ZIMjhBZi9oanJDUUtxeTBUNDgzV3dITXpDd0RzVWhDZFF4WnhBWnl1R09SeUFaaDVRYmt4V2pNWVciLCJtYWMiOiI2ZjVlNWQzOGFiOGVlMzBkMTdiOWI3MTM2OTQwN2FlNDg3NDJmNWFlNmMyZWQ1N2MzOWE3Njc4MWU5ZGI1ZmQ4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:48 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkpPb1F5TWtsdThqdW9VT0xMRXV2aWc9PSIsInZhbHVlIjoiVEJvbkNPWGZGSEsxdEZVOEhFM1FrWFk0OGl6Q2FKOTE2VXl1d3VTeHQ1N1BXZjkyQ21aSGJiY1JNUk5qSXlIQjVqUTh0Y2V1a3ZKQmJmZXFGLzZjdXEvVTFkc0o1MlVXOCsyOGtreXlISWtycFR4eWV4eE5vRzdxOGlJbDI5QVJXWlBpdEFHRW00d2tCdmMwdUwvdjFRRm1KQWlMVFBEOXQwd1plbXNTcWp3MWo4RnJ5WjJaYmFTVnJxNXJEWG9mZzlra003UkRuWjRRNU5oRTZCMVI2dTNYNlQ4TGNaR3o3RkFGU3RXa3NDV25xSUc5U0x1bTFmcEJtemg1Z2pTQTVJdElRK0N1SXcrVWdIWG9kQWxoSnQ3Q25oT3NRUXlweUhhNE5pbGNEQlo4OGhNbndJSmg5VHhTY0RIa3NFZkxlYms2dS9DTFd6cTE1cmViSDd1VXgzU2sxQXJESXQrMVEwZjJMMUZJNkVoYlFOZm5VQ2NWbmtPc08yN3haS1Z3dGlkL1JTLzVjSEdNcjNMejloNDh5R2dsdUtiQVhPdWVjekFWS1E0SzJocE9JeEZmd04rRVFQM3pnTjA4MGRtb1dpUzFnWWlJODNvc2lWOXBvdlIraDFqeDNYTjVFVGNWT1Jpa1A4akdVU2w5LzhzU3FCeVNaWncyQkJMTkIyZGsiLCJtYWMiOiI5YWI3ZTJkMzE5MGNmNzE2NjJlMmQ4MWEzYTNjN2ZhZWZiMjM5ZGY3NTkzNTk2NjJmODE3M2YyMGYzYTFmNWY0IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:23:48 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1413507708\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1867453566 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1867453566\", {\"maxDepth\":0})</script>\n"}}