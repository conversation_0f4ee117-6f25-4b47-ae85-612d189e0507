{"__meta": {"id": "X853be8369cec9b88b357c9fe5f9241bf", "datetime": "2025-06-27 02:15:44", "utime": **********.05208, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990543.638818, "end": **********.052096, "duration": 0.41327786445617676, "duration_str": "413ms", "measures": [{"label": "Booting", "start": 1750990543.638818, "relative_start": 0, "end": 1750990543.98098, "relative_end": 1750990543.98098, "duration": 0.3421618938446045, "duration_str": "342ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1750990543.980989, "relative_start": 0.34217095375061035, "end": **********.052098, "relative_end": 2.1457672119140625e-06, "duration": 0.07110905647277832, "duration_str": "71.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45734672, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02248, "accumulated_duration_str": "22.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.0067558, "duration": 0.02131, "duration_str": "21.31ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.795}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.0363262, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.795, "width_percent": 1.957}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.042149, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 96.753, "width_percent": 3.247}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/20\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1563064819 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1563064819\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-78692233 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-78692233\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-897553797 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-897553797\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-668784371 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1843 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; _clsk=1wa64lz%7C1750990541673%7C13%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ilhtb2ZsNkNQKzQvekJHZVRXQjVwbGc9PSIsInZhbHVlIjoiY210bDR3WTl4QjlaMzhsdmkwMGdha2N4ZkpBLzUvUXJjbVFzVWFlcU83alltdUVUekxIdEdSc0xVT1VrcDluQnA1UmtGYXg1WlRIVXRIYlRrSHFPQzJBc3J1MGpDcC9jZ1diR2RGS3orRWxiSE0vNGxMYkpGN2tUL3l1UE1iZ2lqVENIQ0ZncGVXY3YzTEFQN2VDZTJjbDd1UFB6YmxvdUV2NWJMK0FUL1Q2RThCRXUrcnFpNXFNbkJmTUpTbFo1WXZuWUFhZWdkMlNaZ0RDSm1VcXJMZlVxYkxVakVUSGRmMGRLRUdLWENRaWt5WG9lNGNuK25razhPVWVWU08yd2FEeUp4V2ZESkJvL1BVQVRESlk1bTM0c3Vsc1MzajVKRC9peGF4UVNibktHYnVFQ1hXNC9Nbmt6ck5HQWhMQUxzNWdvUDhFVG9yWlQ1MFlndmE0d1BSczB3TC9mVXYvUkc2K1FJVm12Qy9uZm9RWUxaWnVhZkF2MFk3OU41TFo4L2tHVTR3Vmt6RC9uMytiZzhqY3ZxQUhoOTBLYzN5RnZNWnNhNDJRaUNSUXpwaHlZY0ZGSVcxRU05Z3dXQzl5WStBUmNITkhvcFJJenpjUDN3c2d0aFpnSDFxU3JyVFRibDgvM01ialo5eVp2SkQ4WXJUNkpkVnc1ODNXK0tuaHIiLCJtYWMiOiJiYmEwNzIwZTY3YjE2MTlkN2EzZjg4M2U2MjM1ZWY1NzQxMGVjZDEyMmU1MTU4ZTY3NjQ1YWJhODBiZGNiZTg5IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InJaYmFqcitRZ0Nsd1Z3a2JoTTZCenc9PSIsInZhbHVlIjoiSC9lck9FVytZL0NzdTFRSzY2NmRYanUyZTV3QWZDM1d1TTVrRkl3VFdZVTgwQnAyMVhVZDZoZjFEODdqc0hVYk4wZ3lzcGFaYXo5eU9BSk1NRWxHQm1PNTMrMzl2Wkd0K090YWlNTjdHRFloWUxJVlpnbDg4Y2orMENpSllEQjVKWkc5N2VXblVmTXVaMkdTM1pRWjNkNlQ4Uzc3Y0xrMFNqd2JMNXBxTDlSekpsTDBBaHhZVjNpbzFnblBKSndBc01Ncm5HSjQ2Zmg5VDcra1p0N3lVdGpLWWh0STRNMVF2MzdxbkxvYk9SWDlJOS9XV2d0QU94eHJUaEN3ZjZyZjUzNzFUdDVvdnllZU8xYlFRa2ZjSE4rMVZvOEprSHNUVFU2dTFpb3ZEbkhwS0FKQW51ZldYU3llZ1hPdjRBTlpsMXNjekJkd1pvYjU3NnUrOWQxL0NBekZ1RGUzcU5zdTVZQWpIR3ljUnMyNW9kYkhoQWdTMnhOc2p0ZWZxTU5aQ0JCUzBiVWtwalZkdVZSUExTL2V2QXNuQlNNRUJFYUZzcUxPRzlOVjhES2Vua2tHbnFtcHh4aGd1Rm5kbXozQWlwdmVoWHowT2tQZVhtVUluSlBKbURHRWV4L0FUc2NOczBhSCtJYS9yT2lNeHlncDBiR3VNdmVudE14UVU2eXAiLCJtYWMiOiI4ZmI4MzFjZDlmOGVkZjRhOTIzN2ZjZTBiZjFmYTAzMWYxODFjOTdmOWFmNzA0NjczMDJiOTFhZTVjZmUzZDQwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-668784371\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1999072631 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1999072631\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2026804608 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:15:44 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Inc0cXJXbkYyeDI2R0NnK1ZXR0cvK2c9PSIsInZhbHVlIjoiUEUwN0o5M3ZtbjVla0xzZmhITU5tQ0Z6TFZLSkxiV1QzOUVnM3BpNVdWV2lqLzYrc3lycE5CbWdpNnJoWXJua1hlcG5YanBBaXczTDd1VVVvR29QM29kWmxmTWdmL3krcnBwcEU0b2NrTjJTUDVOdEJwNUkzd1FHcEZ1WUtaRE5VdVpHY2hNTmhmd1JxZnZycVA3QzE4ejg3eFI0bGVPVHduNlF0b3JTKzNvNHVoeWNUMlRMY3lpRkdIRDdPVHhyY25lLzBpSWdiOTU2b0x3bE9zTG90SWFVQkVKWmhTOHhLZGs2dFFjczQ1Qk8zVUdYa1A5NHh5V09lb0MxOHhQbDl3VzBzanlWRk5zUXNvUXM3MENucllndlIyRThPN2hEeitGYWhvNU5TNmh6QjRBbDlpOVVGbW5PdXFuZ0tUSXZMOWdBR0tuVWI1YUtBYm1JU2lFTDJaakJORWRKdGROcFk4Vmw1ZGdsVktwSHl4d04wQUV6cUMrNE0wcXcrSXhienluZnprZVRoUWh4RlVuMXhVRDBZTWQ4bC80dVE1eDlWcm9SekI5bUtZTncrZVMrZGErR1B5bW5aV1JldGRoZWQ1dyt1cTNubkl1aWcwdml3RkhUTVV4SEIwUzR3RXV4ZXVMckp5S3RlTmt3K3ozakxrNEMvOEVlenY1aUsyRmwiLCJtYWMiOiJkMGNmZDVlOWUxNWIyMTA0N2JmMDMzYjg0NmUzNGViZDA4MmFkYmQ0MmM2YjI4YjNjNTAyNzJjZDk1Mjg2YzViIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:44 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImRyTUtrencrcHZlc2Y4VjBJTjNld2c9PSIsInZhbHVlIjoiYWxDUGFNSG96L1I5L0xJZHBTRnNlN0RXMktSSVpMcVpEWkk0RHh2S05FOVRFRFY5MVM2ZUZ6clpnWjkydU9ZZTZpZkhGV29UaUg1Q1lad1hwQ0F3UC9QdkFLYm56YVU1NVBMaEttbE00a0U1Y1NYVFk2OTBRdUZFV2FBR2Zpbnk0R0ZSTms1cHRMQUU5cGZjQk1uQ3JyYi9OTGY0VnVUYkYxM3lBM0gybnRnb0pMeGlmWGl6SjB0alZ6T3BtMTlvMm9SRUZXakdudXNZU0VqRDU5YVh1S1BESWNscGo4K2UrS1g2OEhVbmcxNTF4UE1oeWRrdnhycy9XREU5QlVIdE83b21kcUlRZU44TVRtZGZoMFBuN3Z6WG0vaXN4azJiSVcxM3Y4eWVOQUh0WGVIaE9rT1VOanE1SklEbk1QbkxhL08zNVA4NkxRVUdDbmt1ZFJvNDZhdFN3NTViMDlTaE9mU1RDd0Q3N1NURGVHRmd1Mmh2VzdweENWVHFGcXBsUCtMd3YzS0FhS09zVlpXS3l0ZEc0VTJzV2dYdVJWM3hmR1U0NU9mbndyZnh0UThnMkNGVVBsQTVCVVVWM3BRQ0RPbXNjUjRoRDM1cExtaytrQ1k0VjVKZE40a0RYaXI5R3BQbUh0ZGZSdmRUbVpGSllqTXNxd1ZqYktTWXpkQ3UiLCJtYWMiOiJmYTNlN2E1OTZmNzg4Y2UxODRkOWQ2NzRjYWE1YzdjY2VlODQ3NGJmNzkxNzEzNjQwZjVkMDI1NTViNDAzMWViIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:15:44 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Inc0cXJXbkYyeDI2R0NnK1ZXR0cvK2c9PSIsInZhbHVlIjoiUEUwN0o5M3ZtbjVla0xzZmhITU5tQ0Z6TFZLSkxiV1QzOUVnM3BpNVdWV2lqLzYrc3lycE5CbWdpNnJoWXJua1hlcG5YanBBaXczTDd1VVVvR29QM29kWmxmTWdmL3krcnBwcEU0b2NrTjJTUDVOdEJwNUkzd1FHcEZ1WUtaRE5VdVpHY2hNTmhmd1JxZnZycVA3QzE4ejg3eFI0bGVPVHduNlF0b3JTKzNvNHVoeWNUMlRMY3lpRkdIRDdPVHhyY25lLzBpSWdiOTU2b0x3bE9zTG90SWFVQkVKWmhTOHhLZGs2dFFjczQ1Qk8zVUdYa1A5NHh5V09lb0MxOHhQbDl3VzBzanlWRk5zUXNvUXM3MENucllndlIyRThPN2hEeitGYWhvNU5TNmh6QjRBbDlpOVVGbW5PdXFuZ0tUSXZMOWdBR0tuVWI1YUtBYm1JU2lFTDJaakJORWRKdGROcFk4Vmw1ZGdsVktwSHl4d04wQUV6cUMrNE0wcXcrSXhienluZnprZVRoUWh4RlVuMXhVRDBZTWQ4bC80dVE1eDlWcm9SekI5bUtZTncrZVMrZGErR1B5bW5aV1JldGRoZWQ1dyt1cTNubkl1aWcwdml3RkhUTVV4SEIwUzR3RXV4ZXVMckp5S3RlTmt3K3ozakxrNEMvOEVlenY1aUsyRmwiLCJtYWMiOiJkMGNmZDVlOWUxNWIyMTA0N2JmMDMzYjg0NmUzNGViZDA4MmFkYmQ0MmM2YjI4YjNjNTAyNzJjZDk1Mjg2YzViIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImRyTUtrencrcHZlc2Y4VjBJTjNld2c9PSIsInZhbHVlIjoiYWxDUGFNSG96L1I5L0xJZHBTRnNlN0RXMktSSVpMcVpEWkk0RHh2S05FOVRFRFY5MVM2ZUZ6clpnWjkydU9ZZTZpZkhGV29UaUg1Q1lad1hwQ0F3UC9QdkFLYm56YVU1NVBMaEttbE00a0U1Y1NYVFk2OTBRdUZFV2FBR2Zpbnk0R0ZSTms1cHRMQUU5cGZjQk1uQ3JyYi9OTGY0VnVUYkYxM3lBM0gybnRnb0pMeGlmWGl6SjB0alZ6T3BtMTlvMm9SRUZXakdudXNZU0VqRDU5YVh1S1BESWNscGo4K2UrS1g2OEhVbmcxNTF4UE1oeWRrdnhycy9XREU5QlVIdE83b21kcUlRZU44TVRtZGZoMFBuN3Z6WG0vaXN4azJiSVcxM3Y4eWVOQUh0WGVIaE9rT1VOanE1SklEbk1QbkxhL08zNVA4NkxRVUdDbmt1ZFJvNDZhdFN3NTViMDlTaE9mU1RDd0Q3N1NURGVHRmd1Mmh2VzdweENWVHFGcXBsUCtMd3YzS0FhS09zVlpXS3l0ZEc0VTJzV2dYdVJWM3hmR1U0NU9mbndyZnh0UThnMkNGVVBsQTVCVVVWM3BRQ0RPbXNjUjRoRDM1cExtaytrQ1k0VjVKZE40a0RYaXI5R3BQbUh0ZGZSdmRUbVpGSllqTXNxd1ZqYktTWXpkQ3UiLCJtYWMiOiJmYTNlN2E1OTZmNzg4Y2UxODRkOWQ2NzRjYWE1YzdjY2VlODQ3NGJmNzkxNzEzNjQwZjVkMDI1NTViNDAzMWViIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:15:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2026804608\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-515653540 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/20</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-515653540\", {\"maxDepth\":0})</script>\n"}}