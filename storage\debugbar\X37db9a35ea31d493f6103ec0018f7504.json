{"__meta": {"id": "X37db9a35ea31d493f6103ec0018f7504", "datetime": "2025-06-27 01:26:25", "utime": **********.760319, "method": "POST", "uri": "/chats/favorites", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.25157, "end": **********.760335, "duration": 0.5087649822235107, "duration_str": "509ms", "measures": [{"label": "Booting", "start": **********.25157, "relative_start": 0, "end": **********.693191, "relative_end": **********.693191, "duration": 0.4416210651397705, "duration_str": "442ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.693201, "relative_start": 0.4416310787200928, "end": **********.760337, "relative_end": 2.1457672119140625e-06, "duration": 0.06713604927062988, "duration_str": "67.14ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45029248, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/favorites", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getFavorites", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "favorites", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=454\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:454-477</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.00349, "accumulated_duration_str": "3.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.730595, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 59.026}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.744173, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 59.026, "width_percent": 23.782}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.751328, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 82.808, "width_percent": 17.192}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/vender\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/favorites", "status_code": "<pre class=sf-dump id=sf-dump-2022607269 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-2022607269\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1354397379 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1354397379\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-802550606 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-802550606\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-338156392 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750987476327%7C94%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6ImxSbUpaYjhJVWJJN1F1S0NwelR2dUE9PSIsInZhbHVlIjoiYjFsWUxzdllZaEJ2bi9jVU9GUU9tc3owOUhhSW05WUNLWGoxRW1ObDNwdUNReDdZRjUrUGpjcXVFZkYycUxRRVpCM1Uwa0pxRm9xcFZuc2RvdWU0Z0FMcFZrM29sU092TWtXOE1tSmg3UE5oUkdaVUFjSllOSksxYTBvanBQbVppZS9xYmdHZS9YTnU5blc3ZFg2cGJ3RkJlSGJEeUZZNkhpR2VlVkJIcXY2a2Iva2ZUOUZrZUJIaUhNMzlPYTc4U0NjRzdNbWFOQUNZa1hCVEE3NFppak9OV283QXR4YkpqY1lnUk96OE5BT0wzRkR2YlZaT2REcFNhL21pdGJYYWowVDRuUStEZENkK00wdThBQmlYenNEcFFaS0FVWlZLeU0vOXJJQmNYQ1laUGVNUUl1M2xDK2ZEaS9wdVp3U0tra0ZnWU1ySVVua1lMMjRzZ21NdWYxWjlySWdDSEh0S0NDeDg3VldKbEFkeGpjOEJGbWxUWVgvTE9JZFVLWXJoekZwaGswU0JwaDZSN1RMWXBvOVd1YTJhcXJyZ2xEeFRsbmUvMm5YTUg5SGs5TmNIb3FQdnpwM0orVUxEUVM0VSsycEtxeEhqR0dhazNBUmZNUGJWOVhOK24wM0pkK0dEWDNFTEtOVGVCQmZPdW1kNnF5L24zWVkvWUlHdEtTRzciLCJtYWMiOiI0NGE5MWZiNDA4OTdjMzAyOWZmNDQ1ZWQ0MjBmNWZkNjhiYTMzNjkzMWFlMzFiYmE3OWFkYTVlOTNlNjM3YmI3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjZmZFJuTVVrQUJYUUtlVDF2Z2hwUlE9PSIsInZhbHVlIjoiVWdrNzdwc09sM0FsaGNpVTVLT2VMMk5IelJJSnNTVFdqVkZXUURHcjhUaW5mM2xNQnk2ajlnNFVnZjlTRHNsMzkzMStvWU1Ka1B5Q0o1c3FRay9xa2pvM2NZaWJneUdHT0Z5T3g4WWNPTldOQ1pjZUNHM3d3ejlDU2NxNFhaWXhsNSs4RFE4UU9kRzUyZzNBd1dGZ2orUi9DbXZyQ3cxRTBhTVlITWtlVkJQaUY3SWFvYi9TeS9lMWVReURXL0JKR3VCaHNva3NVeUdDRkozbFhsSVVtRW4xYW91MmdtNFVhdHJmZ01JK0phNnlxY0RpekVORFNhWDdRY0xQTlM0U1BBcWRzRGMyZUJpenZZaTFSSURLUGwxK2NGNEk1ek5CWmUvNGdRSFIxQUYwUTk3SG5UVmJoRUtCMndYclN5MVhhbUVLd01NeUhOc1Y3cmF4MWZ5aU5IZDJ4azhsZUMxWjJqZmJ6dUwzSU9OYXM3emRyUW9oR0h3cU0wOHhCNHNrRzlqYjJMOVRKbDRpUG5tUVZkNmFkc0J4ZFlHVWc1czNhNzg0Sy8xb1dCYTYyeWZNWEhGcTZwc2UwbzRXViswc2l6WkJlRWV0SDZReGtodlFVYVFQMXdZN1dFaDFybm55blhkcjczZlBoNWVJKzEvdllRRWNST1ZXZjU0YzV4VmUiLCJtYWMiOiI3ODNlMTQzYjk5NjcwMmUyMDFmOWI5ZGMzNDg3MjI4NWUyYTUxNTAzY2U2YjMwNTkwZmI3ZmUzNWZjN2JkN2VhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-338156392\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-411950065 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411950065\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1515869319 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 01:26:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InlZY2V3YVY0WXEzbVRKZ2Q2YXFacnc9PSIsInZhbHVlIjoibk9PRG04eXVtbk82TzhRN1hoNnMydGl5NWVaMytMVEhxaDBUN0src1R2a0kwVHNNaVM4R05wdEY3S3drTmtKNW45ZW1LS2x3QWVzbENzYmtNTzJrTlpnRjZjN2NuQ2tVY3hwN0YrLzUyNkZEdTJPUFRWT0RkYjRrZXlIclowem10Mkx1dVR4S3U4VTl6cTA3R0FEV1BTR2dpNkN6OUxPL3Z5cHQ3Ym9NN0NxNVcreThIQVR6cUlhcWZCQ2VyemFmVTRpL2NJMVhzL2g4TlR6V2hEL2Rhc0UybW1oYmhac2tyWFc0WERqbFJxWmRvOEI0eE4wdyszUGNxeVJHRE1YRHdMV2tMWkh0TmRJMSt2MlkwdXR1eGFieGU4NlBmSXJFZUpBaHNSSGdPT3FXcHAzbGhxWjNIMm1oZVBlelN4bzVQSzNQNmlMaGxCRUtHNHFjSlNwMmNsNVJYTHlIZGlaSjBKRmNOZW1mSnh3eGIwNm85ZnZWZDdtMHNCek16UjJUd3dtaXNYTk9HTTBhSmQrSkdEelVTTDFSQ3VMbW9XUW4yMk1YcjFSNlVvYnpiazhERG1uUm1semxKV01STUx5bTl2OWRlNWNON3hRRXNQTi8rb3RtMFdxbHJVeG5mQ3V5eG85WDQ2MHJSbjNKYUlTaTFQd3crN3preWxUYmRlZW0iLCJtYWMiOiI2MGE3ZTQ3ZDY4ZjU3ZWQ1ZTU0NDVjNzg3MmFiZTJjODM3YTQyNmI3NDExYWVmNWRhMmY2OGI0ZWRkZjk4OTRkIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:26:25 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IkwwMnd1d2JyWVByZnVWVkZXZDE3Y1E9PSIsInZhbHVlIjoiWTlBWlg2WEpobXJYVG4zaUEydWdsNnBMT2U3ampCNjBlQmZiRHNVbTBNZXl6V3l0VGdybElhSVcrZEV5bWM4TFJMbUVxY1VMcW04Tmp2TmdsSzJWMHlsRjNUMXBNd2RDNC8wa2tBWjlEcnhlYUV3WkpENmZ2ZTRVMkRkdlVsRzVLazFYTDBHVUtwMVR4dm1mQXVKS0Y0KzNIT0tDblFDREdRMTZUV2J5R3RyTTBTTERKMS9Cbk9nZUt0TW5VcU5uYnNYc2tTTkp5bUtPZTZVekthN1hBUzdQN3l2RTFXdi9tdC84SkVzWHBxdEF6dDJNY1ZYZVJ6WldCbThYbFFQL0l3L0pMN0FIc3pzejhidnhyeEwvSkJrUnFGczdBTy90QmlWaVZzUmhRT1pLMDJCdkpmZ3FTem5wcDFUTm5GaTJoVVAwVnVPK0thNWNpbnJuREFIaUFqZWpwaW5FbUFRVFNBN0JhS2ovQnZVWVhzZWc0dUNPaGdFSy9HbVNmTk9DQXBFTGZtWjd5NzZsUVFMRExVT2Y2NmlIOVBvUmtaSENmZDc2TkhlSmlDRk9rU3RnTi9wOGk3eFhIaXp5UkNpbzBzdnVTRWlpRkcwWXRhRzg4WGhVTDIvY3p5T0xPZkhDK3JRRmkwTGQwd0FQUTdlc1RUbGtsWDJXRVZuWG90SjgiLCJtYWMiOiIzMGRhNjRhYTNlMWMxMDllYTQ0MDEyZmE5OGFhN2E0ZDJhMDQzNGVkYWYxNTQ0OTBlMDYzMWNhY2RhMThiMzEwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 03:26:25 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InlZY2V3YVY0WXEzbVRKZ2Q2YXFacnc9PSIsInZhbHVlIjoibk9PRG04eXVtbk82TzhRN1hoNnMydGl5NWVaMytMVEhxaDBUN0src1R2a0kwVHNNaVM4R05wdEY3S3drTmtKNW45ZW1LS2x3QWVzbENzYmtNTzJrTlpnRjZjN2NuQ2tVY3hwN0YrLzUyNkZEdTJPUFRWT0RkYjRrZXlIclowem10Mkx1dVR4S3U4VTl6cTA3R0FEV1BTR2dpNkN6OUxPL3Z5cHQ3Ym9NN0NxNVcreThIQVR6cUlhcWZCQ2VyemFmVTRpL2NJMVhzL2g4TlR6V2hEL2Rhc0UybW1oYmhac2tyWFc0WERqbFJxWmRvOEI0eE4wdyszUGNxeVJHRE1YRHdMV2tMWkh0TmRJMSt2MlkwdXR1eGFieGU4NlBmSXJFZUpBaHNSSGdPT3FXcHAzbGhxWjNIMm1oZVBlelN4bzVQSzNQNmlMaGxCRUtHNHFjSlNwMmNsNVJYTHlIZGlaSjBKRmNOZW1mSnh3eGIwNm85ZnZWZDdtMHNCek16UjJUd3dtaXNYTk9HTTBhSmQrSkdEelVTTDFSQ3VMbW9XUW4yMk1YcjFSNlVvYnpiazhERG1uUm1semxKV01STUx5bTl2OWRlNWNON3hRRXNQTi8rb3RtMFdxbHJVeG5mQ3V5eG85WDQ2MHJSbjNKYUlTaTFQd3crN3preWxUYmRlZW0iLCJtYWMiOiI2MGE3ZTQ3ZDY4ZjU3ZWQ1ZTU0NDVjNzg3MmFiZTJjODM3YTQyNmI3NDExYWVmNWRhMmY2OGI0ZWRkZjk4OTRkIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:26:25 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IkwwMnd1d2JyWVByZnVWVkZXZDE3Y1E9PSIsInZhbHVlIjoiWTlBWlg2WEpobXJYVG4zaUEydWdsNnBMT2U3ampCNjBlQmZiRHNVbTBNZXl6V3l0VGdybElhSVcrZEV5bWM4TFJMbUVxY1VMcW04Tmp2TmdsSzJWMHlsRjNUMXBNd2RDNC8wa2tBWjlEcnhlYUV3WkpENmZ2ZTRVMkRkdlVsRzVLazFYTDBHVUtwMVR4dm1mQXVKS0Y0KzNIT0tDblFDREdRMTZUV2J5R3RyTTBTTERKMS9Cbk9nZUt0TW5VcU5uYnNYc2tTTkp5bUtPZTZVekthN1hBUzdQN3l2RTFXdi9tdC84SkVzWHBxdEF6dDJNY1ZYZVJ6WldCbThYbFFQL0l3L0pMN0FIc3pzejhidnhyeEwvSkJrUnFGczdBTy90QmlWaVZzUmhRT1pLMDJCdkpmZ3FTem5wcDFUTm5GaTJoVVAwVnVPK0thNWNpbnJuREFIaUFqZWpwaW5FbUFRVFNBN0JhS2ovQnZVWVhzZWc0dUNPaGdFSy9HbVNmTk9DQXBFTGZtWjd5NzZsUVFMRExVT2Y2NmlIOVBvUmtaSENmZDc2TkhlSmlDRk9rU3RnTi9wOGk3eFhIaXp5UkNpbzBzdnVTRWlpRkcwWXRhRzg4WGhVTDIvY3p5T0xPZkhDK3JRRmkwTGQwd0FQUTdlc1RUbGtsWDJXRVZuWG90SjgiLCJtYWMiOiIzMGRhNjRhYTNlMWMxMDllYTQ0MDEyZmE5OGFhN2E0ZDJhMDQzNGVkYWYxNTQ0OTBlMDYzMWNhY2RhMThiMzEwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 03:26:25 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515869319\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-755842485 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"23 characters\">http://localhost/vender</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-755842485\", {\"maxDepth\":0})</script>\n"}}