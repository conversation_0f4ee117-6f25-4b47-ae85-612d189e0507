{"__meta": {"id": "Xef40cda833a792f0da4001d1e226fb74", "datetime": "2025-06-27 00:26:02", "utime": 1750983962.03082, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.533052, "end": 1750983962.030836, "duration": 0.4977841377258301, "duration_str": "498ms", "measures": [{"label": "Booting", "start": **********.533052, "relative_start": 0, "end": **********.965646, "relative_end": **********.965646, "duration": 0.43259406089782715, "duration_str": "433ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.965655, "relative_start": 0.432603120803833, "end": 1750983962.030837, "relative_end": 9.5367431640625e-07, "duration": 0.06518197059631348, "duration_str": "65.18ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45027680, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.01824, "accumulated_duration_str": "18.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 15 limit 1", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.992718, "duration": 0.01732, "duration_str": "17.32ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 94.956}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": 1750983962.018316, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 94.956, "width_percent": 2.577}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (15) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": 1750983962.023993, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.533, "width_percent": 2.467}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost/bill/eyJpdiI6InBCSnZpbFRhcjdqQWg3T0NjbEk2L0E9PSIsInZhbHVlIjoicjNXTDArVnRYcU4ybXoySEZtL0pIUT09IiwibWFjIjoiNTVjZDE5MTMwODk3ZmE0MTZkM2E3MWQ2Yzk4MzI2NjAyMzAyMzc1MmI3MmY2ZjJkY2MwZGZiY2M5MjA0YTg1NiIsInRhZyI6IiJ9/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "impersonated_by": "1", "impersonator_guard": "web", "impersonator_guard_using": "null", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "15", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1720563222 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1720563222\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1556104457 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1556104457\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-419814508 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-419814508\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1425585915 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InBCSnZpbFRhcjdqQWg3T0NjbEk2L0E9PSIsInZhbHVlIjoicjNXTDArVnRYcU4ybXoySEZtL0pIUT09IiwibWFjIjoiNTVjZDE5MTMwODk3ZmE0MTZkM2E3MWQ2Yzk4MzI2NjAyMzAyMzc1MmI3MmY2ZjJkY2MwZGZiY2M5MjA0YTg1NiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">ar,en-US;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2003 characters\">cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; __stripe_mid=3aedaf5a-20fc-47be-a48d-fc0a29ce00daa17389; _clck=1ap6d1q%7C2%7Cfx4%7C0%7C1998; _clsk=8maz9g%7C1750983955568%7C57%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IlZPZ1k2S2VmZmhjVkZGd3BvZEpTY1E9PSIsInZhbHVlIjoiTkdZejREU1hXZDZBVzkvRUhQdEZMMDR1d3Uvb3FucFZZMGh6RjA1UmFqdlQ3bHBxTExMZmlQTHVPazJUcVJxSjB6RC9TaU5GTEladnJVU2JBdFhoQUNCSVpnSGFuNUZySitmUzRQVHlmUzVpTTRYTVRWa3d6VVNkbi9pcEExWjF2TDNUNTE1bUNxVnUvMWxodldzZE1yQjlrckdRMGg5ajh0T3JOQ0MzN1FIQnN4ajV0QXVxZ0NLK1p5dGJ0L2FXS3lhQmF1TzdvN1RzUzdVRVJaVDdlaHM3WWpwaEt1Vjl3b241VCtobVRDREsvN3pXUkxpL0t4OG5pcUNqT2pkUkVyVjdsQnRGallVdkVtM1VPZlphTUF5VXNoWkduaWF3RmVXNVhHcHVTdjVmRHdJNVVYZk5yQ0o2OG45Rm00d2VXcGNIT3d3dUtYSlV5SGJRWUFGbDd0UUdGcmFINzJUSHc3QUhVYzRrM0FLemVYR2xicWdzU1hjUHQ3eEdtOEIybzdLTld5OW5HVlhBOER5ZzhRTk5zM1YyR0tDZW5CYVZocE9TdnBPc0xMUDFZbndtb2llNEQ0ZzNEaW1lOE5ZMFU1bHdESmNXa29adXJqRFdzbGRSSFFKcFFoWjhaaVNWbHNVQU81NzZLNEhQQkh5MUNoWk4zeDRHV200VFJsZ3UiLCJtYWMiOiJkNjdhZTc4OWU5MjkxNDFmMjViMTg5YTcxOGRhODg4YTdmMGQ1YjQ0ZjIzMjAwM2NjNmViYzUzYWFhNjQ4ZmM2IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6Inl2TjJmOERpd2tSM25KRnB4aXp0REE9PSIsInZhbHVlIjoiMk43dUw3MFYvdmE1US9jbjI4Z1pLaUt6TUhvMEVwc3orWHpaR3FLOVJ2NlRaR2xXT0JIYzZYZEZMc2ZSMHY1K3VTaFZpUm9GRnBSMDlzTTR2VFF0TWpHUCtRdzBNd3l3MXZMYzQyVTl0SVdUSVVmT3pwSFh1WE8vN1ZzWVZYUjVVL1pudFZGR2liN0tLeUpGTFJRTG1NblJmTDY1dU96T0FRYXVRaGRIRFJRWWhzM3pVWWlpc2JDTnFSYkNxVDYzTHpEbmN4SHRucXpuQjUwTTg4OFhMVE0wN0VWMDFxaUxkQ2M1TEc1Y3NWVkhtSDVTMlJmNUhQbXRlZng5bzRxa2ptS25wVHJGakF3UUg5TWE0NVVrK1FpdHpzMytqZi9UUDBheUVxMTlQYnFNU1ltZW1yd2tYTkx2SGN6YjBEUlhhZmo4T1lGZDdJZjZwTTFPTDB0WEIxNHdmb2VNODVBNXd0aythQ05xU2VOSERsZjZKd2NNUzQ5Nms2eWpwNndyU0hIcGRmVEN5NTNBeGoyeDhmVGtYWDJVbUlTNlphU3A3NjFiQkRqS0t2TkhkbkJNaC9DVVpxa1BTd29xNDNERWRLSjg1aGYvK2NmejJqNGt3M245eFVGUFN2TXZWMWh4Wkp4QndLUXVOZE5RTzlGQlNCc2UrSWJJb2xxTzB6VnciLCJtYWMiOiJmNGZhNTkzNTE2MWQyM2E1M2EwNmRiODFkZjM5MTcyYzQ4OWI0ODE5ODQwNTFjYTBmZjkwMDYyNzU4NGJmNmU5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1425585915\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Lz6NiFuhSnpbOQ747HyKYgIopK3AaYa4t5ZTjyYp</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1387471403 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 00:26:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6Ik1tdHo3cWs5eWJLVG1WYWZ6MGwySVE9PSIsInZhbHVlIjoiZmUwdm1IVy9lL1pPMWRsSXAwU1JpdlVNY0VhK1FyRU9tMVBrSHI4V0cxR1dCQjJCS2pabmJVeURQVWM0Y2Y5SmhjK0ZmNWEzVEdqYysyaXR1WW1na20zeWNtMkpnK1ZpbkdJakpaSmNtNzVWVS9xRWR5a09pRklwbEpNbXNIRGwwdXc3blhlZlNEYUc4UWJOWlkwWkRMZ0hneXJYTUhaOG9heXUvK0tNUWsraEFReUtpblJMUHgyWU9KdFp6UFg3SEZUQjdDMGhCclg3TEpPaUR3NVBHTUgrVWk5OUNZYnNHaVh3VWkxQS9oUk1Yb1prSUZwRktnWVltRzA2YTJZNkZSUHVYRjNsc0N1SFVTOXEzakNha2F0RDlKYStSbU9ZaHdoaFpKSW1jcHNmeSs3RmJZYkZaZ1Y0N1F4MXR2bjR6eGJZbUp0Rk1TOTcrNEJqTEVPR0Y4M0lyZHFxc2I4SFViVDIwNnlHZVUxRkRCRkFDaFF2M002TFFUWk9KbytxejJzSHZsUGJCclJuYWhYcSszbXRzcGZqeEwxbGt3YjZIYWZzSTloeHhtVCtRZlRmRFQ4d2dYeUpRZFNJbE1Oay9zMXkraFAxNGxLV1NPSkNMQnVZaUJBSXducFVvN3haN1hVdzVla3diV3hzUUxCYXR3U3lhK0RKQ3F1MHE0c3QiLCJtYWMiOiI3OGMwOWY4ZTU2ODdjMzE1MTVkNDZlNDU1MGJlYTM1OWQ5YWQyMjU3YWNiZWQ3M2U2YjI0ODViODdmMTQ0ZjMwIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:26:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6ImE0ZXJNa3ZwSzNlOVRGQjQvZm94ZEE9PSIsInZhbHVlIjoiNWNZU2hsR0MvUkd1c2lEaGswU3Z3YXd6OHBiRklkUlBoREp0cUtzODVsaGNocFRFUnVYSFZmSWYyZ1MxT25RZzBEWGlhelFvWnZuaHA4S1liNHJHa2ZZNlEvRHROUzU0SnlKY2hqVG41N0lESm8zV1JCTnBBcU9JVitBRVl2YTRSVHNHY0dGY2M2TTMyaE5RTEg1RUJ3SnBiWEdiQjkzcmxTRGVJVUZpb0ZqMUpDMHI3WUluVnFGYnZIQUJ3bEovaHFOM2w4M3JSeEp1d1JPRlpaYkZiVjZMM0h1NU9WcExweEFya1VvbWlNT2JmN1MzdWpwWGYwNG9kd0RKc2VvdzJ6RU1TQ1R1YmluM2pta2kvZmxmaDJTeHp4OE9qRk9KSGtOWlJ1WnBqS1hJdlZWK29yell3bVpMZXdLdVFxVERVbm02aTVsR2l3bVRGWUJUd0NhMnpoaWplTHEwQUxVaWpvc1dLSS9jT2kzanZlUmptZzdBUEpBUXU5eGZ6SmpvNldDMVNiaVJ5SVpqd1RYbysyZFRqZ1ZqMDkrbzRxeUxvN0lvbW5FRnh1YmZUOElGWm13WmtzT1ZUUmdyMFNEazVFYk1qdkFqdzNWUXRSRVV1Z2tKL1lsSFlZRGJWb1NaSm5zTXZZZmEzQzBlcTlSZ2xFb1NReldNOHZVL2RQOHAiLCJtYWMiOiJhZjhiNTZmMTZmZmE0OTY5MDU3N2UwOWJlOGVhMzE2NTQwMzg4OTgyODQwYjEyZmQ0YmVjN2I0MjU2MGQ5ODg3IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 02:26:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6Ik1tdHo3cWs5eWJLVG1WYWZ6MGwySVE9PSIsInZhbHVlIjoiZmUwdm1IVy9lL1pPMWRsSXAwU1JpdlVNY0VhK1FyRU9tMVBrSHI4V0cxR1dCQjJCS2pabmJVeURQVWM0Y2Y5SmhjK0ZmNWEzVEdqYysyaXR1WW1na20zeWNtMkpnK1ZpbkdJakpaSmNtNzVWVS9xRWR5a09pRklwbEpNbXNIRGwwdXc3blhlZlNEYUc4UWJOWlkwWkRMZ0hneXJYTUhaOG9heXUvK0tNUWsraEFReUtpblJMUHgyWU9KdFp6UFg3SEZUQjdDMGhCclg3TEpPaUR3NVBHTUgrVWk5OUNZYnNHaVh3VWkxQS9oUk1Yb1prSUZwRktnWVltRzA2YTJZNkZSUHVYRjNsc0N1SFVTOXEzakNha2F0RDlKYStSbU9ZaHdoaFpKSW1jcHNmeSs3RmJZYkZaZ1Y0N1F4MXR2bjR6eGJZbUp0Rk1TOTcrNEJqTEVPR0Y4M0lyZHFxc2I4SFViVDIwNnlHZVUxRkRCRkFDaFF2M002TFFUWk9KbytxejJzSHZsUGJCclJuYWhYcSszbXRzcGZqeEwxbGt3YjZIYWZzSTloeHhtVCtRZlRmRFQ4d2dYeUpRZFNJbE1Oay9zMXkraFAxNGxLV1NPSkNMQnVZaUJBSXducFVvN3haN1hVdzVla3diV3hzUUxCYXR3U3lhK0RKQ3F1MHE0c3QiLCJtYWMiOiI3OGMwOWY4ZTU2ODdjMzE1MTVkNDZlNDU1MGJlYTM1OWQ5YWQyMjU3YWNiZWQ3M2U2YjI0ODViODdmMTQ0ZjMwIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:26:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6ImE0ZXJNa3ZwSzNlOVRGQjQvZm94ZEE9PSIsInZhbHVlIjoiNWNZU2hsR0MvUkd1c2lEaGswU3Z3YXd6OHBiRklkUlBoREp0cUtzODVsaGNocFRFUnVYSFZmSWYyZ1MxT25RZzBEWGlhelFvWnZuaHA4S1liNHJHa2ZZNlEvRHROUzU0SnlKY2hqVG41N0lESm8zV1JCTnBBcU9JVitBRVl2YTRSVHNHY0dGY2M2TTMyaE5RTEg1RUJ3SnBiWEdiQjkzcmxTRGVJVUZpb0ZqMUpDMHI3WUluVnFGYnZIQUJ3bEovaHFOM2w4M3JSeEp1d1JPRlpaYkZiVjZMM0h1NU9WcExweEFya1VvbWlNT2JmN1MzdWpwWGYwNG9kd0RKc2VvdzJ6RU1TQ1R1YmluM2pta2kvZmxmaDJTeHp4OE9qRk9KSGtOWlJ1WnBqS1hJdlZWK29yell3bVpMZXdLdVFxVERVbm02aTVsR2l3bVRGWUJUd0NhMnpoaWplTHEwQUxVaWpvc1dLSS9jT2kzanZlUmptZzdBUEpBUXU5eGZ6SmpvNldDMVNiaVJ5SVpqd1RYbysyZFRqZ1ZqMDkrbzRxeUxvN0lvbW5FRnh1YmZUOElGWm13WmtzT1ZUUmdyMFNEazVFYk1qdkFqdzNWUXRSRVV1Z2tKL1lsSFlZRGJWb1NaSm5zTXZZZmEzQzBlcTlSZ2xFb1NReldNOHZVL2RQOHAiLCJtYWMiOiJhZjhiNTZmMTZmZmE0OTY5MDU3N2UwOWJlOGVhMzE2NTQwMzg4OTgyODQwYjEyZmQ0YmVjN2I0MjU2MGQ5ODg3IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 02:26:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387471403\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-872899836 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NKjWKgvntqwh9S73JjUpb74LSslQK9TKcyUVNYi1</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"227 characters\">http://localhost/bill/eyJpdiI6InBCSnZpbFRhcjdqQWg3T0NjbEk2L0E9PSIsInZhbHVlIjoicjNXTDArVnRYcU4ybXoySEZtL0pIUT09IiwibWFjIjoiNTVjZDE5MTMwODk3ZmE0MTZkM2E3MWQ2Yzk4MzI2NjAyMzAyMzc1MmI3MmY2ZjJkY2MwZGZiY2M5MjA0YTg1NiIsInRhZyI6IiJ9/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>impersonated_by</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>impersonator_guard</span>\" => \"<span class=sf-dump-str title=\"3 characters\">web</span>\"\n  \"<span class=sf-dump-key>impersonator_guard_using</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>15</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-872899836\", {\"maxDepth\":0})</script>\n"}}