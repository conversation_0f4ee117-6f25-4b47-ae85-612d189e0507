{"__meta": {"id": "X497dad392e8447a2b4ee9172dfa37e1c", "datetime": "2025-06-27 02:34:26", "utime": **********.957987, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.515666, "end": **********.958002, "duration": 0.4423360824584961, "duration_str": "442ms", "measures": [{"label": "Booting", "start": **********.515666, "relative_start": 0, "end": **********.884138, "relative_end": **********.884138, "duration": 0.3684720993041992, "duration_str": "368ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.884148, "relative_start": 0.3684818744659424, "end": **********.958005, "relative_end": 2.86102294921875e-06, "duration": 0.07385706901550293, "duration_str": "73.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45735016, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02316, "accumulated_duration_str": "23.16ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.90906, "duration": 0.02174, "duration_str": "21.74ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 93.869}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.940867, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 93.869, "width_percent": 3.152}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9488661, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 97.021, "width_percent": 2.979}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/payment-voucher/22\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-429220232 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-429220232\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1982660485 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1982660485\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1057370775 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1057370775\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991660993%7C42%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IkpnYk9pcHB2TGszQzNudk9HTmRPWlE9PSIsInZhbHVlIjoiSGUxSml0MmN3TlZWYTg2cGhabnRIdkl3OVVrWlRhZVFhZkpmWEtuMUtUNUZ1cVZUR29zUnpMZkF5aGJjS1NSSGlYekt3QVRlMkJER0cvRVcrQ1h1U3g5T25qVTFlZHJySm8xZ2w3UTFUVWdUb3VTSGRRV2E5S0g2dExSd3Z6VVZuckZ0Y2JlTGE4ZmJab1FaamlrKzl5T1dyQ01sWEtLOENSVGs0QUJ0eFBLVGM4RmJ2M3VBWDB5RFNGZE96UHVjZE1QS3A1a0N4K2UrdjdDUDZDOHpmZlk0YUd1bWhPVXR3cFRhaGtrOXZERmgzWGR2aXJ0OVpCaVRtRVBOS0JLT2x5MFBaV2hJdVhkdWlOYVh1WEFhOXdqYzRSQnpOR1J2SXlOdnl0TUpEVWQyZDcxQU9pd2dtaVpRdHBRVzRhVFFOVXNqT3diNXRaeERDYkdrNlQ1NHF2ZFJaNTlMTU9YYkFMSzdaZXFNeE12K290Nkk3YnhTMFRvekZXUnJkZDhIZjZLN0FTY3I5dDZlZ2w1aUIrM3lxdmNQSE93VXFrY2NXY1NTR1hKRW8vZkdFNklwbjJ4RURCT2xkVGFjL0pYWHBoZE92YjRnbWsydGk4cTM0RG5BNVNhcmNLWldyVVZkNDZGRjlsNnJjdG1jdkYzRWplMis3MzZHL2owQWVSNUMiLCJtYWMiOiI5N2MyZjVmZjI2NWQxMWQwZGU2ZTdiNGQ4NDNiMGNhNmEwNzMwMmJmYmY3YzU0M2VhZTRjMGFmMzI3ODAxZDAxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6InZxeEFDSTdSeXRjcTB4d0djZEx0K1E9PSIsInZhbHVlIjoiT0pxVFNRWXV5MHRzUm1aMU9IbnU3aUl5R2xJRTFyRFB0VWhDNU9xVUZEMHVvcmVsQ2dLZ1FIMW4xV1h2eElVYXh2T0NISnoxMnE2MGhmQlVhTndKU2xva095TEhGRDZVZ3Fna0JaMy9laGtPL25BTnd2NUlhcDBySHFTTHdBbWNhYTBiU2Jvdlk1Y0N6aDRoS2s2bU9aTFBGU1VRSHJzc0pDQVFHUDQ3OCtJWkRGV0pMZy8yZGxkMDMyRkNYeHhCY1B6SmtBbTR3Rjh6Y01tRytUSUl4M2tyUlhqa3dPYUU2bnZQeUpKeEpoR2JQREx2YnVmMnNLaVBlcDFzNDJvWG0wS2pQZnhJRjc3WXFhdVd2eGhxMWhuYkhmWDd5ckNaa29YVlQ4VzBPSG9mTnQ4OFhSbGZpMEJwL0NPNzNXazIwS3VJakJnMjBxQm1nMHlaQzlIUHZXM1orWFUwL3l3T1hLMEo5NTR4cmRQK2ZDZHliYmFZTlRzUXZWdjRMemtUVThxeGVIRDRUdEZvTnQxd3FkQnMwZUI4NXRxUTQzdHFjWDNKQUs2eFo4WGljZXlnY0hpT2dOSlRPRkNjbXZYaVhIMFhtWlY1YnRwdEFYSVVJeW1xenlmL1RiSXQ4V3JnQS9rTGpzRDhCWHdkL2ExTlcvWUpuYVd3Ym0vVnZqMkEiLCJtYWMiOiI1M2M4MDIxMDdhZGFkMmM2ODNlMWEzODMxNzg1NjhlZWQ1NmE5MTg4OTFhMTQwMzkzNGUzY2I5YWVhN2NhMDg0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1093322028 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1093322028\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-518188899 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjBtck9PbVZ1M2VqeXA4eWFIbW1SNWc9PSIsInZhbHVlIjoiNjg4MVBBVkNNYlBsR01pbmRId3V0MnNZOTZXNUtMbzBjaG9yTkhiRXUxUUZXYUpXZ25LYVlHUHMyMzdhMTNUTm5QcHY3QlByMGF1MjJld3Jmcjl0VmhUaU9DUk5mMU9ZUXRYWHVsN3hZMmo3QzRCMWdDVkIvamFuZk5Rb3ZsTmM3Z1pIeDdwNHJIUWF3T1B4c05nSm9TRldWN1BuTjhTcEg0RzFtYS9oL3BnZk5Obkd5dlkrT3hkUjhNMTQ3bEE3UjkzRHhVcUluNi95bDliVzE2NlRwM1U3SE53VlVLb09sdEJQUU5lWncrZWl2aHFmYW1PVFBoQ3A2WWlIMEpEOHJoVDZTWlAzb0NMaVpKVjlHaUcvOVVHYUQ3U3NpWVdwaEpablpBUUlEaGU1NVdTVWJkdyt1aCtqTWFCZkNmeGFFOXB4YndkMVozOGk4TzF0NWw5ampWbWJpSG9Fdm1yZU0wNWFqekVOMjhyYVVqWTdBeHdzY3FKdjhTYktDc3BmYmZuWURPbUtZTHRjeHM1S015OGlSekRGRExsNm5aOGd5YnY5dEwvMFY1MzNaSTVhS3Y4dW9qblNlQStLS2h6ZUs3L0swaC9iMUhvQ3h2azZpQWVwRWxOSlBCdEd1bldyaDVnWVBxWTFrYk9sSzhxR2N0N0ZXc3hhMGRyeDc1L2oiLCJtYWMiOiJkYWQxYzJkYTEwZTgwODkxNjM5ZThkMTc2NGUxYzA2MjgwMWVkMTFlODZlOWE3MjlmY2RmY2JkZTMwOGIxNDBlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:26 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6Ik0xUUoyNFJXTVRuaGV3VjJ0WUE2OXc9PSIsInZhbHVlIjoiUGxKd0EwUEkraFZaeG12cnZRSDF3OWM5M0F3SFFURjBTdkhiM05JazZ2N1pqc1BrODhmc3NlNjM2MW9jbTVuc0puTmJ6SUlKclhJODRVMGN2MXFIQUptdEZmNmREUyt2QmpHc016Yy9JTlozeDFZaWtYak9RcjM5bjROSklFQW1TQ2h5VEVSb0NJN2tqSnBzRHZ2ZUVSdGJmOXhXWFNGTTBqM2srNnFZNFJDSWticjhuUVB2MmtCWlIwWDhLN2h4d0hCYThXRHZBTC9ReHV3TWFYbkhrTXNWVVBDQ3g3T0toTU9qWEZQN09RV2V6elVRRUQ3MEhPb2E5MW9wOE1NeStuSlNwTDFya3hUeERXWmN1SjNpUlFIVjFXK0Rzek96M3ExMnZ2UklVcTlIM05KcUQ4RTRxMFNTa3QzMmY3Uy96dzRZOXBYU21UZ0JMNGx4Z3B5Z1JMT2ZxM1crbTNkbFhkVkhMbGo1bWRvOWRpNW9TUWpkRDFueDlGL08xSEllK1FDdmFwZUlZVnErV2RVWEJHRmNNQmd1MXk3bElzMS8zNFI0c2F2SG80SGYrVTQydmF2SXNyNEZvWUFha3BPVUdVR3c4SkdtcVV6cjBQenoveW4zVi9aTWhERmswSklMeDRacmxDRmMza1EvbUd1eEdYd0Q2OTBkeFNKZmdIUnIiLCJtYWMiOiJlMGE5MjYxMWVmYTgyMTljN2I2MzgyNzQzMjlhYzVjZTUxNzc4YzEyZWNiZDc3Mjg4YTYzZTIyN2ZkODYzYmQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:26 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjBtck9PbVZ1M2VqeXA4eWFIbW1SNWc9PSIsInZhbHVlIjoiNjg4MVBBVkNNYlBsR01pbmRId3V0MnNZOTZXNUtMbzBjaG9yTkhiRXUxUUZXYUpXZ25LYVlHUHMyMzdhMTNUTm5QcHY3QlByMGF1MjJld3Jmcjl0VmhUaU9DUk5mMU9ZUXRYWHVsN3hZMmo3QzRCMWdDVkIvamFuZk5Rb3ZsTmM3Z1pIeDdwNHJIUWF3T1B4c05nSm9TRldWN1BuTjhTcEg0RzFtYS9oL3BnZk5Obkd5dlkrT3hkUjhNMTQ3bEE3UjkzRHhVcUluNi95bDliVzE2NlRwM1U3SE53VlVLb09sdEJQUU5lWncrZWl2aHFmYW1PVFBoQ3A2WWlIMEpEOHJoVDZTWlAzb0NMaVpKVjlHaUcvOVVHYUQ3U3NpWVdwaEpablpBUUlEaGU1NVdTVWJkdyt1aCtqTWFCZkNmeGFFOXB4YndkMVozOGk4TzF0NWw5ampWbWJpSG9Fdm1yZU0wNWFqekVOMjhyYVVqWTdBeHdzY3FKdjhTYktDc3BmYmZuWURPbUtZTHRjeHM1S015OGlSekRGRExsNm5aOGd5YnY5dEwvMFY1MzNaSTVhS3Y4dW9qblNlQStLS2h6ZUs3L0swaC9iMUhvQ3h2azZpQWVwRWxOSlBCdEd1bldyaDVnWVBxWTFrYk9sSzhxR2N0N0ZXc3hhMGRyeDc1L2oiLCJtYWMiOiJkYWQxYzJkYTEwZTgwODkxNjM5ZThkMTc2NGUxYzA2MjgwMWVkMTFlODZlOWE3MjlmY2RmY2JkZTMwOGIxNDBlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6Ik0xUUoyNFJXTVRuaGV3VjJ0WUE2OXc9PSIsInZhbHVlIjoiUGxKd0EwUEkraFZaeG12cnZRSDF3OWM5M0F3SFFURjBTdkhiM05JazZ2N1pqc1BrODhmc3NlNjM2MW9jbTVuc0puTmJ6SUlKclhJODRVMGN2MXFIQUptdEZmNmREUyt2QmpHc016Yy9JTlozeDFZaWtYak9RcjM5bjROSklFQW1TQ2h5VEVSb0NJN2tqSnBzRHZ2ZUVSdGJmOXhXWFNGTTBqM2srNnFZNFJDSWticjhuUVB2MmtCWlIwWDhLN2h4d0hCYThXRHZBTC9ReHV3TWFYbkhrTXNWVVBDQ3g3T0toTU9qWEZQN09RV2V6elVRRUQ3MEhPb2E5MW9wOE1NeStuSlNwTDFya3hUeERXWmN1SjNpUlFIVjFXK0Rzek96M3ExMnZ2UklVcTlIM05KcUQ4RTRxMFNTa3QzMmY3Uy96dzRZOXBYU21UZ0JMNGx4Z3B5Z1JMT2ZxM1crbTNkbFhkVkhMbGo1bWRvOWRpNW9TUWpkRDFueDlGL08xSEllK1FDdmFwZUlZVnErV2RVWEJHRmNNQmd1MXk3bElzMS8zNFI0c2F2SG80SGYrVTQydmF2SXNyNEZvWUFha3BPVUdVR3c4SkdtcVV6cjBQenoveW4zVi9aTWhERmswSklMeDRacmxDRmMza1EvbUd1eEdYd0Q2OTBkeFNKZmdIUnIiLCJtYWMiOiJlMGE5MjYxMWVmYTgyMTljN2I2MzgyNzQzMjlhYzVjZTUxNzc4YzEyZWNiZDc3Mjg4YTYzZTIyN2ZkODYzYmQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-518188899\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-886572583 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"35 characters\">http://localhost/payment-voucher/22</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-886572583\", {\"maxDepth\":0})</script>\n"}}