{"__meta": {"id": "X2452ee88d89eb33c61cada70bfb5d2b0", "datetime": "2025-06-27 02:25:30", "utime": **********.136516, "method": "POST", "uri": "/pos-financial-record/closing-shift", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991129.740906, "end": **********.136529, "duration": 0.39562296867370605, "duration_str": "396ms", "measures": [{"label": "Booting", "start": 1750991129.740906, "relative_start": 0, "end": **********.07384, "relative_end": **********.07384, "duration": 0.3329339027404785, "duration_str": "333ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.073849, "relative_start": 0.3329429626464844, "end": **********.13653, "relative_end": 9.5367431640625e-07, "duration": 0.06268095970153809, "duration_str": "62.68ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50171880, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record/closing-shift", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@closeShift", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.closing.shift", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=169\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:169-188</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.007560000000000001, "accumulated_duration_str": "7.56ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.102043, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 21.693}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.111622, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 21.693, "width_percent": 4.63}, {"sql": "update `shifts` set `is_closed` = 1, `closed_at` = '2025-06-27 02:25:30', `closed_by` = 22, `shifts`.`updated_at` = '2025-06-27 02:25:30' where `id` = '46' and `shifts`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2025-06-27 02:25:30", "22", "2025-06-27 02:25:30", "46"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 176}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.121886, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:176", "source": "app/Http/Controllers/FinancialRecordController.php:176", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=176", "ajax": false, "filename": "FinancialRecordController.php", "line": "176"}, "connection": "kdmkjkqknb", "start_percent": 26.323, "width_percent": 42.328}, {"sql": "update `users` set `is_sale_session_new` = 1, `users`.`updated_at` = '2025-06-27 02:25:30' where `id` = 22", "type": "query", "params": [], "bindings": ["1", "2025-06-27 02:25:30", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 183}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.126463, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:183", "source": "app/Http/Controllers/FinancialRecordController.php:183", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=183", "ajax": false, "filename": "FinancialRecordController.php", "line": "183"}, "connection": "kdmkjkqknb", "start_percent": 68.651, "width_percent": 31.349}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "success": "تم إغلاق الشيفت بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record/closing-shift", "status_code": "<pre class=sf-dump id=sf-dump-19175946 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-19175946\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-735443599 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-735443599\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-888652221 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>shift_id</span>\" => \"<span class=sf-dump-str title=\"2 characters\">46</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-888652221\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-224197567 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991127468%7C23%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6IjZ4TUZpTEpsMzVUdFc4bHFaY3dUa2c9PSIsInZhbHVlIjoiL0lmY2V0VW9mcExTNDlmZXQxWmV4WjVtN04zUFV6d3E3TjV6cEt6b04vcStaTy9CSjJJME8wcG1lcUJYaEVHSkRucXAzdFM0cmNUdVRjZWJMVVJ1akZuK21scFJpWEo3aTlueUw3VHNlRlVacWttU1AzVS9HYTRtWms4Rzl3T3k5Snk2Rng0alp4alU0TW9vTVQxb2MzTHNGdzVYRjdUbDd3OFhRaFc5RW5OWDZ4ZEZXZVRYSE9aZUZSOS9ROXQ4TWxoVDJ5RmF1bVFWYWZBQ0xoUW9LbGRreGtLL05hcVJMRjAvckhhcVRjR3FmVU5BSk1vUjd5UWdaMzR4SnlKYjY4UTlzTGRzVDFBNU1OUDJwNUFnenlIWTRLSlptVTBaS0NtVmtGUFFnY0l5cXN6bXUyYmFzdWdiVFEzQW9pcGJtNnBuZTZIK0ZvNktQWGd5eXRHUm0xUXhTbXpMb3NkSkhJRStqZnBFSnhibVllb1pJZDh5d25LMUpUVUVoaEpqV0hjTEt0VXk2dzZ2WHBBeno2NGJ3cGkzS2dtdE0yNnpJckI5R0dEVXNaaEMySFh1clFqQTc4TUQxdUZhZ2dUNUtiZGM3dkpjMnp5Z29VZ2g5RFhjNGFrZVRRM09pZnhsTDVzeUlTL0VKZ0dGSzczMVBIVWN0ZlkzYlVOZmw3QmoiLCJtYWMiOiIzMjE4ODE0M2I0YWM5MGFkYzQ5Yzc5OWNkZmNjY2RlNjAwMDdjOTVhNWFhOWU3ZDE2Mjg2NmEzOTc0MmU0NDliIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IkVUREJEYXFEME0yU1lzQm9IdDFLSFE9PSIsInZhbHVlIjoiTktnNDhUdCtVRU8xZ0o4N0IrSHo2MzNrNGMzV3pQMG5HcDloSUExS1BnTytiQllOSHkxQjdSVjh1OUV3NTZmWkVKM3ZWWVlOT2tDck5GVWZMdFlvcmwvOU9uWnI1SG1FRkVhc21icURrRkFheVZXTDJqK1gySzhMdmRwQStOak42OVY4QlpTd1N0WTFZb0V6K2trb1VQRjFGejNBYUZlTHNFZy9VeE1rcVNiMDlPWDNML1BjODNhQkVweks3S0JXY25sZUhGcks0UVJRL0JFT1VNL0ZmMExZczJEY1RodFZIN2pwYk9TbmxRQVdBRlFQeEJ6RjluS2xSdmxVbzdhTVp0WHBuS3V6Y082MWlObWxQZjI5eXpPaXhYWDE1MzVOWVNtTTJEZ2JaRjYwdmMvNkMvQUFLK3dHQUo0b2xiNlZiSHh4OG4rb2J5K0dtakpGVGtLUEY1amRDcFp3ZlUwWWNSMDFBZm41MDV0TVBLTFRZNmxtYnpId1R6ZFl6WTczVHJWMHdLL2hoYmRNYy9vWDNFR1h6THJnelA4bG81ZThYQ0YzMkV0OXRKOEs4RzQ4WmlNNWIxdmVvRVlBZTNwN1V2b2RCTGlCOUNrV0xzczc3YUhDUWFqTXlWQnBXN0JodXJKOGZnbGVtenVTUlFTK0FxWUFRZDhic3gxTTk4RS8iLCJtYWMiOiJkY2I1MWRjNDQwOTVmMzc3ODY4ZTcwZjRlMTEwZmFiNmNmNmNkOTE4ODdhY2VhOTg4OTFhYThmODg4OGI5NGJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-224197567\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-681492165 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-681492165\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1681296133 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:25:30 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6InF5emxlalR6Z3RDSUNnZ0Z3MExkVHc9PSIsInZhbHVlIjoid2ZwU3RiZ0VhNEsyOHY5dWMzMDZFMWNDNGdiZG1jWGtJek9DR1ZBS0ZmYU9ZMlA1aFhlYXNSWHZQUUkvMlNzbTlUbUpoWGlVM2NRZVBwaW1sMjBaTFVzcUJMdTBHTU4rVytWTFd5OUZGamNVNFA1QWlzOVRlU2NhaGtaTHEzb29jc0RqYzkxZEd3Rkw2L2lhbzNyb1FoR1VDQkpWb3BDdEtPRnl3SkFpc3d3NVljWWJKTEhlUlZVY3hIeG5za2xtdnpkREdERzR2M1cwVVVWQ2J5ZHBMR0lUcEFxaE9kdk12Y3Y1SjhIWUhzcjVEaGpoMkdpWlJoUjBJL1BoaTZYUVlRNWRwaWJVakFVeTdQWUdqTlpJZTVpVXRaZnRaZHFJMFBRUHhBMnNqRDcwQnYvaHhTaE9HV2pPWnNZaDh2UitSZWVRRTQwL2kzVnY5NXVzNEJxeXdXNVptUzhvbUt2aVQ0R3RLR1NaZ0RiUktqSW9LaE9BQWJldlZ4Rll2Q2dJeHIwKzUyWUh6SjlleEVSOHoya0xabEJEbnNBenNKOTNSU01qWVVFVzBWSFZ2N1hrcFRoeFhKMnhzNVZ4bEk5cWg4R1ZidTJaSVYxbUtKRVJBUXU2UnVoak9rQmo0RXRmU244T0NDbkZ3dHBLci9aWUdvL1NwdGsxUFRYbjJ5cWoiLCJtYWMiOiI1Y2Y1MTk1Y2EwYjUxYmVlNzkxM2ZiMDVkZWZhMTNjM2E4YTRhMWI3NzE5ZGY5OTY4YzY0ZmUzOGZjMWU3ZmQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:30 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IlZZdjY0U3FVaWJYQllqeWhBN1oxL3c9PSIsInZhbHVlIjoiWXZOdklNaEd2OW1OYVJyM0Ruc3RXK3VPNzhJWktrRGJLYlV6bkdGUStBN2hCZkhYbmJyaW9MaTVVb1B1ZGFvTG5IdGdpZnBFNDB0YkV6b3pQSnBXSkNxdmQ3ejV4b1JNTjJDS2RCNlExczVXOUgwVWk5NDE0QTBqV04rY1Yvb0NIejBnOXpNRk5uUUlDL0thTHNyZzgwOENNaXZjSkpQN0VYWU9qVUJKaWNKYjRGU1hTNXVHVGFheG90eXZsWURFUFFTUWwxUzE0M2VWK1l6Znh1dnNCTkhKaWVTZ3gvVUV0Zis0REFVMUU1clVkSU9ZWm1paUhHQXRKQ0xSRkFhRC8xNjRnZVdHVDA5c2I5TDU0czZpcGtWYmlWYlhCR2owZzhpa2NySC9JdDViYXp4VmptS1ZCdml2UmpsOWNWbTloSUhrVmpnNXBtejEybVFHaEtGWFJ6eVhaNkpUbEhaREFzMlQzZy9FV2I0RWJ5am5sekwvVm5lZ0FqK0dGVGJrc04xRjZKNlZZVFgxWk9uYmcwNlFhOXl4cDVyUlM3RGQ0YmNEdzc3Mi94NlZkblFTcWxXNGw4blY4TXNhd0o5MjRFeTlUZ3dIYkc3WEt1c3ZOR0NiSGw2MFJNdjJrN3FQNTQ3U0ZjcFJtbGJRbDQvdHpPcTBmZ0svV09scm5uVzciLCJtYWMiOiI5N2Q5OTdkYjMxNDQ0MjJkMWRhZWUwNGZiMzdhZjE3MTE0MGM5N2NjNmIxNTA0ZWU5NjEzYjA0MThlNWNmN2VlIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:25:30 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6InF5emxlalR6Z3RDSUNnZ0Z3MExkVHc9PSIsInZhbHVlIjoid2ZwU3RiZ0VhNEsyOHY5dWMzMDZFMWNDNGdiZG1jWGtJek9DR1ZBS0ZmYU9ZMlA1aFhlYXNSWHZQUUkvMlNzbTlUbUpoWGlVM2NRZVBwaW1sMjBaTFVzcUJMdTBHTU4rVytWTFd5OUZGamNVNFA1QWlzOVRlU2NhaGtaTHEzb29jc0RqYzkxZEd3Rkw2L2lhbzNyb1FoR1VDQkpWb3BDdEtPRnl3SkFpc3d3NVljWWJKTEhlUlZVY3hIeG5za2xtdnpkREdERzR2M1cwVVVWQ2J5ZHBMR0lUcEFxaE9kdk12Y3Y1SjhIWUhzcjVEaGpoMkdpWlJoUjBJL1BoaTZYUVlRNWRwaWJVakFVeTdQWUdqTlpJZTVpVXRaZnRaZHFJMFBRUHhBMnNqRDcwQnYvaHhTaE9HV2pPWnNZaDh2UitSZWVRRTQwL2kzVnY5NXVzNEJxeXdXNVptUzhvbUt2aVQ0R3RLR1NaZ0RiUktqSW9LaE9BQWJldlZ4Rll2Q2dJeHIwKzUyWUh6SjlleEVSOHoya0xabEJEbnNBenNKOTNSU01qWVVFVzBWSFZ2N1hrcFRoeFhKMnhzNVZ4bEk5cWg4R1ZidTJaSVYxbUtKRVJBUXU2UnVoak9rQmo0RXRmU244T0NDbkZ3dHBLci9aWUdvL1NwdGsxUFRYbjJ5cWoiLCJtYWMiOiI1Y2Y1MTk1Y2EwYjUxYmVlNzkxM2ZiMDVkZWZhMTNjM2E4YTRhMWI3NzE5ZGY5OTY4YzY0ZmUzOGZjMWU3ZmQ1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:30 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IlZZdjY0U3FVaWJYQllqeWhBN1oxL3c9PSIsInZhbHVlIjoiWXZOdklNaEd2OW1OYVJyM0Ruc3RXK3VPNzhJWktrRGJLYlV6bkdGUStBN2hCZkhYbmJyaW9MaTVVb1B1ZGFvTG5IdGdpZnBFNDB0YkV6b3pQSnBXSkNxdmQ3ejV4b1JNTjJDS2RCNlExczVXOUgwVWk5NDE0QTBqV04rY1Yvb0NIejBnOXpNRk5uUUlDL0thTHNyZzgwOENNaXZjSkpQN0VYWU9qVUJKaWNKYjRGU1hTNXVHVGFheG90eXZsWURFUFFTUWwxUzE0M2VWK1l6Znh1dnNCTkhKaWVTZ3gvVUV0Zis0REFVMUU1clVkSU9ZWm1paUhHQXRKQ0xSRkFhRC8xNjRnZVdHVDA5c2I5TDU0czZpcGtWYmlWYlhCR2owZzhpa2NySC9JdDViYXp4VmptS1ZCdml2UmpsOWNWbTloSUhrVmpnNXBtejEybVFHaEtGWFJ6eVhaNkpUbEhaREFzMlQzZy9FV2I0RWJ5am5sekwvVm5lZ0FqK0dGVGJrc04xRjZKNlZZVFgxWk9uYmcwNlFhOXl4cDVyUlM3RGQ0YmNEdzc3Mi94NlZkblFTcWxXNGw4blY4TXNhd0o5MjRFeTlUZ3dIYkc3WEt1c3ZOR0NiSGw2MFJNdjJrN3FQNTQ3U0ZjcFJtbGJRbDQvdHpPcTBmZ0svV09scm5uVzciLCJtYWMiOiI5N2Q5OTdkYjMxNDQ0MjJkMWRhZWUwNGZiMzdhZjE3MTE0MGM5N2NjNmIxNTA0ZWU5NjEzYjA0MThlNWNmN2VlIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:25:30 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1681296133\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1508767356 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"21 characters\">&#1578;&#1605; &#1573;&#1594;&#1604;&#1575;&#1602; &#1575;&#1604;&#1588;&#1610;&#1601;&#1578; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508767356\", {\"maxDepth\":0})</script>\n"}}