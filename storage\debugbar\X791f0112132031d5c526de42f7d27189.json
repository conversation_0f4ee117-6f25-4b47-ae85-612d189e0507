{"__meta": {"id": "X791f0112132031d5c526de42f7d27189", "datetime": "2025-06-27 02:12:33", "utime": **********.208119, "method": "POST", "uri": "/pos-financial-record", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750990352.747256, "end": **********.208135, "duration": 0.460878849029541, "duration_str": "461ms", "measures": [{"label": "Booting", "start": 1750990352.747256, "relative_start": 0, "end": **********.082816, "relative_end": **********.082816, "duration": 0.3355598449707031, "duration_str": "336ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.082825, "relative_start": 0.335568904876709, "end": **********.208136, "relative_end": 1.1920928955078125e-06, "duration": 0.12531113624572754, "duration_str": "125ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 50951632, "peak_usage_str": "49MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST pos-financial-record", "middleware": "web, verified, auth, XSS", "controller": "App\\Http\\Controllers\\FinancialRecordController@SetOpeningBalance", "namespace": null, "prefix": "", "where": [], "as": "pos.financial.record.opening.balance", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=134\" onclick=\"\">app/Http/Controllers/FinancialRecordController.php:134-167</a>"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.06228999999999999, "accumulated_duration_str": "62.29ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.111151, "duration": 0.01787, "duration_str": "17.87ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 28.688}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": "middleware", "name": "verified", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\EnsureEmailIsVerified.php", "line": 41}], "start": **********.13702, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 28.688, "width_percent": 0.803}, {"sql": "insert into `shifts` (`shift_opening_balance`, `is_closed`, `created_by`, `warehouse_id`, `updated_at`, `created_at`) values ('1450', 0, 22, 8, '2025-06-27 02:12:33', '2025-06-27 02:12:33')", "type": "query", "params": [], "bindings": ["1450", "0", "22", "8", "2025-06-27 02:12:33", "2025-06-27 02:12:33"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 147}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.148279, "duration": 0.03705, "duration_str": "37.05ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:147", "source": "app/Http/Controllers/FinancialRecordController.php:147", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=147", "ajax": false, "filename": "FinancialRecordController.php", "line": "147"}, "connection": "kdmkjkqknb", "start_percent": 29.491, "width_percent": 59.48}, {"sql": "select * from `financial_records` where (`shift_id` = 45) and `financial_records`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["45"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.186941, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 88.971, "width_percent": 0.979}, {"sql": "insert into `financial_records` (`shift_id`, `opening_balance`, `created_by`, `updated_at`, `created_at`) values (45, '1450', 22, '2025-06-27 02:12:33', '2025-06-27 02:12:33')", "type": "query", "params": [], "bindings": ["45", "1450", "22", "2025-06-27 02:12:33", "2025-06-27 02:12:33"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 154}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.188708, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:154", "source": "app/Http/Controllers/FinancialRecordController.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=154", "ajax": false, "filename": "FinancialRecordController.php", "line": "154"}, "connection": "kdmkjkqknb", "start_percent": 89.95, "width_percent": 5.009}, {"sql": "update `users` set `is_sale_session_new` = 0, `users`.`updated_at` = '2025-06-27 02:12:33' where `id` = 22", "type": "query", "params": [], "bindings": ["0", "2025-06-27 02:12:33", "22"], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Http/Controllers/FinancialRecordController.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Controllers\\FinancialRecordController.php", "line": 162}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 262}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 208}], "start": **********.192812, "duration": 0.00314, "duration_str": "3.14ms", "memory": 0, "memory_str": null, "filename": "FinancialRecordController.php:162", "source": "app/Http/Controllers/FinancialRecordController.php:162", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2FFinancialRecordController.php&line=162", "ajax": false, "filename": "FinancialRecordController.php", "line": "162"}, "connection": "kdmkjkqknb", "start_percent": 94.959, "width_percent": 5.041}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/hrm-dashboard\"\n]", "success": "تم تحديث الرصيد المفتوح بنجاح", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/pos-financial-record", "status_code": "<pre class=sf-dump id=sf-dump-1761810481 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1761810481\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1639077070 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1639077070\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-321727443 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>opening_balance</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1450</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-321727443\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1567956151 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">68</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1842 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; XSRF-TOKEN=eyJpdiI6InlySWd6aGNOTGRsdHRtTUFCeWpkeGc9PSIsInZhbHVlIjoiakM3dUlKVEdOcC9nWnNGYm90ckxPSzBIeE9GanRmKzROcTR5ci9PeGlKQmZvQlAwb2FjclBVbnBWSkhQbkZjclFHeTFha00wMngzcS9KSmY2TFNtb1Bmc0FiU2xMVFJRbEdXdXZ3eGRmaGNXajBZa3NaVU5iQm0yQW53c3RDNFVFRVhOL01YRGg5dzJxcUV2NVlBaWVTSDJHek9WY3FQM2ZjOEU4NVU2eTVVd2ZRZFF3NERvRFZJeEdvbk1Nb1pCcERtcUd2U3hnZXMrMklmQXhSRVBPekpRNDBXTDY0ZVFjTmZTOTN6a3pDU01pa2hRSXhFb1dVVXFvMHg0RHZoRXJrcDJNRnorVXRMR3NsdEExRGJhMExHT2I1WVZVUGY4Y2VocVhYeWJQbUpiZkJ3MjRuenl4QjBtQUpudlF4bmNQeWxrNXp6T0tHQlZpV0JNYnEvcjAvd1g0OUZoY0ZUMm1XZk1zdHUxdWV2eUVYTXhVSElNdWJDcHRuNjZuSXVON29ZK2dsSU93bDdIanNHR1ZMYnoxdzRpM2cySEdFVWMxUVZSdE51T3Facm5td1N5MXpkaURJVFVINUh3QW1zeTBqdVNxZVdkU3YzVm9IMHlSSWlKWFkwVjZ2cVNiS2FnYTYvUEJraTlNQ2JrS1F0WERPRlpWNzI2Q2JPNW5RcWoiLCJtYWMiOiJhYjNjNDAxN2E1NWNkM2FmOTU1ODc0MmMzMzg5YzczZjhlNDJjNDAyODI5NDhiYjdiZWNiY2Q3ZGI5ZTkyZWU3IiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IjB6Z1JUV3I4NTBBeWZNNy9icmhnUEE9PSIsInZhbHVlIjoiMC9mZkNlUHlyVDJpZFpYeXprYXgrWkZyS0pLNmd6T0tRVDJQU0R1c0tlQ2RwbFJMaUdmczdKR2RSTFR6SFJQeUNydGNhZGRESWdzUE15cm9OOE8yMW9CeU1HTGxrZThSKzJIM2Z6SUlGNEpuRHBxRS96S3dvYmVuckNCOXZUYXJCS0EwWUI5ZFJxZ0tRY05uYUZHdWtMb3dScFhRRVhDOTJyYmNPL0RKaVVsNjQ1dFdCdUwvaUZpbUZqMzJKNFhDWTQwQi9hTzFrM2NteURzWmZLS3Jac0JYcWtCMkMvUDVsWmg3bmdnWEZxL294OGVGM2ZJSUF3enRSck5rV0N1QWtzdFRQVG9CR0V0ZU5wOVlrZGlTQktwRDRSZEFBUXJzMzMzcmdDUi9NWnpCeXo3SzF5ZFdNZmVRZFk5QWoyTldHVTRlTUxKOElmVy9JMkJmYk9rZE1VUEExSmk3YmtmQXZtSWVKU05Xd0pmbm1oQm5oVDd6NnhJMldLb3F2dDlZR3FucjF2YjdCT2x5Ly9aaDBVZ21xeFZEQzBtNDYyUmZNOFBNZTgyTXBWMGZxc0g3RVpiRGVvMktXeCtnb3haREcwdGF6L3VtbytQdnA5VEdMY2xCWWV0Ry9waW5PeWpuNlFneExGK0VpMUVKZDAxTkNjL3QxSlEyYUxRRm1MQVMiLCJtYWMiOiJhOTVhYWI2YWYwMDgwMDJlOTYxYzY2Mzk2ZTY4ZjE1ZjQ1OTM5MjcxYmM4N2YxMjBhY2IxMzg2ZjNhNGI1ODg3IiwidGFnIjoiIn0%3D; _clsk=1wa64lz%7C1750990349613%7C5%7C1%7Ci.clarity.ms%2Fcollect</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1567956151\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-303184053 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-303184053\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:12:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IkY3Z3ppcURLZ2M4RzFhTUdBR0JhUUE9PSIsInZhbHVlIjoic0ZBcmk5SjV3RDJyZTB5a2FWbUllUjZyT24zUFBVeEtrMUhBU3dmblBJYWJHb2ZqdkN1bnp2WXF5OVQ5alN5Z3kvNDlKbk1qdjQ5N3VoYW11NkszSXVBZ0xPTXVUOHlXTGpKSmdiM3hpZlhabmNUNVp3RHBMbDVEcVRKQ2tpbm5tTTBUbXcyc2h0UDZGQkFEY3FKdm04YWdoNWJvbmNRU0RPbEJhdTg2aUI5a0VCVmlEa254UWdpZHJjZnNER2RwTEZuSUZzZXNOSnFTcDZqZGRRSWN0MC9ONU9rM0x5TlBHZzFRVmZVdm53Tk5GZldiWUgrSlRIWGJoZVA0eEpDcXp6TGtRRmR2Tkh5NmlVSzNOaklTNW1pNmZWcU4xOFlEU0RsUnlmb3lDR3hRck83YmJKYjNWQkdaWmoybXF2cHF5c0xaZDFsWW5YYnJ0YktMTjFwWUo0SEsyYkxpWFdqV2RvVGN0Wmd3cWNHb3RxbDJRMjBaL0FpNTlvUFBiNXBLMjVLQ3ovRG9tRFlka0ZzZjliSXNoYXNEaUhUNlN3MFpZZStCSjJGVko3VWJ6SWVRd1dSbGVldEJGZytEcE5mRlFvcmpiZFFTYlRBekxjNFcwS01rK1Vjd3pUSkFBWk8vblQrVWF3eHFQOGJJc1dVK1diMlNadmNXank0SE9pdDgiLCJtYWMiOiIwOWQ4ZmY2YTk1NzIzYzFkNjAzZmRiNjE0YWMxODcyNmJiMzExNzZiZTg3ZmU4YWIzZGY3ZjljNWVhNjNjZGE2IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:33 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjNCSjRqaGQzOXo3eXovS2FhZWEraHc9PSIsInZhbHVlIjoiQmVyU1NGTVNjU0llQlQvMzZnb0UwK0t1RjErRVlXY0V2Y3UwWFgxYVpWOXhIKyt1endwOW1pamRvSExCMHpCQTM3UDdTN3hCUlkyVmwxWmZlYk85UUE2MmRZazlzOWZKOThhaFY4b0I1N0dJUWgxMDJ2Uk1aT3pkMnBBQ0xPRzFpczJxdlFuUnBmbCsrVXNhTnZVQ21JZ3Jlb3FHYUJaenl6NysyZmRmRWt4UFVpcGdwUE5YZzh2NU9ra0NuM0VadFovNzdyK3ZhM0RuNm1PbFZvbnZkcUxYVHVkNWUwcUdhQ2VsY29RRHpad056WGhwNDg1STJPaVM0bFNQTUZjM0lCeC96N1RxWXMycG5QSTVwQ2IveGNaZFdtMnBoUm0xa2o2ZkdJNERrVUUrb2dOSWhSb0pwZm9SVlZWNC9tejVNNEJxRlF1S0kyNmlNdCt0WFVsejdBRmtnNTMxb1BUa2dPRlRDSGhRMzVTdE84UE1xVHlNaDFWTjhSVkNXbXluaXNqMmRVOFlMNTFFTWZpQ3JxNXlncVh1OGF6Zk9mUGJHZ0lSd2J2eUdtcGxINGxsZERTdENmSHREN3dYWmxwQUZZTllwVFVuRWtCaHFKVzBDekU5dGFkb0hBc2pxbjRFTFNGR05ldEo2QXl2WHNlc0Q0ektPbU9ndnRQYmJYdWEiLCJtYWMiOiJlNWU2NDVkYjVjZmI1MTU3ZjE0NjJiMDJlYzk3Mjc3MmIwNjg5MWUzMzNlYmIwOGNlYjMwMDRjYWMzZGNmNWJiIiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:12:33 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IkY3Z3ppcURLZ2M4RzFhTUdBR0JhUUE9PSIsInZhbHVlIjoic0ZBcmk5SjV3RDJyZTB5a2FWbUllUjZyT24zUFBVeEtrMUhBU3dmblBJYWJHb2ZqdkN1bnp2WXF5OVQ5alN5Z3kvNDlKbk1qdjQ5N3VoYW11NkszSXVBZ0xPTXVUOHlXTGpKSmdiM3hpZlhabmNUNVp3RHBMbDVEcVRKQ2tpbm5tTTBUbXcyc2h0UDZGQkFEY3FKdm04YWdoNWJvbmNRU0RPbEJhdTg2aUI5a0VCVmlEa254UWdpZHJjZnNER2RwTEZuSUZzZXNOSnFTcDZqZGRRSWN0MC9ONU9rM0x5TlBHZzFRVmZVdm53Tk5GZldiWUgrSlRIWGJoZVA0eEpDcXp6TGtRRmR2Tkh5NmlVSzNOaklTNW1pNmZWcU4xOFlEU0RsUnlmb3lDR3hRck83YmJKYjNWQkdaWmoybXF2cHF5c0xaZDFsWW5YYnJ0YktMTjFwWUo0SEsyYkxpWFdqV2RvVGN0Wmd3cWNHb3RxbDJRMjBaL0FpNTlvUFBiNXBLMjVLQ3ovRG9tRFlka0ZzZjliSXNoYXNEaUhUNlN3MFpZZStCSjJGVko3VWJ6SWVRd1dSbGVldEJGZytEcE5mRlFvcmpiZFFTYlRBekxjNFcwS01rK1Vjd3pUSkFBWk8vblQrVWF3eHFQOGJJc1dVK1diMlNadmNXank0SE9pdDgiLCJtYWMiOiIwOWQ4ZmY2YTk1NzIzYzFkNjAzZmRiNjE0YWMxODcyNmJiMzExNzZiZTg3ZmU4YWIzZGY3ZjljNWVhNjNjZGE2IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjNCSjRqaGQzOXo3eXovS2FhZWEraHc9PSIsInZhbHVlIjoiQmVyU1NGTVNjU0llQlQvMzZnb0UwK0t1RjErRVlXY0V2Y3UwWFgxYVpWOXhIKyt1endwOW1pamRvSExCMHpCQTM3UDdTN3hCUlkyVmwxWmZlYk85UUE2MmRZazlzOWZKOThhaFY4b0I1N0dJUWgxMDJ2Uk1aT3pkMnBBQ0xPRzFpczJxdlFuUnBmbCsrVXNhTnZVQ21JZ3Jlb3FHYUJaenl6NysyZmRmRWt4UFVpcGdwUE5YZzh2NU9ra0NuM0VadFovNzdyK3ZhM0RuNm1PbFZvbnZkcUxYVHVkNWUwcUdhQ2VsY29RRHpad056WGhwNDg1STJPaVM0bFNQTUZjM0lCeC96N1RxWXMycG5QSTVwQ2IveGNaZFdtMnBoUm0xa2o2ZkdJNERrVUUrb2dOSWhSb0pwZm9SVlZWNC9tejVNNEJxRlF1S0kyNmlNdCt0WFVsejdBRmtnNTMxb1BUa2dPRlRDSGhRMzVTdE84UE1xVHlNaDFWTjhSVkNXbXluaXNqMmRVOFlMNTFFTWZpQ3JxNXlncVh1OGF6Zk9mUGJHZ0lSd2J2eUdtcGxINGxsZERTdENmSHREN3dYWmxwQUZZTllwVFVuRWtCaHFKVzBDekU5dGFkb0hBc2pxbjRFTFNGR05ldEo2QXl2WHNlc0Q0ektPbU9ndnRQYmJYdWEiLCJtYWMiOiJlNWU2NDVkYjVjZmI1MTU3ZjE0NjJiMDJlYzk3Mjc3MmIwNjg5MWUzMzNlYmIwOGNlYjMwMDRjYWMzZGNmNWJiIiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:12:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-43416393 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost/hrm-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"29 characters\">&#1578;&#1605; &#1578;&#1581;&#1583;&#1610;&#1579; &#1575;&#1604;&#1585;&#1589;&#1610;&#1583; &#1575;&#1604;&#1605;&#1601;&#1578;&#1608;&#1581; &#1576;&#1606;&#1580;&#1575;&#1581;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43416393\", {\"maxDepth\":0})</script>\n"}}