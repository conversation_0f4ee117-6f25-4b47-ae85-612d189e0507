{"__meta": {"id": "Xae251242f0cedb9464f238c1afdfa058", "datetime": "2025-06-27 02:34:02", "utime": **********.426963, "method": "POST", "uri": "/chats/getContacts", "ip": "::1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750991641.990179, "end": **********.426978, "duration": 0.4367990493774414, "duration_str": "437ms", "measures": [{"label": "Booting", "start": 1750991641.990179, "relative_start": 0, "end": **********.35075, "relative_end": **********.35075, "duration": 0.36057090759277344, "duration_str": "361ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.350761, "relative_start": 0.3605818748474121, "end": **********.426981, "relative_end": 2.86102294921875e-06, "duration": 0.07622003555297852, "duration_str": "76.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 45720304, "peak_usage_str": "44MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST chats/getContacts", "middleware": "web, auth, XSS, pusher, check.messenger.access, guest", "controller": "App\\Http\\Controllers\\vendor\\Chatify\\MessagesController@getContacts", "namespace": "App\\Http\\Controllers\\vendor\\Chatify", "prefix": "chats", "where": [], "as": "contacts.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FHttp%2FControllers%2Fvendor%2FChatify%2FMessagesController.php&line=333\" onclick=\"\">app/Http/Controllers/vendor/Chatify/MessagesController.php:333-390</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.02115, "accumulated_duration_str": "21.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 22 limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.3819711, "duration": 0.020329999999999997, "duration_str": "20.33ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 0, "width_percent": 96.123}, {"sql": "select * from `settings` where `created_by` = 15", "type": "query", "params": [], "bindings": ["15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 59}, {"index": 14, "namespace": null, "name": "app/Models/Utility.php", "file": "C:\\laragon\\www\\erpq24\\app\\Models\\Utility.php", "line": 300}, {"index": 15, "namespace": "middleware", "name": "XSS", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\XSS.php", "line": 28}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/FilterRequest.php", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\FilterRequest.php", "line": 26}], "start": **********.411094, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "Utility.php:59", "source": "app/Models/Utility.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUtility.php&line=59", "ajax": false, "filename": "Utility.php", "line": "59"}, "connection": "kdmkjkqknb", "start_percent": 96.123, "width_percent": 1.986}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (22) and `model_has_roles`.`model_type` = 'App\\Models\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 227}, {"index": 23, "namespace": "middleware", "name": "check.messenger.access", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\CheckMessengerAccess.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "pusher", "file": "C:\\laragon\\www\\erpq24\\app\\Http\\Middleware\\pusherConfig.php", "line": 29}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\laragon\\www\\erpq24\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.417274, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:227", "source": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php:227", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=227", "ajax": false, "filename": "HasRoles.php", "line": "227"}, "connection": "kdmkjkqknb", "start_percent": 98.109, "width_percent": 1.891}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Flaragon%2Fwww%2Ferpq24%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"error\"\n  ]\n  \"new\" => []\n]", "login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82": "22", "_previous": "array:1 [\n  \"url\" => \"http://localhost/pos-financial-record\"\n]", "error": "Access Denied. You do not have permission to access this feature.", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/chats/getContacts", "status_code": "<pre class=sf-dump id=sf-dump-1966370210 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-1966370210\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-395491865 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-395491865\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2074926031 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2074926031\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1487982033 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">http://localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1947 characters\">_clck=1pn1t14%7C2%7Cfx4%7C0%7C2004; cc_cookie={&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false}; cookie_consent_logged=1; _clsk=1wa64lz%7C1750991639383%7C39%7C1%7Ci.clarity.ms%2Fcollect; XSRF-TOKEN=eyJpdiI6Ijk2NnJvV3JwSTdkalNodkwrZDVCNkE9PSIsInZhbHVlIjoiaS9LT2NaZlJ5UVhZRVF5azFXVXJDZjFSU2xuelRJdlRjR2NXUG5uVWlPSk9lVVY2NldFUlA3YkFlbU0wNllZWEdVK0hTMDVSSVVOLzQ4ZzBrM2lDSlJYZ0xCRGZQMHIrTEJDbmNuVWhvWjdsTEFFQ2lxU3pUMWczblZsT3ZUSE52N0pHTEhzUFpxcnVtbVp4dkc1bnU4LzJ5YnNrL091K1l2eit3TGJOZjFPMkFjdWxueCt3aVhtTVhWb25iSUR3bzdoeXk4cjg2QnlTZVpmbGpJN3dud2U5QVNGaklOditYTGV6ZU15U0l5TVQyTHFUVmVUMmptSzJMVXJDWDVxNDU3RXFQQm16TERqM3NQaFhGYmtYWjBHa3lFYjk4SFZ2Tm0wMUdKV1JJaGN5N244U1RHK1cvTWc5NTkwbjZYeDlHWUdCZGdHZ3dEL1hlNHp4NThDNUJSckVNNmNjNnFLSkhjdDRKNjQxTEtiaDkvNWEram05R2NNOXQrblNpWldSYWkwSUtDdmJncXlJemNNcTdkWUJBT1FobGZucUlMSzljYTdrb2VMZDMzTS95Q2FvK1dhbDF5UzI5MWZDTlhPeVQwS2xSUS9pS0dLMlQ1SXBEckd5YnhJTGhsOFZwdjJLNXgveGtqa3VSNmxvV3ZNcnhoYXc0aUljL2FxWHpJM3UiLCJtYWMiOiJhNmVkNTAwZWJiMDdkZTAxYzc2MWE0YjgyZDdjNWY2NmYyZmQxNzFiYzNhZmM5ZWNlOGU0NjRjMDliOTIxNDIxIiwidGFnIjoiIn0%3D; quickly24erp_session=eyJpdiI6IlZTY3J5SlNZcmI1cGxGZHBJR1kvcWc9PSIsInZhbHVlIjoid1NTSzR1dEpXSndhbFJRRW9MM2tjTVpmbER3QnZwQnR6VWs5QW8xc1ZqaUtpbXFuYTF4MmROWEs5b2pLWm1yRklhUzVtSERuYjF3OXd0bmZ1NzR1NkFVNHF5c1VNMExpYnhZL1NPMGRnc3NLTzlpKy9NcnhJaWFuL1JpTTJMdnBhWHJ1a0ZUUG4rRU1GeCtLN0xWKzNla0RzZzJaTHh5NE5aWG9KMXlLU0FHZUhBOTRHRmxpMHNUYUxURmUzTnZnTGFkUVl3UnF3RGhwTWZJcVJOR3ltRVNFbnJtNmZmVTNKd2dnK3hUaXJqZ1dzazFEV3BrVXVabFd0SlBMY0U1cjJIemJGbmR5VVlWZjBSTExOT2pWZWZBWGZUVko4bWNOUjJXZlNuRGh1ZDZENEVJdWYvazc1MXo0RjJMdGlUZWFETnIwcUtzQnlDS1IyVWNhNDBGZVRjdi8yOGMrVFF3aU51S1plMmdkTWlBN2dIMW9tV0o0bHhvU2oxZ3FIVSthUkMyNk1Vc3BrUEY4cm1DRXVsVFdVZDFKY0VGYUU1ckJCeVpCTVMvVkpKT0plWXpSc3d6SFBTMDF6SEtkQUNzSklSME5XSnU4Zk1qUVpHZWpvRTBhQ0pJRm1OTS9adEhkWUdNTVllSUxEUHlVWXlsT3FuZDBUNWpEM1ZGME5nSEEiLCJtYWMiOiIzZjg5MWVhY2Q4NDkzYTlhNzM1YmZmODI5ZDJiZTY1NzdmODdkMjg2MzA4NWZhNTIyMDA1MzFjNzY0ZDk5ZjdiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487982033\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-895468305 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_clck</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_consent_logged</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_clsk</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>quickly24erp_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">wTkb79DbGV03EpIEFnMFfNovgeuaoFivAgb9zkQ3</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-895468305\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2138785651 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 27 Jun 2025 02:34:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"34 characters\">http://localhost/account-dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"926 characters\">XSRF-TOKEN=eyJpdiI6IjZSNGR5Wk1qUzJrTmlvNG9ZM0ZHMnc9PSIsInZhbHVlIjoiYjkyVXlIbUZza0crQVdTNlQrWlM2M0xhamplKzlpTWlBY3B5TEdvY0lxeTM3UG9uOW5DVFAvaXRmM3lwSEZtakEzV3c2d2ZVTGVzeDFVMk85dFB6NURlMzhnYmJlOWdCMCtIb2RpU1JiNU5KdzdtZW5TdzQyM3lRL1FIcW9tbCtSTGNOVkdSMXpJb21ubGxmT1RPcW1nZHJEZlRhem5LaGhOdlhoaGhUUjlDdnE0NUJmVUhDeXE4bi8yK3N6NGVFeXFjN3REcnV4MEtsN0xqK0EwZDF6OGNsTzFMeTJrRUtLRE5CUUVKTzNHbzBsZGtFK2hiWm56YkZDWk1tQzZPL1FOTzhidXpDemgrdXRHOFhpYU5EWmtBTUp0SnZ4UTNkY0JhVHVUeU55UGN2OXRFRDZ3bmo2ckZqbkRvQU9PZzMyeVUxSnlZMkF1eWszU2RNdEFGTGZUemJoSWc1ZWxSV08vcHFOZ1pOOVlGUkFySmN4SndPMjVIOFlUSlRaU3J1dDdKMVRwanl2blN6a2VzVlM2OW9BTGhnaHJNN0ozUjRpamRMSlR2c2JzMkh3dS9pNHFucEJIcE0xc2ZXZXI4QndZQS95SE5TZFJwK2VQbGFCRENuVUM1Y3lPbHA2b2ZhUjcrdEdBWURvYkZvbTY2NU8wcmJoK292dEpncjhTNFIiLCJtYWMiOiI1Mzk1ZGZhYWEyOWQxN2NjZGIwZjAxYTMwM2ZjMzVjNjAxZDc2NTU5YTJkNDk2ZjdmOTQ3YjY4OGNjNmVjZjc1IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:02 GMT; Max-Age=7200; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"946 characters\">quickly24erp_session=eyJpdiI6IjE5anhtWElTZlhDNEFZc2dtMmxpSVE9PSIsInZhbHVlIjoiMkNqZ3dJZ3dQSnpPK1BCaDh4TmloSC9NWllvQkNVRDB3M051YkZjQTFLZzBKLzFyR1NUZ1d0TXpLdWxOUFVIZUQ5VG54MVh1UkJLZ1NXS3QzazduQkJYdHJCMVRMSEhlcHk0TE9kZ0FZQUJmbmRjV2ZqSTVnRlI5ckowTndzbXhOVlNZaDQvd3NIb3FYVWJITDhBMHFsSStBRllwV2k2L1k1RURGMXhQTTY4ZmFSNGtIOWVnSW1SakttM1ZrM1JBZS9qSkkxZ0ZmWXF2VTdnalRtNTBXcDFrNG93cFNlMzIvUUtYU21kWDFIbzlQVUswOUhLcVFvdk1lR0xaQUVuM1laNHNzOXB3T0R5TFB6d2N2VGtBQXZiQS9CYmJKUHNBTE13Nm1SSmNFbXIxRHhYTkFGeko5V2l2VEoxYTBxVGNVenY5YitmM2ljVnI1UEhPM0FvN0RkZjJTNlh4Y2FKV1RJaGZWdEt3VHpZamxKN2Z2bnFrZ1hRN1NNS1lGQUdvT0w5dFp5UXVNalQ4THljUERCYTFXcVpSSVRDeHp3cm1Xd245RGQ3NVdBdnVBOUFGWTZ6a1RxZFBEUldOQnY3Z0tUdWI0VUNRWFp2dE8rZ2JpMTlWZkd0c0pFbXFzWmt3UDlrZi8yUGVJbGpLeXg3b2F2T05Jb0Vzd0FaR0VFeEMiLCJtYWMiOiI4YjljNzcwOGFlNDI5OTA3OGMyYmM1MzNlNmI2ZTIzZmY5OGI4MTU4YjIzYThkODQyN2U3NDk0MWFmODFhZGE4IiwidGFnIjoiIn0%3D; expires=Fri, 27 Jun 2025 04:34:02 GMT; Max-Age=7200; path=/; httponly</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"912 characters\">XSRF-TOKEN=eyJpdiI6IjZSNGR5Wk1qUzJrTmlvNG9ZM0ZHMnc9PSIsInZhbHVlIjoiYjkyVXlIbUZza0crQVdTNlQrWlM2M0xhamplKzlpTWlBY3B5TEdvY0lxeTM3UG9uOW5DVFAvaXRmM3lwSEZtakEzV3c2d2ZVTGVzeDFVMk85dFB6NURlMzhnYmJlOWdCMCtIb2RpU1JiNU5KdzdtZW5TdzQyM3lRL1FIcW9tbCtSTGNOVkdSMXpJb21ubGxmT1RPcW1nZHJEZlRhem5LaGhOdlhoaGhUUjlDdnE0NUJmVUhDeXE4bi8yK3N6NGVFeXFjN3REcnV4MEtsN0xqK0EwZDF6OGNsTzFMeTJrRUtLRE5CUUVKTzNHbzBsZGtFK2hiWm56YkZDWk1tQzZPL1FOTzhidXpDemgrdXRHOFhpYU5EWmtBTUp0SnZ4UTNkY0JhVHVUeU55UGN2OXRFRDZ3bmo2ckZqbkRvQU9PZzMyeVUxSnlZMkF1eWszU2RNdEFGTGZUemJoSWc1ZWxSV08vcHFOZ1pOOVlGUkFySmN4SndPMjVIOFlUSlRaU3J1dDdKMVRwanl2blN6a2VzVlM2OW9BTGhnaHJNN0ozUjRpamRMSlR2c2JzMkh3dS9pNHFucEJIcE0xc2ZXZXI4QndZQS95SE5TZFJwK2VQbGFCRENuVUM1Y3lPbHA2b2ZhUjcrdEdBWURvYkZvbTY2NU8wcmJoK292dEpncjhTNFIiLCJtYWMiOiI1Mzk1ZGZhYWEyOWQxN2NjZGIwZjAxYTMwM2ZjMzVjNjAxZDc2NTU5YTJkNDk2ZjdmOTQ3YjY4OGNjNmVjZjc1IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"932 characters\">quickly24erp_session=eyJpdiI6IjE5anhtWElTZlhDNEFZc2dtMmxpSVE9PSIsInZhbHVlIjoiMkNqZ3dJZ3dQSnpPK1BCaDh4TmloSC9NWllvQkNVRDB3M051YkZjQTFLZzBKLzFyR1NUZ1d0TXpLdWxOUFVIZUQ5VG54MVh1UkJLZ1NXS3QzazduQkJYdHJCMVRMSEhlcHk0TE9kZ0FZQUJmbmRjV2ZqSTVnRlI5ckowTndzbXhOVlNZaDQvd3NIb3FYVWJITDhBMHFsSStBRllwV2k2L1k1RURGMXhQTTY4ZmFSNGtIOWVnSW1SakttM1ZrM1JBZS9qSkkxZ0ZmWXF2VTdnalRtNTBXcDFrNG93cFNlMzIvUUtYU21kWDFIbzlQVUswOUhLcVFvdk1lR0xaQUVuM1laNHNzOXB3T0R5TFB6d2N2VGtBQXZiQS9CYmJKUHNBTE13Nm1SSmNFbXIxRHhYTkFGeko5V2l2VEoxYTBxVGNVenY5YitmM2ljVnI1UEhPM0FvN0RkZjJTNlh4Y2FKV1RJaGZWdEt3VHpZamxKN2Z2bnFrZ1hRN1NNS1lGQUdvT0w5dFp5UXVNalQ4THljUERCYTFXcVpSSVRDeHp3cm1Xd245RGQ3NVdBdnVBOUFGWTZ6a1RxZFBEUldOQnY3Z0tUdWI0VUNRWFp2dE8rZ2JpMTlWZkd0c0pFbXFzWmt3UDlrZi8yUGVJbGpLeXg3b2F2T05Jb0Vzd0FaR0VFeEMiLCJtYWMiOiI4YjljNzcwOGFlNDI5OTA3OGMyYmM1MzNlNmI2ZTIzZmY5OGI4MTU4YjIzYThkODQyN2U3NDk0MWFmODFhZGE4IiwidGFnIjoiIn0%3D; expires=Fri, 27-Jun-2025 04:34:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2138785651\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1522976036 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GQmgcdH0mWEIU9WSOSAs9ha4G3s2jnrrcIcQoLfM</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">error</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => <span class=sf-dump-num>22</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost/pos-financial-record</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>error</span>\" => \"<span class=sf-dump-str title=\"65 characters\">Access Denied. You do not have permission to access this feature.</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1522976036\", {\"maxDepth\":0})</script>\n"}}